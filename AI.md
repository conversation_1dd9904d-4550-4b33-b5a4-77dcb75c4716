# 一些 ai 的使用技巧

首先要明白 ai 在一次生成过程中，持有的上下文，你越描述清楚，效果越好。

ai 很难一次正确，且有时候会改错，要在过程中查看效果，正确的时候，多 commit，生成的代码多 review。

ai 如果有参考，能很好的工作。

列表页面通常是固定模式。

新增页面需要分步骤：0 编辑页复用 1 实现出所有的输入项目 2 精细去完善对应项的交互

## 明确目标

尽可能一次生成出对的代码

## 当前最佳实践

先参考，后实现。

先总体，再局部。

### 列表页

列表页面，通常可以做到一次过，下面是提示词的编写步骤

1. 先指定具体要实现的文件

帮我实现 src\pages\operateLabor\platform\xxx.vue

2. 指定参考文件

参考 src\pages\operateLabor\platform\xxx.vue 进行实现

3. 指定搜索条件，从 prd，利用 ocr 或者复制得到要实现的搜索条件名称

搜索条件有 名称，地址，时间，... 其中名称，地址应该放到 lite 里面

变种：我不需要展开与收起，搜索条件仅需要 名称，地址

4. 指定 api 调用名称和参数（从 swagger ui 复制过来），这个 api 你提前手动/主动写到对应 client.js 中

搜索采用 client.listCorporation ，请求参数为
queryFilterWebQueryCustomerFilters QueryFilterWebQueryCustomerFilters body true
QueryFilterWebQueryCustomerFilters
QueryFilterWebQueryCustomerFilters
start false
integer(int64)
offset false
integer(int64)
limit false
integer(int64)
sorts false
array
Sort
field false
string
direction 可用值:ASCENDING,DESCENDING false
string
withTotal false
boolean
withDisabled false
boolean
withDeleted false
boolean
filters false
WebQueryCustomerFilters
WebQueryCustomerFilters
id 编号 false
integer(int64)
name 客户名称 false
string
createTimeStart 创建时间-起 false
string(date-time)
createTimeEnd 创建时间-止 false
string(date-time)
socialCreditCode 统一社会信用代码 false
string

5. 指定返回值结构

其返回数据结构为
success boolean
message string
errorCode string
data WebApiQueryDataCustomerWithInfo WebApiQueryDataCustomerWithInfo
list array CustomerWithInfo
id 客户 ID integer(int64)
supplierId 灵工平台 id integer(int64)
name 客户名称 string
sn 客户编号 string
shortName 客户简称 string
status 客户状态:1-未合作;2-合作中;3-停止合作 string
regionId 客户地区 id string
address 客户详细地址 string
industry 客户行业 string
type 客户性质 string
size 客户规模 string
source 客户来源 string
salesName 销售负责人姓名 string
serviceMobile 销售负责人电话 string
contactName 客户联系人姓名 string
contactMobile 客户联系人电话 string
remark 客户备注 string
disabled 是否禁用 boolean
userId 用户 ID integer(int64)
operationRoleIds 操作角色 string
enterpriseInfoId 企业信息 ID integer(int64)
enterpriseName 企业名称 string
enterpriseContacts 企业联系人 string
enterpriseContactPhone 企业联系电话 string
enterpriseRemark 企业备注 string
enterpriseAttachments 企业附件文件 ID 列表 string
businessLicenseImage 企业营业执照图片 id string
socialCreditCode 企业统一社会信用代码 string
representativeName 企业法定代表人姓名 string
certificateType 企业法定代表人证件类型 string
certificateNo 企业法定代表人证件号 string
certificateFrontImage 企业法定代表人证件正面照 string
certificateBackImage 企业法定代表人证件背面照 string
registeredAddress 企业注册地址 string
roleIds 角色 Id 列表 array integer
createTime string(date-time)
total integer(int64)

6. 指定 el-table 需要的值，从 prd ocr 或者复制出来

el-table 需要展示的列有：客户名称，客户详细地址，客户联系人姓名，创建时间，状态 操作

操作具有 编辑 禁用 启用 删除 其中删除需要二次确认

7. 后续再用 ai 迭代其他交互效果

如 删除，启用之类的

#### 示例 一个用户列表

仿照 src\pages\operateLabor\platform\corporations.vue 实现 src\pages\operateLabor\platform\users.vue
搜索条件又 用户名称/手机号码 角色名称
调用 client.supplierGetMembers 其请求参数为
{
"roleId": 0,
"memberId": 0,
"nameOrCellphone": ""
}
我不需要展开与收起，搜索条件仅需要 用户名称/手机号码 角色名称 其中 角色名称由 src\pages\operateLabor\platform\selector\roles.vue 提供
返回数据结构为
success boolean
message string
errorCode string
data array MemberVo
memberId integer(int64)
cellphone string
name string
disabled boolean
createTime string(date-time)
modifyTime string(date-time)
roles array RoleVo
id integer(int64)
code 角色编码 string
name 角色名称 string
remark 描述 string
createTime 创建时间 string(date-time)
modifyTime 更新时间 string(date-time)
disabled 是否禁用 boolean
members 成员 array MemberVo

el-table 需要展示的列有：
姓名
手机号
关联角色
更新时间
创建时间
状态
操作

其中关联角色 需要利用 roles 进行格式化，以逗号分隔展示角色名称。

操作具有 编辑 禁用 启用 删除 其中删除需要二次确认

## 其他交互效果后续再用 ai 迭代

补充 supplierUsers.vue 功能
禁用启用 使用 client.supplierDisableMember 其参数为
{
"id": 0,
"disabled": true
}
id 为当前用户 id
成功后，页面需要再次 load 列表禁用启用 使用 client.supplierDisableMember 其参数为{ "id": 0, "disabled": true}id 为当前用户 id 成功后，页面需要再次 load 列表

---

补充 supplierUsers.vue 删除功能
使用 client.supplierRemoveMember 其参数为
{
"id": 0,
"disabled": true
}

### 创建页

套路和列表页差不多，见示例

#### 示例

帮我实现 src\pages\operateLabor\platform\supplierUsersNew.vue 参考 src\pages\operateLabor\platform\rolesNew.vue

调用 client.supplierAddMember 请求参数为
{
"name": "",
"cellphone": "",
"roleIds": []
}

el-form 需要的字段为 名称， 手机号，角色 其中角色使用 src\pages\operateLabor\platform\selector\roles.vue 组件
名称和手机是必填的，角色不是必填的

这个页面同时需要承载编辑功能 使用 client.supplierEditMember 参数如下
{
"merberId": 0,
"name": "",
"cellphone": "",
"roleIds": []
}
编辑页面为/supplierUsers/:id/edit 即当有 id 传入时候，则是编辑，没有则是新增

因为没有获取详情的接口，编辑时候，用户名和手机号码以及 roleIds 需要通过 url 传入
你需要帮我从$route.query 中获取 name cellphone roleIds 并赋值到表单中，以计算属性呈现
注意 roleIds 是数字类型，需要转换

创建/编辑成功后， 给出提示， 返回/supplierUsers
