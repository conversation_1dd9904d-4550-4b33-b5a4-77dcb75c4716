# 前端工具箱

包含阿拉钉平台，与各类银行的适配，与各类第三方应用的适配。

核心是将所有功能收归一处，以功能区块组合出不同的应用。

## 约定

不使用 typescript

如果需要安装依赖，需要前端负责人审批

内联的 css 优先于 style 标签

优先采用 pnpm，当然也可以在本地使用 yarn npm 等，但是不要提交它们的 lock 文件

## 功能分离

主要 pages 中，为具体的页面。通过后缀区分不同的应用.

如 pages/index.vue 为阿拉钉主页
pages/indexCGB.vue 为广发主页（仅在有特殊定制时候才出现）

## IDE 与插件

推荐使用 VSCode，使用其他 IDE 请对规范自行适配。

### 必选插件

Vetur 用于处理 vue 自动完成相关事宜

Prettier 用于格式化代码，保证大家格式一致

GitLens 用于在 VSCode 中管理分支

Code Spell Check 避免写错英文单词

### 选用插件

Code Autocomplete 实际是 TabNine 的包装，用于自动提示

Codeium 编程辅助 AI，用于自动生成代码，和解决代码问题

部分需要翻墙，大家互相帮助下

## 单元测试

jest 测试原生 js 与 vue

vue 的主要测试辅助函数 请参考
https://v1.test-utils.vuejs.org/zh/

### 自动化脚本命令 

```base

# 扫描没有使用的组件
npm run checkNoUsedComponents

# 扫描没有使用的文件
npm run checkNoUsedFiles

# 格式化项目代码
npm run fmt 

```