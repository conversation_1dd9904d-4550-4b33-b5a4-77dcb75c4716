# 开发新需求时，请使用yarn run create 项目名称
## 项目名称：薪酬系统

## 主要工具介绍

| 工具名                  | 作用                                       |
| ----------------------- | ------------------------------------------ |
| vue v2.5                | data binding                               |
| vue-router v3           | vue 路由                                   |
| webpack v4              | 本地发开、打包发布                         |
| webpack-dev-server v3   | 本地启 mock 服务                           |
| webpack-bundle-analyzer | 打包后文件分析                             |
| koa v2                  | 本地模拟数据接口                           |
| axios                   | 处理 get/post                              |
| mockjs                  | 制造模拟数据                               |
| nodemon                 | 修改 node 代码不需要重启监听，一直运行就好 |

## 功能介绍

### 开发

1. koa 模拟数据接口，从而使前后端分离。
2. sourcemap，很好定位。
3. eslint，stylelint 强制部分代码规范。
4. 热替换。
5. nodemon 进程守护。
6. 使用 mockjs 模拟接口返回数据。
7. scss 支持识别 2 倍 3 倍图加载（除了接口获取的图，所有图都用背景图方案）
8. git hooks，防止不符合规范的提交

### 测试

1. 单元测试（含代码覆盖率）
2. e2e 测试

### 发布

1. 将第三方 js 库打包成 vendor.js，从而使项目业务代码修改也不会影响基本稳定的三方 js 代码，充分利用浏览器缓存。
2. 将 css 代码分为两份，一份为基础代码，即上线后几乎不变，另一份为迭代代码，从而充分利用浏览器缓存。
3. 发布以后的所有文件会加 md5 后缀，从而充分利用浏览器缓存。
4. tree shaking。
5. dynamic import，代码不会打包到一个 js 文件里，而会分成 n 个按需加载 js。

## 常用命令

### 开发

#### 1.模拟数据

`npm run mock`

#### 2.页面开发

`npm run dev`

#### 3.一起启动

`npm run dev:mock`
启动以后，可以在运行时，管理已配置的接口根据我们想要的状态，对统一地址、统一参数的接口返回不同数据。
管理接口地址：`{host:port}/mock-switch/`

#### 4.文档本地预览

components: `npm run doc:components`

### 测试

#### 1.单元测试

`npm run test:unit`

#### 2.e2e 测试

`npm run dev`
`npm run test:e2e`

### 代码大小分析

`npm run analyze`

### 发布

`npm run build`
