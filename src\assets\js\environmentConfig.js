const ipAr  = (...ips)=>{
    return "http://"+ips.join("")
}
const sso = {
    dev: ipAr("172.","19",".60.","188",":18010"),
    qa: ipAr("172.","19",".60.","38",":18010"),
    qa2: ipAr("172.","19",".60.","202",":18010"),
    grey: 'https://pre-stage-sso.olading.com',
    stage: 'https://stage-sso.olading.com',
    prod: 'https://olading.com'
}
const salary = {
    qa: ipAr("172.","19",".60.","38",":18490"),
    grey: 'https://pre-stage-xst.olading.com',
    stage: 'https://xsttest.olading.com',
    prod: 'https://xst.olading.com'
}
const approve = {
    qa: ipAr("172.","19",".60.","38",":10028"),
    qa2: ipAr("172.","19",".60.","202",":18025","/index"),
    grey: 'https://pre-stage-wf.olading.com',
    stage: 'https://stage-wf.olading.com',
    prod: 'https://wf.olading.com'
}
const hrSaasH5 = {
    qa: 'https://hr-saas-qa.lanmaoly.com',
    qa2: ipAr("172.","19",".60.","202",":18025","/index"), 
    grey: 'https://pre-h5-hrsaas.olading.com',
    stage: 'https://stage-h5-hrsaas.olading.com',
    prod: 'https://h5-hrsaas.olading.com'
}

export {
    sso,
    salary,
    approve,
    hrSaasH5
}