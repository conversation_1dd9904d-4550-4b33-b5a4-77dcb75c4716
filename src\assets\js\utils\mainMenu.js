export const mainMenu_base = [
  //员工管理
  {
    name: '员工管理',
    businessCode: 'hrEmployee.employee',
    parentId: 4,
    children: [
      {
        name: '花名册',
        businessCode: 'hrEmployee.employee.roster',
        parentId: 248,
        children: null,
        privileges: null,
      },
      {
        name: '入职管理',
        businessCode: 'hrEmployee.employee.entry',
        parentId: 248,
        children: null,
        privileges: null,
      },
      {
        name: '转正管理',
        businessCode: 'hrEmployee.employee.turnRegular',
        parentId: 248,
        children: null,
        privileges: null,
      },
      {
        name: '离职管理',
        businessCode: 'hrEmployee.employee.leave',
        parentId: 248,
        children: null,
        privileges: null,
      },
      {
        name: '异动管理',
        businessCode: 'hrEmployee.employee.changeOpt',
        parentId: 248,
        children: null,
        privileges: null,
      },
      {
        name: '员工关怀',
        businessCode: 'hrEmployee.employee.empcare',
        parentId: 248,
        children: null,
        privileges: null,
      },
      {
        name: '常用报表',
        businessCode: 'hrEmployee.employee.report',
        parentId: 248,
        children: null,
        privileges: null,
      },
      {
        name: '员工信息模板设置',
        businessCode: 'hrEmployee.employee.template',
        parentId: 248,
        children: null,
        privileges: null,
      },
      {
        name: '选项字典设置',
        businessCode: 'hrEmployee.employee.option',
        parentId: 248,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  //合同管理
  {
    name: '合同管理',
    businessCode: 'hrContract.conManage',
    parentId: 264,
    children: [
      {
        name: '劳动合同记录',
        businessCode: 'hrContract.conManage.laborContract',
        parentId: 265,
        children: null,
        privileges: null,
      },
      {
        name: '电子合同',
        businessCode: 'hrContract.conManage.eContract',
        parentId: 265,
        children: null,
        privileges: null,
      },
      {
        name: '合同模板',
        businessCode: 'hrContract.conManage.conTemplate',
        parentId: 265,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  //假勤管理
  {
    name: '假勤管理',
    businessCode: 'hrAttend.attendManage',
    parentId: 271,
    children: [
      {
        name: '考勤组管理',
        businessCode: 'hrAttend.attendManage.group',
        parentId: 272,
        children: null,
        privileges: null,
      },
      {
        name: '班次管理',
        businessCode: 'hrAttend.attendManage.work',
        parentId: 272,
        children: null,
        privileges: null,
      },
      {
        name: '补卡管理',
        businessCode: 'hrAttend.attendManage.supplement',
        parentId: 272,
        children: null,
        privileges: null,
      },
      {
        name: '加班管理',
        businessCode: 'hrAttend.attendManage.overTime',
        parentId: 272,
        children: null,
        privileges: null,
      },
      {
        name: '假期管理',
        businessCode: 'hrAttend.attendManage.leave',
        parentId: 272,
        children: null,
        privileges: null,
      },
      {
        name: '人脸管理',
        businessCode: 'hrAttend.attendManage.faceManager',
        parentId: 272,
        children: null,
        privileges: null,
      },
      {
        name: '每日统计',
        businessCode: 'hrAttend.attendManage.dailyCount',
        parentId: 272,
        children: null,
        privileges: null,
      },
      {
        name: '月度统计',
        businessCode: 'hrAttend.attendManage.monthlyCount',
        parentId: 272,
        children: null,
        privileges: null,
      },
      {
        name: '加班明细',
        businessCode: 'hrAttend.attendManage.overTimeDetail',
        parentId: 272,
        children: null,
        privileges: null,
      },
      {
        name: '打卡时间',
        businessCode: 'hrAttend.attendManage.signTime',
        parentId: 272,
        children: null,
        privileges: null,
      },

      {
        name: '原始记录',
        businessCode: 'hrAttend.attendManage.count',
        parentId: 272,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '绩效管理',
    businessCode: 'salary.performance',
    parentId: 4,
    children: [
      {
        name: '考核管理',
        businessCode: 'kpi.performance.plan',
        parentId: 3331,
        children: null,
        privileges: null,
      },
      {
        name: '个人绩效档案',
        businessCode: 'kpi.performance.archives',
        parentId: 3341,
        children: null,
        privileges: null,
      },
      {
        name: '岗位模板库',
        businessCode: 'kpi.performance.positionBank',
        parentId: 3351,
        children: null,
        privileges: null,
      },
      {
        name: '考核指标库',
        businessCode: 'kpi.performance.indicatorBank',
        parentId: 3361,
        children: null,
        privileges: null,
      },
      {
        name: '考核设置',
        businessCode: 'kpi.performance.planSetting',
        parentId: 3371,
        children: null,
        privileges: null,
      },
      {
        name: '目标地图',
        businessCode: 'kpi.performance.targetMap',
        parentId: 3392,
        children: null,
        privileges: null,
      },
      {
        name: '我的绩效',
        businessCode: 'kpi.performance.myPlan',
        parentId: 3381,
        children: null,
        privileges: null,
      },
      {
        name: '待我考核',
        businessCode: 'kpi.performance.myTodo',
        parentId: 3391,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '社保公积金',
    businessCode: 'salary.social',
    parentId: 4,
    children: [
      {
        name: '增减员',
        businessCode: 'salary.social.floatEmployee',
        parentId: 91,
        children: null,
        privileges: null,
      },
      {
        name: '参保月度台账',
        businessCode: 'salary.social.ledger',
        parentId: 91,
        children: null,
        privileges: null,
      },
      {
        name: '参保方案',
        businessCode: 'salary.social.insuredProject',
        parentId: 91,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '薪资核算',
    businessCode: 'salary.compute',
    parentId: 4,
    children: [
      {
        name: '薪资核算',
        businessCode: 'salary.compute.salaryCheck',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '薪资档案',
        businessCode: 'salary.compute.salaryArchive',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '计薪规则',
        businessCode: 'salary.compute.attendance',
        parentId: 6,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '智能代发',
    businessCode: 'salary.newpayroll',
    parentId: 4,
    children: [
      {
        name: '代发申请',
        businessCode: 'salary.newpayroll.apply',
        parentId: 331,
        children: null,
        privileges: null,
      },
      {
        name: '代发付款',
        businessCode: 'salary.newpayroll.pay',
        parentId: 331,
        children: null,
        privileges: null,
      },
      {
        name: '代发账户',
        businessCode: 'salary.newpayroll.account',
        parentId: 331,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '智能工资条',
    businessCode: 'salary.merchant',
    parentId: 4,
    children: [
      {
        name: '工资条管理',
        businessCode: 'salary.merchant.payStubs',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '工资条设置',
        businessCode: 'salary.merchant.setting',
        parentId: 6,
        children: null,
        privileges: null,
      },
    ],
  },
  {
    name: '个税申报',
    businessCode: 'salary.report',
    parentId: 4,
    children: [
      {
        name: '人员信息采集',
        businessCode: 'salary.report.personReport',
        parentId: 8,
        children: null,
        privileges: null,
      },
      {
        name: '专项附加扣除',
        businessCode: 'salary.report.additionl',
        parentId: 8,
        children: null,
        privileges: null,
      },
      {
        name: '综合所得申报',
        businessCode: 'salary.report.taxReport',
        parentId: 8,
        children: null,
        privileges: null,
      },
      {
        name: '分类所得申报',
        businessCode: 'salary.report.classify',
        parentId: 8,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '税款缴纳',
    businessCode: 'salary.taxpay',
    parentId: 4,
    children: [
      {
        name: '三方协议缴税',
        businessCode: 'salary.taxpay.paytax',
        parentId: 12,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '安全设置',
    businessCode: 'salary.auth',
    parentId: 4,
    children: [
      {
        name: '数据权限管理',
        businessCode: 'salary.auth.dataAuth',
        parentId: 188,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '初始化设置',
    businessCode: 'hrEmployee.init',
    parentId: 4,
    children: [
      {
        name: '法人实体管理',
        businessCode: 'hrEmployee.init.taxSubject',
        parentId: 14,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
];
