/* eslint-disable*/
export const validateTel = (rule, value, callback) => {
  //验证手机号
  if (value) {
    const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;

    if (value.length > 11) {
      callback(new Error('手机号不能多于11位'));
    } else if (value.length < 11) {
      callback(new Error('手机号不能少于11位'));
    } else if (!reg.test(value)) {
      callback(new Error('手机号输入不合规范，请输入正确的手机号码'));
    } else {
      callback();
    }
  } else {
    if (rule.required) {
      callback(new Error('请输入手机号'));
    } else {
      callback();
    }
  }
};

// 校验电话
export const validatePhone = (rule, value, callback) => {
  if (value) {
    let reg = new RegExp('[\\d-()\\s]{6,20}');
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('请输入正确的电话'));
    }
  } else {
    if (rule.required) {
      callback(new Error('请输入电话'));
    } else {
      callback();
    }
  }
};

//自定义数值字段校验
export const validateNumberDecimal = (rule, value, callback) => {
  if (value) {
    let reg = new RegExp('^(0|[1-9]\\d*)(\\s|$|\\.\\d{1,2}\\b)');
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('请输入正确数值'));
    }
  } else {
    if (rule.required) {
      callback(new Error('请输入数值'));
    } else {
      callback();
    }
  }
};

//验证密码
export const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'));
  } else {
    let reg = new RegExp(
      '(?!^(d+|[a-zA-Z]+|[~!@#$%^&*?]+|\\s+)$)^[w~!@#$%^&*?./]{6,20}$'
    );
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('6-20位字母、数字、字符组合'));
    }
  }
};

//验证码
export const validateCode = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入验证码'));
  } else {
    callback();
  }
};

//用户名
export const validUsername = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入用户名'));
  } else {
    callback();
  }
};

export const validateEmail = (rule, value, callback) => {
  //验证邮箱
  if (value === '') {
    callback(new Error('请输入邮箱'));
  } else {
    let reg = new RegExp(
      '^[A-Za-z\\d]+([-_.][A-Za-z\\d]+)*@([A-Za-z\\d]+[-.])+[A-Za-z\\d]{2,4}$'
    );
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('请输入正确的联系邮箱'));
    }
  }
};

export const validateEmail2 = (rule, value, callback) => {
  //验证邮箱
  if (!value) {
    if (rule.required) {
      callback(new Error('请输入邮箱'));
    } else {
      callback();
    }
  } else {
    let reg = new RegExp(
      '^[A-Za-z\\d]+([-_.][A-Za-z\\d]+)*@([A-Za-z\\d]+[-.])+[A-Za-z\\d]{2,4}$'
    );
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('请输入正确的联系邮箱'));
    }
  }
};

export const validateidCard = (rule, value, callback) => {
  //验证证件号码
  if (value === '') {
    callback(new Error('请输入证件号码'));
  } else {
    let reg = new RegExp('(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)');
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('请输入正确的证件号码'));
    }
  }
};

export const validateCompanyName = (rule, value, callback) => {
  //验证企业用户名
  if (value === '') {
    callback(new Error('请输入用户名'));
  } else {
    let reg = new RegExp(
      '(?!^(\\d+|[a-zA-Z]+|[~!@#$%^&*?]+|\\s+)$)^[\\w~!@#$%^&*?./]{6,20}$'
    );
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('6-20位字母、数字、字符组合'));
    }
  }
};

export const validateCheck = (rule, value, callback) => {
  //勾选协议
  if (value === false || value === '') {
    callback(new Error('请勾选协议'));
  } else {
    callback();
  }
};

export const validateIden = (rule, value, callback) => {
  //身份证号
  if (value === '') {
    callback(new Error('请输入身份证号'));
  } else {
    let reg = new RegExp('^\\d{17}[0-9x]$', 'i');
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('身份证号格式有误'));
    }
  }
};

export const validateNo = (rule, value, callback) => {
  //社会信用代码
  if (value === '') {
    callback(new Error('请输入社会信用代码'));
  } else {
    let reg = new RegExp('^[^_IOZSVa-z\\W]{2}\\d{6}[^_IOZSVa-z\\W]{10}$', 'g');
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('社会信用代码有误'));
    }
  }
};

export const validateSignPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入安全密码'));
  } else {
    let reg = new RegExp('\\d{6}$');
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('签署密码格式有误'));
    }
  }
};
