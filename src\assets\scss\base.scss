@charset "utf-8";
@import "./reset";
@import "./variables";

.old-setting p {
  font-size: 18px !important;
}

/*
系统共用样式
*/
body {
  background: #f7f8fd;
}

/* 强制文本换行 */
.textwrap,
.textwrap td,
.textwrap th {
  word-wrap: break-word;
  word-break: break-all;
}

.textwrap-table {
  table-layout: fixed;
}

textarea {
  resize: none !important;
}

/* Responsive images */
.a img {
  max-width: 100%;
}

.btn {
  border: none;
  padding: 0px 15px;
  line-height: 34px;
  height: 34px;
  outline: none;
  display: inline-block;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  border-radius: 4px;
  white-space: nowrap;
}

.btn:link,
.btn:visited,
.btn:hover,
.btn:active {
  color: #ffffff;
}

.btn[disabled="disabled"] {
  background: #d5d5d5 !important;
  opacity: 0.8;
  filter: alpha(opacity=80);
}

.btn:disabled {
  opacity: 0.8;
  filter: alpha(opacity=80);
}

input[disabled="disabled"],
textarea[disabled="disabled"] {
  background: #fff;
}

input:disabled,
textarea:disabled {
  background: #fff;
}

input[readonly="readonly"] {
  color: #777777;
}

input[type="text"],
input[type="password"],
input[type="tel"],
input[type="number"],
input[type="email"] {
  height: 40px;
  font-size: 14px;
  font-weight: 400;
  color: #333;
  border: none;
  border-radius: 5px;
  outline: none;
  vertical-align: middle;
  border: 1px solid #e6e8ec;
}

input::-webkit-contacts-auto-fill-button {
  visibility: hidden;
  display: none !important;
  pointer-events: none;
  position: absolute;
  right: 0;
  width: 0px;
  height: 0px;
}

::-moz-placeholder {
  color: #c6c6c6;
}

::-webkit-input-placeholder {
  color: #c6c6c6;
}

:-ms-input-placeholder {
  color: #c6c6c6;
}

input:-webkit-autofill {
  box-shadow: 0 0 0px 1000px white inset;
  -webkit-box-shadow: 0 0 0px 1000px white inset;
  outline: none;
}

textarea {
  resize: none;
  outline: none;
}

.hidden {
  display: none;
}

.left {
  float: left;
}

.right {
  float: right;
}

/*清除浮动*/
.clearfix:before,
.clearfix:after {
  content: "";
  display: table;
}

.clearfix:after {
  clear: both;
}

.clearfix {
  *zoom: 1;
}

/* ...隐藏 */
.ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
}

/*表单元素重定义*/
select {
  display: inline-block;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

select[disabled="disabled"],
select[disabled="true"] {
  background-color: #f1f1f1;
  color: #888888;
}

select.fullWidth {
  width: 100%;
}

/*去除IE浏览器中所有搜索框的清空icon*/
.keyword-search input[type="text"]::-ms-clear {
  display: none;
}

// el-form-item required样式
.required {
  .el-form-item__label {
    &:before {
      content: "*";
      color: #ff4949;
      margin-right: 4px;
    }
  }
}

.required {
  &:before {
    content: "*";
    color: #ff4949;
    margin-right: 4px;
  }
}

.display-flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

.flex1 {
  -webkit-flex: 1;
  /* Chrome */
  -moz-box-flex: 1;
  /* Firefox 19- */
  -ms-flex: 1;
  /* IE 10 */
  flex: 1;
  /* NEW,  Opera 12.1, Firefox 20+ */
}

a {
  color: $mainColor;
}

//仲裁参与和案件管理样式
.taba,
.tabb {
  width: 80%;
  margin: 10px auto;

  .adds {
    text-align: right;
    margin-top: 20px;
  }

  .details {
    margin: 20px 0 0 0;
    padding: 20px 40px;
    background: #fff;
  }

  .zc_tab {
    text-align: center;

    .titles {
      color: #2b2c30;
      font-size: 22px;
      margin: 20px auto;
      font-weight: bold;
    }

    .tabs {
      padding: 40px;
      background: #fff;
    }

    .pages {
      margin-top: 30px;
    }
  }
}

.card-box {
  padding: 14px;
  background: #fff;
}

.line {
  height: 1px;
  border: none;
  background-color: #e5e5e5;
  width: 96%;
  margin: 0 auto;
}

.fs-container {
  .mine-title {
    // border-bottom:1px solid #ddd;
    line-height: 40px;
    overflow: hidden;

    .title {
      font-size: 18px;
      font-weight: 500;
    }
  }
}

.el-icon-success {
  color: #06b806;
}

.el-icon-warning {
  color: #FF9500;
}

.el-icon-error {
  color: #f56c6c;
}

.el-icon-success,
.el-icon-warning,
.el-icon-error {
  display: inline-block;
  margin-right: 10px;
}

.search-wapper {
  padding-top: 20px;
  border: 1px solid #ddd;
  margin: 20px 0;

  .el-form-item {
    label {
      min-width: 120px;
      display: inline-block;
    }
  }
}

.el-range-editor.el-input__inner {

  //height: 34px;
  //margin-left: 4px;
  input {
    width: 120px;
  }
}

.el-date-editor--daterange.el-input,
.el-date-editor--daterange.el-input__inner,
.el-date-editor--timerange.el-input,
.el-date-editor--timerange.el-input__inner {
  width: 330px;
  overflow: auto;
}

.el-table th>.cell {
  font-size: 13px;
}

//更多
.more-choose {
  color: $mainColor;
  cursor: pointer;
}

.more-style {
  color: #4F71FF;
  cursor: pointer;
  line-height: 30px;
  height: 30px;
}

.el-popover {
  min-width: 140px !important;
  text-align: center !important;
}

.clear-date {
  display: inline-block;
  margin-top: 8px;
  color: #4F71FF;
  cursor: pointer;
  width: 80px;
  height: 40px;
}

.display-flex .contract-header {
  margin: 0 20px !important;
  padding: 0 !important;
}

.display-flex .full-screen .contract-header {
  margin: 0 !important;
  padding: 0 20px !important;
}

.main-title {
  background-color: #fff !important;
  font-size: 16px;
  color: #070F29 !important;
  margin: 0 20px;
  height: 49px;
  line-height: 49px;
  border-bottom: 1px solid #ededed;
}

.main-content {
  padding: 0 20px;
}

.tax-collect-tips {
  background: #fff6e2;
  padding-left: 20px;
  height: 40px;
  font-size: 12px;
  line-height: 40px;
  color: #666;
}

.cumulative {
  .tax-collect-tips {
    margin-left: -20px;
  }
}

.collection {

  .el-select,
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 300px;
  }
}

.back-style {
  display: inline-block;
  cursor: pointer;
  color: $mainColor;
}

.floating-menu {
  z-index: 120;
  padding: 9px 0;
  position: absolute;
  top: 0;
  left: 46px;
  line-height: 31px;
  background: #fff;
  width: 97%;
  text-indent: 20px;

  span {
    margin: 0 10px;
  }

  .button-mini {
    padding: 7px 3px;
    border-radius: 3px;
  }
}

.dialog-cap_test {
  width: 80px;
  cursor: pointer;
  height: 34px;
  vertical-align: middle;
  display: inline-block;
}

//页面内容主体
.page-module {
  .div-module {
    background: #fff;
    //margin-bottom: 20px;
    padding: 20px 20px 16px 20px;
    box-sizing: border-box;
    border-radius: 4px;
    -moz-box-shadow: 3px 5px 15px #c3d0d9;
    box-shadow: 5px 10px 15px #c3d0d9;
  }
}

//分页组件
.pages {
  text-align: right;
  margin: 10px 0;
}

//提示介绍
.intro {
  background: #F6F8FF;
  color: #888888;
  padding: 20px;
  margin-bottom: 10px;
  border-radius: 3px;

  .intro-title {
    color: #000;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 14px;
  }

  .intro-cnt {
    line-height: 1.8;
  }
}

//隐藏属性
.width-fixed {
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 150px;
}

// .el-button--mini {
//   padding: 0px 4px !important;
//   font-size: 14px !important;
//   border-radius: 3px !important;
// }

//返回样式右侧条
.header-line {
  display: inline-block;
  padding: 0 10px;
}

//radio 添加右边距
.radio-all {
  margin-right: 40px;
  border-radius: 4px;
}

.radio-right {
  .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 4px !important;
  }
}

.radio-all-right .el-radio-button__inner {
  border-left: 1px solid #dcdfe6;
  border-radius: 4px 0px 0px 4px;
}

//并列日期
.date-picker-right {
  .el-form-item__content {
    margin-left: 0px !important;
  }
}

.payback-create-dialog {
  .el-date-editor.el-input {
    width: 140px;
  }
}

//右弹窗样式
.drawer-title {
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #f1f1f1;
  padding-bottom: 10px;
  text-align: left;
  margin: 0px 20px;
  color: #333;
  font-size: 18px;
}

.el-drawer__body {
  overflow-y: auto;
}

.salary-employeetype {
  .el-checkbox {
    margin-right: 20px;
  }
}

// 登录公共样式
.login-warp {
  width: 620px;
  margin: 80px auto 0 auto;
  box-sizing: border-box;

  .login-form {
    width: 400px;
    margin: 20px auto 0;

    .sendMsg-item {
      img {
        width: 135px;
        height: 40px;
        cursor: pointer;
      }
    }
  }

  .agreement {
    .change {
      color: #2e3039;
    }

    .agreement-text {
      color: $mainColor;
      cursor: pointer;
    }
  }
}

// 对话框特殊样式
.check-shift .el-dialog__body {
  padding: 0 10px 20px;
}

.special-dialog {
  margin: 0 auto;
}

//绩效
.def_per_section {
  padding: 0 20px;
}

.def_per_section-top {
  margin-top: 20px;
}

.def_per_height {
  //height: calc(100vh - 80px);
}

.def_per_alignItems {
  display: flex;
  align-items: center;
}

.def_per_justifyContent {
  display: flex;
  justify-content: center;
}

.def_per_leftRight {
  display: flex;
  flex-direction: row;
}

.def_per_TopBottom {
  display: flex;
  flex-direction: column;
}

.def_per_flexDirectionRow {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
}

.def_tips {
  // max-width: 60%;
  // line-height: 180%;
}

.def_auto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.def_edit-process-radius {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

// 特殊确认框样式调整
.special-confirm {
  .el-message-box__status {
    display: flex;
    align-items: flex-start;
    height: 100%;

    &.el-icon-warning {
      font-size: 16px !important;
      color: #FF9500;
      margin-top: 4px;
    }
  }

  .el-message-box__message {
    padding-left: 21px !important;
  }
}

.pagination {
  clear: both;
  float: none !important;
  text-align: right;
}

.f-c {
  display: flex;
  align-items: center;
  justify-content: center;
}

.def-p-t-16 {
  padding-top: 16px;
}

.def-p-b-16 {
  padding-bottom: 16px;
}

.def-m-t-16 {
  margin-top: 16px;
}

.def-m-b-16 {
  margin-bottom: 16px;
}

.def-m-b-10 {
  margin-bottom: 10px;
}