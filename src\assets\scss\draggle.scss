//-----------自定义拖动控件样式----start---------------
@import "base";
.my-handle-class {
  position: absolute;
  background-color: pink;
  border: 1px solid black;
  border-radius: 50%;
  height: 10px;
  width: 10px;
  box-model: border-box;
  -webkit-transition: all 300ms linear;
  -ms-transition: all 300ms linear;
  transition: all 300ms linear;
}

.my-handle-class-tl {
  top: -10px;
  left: -10px;
  cursor: nw-resize;
}

.my-handle-class-tm {
  visibility: hidden;
  top: -10px;
  left: 50%;
  margin-left: -7px;
  cursor: n-resize;
}

.my-handle-class-tr {
  visibility: hidden;
  top: -10px;
  right: -10px;
  cursor: ne-resize;
}

.my-handle-class-ml {
  top: 50%;
  margin-top: -7px;
  left: -10px;
  cursor: w-resize;
}

.my-handle-class-mr {
  top: 50%;
  margin-top: -7px;
  right: -10px;
  cursor: e-resize;
}

.my-handle-class-bl {
  bottom: -10px;
  left: -10px;
  cursor: sw-resize;
}

.my-handle-class-bm {
  bottom: -10px;
  left: 50%;
  margin-left: -7px;
  cursor: s-resize;
}

.my-handle-class-br {
  bottom: -10px;
  right: -10px;
  cursor: se-resize;
}

.my-handle-class-tl:hover,
.my-handle-class-tr:hover,
.my-handle-class-tm:hover,
.my-handle-class-ml:hover,
.my-handle-class-mr:hover,
.my-handle-class-bl:hover,
.my-handle-class-bm:hover,
.my-handle-class-br:hover {
  background-color: $mainColor;
  transform: scale(1.3);
}
