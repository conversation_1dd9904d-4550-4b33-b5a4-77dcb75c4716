@import "./helpers.scss";

:root {
  .el-input__prefix {
    width: 25px !important;
  }

  .el-button--text,
  .old-table .click-cell,
  .el-tacitly {
    color: $mainColor !important;
  }

  // .el-icon--right {
  //   color: inherit;
  // }

  .el-message-box {
    padding-bottom: 15px !important;
  }

  .el-message-box__status+.el-message-box__message {
    padding-left: 30px;
  }

  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: $mainColor !important
  }

  .el-button--text,
  .old-table .click-cell,
  .el-tacitly {
    color: $mainColor !important;
  }

  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: $mainColor !important
  }


  .el-menu-vertical-demo {
    width: 223px;
    height: auto;
    background: #4a535e;
    min-height: 85.2vh;
    overflow: hidden;

    .el-menu {
      border: none;
      background: #4a535e;
    }

    .el-menu-item:hover,
    .el-submenu__title:hover {
      color: #fff;
      background: #4a535e;
    }

    .el-menu-item.is-active {
      background: #404a56;
      border-left: 2px solid;
      color: #98a2ad;
    }

    .el-menu-item:focus,
    .el-submenu__title:focus .el-menu-item:hover {
      background: #556774;
    }

    .el-menu-item,
    .el-submenu__title {
      width: 100%;
      height: 55px;
      border-left: 2px solid #4a535e;
      color: #929292;
      padding-left: 40px !important;
      font-size: 16px;

      .iconfont {
        margin-right: 10px;
      }
    }

    .el-menu--inline {
      .el-menu-item {
        padding-left: 70px !important;
      }
    }
  }

  //薪资核算步骤条
  .sflary-el-step {
    .el-step__main {
      color: #999;
    }

    .el-step__title {
      font-size: 14px;
    }

    .el-step__head.is-process {
      color: $mainColor;

      .el-step__icon.is-text {
        border-color: $mainColor;
      }
    }

    .el-step__title.is-process {
      color: $mainColor;
    }
  }

  //公共弹窗样式
  // .diy-el_dialog {
  //   border-radius: 6px;

  //   .el-form-item__content {
  //     height: 40px;
  //   }

  //   .el-form-item {
  //     margin-bottom: 15px;
  //   }

  //   .diy-el_radio {
  //     margin-top: 20px;
  //     margin-left: 18px;
  //   }

  //   .headings {
  //     font-weight: 500;
  //     font-size: 14px;
  //     margin-bottom: 10px;
  //   }

  //   .select-file {
  //     margin-top: 20px;

  //     p {
  //       margin-left: 28px;
  //       margin-top: 15px;

  //       span {
  //         cursor: pointer;
  //         margin-left: 20px;
  //         color: #ff8f2c;
  //       }
  //     }
  //   }
  // }

  // 公共弹窗样式优化
  .diy-el_dialog {
    .diy-el_radio {
      margin-top: 5px;
      margin-left: 28px;
      margin-bottom: 20px;

      /deep/ .el-radio__label {
        color: #46485A !important;
      }
    }

    .select-file {
      margin-top: 10px;

      p {
        margin-left: 32px;
      }

      .explain {
        margin-top: 6px;
      }

      span {
        cursor: pointer;

        img {
          margin-left: 10px;
          width: 74px;
          height: 16px;
          vertical-align: middle;
        }
      }

      .avatar-uploader {
        margin-left: 32px;
        margin-top: 20px;
      }
    }

    /deep/ .el-step__icon.is-text {
      width: 18px;
      height: 18px;
    }

    .diy-export {
      .download {
        margin-left: 20px;
      }

      a {
        img {
          width: 16px !important;
          height: 16px;
          vertical-align: middle;
        }

        span {
          display: inline-block;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #4F71FF;
          letter-spacing: 0;
        }
      }
    }
  }

  //element table样式
  .el-table th,
  .el-table td {
    // text-align: center !important;
  }

  .el-date-editor.el-input {
    width: 140px;
  }

  .social-increace-dialog {
    .el-date-editor.el-input {
      width: 310px;
    }

    .provident-month .el-date-editor.el-input {
      width: 140px;
    }
  }

  .el-radio {
    margin: 10px !important;
  }

  .zui-dialog{
    .el-dialog__header{
      margin:0;
    }
  }

  .zui-select-user-dialog{
    .el-radio{
      margin: 0 !important;
    }
    .search-input .el-input__inner{
      padding-left:15px ;
    }
  }

  .el-submenu .el-menu-item {
    min-width: 170px;
  }

  .el-button--primary {
    border: 1px solid $mainColor;
    // font-size: 14px;
  }

  // .el-dropdown {
  //   .el-button {
  //     padding: 9px 0px;
  //   }
  // }

  .search-input {
    width: 280px;

    .el-input__inner {
      padding: 0 30px;
    }

    .el-input__icon {
      line-height: 40px;
    }
  }

  .search-box {
    .el-input-group__append {
      background-color: $mainColor;
      color: #fff;
    }
  }

  .el-dropdown-menu__item.current-tab-sub_name {
    @include ellipsis;
    width: 200px;
    display: block;
  }

  .el-month-table td.today .cell {
    color: #606266;
    font-weight: normal;
    padding: 0px;
  }

  .salarySet-page {
    .el-step__head.is-finish {
      color: #303133 !important;
      border-color: #303133 !important;
    }

    .el-step__title.is-finish {
      color: #303133 !important;
    }
  }

  //超出部分隐藏
  .hiden-con {
    display: inline-block;
    width: 150px;
    overflow: hidden;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis !important;
    text-align: left;
  }

  .funStyle {
    color: #4F71FF;
    cursor: pointer;
    // padding: 0px 10px;
    padding-right: 10px;
    font-size: 14px;
  }

  .disable-table-name {
    color: #c0c4cc;
  }

  .paid-eidt {
    .el-checkbox__input.is-checked+.el-checkbox__label {
      color: #606266;
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #606266;
      border-color: #606266;
    }
  }

  .el-step.is-simple:not(:last-of-type) .el-step__title {
    max-width: 60%;
    word-break: break-all;
  }

  .el-table td .number-right {
    text-align: right;
  }

  //表头与内容不对齐问题
  .el-table th.gutter {
    display: table-cell !important;
  }

  .input-bigger {

    .el-select,
    .el-input {
      width: 320px !important;
    }
  }

  .data-create {
    .el-form-item {
      border-bottom: 1px solid #eee;
      padding: 4px 0px;
      margin-bottom: 0px;
    }

    .el-form-item__label {
      padding: 0 30px 0 0;
    }
  }

  .taxPaid {
    .el-radio__label {
      display: none;
    }
  }

  // .v-modal {
  //   z-index: 2000 !important;
  // }
  .notification {
    .check-tip {
      margin-top: 4px;

      .el-checkbox__label {
        font-size: 12px;
      }
    }
  }

  .attrition-detail,
  .employee-detail,
  .add-employee,
  .insured-account .screen-dialog {

    .el-input--suffix .el-input__inner,
    input[type="text"] {
      width: 300px;
    }

    .el-date-editor.el-input {
      width: 280px;
    }

    .provident-month {
      .el-input--suffix .el-input__inner {
        width: 170px;
      }

      .el-date-editor.el-input {
        width: 168px;
      }
    }
  }

  .company-change {
    .el-input--prefix .el-input__inner {
      padding: 14px 30px;
    }
  }

  .drop-down {
    margin: 10px 0px;

    input[type="text"] {
      border: none;
      text-align: center;
    }
  }

  .input-right {
    input[type="text"] {
      text-align: right;
    }
  }

  .el-autocomplete-suggestion {
    // display: none;
  }

  .salary-area,
  .un-need-salary {
    input[type="text"] {
      height: 28px;
    }

    .el-tabs__item {
      padding: 0 10px;
    }

    .input-style {
      font-size: 13px;
    }
  }

  // 选择服务页面头部样式
  .main-header {
    .el-input__inner {
      height: 32px;
      display: flex;
      align-items: center;
      line-height: 32px;
      font-size: 14px;
      width: 192px;
      border-radius: 100px;
    }

    .el-select .el-input .el-select__caret {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .employee-more-operation .el-button {
    padding: 9px 14px 9px 20px;
  }

  // 提取了表格和分页的公共样式
  .staff-table {
    position: relative; //批量选择定位需要

    .el-table__empty-block {
      width: 100% !important;
    }

    .check-staff_table {
      overflow-x: auto;
    }

    .staff-page {
      margin: 10px 0;
      text-align: right;
    }

    .table-name {
      color: $mainColor;
      cursor: pointer;
    }
  }

  .table-name {
    color: $mainColor;
    cursor: pointer;
  }

  // 提取了筛选和操作按钮公共样式
  .check-staff-menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;

    .search-input {
      margin-right: 10px;
    }

    >div {
      display: flex;
      align-items: center;
    }

    .more-operation {
      margin: 0 0 0 10px;

      // /deep/.el-button {
      //   padding: 9px 14px 9px 20px;
      //   span {
      //     display: flex;
      //     align-items: center;
      //   }
      // }
      .iconsanjiao {
        font-size: 22px;
      }
    }
  }

  // 公共弹窗样式
  .regular-dialog {

    .el-select,
    .el-input,
    .el-textarea {
      width: 250px;
    }
  }

  // 详情表单样式
  .detail-form {

    // .base-info-con,
    // .insured-info-con {
    //   width: 1000px;
    //   margin: 0 auto;
    // }
    .info-title {
      font-weight: 500;
      font-size: 16px;
      margin: 20px;
      line-height: 18px;
      display: flex;
      align-items: center;
    }

    .info-title::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 18px;
      background-color: $mainColor;
      border-radius: 3px;
      margin-right: 8px;
    }

    .save-box {
      border-top: 1px solid #e5e5e5;
      text-align: center;
      margin-top: 10px;

      .el-button {
        margin: 20px 0;
      }
    }
  }

  .confirm-small {
    font-size: 12px !important;
  }

  // 员工管理提示部分样式
  .staff-manage {
    //height: calc(100vh - 80px);

    .content-con {
      padding: 0 20px;

      .company {
        margin-bottom: 10px;

        /deep/ input[type="text"] {
          border: none !important;
        }
      }

      .tip-con {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20px;

        p {
          display: flex;
          align-items: center;

          i {
            font-size: 18px;
            margin-right: 5px;
          }
        }

        .operate {
          color: $mainColor;
          cursor: pointer;
        }
      }

      .tip-text {
        i {
          color: #999;
          font-size: 16px;
        }
      }
    }

    .tab-con {
      font-size: 16px;
      border-radius: 25px;
      border: 1px solid $mainColor;
      background: #fff;
      // width: 404px;
      margin: 20px auto 0 auto;
      box-sizing: border-box;

      >span {
        cursor: pointer;
        display: inline-block;
        padding: 10px 35px;
      }

      .active {
        color: #fff;
        // background: $mainColor;
        background: $mainColor;
        border-radius: 23px;
      }
    }
  }

  .depart-con {
    display: flex;
    align-items: center;

    .el-input {
      width: 200px;
    }

    input[type="text"] {
      border: none;
    }
  }

  .upload-disable {
    .el-upload--picture-card {
      display: none;
    }
  }

  .defaultBg {
    .el-upload {
      background-size: 100% 100%;
    }
  }

  .defaultBg {
    display: flex;

    .el-upload {
      width: 80px;
      height: 60px;
    }

    .el-upload--picture-card {
      line-height: 70px;

      .el-upload-list__item {
        float: left;
        width: 80px;
      }
    }

    .el-upload-list--picture-card .el-upload-list__item {
      width: 80px;
      height: 60px;
    }
  }

  //部门树
  .depart-tree-con {
    display: flex;
    margin: 10px 0;

    .select-con {
      margin-left: 10px;
      cursor: pointer;
    }

    .reset {
      margin-left: 50px;
    }
  }

  .tree-popper {
    .el-tree {
      margin-top: 10px;
    }

    // .el-tree-node:focus > .el-tree-node__content {
    //   background-color: transparent !important;
    //   color: $mainColor;
    // }
  }

  .screening-select {
    margin-bottom: 20px;

    span {
      display: inline-block;
      text-align: center;
      border: 1px solid #dcdfe6;
      /*margin-right: px;*/
      font-size: 12px;
      padding: 0 15px;
      cursor: pointer;
      line-height: 30px;
    }

    span:first-child {
      border-radius: 4px;
    }

    .right-span:nth-child(2) {
      border-left: 1px solid #dcdfe6;
      border-radius: 4px 0px 0px 4px;
      margin-left: 10px;
    }

    .right-span:last-child {
      border-left: none;
      border-radius: 0px 4px 4px 0px;
    }

    .active {
      color: #fff;
      border: 1px solid $mainColor;
      background: $mainColor;
    }
  }

  .el-select__input {
    border: none !important;
  }

  // 营业执照样式
  .businessLicenseBg {
    .el-upload {
      background: url("../images/businessLicenseBg.png") no-repeat center center;
      background-size: 100% 100%;
    }
  }

  .idCardCopy {
    .el-upload {
      background: url("../images/copyBg.png") no-repeat center center;
      background-size: 100% 100%;
    }
  }

  .el-upload--picture-card {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-upload-list--picture-card .el-upload-list__item {
    width: 100%;
    height: 100%;
  }

  .el-upload-list__item.is-ready,
  .el-upload-list__item.is-uploading {
    display: none;
  }

  // 抽屉组件样式覆盖
  .el-drawer {
    :focus {
      outline: 0;
    }
  }

  .el-drawer__container:focus {
    outline: none;
  }

  .el-drawer:focus {
    outline: none;
  }

  .el-drawer__header:focus {
    outline: none;
  }

  .el-icon-info {
    color: #606266;
  }

  .el-tabs__item:focus.is-active.is-focus:not(:active) {
    box-shadow: none;
  }

  .tip-warning-con {
    height: 36px;
    line-height: 36px;
    background: #fff2e0;
    border-radius: 4px;
    border: 1px solid #ffcc86;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    box-sizing: border-box;
    color: #555555;

    p {
      display: flex;
      align-items: center;
    }

    .el-icon-warning-outline {
      font-size: 20px;
      color: #ffcc86;
      margin-right: 5px;
    }

    .el-icon-close {
      font-size: 20px;
      cursor: pointer;
    }
  }

  // element弹窗样式
  .el-dialog {
    border-radius: 8px;
  }

  .el-dialog__header {
    border-bottom: 1px solid #eaeaea;
    padding: 16px 0;
    margin: 0 24px;

    .el-dialog__title {
      display: inline-block;
      height: 24px;
      font-size: 16px;
      color: #070f29;
    }
  }

  .el-dialog__body {
    padding: 24px;
    color: #070f29;
  }
}

.el-dialog__body {
  padding: 24px;
  color: #070f29;
}

.el-dialog__footer {
  // border-top: 1px solid #eaeaea;
  padding: 20px 0 !important;
  margin: 0 24px;
}

.el-radio,
.el-checkbox {
  color: #555555;
}

.el-radio__input.is-checked+.el-radio__label,
.el-checkbox__input.is-checked+.el-checkbox__label {
  color: #555555;
}

// 步骤条样式
.el-step.is-horizontal .el-step__line {
  height: 1px !important;
}

.record-poper {
  margin-top: 0px !important;
  padding: 10px 0 !important;
}

.margin-top-10 {
  margin-top: 10px;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: $mainColor;
  background: $mainColor;
  background-clip: content-box !important;
  padding: .2em;
}

.el-radio__input.is-checked .el-radio__inner::after {
  background: none;
}

.tree-cascader .el-cascader-panel .el-radio {
  display: none;
}

.tree-cascader .el-cascader-node__label {
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.el-tabs__nav-wrap::after {
  display: none;
}

.el-tooltip__popper {
  max-width: 30%;
  line-height: 20px !important;
}

.cancel-btn {
  color: #777C94 !important;
}