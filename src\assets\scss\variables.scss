@charset "UTF-8";
$pageBarWidth: 78px;
@import 'root';

//主题色
// $mainColor:#4F71FF;
$mainColor:var(--color-primary);
$successGreen:#3daea7; //成功绿
$errorRed:#fc2736; //失败红
$bodyBgGrey:#f7f8fd;


//文字颜色
$textLight:#888888;
$textDark:#555555;
//内容区文本
$textMainHeaderColor: #dfdfdf; //主标题
$textViceHeaderColor: #9e9ea6; //副标题
$textMainColor: #555459; //主文本
$textViceColor: #717274; //副文本
$textTipColor: #c6c6c6; //提示（input后跟着的提示）

//元素状态
$optionColor: #9e9ea6; //select option hover色
$inputBgColor: #f7f7f7; //input背景色

//按钮颜色
//容器颜色
$lineBorderPointer:#FF8F2C;
$lineBorderColor: #e0e0e0; //线边框
//其他颜色
$errorBg:#ffe9eb;
$attentionColor:#ff7044; //着重色
$lineColor : #d5d5d5; //分割线色
//容器边距
//尺寸