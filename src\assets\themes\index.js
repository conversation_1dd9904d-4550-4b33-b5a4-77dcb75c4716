import defaultProperties from './default'
import red from './red'
import orange from './orange'
import boss from './boss'

const themes = { default: defaultProperties, red, orange, boss }

export function setTheme(theme) {
  if (!theme && window.env && window.env.theme) {
    theme = window.env.theme
  }
  if (!theme && (!window.env || !window.env.theme)) {
    theme = 'default'
  }

  if (!theme) {
    throw new Error('theme is required')
  }

  const properties = themes[theme]
  console.log(themes[theme], 'themes[theme]')
  console.log(theme, 'theme')
  if (!properties) {
    throw new Error('theme css properties is required')
  }

  console.log('set theme to', theme)

  const body = document.body

  body.style.setProperty('--o-primary-color', properties.primary)
  body.style.setProperty('--o-primary-color-hover', properties.primaryHover)
  body.style.setProperty('--o-primary-color-active', properties.primaryActive)
}
