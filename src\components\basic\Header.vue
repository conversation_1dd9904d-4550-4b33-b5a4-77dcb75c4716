<template>
  <old-header
    v-if="apiUrl"
    :token="token"
    :apiUrl="apiUrl"
    :environment="environment"
    :jump-list="mapUrl"
    :isCustomLogic="true"
    :headerSourceFlag="true"
    :headerBackground="headerBg"
    headerUrl=""
    :userProfile="userProfile"
    @exitLogin="exitLogin"
    systemName="人事管理"
  />
</template>
<script>
import * as AT from "@/store/actionTypes";
export default {
  data() {
    return {
      environment: window.env.server_env,
      headerBg: "#" + (window.env.theme || "4f71ff"),
      token: window.sessionStorageOther.getItem("token"),
      apiUrl: window.env.requestUrl,
      userProfile: window.env.theme
        ? require("../../assets/images/user_c81930.png")
        : require("../../assets/images/user-profile.png"),
      mapUrl: window.env.mapUrl
    };
  },
  created() {
    // switch (window.env.theme) {
    //   case "c81930":
    //     this.userProfile = require("../../assets/images/user_c81930.png");
    //     break;
    //   case "b6002a":
    //     this.userProfile = require("../../assets/images/user_c81930.png");
    //     break;
    //   default:
    //     this.userProfile = require("../../assets/images/user-profile.png");
    //     break;
    // }
  },
  methods: {
    // 退出登录
    exitLogin() {
      this.$store.commit(AT.EDITQUIT);
      window.open(`${window.env.mapUrl['work']}login`, '_self')
    }
  }
};
</script>
<style>
/* .old-menu li .el-dropdown-link,
.old-menu li .old-more {
  color: #070f29 !important;
}
.old-menu li .el-dropdown-link.old-active:after {
  background: #070f29 !important;
}
.cha {
  color: red;
} */
</style>
