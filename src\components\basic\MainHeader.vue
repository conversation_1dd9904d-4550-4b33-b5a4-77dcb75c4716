<template>
  <div class="main-header">
    <div class="select_account flex1">
      <i class="iconfont iconmorentouxiang old-head_portrait"></i>
      <el-input
        v-if="getMerchantId"
        v-model="relevantMerchantVos[0].name"
        readonly
      ></el-input>
      <el-select
        v-model="searchForm.merchantId"
        placeholder="请选择"
        @change="handleToggleMerchant"
        clearable
        v-else
      >
        <el-option
          v-for="item in relevantMerchantVos"
          :key="item.merchantId"
          :label="item.name"
          :value="item.merchantId"
        ></el-option>
      </el-select>
    </div>
    <div class="logo flex1">
      <img src="../../assets/images/logo-yongyou.png" alt />
    </div>
    <div class="header-icon flex1">
      <span
        class="icon084tuichu iconfont"
        title="退出登录"
        @click="handleQuitOlading"
      ></span>
    </div>
  </div>
</template>
<script>
import * as AT from "@/store/actionTypes";
import * as ACT from "@/modules/login/store/actionTypes";
import { mapState } from "vuex";
export default {
  components: {},
  computed: {
    ...mapState({
      merchantId: "merchantId",
      loginStore: "loginStore"
    }),
    getMerchantId() {
      // if (this.relevantMerchantVos.length !== 0) {
      //     if (!this.merchantId && this.merchantId !== null) {
      //         this.searchForm.merchantId = this.relevantMerchantVos[0].merchantId;
      //     } else {
      //         this.searchForm.merchantId = this.merchantId;
      //     }
      // }
      if (
        (this.loginStore.userType === "ENTERPRISE" ||
          !this.homeInfo.operator) &&
        this.relevantMerchantVos.length > 0
      ) {
        return true;
      }
    }
  },
  data() {
    return {
      isList: false,
      searchForm: {
        merchantId: ""
      },
      consultingShow: {
        isshow: false
      },
      popShow: {
        isshow: false
      },
      relevantMerchantVos: [],
      homeInfo: {}
    };
  },
  created() {
    this.$store.dispatch("selectServerStore/actionBindInfo").then(res => {
      if (res.success) {
        this.$store.commit(AT.SETBINDINFO, res.data.relevantMerchantVos);
        this.$store.commit(AT.SETHOMEINFO, res.data);
        this.relevantMerchantVos = res.data.relevantMerchantVos;
        this.searchForm.merchantId = this.relevantMerchantVos[0].merchantId;
        this.homeInfo = res.data;
        this.$nextTick(() => {
          this.handleToggleMerchant();
        });
      }
    });
  },
  methods: {
    //切换企业
    handleToggleMerchant(e) {
      this.$store
        .dispatch("homePageStore/actioncChangeMerchant", this.searchForm)
        .then(res => {
          this.$store.dispatch("loginStore/acProfile");
          this.$store.commit(AT.SET_TOKEN, res.data);
          this.$store.dispatch("selectServerStore/actionBindInfo").then(res => {
            if (res.success) {
              this.$store.commit(AT.SETBINDINFO, res.data.relevantMerchantVos);
              this.$store.commit(AT.SETHOMEINFO, res.data);
            }
          });
        });
    },
    //退出
    handleQuitOlading() {
      this.$confirm("是否退出？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false
      }).then(() => {
        this.$store.commit(AT.EDITQUIT);
        this.$router.replace({
          path: "/login"
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.main-header {
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  .header_button {
    position: relative;
    top: -1px;
    button {
      height: 25px;
      line-height: 23px;
      margin-left: 10px;
      border-radius: 100px;
    }
  }
  img {
    height: 50px;
  }

  .logo {
    justify-content: center;
    position: relative;
    top: 0px;
    display: flex;
    cursor: pointer;
  }

  .select_account {
    display: flex;
    align-items: center;
    position: relative;
    left: 32px;

    .old-head_portrait {
      color: #8a8a8a;
      font-size: 35px;
      margin-right: 14px;
    }
  }

  .header-icon {
    position: relative;
    display: flex;
    right: 70px;
    justify-content: flex-end;

    span {
      font-size: 25px;
      cursor: pointer;
      position: relative;
      color: #757f8c;
      margin-right: 30px;

      i {
        position: absolute;
        right: -2px;
        top: 0;
        background: #c95b5b;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 10px;
        height: 10px;
        color: #fff;
        border-radius: 100px;
        font-size: 12px;
        font-style: normal;
      }
    }

    span:last-child {
      margin: 0;
    }
  }
}
</style>
