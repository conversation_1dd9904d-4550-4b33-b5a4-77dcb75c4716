<template>
  <div class="baseInsuredDetailForm">
    <el-form
      ref="form"
      :model="form"
      label-width="120px"
      :rules="rules"
      style="width: 900px"
    >
      <div style="display: flex; gap: 40px">
        <el-form-item prop="insuranceType" label="险种名称">
          <el-select
            v-model="form.insuranceType"
            @change="handleInsuranceTypeChange"
          >
            <el-option label="养老保险" value="ENDOWMENT_INSURANCE"></el-option>
            <el-option label="医疗保险" value="MEDICAL_INSURANCE"></el-option>
            <el-option label="公积金" value="ACCUMULATION_FUND"></el-option>
            <el-option
              label="失业保险"
              value="UNEMPLOYMENT_INSURANCE"
            ></el-option>
            <el-option label="工伤保险" value="INJURY_INSURANCE"></el-option>
            <el-option label="生育保险" value="BIRTH_INSURANCE"></el-option>
            <el-option label="大病医疗" value="SERIOUS_DISEASE_TREATMENT"></el-option>
            
          </el-select>
        </el-form-item>
        <el-form-item prop="insuredProjectType" label="险种类型">
          <el-select v-model="form.insuredProjectType" disabled>
            <el-option label="社保" value="SOCIAL_INSURANCE"></el-option>
            <el-option label="公积金" value="ACCUMULATION_FUND"></el-option>
            <el-option label="失业" value="unemployment"></el-option>
            <el-option label="工伤" value="injury"></el-option>
            <el-option label="生育" value="maternity"></el-option>
          </el-select>
        </el-form-item>
      </div>

      <div style="display: flex; gap: 40px">
        <el-form-item
          prop="baseNumberMax"
          label="基数上限"
          style="flex: 0 0 240px"
        >
          <el-input
            type="number"
            :step="1"
            :min="0"
            v-model="form.baseNumberMax"
            @focus="clearIfZero('baseNumberMax')"
            @blur="resetIfEmpty('baseNumberMax')"
            @change="
              validateFieldValue(
                'baseNumberMax',
                form.baseNumberMax,
                0,
                100000000
              )
            "
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="baseNumberMin"
          label="基数下限"
          style="flex: 0 0 240px"
        >
          <el-input
            type="number"
            :step="1"
            :min="0"
            v-model="form.baseNumberMin"
            @focus="clearIfZero('baseNumberMin')"
            @blur="resetIfEmpty('baseNumberMin')"
            @change="
              validateFieldValue('baseNumberMin', form.baseNumberMin, 0, 10000)
            "
          ></el-input>
        </el-form-item>
      </div>

      <div style="display: flex; gap: 40px">
        <el-form-item prop="compScale" label="单位比例" style="flex: 0 0 240px">
          <el-input
            type="number"
            :step="1"
            :min="0"
            :max="100"
            v-model="form.compScale"
            @focus="clearIfZero('compScale')"
            @blur="resetIfEmpty('compScale')"
            @change="validateFieldValue('compScale', form.compScale, 0, 100)"
          >
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="compFixedAmount"
          label="单位固定金额"
          style="flex: 0 0 240px"
        >
          <el-input
            type="number"
            step="1"
            v-model="form.compFixedAmount"
            @focus="clearIfZero('compFixedAmount')"
            @blur="resetIfEmpty('compFixedAmount')"
            @change="
              validateFieldValue(
                'compFixedAmount',
                form.compFixedAmount,
                0,
                1000000
              )
            "
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="compMantissaRule"
          label="单位尾数规则"
          style="flex: 1"
        >
          <el-select v-model="form.compMantissaRule">
            <el-option label="四舍五入至分" value="ROUND_UNTIL_FEN"></el-option>
            <el-option label="四舍五入至元" value="ROUND_UNTIL_YUAN"></el-option>
            <el-option label="四舍五入至角" value="ROUND_UNTIL_JIAO"></el-option>
            <el-option label="直接舍去" value="round_down"></el-option>
            <el-option label="精确计算" value="exact"></el-option>
          </el-select>
        </el-form-item>
      </div>
      <div style="display: flex; gap: 40px">
        <el-form-item
          prop="personScale"
          label="个人比例"
          style="flex: 0 0 240px"
        >
          <el-input
            type="number"
            :step="1"
            :min="0"
            :max="100"
            v-model="form.personScale"
            @focus="clearIfZero('personScale')"
            @blur="resetIfEmpty('personScale')"
            @change="
              validateFieldValue('personScale', form.personScale, 0, 100)
            "
          >
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="personFixedAmount"
          label="个人固定金额"
          style="flex: 0 0 240px"
        >
          <el-input
            type="number"
            :step="1"
            :min="0"
            v-model="form.personFixedAmount"
            @focus="clearIfZero('personFixedAmount')"
            @blur="resetIfEmpty('personFixedAmount')"
            @change="
              validateFieldValue(
                'personFixedAmount',
                form.personFixedAmount,
                0,
                1000000
              )
            "
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="personMantissaRule"
          label="个人尾数规则"
          style="flex: 1"
        >
          <el-select v-model="form.personMantissaRule">
            <el-option label="四舍五入至分" value="ROUND_UNTIL_FEN"></el-option>
            <el-option label="四舍五入至元" value="ROUND_UNTIL_YUAN"></el-option>
            <el-option label="四舍五入至角" value="ROUND_UNTIL_JIAO"></el-option>
            <el-option label="直接舍去" value="round_down"></el-option>
            <el-option label="精确计算" value="exact"></el-option>
          </el-select>
        </el-form-item>
      </div>
      <div style="text-align: right" v-if="!baseInsuredDetail.id">
        <el-button type="primary" @click="onCreate">新增</el-button>
      </div>
      <div style="text-align: right" v-else>
        <el-button type="text" @click="onUpdate">更新</el-button>
        <el-button type="text" style="color: red" @click="onDelete">
          删除
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    baseInsuredDetail: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      rules: {
        insuranceType: [
          { required: true, message: '请选择险种', trigger: 'blur' }
        ],
        baseNumberMax: [
          { required: true, message: '请填入基数上限', trigger: 'blur' }
        ],
        baseNumberMin: [
          { required: true, message: '请填入基数下限', trigger: 'blur' }
        ],
        compScale: [
          { required: true, message: '请填入单位比例', trigger: 'blur' }
        ],
        compFixedAmount: [
          { required: true, message: '请填入单位固定金额', trigger: 'blur' }
        ],
        compMantissaRule: [
          { required: true, message: '请填入单位尾数规则', trigger: 'blur' }
        ],
        personScale: [
          { required: true, message: '请填入个人比例', trigger: 'blur' }
        ],
        personFixedAmount: [
          { required: true, message: '请填入个人固定金额', trigger: 'blur' }
        ],
        personMantissaRule: [
          { required: true, message: '请填入个人尾数规则', trigger: 'blur' }
        ]
      },
      form: this.baseInsuredDetail || {}
    }
  },
  created() {
    if (Object.keys(this.form).length === 0) {
      return
    }
  },
  methods: {
    validateFieldValue(field, value, min, max) {
      // 将值转换成数
      const num = Number(value)
      if (num < min) {
        this.form[field] = min
      } else if (num > max) {
        this.form[field] = max
      } // 如果在范围内, 什么都不做，保持用户输入的值
    },
    handleInsuranceTypeChange(v) {
      switch (v) {
        case 'ENDOWMENT_INSURANCE':
          this.form.insuredProjectType = 'SOCIAL_INSURANCE'
          break
        case 'MEDICAL_INSURANCE':
          this.form.insuredProjectType = 'SOCIAL_INSURANCE'
          break
        case 'SERIOUS_DISEASE_TREATMENT':
          this.form.insuredProjectType = 'SOCIAL_INSURANCE'
          break
        case 'UNEMPLOYMENT_INSURANCE':
          this.form.insuredProjectType = 'SOCIAL_INSURANCE'
          break
        case 'INJURY_INSURANCE':
          this.form.insuredProjectType = 'SOCIAL_INSURANCE'
          break
        case 'BIRTH_INSURANCE':
          this.form.insuredProjectType = 'SOCIAL_INSURANCE'
          break
        case 'ACCUMULATION_FUND':
          this.form.insuredProjectType = 'ACCUMULATION_FUND'
          break

        default:
          break
      }
    },
    clearIfZero(field) {
      if (this.form[field] === 0) {
        this.form[field] = '' // 清空初始值0
      }
    },
    resetIfEmpty(field) {
      if (this.form[field] === '') {
        this.form[field] = 0 // 如果为空，重置为0
      }
    },

    onCreate() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('onCreate', { ...this.form }, () => {
            this.$refs.form.resetFields()
          })
        }
      })
      //this.$message.success('新增成功')
    },
    onUpdate() {
      this.$emit('onUpdate', { ...this.form })
    },
    async onDelete() {
      const isOk = await this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      if (!isOk) {
        return
      }

      this.$emit('onDelete', this.form)
    }
  }
}
</script>

<style></style>
