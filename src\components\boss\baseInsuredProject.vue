<template>
  <div class="baseInsuredProject" style="border: 1px solid #ccc">
    <div
      style="
        border-bottom: 1px solid #ccc;
        display: flex;
        gap: 20px;
        padding: 10px 12px;
        align-items: center;
      "
    >
      <div
        class="leftBox"
        style="
          flex: 9;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        "
      >
        <span> {{ baseInsuredProject.id }} </span>
        <span> {{ baseInsuredProject.insuredName }} </span>
        <span> {{ baseInsuredProject.city.name }} </span>
        <span>
          {{
            baseInsuredProject.accumulationFundYn
              ? '缴纳公积金'
              : '不缴纳公积金'
          }}
        </span>
        <span> {{ baseInsuredProject.createTime }} </span>
      </div>
      <div style="flex: 1; text-align: right">
        <el-button type="text"  @click="edit">
          修改
        </el-button>
        <el-button type="text" @click="del">删除</el-button>
      </div>
    </div>
    <div
      v-if="!baseInsuredProject.baseInsuredDetails.length"
      style="padding: 20px; text-align: center"
    >
      没有参保条目

      <el-button
        type="text"
        @click="
          $router.push(
            `/baseInsuredDetails/new?projectId=${baseInsuredProject.id}`
          )
        "
      >
        去创建
      </el-button>
    </div>
    <BaseInsuredProjectDetail
      :index="index + 1"
      :baseInsuredProjectDetail="baseInsuredProjectDetail"
      :key="index"
      v-for="(
        baseInsuredProjectDetail, index
      ) in baseInsuredProject.baseInsuredDetails"
    />
  </div>
</template>

<script>
import BaseInsuredProjectDetail from './baseInsuredProjectDetail.vue'
export default {
  components: {
    BaseInsuredProjectDetail
  },
  props: {
    baseInsuredProject: {
      type: Object,
      default: {}
    }
  },
  methods: {
    del() {
      this.$confirm('删除该方案不可恢复, 是否继续?', '删除方案', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$emit('onDelete', this.baseInsuredProject)
        })
        .catch(() => {})
    },
    edit() {
      this.$emit('onEdit', this.baseInsuredProject)
    }
  }
}
</script>

<style></style>
