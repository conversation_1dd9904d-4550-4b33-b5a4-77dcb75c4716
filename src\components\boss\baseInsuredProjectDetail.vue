<template>
  <div
    class="baseInsuredProjectDetail"
    style="display: flex; align-items: top; padding: 12px"
  >
    <div style="flex: 0 0 40px">{{ index }}</div>
    <div style="flex: 1; line-height: 30px">
      <span class="item">
        险种类型：{{
          translateInsuranceType(baseInsuredProjectDetail?.insuredProjectType)
        }}</span
      >

      <span class="item">
        险种名称：{{
          translateInsuranceNameType(baseInsuredProjectDetail?.insuranceType)
        }}
      </span>
      <br />
      <span class="item">
        基数上限：{{ baseInsuredProjectDetail?.baseNumberMax?.toFixed(2) }}
      </span>
      <span class="item">
        基数下限：{{ baseInsuredProjectDetail?.baseNumberMin?.toFixed(2) }}
      </span>
      <br />
      <span class="item">
        单位比例：{{ formatRate(baseInsuredProjectDetail?.compScale * 100) }}%
      </span>
      <span class="item">
        单位固定金额：{{
          baseInsuredProjectDetail?.compFixedAmount?.toFixed(2)
        }}
      </span>
      <span class="item">
        单位尾数规则：{{
          translateMantissaCompRule(baseInsuredProjectDetail?.compMantissaRule)
        }}
      </span>
      <br />
      <span class="item">
        个人比例：{{ formatRate(baseInsuredProjectDetail.personScale * 100) }}%
      </span>
      <span class="item">
        个人固定金额：{{
          baseInsuredProjectDetail?.personFixedAmount?.toFixed(2)
        }}
      </span>
      <span class="item">
        个人尾数规则：{{
          translateMantissaPersonRule(
            baseInsuredProjectDetail?.personMantissaRule
          )
        }}
      </span>
      <br />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    index: Number,
    baseInsuredProjectDetail: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      //险种类型
      insuranceTypeMap: {
        SOCIAL_INSURANCE: '社保',
        ACCUMULATION_FUND: '公积金',
        unemployment: '失业',
        injury: '工伤',
        maternity: '生育'
      },
      //险种名称
      insuranceNameType: {
        ENDOWMENT_INSURANCE: '养老保险',
        UNEMPLOYMENT_INSURANCE: '失业保险',
        SERIOUS_DISEASE_TREATMENT: '大病医疗',
        MEDICAL_INSURANCE: '医疗保险',
        INJURY_INSURANCE: '工伤保险',
        BIRTH_INSURANCE: '生育保险',
        ACCUMULATION_FUND: '公积金'
      },
      compMantissaRule: {
        ROUND_UNTIL_FEN: '四舍五入至分',
        round_down: '直接舍去',
        exact: '精确计算',
        ROUND_UNTIL_YUAN: '四舍五入至元',
        ROUND_UNTIL_JIAO: '四舍五入至角'
      },
      personMantissaRule: {
        ROUND_UNTIL_FEN: '四舍五入至分',
        round_down: '直接舍去',
        exact: '精确计算',
        ROUND_UNTIL_YUAN: '四舍五入至元',
        ROUND_UNTIL_JIAO: '四舍五入至角'
      }
    }
  },
  methods: {
    formatRate(rate) {
      const s = rate + ''
      const dotIndex = s.indexOf('.')
      return s.substring(0, dotIndex + 2)
    },
    translateInsuranceType(type) {
      return this.insuranceTypeMap[type] || '未知类型'
    },
    translateInsuranceNameType(type) {
      return this.insuranceNameType[type] || '未知类型'
    },
    translateMantissaCompRule(rule) {
      return this.compMantissaRule[rule] || '未知规则'
    },
    translateMantissaPersonRule(rule) {
      return this.personMantissaRule[rule] || '未知规则'
    }
  }
}
</script>

<style scoped>
.item {
  display: inline-block;
  width: 280px;
}
</style>
