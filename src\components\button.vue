<template>
  <el-button
    @click="handleSaveClick"
    v-on="$listeners"
    v-bind="$attrs"
    :loading="isLoading"
  >
    <slot />
  </el-button>
</template>
<script>
export default {
  props: {
    click: {
      type: [Function, null],
      default: null,
    },
  },
  data() {
    return {
      isLoading: false,
    };
  },
  methods: {
    async handleSaveClick() {
      const AsyncFunction = (async () => {}).constructor;
      const clickFn = this.click;
      if (!this.click) return;
      if (clickFn instanceof AsyncFunction === true) {
        this.isLoading = true;
        await clickFn();
        this.isLoading = false;
        return;
      }
      clickFn();
    },
  },
};
</script>
