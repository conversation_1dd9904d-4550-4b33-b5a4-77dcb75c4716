<template>
  <div class="full-screen">
    <div v-if="hasHeader" class="header display-flex">
      <div class="return flex1">
        <i v-if="hasReturnBtn" class="el-icon-arrow-left" @click="goBack"></i>
        <span v-if="hasReturnBtn" class="back-line"></span>
        <span class="fs-title" :title="fsTitle">{{ fsTitle }}</span>
        <span style="margin-left: 40px; font-size: 16px">{{ fsText }}</span>
      </div>
      <div class="buttons-warp flex1">
        <slot name="fs-buttons"></slot>
      </div>
    </div>
    <div
      class="fs-container"
      :style="{
        backgroundColor: bgColor,
        overflow: hasScroll ? 'auto' : 'hidden',
      }"
    >
      <slot name="fs-container"></slot>
    </div>
  </div>
</template>
<script>
import * as AT from "@/store/actionTypes";
export default {
  components: {},
  props: {
    hasReturnBtn: {
      type: Boolean,
      default: true,
    },
    hasHeader: {
      type: Boolean,
      default: true,
    },
    hasScroll: {
      type: Boolean,
      default: true,
    },
    fsTitle: {
      type: String,
      default: "fs-title",
    },
    fsText: {
      type: String,
    },
    bgColor: {
      type: String,
      default: "#F1F1F1",
    },
    goUrl: {
      type: String,
      default: "",
    },
  },
  created() {
    this.$store.commit(AT.SHOWAPP, false);
  },
  mounted() {
    this.$store.commit(AT.SHOWAPP, false);
  },
  destroyed() {
    this.$store.commit(AT.SHOWAPP, true);
  },
  methods: {
    goBack() {
      this.goUrl ? this.$router.push(this.goUrl) : this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.full-screen {
  .header {
    box-sizing: border-box;
    background-color: $mainColor;
    color: #fff;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #f3f3f3;

    .buttons-warp {
      overflow: hidden;
      text-align: right;
      padding-right: 50px;
    }
    .return {
      text-align: left;
      padding-left: 40px;
      font-size: 20px;
      display: flex;
      align-items: center;
      .el-icon-arrow-left {
        cursor: pointer;
      }
    }
    .back-line {
      display: inline-block;
      width: 1px;
      height: 20px;
      background-color: #fff;
      margin-left: 20px;
      opacity: 0.5;
    }
    .fs-title {
      margin-left: 40px;
      font-size: 20px;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .fs-container {
    padding-bottom: 0px;
    height: calc(100vh - 50px);
    overflow: auto;
  }
}
</style>
