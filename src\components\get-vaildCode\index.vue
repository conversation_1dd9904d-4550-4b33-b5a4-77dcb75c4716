<template>
    <div>
        <old-button
            tag="span"
            type="primary"
            :disabled="showTimerPhone"
            @click="getVerifyCode"
            v-if="getType === 'cellPhone'"
        >
            <span class="count">{{ showTimerPhone ? `获取验证码(${count}s)` : "获取验证码" }}</span>
        </old-button>
        <old-button
            tag="span"
            type="primary"
            :disabled="showTimerEmail"
            @click="getVerifyEmailCode"
            v-else
        >
            <span class="count">{{ showTimerEmail ? `获取验证码(${countEmail}s)` : "获取验证码" }}</span>
        </old-button>
    </div>
</template>
<script>
const TIME_COUNT = 60;
const TIME_COUNTEMAIL = 60;
export default {
    name: "getVaildCode",
    props: {
        email: {
            type: String,
        },
        cellPhone: {
            type: String,
        },
        getType: {
            type: String,
        },
        userType: {
            type: String,
            default: "PERSONAL",
        },
        changeEmail: {
            type: Boolean,
            default: false,
        },
        resetPassword: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: "",
        },
        phoneChange: {
            type: String,
            default: "",
        },
        account: {
            type: String,
            default: "",
        },
        showTimer: {
            type: String,
            default: "stop",
        },
        timeEmail: {
            type: String,
            default: "stop",
        },
    },
    watch: {
        showTimer(val) {
            if (val == "stop") {
                this.showTimerPhone = false;
            }
        },
        timeEmail(val) {
            if (val == "stop") {
                this.showTimerEmail = false;
            }
        },
    },
    data() {
        return {
            showTimerPhone: false,
            showTimerEmail: false,
            count: "",
            countEmail: "",
        };
    },
    methods: {
        // 手机验证码
        getVerifyCode() {
            if (this.cellPhone != "") {
                if (this.type == "ENTERPRISE") {
                    this.$store
                        .dispatch("findPassStore/acSendSmsResetPassword", {
                            cellPhone: this.cellPhone,
                            account: this.account,
                        })
                        .then((res) => {
                            if (res.success) {
                                this.$emit("getVerifyCode", res.data);
                                if (res.success) {
                                    this.setTimer();
                                }
                            }
                        });
                } else {
                    if (this.cellPhone == "getTokenPhone") {
                        //无需token无需手机号获取验证码
                        this.$store
                            .dispatch("loginStore/acSendSmsCode", {
                                systemName: "OLD_QY",
                            })
                            .then((res) => {
                                if (res.success) {
                                    this.$emit("getVerifyCode", res.data);
                                    if (res.success) {
                                        this.setTimer();
                                    }
                                }
                            });
                    } else {
                        if (!this.phoneChange) {
                            //无需token通过传手机号获取验证码
                            this.$store
                                .dispatch("loginStore/acCodeSms", {
                                    cellPhone: this.cellPhone,
                                    systemName: "OLD_QY",
                                })
                                .then((res) => {
                                    if (res.success) {
                                        this.$emit("getVerifyCode", res.data);
                                        if (res.success) {
                                            this.setTimer();
                                        }
                                    }
                                });
                        } else {
                            //需要token通过传手机号获取验证码
                            this.$store
                                .dispatch(
                                    "loginStore/acModifyPhoneSendSmsCode",
                                    {
                                        cellPhone: this.cellPhone,
                                        systemName: "OLD_QY",
                                    }
                                )
                                .then((res) => {
                                    if (res.success) {
                                        this.$emit("getVerifyCode", res.data);
                                        if (res.success) {
                                            this.setTimer();
                                        }
                                    }
                                });
                        }
                    }
                }
            } else {
                this.$message.error("请输入手机号码");
            }
        },
        //邮箱验证码
        getVerifyEmailCode() {
            if (this.email != "") {
                if (this.resetPassword) {
                    //找回企业密码时获取邮箱验证码
                    this.$store
                        .dispatch("findPassStore/acSendEmailResetPassword", {
                            email: this.email,
                        })
                        .then((res) => {
                            this.$emit(
                                "getVerifyEmailCode",
                                res.data.emailToken
                            );
                            if (res.success) {
                                this.setTimerEmail();
                            }
                        });
                } else if (this.changeEmail && this.userType == "ENTERPRISE") {
                    //更换企业邮箱时获取验证码
                    this.$store
                        .dispatch("accountManagementStore/acEnterpriseSend")
                        .then((res) => {
                            if (res.success) {
                                this.$emit(
                                    "getVerifyEmailCode",
                                    res.data.token
                                );
                                if (res.success) {
                                    this.setTimerEmail();
                                }
                            }
                        });
                } else {
                    //个人邮箱获取验证码
                    this.$store
                        .dispatch("accountManagementStore/acEmailSend", {
                            email: this.email,
                        })
                        .then((res) => {
                            if (res.success) {
                                this.$emit(
                                    "getVerifyEmailCode",
                                    res.data.emailToken
                                );
                                if (res.success) {
                                    this.setTimerEmail();
                                }
                            }
                        });
                }
            } else {
                this.$message.error("请输入邮箱");
            }
        },

        //倒计时显示
        setTimer() {
            let timer = "";
            if (!timer) {
                this.count = TIME_COUNT;
                this.showTimerPhone = true;
                timer = setInterval(() => {
                    if (this.count > 1 && this.count <= TIME_COUNT) {
                        this.count--;
                    } else {
                        this.showTimerPhone = false;
                        clearInterval(this.timer);
                        timer = null;
                    }
                }, 1000);
            }
        },
        //邮箱倒计时显示
        setTimerEmail() {
            let timer = "";
            if (!timer) {
                this.countEmail = TIME_COUNTEMAIL;
                this.showTimerEmail = true;
                timer = setInterval(() => {
                    if (
                        this.countEmail > 1 &&
                        this.countEmail <= TIME_COUNTEMAIL
                    ) {
                        this.countEmail--;
                    } else {
                        this.showTimerEmail = false;
                        clearInterval(this.timer);
                        timer = null;
                    }
                }, 1000);
            }
        },
    },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
</style>
