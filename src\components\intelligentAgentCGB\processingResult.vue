<template>
  <div>
    <div style="background-color: #f2f2f2">
      <br />
      <div v-if="commitStatus" style="margin: 20px 0">
        <div style="display: flex; justify-content: center">
          <el-image
            style="width: 30px; height: 30px; margin-right: 10px"
            :src="require('../../assets/images/cgbResultSuccess.png')"
          />
          <h3 style="margin-top: 3px">代发文件提交完成</h3>
        </div>
        <p style="text-align: center; margin: 18px 0; color: #ea818e">
          请到我行企业网银审核并完成发放
        </p>
        <p style="text-align: center; margin: 15px 0">
          <a
            href="https://ebank.cgbchina.com.cn/corporbank/"
            target="_blank"
            style="cursor: pointer; color: #d70a25; text-decoration: underline"
            >打开企业网银</a
          >
        </p>
      </div>
      <div v-else style="margin: 20px 0">
        <div style="display: flex; justify-content: center">
          <el-image
            style="width: 30px; height: 30px; margin-right: 10px"
            :src="require('../../assets/images/cgbResultFail.png')"
          />
          <h3 style="margin-top: 3px">代发文件提交失败，请重新提交</h3>
        </div>
        <p style="text-align: center; margin: 10px 0">
          错误码：{{ this.result.errorCode }}
        </p>
        <p style="text-align: center; margin: 10px 0">
          错误信息：{{ this.result.errorMsg }}
        </p>
      </div>
      <br />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    commitStatus: Boolean,
    result: {
      type: Object,
      default: {},
    },
  },
  methods: {},
};
</script>


<style>
</style>