<template>
  <div class="sourcesOfFund">
    应付总金额:
    <Money :amount="totalAmountPayable" style="color: red" />
    当前已选择可代发额度:<Money
      style="color: red"
      :amount="currentTotalAmount"
    />
    <div style="margin-top: 10px">
      <el-table
        :data="accountSourceRecords"
        border
        size="small"
        :header-cell-style="{ background: '#f2f2f2' }"
        @selection-change="handleSelectionChange"
        max-height="370"
        v-loading="loading"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column
          fixed
          prop="transferNo"
          label="交易流水号"
        ></el-table-column>
        <el-table-column prop="recordDate" label="记账日期"></el-table-column>
        <el-table-column prop="amount" label="收入金额"></el-table-column>
        <el-table-column
          prop="amountEnabled"
          label="可代发额度"
        ></el-table-column>
        <el-table-column
          prop="opponencyAccount"
          label="对方账号"
        ></el-table-column>
        <el-table-column
          prop="opponencyAccotName"
          label="对方户名"
        ></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import Money from "../../components/money.vue";
export default {
  components: {
    Money,
  },
  computed: {
    currentTotalAmount() {
      var total = 0.0;
      for (var c of this.selectedItems) {
        total += c.amountEnabled;
      }
      return total;
    },
  },
  props: {
    totalAmountPayable: {
      type: Number,
      default() {
        return 0;
      },
    },
    accountSourceRecords: {
      type: Array,
      default: () => {},
    },
    loading:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      selectedItems: [],
    };
  },
  methods: {
    handleSelectionChange(val) {
      this.selectedItems = val;
    },
  },
};
</script>

<style>
</style>