<template>
  <div
    class="steps"
    style="display: flex; width: 60%; text-align: center; align-items: center"
  >
    <div class="step">
      <span class="cycle" :class="current === 1 ? 'activate' : ''">1</span>
      <span>资金来源</span>
    </div>
    <div class="line"></div>

    <div class="step">
      <span class="cycle" :class="current === 2 ? 'activate' : ''">2</span>
      <span>上传代发文件</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    current: {
      type: Number,
      default() {
        return 1;
      },
    },
  },
};
</script>

<style scoped>
.step {
  display: flex;
  align-items: center;
}
.cycle {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 50%;
  border: 1px solid var(--color-primary);
  margin-right: 10px;
}
.cycle.activate {
  background: var(--color-primary);
  color: #fff;
}
.line {
  border-bottom: 1px solid #fafafa;
  flex: 1 1 100px;
  margin: 0 10px;
}
</style>