<template>
  <header class="header main-title">
    <el-row type="flex">
      <el-col :span="12">
        <span>{{ title }}</span>
      </el-col>
    </el-row>
  </header>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "",
    },
  },
};
</script>
<style scoped lang="scss">
.header {
  border-bottom: 1px solid #ededed;
  .add-table {
    cursor: pointer;
    float: right;
    color: var(--color-primary);
  }
  .iconxinzeng {
    font-size: 18px;
    color: #9c9c9c;
    position: relative;
    top: 1px;
  }
}
</style>