<script>
export default {
  props: {
    content: {
      type: String,
      default: '提示内容',
      require: true
    },
    tag: {
      type: String,
      default: 'span'
    },
    ellipsis: {
      type: Number,
      default: 1
    },
    placement: {
      type: String,
      default: 'top'
    }
  },
  data() {
    return {
      disabled: false
    }
  },
  methods: {
    handleMouseEnter(event) {
      const { target } = event
      const range = document.createRange()
      range.setStart(target, 0)
      range.setEnd(target, target.childNodes.length)
      const rangeWidth = Number.parseInt(
        range.getBoundingClientRect().width,
        10
      )
      this.disabled = !(
        rangeWidth > target.offsetWidth ||
        target.scrollWidth > target.offsetWidth
      )
    }
  },
  render(h) {
    const defaultSlots = this.$slots.default
    let slot = h(
      this.tag,
      {
        on: {
          mouseenter: this.handleMouseEnter,
          click: () => {
            this.$emit('click')
          }
        }
      },
      defaultSlots
        ? this.$slots.default
        : [
            h(
              'span',
              {
                class: `ellipsis-${this.ellipsis}`
              },
              this.content
            )
          ]
    )

    return h(
      'el-tooltip',
      {
        props: {
          effect: 'dark',
          placement: this.placement,
          disabled: this.disabled,
          content: this.content,
          ...this.$attrs
        },
        class: `ellipsis-${this.ellipsis}`
      },
      [slot]
    )
  }
}
</script>

<style scoped>
.ellipsis-1 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}
.ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}
</style>
