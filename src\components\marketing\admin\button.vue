<template>
  <el-button @click="handleSaveClick" :loading="isLoading" :type="type" :size="size">{{text}}</el-button>
</template>
<script>
export default {
  props: {
    click: {
      type: [Function, null],
      default: null
    },
    type:{
      type: String,
      default: 'primary'
    },
    text:{
      type: String,
      default: '确定'
    },
    size:{
      type: String,
      default: 'small'
    }
  },
  data() {
    return {
      isLoading: false
    }
  },
  methods: {
    async handleSaveClick() {
      const AsyncFunction = (async () => {}).constructor
      const clickFn = this.click
      if (!this.click) return
      if (clickFn instanceof AsyncFunction === true) {
        this.isLoading = true
        try {
          await clickFn()
        }finally {
          this.isLoading = false
        }
        return
      }
      clickFn()
    }
  }
}
</script>
