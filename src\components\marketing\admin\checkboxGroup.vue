<template>
  <el-checkbox-group v-model="selectedValues" @change="onInput">
    <el-checkbox
      v-for="option in options"
      :label="option.value"
      :key="option.value"
    >
      {{ option.label }}
    </el-checkbox>
  </el-checkbox-group>
</template>

<script>
import deepClone from 'kit/helpers/deepClone'
export default {
  props: {
    options: {
      type: Array,
      required: true
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedValues: []
    }
  },
  created() {
    this.initSelectValue()
  },
  methods: {
    initSelectValue() {
      this.selectedValues = deepClone(this.value)
    },
    onInput() {
      this.$emit('input', this.selectedValues)
    }
  },
  watch: {
    value() {
      this.initSelectValue()
    }
  }
}
</script>
