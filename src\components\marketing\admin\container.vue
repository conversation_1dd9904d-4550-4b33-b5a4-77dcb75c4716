<template>
  <div class="marketing-container" :style="{ minWidth }">
    <header class="marketing-container-header">
      <div v-if="back" class="back" @click="handleBackBtnClick">
        <i class="icon olading-iconfont oi-icon_arrow1" />
        <span class="text">返回</span>
        <i class="line" />
      </div>
      <h1 class="title">
        {{ containerTitle }}
      </h1>
      <slot name="header-right" />
    </header>
    <div
      ref="marketing-container-main "
      class="main customer-webkit-scrollbar"
      @scroll="onScroll"
    >
      <slot />
    </div>
    <div v-if="showFootButton" class="footer">
      <el-button
        v-if="ifShowCancelButton"
        type="info"
        size="small"
        class="cancel button"
        :loading="isCancelButtonLoading"
        @click="cancel"
      >
        {{ cancelButtonText }}
      </el-button>
      <el-button
        v-if="$listeners.confirm"
        :loading="isSaveButtonLoading"
        type="primary"
        size="small"
        class="button"
        @click="confirm"
      >
        {{ confirmButtonText }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { delay } from 'kit/helpers/delay'
export default {
  name: 'app-detail-marketing-container',
  props: {
    title: {
      type: String,
      default: ''
    },
    minWidth: {
      type: String,
      default: '1056px'
    },
    backRouterName: {
      type: String,
      default: ''
    },
    back: {
      type: Boolean,
      default: true
    },
    hideFootButton: {
      type: Boolean,
      default: false
    },
    cancelButtonText: {
      type: String,
      default: '取消'
    },
    confirmButtonText: {
      type: String,
      default: '保存'
    },
    showCancelButton: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isSaveButtonLoading: false,
      isCancelButtonLoading: false
    }
  },
  computed: {
    showFootButton() {
      if (this.hideFootButton) return false
      return this.$listeners.confirm || this.$listeners.cancel
    },
    ifShowCancelButton() {
      if (!this.showCancelButton) return false
      return this.$listeners.cancel
    },
    containerTitle() {
      return this.title || this.$route.meta.title
    }
  },
  methods: {
    onScroll() {},
    async toggleSaveButtonLoading(show = true) {
      await delay(100)
      this.isSaveButtonLoading = show
    },
    async confirm() {
      try {
        this.toggleSaveButtonLoading()
        this.$listeners.confirm && (await this.$listeners.confirm())
      } finally {
        this.toggleSaveButtonLoading(false)
      }
    },
    async cancel() {
      this.isCancelButtonLoading = true
      try {
        this.$listeners.cancel && (await this.$listeners.cancel())
      } finally {
        this.isCancelButtonLoading = false
      }
    },

    // 返回
    handleBackBtnClick() {
      if (this.$listeners.back) {
        this.$listeners.back()
      } else if (this.backRouterName) {
        this.$router.replace({
          name: this.backRouterName
        })
      } else {
        this.$router.back()
      }
    }
  }
}
</script>
<style scoped>
.marketing-container {
  height: calc(100vh - 78px);
  width: 100%;
  border-radius: 8px 0 0 0;
  background: #fff;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
}
.marketing-container .main {
  overflow-y: scroll;
  position: relative;
  flex: 1;
}
.oi-icon_arrow1 {
  margin-right: 6px;
  font-size: 14px;
}
.marketing-container-header {
  display: flex;
  margin: 0 24px;
  height: 56px;
  border-bottom: 1px solid #e4e7edff;
  align-items: center;
}
.marketing-container-header .back {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--o-primary-color);
}
.marketing-container-header .back .text {
  font-size: 14px;
}
.marketing-container-header .back .line {
  font-size: 16px;
  display: block;
  width: 1px;
  height: 16px;
  background-color: #e6e8ec;
  margin: 0 12px;
}
.marketing-container-header .title {
  color: #1e2228ff;
  font-size: 16px;
  font-weight: 600;
  font-family: 'PingFang SC';
}
.marketing-container .footer {
  background: #ffffff;
  box-shadow: 0 -2px 4px 0 #0000000f, 0 -1px 2px 0 #00000005,
    0 -1px 2px -2px #0000001a;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  height: 56px;
  line-height: 56px;
}
.marketing-container .footer .button {
  width: 74px;
}
.marketing-container .footer .el-button--primary:hover {
  border: 1px solid var(--o-primary-color);
}
.marketing-container .footer .cancel {
  border: 1px solid #cbced8;
  background: #fff;
  margin-right: 2px;
  color: #777c94;
}
</style>
