<template>
  <el-row type="flex" class="coupon-add-delete-button">
    <button @click="handleCopyClick" v-if="showCopyButton">
      <i class="icon iconfont icon-edit-interaction-copy" />
    </button>
    <i class="line" v-if="showCopyButton" />
    <button @click="handleRemoveClick">
      <i class="icon iconfont icon-base-delete" />
    </button>
  </el-row>
</template>

<script>
export default {
  props: {
    showCopyButton: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleCopyClick() {
      this.$emit('copy')
    },
    handleRemoveClick() {
      this.$emit('remove')
    }
  }
}
</script>

<style scoped>
.coupon-add-delete-button {
  position: relative;
}
button {
  background: none;
  border: none;
  width: 32px;
  cursor: pointer;
  text-align: center;
}
div {
  height: 24px;
  border-radius: 6px;
  opacity: 1;
  background: #ffffffff;
  box-shadow: 0 5px 12px 2px #0000000a, 0 3px 6px 0 #0000000a,
    0 1px 2px -2px #0000001a;
}
button i {
  font-size: 14px;
  color: #4e5769ff;
}
.line {
  position: absolute;
  width: 1px;
  background-color: #4e5769ff;
  height: 12px;
  top: 6px;
  bottom: 6px;
  right: 32px;
}
</style>
