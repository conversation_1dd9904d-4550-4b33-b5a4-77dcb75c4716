<template>
  <el-popover
    placement="bottom"
    style="display: block"
    width="200"
    ref="popover"
    :disabled="disabled"
    trigger="click"
  >
    <ul class="colors">
      <li
        v-for="item in colors"
        :class="{ active: value === item }"
        @click="handleClick(item)"
        :key="item"
        :style="{ background: item }"
      />
    </ul>
    <div class="coupon-color-picker" slot="reference">
      <div class="color" :style="{ background: value }"></div>
      <i class="el-icon-arrow-down" />
    </div>
  </el-popover>
</template>
<script>
export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    colors: {
      type: Array,
      default: () => []
    }
  },
  inject: ['disabled'],
  methods: {
    handleClick(color) {
      this.$emit('input', color)
      this.$refs.popover.showPopper = false
      this.validate()
    },
    validate() {
      const prop = this?.$parent?.prop
      this.$parent?.elForm?.validateField(prop)
    }
  }
}
</script>

<style scoped>
.coupon-color-picker {
  width: 92px;
  height: 32px;
  border-radius: 6px;
  opacity: 1;
  border: 1px solid #cad0dbff;
  background: #ffffffff;
  display: flex;
  align-items: center;
  padding-left: 12px;
  cursor: pointer;
}
.color {
  width: 40px;
  height: 20px;
  border-radius: 4px;
  opacity: 1;
  border: 1px solid #e4e7edff;
  background: #ffffffff;
  margin-right: 6px;
}
.el-icon-arrow-down {
  color: #828b9bff;
}
::v-deep.el-icon-arrow-down:before {
  font-size: 16px;
  margin-left: 10px;
  font-weight: bold !important;
}
.colors {
  display: flex;
  align-items: center;
  justify-self: center;
  gap: 16px;
}
.colors li:first-child {
  border: 1px solid #e4e7edff;
}
.colors li {
  width: 40px;
  height: 20px;
  cursor: pointer;
  border-radius: 4px;
  position: relative;
}
.colors li.active::after {
  content: '';
  position: absolute;
  border: 1px solid #f77234ff;
  padding: 4px;
  left: -5px;
  top: -5px;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
</style>
