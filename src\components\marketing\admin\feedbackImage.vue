<template>
  <el-image :src="imgURL" fit="contain" style="display: block" />
</template>

<script>
import wechatActivityCreatedSuccessfully from 'kit/assets/images/marketing/admin/wechatActivityCreatedSuccessfully.png'

const images = {
  wechatActivityCreatedSuccessfully
}

export default {
  props: {
    type: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      imgURL: ''
    }
  },
  async created() {
    this.imgURL = wechatActivityCreatedSuccessfully
  }
}
</script>

<style scoped>
img {
  max-width: 100%;
  height: auto;
}
</style>
