<template>
  <div :class="className" class="fileImportPrompt" v-if="ifShowComponent">
    <i class="el-icon-error" v-if="className === 'fail'" />
    <i class="el-icon-warning" v-if="className === 'partSuccess'" />
    <i class="el-icon-success" v-if="className === 'success'" />
    <div v-if="className === 'success'">
      <h2>导入成功</h2>
      <p>成功导入{{ successCount }}条数据</p>
    </div>
    <div v-if="className === 'partSuccess'">
      <h2>部分导入失败</h2>
      <p>
        有 <span style="color: #f63939" v-text="failCount" /> 条数据导入失败，请
        <a :href="failFileURL">导出失败结果文件</a>，重新上传失败数据
      </p>
      <p>
        已成功导入
        <span style="color: #07bb06" v-text="successCount" /> 条数据。
      </p>
    </div>
    <div v-if="className === 'fail'">
      <h2>全部导入失败</h2>
      <p>请 <a :href="failFileURL">导出失败结果文件</a>，重新上传失败数据</p>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    successCount: {
      default: 0,
      type: Number
    },
    failCount: {
      default: 0,
      type: Number
    },
    failFileURL: {
      default: '',
      type: String
    }
  },
  computed: {
    className() {
      if (!this.failCount) return 'success'
      if (this.failCount && this.successCount) return 'partSuccess'
      if (!this.successCount) return 'fail'
      return 'success'
    },
    ifShowComponent() {
      return this.successCount || this.failCount
    }
  }
}
</script>
<style scoped>
.fileImportPrompt {
  width: 752px;
  border-radius: 8px;
  opacity: 1;
  border: 1px solid #89e47f;
  background: #ecffe8;
  display: flex;
  padding: 20px 25px;
  padding-bottom: 16px;
  box-sizing: border-box;
}
.fileImportPrompt h2 {
  height: 24px;
  opacity: 1;
  color: #1e2228;
  font-size: 16px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 24px;
  margin-bottom: 4px;
}
.fileImportPrompt p {
  height: 22px;
  opacity: 1;
  color: #1e2228;
  font-size: 14px;
  font-weight: 400;
  text-align: left;
  line-height: 22px;
  font-family: 'PingFang SC';
  margin-bottom: 4px;
}
.fileImportPrompt a {
  color: #4f71ff;
  font-weight: 600px;
  text-decoration: none;
}
.fileImportPrompt.partSuccess {
  background: #fffae8;
  border-color: #ffdc8c;
}
.fileImportPrompt.fail {
  background: #ffece8;
  border-color: #fbaaa1;
}
.fileImportPrompt i {
  font-size: 22px;
  margin-right: 18px;
}
.fileImportPrompt .el-icon-success {
  color: #15bf14;
}
.fileImportPrompt .el-icon-warning {
  color: #ff9a01;
}
.fileImportPrompt .el-icon-error {
  color: #f63939;
}
</style>
