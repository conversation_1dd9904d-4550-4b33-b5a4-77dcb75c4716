<template>
  <img
    style="width: 100px"
    :src="smsCodeURL"
    @click="handleReloadClick()"
    v-if="token"
  />
</template>

<script>
import makeMarketingClient from 'kit/services/marketing/makeClient'
import handleErrorH5 from 'kit/helpers/handleErrorH5'
const marketingClient = makeMarketingClient()

export default {
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  created() {
    this.handleReloadClick()
  },
  computed: {
    smsCodeURL() {
      const baseUrl = window.env.api
      if (!baseUrl) {
        handleErrorH5('无法获取平台的API地址')
        return
      }
      return `${baseUrl}/marketing/sms/imageCaptcha?token=${encodeURIComponent(
        this.token
      )}`
    }
  },
  data() {
    return {
      token: ''
    }
  },
  methods: {
    async handleReloadClick() {
      const [err, r] = await marketingClient.smsCreateImageCaptcha()
      if (err) {
        handleError(err)
        return
      }
      this.token = r.data
      this.onInput()
    },
    onInput() {
      this.$emit('input', this.token)
    }
  }
}
</script>

<style></style>
