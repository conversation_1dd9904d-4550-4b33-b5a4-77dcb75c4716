<template>
  <div class="rule">
    <div class="activity-time">
      <h2><span>活动时间</span></h2>
      <div class="activity-time-date">
        {{ availableBeginTime | date }} ～ {{ availableEndTime | date }}
      </div>
    </div>
    <div class="activity-rule">
      <h2><span>活动规则</span></h2>
      <pre class="activity-rule-text">{{ info.ruleInfo }}</pre>
    </div>
  </div>
</template>
<script>
import formatDateTime from 'kit/formatters/dateTime'
export default {
  filters: {
    date(value) {
      if (!value) return ''
      return formatDateTime('yyyy-MM-dd', value)
    }
  },
  props: {
    info: Object
  },
  computed: {
    availableBeginTime() {
      return this.info.availableBeginTime.replace(/-/g, '/')
    },
    availableEndTime() {
      return this.info.availableEndTime.replace(/-/g, '/')
    }
  }
}
</script>
<style scoped>
.rule {
  background-color: #fff;
  padding: 0.6rem 0.48rem 1rem 0.48rem;
  text-align: center;
  overflow: hidden;
}
h2 {
  opacity: 1;
  color: #1e2228ff;
  font-size: 16px;
  font-weight: 600;
  font-family: 'PingFang SC';
  text-align: center;
  line-height: 24px;
  display: flex;
  align-items: center;
  margin: 0;
  margin-bottom: 18px;
  justify-content: center;
}
h2 span {
  margin-left: 10px;
  position: relative;
}
h2 span::after {
  content: '';
  width: 68px;
  height: 3px;
  opacity: 1;
  background: linear-gradient(115deg, #f77234ff 0%, #f7723400 100%);
  position: absolute;
  bottom: -2px;
  left: 0;
}
h2::before {
  content: '';
  display: block;
  width: 20px;
  height: 20px;
  background: url('kit/assets/images/<EMAIL>') no-repeat center center;
  background-size: cover;
}
.activity-rule h2::before {
  background: url('kit/assets/images/<EMAIL>') no-repeat center center;
  background-size: cover;
}
.activity-time {
  margin-bottom: 36px;
}
.activity-time-date {
  opacity: 1;
  color: #4e5769ff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: center;
  line-height: 22px;
}
.activity-rule-text {
  color: #4e5769ff;
  font-size: 14px;
  text-align: left;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  line-height: 22px;
}
</style>
