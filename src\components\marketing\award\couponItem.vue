<template>
  <div
    class="item"
    style="height: 88px; width: 100%"
    :style="{ backgroundColor: info.themeColor }"
  >
    <VantImage :src="info.logoUrl" width="90" />
    <div class="title">{{ info.name }}</div>

    <div
      class="button"
      :class="{ disabled: isDisabled }"
      @click="collection(info)"
    >
      领取
    </div>
  </div>
</template>
<script>
import { Image, Toast } from 'vant'

export default {
  components: {
    VantImage: Image
  },
  props: {
    info: Object,
    disabled: Boolean
  },
  data() {
    return {
      isCollection: false
    }
  },
  computed: {
    isDisabled() {
      if (this.disabled) return true
      // 如果不可重复领取 ， 当前用户 已领取 设置禁用
      if (!this.info.repeatGet && this.info.getCount > 0) return true
      return false
    }
  },
  methods: {
    collection(info) {
      if (this.isDisabled) return
      if (this.isCollection) return
      this.isCollection = true
      const toast = Toast.loading({
        message: '正在领取中...',
        forbidClick: true,
        loadingType: 'spinner',
        duration: 1000000
      })

      const done = () => {
        toast.clear()
        this.isCollection = false
      }

      this.$emit('collection', [info, done])
    }
  }
}
</script>
<style scoped>
.item {
  position: relative;
  overflow: hidden;
  /* background: url('kit/assets/images/<EMAIL>') no-repeat center center; */
  /* background-size: cover; */
  display: flex;
  align-items: center;
  border-radius: 5px;
}

.item::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  background: #f1f4f8;
  border-radius: 50%;
  left: 84px;
  z-index: 99;
  bottom: -6px;
}
.item::before {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  background: #f1f4f8;
  border-radius: 50%;
  left: 84px;
  z-index: 1;
  top: -6px;
}
.button {
  width: 60px;
  height: 26px;
  border-radius: 30px;
  opacity: 1;
  background: linear-gradient(270deg, #ff6923ff 0%, #ff9f57ff 100%);
  box-shadow: 0 4px 4px 0 #f772344d;
  text-align: center;
  line-height: 26px;
  margin: 0 16px;
  color: #ffffffff;
  font-size: 14px;
}
.button.disabled {
  background: #e4e7edff;
  color: #828b9bff;
  box-shadow: none;
}
.title {
  flex: 1;
  padding-left: 15px;
  opacity: 1;
  color: #771f06ff;
  font-size: 16px;
  font-weight: 600;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
