<template>
  <div class="loading">
    <Loading :color="color" />
  </div>
</template>
<script >
import { Loading } from 'vant'

export default {
  props: {
    color: {
      type: String,
      default: '#FF432B'
    }
  },
  components: {
    Loading
  }
}
</script>

<style scoped>
.loading {
  position: fixed;
  left: 0;
  width: 100%;
  height: 100%;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9;
  background: #fff;
}
</style>
