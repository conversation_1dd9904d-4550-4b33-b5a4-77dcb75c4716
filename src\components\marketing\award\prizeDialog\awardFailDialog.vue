<template>
  <div>
    <img
      src="kit/assets/images/marketing/mobile/luckyWheelDraw/<EMAIL>"
      v-show="false"
    />
    <Dialog
      v-model="show"
      :show-confirm-button="false"
      className="prizeDialog"
      style="width: 6rem"
    >
      <div style="position: relative; width: 6rem; margin: 0 auto">
        <i
          class="close icon iconfont icon-remind-close-circle"
          @click="open(false)"
        />
        <img
          src="kit/assets/images/marketing/mobile/luckyWheelDraw/<EMAIL>"
          style="display: block; width: 100%"
        />
        <div class="prizeDialogContainer">
          <div
            v-for="(item, key) in statusMap"
            :key="key"
            v-show="key === type"
            class="fail"
          >
            <p>{{ item.content[0] }}</p>
            <p>{{ item.content[1] }}</p>
          </div>
          <button class="custom-button" @click="handleClick">
            <span>{{ buttonText }}</span>
          </button>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script>
import DynamicCoupon from './dynamicCoupon.vue'
import { Dialog } from 'vant'
import {
  NO_PRIZE_ERROR_CODE,
  PRIZES_FULLY_ALLOCATED_CODE
} from 'kit/pages/marketing/admin/constants'

export default {
  components: {
    Dialog: Dialog.Component,
    DynamicCoupon
  },
  data() {
    const statusMap = {
      notWinning: {
        content: ['这次没中奖', '再接再厉哦~'],
        text: '继续抽奖',
        handle: () => this.open(false)
      },
      prizesFullyAllocated: {
        content: ['奖品已经派完', '下次请早点哦~'],
        text: '我知道了',
        handle: () => this.open(false)
      },
      noMoreChances: {
        content: ['您的机会已用完', '下次再来哦~'],
        text: '我知道了',
        handle: () => this.open(false)
      }
    }

    return {
      statusMap,
      show: false,
      type: 'notWinning'
    }
  },
  computed: {
    buttonText() {
      return this.statusMap[this.type].text
    }
  },
  methods: {
    open(show = true) {
      this.show = show
    },
    showDialog(type) {
      this.type = type
      this.show = true
    },
    showErrCodeDialog(code) {
      const typeMap = {
        [NO_PRIZE_ERROR_CODE]: 'notWinning',
        [PRIZES_FULLY_ALLOCATED_CODE]: 'prizesFullyAllocated'
      }
      let type = typeMap[code]
      if (!type) return false
      this.type = type
      this.show = true
      return true
    },
    handleClick() {
      this.statusMap[this.type].handle()
    }
  }
}
</script>

<style scoped>
.prizeDialog {
  background: none;
}
.prizeDialogContainer {
  position: fixed;
  top: 3.8rem;
  left: 0;
  width: 100%;
}
.prizeDialogContainer h2 {
  height: 0.64rem;
  opacity: 1;
  color: #1e2228ff;
  font-size: 0.48rem;
  font-weight: 600;
  font-family: 'PingFang SC';
  text-align: center;
  line-height: 0.64rem;
  margin: 0.18rem auto 0.4rem;
}
.custom-button {
  width: 196px;
  height: 68px;
  background: url('kit/assets/images/marketing/mobile/blindBox/<EMAIL>')
    no-repeat center;
  background-size: cover;
  color: #ffffffff;
  font-size: 16px;
  font-weight: 500;
  font-family: 'PingFang SC';
  text-align: center;
  padding: 0;
  border: 0;
  display: block;
  margin: 10px auto;
}
.custom-button span {
  display: block;
  margin-bottom: 12px;
}
.fail {
  color: #1e2228;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 0.36rem;
  font-style: normal;
  font-weight: 600;
  line-height: 0.64rem;
  margin-bottom: 2.7rem;
}
.fail p {
  margin: 0;
}
.fail p:nth-child(1) {
  margin-bottom: 0.14rem;
}
.close {
  position: absolute;
  width: 1rem;
  height: 1rem;
  color: #fff;
  top: 1.1rem;
  right: 0;
  font-size: 0.64rem;
  text-align: center;
  line-height: 1rem;
  opacity: 0.8;
}
</style>
