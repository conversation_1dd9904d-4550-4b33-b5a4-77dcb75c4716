<template>
  <div>
    <img
      src="kit/assets/images/marketing/mobile/scratchLotteryTicket/<EMAIL>"
      v-show="false"
    />
    <Dialog v-model="show" :show-confirm-button="false" className="prizeDialog">
      <div style="position: relative; width: 6rem; margin: 0 auto">
        <i
          class="close icon iconfont icon-remind-close-circle"
          @click="open(false)"
        />
        <img
          src="kit/assets/images/marketing/mobile/scratchLotteryTicket/<EMAIL>"
          style="width: 100%; display: block"
        />
        <div class="prizeDialogContainer">
          <h2>恭喜您获得</h2>
          <DynamicCoupon
            :prizeName="prizeName"
            style="margin: 0 auto 1.06rem; width: 3.5rem"
          />
          <button class="custom-button" @click="handleClick">
            <span>查看中奖记录</span>
          </button>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script>
import DynamicCoupon from './dynamicCoupon.vue'
import { Dialog } from 'vant'

export default {
  components: {
    DynamicCoupon,
    Dialog: Dialog.Component
  },
  data() {
    return {
      show: false,
      prizeName: '满200减5元'
    }
  },
  methods: {
    open(show = true) {
      this.show = show
    },
    handleClick() {
      this.$emit('confirm')
      this.open(false)
      this.$router.push('/winningRecords')
    }
  }
}
</script>

<style scoped>
.prizeDialog {
  background: none;
}
.prizeDialogContainer {
  position: fixed;
  top: 2.66rem;
  left: 0;
  width: 100%;
}
.prizeDialogContainer h2 {
  height: 0.64rem;
  opacity: 1;
  color: #1e2228ff;
  font-size: 0.48rem;
  font-weight: 600;
  font-family: 'PingFang SC';
  text-align: center;
  line-height: 0.64rem;
  margin: 0.18rem auto 0.4rem;
}
.custom-button {
  width: 196px;
  height: 68px;
  background: url('kit/assets/images/marketing/mobile/blindBox/<EMAIL>')
    no-repeat center;
  background-size: cover;
  color: #ffffffff;
  font-size: 16px;
  font-weight: 500;
  font-family: 'PingFang SC';
  text-align: center;
  padding: 0;
  border: 0;
  display: block;
  margin: 10px auto;
}
.custom-button span {
  display: block;
  margin-bottom: 12px;
}
.close {
  position: absolute;
  width: 1rem;
  height: 1rem;
  color: #fff;
  top: -0.16rem;
  right: 0;
  font-size: 0.64rem;
  text-align: center;
  line-height: 1rem;
  opacity: 0.8;
}
</style>
