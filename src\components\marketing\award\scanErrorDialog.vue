<template>
  <Dialog v-model="show" :showConfirmButton="false">
    <div style="min-height: 423px;">
      <img src="kit/assets/images/<EMAIL>" v-if="type==='end'" style="width:320px;" alt="">
      <img src="kit/assets/images/<EMAIL>" v-if="type==='full'" style="width:320px;" alt="">
      <img src="kit/assets/images/<EMAIL>" v-if="type==='close'" style="width:320px;" alt="">
      <div class="content">
        <h1>{{title}}</h1>
        <p>{{content}}</p>
      </div>
    </div>
    <!-- <footer><button @click="open(false)">我知道了</button></footer> -->
  </Dialog>
</template>
<script>
import { Dialog } from 'vant';
// 局部注册
export default {
  props:{
    type:{
      default:"end",
      type:String
    },
    title:{
      default:"活动结束",
      type:String
    },
    content:{
      default:"活动结束，请下次趁早哦~",
      type:String
    },
  },
  components: {
    Dialog: Dialog.Component,
  },
  data(){
    return {
      show:false
    }
  },
  methods:{
    open(show=true){
      this.show = show
    }
  }
};
</script>

<style scoped>
.content{
  position: absolute;
  top: 240px;
  width: 100%;
  left:0;
  padding: 0 .4rem;
  box-sizing: border-box;
}
h1{
  color: #1e2228ff;
  font-size: 18px;
  font-weight: 600;
  font-family: "PingFang SC";
  text-align: left;
  line-height: 26px;
  width: 100%;
  text-align: center;
}
p{
  color: #828b9bff;
  font-weight: 400;
  font-family: "PingFang SC";
  text-align: left;
  font-size: .28rem;
  line-height: .44rem;
  text-align: center;
}
button{
  width: 174px;
  height: 52px;
  background: url("kit/assets/images/<EMAIL>") no-repeat center;
  background-size: cover;
  color: #ffffffff;
  font-size: 14px;
  font-weight: 400;
  font-family: "PingFang SC";
  text-align: center;
  border:0;
  text-indent: -9999999px;
}
footer{
  position:absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 22px;
}
</style>