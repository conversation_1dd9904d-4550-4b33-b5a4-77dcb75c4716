<template>
  <div class="activity">
    <div @click="detail">
      <div class="title" style="display: flex; align-items: flex-start">
        <div
          style="margin-top: 5px"
          v-if="activity.status === '1'"
          class="tag-start"
        >
          待开始
        </div>
        <div
          style="margin-top: 5px"
          v-if="activity.status === '2'"
          class="tag-progress"
        >
          进行中
        </div>
        <div
          style="margin-top: 5px"
          v-if="activity.status === '3'"
          class="tag-start"
        >
          已结束
        </div>
        <div style="flex: 1">
          <h2 style="padding: 0; margin: 0">{{ activity.name }}</h2>
        </div>
        <i
          style="margin-top: 5px"
          class="icon iconfont icon-direction-arrow-border-right"
        ></i>
      </div>
      <div :class="[activity.status !== '2' ? 'no-margin' : 'content']">
        <div class="time">
          <span>活动时间：</span>
          <div
            class="text-ellipsis-2line"
            style="flex: 1; position: relative; top: 0.5px"
          >
            {{ activityTime }}
          </div>
        </div>

        <div
          style="display: flex; color: #828b9b; font-size: 14px; margin: 4px 0"
        >
          <span>达标资格：</span>
          <div class="text-ellipsis-2line" style="flex: 1">
            {{ activity.qualification }}
          </div>
        </div>

        <div style="display: flex; color: #828b9b; font-size: 14px">
          <span>活动奖励：</span>
          <div class="text-ellipsis-2line" style="flex: 1">
            {{ activity.rewardInfo }}
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="activity.status === '2'"
      style="display: flex; justify-content: space-around; font-size: 14px"
    >
      <a style="color: #4e5769" @click="copyLink">复制链接</a>
      <span style="color: #e4e7ed">|</span>
      <a style="color: #f77234" @click="$emit('showQrCode', activity)">
        展开二维码
      </a>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    activity: Object,
    linkUrl: String
  },
  data() {
    return {
      activityTime: ''
    }
  },
  created() {
    this.activityTime =
      this.activity.availableBeginTime.split(' ')[0] +
      ' ~ ' +
      this.activity.availableEndTime.split(' ')[0]
  },
  methods: {
    detail() {
      this.$router.push({
        path: '/activityDetail',
        query: {
          id: this.activity.id,
          promoterName: this.activity.promoter.name
        }
      })
    },
    copyLink() {
      this.$emit('copyLink', this.activity.id)
    }
  }
}
</script>

<style scoped>
.text-ellipsis-2line {
  overflow: hidden;
  text-overflow: ellipsis;
  /* 超出部分省略号 */
  word-break: break-all;
  /* break-all(允许在单词内换行。) */
  display: -webkit-box;
  /* 对象作为伸缩盒子模型显示 */
  -webkit-box-orient: vertical;
  /* 设置或检索伸缩盒对象的子元素的排列方式 */
  -webkit-line-clamp: 2;
  /* 显示的行数 */
  padding-right: 20px;
}
.tag-progress {
  width: 44px;
  height: 20px;
  line-height: 20px;
  border-radius: 4px;
  opacity: 1;
  background: #07bb06ff;
  color: #ffffffff;
  font-size: 12px;
  text-align: center;
  margin-right: 12px;
}
.tag-start {
  width: 44px;
  height: 20px;
  line-height: 20px;
  border-radius: 4px;
  opacity: 1;
  background: #a6aebd;
  color: #ffffffff;
  font-size: 12px;
  text-align: center;
  margin-right: 12px;
}
.time {
  display: flex;
  align-items: center;
  color: #828b9b;
  font-size: 14px;
}
.content {
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #f1f1f1;
  padding: 8px 0;
  margin-bottom: 16px;
}
.no-margin {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
  margin-bottom: 0;
}
</style>
