<template>
  <div style="height: 420px">
    <div
      style="
        width: 108px;
        height: 28px;
        position: absolute;
        left: 160px;
        margin-left: -54px;
        background: #ffffffff;
        text-align: center;
        color: #f77234ff;
        font-size: 14px;
        line-height: 28px;
        border-radius: 0 0 8px 8px;
      "
    >
      {{ promoterName }}
    </div>
    <div style="width: 100%; height: 0.88rem; position: absolute; top: 50px">
      <div style="display: flex; flex-direction: column; align-items: center">
        <div style="color: #ffffffff; font-size: 14px">
          剩余可发放：{{ leftCount }}
        </div>
        <div
          style="
            color: #f77234ff;
            font-size: 12px;
            background: #fff3b5ff;
            display: flex;
            align-items: center;
            padding: 2px 0 2px 8px;
          "
          @click="info"
        >
          <span>查看已发放明细</span>
          <i class="icon iconfont icon-direction-arrow-border-double-right"></i>
        </div>
      </div>
    </div>
    <img src="../../../assets/images/qr-code-bg.png" />
    <img
      v-if="leftCount !== 0"
      style="position: absolute; height: 150px; left: 27%; top: 27%"
      :src="qrCodeURL"
    />

    <div
      v-if="!isEffective"
      style="flex-direction: column"
      class="lose-efficacy"
      @click="refreshQrCode"
    >
      <img width="66px" src="kit/assets/images/refresh.png" />
      <div style="color: #f77234; font-size: 14px; font-weight: 600">刷新</div>
    </div>
    <div v-if="leftCount === 0" class="lose-efficacy">
      <img width="99px" src="kit/assets/images/Layer_1.png" />
    </div>
    <div v-if="isEffective && leftCount !== 0" class="effective">
      有效时间：{{ secondsToMs(effectiveTime) }}
    </div>
    <div v-if="!isEffective" class="invalid">二维码已失效</div>
    <div v-if="leftCount === 0" class="lack">剩余可发放数量不足</div>
    <div class="button" @click="closeQrCode">关闭二维码</div>
  </div>
</template>
<script>
var timer = null
import { secondsToMs } from 'kit/formatters/secondsToMs'
export default {
  props: {
    id: Number,
    promoterName: String,
    qrCodeURL: String,
    leftCount: Number
  },
  data() {
    return {
      effectiveTime: 6,
      isEffective: true
    }
  },
  created() {},
  methods: {
    secondsToMs,
    start() {
      clearInterval(timer)
      this.effectiveTime = 300
      timer = setInterval(() => {
        if (this.effectiveTime <= 1) {
          this.isEffective = false
          clearInterval(timer)
          return
        }

        this.effectiveTime--
      }, 1000)
    },
    refreshQrCode() {
      this.isEffective = true
      this.$emit('refreshQrCode')
    },
    closeQrCode() {
      this.isEffective = true
      clearInterval(timer)
      this.$emit('closeQrCode')
    },
    info() {
      this.$router.push({
        path: '/distributionDetails',
        query: { id: this.id }
      })
    }
  }
}
</script>
<style scoped>
.effective {
  color: #cc2329ff;
  font-size: 14px;
  position: absolute;
  left: 105px;
  top: 290px;
}
.lose-efficacy {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: 150px;
  height: 150px;
  left: 27%;
  top: 27%;
  background: #ffffffe6;
}
.invalid {
  color: #cc2329ff;
  font-size: 14px;
  position: absolute;
  left: 116px;
  top: 290px;
}
.lack {
  color: #cc2329ff;
  font-size: 14px;
  position: absolute;
  left: 100px;
  top: 290px;
}
.button {
  position: absolute;
  bottom: 30px;
  left: 73px;
  width: 173px;
  height: 52px;
  line-height: 45px;
  color: #ffffff;
  font-size: 14px;
  text-align: center;
  background-image: url('../../../assets/images/btn_bg.png');
}
</style>
