<template>
  <Tabbar route style="padding: 10px 0 16px">
    <TabbarItem replace to="/activities" name="activities">
      <template>
        <img
          width="24px"
          style="height: 40px"
          :src="
            activeCode === 'activities' ? iconWorkbenchActive : iconWorkbench
          "
        />
      </template>
    </TabbarItem>
    <TabbarItem replace to="/mine" name="mine">
      <template>
        <img
          width="24px"
          style="height: 40px"
          :src="activeCode === 'mine' ? iconMineActive : iconMine"
        />
      </template>
    </TabbarItem>
  </Tabbar>
</template>

<script>
import iconMine from 'kit/assets/images/icon/mine.png'
import iconMineActive from 'kit/assets/images/icon/mine_active.png'
import iconWorkbench from 'kit/assets/images/icon/workbench.png'
import iconWorkbenchActive from 'kit/assets/images/icon/workbench_active.png'
import { Tabbar, TabbarItem } from 'vant'
export default {
  components: {
    Tabbar,
    TabbarItem
  },
  data() {
    return {
      activeCode: this.$route?.meta?.code,
      iconMine,
      iconMineActive,
      iconWorkbench,
      iconWorkbenchActive
    }
  },
  methods: {
    switchWorkbench() {
      this.$emit('goWorkbench')
    },
    switchMine() {
      this.$emit('goMine')
    }
  }
}
</script>

<style scoped></style>
