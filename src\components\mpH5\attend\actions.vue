<template>
  <div
    class="actions"
    style="
      margin-top: 2px;
      height: 79px;
      box-shadow: 0 -4px 8px 0 rgba(0, 0, 0, 0.05);
      padding: 5px 0 26px 0;
      display: flex;
      background: #fff;
      box-sizing: border-box;
    "
  >
    <div
      style="width: 50%; text-align: center"
      :style="{ color: active === 0 ? '#4f71ff' : '#adb5d0' }"
      @click="$emit('goCheckIn')"
    >
      <i
        class="iconfont icon-clock-in-full"
        style="display: block; font-size: 32px"
      />
      <span style="font-size: 14px"> 打卡</span>
    </div>
    <div
      style="width: 50%; text-align: center"
      :style="{ color: active === 1 ? '#4f71ff' : '#adb5d0' }"
      @click="$emit('goStats')"
    >
      <i
        class="iconfont icon-statistics-full"
        style="display: block; font-size: 32px"
      />
      <span style="font-size: 14px">统计</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    active: {
      type: Number
    }
  }
}
</script>

<style>
</style>