<template>
  <div
    style="
      background: #fff;
      border-radius: 6px;
      padding: 10px 15px;
      line-height: 22px;
      margin-bottom: 10px;
      color: #262935;
    "
    class="action"
    v-if="checkIn"
  >
    <div style="color: #71788f" v-if="checkIn.isWorkDay">
      <span v-if="!attendGroup.isFreeMode()">
        {{ checkIn.goWork() ? '上班' : '下班' }}时间
        {{ checkIn.workTime() }}
        {{ checkIn.isShowCrossingDayWorkTime() ? '(次日)' : '' }}
      </span>
      <span v-else> {{ checkIn.goWork() ? '上班' : '下班' }}打卡 </span>
    </div>
    <div v-else style="color: #71788f">您今天休息</div>

    <CheckInActionButton
      :checkIn="checkIn"
      :attendGroup="attendGroup"
      :location="location"
      :style="{ opacity:address.includes('不在考勤范围')?0.5:1 }"

      @click="$emit('checkIn', checkIn)"
    />

    <div style="text-align: center" v-if="!location">
      当前位置服务未开启，需打开定位服务
    </div>
    <div v-else style="text-align: center">
      <div
        :style="{
          height: '65px',
          visibility: location && location.standardAddress ? '' : 'hidden'
        }"
      >
        <i
          v-show="address.includes('不在考勤范围')"
          class="iconfont icon-remind-exclamation-circle"
          style="position: relative; top: 1px"
        />
        <i
          class="iconfont icon-remind-check-circle"
          style="color: #00b4b3"
          v-show="!address.includes('不在考勤范围')"
          v-if="!location.isOutSide"
        />
        <i
          v-show="!address.includes('不在考勤范围')"
          v-else
          class="iconfont icon-position-full"
        />
        {{ address }}
      </div>

      <div @click="
            () => {
              startLoading()
              $emit('reLocation')
            }
          "
        >
        <a
          style="color: #4f71ff"
          v-if="!location.loading"
        >
          重新定位
        </a>
        <span style="color: #4f71ff" v-else >重新定位中{{ loading }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import CheckInActionButton from './checkInActionButton.vue'
var timeHandler = null
export default {
  components: {
    CheckInActionButton,
  },
  computed: {
    address() {
      if (this.location.failReason) {
        return this.location.failReason
      }
      if (!this.location.standardAddress) {
        return '未获取到地址'
      }

       console.log({
        attendGroup:this.attendGroup,checkIn:this.checkIn,location:this.location
      })

      // 自有班制判断
      if(!this.attendGroup.attendPlaceList.length) {
        return `当前地址：${this.location.standardAddress}`
      }

      if (this.location.isOutSide && !this.attendGroup.allowedOutside) {
        return '考勤范围：当前地点不在考勤范围内'
      }
      if (!this.location.isOutSide) {
        return `已进入考勤范围：${this.location.standardAddress}`
      }

     

      return `当前地址：${this.location.standardAddress}`
    }
  },
  watch: {
    'location.loading': {
      handler(v) {
        if (v) {
          clearInterval(timeHandler)
        }
      }
    }
  },
  props: {
    location: {
      type: Object,
      default: null
    },
    attendGroup: {
      type: Object,
      default: null
    },
    checkIn: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      loading: ''
    }
  },
  methods: {
    startLoading() {
      this.loading = '...'
    //   timeHandler = setInterval(() => (this.loading += '.'), 1000)
    }
  }
}
</script>

<style>
</style>