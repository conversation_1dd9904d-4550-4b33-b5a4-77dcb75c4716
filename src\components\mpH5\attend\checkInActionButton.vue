<template>
  <div
    :class="{outSideWrap:isOutSide}"
    style="margin: 20px auto; width: 170px; height: 170px; border-radius: 50%"
    :style="{
      background: `${realColor}50`,
    }"
    @click="$emit('click')"
  >
    <div
      :class="{outSide:isOutSide}"
      style="
        width: 160px;
        height: 160px;
        border-radius: 50%;
        position: relative;
        top: 5px;
        left: 5px;
        font-size: 24px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      "
      :style="{
        background: `${realColor}`
      }"
    >
      <div style="text-align: center">
        {{ now }}
        <div style="font-size: 14px">
          {{ actionText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import formatDateTime from 'kit/formatters/dateTime'
export default {
  computed: {
    actionText() {
      const _ = this.now
      return this.checkIn.action(this.attendGroup, this.location)
    },
    isOutSide(){
      return this.actionText === '外勤打卡'
    },
    realColor() {
      const _ = this.now
      const color = this.checkIn.actionButtonColor(
        this.attendGroup,
        this.location
      )

      switch (color) {
        case 'red':
          return '#e14c46'
        case 'green':
          return '#00b4b3'
        case 'gray':
          return '#aac6f5'
        default:
          return '#4185f8'
      }
    }
  },
  props: {
    checkIn: {
      type: Object,
      default: null
    },
    location: {
      type: Object,
      default: null
    },
    attendGroup: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      now: formatDateTime({ format: 'HH:mm:ss' })
    }
  },
  created() {
    setInterval(() => {
      this.now = formatDateTime({ format: 'HH:mm:ss' })
    }, 1000)
  }
}
</script>

<style scoped>
  .outSide{
    background: #00ADAA !important ;
  }
  .outSideWrap{
    background: #6BD6D4 !important;
  }
</style>
