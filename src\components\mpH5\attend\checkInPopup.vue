<template>
  <Popup
    v-model="shown"
    position="bottom"
    closeable
    round
    style="font-weight: bold;"
    @close="$emit('close')"
    v-bind="$attrs"
  >
    <div style="padding: 0 15px">
      <h3 style="text-align: center; padding: 30px 0 20px 0">{{ title }}</h3>
      <span>打卡时间: {{ now }}</span>
      <div
        class="address"
        style="color: #71788f; position: relative; top: 12px"
        v-if="address"
      >
        <i class="iconfont icon-position-full" />
        {{ address }}
      </div>
      <hr style="margin: 20px 0" color="#f5f5f5" />
      <Uploader
        multiple
        :max-count="3"
        v-model="fileList"
        class="uploader"
        style="width: 100%"
        :after-read="file => $emit('upload', file)"
      >
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
          "
        >
          <span>
            拍照
            <span v-if="phonesRequired" style="color: red">*</span>
            <span style="font-weight: 300">(最多3张)</span>
          </span>
          <i
            style="
              color: #3470fa;
              font-size: 24px;
              position: absolute;
              right: 10px;
            "
            class="iconfont icon-photograph-full"
          />
        </div>
      </Uploader>
      <hr style="margin: 20px 0" color="#f5f5f5" />
      备注 <span v-if="commentRequired" style="color: red">*</span>
      <Field
        v-model="value"
        style="
          background: #eff0f4;
          height: 85px;
          margin-top: 20px;
          border-radius: 5px;
          margin-bottom: 40px;
        "
        type="textarea"
        maxlength="200"
        placeholder="最多可输入200字"
      />
    </div>
    <div style="background: #eff0f4; height: 5px"></div>
    <div
      style="
        text-align: center;
        height: 80px;
        line-height: 80px;
        font-size: 18px;
      "
    >
      <a style="color: #3470fa" @click="$emit('checkIn', value)">
        {{ actionTitle }}
      </a>
    </div>
  </Popup>
</template>

<script>
import { Field, Uploader, Popup } from 'vant'
import formatDateTime from 'kit/formatters/dateTime'
export default {
  components: { Field, Uploader, Popup },
  created() {
    setInterval(() => {
      this.now = formatDateTime({ format: 'HH:mm:ss' })
    }, 1000)
  },
  props: {
    phonesRequired: Boolean,
    commentRequired: Boolean,
    title: {
      type: String,
      default: '确认早退打卡吗?'
    },
    actionTitle: {
      type: String,
      default: '早退打卡'
    },
    address: {
      type: String
    },
    defaultShown: {
      type: Boolean
    }
  },
  data() {
    return {
      fileList: [],
      value: '',
      now: formatDateTime({ format: 'HH:mm:ss' }),
      shown: this.defaultShown
    }
  },
  methods: {
    open() {
      this.shown = true
    },
    close() {
      this.shown = false
    }
  }
}
</script>

<style scoped>
.uploader ::v-deep .van-uploader__input-wrapper {
  order: 0;
}
.uploader ::v-deep .van-uploader__preview {
  order: 1;
}
</style>