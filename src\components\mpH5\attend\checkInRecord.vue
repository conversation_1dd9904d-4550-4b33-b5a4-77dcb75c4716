<template>
  <div
    v-if="checkInRecord"
    style="
      background: #fff;
      border-radius: 6px;
      padding: 10px 15px;
      line-height: 32px;
      margin-bottom: 10px;
    "
    class="checkInRecord"
  >
    <div
      style="color: #71788f"
      v-if="!checkInRecord.isFreeMode()"
      class="workTime"
    >
      {{ checkInRecordsLength > 2 ? checkInRecord.timesString() : '' }}
      {{ checkInRecord.goWork() ? '上班时间' : '下班时间' }}
      {{ checkInRecord.shortStandardTime() }}
      {{ checkInRecord.isShowCrossingDayStandardFlag() ? '(次日)' : '' }}
    </div>
    <div style="color: #71788f" v-else>
      {{ checkInRecordsLength > 2 ? checkInRecord.timesString() : '' }}
      {{ checkInRecord.goWork() ? '上班' : '下班' }}
    </div>

    <div style="display: flex; justify-content: space-between; color: #71788f">
      <span v-if="checkInRecord.isNeedShowCheckInTime()">
        打卡时间
        {{ checkInRecord.shortCheckInTime() }}
      </span>
      <span v-else>无打卡时间</span>

      <Tag
        round
        size="large"
        :color="`${checkInRecord.resultColor(attendGroup)}40`"
        style="padding: 0px 16px"
        :style="{
          color: checkInRecord.resultColor(attendGroup)
        }"
      >
        {{ checkInRecord.tagString(attendGroup) }}
      </Tag>
    </div>

    <div
      class="location"
      style="color: #71788f"
      v-if="checkInRecord.recordAddress"
    >
      <i class="iconfont icon-position-full" />
      {{ checkInRecord.recordAddress }}
    </div>
    <div v-else style="color: #71788f">
      <i class="iconfont icon-position-full" />
      无打卡位置
    </div>
    <div style="display: flex; justify-content: space-between; color: #4f71ff">
      <a
        v-if="checkInRecord.isNeedShowUpdateTimeButton()"
        @click="$emit('updateWorkingTime', checkInRecord)"
      >
        更新{{ checkInRecord.goWork() ? '上班时间' : '下班时间' }}
      </a>
      <a
        v-if="checkInRecord.isNeedShowFixAttendButton(attendGroup)"
        @click="$emit('fixAttend', checkInRecord)"
      >
        {{ checkInRecord.fixAttendButtonLabel() }}
      </a>
    </div>
  </div>
</template>

<script>
import { Tag } from 'vant'
export default {
  components: {
    Tag
  },
  props: {
    checkInRecordsLength: Number,
    attendGroup: {
      type: Object,
      default: null
    },
    checkInRecord: {
      type: Object,
      default: null
    }
  }
}
</script>

<style>
</style>