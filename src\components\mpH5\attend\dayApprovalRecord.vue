<template>
  <div
    class="dayApprovalRecord"
    style="background: #fff; border-radius: 6px; padding: 10px 15px"
    @click="$emit('goApproval', approvalRecord)"
  >
    <div style="display: flex; align-items: center">
      <i
        style="font-size: 20px"
        :style="{
          color: iconColor(approvalRecord.approveType)
        }"
        :class="approvalRecord.approveType | iconClass"
      />
      {{ approvalRecord.approveType | statusToString }}
    </div>
    <div
      v-for="(approve, index) in approvalRecord.approveList"
      :key="index"
      style="margin-top: 10px"
    >
      <span v-if="approvalRecord.approveType !== 'SUPPLY_PASS'">
        {{ approve.startDate.slice(0, -3) }} ~
        {{ approve.endDate.slice(0, -3) }}
      </span>
      <span v-else>{{ approve.startDate.slice(0, -3) }}</span>
    </div>
     <div v-if="approvalRecord.approveType !== 'SUPPLY_PASS'" style="color:#666;margin-top:10px;">
        共{{ approvalRecord.leaveDuration}}{{ approvalRecord.approveList[0].leaveUnit | unit }}
      </div>
  </div>
</template>

<script>
import signResultToString from 'kit/formatters/mpH5/signResultToString'
export default {
  filters: {
    unit(v) {
      switch (v) {
        case 'HOUR':
          return '小时'
        case 'DAY':
          return '天'
      }

      return v
    },
    iconClass(v) {
      switch (v) {
        case 'LEAVE':
          return 'iconfont icon-color-ask-for-leave'
        case 'SUPPLY_PASS':
          return 'iconfont icon-color-attendance-make-up-card'
        case 'BUSINESS_TRIP':
          return 'iconfont icon-color-be-away-on-official-business'
        case 'OUTSIDE_WORK':
          return 'iconfont icon-color-application-for-going-out'
        case 'OVER_TIME':
          return 'iconfont icon-color-work-overtime'
      }
    },
    statusToString(v) {
      return signResultToString(v)
    }
  },
  props: {
    approvalRecord: Object
  },
  methods: {
    iconColor(v) {
      switch (v) {
        case 'SUPPLY_PASS':
          return '#3470fa'
        case 'LEAVE':
          return '#2caaf2'
        case 'BUSINESS_TRIP':
          return '#e29f67'
        case 'OUTSIDE_WORK':
          return '#6b91e8'
        case 'OVER_TIME':
          return '#78d6ca'
      }
    }
  }
}
</script>

<style>
</style>