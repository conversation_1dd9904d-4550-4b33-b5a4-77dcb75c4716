<template>
  <Steps
    v-if="checkInRecords.length"
    class="steps"
    direction="vertical"
    style="background: #f3f4f5; margin-right: 5px"
    active-color="#5e647d"
    inactive-color="#4f71ff"
    finish-icon="checked"
    active-icon="clock"
    :active="999"
  >
    <Step
      v-show="checkInRecord.shown()"
      :key="`checkIn${index}`"
      v-for="(checkInRecord, index) in checkInRecords"
    >
      <CheckInRecord
        :checkInRecordsLength="checkInRecords.length"
        :checkInRecord="checkInRecord"
        :attendGroup="attendGroup"
        @fixAttend="v => $emit('fixAttend', v)"
      />
    </Step>
  </Steps>
  <div
    v-else
    style="margin: 40px auto; width: 220px; text-align: center; color: #848587"
  >
    <img :src="emptyIcon" width="108" />
    <br />
    暂无打卡记录
  </div>
</template>

<script>
import { Steps, Step } from 'vant'
import CheckInRecord from './checkInRecord.vue'
import emptyIcon from 'kit/assets/images/icon/empty.png'
export default {
  components: {
    Steps,
    Step,
    CheckInRecord
  },
  props: {
    checkInRecords: Array,
    attendGroup: Object
  },
  data() {
    return {
      emptyIcon
    }
  }
}
</script>

<style>
</style>