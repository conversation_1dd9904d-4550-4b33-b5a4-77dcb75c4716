<template>
  <div
    v-if="employee"
    class="employeeInfo"
    style="
      padding: 15px;
      height: 80px;
      box-sizing: border-box;
      box-shadow: 0 2px rgba(0, 0, 0, 0.05);
      border-radius: 6px;
      background: #fff;
      display: flex;
      align-items: center;
    "
  >
    <div
      class="tag"
      style="
        height: 50px;
        flex: 0 0 50px;
        margin-right: 10px;
        font-size: 18px;
        background: #4185f8;
        line-height: 50px;
        color: #fff;
        text-align: center;
        border-radius: 6px;
      "
    >
      {{ employee.shortName() }}
    </div>
    <div style="flex: 1">
      <div class="name">{{ employee.empName || '暂无姓名' }}</div>
      <div
        class="company"
        style="color: #71788f"
        v-if="showAttendGroup && !attendGroup"
      >
        未加入考勤组
      </div>
      <div
        v-if="showAttendGroup && attendGroup"
        class="company"
        style="color: #71788f"
      >
        {{ attendGroup.agName }}
        <a style="color: #4f71ff" @click="$emit('goAttendRule')">考勤规则</a>
      </div>
    </div>
    <Button
      v-if="attendGroup && attendGroup.opened()"
      round
      type="info"
      size="normal"
      style="height: 34px; flex: 0 0 70px"
      @click="$emit('goAttendApply')"
    >
      申请
    </Button>
  </div>
</template>

<script>
import { Button } from 'vant'
export default {
  components: { Button },
  props: {
    showAttendGroup: {
      type: Boolean,
      default: true
    },
    employee: Object,
    attendGroup: Object
  }
}
</script>
