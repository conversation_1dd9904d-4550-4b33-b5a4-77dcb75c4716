import formatDateTime from '../../../formatters/dateTime'

const isDate = date => {
  //use prototype
  return Object.prototype.toString.call(date) === '[object Date]'
}
function nextMonthFirstDate(date) {
  if (!isDate(date)) {
    throw new Error('date need be a Date object')
  }
  const copy = new Date(date)
  copy.setHours(0, 0, 0)
  //转为15号
  copy.setDate(15)

  const day = copy.getDate()
  copy.setDate(day + 17)
  copy.setDate(1)

  return copy
}

const monthLastDate = dateStr => {
  const date = new Date(dateStr)
  const nDate = nextMonthFirstDate(date)
  //减去一秒
  const time = nDate.getTime() - 1000
  const currentMonthLastDate = new Date(time)

  return currentMonthLastDate
}

export default monthLastDate
