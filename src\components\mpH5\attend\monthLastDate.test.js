import formatDateTime from '../../../formatters/dateTime'
import monthLastDate from './monthLastDate'

describe('currentMonthLastDate', () => {
  it('currentMonthLastDate', () => {
    var r = monthLastDate('2023-08-01')
    var output = formatDateTime({ format: 'yyyy-MM-dd' }, r)
    expect(output).toBe('2023-08-31')

    r = monthLastDate('2023-08-31')
    output = formatDateTime({ format: 'yyyy-MM-dd' }, r)
    expect(output).toBe('2023-08-31')

    r = monthLastDate('2023-08-15')
    output = formatDateTime({ format: 'yyyy-MM-dd' }, r)
    expect(output).toBe('2023-08-31')

    r = monthLastDate('2023-02-28')
    output = formatDateTime({ format: 'yyyy-MM-dd' }, r)
    expect(output).toBe('2023-02-28')

    r = monthLastDate('2023-02-15')
    output = formatDateTime({ format: 'yyyy-MM-dd' }, r)
    expect(output).toBe('2023-02-28')

    r = monthLastDate('2020-02-29')
    output = formatDateTime({ format: 'yyyy-MM-dd' }, r)
    expect(output).toBe('2020-02-29')

    r = monthLastDate('2020-02')
    output = formatDateTime({ format: 'yyyy-MM-dd' }, r)
    expect(output).toBe('2020-02-29')

    r = monthLastDate('2023-12-31')
    output = formatDateTime({ format: 'yyyy-MM-dd' }, r)
    expect(output).toBe('2023-12-31')

    r = monthLastDate('2023')
    output = formatDateTime({ format: 'yyyy-MM-dd' }, r)
    expect(output).toBe('2023-01-31')
  })
})
