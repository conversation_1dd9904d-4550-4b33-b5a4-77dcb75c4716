<template>
  <Calendar
    class="monthStatsCalendar"
    color="#1989fa"
    :poppable="false"
    :show-confirm="false"
    :show-subtitle="false"
    :show-mark="false"
    style="height: 320px; text-align: center"
    row-height="40"
    :default-date="currentSelectedDate"
    :min-date="currentMonthStartDate"
    :max-date="currentMonthLastDate"
    :formatter="calendarFormatter"
    first-day-of-week="1"
    @select="d => selectDate(d)"
  >
    <template v-slot:title>
      <div style="gap: 20px; display: flex; justify-content: center">
        <i
          class="iconfont icon-direction-arrow-border-left"
          @click="prevMonth"
        />
        <span>{{ fullMonth }}</span>
        <i
          class="iconfont icon-direction-arrow-border-right"
          @click="nextMonth"
        />
      </div>
    </template>
    <template v-slot:bottom-info="day">
      <div
        v-if="day.color"
        style="
          width: 6px;
          height: 6px;
          border-radius: 50%;
          display: inline-block;
        "
        :style="{
          background: `${day.color}`
        }"
      ></div>
    </template>
  </Calendar>
</template>

<script>
import formatDateTime from 'kit/formatters/dateTime'
import { Calendar } from 'vant'
import monthLastDate from './monthLastDate'
import statsColor from './statsColor'
const calcCurrentSelectDate = currentMonth => {
  const copy = new Date(currentMonth)
  copy.setDate(1)
  return copy
}
export default {
  components: {
    Calendar
  },
  computed: {
    fullMonth() {
      return formatDateTime({ format: 'yyyy年MM月' }, this.currentMonth)
    },
    currentMonthLastDate() {
      return monthLastDate(this.currentMonth)
    },
    currentMonthStartDate() {
      const date = new Date(this.currentMonth)
      date.setDate(1)
      return date
    }
  },
  props: {
    defaultDate: null,
    //map
    // {
    // day: color //ex '2023-08-12':'blue'
    // }
    dayColor: {
      type: Object,
      default: () => testDayColor
    }
  },
  data() {
    return {
      currentMonth: this.defaultDate ? this.defaultDate : new Date(),
      currentSelectedDate: this.defaultDate ? this.defaultDate : new Date()
    }
  },
  methods: {
    prevMonth() {
      const lastDate = new Date(this.currentMonthLastDate)
      const day = lastDate.getDate()
      lastDate.setDate(day - 32)
      this.currentMonth = lastDate

      this.selectDate(calcCurrentSelectDate(this.currentMonth))
      this.$emit(
        'switchMonth',
        formatDateTime(
          {
            format: 'yyyy-MM-01'
          },
          this.currentMonth
        )
      )
    },
    nextMonth() {
      const lastDate = this.currentMonthLastDate
      const time = lastDate.getTime() + 1000
      this.currentMonth = new Date(time)

      this.selectDate(calcCurrentSelectDate(this.currentMonth))
      this.$emit(
        'switchMonth',
        formatDateTime(
          {
            format: 'yyyy-MM-01'
          },
          this.currentMonth
        )
      )
    },
    calendarFormatter(day) {
      const dayStr = formatDateTime({ format: 'yyyy-MM-dd' }, day.date)
      if (this.dayColor && this.dayColor[dayStr]) {
        day.color = statsColor(this.dayColor[dayStr])
      }

      // console.log(day, dayStr, day.color)
      return day
    },
    selectDate(date) {
      this.currentSelectedDate = date
      this.$emit('selectDate', date)
    }
  }
}
</script>

<style scoped>
.monthStatsCalendar ::v-deep .van-calendar__header {
  box-shadow: none;
}
.monthStatsCalendar ::v-deep .van-calendar__month-title {
  display: none;
}
.monthStatsCalendar ::v-deep .van-calendar__bottom-info {
  text-align: center;
  bottom: -2px;
}
.monthStatsCalendar ::v-deep .van-calendar__selected-day {
  width: 22px !important;
  height: 22px !important;

  border-radius: 50% !important;
}
</style>