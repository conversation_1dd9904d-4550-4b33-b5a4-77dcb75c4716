<template>
  <div
    class="monthStatsSummaries"
    style="background: #fff"
    v-if="summaries && summaries.length"
  >
    <div style="padding: 15px" v-if="shortMonth">{{ shortMonth }}月统计</div>

    <div
      class="summaries"
      style="display: flex; color: #5e647d"
      @click="$emit('more')"
    >
      <div
        class="item"
        :key="index"
        v-for="(item, index) in summaries"
        style="flex: 1 1 20%; text-align: center; margin-bottom: 15px"
      >
        <span
          style="font-weight: 600; font-size: 24px"
          :style="{
            color: realColor(item.color)
          }"
        >
          {{ item.count }}
        </span>
        <div
          class="desc"
          style="
            margin-top: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
          "
        >
          <div
            class="dot"
            style="
              width: 5px;
              height: 5px;
              border-radius: 50%;
              margin-right: 5pxc;
              margin-right: 5px;
            "
            :style="{
              background: realColor(item.color)
            }"
          ></div>
          {{ item.label }}
        </div>
      </div>
      <div class="item" style="flex: 1 1 20%; text-align: center">
        <span
          style="
            font-weight: 600;
            font-size: 24px;
            position: relative;
            top: -5px;
          "
          >...</span
        >
        <div
          style="
            margin-top: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
          "
        >
          更多
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import statsColor from './statsColor'
export default {
  props: {
    shortMonth: String,
    // {
    //   count:number,
    //   color: red | green
    //   label: string
    // }
    summaries: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    realColor(color) {
      return statsColor(color)
    }
  }
}
</script>
