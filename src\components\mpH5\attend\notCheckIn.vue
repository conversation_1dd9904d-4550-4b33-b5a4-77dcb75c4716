<template>
  <div
    style="
      background: #fff;
      border-radius: 6px;
      height: 68px;
      color: #60667f;
      padding-left: 15px;
      display: flex;
      align-items: center;
    "
    class="noCheckIn"
    v-if="notCheckIn"
  >
    {{ notCheckIn.workTimesString() }}
    {{ notCheckIn.isShowCrossingDayStandardFlag() ? '(次日)' : '' }}
  </div>
</template>

<script>
export default {
  props: {
    notCheckIn: {
      type: Object,
      default: null
    }
  }
}
</script>

<style>
</style>