<template>
  <div
    class="shiftInfo"
    v-if="shiftInfo"
    style="color: #5e647d; text-align: center"
  >
    <div
      class="others"
      v-if="shiftInfo.groupName && shiftInfo.shiftType !== 'FREE'"
    >
      <div>{{ shiftInfo.groupName }}:</div>
      <span
        v-for="(item, index) in shiftInfo.workingShiftDetailResultList"
        :key="index"
      >
        {{ item.workingBegin | formatTime }}
        {{ isWorkCrossingDayByFlag(item.workBeginOverDayFlag) ? '(次日)' : '' }}
        ~
        {{ item.workingEnd | formatTime }}
        {{ isWorkCrossingDayByFlag(item.workEndOverDayFlag) ? '(次日)' : '' }}
      </span>
    </div>

    <div class="free" v-if="shiftInfo.shiftType === 'FREE'">
      在
      <span
        v-for="(item, index) in shiftInfo.workingShiftDetailResultList"
        :key="index"
      >
        {{ item.workingBegin | formatTime }} ~
        {{ item.workingEnd | formatTime }}
      </span>
      自由打卡
    </div>
  </div>
</template>

<script>
function isWorkCrossingDayByFlag(workEndFlag) {
  const CROSSDAY_FLAG = "NEXT_DAY"
  return CROSSDAY_FLAG === workEndFlag;
}
export default {
  filters: {
    formatTime(time) {
      return `${time.substr(0, 2)}:${time.substr(2)}`
    }
  },
  props: {
    shiftInfo: {
      type: Object
    }
  },
  methods: {
    isWorkCrossingDayByFlag,
  }
}
</script>

<style>
</style>