<template>
  <div
    class="shift-button-container"
  >
    <div
      class="shift-button"
      :key="`switch${index}`"
      v-for="(item, index) in attendSwithGroupItems"
      @click="handleClick(item, index)"
    >
      <div class="title">
        {{ item.title }}
      </div>
      <div class="date">{{ item.date }}</div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    attendSwithGroupItems: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    backgroundImage() {
    }
  },
  data() {
    return {
      defaultIndex: 0,
      activeIndex: 0,
    }
  },
  methods: {
    handleClick(item, index) {
      if (this.activeIndex === index) return

      this.activeIndex = index
      this.$emit('switchShift', item)
    }
  }
}
</script>
<style scoped>
.shift-button-container {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: 0 0;
  height: 56px;
  display: flex;
  align-items: center;
  padding: 0 15px 0 32px;
  background-clip: content-box;
}
.shift-button {
  flex: 1;
  text-align: center;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.shift-button-container .shift-button-active {
  border-radius: 4px;
}
.shift-button-container .title {
  font-family: PingFangSC-SNaNpxibold;
  font-weight: 600;
  font-size: 14px;
  color: #828b9b;
}
.shift-button-container .title-active {
  color: #a6aebd;
}
.shift-button-container .date {
  font-family: PingFangSC-Regular;
  font-size: 10px;
  color: #a6aebd;
}
.shift-button-container .date-active {
  color: #828b9b;
}
</style>