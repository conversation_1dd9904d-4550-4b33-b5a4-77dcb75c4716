<template>
  <Popup title="提示" v-model="dialogVisible" :style="{ height: '200px' ,width:'250px',padding:'20px'}">
    <h3>您暂未绑定邮箱</h3>
    <div>请在下方输入，稍后我们会将邮件发送至您的邮箱，请注意查收</div>
    <Field v-model="email" />
    <span  class="dialog-footer">
      <Button style="margin-right:20px" @click="dialogVisible = false">取 消</Button>
      <Button type="primary" @click="submit">确 定</Button>
    </span>
  </Popup>
</template>

<script>
import { Popup, Field,Button } from 'vant'
import  handleError  from '../../../helpers/handleErrorH5'
export default {
  name: 'BindMailboxDialog',
  components:{
    Popup,
    Field,
    Button
  },
  data() {
    return {
      email: '',
      dialogVisible: false
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
    },
    close() {
      this.email = ''
      this.dialogVisible = false
    },
    submit() {
      if (!this.email) {
        return handleError({ message: '请输入邮箱' })
      }
      this.$emit('submit', this.email)
      this.close()
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>