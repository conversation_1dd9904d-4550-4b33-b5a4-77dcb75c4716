<template>
  <Popup v-model="show" position="bottom" style="padding-bottom:0px" >
    <div class="item" @click="copy">复制链接</div>
    <!-- <div class="item" @click="sendEmail">发送至邮箱</div> -->
  </Popup>

</template>

<script>
import { Popup } from 'vant';

export default {
  components: {
    Popup
  },
  data() {
    return {
      show: false
    }
  },
  methods: {
    sendEmail(){
      this.$emit('sendEmail')
    },
    copy(){
      this.$emit('copy')
    },
    close() {
      this.show = false
    },
    open() {
      this.show = true
    }
  }
}
</script>

<style scoped>
  .item{
    text-align: center;
    padding : 30px 0;
    border-bottom: 1px solid #e6e6e6;
  }
</style>