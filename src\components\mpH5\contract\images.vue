<template>
  <div>
    <div
      style="position: fixed; z-index: 9; top: 40px; left: 35px"
      v-if="totalPages"
    >
      {{ currentPage }} / {{ totalPages }}
    </div>
    <VanImage
      style="margin-bottom: 10px"
      width="375"
      height="580"
      :key="index"
      v-for="(img, index) in imgs"
      :src="img"
      @load="load"
      @click="preview"
    />
  </div>
</template>

<script>
import { Image as VanImage, ImagePreview } from 'vant'
export default {
  name: 'images',
  components: {
    VanImage,
    ImagePreview
  },
  props: {
    imgs: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      totalPages: 0,
      currentPage: 0
    }
  },
  methods: {
    preview() {
      ImagePreview({
        images: this.imgs,
        startPosition: this.currentPage - 1,
        closeable: true
      })
    },
    load() {
      const documentElement = window.document.documentElement
      this.currentPage = Math.floor(documentElement.scrollTop / 590 + 1)
      this.totalPages = Math.floor(documentElement.offsetHeight / 590)

      window.onscroll = () => {
        const scrollTop = documentElement.scrollTop
        this.currentPage = Math.floor(scrollTop / 590 + 1)
      }
    }
  }
}
</script>

<style>
</style>