<template>
  <Popup v-model="show" @close="close" round closeable   :style="{ height: '220px',width:'300px',textAlign: 'center'}" >
    <h3>请输入拒绝通过原因</h3>
    <Field
      v-model="reason"
      rows="3"
      autosize
      type="textarea"
      maxlength="100"
      placeholder="请输入拒绝原因"
      show-word-limit
    />
    <div>
      <Button style="width: 100px;margin-right:10px" @click="close">取消</Button>
      <Button type="primary" style="width: 100px" @click="confirm">确定</Button>
    </div>
  </Popup>
</template>

<script>
import { Popup, Field,Button } from 'vant'

export default {
  name: 'RejectSign',
  components: {
    Popup,
    Field,
    Button
  },
  data() {
    return {
      show: false,
      reason:''
    }
  },
  methods: {
    open() {
      this.show = true
    },
    close() {
      this.reason = ''
      this.show = false
    },
    confirm(){
      this.$emit('confirm',this.reason)
      this.close()
    }
  }
}
</script>

<style>
</style>