<template>
  <div
    style="
      display: flex;
      justify-content: space-between;
      background: #fff;
      padding: 20px 0 20px 20px;
      border-radius: 8px;
      height: 60px;
      margin-bottom: 15px;
    "
  >
    <div>
      <h3 style="margin: 0; margin-bottom: 20px">
        {{ contract.contractName }}
      </h3>
      <div>
        <span
          style="
            background: #ffe8e6;
            color: #f43e3e;
            margin-right: 5px;
            display: inline-block;
            width: 15px;
            height: 15px;
          "
          >发</span
        >
        <span style="color: #666">{{ contract.taxSubName }}</span>
      </div>
    </div>
    <div style="display: flex">
      <div
        style="
          text-align: right;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        "
      >
        <div style="margin-bottom: 20px; color: #6a6f7f">
          {{ formatTime(contract.createdTime) }}
        </div>
        <div>{{ elContractStatus[contract.contractSignStatus] }}</div>
      </div>
      <div style="line-height: 60px; padding: 0 20px">
        <i
          style="color: #666"
          class="iconfont icon-direction-arrow-border-right"
        >
        </i>
      </div>
    </div>
  </div>
</template>

<script>
import { elContractStatus } from '../../../formatters/mpH5/constants'
import dateTime from '../../../formatters/dateTime'
export default {
  props: {
    contract: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      elContractStatus
    }
  },
  methods: {
    formatTime(time) {
      if (!time) return
      const data = new Date(time).getTime()
      return dateTime('yyyy-MM-dd', data)
    }
  }
}
</script>

<style>
</style>