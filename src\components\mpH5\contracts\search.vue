<template>
  <Search v-model="value" placeholder="请输入搜索关键词" @search="onSearch">
    <template #action>
      <div @click="onSearch">搜索</div>
    </template>
  </Search>
</template>

<script>
import { Search } from 'vant'

export default {
  components: {
    Search
  },
  data() {
    return {
      value: ''
    }
  },
  methods:{
    onSearch(val){
      this.$emit('search',val)
    }
  }
}
</script>

<style>
</style>