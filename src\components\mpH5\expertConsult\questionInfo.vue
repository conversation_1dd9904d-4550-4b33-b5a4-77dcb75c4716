<template>
  <div style="border-bottom: 1px solid #eee; padding-bottom: 10px">
    <h3 style="margin-bottom: 5px">{{ title }}</h3>
    <div
      style="
        height: 20px;
        padding: 0 10px;
        background: rgba(65, 133, 248, 0.1);
        border-radius: 10px;
        display: inline-block;
        color: #4f71ff;
        margin: 5px 0;
        font-size: 12px;
      "
    >
      {{ consultType }}
    </div>
    <div style="display: flex; justify-content: space-between; color: #aeb4bc">
      <span>{{ consultUserName }}</span>
      <span>{{ consultTime }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    consultType: {
      type: String,
      default: ''
    },
    consultUserName: {
      type: String,
      default: ''
    },
    consultTime: {
      type: String,
      default: ''
    }
  }
}
</script>

<style>
</style>