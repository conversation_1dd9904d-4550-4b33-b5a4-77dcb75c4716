<template>
  <div
    @click="$emit('detail', question.consultQuestionId)"
    style="border-bottom: 1px solid #eee; padding-bottom: 10px"
  >
    <h3 :class="{ haveRead: isHaveRead(question.replyState) }">
      {{ question.title }}
    </h3>
    <div style="display: flex; justify-content: space-between; color: #aeb4bc">
      <span>{{ expertConsultQuestionStatus[question.replyState] }}</span>
      <span>{{ question.createTime }}</span>
    </div>
  </div>
</template>

<script>
import { expertConsultQuestionStatus } from '../../../formatters/mpH5/constants'
export default {
  props: {
    question: {
      type: Object,
      required: true,
      validate: function (question) {
        const requiredProps = [
          'consultQuestionId',
          'createTime',
          'replyState',
          'title'
        ]
        if (!requiredProps.every(key => Object.keys(question).includes(key))) {
          return false
        }

        return true
      }
    }
  },
  data() {
    return {
      expertConsultQuestionStatus
    }
  },
  methods: {
    // PENDING_REPLY: "待回复",ALREADY_REPLY: "已回复",ALREADY_ASK: "已提问"
    isHaveRead(state) {
      return state === 'ALREADY_REPLY'
    }
  }
}
</script>

<style>
.haveRead {
  color: #aeb4bc;
}
</style>