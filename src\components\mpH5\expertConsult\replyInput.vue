<template>
  <Popup
    :style="{ height: '30%', width: '300px' }"
    v-model="show"
    round
    title="标题"
    show-cancel-button
  >
    <div style="position: 'relative'">
      <Field
        v-model="message"
        rows="6"
        autosize
        type="textarea"
        maxlength="50"
        placeholder="请输入回复内容"
        show-word-limitd
      />
      <div style="position: absolute; bottom: 0px; width: 100%">
        <Button style="width: 50%" @click="close">取消</Button>
        <Button style="width: 50%" type="primary" @click="confirm">确定</Button>
      </div>
    </div>
  </Popup>
</template>

<script>
import { Popup, Field, Button } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
export default {
  components: {
    Popup,
    Field,
    Button
  },
  data() {
    return {
      show: false,
      message: ''
    }
  },
  methods: {
    confirm() {
      if (this.message == '') {
        handleError('回复内容不能为空')
        return
      }
      this.$emit('confirm', this.message)
      this.close()
    },
    open() {
      this.show = true
    },
    close() {
      this.message = ''
      this.show = false
    }
  }
}
</script>

<style>
</style>