<template>
  <div>
    <div
      style="
        display: flex;
        align-items: center;
        height: 45px;
        border-top: 1px solid #eee;
        padding-top: 20px;
      "
    >
      <div
        style="
          border-radius: 50%;
          border: 2px solid #fff;
          box-shadow: 0 0 0 2px #4f71ff;
          background: #4f71ff;
          width: 40px;
          height: 40px;
          margin-right: 10px;
          color: #fff;
          text-align: center;
          font-size: 12px;
          line-height: 40px;
        "
      >
        {{ role }}
      </div>
      <div style="flex: 1">
        <h4 style="margin: 0 0 5px 0">{{ replyUserName }}</h4>
        <span> {{ replyTime }} </span>
      </div>
    </div>
    <div style="margin: 20px 0">{{ content }}</div>
  </div>
</template>

<script>
export default {
  props: {
    replyUserName: {
      type: String,
      default: ''
    },
    replyTime: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },
  computed: {
    role() {
      return this.replyUserName.includes('咨询') ? this.replyUserName : '专家'
    }
  }
}
</script>

<style>
</style>