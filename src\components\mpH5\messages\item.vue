<template>
  <div style="padding: 10px; background: #fff" @click="detail">
    <div style="display: flex; justify-content: space-between">
      <div style="display: flex">
        <i
          v-if="!message.read"
          style="
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 10px;
            background: #4f71ff;
            display: inline-block;
            align-self: center;
          "
        ></i>
        <span
          style="
            color: #24262a;
            font-size: 16px;
            font-weight: bold;
            align-self: center;
          "
          >{{ message.title }}</span
        >
      </div>
      <span style="color: #a8acba; font-size: 14px">{{
        message.updateTime
      }}</span>
    </div>
    <p
      style="color: #777; font-size: 14px; word-break: break-all"
      v-html="message.content"
    ></p>
  </div>
</template>

<script>
export default {
  props: {
    message: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    detail() {
      this.$emit('detail', this.message)
    }
  }
}
</script>

<style>
</style>