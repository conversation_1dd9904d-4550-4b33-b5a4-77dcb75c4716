<template>
  <div>
    <Field
      readonly
      clickable
      :value="value"
      @click="showPicker = true"
    />
    <Popup v-model="showPicker" round position="bottom">
      <Picker
        show-toolbar
        :columns="columns"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      />
    </Popup>
  </div>
</template>

<script>
import { Picker, Field, Popup } from 'vant'

export default {
  components: {
    Picker,
    Field,
    Popup
  },
  data() {
    return {
      columns: [
        { text: '全部', value: '' },
        { text: '消息通知', value: 'MESSAGE' },
        { text: '活动通知', value: 'ACTIVITY' },
        { text: '系统通知', value: 'SYSTEM' },
        { text: '公告通知', value: 'NOTICE' }
      ],
      value: '全部',
      showPicker: false
    }
  },
  methods: {
    onConfirm(value) {
      this.value = value.text
      this.showPicker = false
      this.$emit('input', value)
    }
  }
}
</script>

<style>
</style>