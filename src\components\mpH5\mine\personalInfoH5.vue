<template>
  <div class="personalInfo" style="display: flex; align-items: center">
    <img
      style="width: 58px; height: 58px; margin-right: 12px"
      :src="personalAvatar"
      alt=""
    />
    <div style="color: #ffffff">
      <h2
        style="
          margin: 0px 0 10px 0;
          font-family: PingFangSC-SNaNpxibold;
          font-weight: 600;
          font-size: 18px;
          text-align: left;
          line-height: 18px;
        "
      >
        {{ name }}，{{ timeGreet }}
      </h2>
      <div>
        <template v-if="joinMerchants <= 0"> 您还未加入任何企业 </template>
        <template v-else>
          {{ merchant.name }}
          <span
            v-if="joinedMerchant && joinedMerchant.length > 1"
            @click="changeMerchant"
            style="margin-left: 10px; cursor: pointer"
            class="iconfont icon-direction-interaction-swap"
          ></span>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import personalAvatar from 'kit/assets/images/personal_avatar.png'
export default {
  computed: {
    name() {
      if (this.user.realName) {
        return this.user.realName
      }

      const phone = this.user.cellPhone || this.user.cellphone
      if (phone) {
        return phone.replace(/(\d{3})(\d{4})(\d{4})/, '**$3')
      }

      return ''
    },
    timeGreet() {
      const hour = new Date().getHours()
      if (hour === 12) {
        return '中午好'
      }
      if (hour >= 12) {
        return '下午好'
      }
      if (hour >= 18) {
        return '晚上好'
      }

      return '上午好'
    },
    joinMerchants() {
      return this.joinedMerchant.filter(merchant => !merchant.disabled)
    }
  },
  props: {
    user: {
      type: Object,
      default: () => {}
    },
    merchant: {
      type: Object,
      default: () => {}
    },
    joinedMerchant: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      personalAvatar
    }
  },
  methods: {
    changeMerchant() {
      this.$emit('changeMerchant')
    }
  }
}
</script>

<style>
</style>