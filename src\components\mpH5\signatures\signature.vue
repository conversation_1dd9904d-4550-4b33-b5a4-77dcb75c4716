<template>
  <div style="border: 1px solid #eee">
    <div
      style="
        border-bottom: 1px solid #eee;
        height: 258px;
        display: flex;
        align-items: center;
      "
    >
      <img style="width: 100%" :src="signature.imageAddr" />
    </div>
    <div style="height: 54px; display: flex; align-items: center">
      <Checkbox
        style="
          display: flex;
          justify-content: center;
          align-items: center;
          flex: 1;
        "
        @click="$emit('setDefault', signature.imageId)"
        :value="signature.isdefaultImg"
      >
        设置默认
      </Checkbox>
      <div
        style="flex: 1; text-align: center"
        @click="$emit('remove', signature.imageId)"
        v-if="!notAllowDelete"
      >
        删除
      </div>
    </div>
  </div>
</template>

<script>
import { Checkbox } from 'vant'

const signImgType = {
  system: 'SYSTEM',
  handWriter: 'HAND_WRITER'
}
export default {
  components: {
    Checkbox
  },
  props: {
    signature: {
      type: Object,
      validator(value) {
        if (!Object.keys(value).length) {
          return false
        }

        const requiredProps = [
          'imageAddr',
          'imageId',
          'isdefaultImg',
          'signImgType'
        ]

        if (!requiredProps.every(key => Object.keys(value).includes(key))) {
          return false
        }

        return true
      }
    }
  },

  computed: {
    notAllowDelete() {
      return this.signature === signImgType.system
    }
  }
}
</script>

<style>
</style>