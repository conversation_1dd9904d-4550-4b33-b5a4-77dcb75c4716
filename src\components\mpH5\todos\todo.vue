<template>
  <div
    style="
      background: #fff;
      padding: 20px 20px 10px 20px;
      border-radius: 8px;
      margin-bottom: 15px;
    "
    @click="toDeal"
  >
    <div style="display: flex; justify-content: space-between;align-items: center;margin-bottom:10px">
      <h3 style="width:250px;margin: 0">{{ todo.title }}</h3>
      <span style="color: #02a7f0">{{status(todo)}}</span>
    </div>
    <div style="color: #555555">
      {{ todo.todoText }}
    </div>
    <div
      style="
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #a5a5a5;
        font-size: 14px;
        border: 1px solid #f9f9f9;
        margin-top: 10px;
        padding-top: 8px;
      "
    >
      <span>处理人: {{ todo.sponsorUser.realName }}</span>
      <span>{{ formatTime(todo.createTime) }}</span>
    </div>
  </div>
</template>

<script>
import { approvalTodo, contractTodo } from '../../../formatters/mpH5/constants'
export default {
  props: {
    todo: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    toDeal() {
      this.$emit('toDeal', this.todo)
    },
    formatTime(time) {
      if (!time) {
        return ''
      }
      const date = new Date(time)
      const formattedDateTime = new Intl.DateTimeFormat('zh-CN', {
        timeStyle: 'short',
        dateStyle: 'medium'
      }).format(date)
      return formattedDateTime
    },
    status(todo) {
      if (
        todo.appCode === approvalTodo.appCode &&
        todo.appTaskCode === approvalTodo.appTaskCode &&
        todo.status === "TODO"
      ) {
       return "待审批"
      }

      if (
        todo.appCode === contractTodo.appCode &&
        todo.appTaskCode === contractTodo.appTaskCode&&
        todo.status === "TODO"
      ) {
        
        return "待签署"
      }
    }
  }
}
</script>

<style>
</style>