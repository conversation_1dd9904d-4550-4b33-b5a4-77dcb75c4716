<template>
  <div
    style="
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 15px;
    "
  >
    <div
      style="display: flex; justify-content: space-between; align-items: center"
    >
      <h3 style="margin: 0">{{title}}</h3>
      <span>{{date}}</span>
    </div>
    <div style="margin-top:10px;color:#555555">
      <span>{{content}}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    date: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },

}
</script>

<style>
</style>