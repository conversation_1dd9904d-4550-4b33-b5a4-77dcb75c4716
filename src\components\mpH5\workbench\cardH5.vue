<template>
  <div
    class="card"
    style="
      position: relative;
      border-radius: 8px;
    "
  >
    <h2
      style="
        padding: 0;
        margin: 0;
        margin-bottom: 20px;
        font-size: 16px;
        color: #24262a;
        line-height: 16px;
      "
    >
      {{ title }}
    </h2>
    <div style="position: absolute; right: 0; top: 0">
      <slot name="extra">  </slot>
    </div>

    <slot />
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      validator: function (value) {
        if (!value) {
          return false
        }

        return true
      }
    }
  }
}
</script>

<style>
</style>