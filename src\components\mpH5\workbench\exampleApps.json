{"success": true, "message": null, "errorCode": "0", "data": {"menuGroupVos": [{"code": "EMPLOYEE", "name": "员工服务", "order": 1, "entryVos": [{"code": "SALARY_MANAGER", "name": "工资条", "icon": "icon_salary_sheet.png", "path": "/pages/salary/main", "order": 1, "type": "MP"}, {"code": "CONTRACT_MANAGEMENT", "name": "合同协议", "icon": "icon_contract.png", "path": "/pages/fileManage/main", "order": 2, "type": "MP"}, {"code": "ENTERPRISE_TRAINING", "name": "培训", "icon": "icon_training.png", "path": "/pages/training/main", "order": 3, "type": "MP"}, {"code": "HR", "name": "个人档案", "icon": "icon_personal_file.png", "path": "/pages_category/switchMy/personal/staffInfo/main", "order": 7, "type": "MP"}, {"code": "WORKFLOW", "name": "审批(旧)", "icon": "icon_approve.png", "path": "/viewApproval", "order": 8, "type": "APPROVAL"}]}, {"code": "HRATTEND", "name": "假勤服务", "order": 2, "entryVos": [{"code": "attendance", "name": "考勤打卡", "icon": "icon_attendance.png", "path": "/pages/checkWork/main", "order": 0, "type": "MP"}, {"code": "statistics", "name": "考勤统计", "icon": "icon_statistics.png", "path": "/pages/checkWork/main?jump=true", "order": 0, "type": "MP"}, {"code": "645b5d1f6037b5609c973fc8", "name": "出差申请流程", "icon": "34.png", "path": "/pages/workflow/main?designId=645b5d1f6037b5609c973fc8&processId=645a0d996037b5609c973ac2&processDesignId=645b5d1f6037b5609c973fc9&name=出差申请流程", "order": 0, "type": "MP"}, {"code": "63f56fc73c8e584064153078", "name": "因公外出流程", "icon": "34.png", "path": "/pages/workflow/main?designId=63f56fc73c8e584064153078&processId=635b8c493a68e32cc8ccba36&processDesignId=63f56fc73c8e584064153079&name=因公外出流程", "order": 0, "type": "MP"}, {"code": "642e3ae06037b5609c9683ef", "name": "请假申请流程", "icon": "34.png", "path": "/pages/workflow/main?designId=642e3ae06037b5609c9683ef&processId=635b7cdb36ff412aa6bba42d&processDesignId=642e3ae06037b5609c9683f0&name=请假申请流程", "order": 0, "type": "MP"}, {"code": "635b9ba936ff412aa6bba46e", "name": "补卡申请流程", "icon": "34.png", "path": "/pages/workflow/main?designId=635b9ba936ff412aa6bba46e&processId=635b76123a68e32cc8ccba08&processDesignId=635b9ba936ff412aa6bba46f&name=补卡申请流程", "order": 0, "type": "MP"}]}, {"code": "AUTOAPPROVAL", "name": "常用审批", "order": 3, "entryVos": [{"code": "634664b98ccaa8760b1197f6", "name": "费用报销流程", "icon": "30.png", "path": "/pages/workflow/main?designId=644656306037b5609c96d1af&processId=634664b98ccaa8760b1197f6&processDesignId=644656306037b5609c96d1b0&name=费用报销流程", "order": 0, "type": "MP"}, {"code": "63564c7432b7687873c59447", "name": "差旅报销流程", "icon": "30.png", "path": "/pages/workflow/main?designId=63a3edf6960720713788c612&processId=63564c7432b7687873c59447&processDesignId=63a3edf6960720713788c613&name=差旅报销流程", "order": 0, "type": "MP"}, {"code": "63565f9732b7687873c59740", "name": "付款申请流程", "icon": "30.png", "path": "/pages/workflow/main?designId=645a0bff6037b5609c9739f5&processId=63565f9732b7687873c59740&processDesignId=645a0bff6037b5609c9739f6&name=付款申请流程", "order": 0, "type": "MP"}, {"code": "635660cc32b7687873c5975d", "name": "招待费超限审批", "icon": "30.png", "path": "/pages/workflow/main?designId=6361d0b6c6d8c85d3f37b22c&processId=635660cc32b7687873c5975d&processDesignId=6361d0b6c6d8c85d3f37b22d&name=招待费超限审批", "order": 0, "type": "MP"}, {"code": "6356627e32b7687873c59775", "name": "财务特殊事项流程", "icon": "30.png", "path": "/pages/workflow/main?designId=6361e23ac6d8c85d3f37b253&processId=6356627e32b7687873c59775&processDesignId=6361e23ac6d8c85d3f37b254&name=财务特殊事项流程", "order": 0, "type": "MP"}, {"code": "63566cf232b7687873c597bf", "name": "发票申请流程", "icon": "30.png", "path": "/pages/workflow/main?designId=6373578b227e0d17373f0bcf&processId=63566cf232b7687873c597bf&processDesignId=6373578b227e0d17373f0bd0&name=发票申请流程", "order": 0, "type": "MP"}, {"code": "63466ed01b58397444fa80f4", "name": "借款申请流程", "icon": "30.png", "path": "/pages/workflow/main?designId=646ef90adfbc9479388f3fd5&processId=63466ed01b58397444fa80f4&processDesignId=646ef90adfbc9479388f3fd6&name=借款申请流程", "order": 0, "type": "MP"}, {"code": "641a9dc28f40267c5153c80e", "name": "权限申请流程1", "icon": "21.png", "path": "/pages/workflow/main?designId=6444fe806037b5609c96cbd3&processId=641a9dc28f40267c5153c80e&processDesignId=6444fe806037b5609c96cbd4&name=权限申请流程1", "order": 0, "type": "MP"}, {"code": "640ae4d18f40267c5153a349", "name": "钉灵工商户接口开通", "icon": "21.png", "path": "/pages/workflow/main?designId=64473b7e6037b5609c96d277&processId=640ae4d18f40267c5153a349&processDesignId=64473b7e6037b5609c96d278&name=钉灵工商户接口开通", "order": 0, "type": "MP"}, {"code": "640a92c58f40267c51539f82", "name": "接口产品使用", "icon": "21.png", "path": "/pages/workflow/main?designId=64473bcd6037b5609c96d27a&processId=640a92c58f40267c51539f82&processDesignId=64473bcd6037b5609c96d27b&name=接口产品使用", "order": 0, "type": "MP"}]}, {"code": "FINANCE", "name": "财务管理", "order": 5, "entryVos": null}, {"code": "SPIRITUAL", "name": "灵工管理", "order": 6, "entryVos": null}, {"code": "SERVICE_SECTOR", "name": "服务专区", "order": 7, "entryVos": [{"code": "WELFARE", "name": "企业福利", "icon": "icon_benefits.png", "path": "/pages_business/employee/welfare", "order": 0, "type": "MP"}, {"code": "YUAN_GONG_JIAN_KANG", "name": "员工健康", "icon": "/api/v3/download/d70afdd5d8c54d979df3d4a8fa190fe9/员工健康@2x.png", "path": "", "order": 3, "type": "MP"}, {"code": "QI_YE_TUAN_XIAN", "name": "企业团险", "icon": "/api/v3/download/5446e3fa929c4f8aa8abce8f985efc78/2x.png", "path": "", "order": 2, "type": "MP"}]}, {"code": "FINANCIAL", "name": "金融专区", "order": 8, "entryVos": [{"code": "DEPOSIT_YIBAO", "name": "易起富", "icon": "icon_financial_application.png", "path": "https://p2f.yeepay.com/index", "order": 0, "type": "H5"}]}, {"code": "OTHER", "name": "其他", "order": 9, "entryVos": null}]}}