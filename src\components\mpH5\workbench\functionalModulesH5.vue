<template>
  <Card style="background: #fff; padding: 20px 16px" :title="name">
    <template v-if="joinedMerchant.length === 0" #extra>
      <div
        style="
          width: 40px;
          height: 20px;
          background: #ffac04;
          border-radius: 0 8px 0 8px;
          border-radius: 0 8px 0 8px;
          color: #fff;
          text-align: center;
          font-size: 12px;
        "
      >
        示例
      </div>
    </template>
    <div style="display: flex; flex-wrap: wrap">
      <div
        style="
          flex: 0 0 25%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          gap: 5px;
          position: relative;
        "
        :style="{
          'margin-bottom': '10px'
        }"
        class="item"
        :key="index"
        v-for="(entry, index) in formatEntryVos"
        @click="$emit('go', entry)"
      >
        <Badge
          v-if="entry.quantity > 0"
          style="position: absolute; top: -6px; right: 10px"
          :content="entry.quantity"
        >
        </Badge>
        <img :src="src(entry.icon)" style="width: 32px; height: 32px" />
        <div
          style="
            height: 12px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #46485a;
            text-align: center;
            line-height: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 60px;
            margin: 0 auto;
          "
        >
          {{ entry.name }}
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
import { Badge } from 'vant'
import Card from './cardH5.vue'
import transferToStaticImageHref from 'kit/helpers/transferToStaticImageHref'
export default {
  components: {
    Card,
    Badge
  },
  computed: {
    formatEntryVos() {
      var r = []
      for (var i = 0; i < this.entryVos.length; i++) {
        const entry = this.entryVos[i]
        // if (entry.name.includes('考勤')) {
        //   continue
        // }
        if (i > 6) {
          r.push({
            name: '更多',
            icon: 'icon_more.png',
            type: 'MORE',
            path: '/index'
          })
          break
        }
        r.push(entry)
      }

      return r
    }
  },
  props: {
    name: {
      type: String,
      validator: function (value) {
        if (!value) {
          return false
        }

        return true
      }
    },
    entryVos: {
      type: Array,
      default: () => []
    },
    joinedMerchant: {
      type: Array,
      default: () => []
    }
  },
  created() {
    console.log('entryVos', this.entryVos)
  },
  methods: {
    src(icon) {
      if (icon.includes('http') && icon.includes('//')) {
        return icon
      }
      return transferToStaticImageHref(icon, { icon: true })
    }
  }
}
</script>

<style></style>
