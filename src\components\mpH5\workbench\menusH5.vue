<template>
  <Tabbar v-model="active" @change="switchItem">
    <TabbarItem name="todos">
      <span style="margin-bottom: 7px">待办</span>
      <template #icon="props">
        <img :src="props.active ? iconTodoActive : iconTodo" />
      </template>
    </TabbarItem>
    <TabbarItem name="workbench">
      <span style="margin-bottom: 7px">工作台</span>
      <template #icon="props">
        <img :src="props.active ? iconWorkbenchActive : iconWorkbench" />
      </template>
    </TabbarItem>
    <TabbarItem name="mine">
      <span style="margin-bottom: 7px">我的</span>
      <template #icon="props">
        <img :src="props.active ? iconMineActive : iconMine" />
      </template>
    </TabbarItem>
  </Tabbar>
</template>

<script>
import iconMine from '../../../assets/images/icon/icon_mine.png'
import iconMineActive from '../../../assets/images/icon/icon_mine_active.png'
import iconWorkbench from '../../../assets/images/icon/icon_workbench.png'
import iconWorkbenchActive from '../../../assets/images/icon/icon_workbench_active.png'
import iconTodo from '../../../assets/images/icon/icon_todo.png'
import iconTodoActive from '../../../assets/images/icon/icon_todo_active.png'
import { Tabbar, TabbarItem } from 'vant'
export default {
  components: {
    Tabbar,
    TabbarItem
  },
  props: {
    menus: {
      type: Array,
      default: () => []
    },
    defaultAction: {
      type: String,
      default() {
        return 'workbench'
      }
    }
  },
  data() {
    return {
      active: this.defaultAction,
      iconMine,
      iconMineActive,
      iconWorkbench,
      iconWorkbenchActive,
      iconTodo,
      iconTodoActive
    }
  },
  // created() {
  //   if (location.href.includes('/mine')) {
  //     this.active = 1
  //   }

  //   this.active = 0
  // },
  methods: {
    switchItem(active) {
      switch (active) {
        case 'workbench':
          this.$router.push('/workbench')
          break
        case 'mine':
          this.$router.push('/mine')
          break
        case 'todos':
          this.$router.push('/todoType')
          break

        default:
          break
      }
    }
  }
}
</script>

<style scoped>
.selected {
  flex: 0 0 33%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  color: #777c94;
}
.selected i {
  text-align: center;
}
.selected:hover {
  color: #4f71fe;
}
</style>
