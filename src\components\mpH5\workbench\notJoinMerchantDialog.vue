<template>
  <van-dialog
    style="
      width: 325px;
      height: 310px;
      background: #ffffff;
      border-radius: 10px;
      overflow: visible;
    "
    v-model="showNotJoinMerchantDialog"
    :show-confirm-button="false"
    :show-cancel-button="false"
  >
    <div
      style="
        position: relative;
        padding: 0 20px;
        padding-top: 92px;
        display: flex;
        flex-direction: column;
        align-items: center;
      "
    >
      <!-- <img
        style="width: 280px; height: 199px; position: absolute; top: -90px"
        :src="picJoinMerchant"
      /> -->
      <h2>加入或创建企业</h2>
      <p
        style="
          margin: 0;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #46485a;
          letter-spacing: 0;
          text-align: justify;
          line-height: 20px;
        "
      >
        若您所在企业已有平台账号，请联系管理人员加入已有企业；也可以前往电脑端登录管理后台创建全新企业。
      </p>
      <a
        style="
          border-radius: 6px;
          margin-top: 25px;
          width: 100%;
          height: 40px;
          background: #4f71ff;
          color: #fff;
          text-align: center;
          line-height: 40px;
        "
        @click="close"
        >好的，我知道了</a
      >
    </div>
  </van-dialog>
</template>

<script>
import { Dialog, Button } from 'vant'
import picJoinMerchant from 'kit/assets/images/pic_join_merchant.png'
export default {
  components: {
    [Dialog.Component.name]: Dialog.Component,
    Button
  },
  data() {
    return {
      picJoinMerchant,
      showNotJoinMerchantDialog: false
    }
  },
  methods: {
    open() {
      this.showNotJoinMerchantDialog = true
    },
    close() {
      this.showNotJoinMerchantDialog = false
      // 当pc端加入企业 刷新重新获取数据
      window.location.reload()
    }
  }
}
</script>

<style>
</style>