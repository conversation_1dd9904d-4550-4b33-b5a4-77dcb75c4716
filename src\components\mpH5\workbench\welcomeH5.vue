<template>
  <div
    class="welcome"
    style="display: flex; margin: 16px 0; align-items: center"
  >
    <img width="58" height="58" :src="userDefaultImage" />
    <div
      class="content"
      style="
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        text-align: left;
      "
    >
      <h1
        style="
          height: 18px;
          font-weight: 600;
          font-size: 18px;
          color: #24262a;
          text-align: left;
          line-height: 18px;
          margin: 0;
          padding: 0;
        "
      >
        {{ name }}{{ `, ${timeGreet}` }}
      </h1>
      <span
        style="
          margin-top: 10px;
          height: 12px;
          font-weight: 400;
          font-size: 12px;
          color: #777c94;
          text-align: left;
          line-height: 12px;
        "
        >{{ introduction }}</span
      >
    </div>
    <div
      class="notification"
      style="text-align: right; flex: 20px; position: relative"
    >
      <i
        style="font-size: 20px"
        class="iconfont icon-media-notification-on"
        @click="$emit('goNotifications')"
      />
      <Badge
        v-if="unreadCount > 0"
        style="position: absolute; top: -6px; right: -8px"
        :content="unreadCount"
      >
      </Badge>
    </div>
  </div>
</template>

<script>
import { Badge } from 'vant'
import userDefaultImage from 'kit/assets/images/user_default.png'
import { isObject } from 'kit/helpers'
export default {
  components: {
    Badge
  },
  computed: {
    timeGreet() {
      const hour = new Date().getHours()
      if (hour === 12) {
        return '中午好'
      }
      if (hour >= 12) {
        return '下午好'
      }
      if (hour >= 18) {
        return '晚上好'
      }

      return '上午好'
    },
    duration() {
      if (!this.merchant.entryTime) {
        return ''
      }

      const entryTime = new Date(this.merchant.entryTime).getTime()
      const nowTime = new Date().getTime()

      const v = parseInt((nowTime - entryTime) / (86400 * 1000), 10)
      if (!v) {
        return '-天'
      }
      if (v < 90) {
        return `<span style="font-weight: 500;font-size: 24px;color: #24262a;">${v}</span>天`
      }
      if (v < 365) {
        const month = parseInt(v / 30, 10)
        const remain = v - month * 30
        return `<span style="font-weight: 500;font-size: 24px;color: #24262a;">${month}</span>月<span style="font-weight: 500;font-size: 24px;color: #24262a;">${remain}</span>天`
      }
      if (v > 365) {
        const year = parseInt(v / 365, 10)
        const remain = v - year * 365
        return `<span style="font-weight: 500;font-size: 24px;color: #24262a;">${year}</span>年<span style="font-weight: 500;font-size: 24px;color: #24262a;">${remain}</span>天`
      }
      return value
    },
    introduction() {
      if (this.joinedMerchant.length > 1) {
        return this.merchant.name
      }
      if (this.joinedMerchant.length === 1 && this.merchant.entryTime) {
        return `您已加入公司${this.duration}`
      }
      if (this.joinedMerchant.length === 1 && !this.merchant.entryTime) {
        return this.merchant.name
      }

      return '欢迎体验新一代企业数字化平台'
    },
    name() {
      if (this.user.realName) {
        return this.user.realName
      }

      const phone = this.user.cellPhone || this.user.cellphone
      if (phone) {
        return phone.replace(/(\d{3})(\d{4})(\d{4})/, '**$3')
      }

      return ''
    }
  },
  props: {
    joinedMerchant: {
      type: Array
    },
    unreadCount: {
      type: Number,
      default: 0
    },
    user: {
      type: Object,
      default: () => {},
      validator: function (value) {
        if (!value) {
          return false
        }

        if (!isObject(value)) {
          return false
        }

        if (!value.realName && !value.cellPhone) {
          return false
        }

        return true
      }
    },
    merchant: {
      type: Object,
      validator: function (value) {
        if (!isObject(value)) {
          return false
        }

        return true
      }
    }
  },
  data() {
    return {
      userDefaultImage
    }
  }
}
</script>

<style></style>
