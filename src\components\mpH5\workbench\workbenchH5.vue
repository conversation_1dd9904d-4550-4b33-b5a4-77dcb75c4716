<template>
  <div class="workbench">
    <div style="background: #fff; height: 146px; padding: 0 16px">
      <Welcome
        :merchant="profile.merchant"
        :user="profile.user"
        :joinedMerchant="profile.joinedMerchant"
        :unreadCount="unreadCount"
        @goNotifications="$emit('goNotifications')"
      />
    </div>
    <div
      class="content"
      style="
        background: #f5f5f5;
        padding: 0 16px;
        min-height: 415px;
        box-sizing: border-box;
      "
    >
      <div style="position: relative; top: -56px">
        <BannerAdvertise style="margin-bottom: 10px" />
        <div :key="index" v-for="(app, index) in apps">
          <FunctionalModulesH5
            style="margin-bottom: 10px"
            v-if="app.entryVos && app.entryVos.length > 0"
            :name="app.name"
            :entryVos="app.entryVos"
            :joinedMerchant="profile.joinedMerchant"
            @go="entry => $emit('go', app, entry)"
          />
        </div>
      </div>
      <div
        style="
          width: 100vw;
          left: 0;
          position: fixed;
          bottom: 0;
          background: #ffffff;
        "
      >
        <Actions defaultAction="workbench" />
      </div>
    </div>
  </div>
</template>

<script>
import Actions from './menusH5.vue'
import BannerAdvertise from './bannerAdvertiseH5.vue'
import FunctionalModulesH5 from './functionalModulesH5.vue'
import Welcome from './welcomeH5.vue'
export default {
  components: {
    Welcome,
    BannerAdvertise,
    Actions,
    FunctionalModulesH5
  },
  props: {
    profile: {
      type: Object,
      default: () => {},
      validator: function (value) {
        return true
      }
    },
    unreadCount: {
      type: Number,
      default: 0
    },
    apps: {
      type: Array,
      default: () => [],
      validator: function (value) {
        return true
      }
    }
  }
}
</script>

<style></style>
