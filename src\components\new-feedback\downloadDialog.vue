<template>
  <div class="download-dialog">
    <el-dialog
      width="740px"
      :visible.sync="isShowDialog"
      :title="paramsObj.dialogTitle"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div
        v-for="(item, index) in taxSubList"
        :key="index"
        v-show="!isShowReturn"
      >
        <div v-if="item.dealStatus === 'SUCCESS'">
          <div class="tip-title">
            <i class="el-icon-success"></i>下载成功，请保存文件
          </div>
          <div class="download-con">
            <img src="@/assets/images/iconImg.png" />
            <div
              @click="handleDownloadImg(item.fileIds)"
              style="cursor: pointer"
            >
              <i class="iconfont iconxiazai"></i>保存
            </div>
          </div>
        </div>
        <div v-if="item.dealStatus === 'PROCESSING'">
          <div v-loading="taskLoading" style="height: 100px"></div>
          任务处理中，请稍等...
        </div>
        <div v-if="item.dealStatus === 'FAIL'">
          <div class="tip-title"><i class="el-icon-error"></i>下载失败</div>
          反馈信息：{{ item.failReason }}
        </div>
      </div>
      <div
        v-if="dialogLoading"
        v-loading="dialogLoading"
        style="height: 100px"
      ></div>
      <div v-show="isShowReturn">
        <div class="tip-title">
          <i class="el-icon-warning"></i>任务还在处理中
        </div>
        <div>请稍后点击{{ paramsObj.freeBackTip }}获取处理结果</div>
      </div>
      <div slot="footer" v-if="isShowBtn">
        <el-button @click="handleClose" type="primary">关闭</el-button>
      </div>
    </el-dialog>
    <authorizeTip ref="authorizeTip"></authorizeTip>
  </div>
</template>
<script>
import authorizeTip from "@/components/tool/authorizeTip";
import { apiGetFile } from "@/modules/taxPaid/store/api";
export default {
  components: {
    authorizeTip,
  },
  props: {
    sign: String, //页面标识
    timeObj: Object, //时间
  },
  data() {
    return {
      dialogLoading: false,
      taxSubList: [],
      reportReturnList: [],
      isShowDialog: false,
      taskLoading: true,
      isShowReturn: false,
      isShowBtn: false,
      paramsObj: {},
    };
  },
  created() {},
  methods: {
    show(data) {
      if (data) {
        this.paramsObj = data;
        this.taxSubList = [];
        this.isShowBtn = false;
        this.handleDownload();
      }
    },
    handleDownload() {
      this.isShowDialog = true;
      this.dialogLoading = true;
      this.$store
        .dispatch(this.paramsObj.validAction, this.paramsObj.validParameter)
        .then((res) => {
          if (res.success) {
            //验证通过
            if (res.data.status === "SUCCESS") {
              this.taxSubList = res.data.taxSubList;
              //是否进行下步查询
              if (
                res.data.taxSubList
                  .map((item) => item.dealStatus === "PROCESSING")
                  .includes(true)
              ) {
                this.selectShuiyou();
              } else {
                this.isShowBtn = true;
              }
            } else {
              //授权失败
              this.isShowDialog = false;
              this.$refs.authorizeTip.show();
            }
          } else {
            this.isShowDialog = false;
          }
        })
        .finally(() => {
          this.dialogLoading = false;
          this.$parent.freshList(this.sign);
        });
    },
    //查询第一次
    selectShuiyou() {
      setTimeout(() => {
        this.$store
          .dispatch(this.paramsObj.querytAction, this.paramsObj.validParameter)
          .then((res) => {
            if (res.success) {
              if (res.data.status === "SUCCESS") {
                this.taxSubList = res.data.taxSubList;
                if (
                  res.data.taxSubList
                    .map((item) => item.dealStatus === "PROCESSING")
                    .includes(true)
                ) {
                  this.selectSec();
                } else {
                  this.isShowBtn = true;
                }
              }
            }
          });
      }, this.timeObj.first);
    },
    //第二次查询
    selectSec() {
      setTimeout(() => {
        this.$store
          .dispatch(this.paramsObj.querytAction, this.paramsObj.validParameter)
          .then((res) => {
            if (res.data.status === "SUCCESS") {
              this.taxSubList = res.data.taxSubList;
              if (
                res.data.taxSubList
                  .map((item) => item.dealStatus === "PROCESSING")
                  .includes(true)
              ) {
                this.selectThird();
              } else {
                this.isShowBtn = true;
              }
            }
          });
      }, this.timeObj.second);
    },
    //第三次查询
    selectThird() {
      setTimeout(() => {
        this.$store
          .dispatch(this.paramsObj.querytAction, this.paramsObj.validParameter)
          .then((res) => {
            if (res.success) {
              this.taxSubList = res.data.taxSubList;
              //反馈信息
              if (
                res.data.taxSubList
                  .map((item) => item.dealStatus === "PROCESSING")
                  .includes(true)
              ) {
                this.isShowReturn = true;
              }
              this.isShowBtn = true;
            }
          });
      }, this.timeObj.third);
    },
    handleClose() {
      this.isShowDialog = false;
      this.taxSubList = [];
      this.$parent.freshList(this.sign);
    },
    //下载图片
    async handleDownloadImg(fileIds) {
      await apiGetFile(fileIds.join("-"));
    },
  },
};
</script>
<style lang="scss" scoped>
.download-dialog {
  /deep/.el-dialog__body {
    text-align: center;
    color: #6a6f7f;
    min-height: 100px;
  }
  .tip-title {
    color: #070f29;
    font-size: 16px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      font-size: 30px;
    }
  }
  .download-con {
    width: 186px;
    height: 114px;
    border: 1px dashed #cccccc;
    border-radius: 8px;
    margin: 0 auto;
    color: #4f71ff;
    img {
      margin: 25px auto 15px auto;
      display: block;
    }
    i {
      margin-right: 3px;
    }
  }
}
</style>
