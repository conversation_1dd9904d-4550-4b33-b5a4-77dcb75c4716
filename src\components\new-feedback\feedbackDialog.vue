<template>
  <div class="feedback-dialog">
    <el-dialog
      width="550px"
      :visible.sync="isShowDialog"
      title="获取反馈"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div
        v-for="(item, index) in taxSubList"
        :key="index"
        v-show="!isShowReturn"
      >
        <div v-if="item.dealStatus === 'SUCCESS'">
          <div class="tip-title">
            <i class="el-icon-success"></i>下载成功，请保存文件
          </div>
          <div class="download-con">
            <img src="@/assets/images/iconImg.png" />
            <div
              @click="handleDownloadImg(item.fileIds)"
              style="cursor: pointer"
            >
              <i class="iconfont iconxiazai"></i>保存
            </div>
          </div>
        </div>
        <div v-if="item.dealStatus === 'PROCESSING'">
          <div v-loading="taskLoading" style="height: 100px"></div>
          任务处理中，请稍等...
        </div>
        <div v-if="item.dealStatus === 'FAIL'">
          <div class="tip-title"><i class="el-icon-error"></i>下载失败</div>
          反馈信息：{{ item.failReason }}
        </div>
      </div>
      <!-- <div
        v-if="dialogLoading"
        v-loading="dialogLoading"
        style="height: 100px"
      ></div> -->
      <div v-show="isShowReturn">
        <div class="tip-title">
          <i class="el-icon-warning"></i>任务还在处理中
        </div>
        <div>请稍后点击{{ paramsObj.freeBackTip }}获取处理结果</div>
      </div>
      <div slot="footer" v-if="isShowBtn">
        <el-button @click="handleClose" type="primary">关闭</el-button>
      </div>
    </el-dialog>
    <authorizeTip ref="authorizeTip"></authorizeTip>
  </div>
</template>
<script>
import authorizeTip from "@/components/tool/authorizeTip";
import { apiGetFile } from "@/modules/taxPaid/store/api";
export default {
  components: {
    authorizeTip,
  },
  props: {
    sign: String, //页面标识
  },
  data() {
    return {
      paramsObj: {
        stopTip: "", //终止文案
        processingTip: "", //进行中文案
        validParameter: "", //校验参数
        querytAction: "", //查询action
        freeBackTip: "",
      },
      isShowDialog: false, //弹窗
      dialogLoading: false,
      isShowReturn: false,
      taxSubList: [],
      isShowBtn: false,
    };
  },
  methods: {
    show(data) {
      if (data) {
        this.paramsObj = data;
        this.taxSubList = [];
        this.isShowBtn = false;
        this.handleReportInfo();
      }
    },
    //获取反馈
    handleReportInfo() {
      this.$store
        .dispatch(this.paramsObj.querytAction, this.paramsObj.validParameter)
        .then((res) => {
          if (res.success) {
            this.isShowDialog = true;
            // 已授权，有查询结果
            if (res.data.status === "SUCCESS") {
              this.taxSubList = res.data.taxSubList;
              this.isShowBtn = true;
              if (
                res.data.taxSubList
                  .map((item) => item.dealStatus === "PROCESSING")
                  .includes(true)
              ) {
                this.isShowReturn = true;
              }
            } else {
              this.isShowDialog = false;
              this.$refs.authorizeTip.show(); //未授权
            }
          }
        })
        .finally(() => {
          this.$parent.freshList(this.sign);
        });
    },
    handleClose() {
      this.isShowDialog = false;
      this.taxSubList = [];
      this.$parent.freshList(this.sign);
    },
    //下载图片
    async handleDownloadImg(fileIds) {
      await apiGetFile(fileIds.join("-"));
    },
  },
};
</script>
<style lang="scss" scoped>
.feedback-dialog {
  /deep/.el-dialog__body {
    text-align: center;
    color: #6a6f7f;
    min-height: 100px;
  }
  .tip-title {
    color: #070f29;
    font-size: 16px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      font-size: 30px;
    }
  }
  .download-con {
    width: 186px;
    height: 114px;
    border: 1px dashed #cccccc;
    border-radius: 8px;
    margin: 0 auto;
    color: #4f71ff;
    img {
      margin: 25px auto 15px auto;
      display: block;
    }
    i {
      margin-right: 3px;
    }
  }
}
</style>
