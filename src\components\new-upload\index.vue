<template>
  <div
    class="uploadCon"
    :class="
      (fileListLength == limit || !isEdit) && oldUploadBackground == 'defaultBg'
        ? 'upload-disable'
        : ''
    "
  >
    <div class="upload">
      <el-tooltip
        class="item"
        effect="dark"
        content="支持JPG、PNG、JPEG、PDF格式的文件，大小不超过10MB"
        placement="right"
      >
        <el-upload
          :action="imageUrl"
          :headers="heanderToken"
          ref="upload"
          v-model="upload"
          list-type="picture-card"
          accept=".jpg,.jpeg,.png,.pdf"
          :file-list="fileList"
          :before-upload="beforeUpload"
          :on-change="upChange"
          :on-success="handleSuccess"
          :on-error="handleAvatarerror"
          :limit="limit"
          :on-exceed="handleExceed"
          :style="{ width: providewidth, height: provideheight }"
          :class="oldUploadBackground"
        >
          <i slot="default" class="el-icon-plus"></i>
          <div
            slot="file"
            slot-scope="{ file }"
            style="width: 100%; height: 100%"
          >
            <img
              class="el-upload-list__item-thumbnail"
              :src="pdfDefaultImg"
              alt
              v-if="file.url.substring(file.url.length - 3) == 'pdf'"
            />
            <img
              class="el-upload-list__item-thumbnail"
              :src="file.url"
              alt
              v-else
            />
            <p :title="file.name" class="file-name">{{ file.name }}</p>
            <span class="el-upload-list__item-actions">
              <span
                class="el-upload-list__item-preview"
                @click="handlePictureCardPreview(file)"
              >
                <i class="el-icon-zoom-in"></i>
              </span>
              <span
                v-if="!isEdit"
                class="el-upload-list__item-delete"
                @click="handleDownload(file)"
              >
                <i class="el-icon-download"></i>
              </span>
              <span
                v-if="isEdit"
                class="el-upload-list__item-delete"
                @click="handleRemove(file)"
              >
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
        </el-upload>
      </el-tooltip>

      <p class="text" v-if="showText == 'true'">{{ tipText }}</p>
    </div>
    <div
      class="example"
      v-if="showExample == 'true'"
      :style="{ width: providewidth, height: provideheight }"
    >
      <img :src="exampleImg" alt />
      <p class="text" v-if="showText == 'true'">{{ exampleTipText }}</p>
    </div>
  </div>
</template>
<script>
import pdfDefaultImg from "@/assets/images/pdf.png";
import { baseUrl } from "@/request/fetch";
import { getToken } from "@olading/olading-business-ui";
export default {
  name: "newUpload",
  props: {
    exampleImg: {
      type: String,
    },
    uploadSrc: {
      type: String,
    },
    providewidth: {
      type: String,
    },
    provideheight: {
      type: String,
    },
    showExample: {
      type: String,
    },
    showText: {
      type: String,
    },
    tipText: {
      type: String,
    },
    exampleTipText: {
      type: String,
    },
    fileList: {
      type: Array,
      default: () => [],
    },
    oldUploadBackground: {
      type: String,
    },
    isEdit: {
      type: Boolean,
    },
    limit: {
      type: Number,
      default: 3,
    },
    imageUrl: {
      type: String,
      default: baseUrl + "/api/hrsaas-emp/archive/upload",
    },
    downloadUrl: {
      type: String,
      default: "staffManageStore/actionDownloadArchive",
    },
  },
  watch: {
    fileList(val) {
      this.fileListLength = val.length;
    },
  },
  created() {
    this.fileListLength = this.fileList.length;
  },
  data() {
    return {
      isActive: "",
      upload: "",
      fileListLength: 0,
      heanderToken: {
        Authorization: getToken(),
      },
      pdfDefaultImg: pdfDefaultImg,
    };
  },
  methods: {
    handlePictureCardPreview(file) {
      window.open(file.url);
    },
    handleDownload(file) {
      let downloadUrl = this.downloadUrl;
      const { archiveId } = file;
      let param = {
        archiveId: archiveId,
      };
      this.$store.dispatch(downloadUrl, param);
    },
    handleRemove(file) {
      this.$emit("handleRemove", file);
    },
    beforeUpload(file) {
      this.$emit("handleBeforeUpload", false);
      let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isImg =
        file.type === "image/jpeg" ||
        file.type === "image/jpg" ||
        file.type === "image/png" ||
        fileType === "pdf";
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isImg) {
        this.$message({
          message: "上传文件类型只能是JPG/JPEG/PNG/PDF格式!",
          type: "warning",
        });
      }
      if (!isLt10M) {
        this.$message({
          message: "上传文件大小不能超过 10MB!",
          type: "warning",
        });
      }
      this.$emit("handleBeforeUpload", true);
      return isImg && isLt10M;
    },
    upChange(file, fileList) {
      this.fileListLength = fileList.length;
    },
    handleSuccess(res, file, fileList) {
      console.log(">>>", res, file, fileList);
      this.$emit("handleSuccess", res.data);
    },
    handleAvatarerror() {
      this.$message.error("上传有误");
    },
    handleExceed(files, fileList) {
      this.$message.warning(`限制为${this.limit}张图片`);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.uploadCon {
  display: flex;
  .text {
    text-align: center;
    line-height: 40px;
    height: 40px;
    background: #fff;
    display: block;
  }
  .example {
    float: left;
    width: 148px;
    height: 148px;
    margin-left: 64px;
    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
  .file-name {
    width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  /deep/.el-upload-list__item-thumbnail {
    border: 1px solid #c0ccda;
    border-radius: 6px;
    height: 60px;
    width: 80px;
    box-sizing: border-box;
  }
  /deep/.el-upload-list__item {
    height: auto;
    border: none;
    line-height: normal;
  }
  /deep/.el-upload-list__item-actions {
    height: 60px;
  }
}
</style>
