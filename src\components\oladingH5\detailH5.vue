<template>
  <div class="employeePayroll">
    <h3 style="text-align: center">工资条详情</h3>
    <div style="margin: 0 20px 20px; display: flex; align-items: center">
      <div class="avatar">{{ currentItemObj.name.substr(-2) }}</div>
      <div class="info" style="color: #646a73; font-size: 14px">
        <h5>{{ currentItemObj.name }}</h5>
        <span style="color: #646a73">{{ currentItemObj.mobile }}</span>
      </div>
    </div>
    <div v-for="(item, index) in payroll" :key="index">
      <div style="background: #f6f6f6; padding: 15px 5px; color: #787b83">
        <span>{{ item.salaryGroupName }}</span>
      </div>
      <div v-for="(cell, index) in item.salaryStubsItems" :key="index">
        <div
          style="
            display: flex;
            justify-content: space-between;
            padding: 15px 10px;
          "
        >
          <span>{{ cell.salaryItemName }}</span>
          <span style="font-weight: 600">{{ cell.salary }}</span>
        </div>
      </div>
    </div>
    <div class="tip">温馨提示：工资条属于敏感信息，请保密</div>
  </div>
</template>

<script>
export default {
  props: {
    payroll: {
      type: Array,
      default: []
    },
    currentItemObj: {
      type: Object,
      default: {}
    }
  }
}
</script>

<style scoped>
.avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(to right, #659afe, #4180ff);
  margin-right: 16px;
  border-radius: 50%;
  text-align: center;
  line-height: 50px;
  color: #fff;
  font-size: 14px;
}
.info h5 {
  margin: 0 10px;
  font-size: 16px;
  color: #1f2329;
  font-weight: bold;
}
.tip {
  position: fixed;
  bottom: 0;
  background: #dee0e3;
  width: 100%;
  text-align: center;
  line-height: 40px;
}
</style>