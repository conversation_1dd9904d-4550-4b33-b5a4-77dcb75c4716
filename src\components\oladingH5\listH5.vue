<template>
  <div class="employeePayrolls">
    <h3 style="text-align: center">工资条</h3>
    <div
      @click="show"
      :class="{ active: optionsShown }"
      style="margin-left: 10px; display: flex; align-items: center"
    >
      <span>{{ selectedYear }}</span>
      <i class="iconfont icon-direction-arrow-border-down"></i>
    </div>
    <div
      v-show="optionsShown"
      style="
        height: 110px;
        width: 294px;
        overflow-y: auto;
        z-index: 9999;
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
        border: solid 1px #e4e7ed;
        padding: 5px 1px 5px 1px;
        margin-top: 8px;
      "
    >
      <div
        v-for="(option, index) in options"
        :key="index"
        @click="onSelect(option)"
        style="width: 100%"
      >
        <div class="item" style="padding: 0 0 0 20px">
          {{ option.label }}
        </div>
      </div>
    </div>
    <div v-for="(item, index) in employeePayrolls" :key="index">
      <div
        style="
          display: flex;
          justify-content: space-between;
          background-color: #f6f6f6;
          color: #62666e;
          padding: 10px 10px 0 10px;
          font-weight: 600;
        "
      >
        <span>{{ selectedYear }}{{ item.monthName }}月</span>
        <span>{{ item.monthSalaryTotal }}</span>
      </div>
      <div v-for="(cell, index) in item.salaryStubsInfos" :key="index">
        <div
          style="background-color: #fffff; padding: 15px 10px"
          @click="payrollDetail(cell)"
        >
          <div
            style="
              display: flex;
              justify-content: space-between;
              color: #2f3336;
              font-size: 14px;
              font-weight: 600;
            "
          >
            <span>{{ cell.salaryStubsName }}</span>
            <div>
              <span>{{ cell.shouldSalary }}</span>
              <i class="iconfont icon-direction-arrow-border-right"></i>
            </div>
          </div>
          <span style="color: #a4a3a7; font-size: 8px">{{
            cell.createTime
          }}</span>
        </div>
      </div>
    </div>
    <div
      style="
        height: calc(100vh - 90px);
        display: flex;
        align-items: center;
        justify-content: center;
      "
      v-if="!employeePayrolls.length"
    >
      暂无数据
    </div>
  </div>
</template>

<script>
export default {
  props: {
    employeePayrolls: {
      type: Array,
      default: []
    },
    currentYear: String
  },
  data() {
    return {
      selectedYear: this.currentYear,
      options: [
        {
          value: '2023',
          label: '2023年'
        },
        {
          value: '2022',
          label: '2022年'
        },
        {
          value: '2021',
          label: '2021年'
        },
        {
          value: '2020',
          label: '2020年'
        },
        {
          value: '2019',
          label: '2019年'
        },
        {
          value: '2018',
          label: '2018年'
        },
        {
          value: '2017',
          label: '2017年'
        },
        {
          value: '2016',
          label: '2016年'
        },
        {
          value: '2015',
          label: '2015年'
        }
      ],
      optionsShown: false
    }
  },
  methods: {
    show() {
      this.optionsShown = !this.optionsShown
    },
    onSelect(option) {
      this.optionsShown = false
      this.selectedYear = option.value
      this.$emit('refresh', option.value)
    },
    payrollDetail(cell) {
      this.$emit('goDetail', cell)
    }
  }
}
</script>

<style scoped>
.icon-direction-arrow-border-down {
  display: inline-block;
  font-size: 27px;
  transition: all 0.3s;
  color: #c0c4cc;
  transform: rotate(0) scale(0.5);
}
.active .icon-direction-arrow-border-down {
  transform: rotate(180deg) scale(0.5);
}
</style>