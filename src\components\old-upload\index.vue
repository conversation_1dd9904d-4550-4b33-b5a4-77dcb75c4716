<template>
  <div
    class="uploadCon"
    :class="fileListLength == limit || !isEdit ? 'upload-disable' : ''"
  >
    <div class="upload">
      <el-upload
        v-if="checkApiPath()"
        :action="imageUrl"
        :headers="heanderToken"
        ref="upload"
        v-model="upload"
        list-type="picture-card"
        :on-preview="handlePictureCardPreview"
        accept="image/png, image/jpeg"
        :on-remove="handleRemove"
        :file-list="fileList"
        :before-upload="beforeUpload"
        :on-change="upChange"
        :on-success="handleSuccess"
        :on-error="handleAvatarerror"
        :limit="limit"
        :on-exceed="handleExceed"
        :style="{ width: providewidth, height: provideheight }"
        :class="[
          oldUploadBackground,
          { 'hide-delete-icon': !isEdit && oldUploadBackground == 'defaultBg' },
        ]"
      >
        <i v-if="oldUploadBackground == 'defaultBg'" class="el-icon-plus"></i>
      </el-upload>
      <p class="text" v-if="showText == 'true' && isEdit">{{ tipText }}</p>
    </div>
    <div
      class="example"
      v-if="showExample == 'true' && isEdit"
      :style="{ width: providewidth, height: provideheight }"
    >
      <img :src="exampleImg" alt />
      <p class="text" v-if="showText == 'true'">{{ exampleTipText }}</p>
    </div>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>
<script>
import { baseUrl } from "@/request/fetch";
import { getToken } from "@olading/olading-business-ui";
export default {
  name: "oldUpload",
  props: {
    exampleImg: {
      type: String,
    },
    uploadSrc: {
      type: String,
    },
    providewidth: {
      type: String,
    },
    provideheight: {
      type: String,
    },
    showExample: {
      type: String,
    },
    showText: {
      type: String,
    },
    tipText: {
      type: String,
    },
    exampleTipText: {
      type: String,
    },
    fileList: {
      type: Array,
      default: () => [],
    },
    oldUploadBackground: {
      type: String,
    },
    currentData: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
    },
    limit: {
      type: Number,
      default: 1,
    },
    imageUrl: {
      type: String,
      default: window.env.apiPath + "/api/hrsaas-emp/archive/upload",
    },
  },
  watch: {
    fileList(val) {
      this.fileListLength = val.length;
    },
  },
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: "",
      isActive: "",
      upload: "",
      fileListLength: 0,
      heanderToken: {
        Authorization: getToken(),
      },
    };
  },
  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleRemove(file, fileList) {
      this.fileListLength = fileList.length;
      this.$emit("handleRemove", this.currentData, file, fileList);
    },
    beforeUpload(file) {
      let _this = this;
      return new Promise((resolve, reject) => {
        let isLt2M = file.size / 1024 / 1024 < 10; // 判定图片大小是否小于10MB
        if (!isLt2M) {
          reject();
        }
        let image = new Image(),
          resultBlob = "";
        image.src = URL.createObjectURL(file);
        image.onload = () => {
          // 调用方法获取blob格式，方法写在下边
          resultBlob = _this.compressUpload(image);
          resolve(resultBlob);
        };
        image.onerror = () => {
          reject();
        };
      });
    },
    /* 图片压缩方法-canvas压缩 */
    compressUpload(image) {
      let canvas = document.createElement("canvas");
      let ctx = canvas.getContext("2d");
      let initSize = image.src.length;
      let { width } = image,
        { height } = image;
      canvas.width = width;
      canvas.height = height;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(image, 0, 0, width, height);

      // 进行最小压缩0.1
      let compressData = canvas.toDataURL("image/jpeg", 0.1);

      // 压缩后调用方法进行base64转Blob，方法写在下边
      let blobImg = this.dataURItoBlob(compressData);
      return blobImg;
    },
    /* base64转Blob对象 */
    dataURItoBlob(data) {
      let byteString;
      if (data.split(",")[0].indexOf("base64") >= 0) {
        byteString = atob(data.split(",")[1]);
      } else {
        byteString = unescape(data.split(",")[1]);
      }
      let mimeString = data.split(",")[0].split(":")[1].split(";")[0];
      let ia = new Uint8Array(byteString.length);
      for (let i = 0; i < byteString.length; i += 1) {
        ia[i] = byteString.charCodeAt(i);
      }
      return new Blob([ia], { type: mimeString });
    },
    upChange(file, fileList) {
      this.fileListLength = fileList.length;
    },
    handleSuccess(res, file, fileList) {
      this.$emit("handleSuccess", res.data, this.currentData, file, fileList);
    },
    handleAvatarerror() {
      this.$message.error("上传有误");
    },
    handleExceed(files, fileList) {
      this.$message.warning(`限制为${this.limit}张图片`);
    },
    checkApiPath() {
      return typeof window.env.apiPath === "undefined" ? false : true;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.uploadCon {
  display: flex;
  .text {
    text-align: center;
    line-height: 40px;
    height: 40px;
    background: #fff;
    display: block;
  }
  .example {
    float: left;
    width: 148px;
    height: 148px;
    margin-left: 64px;
    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
