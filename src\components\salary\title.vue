<template>
  <header :class="headerClass">
    <el-row type="flex">
      <div class="back-style" @click="handleBackClick" v-if="back">
        <!-- <span class="el-icon-arrow-left" style="font-size:16px;transform:scale(1.1);color:var(--o-primary-color)"></span> -->
        <i class="el-icon-arrow-left" style="color:#999999"/>
        <span class="titleText">返回</span>
      </div>
      <div><span class="header-line" v-if="back">|</span></div>
      <div class="title">
        <slot />
      </div>
    </el-row>
  </header>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: '标题'
    },
    back: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    headerClass() {
      return this.back ? 'main-title-back' : 'main-title'
    }
  },
  methods: {
    handleBackClick() {
      if(this.back &&this.$listeners['on-back']){
        this.$emit('on-back')
        return 
      }
        
      this.$router.back()
    }
  }
}
</script>

<style scoped scoped>
.main-title,
.main-title-back {
  background-color: #fff;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  height: 50px;
  line-height: 50px;
  padding-left: 10px;
  border-bottom: 1px solid #ededed;
  padding-right: 10px;
}
.main-title {
  margin: 0 24px;
  padding-left: 0;
}
.main-title .el-icon-close {
  margin-right: 20px;
}
.title {
  display: flex;
  flex: 1;
  align-items: center;
  font-family: MicrosoftYaHei;
  font-weight: 700;
  font-size: 18px;
  color: #24262a;
  letter-spacing: 0;
  margin: 0;
}

.titleText {
  font-family: MicrosoftYaHei;
  font-weight: 400;
  font-size: 16px;
  color: #999999;
  letter-spacing: 0;
  text-align: right;
  margin: 0;
  /* color: var(--o-primary-color); */
}
.back-style {
  font-size: 14px;
  font-weight: normal;
  color: #333;
  cursor: pointer;
}
.back-style i {
  margin-right: 6px;
}
</style>
