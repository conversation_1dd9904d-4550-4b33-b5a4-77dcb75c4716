<template>
  <div>
    <TableDataSelectAllCheckbox
      v-model="isAllTableDataSelected"
      :selectedTotalCount="selectedTotalCount"
      style="margin-bottom: 10px"
      :disabled="disabled"
      @input="checkboxOnInput"
      v-if="privilegeVoList.includes('salary.report.taxReport.allPage')"
    />
    <!-- <div>排除的个数：{{ excludedSelectedItems.length }}</div> -->
    <el-table
      v-bind="$attrs"
      v-on="$listeners"
      :data="data"
      ref="table"
      @select="handleSelect"
      @select-all="handleSelectAll"
      @selection-change="handleSelectItem"
    >
      <slot />
    </el-table>
  </div>
</template>
<script>
import TableDataSelectAllCheckbox from "./tableDataSelectAllCheckbox.vue";
import { deepClone } from "@/utils/utils";
import { mapState } from "vuex";

export default {
  components: {
    TableDataSelectAllCheckbox,
  },
  computed: {
    disabled(){
      return !this.total
    },
    currentPageCount() {
      return this.selectItems.length;
    },
    excludedCount() {
      return this.excludedSelectedItems.length;
    },
    selectedTotalCount() {
      if (this.isAllTableDataSelected) return this.total - this.excludedCount;
      return this.currentPageCount;
    },
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  data() {
    return {
      isAllTableDataSelected: false,
      excludedSelectedItems: [],
      selectItems: [],
      oldCurrPage: this.currPage,
    };
  },
  props: {
    currentPage: {
      type: Number,
      default: 0,
    },
    diffKey: {
      default: "id",
      type: String,
    },
    total: {
      type: Number,
      default: 0,
    },
    data: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    // 监听列表数据变化
    data: {
      handler() {
        const { oldCurrPage, currentPage } = this;
        // 是否用户选择全部数据
        if (!this.isAllTableDataSelected) return;
        // 判断用户是否切换分页导致的
        if (oldCurrPage !== currentPage) {
          this.oldCurrPage = currentPage;
          this.triggerUpdateTableRowSelect();
        }
      },
      deep: true,
    },
    excludedSelectedItems() {
      if (this.excludedSelectedItems.length === this.total) {
        this.resetSelectAllButtonStatus();
      }
    },
  },
  methods: {
    async triggerUpdateTableRowSelect() {
      const { diffKey, data, excludedSelectedItems, tableRowSelection } = this;

      const selectRows = data.filter((item) => {
        const excludesItem = excludedSelectedItems.some(
          (eItem) => eItem[diffKey] === item[diffKey]
        );
        return !excludesItem;
      });

      await this.$nextTick();

      selectRows.forEach((item) => tableRowSelection(item));
    },

    resetSelectAllButtonStatus() {
      this.isAllTableDataSelected = false;
    },

    handleSelect(selection, row) {
      const { diffKey, excludedSelectedItems } = this;

      this.$emit("select");

      // 如果不是选择全部 不处理
      if (!this.isAllTableDataSelected) {
        this.excludedSelectedItems = [];
        return;
      }

      const isDelete = !selection.some((item) => item === row);
      const hasDeleteItem = excludedSelectedItems.some(
        (item) => item[diffKey] === row[diffKey]
      );

      // 删除了 ， 并且删除集合里不包含 已经删除的，避免重复添加
      if (isDelete && !hasDeleteItem) {
        this.excludedSelectedItems.push(row);
      }

      console.log({
        isDelete,
        hasDeleteItem,
        excludedSelectedItems: this.excludedSelectedItems,
        row,
      });

      // 添加回来了
      if (!isDelete) {
        this.excludedSelectedItems = this.excludedSelectedItems.filter(
          (item) => item[diffKey] !== row[diffKey]
        );
      }
    },
    handleSelectItem(row) {
      this.selectItems = deepClone(row);
      this.$emit("selection-change", row);
    },

    tableRowSelection(row) {
      const elTable = this.$refs.table;
      elTable.toggleRowSelection(row, true);
    },
    tableClearSelection() {
      const elTable = this.$refs.table;
      elTable.clearSelection();
    },
    checkboxOnInput(check) {
      this.excludedSelectedItems = [];
      if (!this.total) {
        return this.$showMessage("暂无可勾选的数据", "error");
      }
      if (check) {
        this.data.forEach((row) => {
          this.tableRowSelection(row);
        });
        return;
      }
      this.tableClearSelection();
    },
    handleSelectAll(selection) {
      const { diffKey, excludedSelectedItems } = this;
      this.$emit("select-all", selection);
      // 点击全选按钮 -> 取消当前页全部选择
      if (!selection.length) {
        this.data.forEach((row) => this.handleSelect(selection, row));
        return;
      }
      // 点击全选按钮 -> 全选当前页全部选择
      this.excludedSelectedItems = excludedSelectedItems.filter((item) => {
        if (selection.some((it) => item[diffKey] === it[diffKey])) {
          return false;
        }
        return true;
      });
    },
  },
};
</script>