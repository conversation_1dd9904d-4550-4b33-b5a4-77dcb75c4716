<template>
  <div style="display: flex">
    <span style="margin-right: 20px"
      >已选中 <span style="color: red">{{ selectedTotalCount }}</span> 条</span
    >
    <el-checkbox :disabled="disabled" :value="value" @input="onInput">选择全部页面数据</el-checkbox>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    disabled:{
      type: Boolean,
      default: false,
    },
    selectedTotalCount: {
      type: Number,
      default: 0,
    },
  },
  methods: {
    onInput(check) {
      this.$emit("input", check);
    },
  },
};
</script>