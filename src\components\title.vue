<template>
  <div
    style="
      padding: 0 20px;
      font-size: 17px;
      height: 60px;
      border-bottom: 1px solid #ededed;
      line-height: 61px;
      display: flex;
    "
  >
    <div v-if="withBackButton">
      <a style="cursor: pointer" @click="$router.back()">返回</a
      ><span style="margin: 0 10px">|</span>
    </div>
    <slot>
      {{ title }}
    </slot>
  </div>
</template>

<script>
export default {
  props: {
    withBackButton: Boolean,
    title: String,
  },
};
</script>

<style>
</style>