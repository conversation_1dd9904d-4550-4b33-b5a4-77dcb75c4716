<template>
  <el-dialog title="添加部门" :visible.sync="addDialog" width="500px">
    <el-form :model="addForm" ref="addForm" label-width="100px">
      <el-form-item prop="parentId" label="上级部门">
        <select-tree
          ref="selectTree"
          :options="departmentList"
          :value="addForm.parentId"
          :clearable="false"
          @getValue="getValue"
        />
      </el-form-item>
      <el-form-item
        prop="departmentName"
        label="部门名称"
        :rules="{
          required: true,
          message: '请输入部门名称',
          trigger: 'blur',
        }"
      >
        <el-input
          v-model.trim="addForm.departmentName"
          placeholder="请输入部门名称"
          maxlength="64"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button @click="addDialog = false">取消</el-button>
      <el-button type="primary" @click="handleSave">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { mapState } from "vuex";
import selectTree from "@/components/tool/selectTree";
import { apiAddDepartment } from "../../modules/homePage/store/api";
export default {
  components: { selectTree },
  data() {
    return {
      addDialog: false, //添加部门弹窗
      addForm: {
        parentId: "",
        departmentName: "",
      },
      selectTreeName: "",
    };
  },
  computed: {
    ...mapState("homePageStore", {
      departmentList: "departmentList",
    }),
  },
  methods: {
    show() {
      this.addDialog = true;
      this.addForm.parentId = this.departmentList[0].id;
      this.$nextTick(() => {
        this.$refs.addForm.resetFields();
      });
    },
    //添加部门取值
    getValue(value, name) {
      this.selectTreeName = name;
      this.addForm.parentId = value;
    },
    //保存
    handleSave() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          apiAddDepartment({ ...this.addForm }).then((res) => {
            if (res.success) {
              let addFormDt = {
                selectTreeName: this.selectTreeName
                  ? this.selectTreeName
                  : this.departmentList[0].label,
                inputName: this.addForm.departmentName,
                id: res.data,
              };
              this.$emit("addFormDt", addFormDt);
              setTimeout(() => {
                this.$store.dispatch("homePageStore/actioncGetDepartmentList");
                this.$message.success("保存成功");
                this.addDialog = false;
              }, 1000);
            }
          });
        } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-select,
.el-input {
  width: 300px;
}
</style>
