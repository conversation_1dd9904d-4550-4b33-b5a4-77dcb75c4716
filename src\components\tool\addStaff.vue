<template>
  <div class="add-staff">
    <el-dialog
      title="增加人员"
      :visible.sync="isShowAddStaff"
      width="860px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="add-staff-con">
        <div class="table-con">
          <div class="select-con">
            <el-select
              v-model="addForm.taxSubId"
              placeholder="请选择公司"
              @change="changeCompany"
              clearable
            >
              <el-option
                v-for="item in taxSubjectInfoList"
                :key="item.taxSubId"
                :label="item.taxSubName"
                :value="item.taxSubId"
              ></el-option>
            </el-select>
            <el-select
              v-model="addForm.empType"
              placeholder="用工性质"
              @change="getList"
              clearable
            >
              <el-option
                v-for="item in enumEmpTypeOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-select
              v-model="addForm.enumEmpStatus"
              placeholder="员工状态"
              @change="getList"
              clearable
            >
              <el-option
                v-for="item in employStatusOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-input
              v-model="addForm.key"
              placeholder="姓名/工号"
              @keyup.enter.native="getList"
            ></el-input>
          </div>
          <el-table
            :data="staffTable"
            ref="staffTable"
            tooltip-effect="dark"
            height="300px"
            style="width: 100%; margin-top: 10px"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
              fixed="left"
            ></el-table-column>
            <el-table-column label="姓名" width="120">
              <template slot-scope="scope">{{ scope.row.empName }}</template>
            </el-table-column>
            <el-table-column label="工号" width="80">
              <template slot-scope="scope">{{ scope.row.empNo }}</template>
            </el-table-column>
            <el-table-column
              prop="taxSubName"
              label="公司名称"
              width="180"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column label="用工性质" width="80">
              <template slot-scope="scope">{{
                scope.row.empType | filterEmpType
              }}</template>
            </el-table-column>
            <el-table-column label="入职日期" width="120">
              <template slot-scope="scope">{{ scope.row.empDay }}</template>
            </el-table-column>
            <el-table-column label="最后工作日" width="120">
              <template slot-scope="scope">{{ scope.row.leaveDay }}</template>
            </el-table-column>
          </el-table>
        </div>
        <div class="choose-con">
          <div class="choose-title">
            已选 <span class="staff-style">{{ checkedPerson.length }}人</span>
          </div>
          <div class="choose-item-con">
            <div
              v-for="(item, index) in checkedPerson"
              :key="index"
              class="choose-item"
            >
              <p class="choose-name">
                <span>{{ item.empName }}</span>
                <span class="emNo">{{ item.empNo }}</span>
              </p>
              <i class="el-icon-circle-close" @click="removeStaff(item)"></i>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="handleSubmit" :loading="btnLoading">
          确定
        </el-button>
        <el-button @click="isShowAddStaff = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import * as constData from "../../modules/paymaster/util/constData";
import { mapState } from "vuex";
export default {
  props: {},
  data() {
    return {
      addForm: {
        checkId: "",
        taxSubId: "", //公司
        empType: "", //用工性质
        enumEmpStatus: "", //人员状态
        key: "",
      },
      companyOptions: [],
      enumEmpTypeOption: [], //用工性质
      employStatusOption: constData.employStatusOption, //员工状态
      isShowAddStaff: false,
      staffTable: [],
      checkedPerson: [],
      idsDtoList: [],
      listAction: "",
      saveAction: "",
      btnLoading: false,
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
      taxSubjectInfoList: (state) => state.taxSubjectInfoList,
    }),
  },
  created() {},
  methods: {
    show(form) {
      this.enumEmpTypeOption = [{ label: "全部", value: "" }].concat(
        constData.enumEmpTypeOption
      );
      form.checkId ? (this.addForm.checkId = form.checkId) : "";
      //筛选条件置空
      this.addForm.taxSubId = "";
      this.addForm.empType = "";
      this.addForm.enumEmpStatus = "";
      this.addForm.key = "";
      this.listAction = form.listAction;
      this.saveAction = form.saveAction;
      this.isShowAddStaff = true;
      this.getList();
    },
    changeCompany() {
      this.getList();
    },
    getList() {
      this.$store
        .dispatch(this.listAction, {
          checkId: this.addForm.checkId,
          key: this.addForm.key,
          queryFilterParam: {
            empType: this.addForm.empType,
            enumEmpStatus: this.addForm.enumEmpStatus,
            taxSubId: this.addForm.taxSubId,
          },
        })
        .then((res) => {
          if (res.success) {
            this.staffTable = res.data.data;
          }
        });
    },
    removeStaff(item) {
      this.checkedPerson = this.checkedPerson.filter(
        (ite) => JSON.stringify(ite) != JSON.stringify(item)
      );
      this.$refs.staffTable.toggleRowSelection(item, false);
    },
    handleSubmit() {
      this.btnLoading = true;
      if (this.idsDtoList.length === 0) {
        this.$message.warning("请选择数据");
        this.btnLoading = false;
        return;
      }
      this.$store
        .dispatch(this.saveAction, {
          checkId: this.addForm.checkId,
          idsDtoList: this.idsDtoList,
          key: this.addForm.key,
          queryFilterParam: {
            empType: this.addForm.empType,
            enumEmpStatus: this.addForm.enumEmpStatus,
            taxSubId: this.addForm.taxSubId,
          },
        })
        .then((res) => {
          if (res.success) {
            this.isShowAddStaff = false;
            this.$message.success("添加成功");
            this.$emit("freshList");
          }
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    handleSelectionChange(data) {
      console.log("data", data);
      let idsDtoList = [];
      if (data.length > 0) {
        data.forEach((item) => {
          if (
            !JSON.stringify(this.checkedPerson).includes(JSON.stringify(item))
          ) {
            this.checkedPerson.push(item);
          }
        });
        // this.checkedPerson = data;

        this.checkedPerson.forEach((item) => {
          idsDtoList.push({
            compEmpId: item.compEmpId,
            empId: item.empId,
            bankId: item.bankId,
          });
        });
        this.idsDtoList = idsDtoList;
      } else {
        this.checkedPerson = [];
        this.idsDtoList = [];
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.add-staff {
  .add-staff-con {
    width: 820px;
    display: flex;
    border-top: 1.2px solid #f7f7f7;
    .table-con {
      width: 78%;
      padding-top: 10px;
      border-right: 1.2px solid #f7f7f7;
    }
    .choose-con {
      padding: 0px 10px;
      width: 24%;
      .choose-title {
        height: 34px;
        line-height: 34px;
        border-bottom: 1.2px solid #f7f7f7;
        padding-top: 10px;
        margin-bottom: 10px;
        .staff-style {
          float: right;
        }
      }
      .choose-item-con {
        height: 300px;
        overflow-y: auto;
        .choose-item {
          line-height: 24px;
          height: 24px;
          display: flex;
          .choose-name {
            // display: inline-block;
            width: 135px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .emNo {
            // display: inline-block;
            margin: 0px 26px 0px 6px;
          }
          .el-icon-circle-close {
            // float: right;
            margin: 6px 2px 0px 0px;
            font-size: 16px;
            font-weight: bold;
            display: none;
            cursor: pointer;
          }
        }
        .choose-item:hover .el-icon-circle-close {
          display: inline-block;
        }
      }
    }
  }
  /deep/.el-input {
    width: 140px;
  }
  /deep/.el-input__icon {
    line-height: 30px;
  }
}
</style>
