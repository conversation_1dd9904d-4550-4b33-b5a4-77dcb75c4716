<template>
  <div class="authorizeTip">
    <el-dialog
      title="提示"
      :visible.sync="isShowAuthorize"
      width="400px"
      class="diy-el_dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div style="display: flex; justify-content: flex-start">
        <i class="el-icon-warning" style="color: #ff9500; font-size: 26px"></i>
        <div style="width: 280px">
          公司纳税主体认证未验证通过，请前往法人实体管理中验证！
        </div>
      </div>
      <router-link to="/initialize/paid" style="line-height: 70px">
        前往验证
      </router-link>
      <div slot="footer">
        <el-button type="primary" plain size="mini" @click="handleCloseModel">
          我知道了
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      isShowAuthorize: false,
    };
  },
  methods: {
    //改变radio
    show() {
      this.isShowAuthorize = true;
    },
    handleCloseModel() {
      this.isShowAuthorize = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.import-data {
  .download {
    cursor: pointer;
  }
  .importCount {
    margin: 10px auto;
  }
}
</style>
