<template>
  <div>
    <el-dialog
      width="800px"
      :title="title"
      :visible.sync="visible"
      @close="close"
    >
      <div
        style="
          display: flex;
          justify-content: space-between;
          margin: 0 196px;
          align-items: center;
        "
      >
        <div
          class="step"
          :class="[active === 1 ? 'active-step' : 'default-step']"
        >
          <div class="circle">
            <span v-if="active === 1">1</span>
            <i v-else style="color: #4f71ff" class="el-icon-check"></i>
          </div>
          <span>上传文件</span>
        </div>
        <div
          style="width: 136px; height: 0; opacity: 1"
          :class="[active === 1 ? 'default-line' : 'active-line']"
        ></div>
        <div
          class="step"
          :class="[active === 2 ? 'active-step' : 'default-step']"
        >
          <div class="circle">2</div>
          <span>导入结果</span>
        </div>
      </div>
      <FileImportPrompt
        v-if="active === 2"
        :successCount="exportResult.successCount"
        :failCount="exportResult.failCount"
        :apiImportVerifyErrorLog="apiImportVerifyErrorLog"
        :uuid="uuid"
        style="width: 100%; margin-top: 25px"
      />
      <div v-if="active === 1">
        <div
          style="border-radius: 6px; margin: 25px 0 16px; text-align: center"
        >
          <el-upload
            width="100%"
            :headers="headerToken"
            ref="upload"
            class="upload"
            drag
            :data="uploadData"
            :file-list="fileList"
            :before-upload="beforeUpload"
            :on-success="onSuccess"
            :on-change="onChange"
            :on-remove="onRemove"
            :on-error="onError"
            accept=".xls, .xlsx"
            :action="apiCheck"
          >
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 30px 0 24px;
              "
            >
              <img src="@/assets/images/upload-excel.svg" alt="" />
              <el-button
                style="
                  width: 88px;
                  margin: 10px 0 8px;
                  padding: 5px 16px;
                  line-height: 20px;
                  border: 1px solid #cad0dbff;
                  color: #1e2228;
                  display: flex;
                  justify-content: center;
                "
                plain
                >{{ fileList.length ? "重新选择" : "选择文件" }}</el-button
              >
              <div style="color: #828b9b; font-size: 14px">
                支持xlsx和xls文件，文件大小不超过5M
              </div>
            </div>
          </el-upload>
        </div>
        <div class="tips">
          <div style="line-height: 22px; font-weight: 400; margin-bottom: 8px">
            温馨提示:
          </div>
          <div class="tips-content">
            <div>
              1.下载<a
                style="
                  color: #4f71ff;
                  font-weight: 600;
                  cursor: pointer;
                  margin: 0 5px;
                "
                @click="downloadTemplate"
              >
                导入模版 </a
              >，编辑后导入
            </div>
            <div>2.请将文件置于第一个sheet，文件不支持多行表头</div>
            <div>3.请不要上传锁定的文件，将导致错误</div>
            <div>4.请阅读说明后进行导入操作，标红字段为必填项目。</div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <div v-if="active === 1">
          <el-button type="plain" @click="visible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleImport"
            :disabled="!fileList.length || !uuid"
            :loading="loading"
            >导入</el-button
          >
        </div>
        <div v-if="active === 2 && exportResult.failCount === 0">
          <el-button type="plain" @click="visible = false">返回列表</el-button>
          <el-button type="primary" @click="continueUpload">继续上传</el-button>
        </div>
        <div v-if="active === 2 && exportResult.failCount !== 0">
          <el-button @click="continueUpload">继续上传</el-button>
          <el-button type="primary" @click="downloadFailFile"
            >下载失败文件</el-button
          >
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import FileImportPrompt from "./fileImportPrompt.vue";
import { getToken } from "@olading/olading-business-ui";

export default {
  components: {
    FileImportPrompt,
  },
  props: {
    title: {
      type: String,
      default: "",
    },
    apiCheck: String, //校验接口
    apiDownloadTemplate: Function, //下载模板
    apiConfirmImport: Function, //确认导入
    apiImportVerifyErrorLog: Function, //下载错误文件
  },
  data() {
    return {
      visible: false,
      loading: false,
      headerToken: {
        Authorization: `${getToken()}`,
      },
      uploadData: {},
      fileList: [],
      uuid: "",
      exportResult: {
        successCount: 0,
        failCount: 0,
        failUrl: "",
      },
      active: 1,
    };
  },
  created() {
    window.that = this;
  },
  methods: {
    resetExportResult() {
      this.exportResult.successCount = 0;
      this.exportResult.failCount = 0;
    },
    open() {
      this.visible = true;
      this.resetExportResult();
      this.active = 1;
    },
    close() {
      this.fileList = [];
      this.resetExportResult();
      this.$emit("refresh");
    },
    async downloadTemplate() {
      await this.apiDownloadTemplate();
    },
    beforeUpload(file) {
      var testMsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isExcel = testMsg === "xls" || testMsg === "xlsx";
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isExcel) {
        this.$message({
          message: "文件格式有误，请选择xlsx或xls文件",
          type: "warning",
        });
        return false;
      }
      if (!isLt5M) {
        this.$message({
          message: "文件大小超过5M，请重选文件",
          type: "warning",
        });
        return false;
      }
    },
    async handleImport() {
      this.loading = true;
      this.fileList[0].status = "ready";
      const res = await this.apiConfirmImport(this.uuid);
      this.loading = false;
      if (res.success) {
        this.active++;
      }
    },
    continueUpload() {
      this.fileList = []
      this.active = 1;
    },
    async downloadFailFile() {
      await this.apiImportVerifyErrorLog(this.uuid);
    },
    async onSuccess(response, file, fileList) {
      this.uuid = "";
      if (!response.success) {
        this.$message.error(response.message);
        this.$refs.upload.clearFiles();
        return;
      }
      this.uuid = response.data.uuid;
      this.$nextTick(() => {
        this.exportResult = response.data;
      });
    },
    onChange(file, fileList) {
      this.resetExportResult();
      if (fileList.length) {
        this.fileList = [fileList[fileList.length - 1]];
      }
    },
    onRemove() {
      this.fileList = [];
    },
    onError(error, file) {
      this.loading = false;
      this.$message.error("上传失败，请检查网络");
    },
  },
};
</script>
<style scoped>
.button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 168px;
  height: 32px;
  border-radius: 6px;
  margin: 8px 0 4px;
  border: 1px solid #cad0dbff;
  background: #ffffffff;
  box-shadow: 0 2px 0 0 #00000005;
  cursor: pointer;
}
.circle {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  margin-right: 8px;
  color: #fff;
  text-align: center;
  line-height: 22px;
}
.step {
  display: flex;
  align-items: center;
}
.active-step .circle {
  background: #4f71ff;
  color: #ffffff;
}
.active-step > span {
  font-weight: 600;
}
.default-step .circle {
  background: #e4e7ed;
  color: #828b9b;
}
.default-line {
  border: 1px solid #e4e7ed;
}
.active-line {
  border: 1px solid #4f71ff;
}
.tips {
  color: #858e9d;
  font-weight: 500;
  margin-bottom: 10px;
}
.tips-content div {
  line-height: 22px;
  font-weight: 400;
}
::v-deep .el-upload {
  width: 100%;
}
::v-deep .el-upload-dragger {
  width: 100%;
  border: 1px dashed #d9d9d9;
  background-color: #f7f9fcff;
}
::v-deep .el-dialog__header {
  padding: 16px 24px 16px;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
  margin: 0;
}
::v-deep .el-dialog__body {
  min-height: 260px;
  padding-bottom: 0;
}
::v-deep .el-upload-list__item {
  transition: none !important;
}
::v-deep .el-upload-list__item-name {
  text-align: left;
}
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: none;
}
::v-deep .el-upload-list__item.is-ready,
::v-deep .el-upload-list__item.is-uploading {
  display: block !important;
}
</style>
