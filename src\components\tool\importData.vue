<template>
  <div class="import-data">
    <el-dialog
      :title="title"
      :visible.sync="isShowIncrease"
      width="600px"
      class="diy-el_dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="closeModel"
      @close="dialogClose"
    >
      <div class="diy-steps">
        <el-steps direction="vertical" align-center>
          <el-step title="选择导入匹配方式"> </el-step>
          <div class="diy-el_radio">
            <el-radio-group v-model="radio">
              <div v-for="(item, index) in radioList" :key="index">
                <el-radio :label="item.lable" @change="handleRadioValue">
                  {{ item.title }}
                </el-radio>
              </div>
            </el-radio-group>
          </div>
          <el-step title="选择文件" class="second-step" style="height: 0px">
          </el-step>
        </el-steps>
        <div class="select-file">
          <p>
            支持xlsx和xls文件，文件不超过5M，建议使用标准模板格式
            <span>
              <a style="color: #4f71ff" @click="handleTemplate">
                <span class="olading-iconfont oi-download"></span>下载模板
              </a>
            </span>
          </p>
          <p class="instructions">{{ tips }}</p>
          <p v-for="(item, index) in tipList" :key="index">{{ item }}</p>
          <el-upload
            class="avatar-uploader"
            :action="apiCheck"
            :headers="myHeaders"
            :file-list="fileList"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            :disabled="isUploadLoading"
            :on-success="handleSuccess"
            :data="parameterData"
            accept=".xls, .xlsx"
          >
            <el-button size="small" type="primary" :loading="isUploadLoading">{{
              isUploadFile ? "重新上传" : "选择文件"
            }}</el-button>
          </el-upload>
          <div v-show="uuid" style="margin: 15px 0 0 28px">
            <span v-if="failCount === 0"
              ><i class="el-icon-success"></i
              ><strong style="color: #41bd5a">数据全部校验通过</strong></span
            >
            <span v-else-if="failCount !== 0 && successCount !== 0">
              <i class="el-icon-warning"></i>数据部分校验通过，有<strong
                style="color: red"
                >{{ failCount }}</strong
              >条数据错误
            </span>
            <span v-else-if="successCount === 0"
              ><i class="el-icon-error">数据全部未通过校验</i></span
            >
            <span class="diy-export">
              <a
                @click="handleDownload"
                v-if="failCount !== 0"
                class="download"
              >
                <img src="../../assets/images/export-error.png" alt="" />
                <span>导出错误数据</span>
              </a>
            </span>
          </div>
        </div>
      </div>
      <span slot="footer">
        <el-button @click="isShowIncrease = false">取消</el-button>
        <el-button
          type="primary"
          :loading="isLoading"
          :disabled="successCount === 0"
          @click="uploadFile"
        >
          导入通过数据
        </el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="导入结果"
      :visible.sync="isShowIncreaseFinish"
      width="500px"
      class="importFinishDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="title"><i class="el-icon-success"></i>导入完成</div>
      <div class="importCount">
        导入成功
        <span style="color: #06b806">{{ importFinishForm.successCount }}</span>
        条数据, 导入失败
        <span style="color: red">{{ importFinishForm.failCount }}</span>
        条数据。
      </div>
      <div>
        <a
          @click="handleDownload"
          v-if="importFinishForm.failCount !== 0"
          class="download"
        >
          下载日志
        </a>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="importMemberFinish">确定</el-button>
        <el-button @click="isShowIncreaseFinish = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getToken } from "@olading/olading-business-ui";
export default {
  props: {
    radioList: Array,
    apiCheck: String, //校验接口
    apiDownloadLog: String, //下载日志接口
    apiDownloadTemplate: String, //下载模板
    parameterData: Object, //校验参数
    downloadQueryData: Object, //下载参数
    impoartAction: String, //导入通过数据接口  需为action
    title: String, //标题,
    sendRadio: String,
    uploadFileData: Object, //导入通过数据参数
    tips: String,
    tipList: Array,
  },
  data() {
    return {
      myHeaders: { Authorization: getToken() },
      isShowIncrease: false,
      isUploadLoading: false,
      importFinishForm: {
        failCount: "",
        successCount: 0,
      },
      radio: "",
      isShowIncreaseFinish: false,
      failCount: 0,
      fileList: [],
      successCount: 0,
      uuid: "",
      closeModel: true,
      isUploadFile: false,
      isLoading: false,
    };
  },
  created() {
    this.radio = this.sendRadio;
  },
  methods: {
    handleTemplate() {
      if (this.downloadQueryData) {
        this.$store.dispatch(this.apiDownloadTemplate, this.downloadQueryData);
      } else {
        this.$store.dispatch(this.apiDownloadTemplate);
      }
    },
    handleDownload() {
      this.$store.dispatch(this.apiDownloadLog, { uuid: this.uuid });
    },
    show() {
      this.radio = this.sendRadio;
      this.isShowIncrease = true;
      this.fileList = [];
      this.uuid = "";
      this.successCount = 0;
    },
    //改变radio
    handleRadioValue(value) {
      this.$emit("changeRadioValue", value);
    },
    apiUploadFile() {
      return new Promise((resolve) => {
        this.$store
          .dispatch(this.impoartAction, this.uploadFileData)
          .then((res) => {
            if (res.success) {
              resolve(res);
            }
          })
          .catch((err) => {
            resolve({ success: false });
            if (err.toString().includes("timeout")) {
              return this.$message.error("导入中，请稍后查看导入结果!");
            }
          })
          .finally(() => {});
      });
    },
    async uploadFile() {
      this.isLoading = true;
      this.uploadFileData.uuid = this.uuid;
      const res = await this.apiUploadFile();
      this.isLoading = false;
      if (res.success) {
        let importData = res.data;
        this.importFinishForm.failCount = importData.failCount;
        this.importFinishForm.successCount = importData.successCount;
        this.isShowIncrease = false;
        this.isShowIncreaseFinish = true;
      }
    },
    beforeUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isxls = testmsg === "xls" || testmsg === "xlsx";
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isxls) {
        this.$message({
          message: "文件格式有误，请选择xlsx或xls文件",
          type: "warning",
        });
        return false;
      }
      if (!isLt5M) {
        this.$message({
          message: "文件大小超过5M，请重选文件",
          type: "warning",
        });
        return false;
      }
      this.isUploadLoading = true;
      // return isxls && isLt5M;
    },
    handleSuccess(res, file, fileList) {
      if (res.success) {
        let data = res.data;
        if (data.totalCount && data.totalCount == 0) {
          this.$message.warning("模板中无数据,请导入数据！");
        }
        this.successCount = data.successCount;
        this.failCount = data.failCount;
        this.uuid = data.uuid;
        this.isUploadFile = true;
        this.fileList = [fileList[fileList.length - 1]];
      } else {
        this.uuid = "";
        this.successCount = 0;
        this.$message.error(res.message);
        this.isUploadFile = true;
        this.fileList = [];
      }
      this.isUploadLoading = false;
    },
    //删除文件
    handleRemove(file, fileList) {
      this.successCount = 0;
      if (file && file.status === "success") {
        this.uuid = "";
        this.isUploadFile = false;
      }
    },
    importMemberFinish() {
      this.isShowIncrease = false;
      this.isShowIncreaseFinish = false;
      this.$emit("getLoading");
    },
    dialogClose() {
      this.isUploadFile = false;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/el-steps.scss";
.import-data {
  .download {
    cursor: pointer;
  }
  .importCount {
    margin: 10px auto;
  }
  /deep/ .el-list-enter-active,
  /deep/ .el-list-leave-active {
    transition: none;
  }

  /deep/ .el-list-enter,
  /deep/ .el-list-leave-active {
    opacity: 0;
  }
  /deep/ .el-step.is-vertical .el-step__line {
    top: 3px;
    height: 125px;
    left: 8px;
    width: 1px;
  }
}
</style>
