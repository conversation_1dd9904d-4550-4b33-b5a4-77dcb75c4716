<template>
  <div
    class="notification"
    :style="{ width: notiwidth + 'px', right: 210 + 'px' }"
    v-if="notiShow.isShow"
  >
    <div class="notification-group">
      <i class="el-icon-warning"></i>
      <div>
        <p>
          初次使用本系统计算本月个税与工资前，若上月个税暂未向税局申报，请导入上月收入与减除。否则无法获取累计收入等计税累计项的值
        </p>
        <p class="tip">操作路径：本界面右上角【更多 - 上月收入与减除填写】</p>
        <p class="check-tip">
          <el-checkbox v-model="salaryHideTip">不再提示</el-checkbox>
        </p>
      </div>
    </div>
    <i class="el-icon-close" @click="closeNoti"></i>
  </div>
</template>
<script>
export default {
  props: {
    notiwidth: {
      type: Number,
      default: 480
    },
    notiShow: {
      type: Object,
      default: null
    },
    ruleId: {
      type: String,
      default: ""
    },
    actionUrl: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      salaryHideTip: false
    };
  },
  created() {},
  methods: {
    show() {},
    closeNoti() {
      this.$store
        .dispatch(this.actionUrl, {
          ruleId: this.ruleId,
          salaryHideTip: this.salaryHideTip
        })
        .then(res => {
          if (res.success) {
            this.notiShow.isShow = false;
          }
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.notification {
  display: flex;
  padding: 10px 26px 6px 13px;
  border-radius: 8px;
  box-sizing: border-box;
  border: 1px solid #ebeef5;
  position: fixed;
  top: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s, transform 0.3s, left 0.3s, right 0.3s, top 0.4s,
    bottom 0.3s;
  overflow: hidden;
  z-index: 99;
}
.notification-group {
  display: flex;
  margin-left: 13px;
  font-size: 11px;
  line-height: 18px;
  color: #606266;
  text-align: justify;
}
.el-icon-warning {
  font-size: 19px;
}
.el-icon-close {
  position: relative;
  right: -10px;
  font-size: 14px;
  cursor: pointer;
}
.tip {
  color: #FF9500;
}
.check-tip {
  text-align: right;
}
</style>
