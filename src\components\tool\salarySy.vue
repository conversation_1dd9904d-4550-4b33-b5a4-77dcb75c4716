<template>
  <div class="selectSY">
    <el-dialog
      :visible.sync="isShowReportInfo"
      title="反馈信息"
      width="550px"
      class="diy-el_dialog"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-row v-for="(item, index) in reportInfoList" :key="index">
        <div v-if="item.dealStatus === 'SUCCESS'">
          <el-col :span="12" style="height: 30px">
            【{{ item.taxSubName }}】
          </el-col>
          <el-col :span="12">{{ stopTip }}任务完成</el-col>
        </div>
        <div v-if="item.dealStatus === 'PROCESSING'">
          <el-col :span="12" style="height: 30px">
            【{{ item.taxSubName }}】
          </el-col>
          <el-col :span="12">{{ stopTip }}任务处理中…</el-col>
        </div>
        <div v-if="item.dealStatus === 'FAIL'">
          <el-col :span="12" style="height: 30px">
            【{{ item.taxSubName }}】
          </el-col>
          <el-col :span="12">{{ stopTip }}失败，{{ item.failReason }}</el-col>
        </div>
      </el-row>
      <div v-loading="reportInfoLoading" style="height: 40px"></div>
      <div v-show="showReturn" style="color: #ff9500">
        任务仍在处理中，请稍后点击{{ freeBackTip }}查询结果
      </div>
      <div slot="footer">
        <el-button v-show="isShowIknow" @click="onIknow" type="primary" plain>
          我知道了
        </el-button>
      </div>
    </el-dialog>
    <UpperLimit
      ref="upperLimit"
      :realPersonCount="realPersonCount"
    ></UpperLimit>
    <authorizeTip ref="authorizeTip"></authorizeTip>
  </div>
</template>
<script>
import UpperLimit from "@/components/tool/upperLimit";
import authorizeTip from "@/components/tool/authorizeTip";
import { isWechatWorkEnv } from "../../util/wechat";

export default {
  components: {
    UpperLimit,
    authorizeTip,
  },
  props: {
    validParameter: Object, //校验参数
    validAction: String, //校验action
    querytAction: String, //查询action
    sign: String, //页面标识
    stopTip: String, //终止文案
    timeObj: Object, //时间
    freeBackTip: String, //待反馈按钮提示
  },
  data() {
    return {
      isWechatWorkEnv,
      reportInfoList: [],
      reportReturnList: [],
      isShowReturnInfo: false,
      isShowReportInfo: false,
      reportInfoLoading: false,
      isShowIknow: false,
      returnTip: "",
      showReturn: false,
      realPersonCount: "", //套餐上限人数
    };
  },
  created() {},
  methods: {
    show(data) {
      if (data) {
        this.reportInfoList = [];
        this.showReturn = false;
        this.isShowIknow = false;
      } else {
        this.isShowReportInfo = false;
      }
    },
    apiActionBatchDownload() {
      return new Promise((resolve) => {
        this.$store
          .dispatch(this.validAction, this.validParameter)
          .then((res) => {
            if (res.success) {
              resolve(res);
            }
          })
          .catch(() => {
            resolve({ success: false });
          })
          .finally(() => {
            this.$parent.freshList(this.sign);
          });
      });
    },
    async handleExport() {
      this.reportInfoLoading = true;
      // this.isShowReportInfo = isWechatWorkEnv ? false : true;
      this.returnTip = "";
      console.log("===", this.validAction, this.validParameter);
      const res = await this.apiActionBatchDownload();
      if (res.success) {
        if (res.data.isOver) {
          this.$refs.upperLimit.open();
          this.realPersonCount = res.data.realPersonCount;
          return;
        }
        //验证通过
        if (res.data.status === "SUCCESS") {
          this.isShowReportInfo = true;
          this.reportInfoList = res.data.taxSubList;
          //是否进行下步查询
          if (
            res.data.taxSubList
              .map((item) => item.dealStatus === "PROCESSING")
              .includes(true)
          ) {
            this.selectShuiyou();
          } else {
            //全部成功或失败
            this.reportInfoLoading = false;
            this.isShowIknow = true;
          }
        } else {
          //授权失败
          this.reportInfoLoading = false;
          this.isShowReportInfo = false;
          this.$refs.authorizeTip.show();
        }
      } else {
        this.reportInfoLoading = false;
        this.isShowReportInfo = false;
      }
    },
    selectShuiyou() {
      this.isShowIknow = false;
      //查询第一次
      setTimeout(() => {
        this.$store
          .dispatch(this.querytAction, this.validParameter)
          .then((r0) => {
            if (r0.success) {
              if (r0.data.status === "SUCCESS") {
                this.reportInfoList.forEach((item, index) => {
                  r0.data.taxSubList.forEach((it) => {
                    if (item.taxSubId == it.taxSubId) {
                      this.reportInfoList[index] = it;
                    }
                  });
                });
                if (
                  r0.data.taxSubList
                    .map((item) => item.dealStatus === "PROCESSING")
                    .includes(true)
                ) {
                  this.selectSec();
                } else {
                  this.reportInfoLoading = false;
                  this.isShowIknow = true;
                }
              }
            }
          });
      }, this.timeObj.first);
    },
    //第二次查询
    selectSec() {
      setTimeout(() => {
        this.$store
          .dispatch(this.querytAction, this.validParameter)
          .then((r0) => {
            if (r0.data.status === "SUCCESS") {
              this.reportInfoList.forEach((item, index) => {
                r0.data.taxSubList.forEach((it) => {
                  if (item.taxSubId == it.taxSubId) {
                    this.reportInfoList[index] = it;
                  }
                });
              });
              if (
                r0.data.taxSubList
                  .map((item) => item.dealStatus === "PROCESSING")
                  .includes(true)
              ) {
                this.selectThird();
              } else {
                this.reportInfoLoading = false;
                this.isShowIknow = true;
              }
            }
          });
      }, this.timeObj.second);
    },
    //第三次查询
    selectThird() {
      setTimeout(() => {
        this.$store
          .dispatch(this.querytAction, this.validParameter)
          .then((re) => {
            if (re.success) {
              this.reportInfoList.forEach((item, index) => {
                re.data.taxSubList.forEach((it) => {
                  if (item.taxSubId == it.taxSubId) {
                    this.reportInfoList[index] = it;
                  }
                });
              });
              //反馈信息
              if (
                re.data.taxSubList
                  .map((item) => item.dealStatus === "PROCESSING")
                  .includes(true)
              ) {
                this.showReturn = true;
              }
              this.reportInfoLoading = false;
              this.isShowIknow = true;
            }
          });
      }, this.timeObj.third);
    },
    onIknow() {
      this.isShowReportInfo = false;
      this.$parent.freshList(this.sign);
    },
  },
};
</script>
<style lang="scss" scoped></style>
