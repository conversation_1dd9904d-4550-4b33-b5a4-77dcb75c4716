<template>
  <div class="selectTree">
    <div
      style="
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 200px;
      "
      v-show="isWechatWorkEnv && valueTitle"
    >
      <WechatUserNameOrDepartmentName :departmentId="getWechatDepartmentId()" />
      <i
        class="el-icon-close"
        style="cursor: pointer; color: #1a73e8"
        @click="clearDepartment"
      />
    </div>
    <el-select
      v-show="isNeedShow()"
      :value="valueTitle"
      :title="valueTitle"
      :clearable="clearable"
      :disabled="disabled"
      @clear="clearHandle"
      popper-class="select-tree"
      ref="selectTree"
    >
      <el-input
        class="selectInput"
        :placeholder="placeholder"
        v-model.trim="filterText"
      >
      </el-input>

      <el-option :value="valueTitle" :label="valueTitle" class="options">
        <el-tree
          id="tree-option"
          ref="tree"
          :expand-on-click-node="isExpand"
          :accordion="accordion"
          :data="options"
          :props="props"
          :node-key="props.value"
          :default-expanded-keys="defaultExpandedKey"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
        >
          <span slot-scope="{ data }">
            <span v-if="!isWechatWorkEnv">{{ data.label }}</span>
            <span v-if="isWechatWorkEnv"
              ><WechatUserNameOrDepartmentName :departmentId="data.label"
            /></span>
          </span>
        </el-tree>
      </el-option>
    </el-select>
  </div>
</template>
<script>
import { isWechatWorkEnv, findUsers } from "@/util/wechat";
import WechatUserNameOrDepartmentName from "../../modules/staffManage/components/wechatUserNameOrDepartmentName.vue";

export default {
  components: {
    WechatUserNameOrDepartmentName,
  },
  name: "el-tree-select",
  props: {
    /* 配置项 */
    props: {
      type: Object,
      default: () => {
        return {
          value: "id", // ID字段名
          label: "label", // 显示名称
          children: "children", // 子级字段名
        };
      },
    },
    /* 选项列表数据(树形结构的对象数组) */
    options: {
      type: Array,
      default: () => {
        return [];
      },
    },
    /* 初始值 */
    value: {
      type: Number,
      default: () => {
        return null;
      },
    },
    /* 可清空选项 */
    clearable: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    /* 自动收起 */
    accordion: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    placeholder: {
      type: String,
      default: () => {
        return "检索关键字";
      },
    },
    /* 是否可用 */
    disabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    defaultName: {
      type: String,
      default: () => {
        return "";
      },
    },
  },
  data() {
    return {
      isWechatWorkEnv,
      filterText: "",
      valueId: this.value, // 初始值
      valueTitle: "",
      defaultExpandedKey: [],
      isExpand: false,
    };
  },
  mounted() {
    this.initHandle();
  },
  methods: {
    clearDepartment() {
      this.valueTitle = "";
      this.filterText = "";
    },
    getWechatDepartmentId() {
      const tmp = this.valueTitle.split("/");
      const l = tmp.length;
      return tmp[l - 1];
    },
    isNeedShow() {
      if (isWechatWorkEnv) {
        if (this.valueTitle) {
          return false;
        }
      }
      return true;
    },
    // 初始化值
    initHandle() {
      if (this.valueId) {
        let valueId = this.valueId;
        let tree = this.$refs.tree;
        this.breadList = []; //初始化
        this.getTreeNode(tree.getNode(valueId));
        this.valueTitle = this.breadList.join("/");
        this.$refs.tree.setCurrentKey(valueId); // 设置默认选中
      }
      this.initScroll();
    },
    // 初始化滚动条
    initScroll() {
      this.$nextTick(() => {
        let scrollWrap = document.querySelectorAll(
          ".selectTree .el-scrollbar .el-select-dropdown__wrap"
        )[0];
        let scrollBar = document.querySelectorAll(
          ".selectTree .el-scrollbar .el-scrollbar__bar"
        );
        scrollWrap.style.cssText =
          "margin: 0px; max-height: none; overflow: hidden;";
        scrollBar.forEach((ele) => (ele.style.width = 0));
      });
    },
    handleNodeClick(e) {
      let tree = this.$refs.tree;
      this.breadList = [];
      this.getTreeNode(tree.getNode(e.id));
      this.valueTitle = this.breadList.join("/");
      this.valueId = e[this.props.value];
      this.$refs.selectTree.handleClose();
      this.$emit("getValue", this.valueId, this.valueTitle);
    },
    getTreeNode(node) {
      //获取当前树节点和其父级节点
      if (node) {
        if (node.label !== undefined) {
          this.breadList.unshift(node.label); //在数组头部添加元素
          this.getTreeNode(node.parent); //递归
        }
      }
    },
    // 清除选中
    clearHandle() {
      this.filterText = "";
      this.valueTitle = "";
      this.valueId = null;
      this.clearSelected();
      this.$emit("getValue", null);
    },
    /* 清空选中样式 */
    clearSelected() {
      let allNode = document.querySelectorAll("#tree-option .el-tree-node");
      allNode.forEach((element) => element.classList.remove("is-current"));
    },
    filterNode(value, data) {
      if (!value) return true;
      if (isWechatWorkEnv) {
        if (value.length) {
          return value.find((item) => item.departmentId === data.id);
        }
        return false;
      }
      return data.label.indexOf(value) !== -1;
    },
  },
  watch: {
    value() {
      this.valueId = this.value;
      this.initHandle();
    },
    async filterText(val) {
      if (isWechatWorkEnv && val) {
        val = await findUsers(val);
      }
      this.$refs.tree.filter(val);
    },
    options: {
      handler() {
        this.options.forEach((item) => {
          this.defaultExpandedKey.push(item.id);
        });
      },
      deep: true,
      immediate: true,
    },
    defaultName(val) {
      if (val) {
        this.valueTitle = val;
      }
    },
  },
};
</script>
<style scoped>
.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  min-height: 26px;
  max-height: 195px;
  padding: 0;
  margin: 0 5px;
  overflow-x: hidden;
  overflow-y: auto;
}
.el-select-dropdown__item.selected {
  font-weight: normal;
}
ul li >>> .el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
}
.el-tree-node__label {
  font-weight: normal;
}
.el-tree >>> .is-current .el-tree-node__label {
  color: #4f71ff;
  font-weight: 700;
}
.el-tree >>> .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}
.el-tree >>> .el-tree-node__content {
  height: 26px;
}
.el-select-dropdown__item {
  line-height: 26px;
}
.selectInput {
  padding: 0 5px;
  box-sizing: border-box;
  margin-bottom: 10px;
}
/* 开发禁用 */
/* .el-tree-node:focus>.el-tree-node__content{
    background-color:transparent;
    background-color: #f5f7fa;
    color: #c0c4cc;
    cursor: not-allowed;
  }
  .el-tree-node__content:hover{
    background-color: #f5f7fa;
  } */
</style>
