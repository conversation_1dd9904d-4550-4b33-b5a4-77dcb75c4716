<template>
  <div>
    <el-dialog class="dialog" :visible.sync="dialogVisible" width="30%">
      <div
        style="
          text-align: center;
          font-size: 20px;
          color: #606060;
          margin-bottom: 30px;
        "
      >
        套餐升级提醒
      </div>

      <div style="display: flex; flex-direction: column; margin-bottom: 40px">
        <div style="margin-bottom: 20px">
          套餐人数上限为<span style="color: #f8b33e; margin: 0 5px">{{
            realPersonCount
          }}</span
          >人，累计报送人数超过了人数上限！
        </div>
        <span style="margin-bottom: 10px">企业可以通过以下方式提高累计报送人数上限：</span>
        <span style="font-size: 14px; margin-bottom: 5px">1.购买增量包</span>
        <span style="font-size: 14px">2.升级套餐</span>
      </div>
      <span style="display: block; text-align: center">
        <el-button type="primary" @click="dialogVisible = false">
          我知道了
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    realPersonCount: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
    }
  }
}
</script>
<style scoped>
::v-deep .el-dialog__header {
  border-bottom: none;
}
::v-deep .el-dialog__body {
  padding-top: 0;
}
</style>