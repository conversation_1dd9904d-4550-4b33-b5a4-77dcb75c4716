<template>
  <span class="countdown"> {{ countdownString }} </span>
</template>

<script>
//countdown.vue
var timer = null
export default {
  props: {
    template: {
      type: String,
      default: '%ds',
      validator(val) {
        if (!val.includes('%d')) {
          return false
        }
        
        return true
      }
    },
    total: {
      type: Number,
      default: 60
    }
  },
  computed: {
    countdownString() {
      return this.template.replace('%d', this.countdown)
    }
  },
  data() {
    return {
      countdown: this.total
    }
  },
  methods: {
    start() {
      if (this.countdown !== this.total) {
        return
      }

      timer = setInterval(() => {
        if (this.countdown <= 1) {
          clearInterval(timer)
          this.countdown = this.total
          this.$emit('finish')
          return
        }

        this.countdown--
      }, 1000)
    }
  }
}
</script>

<style>
</style>