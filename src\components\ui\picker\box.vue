<template>
  <div
    class="pickerBox"
    style="width: 800px; height: 600px; background: #fff; border-radius: 8px"
    :style="{
      width: isSinglePicker ? '560px' : '800px'
    }"
  >
    <div
      class="header"
      style="
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f5f5f5;
        padding: 16px 24px 15px 24px;
      "
    >
      <span
        class="title"
        style="
          color: #1e2228;
          font-size: 16px;
          font-weight: 600;
          font-family: 'PingFang SC';
          text-align: left;
          line-height: 24px;
        "
      >
        {{ title }}
      </span>
      <i
        class="iconfont icon-remind-close"
        style="cursor: pointer; color: #828b9b"
        @click="$emit('cancel')"
      />
    </div>
    <div class="content" style="display: flex; height: 492px; overflow: hidden">
      <div
        class="left"
        :style="{
          flex: isSinglePicker ? '0 0 559px' : '0 0 399px',
          'border-right': isSinglePicker ? 'none' : '1px solid #f5f5f5'
        }"
        style="display: flex; flex-direction: column"
      >
        <div style="padding: 16px 24px 0 24px" v-if="$slots.tabs">
          <slot name="tabs"></slot>
        </div>

        <div style="padding: 16px 24px 0 24px" v-if="$slots.search">
          <slot name="search"></slot>
        </div>
        <div style="padding: 16px 24px 0 24px" v-if="$slots.breadcrumb">
          <slot name="breadcrumb"></slot>
        </div>
        <div
          v-if="!loading"
          style="
            padding: 16px 0px 0 24px;
            overflow: hidden;
            overflow-y: auto;
            flex: 1;
          "
          class="webkit-scrollbar"
          ref="list"
          v-infinite-scroll="leftListBottomReached"
          infinite-scroll-distance="100"
        >
          <div style="padding-right: 24px">
            <slot name="list" v-bind="{ multiple: !isSinglePicker }"></slot>
          </div>
        </div>
        <div v-else style="text-align: center">
          <i class="el-icon-loading" />加载中...
        </div>
      </div>
      <div
        class="right"
        style="padding-top: 16px; padding-left: 24px; flex: 0 0 376px"
      >
        <div
          class="webkit-scrollbar"
          style="height: 433px; overflow: hidden; overflow-y: auto"
          ref="selectedList"
          infinite-scroll-distance="100"
          v-infinite-scroll="rightListBottomReached"
        >
          <slot name="selectedList"></slot>
        </div>
        <div style="padding-right: 24px">
          <slot name="result"></slot>
        </div>
      </div>
    </div>
    <div
      class="footer"
      style="
        padding: 0 24px;
        height: 52px;
        border-top: 1px solid #f5f5f5;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
      "
    >
      <el-checkbox
        @change="$emit('changeOnlyDirectDepartmentEmployee', checked)"
        v-model="checked"
        :style="{
          visibility: onlyDirectDepartmentEmployeeShown ? '' : 'hidden'
        }"
      >
        仅选择部门直属成员
      </el-checkbox>
      <div class="actions">
        <el-button plain @click="$emit('cancel')">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm">
          确定
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import emitter from './emitter'
var timeoutHandler = null
export default {
  mixins:[emitter],
  props: {
    title: {
      type: String,
      validator(v) {
        if (!v) {
          console.error('title is required')
          return false
        }

        return true
      }
    },
    loading:Boolean,
    onlyDirectDepartmentEmployeeShown: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      confirmLoading: false,
      checked: this.onlyDirectDepartmentEmployeeShown
    }
  },
  computed: {
    isSinglePicker() {
      return !(this.$slots.selectedList && this.$slots.result)
    }
  },
  methods: {
    confirm() {
      const showLoading = () => {
        this.confirmLoading = true
      }
      const hideLoading = () => {
        this.confirmLoading = false
      }
      this.$emit('confirm', showLoading,hideLoading)
    },
    leftListBottomReached(){
      if(timeoutHandler){
        clearTimeout(timeoutHandler)
      }
      timeoutHandler = setTimeout(
        () => this.$emit('leftListBottomReached'),
        300
      )
    },
    rightListBottomReached(){
      if(timeoutHandler){
        clearTimeout(timeoutHandler)
      }
      timeoutHandler = setTimeout(
        () => this.$emit('rightListBottomReached'),
        300
      )
    }
  }
}
</script>

<style scoped>
.webkit-scrollbar:hover::-webkit-scrollbar {
  visibility: visible;
}

.webkit-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  visibility: hidden;
}

.webkit-scrollbar::-webkit-scrollbar-thumb {
  visibility: hidden;
}

.webkit-scrollbar:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  visibility: visible;
}
</style>
