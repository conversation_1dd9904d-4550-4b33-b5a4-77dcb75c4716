<template>
  <div
    class="breadcrumb"
    style="align-items: center"
    v-if="departments && departments.length"
  >
    <div
      style="
        height: 22px;
        opacity: 1;
        color: #828b9b;
        font-size: 14px;
        font-weight: 400;
        font-family: 'PingFang SC';
        text-align: left;
        line-height: 22px;
        cursor: pointer;
        display: inline-block;
      "
      :key="index"
      v-for="(department, index) in departments"
      @click="handleClick(department)"
    >
      <span
        :style="{
          color: isLast(index) ? '#1E2228' : '#828b9b'
        }"
      >
        {{ department.name }}
      </span>
      <i
        v-if="!isLast(index)"
        class="iconfont icon-direction-arrow-border-right"
        style="position: relative; left: -3px; top: 1px"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    departments: {
      type: Array,
      default() {
        return []
      }
    }
  },
  methods: {
    isLast(index) {
      return index === this.departments.length - 1
    },
    handleClick(department) {
      this.$emit('click', department)
    }
  }
}
</script>
