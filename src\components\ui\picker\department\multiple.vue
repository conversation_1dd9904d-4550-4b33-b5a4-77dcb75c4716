<template>
  <Box
    title="多选部门"
    :onlyDirectDepartmentEmployeeShown="onlyDirectDepartmentEmployeeShown"
    @confirm="$emit('confirm')"
    @cancel="$emit('cancel')"
  >
    <template #search>
      <Search @search="handleSearch" />
    </template>
    <template #breadcrumb>
      <Breadcrumb
        :departments="breadcrumbDepartments"
        @click="handleBreadcrumbClick"
      />
    </template>
    <template #list>
      <List
        :searching="searching"
        :departments="departments"
        :selectedDepartments="selectedDepartments"
        @select="v => $emit('select', v)"
        @unselect="v => $emit('unselect', v)"
        @selectAll="$emit('selectAll')"
        @unselectAll="$emit('unselectAll')"
        @clickDepartmentSubdivision="handleClickDepartmentSubdivision"
      />
    </template>
    <template #selectedList>
      <SelectedList
        :selectedDepartments="selectedDepartments"
        @unselect="v => $emit('unselect', v)"
      />
    </template>
    <template #result>
      <Result @clear="$emit('clear')">
        <span v-if="selectedDepartments.length">
          已选择：部门 {{ selectedDepartments.length }}
        </span>
      </Result>
    </template>
  </Box>
</template>

<script>
import Box from '../box.vue'
import Search from './search.vue'
import Breadcrumb from './breadcrumb.vue'
import List from './multiple/list.vue'
import SelectedList from './multiple/selectedList.vue'
import Result from '../result.vue'

const defaultBreadcrumbDepartments = [{ id: '', name: '根部门' }]
export default {
  components: {
    Box,
    Search,
    Breadcrumb,
    List,
    SelectedList,
    Result
  },
  props: {
    departments: {
      type: Array
    },
    selectedDepartments: {
      type: Array
    },
    onlyDirectDepartmentEmployeeShown: Boolean
  },
  data() {
    return {
      searching: false,
      breadcrumbDepartments: defaultBreadcrumbDepartments
    }
  },
  methods: {
    handleSearch(v) {
      this.searching = v ? true : false
      this.$emit('search', v)
    },
    handleBreadcrumbClick(v) {
      var r = []
      for (var c of this.breadcrumbDepartments) {
        r.push(c)
        if (c.id === v.id) {
          break
        }
      }

      this.breadcrumbDepartments = r
      this.$emit('clickBreadcrumbDepartment', v)
    },
    handleClickDepartmentSubdivision(v) {
      var n = [...this.breadcrumbDepartments]
      n.push(v)
      this.breadcrumbDepartments = n

      this.$emit('clickDepartmentSubdivision', v)
    }
  }
}
</script>

<style>
</style>