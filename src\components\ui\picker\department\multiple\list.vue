<template>
  <div class="list" v-if="departments.length">
    <el-checkbox
      :value="isAllSelected"
      @change="handleChange"
      style="padding-bottom: 8px"
    >
      全选
    </el-checkbox>
    <Item
      :key="index"
      v-for="(department, index) in departments"
      :department="department"
      :selected="selected(department)"
      @select="v => $emit('select', v)"
      @unselect="v => $emit('unselect', v)"
      @clickDepartmentSubdivision="v => $emit('clickDepartmentSubdivision', v)"
      style="padding-bottom: 8px"
    />
  </div>
  <NoData
    v-else-if="!departments.length && !searching"
    style="position: relative; top: 100px; left: calc(50% - 50px)"
  />
  <NoSearchResult
    v-else-if="!departments.length && searching"
    style="position: relative; top: 100px; left: calc(50% - 50px)"
  />
</template>

<script>
import Item from './listItem.vue'
import NoData from '../../../svgIcon/noData.vue'
import NoSearchResult from '../../../svgIcon/noSearchResult.vue'

export default {
  computed: {
    isAllSelected() {
      var allSelect = true
      for (var c of this.departments) {
        if (c.disabled) {
          continue
        }
        var selected = false
        for (var cc of this.selectedDepartments) {
          if (c.id === cc.id) {
            selected = true
            break
          }
        }
        if (!selected) {
          allSelect = false
          break
        }
      }

      return allSelect
    }
  },
  components: {
    Item,
    NoData,
    NoSearchResult
  },
  props: {
    searching: Boolean,
    departments: {
      type: Array,
      default() {
        return []
      }
    },
    selectedDepartments: {
      type: Array,
      default() {
        return []
      }
    }
  },
  methods: {
    handleChange(checked) {
      if (checked) {
        this.$emit('selectAll', this.departments)
        return
      }

      this.$emit('unselectAll', [])
    },
    selected(department) {
      if (!this.selectedDepartments || !this.selectedDepartments.length) {
        return false
      }

      const noExisted = !this.selectedDepartments.find(
        item => item.id === department.id
      )

      return noExisted === false
    }
  }
}
</script>
