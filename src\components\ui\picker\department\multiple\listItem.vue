<template>
  <div
    class="item"
    style="display: flex; justify-content: space-between; align-items: center"
    :style="{
      opacity: department.disabled ? '0.5' : '1',
      cursor: department.disabled ? 'not-allowed' : 'pointer'
    }"
    v-if="department && department.name"
  >
    <el-checkbox
      style="display: flex; align-items: center"
      :disabled="department.disabled"
      :value="selected"
      @change="
        v => (v ? $emit('select', department) : $emit('unselect', department))
      "
    >
      <div style="display: flex; align-items: center; flex: 1">
        <div
          style="
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: #f0f5ff;
            margin-right: 8px;
          "
        >
          <i
            class="iconfont icon-application-hierarchy"
            style="color: #2f54eb; position: relative; top: 7px; left: 8px"
          />
        </div>
        <span
          class="name"
          style="
            height: 22px;
            color: #1e2228;
            font-weight: 400;
            line-height: 22px;
          "
        >
          {{ department.name }}
        </span>
        <span
          class="count"
          style="
            height: 22px;
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            position: relative;
            left: 8px;
            color: #828b9b;
          "
          v-if="department.employeeTotal"
        >
          ({{ department.employeeTotal }})
        </span>
      </div>
    </el-checkbox>
    <a
      style="flex: 0 0 32px; color: #4f71ff"
      v-if="department.children && department.children.length"
      :style="{
        color: department.disabled ? '#828b9b' : 'var(--o-primary-color)'
      }"
      @click="
        !department.disabled
          ? $emit('clickDepartmentSubdivision', department)
          : () => {}
      "
    >
      下级
    </a>
  </div>
</template>

<script>
export default {
  props: {
    selected: Boolean,
    department: {
      type: Object,
      validator() {
        return true
      }
    }
  },
  computed: {}
}
</script>