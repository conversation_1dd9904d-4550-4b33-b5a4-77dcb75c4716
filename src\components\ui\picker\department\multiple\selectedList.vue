<template>
  <div class="selectedList" v-if="selectedDepartments.length">
    <SelectedListItem
      :selected="false"
      :key="index"
      :department="department"
      v-for="(department, index) in selectedDepartments"
      @unselect="v => $emit('unselect', v)"
    />
  </div>
  <NoData
    v-else-if="noDataShown && !selectedDepartments.length"
    style="position: relative; top: 100px; left: 120px"
  />
</template>

<script>
import SelectedListItem from './selectedListItem.vue'
import NoData from '../../../svgIcon/noData.vue'
export default {
  components: {
    SelectedListItem,
    NoData
  },
  props: {
    noDataShown: {
      type: Boolean,
      default() {
        return true
      }
    },
    selectedDepartments: {
      type: Array,
      default() {
        return []
      }
    }
  }
}
</script>