<template>
  <div
    class="selectedListItem"
    style="
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 8px;
      margin-bottom: 8px;
      padding: 8px;
      height: 32px;
      border-radius: 6px;
      cursor: pointer;
    "
  >
    <div style="flex: 1; display: flex; align-items: center; overflow: hidden">
      <div
        style="
          width: 32px;
          height: 32px;
          text-align: center;
          border-radius: 8px;
          opacity: 1;
          background: #f0f5ff;
          margin-right: 8px;
        "
      >
        <i
          class="iconfont icon-application-hierarchy"
          style="color: #2f54eb; position: relative; top: 5px"
        />
      </div>
      <div class="name">
        <div class="count">
          {{ department.name }}
          <span style="color: #828b9b" v-if="department.employeeTotal">
            ({{ department.employeeTotal }})
          </span>
        </div>
        <div style="color: #828b9b; font-size: 12px">
          {{ parentDepartments }}
        </div>
      </div>
    </div>
    <i
      @click="$emit('unselect', department)"
      class="iconfont icon-remind-close"
      style="cursor: pointer; color: #828b9b; position: relative; left: -8px"
    />
  </div>
</template>

<script>
export default {
  props: {
    department: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    parentDepartments() {
      // debugger
      if (this.department.parentDepartments.length <= 2) {
        return ''
      }
      if (this.department.parentDepartments.length <= 3) {
        return this.department.parentDepartments[1].name
      }
      if (this.department.parentDepartments.length <= 4) {
        return (
          this.department.parentDepartments[1].name +
          this.department.parentDepartments[2].name
        )
      }
      const last = this.department.parentDepartments.length - 1
      return (
        '...' +
        '/' +
        this.department.parentDepartments[last - 2].name +
        '/' +
        this.department.parentDepartments[last - 1].name
      )
    }
  }
}
</script>

<style scoped>
.selectedListItem:hover {
  background: #f2f4f7;
}
</style>