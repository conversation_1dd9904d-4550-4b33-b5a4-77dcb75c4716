<template>
  <div class="search">
    <Input
      v-model="value"
      :allowZero="true"
      :trim="true"
      :placeholder="placeholder"
      suffix-icon="icon iconfont icon-base-find-search"
      @input="change"
      maxlength="20"
      clearable
      @clear="onClear"
    />
  </div>
</template>

<script>
import Input from 'kit/components/marketing/admin/input.vue'
var timeoutHandler = null
var lastValue = ''
export default {
  components: {
    Input
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    }
  },
  data() {
    return {
      value: '',
      timeoutHandler: null
    }
  },
  methods: {
    change() {
      if(lastValue === this.value){
        return 
      }

      if (timeoutHandler) {
        clearTimeout(timeoutHandler)
      }
      lastValue = this.value
      timeoutHandler = setTimeout(() => this.$emit('search', this.value), 300)
    },
    onClear() {
      this.$emit('search', this.value)
    }
  }
}
</script>
<style scoped>
::v-deep .el-input__suffix {
  right: 9px;
}
</style>
