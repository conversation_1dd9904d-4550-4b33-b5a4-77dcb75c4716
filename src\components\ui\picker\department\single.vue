<template>
  <Box
    title="单选部门"
    :onlyDirectDepartmentEmployeeShown="onlyDirectDepartmentEmployeeShown"
    @confirm="$emit('confirm')"
    @cancel="$emit('cancel')"
  >
    <template #search>
      <Search placeholder="请输入部门名称" @search="handleSearch" />
    </template>
    <template #breadcrumb>
      <Breadcrumb
        :departments="breadcrumbDepartments"
        @click="v => $emit('clickBreadcrumbDepartment', v)"
      />
    </template>
    <template #list>
      <List
        :departments="departments"
        :selectedDepartment="selectedDepartment"
        :searching="searching"
        @select="v => $emit('select', v)"
        @unselect="v => $emit('unselect', v)"
        @clickDepartmentSubdivision="
          v => $emit('clickDepartmentSubdivision', v)
        "
      />
    </template>
  </Box>
</template>

<script>
import Box from '../box.vue'
import Search from './search.vue'
import Breadcrumb from './breadcrumb.vue'
import List from './single/list.vue'
export default {
  components: {
    Box,
    Search,
    Breadcrumb,
    List
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入部门名称、员工姓名'
    },
    departments: {
      type: Array
    },
    selectedDepartment: {
      type: Object
    },
    onlyDirectDepartmentEmployeeShown: Boolean,
    breadcrumbDepartments: {
      type: Array
    }
  },
  data() {
    return {
      searching: false
    }
  },
  methods: {
    handleSearch(v) {
      this.searching = v ? true : false
      this.$emit('search', v)
    }
  }
}
</script>
