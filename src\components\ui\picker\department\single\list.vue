<template>
  <div class="list" v-if="departments.length">
    <Item
      :searching="searching"
      :key="department.id"
      v-for="department in departments"
      :selected="selectedDepartment && selectedDepartment.id === department.id"
      :department="department"
      @select="v => $emit('select', v)"
      @unselect="v => $emit('unselect', v)"
      @clickDepartmentSubdivision="v => $emit('clickDepartmentSubdivision', v)"
    />
  </div>
  <div
    v-else-if="!departments.length && !searching && noDataShown"
    style="display: flex; justify-content: center; align-items: center"
  >
    <NoData />
  </div>
  <div
    v-else-if="!departments.length && searching && noSearchResultShown"
    style="display: flex; justify-content: center; align-items: center"
  >
    <NoSearchResult />
  </div>
</template>

<script>
import NoData from '../../../svgIcon/noData.vue'
import NoSearchResult from '../../../svgIcon/noSearchResult.vue'
import Item from './listItem.vue'
export default {
  components: {
    NoData,
    NoSearchResult,
    Item
  },
  props: {
    searching: Boolean,
    noSearchResultShown: {
      type: Boolean,
      default() {
        return true
      }
    },
    noDataShown: {
      type: Boolean,
      default() {
        return true
      }
    },
    departments: {
      type: Array,
      default() {
        return []
      }
    },
    selectedDepartment: {
      type: Object
    }
  }
}
</script>