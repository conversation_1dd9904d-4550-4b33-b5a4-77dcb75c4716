<template>
  <div
    class="item"
    style="
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 8px;
      height: 32px;
      border-radius: 6px;
    "
    :style="{
      background: selected ? '#E8F0FF' : '',
      opacity: department.disabled ? '0.5' : '1',
      cursor: department.disabled ? 'not-allowed' : 'pointer'
    }"
    v-if="department && department.name"
    @click="handleClick"
  >
    <div
      style="
        width: 32px;
        height: 32px;
        text-align: center;
        border-radius: 8px;
        background: #f0f5ff;
        margin-right: 8px;
        flex: 0 0 32px;
        display: flex;
        justify-content: center;
        align-items: center;
      "
    >
    <!-- A14部门负责人维护时，展现图标优化为红色 -->
      <i class="iconfont icon-application-hierarchy" style="color: var(--o-primary-color);" />
    </div>
    <div class="name" style="flex: 1">
      <div
        class="count"
        style="position: relative"
        :style="{
          top: searching && departmentParentDepartments ? '-3px' : ''
        }"
      >
        {{ department.name }}
        <span style="color: #828b9b">
          ({{ department.employeeTotal || 0 }})
        </span>
      </div>
      <div
        style="color: #828b9b; font-size: 12px; position: relative; top: 3px"
        v-if="searching"
      >
        {{ departmentParentDepartments }}
      </div>
    </div>
    <a
      v-if="department.children?.length"
      style="flex: 0 0 32px"
      :style="{
        color:
          department.disabled ? '#828b9b' : 'var(--o-primary-color)'
      }"
      @click="
        !department.disabled
          ? $emit('clickDepartmentSubdivision', department)
          : () => {}
      "
    >
      下级
    </a>
  </div>
</template>

<script>
import formatDepartmentsToStringWithBackslash from 'kit/formatters/marketing/formatDepartmentsToStringWithBackslash'
export default {
  computed: {
    departmentParentDepartments() {
      return formatDepartmentsToStringWithBackslash(
        this.department.parentDepartments
      )
    }
  },
  props: {
    searching: Boolean,
    selected: Boolean,
    department: {
      type: Object
    }
  },
  methods: {
    handleClick() {
      if (this.department.disabled) {
        return
      }

      if (this.selected) {
        this.$emit('unselect', this.department)
        return
      }
      this.$emit('select', this.department)
    }
  }
}
</script>

<style scoped>
.item:hover {
  background: #f2f4f7;
}
</style>
