const colors = [
  '#4F71FF',
  '#FFAC04',
  '#07BB06',
  '#71248D',
  '#c65306',
  '#006e5f',
  '#b5bb1a'
]
const hashCode = str => {
  str = `${str}HashCode`
  var hash = 0,
    i,
    chr
  if (str.length === 0) return hash
  for (i = 0; i < str.length; i++) {
    chr = str.charCodeAt(i)
    hash = (hash << 5) - hash + chr
    hash |= 0
  }
  if (hash < 0) {
    hash = Math.abs(hash) + 1
  }

  return hash
}
const getColorByEmployId = id => {
  const hash = hashCode(id)
  const c = hash % colors.length
  return colors[c]
}

export default getColorByEmployId
