<template>
  <Box :title="title" @confirm="$emit('confirm')" @cancel="$emit('cancel')">
    <template #search>
      <Search @search="handleSearch" />
    </template>
    <template #breadcrumb>
      <div
        style="
          height: 22px;
          color: #828b9b;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
        "
      >
        您可能想找
      </div>
    </template>
    <template #list>
      <List
        :searching="searching"
        :employees="employees"
        :selectedEmployees="selectedEmployees"
        @select="v => $emit('select', v)"
        @unselect="v => $emit('unselect', v)"
        @selectAll="$emit('selectAll')"
        @unselectAll="$emit('unselectAll')"
      />
    </template>
    <template #selectedList>
      <SelectedList
        :selectedEmployees="selectedEmployees"
        @unselect="v => $emit('unselect', v)"
      />
    </template>
    <template #result>
      <Result @clear="$emit('clear')">
        <span v-if="selectedEmployees.length">
          已选择：人员 {{ selectedEmployees.length }}
        </span>
      </Result>
    </template>
  </Box>
</template>

<script>
import Box from '../box.vue'
import Search from '../department/search.vue'
import List from './multiple/list.vue'
import SelectedList from './multiple/selectedList.vue'
import Result from '../result.vue'

export default {
  components: {
    Box,
    Search,
    List,
    SelectedList,
    Result
  },
  props: {
    title: {
      type: String
    },
    employees: {
      type: Array
    },
    selectedEmployees: {
      type: Array
    }
  },
  data() {
    return {
      searching: false
    }
  },
  methods: {
    handleSearch(v) {
      this.searching = v ? true : false
      this.$emit('search', v)
    }
  }
}
</script>