<template>
  <div class="list" v-if="employees.length">
    <el-checkbox
      :value="isAllChecked"
      @change="handleChange"
      :indeterminate="indeterminate"
      v-show="ifShowAllSelected"
      style="padding-bottom: 8px"
      v-if="!searching"
    >
      全选
    </el-checkbox>
    <span v-else>搜索结果</span>
    <Item
      :searching="searching"
      :key="index"
      v-for="(employee, index) in employees"
      :employee="employee"
      :selected="selected(employee)"
      :disabled="employee.disabled"
      @select="v => $emit('select', v)"
      @unselect="v => $emit('unselect', v)"
      style="padding-bottom: 8px"
    />
  </div>
  <div
    v-else-if="!employees.length && !searching"
    style="display: flex; justify-content: center; align-items: center"
  >
    <NoData />
  </div>
  <div
    v-else-if="!employees.length && searching"
    style="display: flex; justify-content: center; align-items: center"
  >
    <NoSearchResult />
  </div>
</template>

<script>
import Item from './listItem.vue'
import NoData from '../../../svgIcon/noData.vue'
import NoSearchResult from '../../../svgIcon/noSearchResult.vue'

export default {
  componentName: 'leftEmployeeList',
  computed: {
    ifShowAllSelected() {
      return this.paginationEmployees.some(item => !item.disabled)
    },
    paginationEmployees() {
      return this.employees.slice(this.pagination.start, this.pagination.end)
    },
    employeesLength() {
      return this.employees.length
    },
    indeterminate() {
      if (this.isAllChecked) {
        return false
      }
      for (var c of this.employees) {
        if (c.disabled) {
          continue
        }
        if (this.allSelectedEmployees.find(item => item.id === c.id)) {
          return true
        }
      }

      return null
    }
  },
  components: {
    Item,
    NoData,
    NoSearchResult
  },
  props: {
    searching: Boolean,
    isAllChecked: Boolean,
    employees: {
      type: Array,
      default() {
        return []
      }
    },
    allSelectedEmployees: {
      type: Array,
      default() {
        return []
      }
    },
    selectedEmployees: {
      type: Array,
      default() {
        return []
      }
    }
  },
  created() {
    this.$on('pickerBoxListScroll', target => {
      if (this.pagination.end >= this.employeesLength) {
        console.log('已经加载完全部数据')
        return
      }
      if (target.scrollTop + target.clientHeight >= target.scrollHeight) {
        this.pagination.end = this.pagination.end + this.pagination.size
      }
    })
  },
  data() {
    return {
      pagination: {
        start: 0,
        end: 10,
        size: 30
      }
    }
  },
  methods: {
    handleChange() {
      if (!this.isAllChecked) {
        this.$emit('selectAll')
        return
      }

      this.$emit('unselectAll')
    },
    selected(employee) {
      const existed = this.allSelectedEmployees.find(
        item => item.id === employee.id
      )
      if (existed) {
        return true
      }

      return false
    }
  }
}
</script>
