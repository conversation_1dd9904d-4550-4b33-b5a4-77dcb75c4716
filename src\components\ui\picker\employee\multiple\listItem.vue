<template>
  <div
    class="item"
    style="display: flex; justify-content: space-between; align-items: center"
    :style="{
      opacity: employee.disabled ? '0.5' : '1',
      cursor: employee.disabled ? 'not-allowed' : 'pointer'
    }"
    v-if="employee && employee.name"
  >
    <el-checkbox
      style="display: flex; align-items: center"
      :disabled="employee.disabled || disabled"
      :value="selected"
      @change="
        v => (v ? $emit('select', employee) : $emit('unselect', employee))
      "
    >
      <div style="display: flex; align-items: center; flex: 1">
        <div
          style="
            width: 32px;
            height: 32px;
            border-radius: 8px;
            margin-right: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
          "
          :style="{
            background: `${color}20`
          }"
        >
          <span
            :style="{
              color: color
            }"
          >
            {{ lastName }}
          </span>
        </div>
        <div class="name">
          {{ employee.name }}
          <div style="color: #828b9b; font-size: 12px" v-if="searching">
            {{ employeeDepartments }}
          </div>
        </div>
      </div>
    </el-checkbox>
  </div>
</template>

<script>
import formatDepartmentsToStringWithBackslash from 'kit/formatters/marketing/formatDepartmentsToStringWithBackslash'
import getColorByEmployId from '../color'
export default {
  computed: {
    color() {
      return getColorByEmployId(this.employee.id)
    },
    lastName() {
      return this.employee.name[this.employee.name.length - 1]
    },
    employeeDepartments() {
      return formatDepartmentsToStringWithBackslash(this.employee.departments)
    }
  },
  props: {
    searching: Boolean,
    disabled:Boolean,
    departmentsNotShown: {
      type: Boolean
    },
    selected: Boolean,
    employee: {
      type: Object,
      validator() {
        return true
      }
    }
  }
}
</script>