<template>
  <div class="selectedList" v-if="hadData">
    <SelectedListItem
      :selected="false"
      :key="index"
      :employee="employee"
      v-for="(employee, index) in selectedEmployees"
      @unselect="v => $emit('unselect', v)"
    />
  </div>
  <NoData
    v-else-if="noDataShown && !hadData"
    style="position: relative; top: 100px; left: 120px"
  />
</template>

<script>
import SelectedListItem from './selectedListItem.vue'
import NoData from '../../../svgIcon/noData.vue'
export default {
  componentName: 'selectedEmployeeList',
  components: {
    SelectedListItem,
    NoData
  },
  computed: {
    hadData(){
      if(this.selectedEmployees.length){
        return true
      }
      return false
    }
  },
  props: {
    noDataShown: {
      type: Boolean,
      default() {
        return true
      }
    },
    selectedEmployees: {
      type: Array,
      default() {
        return []
      }
    }
  }
}
</script>
