<template>
  <div
    class="selectedListItem"
    style="
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 8px;
      margin-bottom: 8px;
      padding: 8px;
      height: 32px;
      border-radius: 6px;
      cursor: pointer;
    "
  >
    <div style="flex: 1; display: flex; align-items: center; overflow: hidden">
      <div
        style="
          width: 32px;
          height: 32px;
          border-radius: 8px;
          margin-right: 8px;
          display: flex;
          justify-content: center;
          align-items: center;
        "
        :style="{
          background: `${color}20`
        }"
      >
        <span
          :style="{
            color: color
          }"
        >
          {{ lastName }}
        </span>
      </div>
      <div class="name">
        <div style="position: relative; top: -3px">{{ employee.name }}</div>
        <div
          style="color: #828b9b; font-size: 12px; position: relative; top: 3px"
          v-if="employee.departments && employee.departments.length"
        >
          {{ employeeDepartments }}
        </div>
      </div>
    </div>
    <i
      @click="$emit('unselect', employee)"
      class="iconfont icon-remind-close"
      style="
        cursor: pointer;
        color: #828b9b;
        position: relative;
        left: -8px;
        font-size: 14px;
      "
    />
  </div>
</template>

<script>
import getColorByEmployId from '../color'
import formatDepartmentsToStringWithBackslash from 'kit/formatters/marketing/formatDepartmentsToStringWithBackslash'

export default {
  computed: {
    color() {
      return getColorByEmployId(this.employee.id)
    },
    lastName() {
      return this.employee.name[this.employee.name.length - 1]
    },
    employeeDepartments() {
      return formatDepartmentsToStringWithBackslash(this.employee.departments)
    }
  },
  props: {
    employee: {
      type: Object,
      default() {
        return {}
      }
    }
  }
}
</script>

<style scoped>
.selectedListItem:hover {
  background: #f2f4f7;
}
</style>
