<template>
  <Box
    :title="title"
    @confirm="$emit('confirm')"
    @cancel="$emit('cancel')"
    :onlyDirectDepartmentEmployeeShown="false"
  >
    <template #search>
      <Search @search="handleSearch" />
    </template>
    <template #breadcrumb>
      <div
        style="
          height: 22px;
          color: #828b9b;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
        "
      >
        您可能想找
      </div>
    </template>
    <template #list>
      <List
        :employees="employees"
        :selectedEmployee="selectedEmployee"
        :searching="searching"
        @select="v => $emit('select', v)"
        @unselect="v => $emit('unselect', v)"
        @clickEmployeeSubdivision="handleClickEmployeeSubdivision"
      />
    </template>
  </Box>
</template>

<script>
import Box from '../box.vue'
import Search from '../department/search.vue'
import List from './single/list.vue'
export default {
  components: {
    Box,
    Search,
    List
  },
  props: {
    title: {
      type: String
    },
    employees: {
      type: Array
    },
    selectedEmployee: {
      type: Object
    }
  },
  data() {
    return {
      searching: false
    }
  },
  methods: {
    handleSearch(v) {
      this.searching = v ? true : false
      this.$emit('search', v)
    },
    handleBreadcrumbClick(v) {
      var r = []
      for (var c of this.breadcrumbEmployees) {
        r.push(c)
        if (c.id === v.id) {
          break
        }
      }

      this.breadcrumbEmployees = r
      this.$emit('clickBreadcrumbEmployee', v)
    },
    handleClickEmployeeSubdivision(v) {
      var n = [...this.breadcrumbEmployees]
      n.push(v)
      this.breadcrumbEmployees = n

      this.$emit('clickEmployeeSubdivision', v)
    }
  }
}
</script>
