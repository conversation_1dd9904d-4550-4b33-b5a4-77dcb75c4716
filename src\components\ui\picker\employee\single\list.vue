<template>
  <div>
    <div class="list" v-if="employees?.length">
      <Item
        :key="employee.id"
        :searching="searching"
        v-for="employee in employees"
        :selected="selectedEmployee && selectedEmployee.id === employee.id"
        :employee="employee"
        @select="v => $emit('select', v)"
        @unselect="v => $emit('unselect', v)"
      />
    </div>
    <NoData
      v-else-if="!employees?.length && !searching && !departments.length"
      style="position: relative; top: 100px; left: calc(50% - 50px)"
    />
    <NoSearchResult
      v-else-if="!employees?.length && searching"
      style="position: relative; top: 100px; left: calc(50% - 50px)"
    />
  </div>
</template>

<script>
import NoData from '../../../svgIcon/noData.vue'
import NoSearchResult from '../../../svgIcon/noSearchResult.vue'
import Item from './listItem.vue'
export default {
  components: {
    NoData,
    NoSearchResult,
    Item
  },
  props: {
    searching: Boolean,
    departments: {
      type: Array,
      default() {
        return []
      }
    },
    employees: {
      type: Array,
      default() {
        return []
      }
    },
    selectedEmployee: {
      type: Object
    }
  }
}
</script>