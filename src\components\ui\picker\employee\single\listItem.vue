<template>
  <div
    class="item"
    style="
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 8px;
      height: 32px;
      border-radius: 6px;
    "
    :style="{
      background: selected ? '#E8F0FF' : '',
      opacity: employee.disabled ? '0.5' : '1',
      cursor: employee.disabled ? 'not-allowed' : 'pointer'
    }"
    v-if="employee && employee.name"
    @click="handleClick"
  >
    <div
      style="width: 32px; height: 32px; border-radius: 8px; margin-right: 8px;display: flex; align-items: center;justify-content: center"
      :style="{
        background: `${color}20`
      }"
    >
      <span
        :style="{
          color: color
        }"
      >
        {{ lastName }}
      </span>
    </div>
    <div class="name">
      <span>{{ employee.name }}</span>
      <div style="color: #828b9b; font-size: 12px" v-if="searching">
        {{ employeeDepartments }}
      </div>
    </div>
  </div>
</template>

<script>
import formatDepartmentsToStringWithBackslash from 'kit/formatters/marketing/formatDepartmentsToStringWithBackslash'
import getColorByEmployId from '../color'
export default {
  computed: {
    color() {
      return getColorByEmployId(this.employee.id)
    },
    lastName() {
      return this.employee.name[this.employee.name.length - 1]
    },
    employeeDepartments() {
      return formatDepartmentsToStringWithBackslash(this.employee.departments)
    }
  },
  props: {
    searching: Boolean,
    selected: Boolean,
    employee: {
      type: Object
    }
  },
  methods: {
    handleClick() {
      if (this.employee.disabled) {
        return
      }

      if (this.selected) {
        this.$emit('unselect', this.employee)
        return
      }
      this.$emit('select', this.employee)
    }
  }
}
</script>

<style scoped>
.item:hover {
  background: #f2f4f7;
}
.name{
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 3px;
}
</style>