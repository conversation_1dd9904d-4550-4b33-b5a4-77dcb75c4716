<template>
  <Box
    :title="title"
    :loading="loading"
    :onlyDirectDepartmentEmployeeShown="false"
    @confirm="$emit('confirm')"
    @cancel="$emit('cancel')"
    @leftListBottomReached="
      !searching ? $emit('leftListBottomReached') : void 0
    "
    @rightListBottomReached="$emit('rightListBottomReached')"
  >
    <template #tabs v-if="tabs.length">
      <Tabs :tabs="tabs" @change="handleTabChange" />
    </template>
    <template #search v-if="activatedTab == '人员'">
      <Search @search="handleSearch" />
    </template>
    <template #breadcrumb v-if="activatedTab == '部门'">
      <Breadcrumb
        :departments="breadcrumbDepartments"
        @click="v => $emit('clickBreadcrumbDepartment', v)"
      />
    </template>
    <template #list>
      <div>
        <DepartmentList
          v-if="activatedTab === '部门'"
          :searching="false"
          :departments="departments"
          :selectedDepartments="selectedDepartments"
          @clickDepartmentSubdivision="
            v => $emit('clickDepartmentSubdivision', v)
          "
          @select="v => $emit('selectDepartment', v)"
          @unselect="v => $emit('unselectDepartment', v)"
          @selectAll="$emit('selectAllDepartments')"
          @unselectAll="$emit('unselectAllDepartments')"
        />
        <EmployeeList
          v-if="activatedTab === '人员'"
          :itemDepartmentsNotShown="false"
          :searching="searching"
          :employees="employees"
          :allSelectedEmployees="allSelectedEmployees"
          :selectedEmployees="selectedEmployees"
          :isAllChecked="isAllEmployeesChecked"
          @select="v => $emit('selectEmployee', v)"
          @unselect="v => $emit('unselectEmployee', v)"
          @selectAll="$emit('selectAllEmployees')"
          @unselectAll="$emit('unselectAllEmployees')"
        />
      </div>
    </template>
    <template #selectedList>
      <div>
        <DepartmentSelectedList
          :noDataShown="false"
          :selectedDepartments="selectedDepartments"
          @unselect="v => $emit('unselectDepartment', v)"
        />
        <EmployeeSelectedList
          :noDataShown="false"
          :selectedEmployees="selectedEmployees"
          @unselect="v => $emit('unselectEmployee', v)"
        />
      </div>
    </template>
    <template #result>
      <Result @clear="$emit('clear')">
        <template>
          <span v-if="selectedDepartments.length || selectedEmployees.length">
            已选择：
          </span>
          <span v-if="selectedDepartments.length">
            部门 {{ selectedDepartments.length }}
          </span>
          <span v-if="selectedEmployees.length">
            人员 {{ allSelectedEmployees.length }}
          </span>
        </template>
      </Result>
    </template>
  </Box>
</template>

<script>
import Box from './box.vue'
import Tabs from './tabs.vue'
import Search from './department/search.vue'
import Breadcrumb from './department/breadcrumb.vue'
import DepartmentList from './department/multiple/list.vue'
import DepartmentSelectedList from './department/multiple/selectedList.vue'
import EmployeeList from './employee/multiple/list.vue'
import EmployeeSelectedList from './employee/multiple/selectedList.vue'
import Result from './result.vue'
export default {
  components: {
    Box,
    Tabs,
    Search,
    Breadcrumb,

    EmployeeList,
    DepartmentSelectedList,

    DepartmentList,
    EmployeeSelectedList,
    Result
  },
  props: {
    tabs: {
      type: Array,
      default() {
        return ['部门', '人员']
      }
    },
    defaultActivatedTab: {
      type: String,
      default() {
        return '部门'
      }
    },
    title: {
      type: String,
      validator(v) {
        return v
      }
    },
    departments: {
      type: Array,
      default() {
        return []
      }
    },
    allSelectedEmployees: {
      type: Array,
      default() {
        return []
      }
    },
    selectedDepartments: {
      type: Array
    },
    employees: {
      type: Array,
      default() {
        return []
      }
    },
    selectedEmployees: {
      type: Array
    },
    breadcrumbDepartments: {
      type: Array
    },
    isAllEmployeesChecked: {
      type: Boolean
    },
    loading: {
      type: Boolean
    }
  },
  data() {
    return {
      searching: false,
      activatedTab: this.defaultActivatedTab
    }
  },
  methods: {
    handleTabChange(item) {
      this.activatedTab = item
    },
    handleSearch(v) {
      this.searching = v ? true : false
      this.$emit('search', v, this.activatedTab)
    }
  }
}
</script>

<style>
</style>