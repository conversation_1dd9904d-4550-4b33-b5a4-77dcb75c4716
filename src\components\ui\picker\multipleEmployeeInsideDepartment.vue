<template>
  <Box
    :title="title"
    :onlyDirectDepartmentEmployeeShown="false"
    :loading="loading"
    @confirm="
      (showLoading, hideLoading) => $emit('confirm', showLoading, hideLoading)
    "
    @cancel="$emit('cancel')"
    @leftListBottomReached="
      !searching ? $emit('leftListBottomReached') : void 0
    "
    @rightListBottomReached="$emit('rightListBottomReached')"
  >
    <template #search>
      <Search @search="handleSearch" />
    </template>
    <template #breadcrumb>
      <Breadcrumb
        :departments="breadcrumbDepartments"
        @click="v => $emit('clickBreadcrumbDepartment', v)"
      />
    </template>
    <template #list>
      <div style="max-height:220px;overflow: hidden;overflow-y: auto;padding-right:5px;">
        <DepartmentSingleList
          :noSearchResultShown="false"
          :noDataShown="false"
          :searching="searching"
          :departments="departments"
          :selectedDepartment="selectedDepartment"
          @select="v => $emit('selectDepartment', v)"
          @unselect="v => $emit('unselectDepartment', v)"
          @clickDepartmentSubdivision="
            v => $emit('clickDepartmentSubdivision', v)
          "
        />
      </div>
      <EmployeeMultipleList
        :itemDepartmentsNotShown="true"
        :isAllChecked="isAllChecked"
        :searching="searching"
        :employees="employees"
        :allSelectedEmployees="allSelectedEmployees"
        :selectedEmployees="selectedEmployees"
        @select="v => $emit('selectEmployee', v)"
        @unselect="v => $emit('unselectEmployee', v)"
        @selectAll="() => $emit('selectAllEmployees')"
        @unselectAll="() => $emit('unselectAllEmployees')"
      />
    </template>
    <template #selectedList>
      <SelectedList
        :allSelectedEmployees="allSelectedEmployees"
        :selectedEmployees="allSelectedEmployees"
        @unselect="v => $emit('unselectSelectedEmployee', v)"
      />
    </template>
    <template #result>
      <Result v-show="allSelectedEmployees.length > 0" @clear="$emit('clear')">
        <span> 已选择：人员 {{ allSelectedEmployees.length }} </span>
      </Result>
    </template>
  </Box>
</template>

<script>
import Box from './box.vue'
import Search from './department/search.vue'
import Breadcrumb from './department/breadcrumb.vue'
import DepartmentSingleList from './department/single/list.vue'
import EmployeeMultipleList from './employee/multiple/list.vue'
import SelectedList from './employee/multiple/selectedList.vue'
import Result from './result.vue'
export default {
  components: {
    Box,
    Search,
    Breadcrumb,
    EmployeeMultipleList,
    DepartmentSingleList,
    SelectedList,
    Result
  },
  props: {
    title: {
      type: String,
      validator(v) {
        return v
      }
    },
    loading: Boolean,
    isAllChecked: Boolean,
    breadcrumbDepartments: {
      type: Array
    },
    departments: {
      type: Array,
      default() {
        return []
      }
    },
    employees: {
      type: Array,
      default() {
        return []
      }
    },
    allSelectedEmployees: {
      type: Array,
      default() {
        return []
      }
    },
    selectedEmployees: {
      type: Array,
      default() {
        return []
      }
    },
    selectedDepartment: {
      type: Object,
      default() {
        return null
      }
    }
  },
  data() {
    return {
      searching: false
    }
  },
  methods: {
    handleSearch(v) {
      this.searching = v.trim() ? true : false
      this.$emit('search', v.trim())
    }
  }
}
</script>
