<template>
  <Box
    :title="title"
    :onlyDirectDepartmentEmployeeShown="false"
    @confirm="$emit('confirm')"
    @cancel="$emit('cancel')"
  >
    <template #search>
      <Search @search="handleSearch" :placeholder="placeholder" />
    </template>
    <template #breadcrumb>
      <Breadcrumb
        v-if="!searching"
        :departments="breadcrumbDepartments"
        @click="handleBreadcrumbClick"
      />
    </template>
    <template #list>
      <div>
        <DepartmentList
          :searching="false"
          v-if="departments.length && !searching"
          :departments="departments"
          @clickDepartmentSubdivision="handleClickDepartmentSubdivision"
        />
        <List
          :itemDepartmentsNotShown="true"
          :searching="searching"
          :employees="employees"
          :departments="departments"
          :selectedEmployee="selectedEmployee"
          @select="v => $emit('select', v)"
          @unselect="v => $emit('unselect', v)"
        />
      </div>
    </template>
  </Box>
</template>

<script>
import Box from './box.vue'
import Search from './department/search.vue'
import Breadcrumb from './department/breadcrumb.vue'
import DepartmentList from './department/single/list.vue'
import List from './employee/single/list.vue'

export default {
  components: {
    Box,
    Search,
    Breadcrumb,
    List,
    DepartmentList
  },
  props: {
    title: {
      type: String,
      validator(v) {
        return v
      }
    },
    breadcrumbDepartments:{
      type: Array,
      default() {
        return []
      }
    },
    departments: {
      type: Array,
      default() {
        return []
      }
    },
    placeholder:{
      type:String,
      default:""
    },
    employees: {
      type: Array,
      default() {
        return []
      }
    },
    selectedEmployee: {
      type: Object
    }
  },
  data() {
    return {
      searching: false,
    }
  },
  methods: {
    handleSearch(v) {
      this.searching = v ? true : false
      this.$emit('search', v)
    },
    handleBreadcrumbClick(item) {
      var result = []
      for (var c of this.breadcrumbDepartments) {
        result.push(c)
        if (c.id === item.id) {
          break
        }
      }

      this.$emit("update:breadcrumbDepartments",result)
      this.$emit('clickBreadcrumbDepartment', item)
    },
    handleClickDepartmentSubdivision(v) {
      var n = [...this.breadcrumbDepartments]
      n.push(v)
      this.$emit("update:breadcrumbDepartments",n)
      this.$emit('clickDepartmentSubdivision', v)
    }
  }
}
</script>