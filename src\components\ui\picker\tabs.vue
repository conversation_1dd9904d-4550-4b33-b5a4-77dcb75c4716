<template>
  <div class="tabs" v-if="tabs && tabs.length">
    <div
      :class="index === activated ? 'item activated' : 'item'"
      @click="handleClick(index)"
      :key="index"
      v-for="(item, index) in tabs"
    >
      {{ getName(item) }}
    </div>
  </div>
</template>

<script>
import { isString } from '../../../helpers'
export default {
  props: {
    tabs: {
      type: Array
    }
  },
  data() {
    return {
      activated: 0
    }
  },
  methods: {
    getName(item) {
      if (isString(item)) {
        return item
      }

      return item.name
    },
    handleClick(index) {
      this.activated = index
      this.$emit('change', this.tabs[index])
    }
  }
}
</script>

<style scoped>
.tabs {
  border-radius: 4px;
  padding: 5px 5px;
  display: flex;
  align-items: center;
  background: #f2f4f7;
}
.item {
  width: 87px;
  height: 28px;
  line-height: 28px;
  margin: 0 2px;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
}
.item.activated {
  background: #fff;
  cursor: default;
}
</style>