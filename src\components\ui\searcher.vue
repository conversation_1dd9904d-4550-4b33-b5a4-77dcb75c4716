<template>
  <div class="searcher">
    <el-form :inline="true" class="search">
      <div
        v-if="$scope.lite"
        class="lite"
        style="display: flex; align-items: center"
      >
        <div
          class="form-items"
          style="display: flex; gap: 10px; width: calc(100% - 290px)"
        >
          
        </div>

        <el-button type="text" @click="fullShown = true">展开</el-button>
        <div style="text-align: right; flex: 1">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    conditions: {
      type: Array,
      default() {
        return []
      }
    }
  },
  computed: {
    isNeedLite() {
      return this.conditions.length > 2
    }
  }
}
</script>

<style scoped></style>
