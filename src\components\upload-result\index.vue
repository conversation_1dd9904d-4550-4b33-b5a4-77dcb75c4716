<template>
  <div class="async-result">
    <!-- 查看导入结果 -->
    <el-dialog
      title="导入结果"
      :visible.sync="isShowResult"
      :destroy-on-close="true"
      @close="close"
      width="900px"
      class="result-dialog"
    >
      <el-table
        :data="resultData"
        :header-cell-style="{ background: '#F1F1F1' }"
        stripe
        border
        height="300px"
      >
        <el-table-column
          prop="fileName"
          label="文件名称"
          width="120"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="createTime"
          label="导入时间"
          width="160"
        ></el-table-column>
        <el-table-column
          prop="successCount"
          label="导入成功条数"
        ></el-table-column>
        <el-table-column
          prop="failedCount"
          label="导入失败条数"
        ></el-table-column>
        <el-table-column
          prop="totalNumber"
          label="导入总条数"
        ></el-table-column>
        <el-table-column prop="taskStatus" label="导入状态">
          <template slot-scope="scope">
            {{ scope.row.taskStatus | filterImportStatus }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              v-show="scope.row.failedCount > 0"
              @click="handleDownloadFail(scope.row)"
            >
              下载失败记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" size="small" @click="isShowResult = false">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { apiGetImportTaskList, apiDownloadFail } from "./api";
export default {
  props: ["importType"],
  data() {
    return {
      isShowResult: false, //查看导入结果
      resultData: [],
    };
  },
  methods: {
    //查看导入结果
    handleShowResult() {
      apiGetImportTaskList({
        checkId: this.$route.query.id,
        importType: this.importType,
      }).then((res) => {
        if (res.success) {
          this.resultData = res.data;
          this.isShowResult = true;
        }
      });
    },
    //下载失败记录
    async handleDownloadFail(row) {
      await apiDownloadFail({ taskNo: row.taskId });
    },
    close() {
      this.$emit("refresh");
    },
  },
};
</script>
<style lang="scss" scoped>
.async-result {
  .el-steps {
    margin: 30px 0 50px 0;
    width: 910px;
  }
  .main-content {
    position: relative;
    min-height: 570px;
    padding: 0;
    .content-con {
      height: 400px;
      overflow: auto;
    }
  }
  .upload-finish {
    color: #6a6f7f;
    text-align: center;
    i {
      font-size: 72px;
    }
    p {
      font-size: 24px;
      font-weight: bold;
      color: #070f29;
      margin: 20px 0;
    }
  }
  .footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    border-top: 1px solid #ededed;
    text-align: center;
    padding: 15px 0;
    /deep/.el-button--primary {
      font-size: 12px;
    }
  }
}
/deep/.el-dialog__body {
  padding: 0;
}
.result-dialog {
  .main-content {
    height: 400px;
  }
  /deep/.el-table {
    margin: 30px 20px;
    width: calc(100% - 40px);
  }
}
</style>