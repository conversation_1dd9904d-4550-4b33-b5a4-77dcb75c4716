export default {
	name: 'el-table-infinite-scroll',
	bind(el, binding, vnode) {
		const tableWrapper = el.querySelector('.el-table__body-wrapper')
		binding.handler = function(){
			const {scrollTop, scrollHeight, clientHeight} = tableWrapper
			console.log(scrollHeight,scrollTop + clientHeight,scrollHeight <= scrollTop + clientHeight,'scrollHeight <= scrollTop + clientHeight')
			if (scrollHeight <= scrollTop + clientHeight+10) {
				binding.value && binding.value()
			}
		}
		tableWrapper.addEventListener('scroll', binding.handler)
	},
	unbind(el, binding) {
		el.removeEventListener('scroll', binding.handler)
		el = null
	}
}
