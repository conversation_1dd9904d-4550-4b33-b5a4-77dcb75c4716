import Vue from 'vue';
import filterSample from './sample';

Vue.filter('filterSample', filterSample);
//data时间转化
const resetDate = val => {
  return val ? val.split(' ')[0] : '';
};
//截取年月
const getYearAndMonth = val => {
  if (val) {
    let str = val.split(' ')[0];
    let year = str.substr(0, 4);
    let month = str.substr(5, 2);
    return `${year}年${month}月`;
  } else {
    return '';
  }
};
//计税规则
const texRule = val => {
  switch (val) {
    case 'SALARY_PAY_RULE': {
      return '按工资薪金所得计算个税';
    }
    case 'NOT_SALARY_PAY_RULE': {
      return '非工资薪金所得计算个税';
    }
    case 'PERSON_PAY_RULE': {
      return '按劳务报酬所得计算个税';
    }
    case 'YEAR_END_BONUS': {
      return '按全年一次性奖金计算个税';
    }
  }
};
//核对人员计税规则
const filterTaxRule = val => {
  switch (val) {
    case 'SALARY_PAY_RULE': {
      return '正常工资薪金';
    }
    case 'PERSON_PAY_RULE': {
      return '一般劳务报酬';
    }
    case 'YEAR_END_BONUS': {
      return '全年一次性奖金';
    }
    case 'REMOVE_CONTRACT_RULE': {
      return '解除劳动合同一次性补偿金';
    }
  }
};
//薪资核算状态
const salaryCheckStatus = val => {
  switch (val) {
    case 'NONE': {
      return '未启动';
    }
    case 'INIT': {
      return '未计算';
    }
    case 'CHECKED_MEMBER': {
      return '人员已核对';
    }
    case 'CHECKED_SALARY': {
      return '薪资已核对';
    }
    case 'WAIT_BACK': {
      return '待反馈';
    }
    case 'AUDIT_REJECT': {
      return '审核拒绝';
    }
    case 'COMPUTED': {
      return '已计算';
    }
    case 'AUDITED': {
      return '审核通过';
    }
    case 'PAID': {
      return '薪资已发放';
    }
    case 'IMPORT_PAID': {
      return '薪资已发放';
    }
    case 'FINISH': {
      return '结束';
    }
  }
};
//用工性质
export const filterEmpType = val => {
  switch (val) {
    case 'FULL_TIME': {
      return '全职';
    }
    case 'PART_TIME': {
      return '兼职';
    }
    case 'PRACTICE': {
      return '实习';
    }
    case 'LABOUR': {
      return '劳务';
    }
    case 'RE_EMPLOY': {
      return '退休返聘';
    }
  }
};
//学历
const filterEducation = val => {
  switch (val) {
    case 'MASTER': {
      return '研究生';
    }
    case 'REGULAR': {
      return '大学本科';
    }
    case 'OTHER': {
      return '大学本科以下';
    }
  }
};
//员工状态
export const filterEmployStatus = val => {
  switch (val) {
    case 'ON_THE_JOB': {
      return '在职';
    }
    case 'DIMISSION': {
      return '离职';
    }
    case 'RETIRED': {
      return '退休';
    }
  }
};

//转正状态
export const filterTurnRegularStatus = val => {
  switch (val) {
    case 'PROBATION': {
      return '试用';
    }
    case 'OFFICIAL': {
      return '转正';
    }
    case 'NO_PROBATION': {
      return '无试用期';
    }
  }
};

//确认状态
export const filterConfirmStatus = val => {
  switch (val) {
    case 'LEAVE_WAIT_CONFIRM': {
      return '待离职';
    }
    case 'ENTRY_WAIT_CONFIRM': {
      return '待入职';
    }
    case 'CONFIRMED': {
      return '已确认';
    }
  }
};

//入职登记表
export const filterRegisterFormStatus = val => {
  switch (val) {
    case 'COMMITTED': {
      return '已提交';
    }
    case 'UNCOMMITTED': {
      return '未提交';
    }
  }
};

//发薪状态
export const payrollStatus = val => {
  switch (val) {
    case 'INIT': {
      return '未提交';
    }
    case 'PROCESSING': {
      return '处理中';
    }
    case 'FINISH': {
      return '处理完成';
    }
    case 'FAIL': {
      return '处理失败';
    }
  }
};
//发薪状态 明细详情
export const payrollStatusDetail = val => {
  switch (val) {
    case 'PROCESSING': {
      return '交易中';
    }
    case 'SUCCESS': {
      return '交易成功';
    }
    case 'FAILED': {
      return '交易失败';
    }
  }
};
//授权状态
export const authStatus = val => {
  switch (val) {
    case 'ING': {
      return '授权中';
    }
    case 'SUCCESS': {
      return '授权成功';
    }
    case 'FAIL': {
      return '授权失败';
    }
  }
};

//代发类型
export const payrollType = val => {
  switch (val) {
    case 'TYPE_01': {
      return '代发工资';
    }
    case 'TYPE_02': {
      return '代发奖金';
    }
    case 'TYPE_03': {
      return '代发养老金';
    }
    case 'TYPE_04': {
      return '代发补贴';
    }
    case 'TYPE_05': {
      return '代发劳务费';
    }
    case 'TYPE_06': {
      return '代发奖学金';
    }
    case 'TYPE_07': {
      return '代发加班费';
    }
    case 'TYPE_08': {
      return '代发福利费';
    }
    case 'TYPE_09': {
      return '代发报销款';
    }
    case 'TYPE_10': {
      return '代发差旅费';
    }
    case 'TYPE_11': {
      return '代发交通费';
    }
    case 'TYPE_12': {
      return '代发通讯费';
    }
    case 'TYPE_13': {
      return '代发社保金';
    }
    case 'TYPE_14': {
      return '代发车贴';
    }
    case 'TYPE_15': {
      return '代发拆迁款';
    }
    case 'TYPE_16': {
      return '代发其他';
    }
  }
};
//对比上月状态
export const compareLastMonthOperation = val => {
  switch (val) {
    case 'ADD': {
      return '增员';
    }
    case 'UPDATE': {
      return '更新';
    }
    case 'DELETE': {
      return '减员';
    }
  }
};
//户口性质
const householdRegistrationType = val => {
  switch (val) {
    case 'CITY': {
      return '城镇户口';
    }
    case 'VILLAGE': {
      return '农村户口';
    }
  }
};
const filterType = val => {
  switch (val) {
    case 'SYSYEM': {
      return '系统项';
    }
    case 'COMPUTE': {
      return '计算项';
    }
    case 'BEFORE_TAX': {
      return '税前';
    }
    case 'DEDUCT': {
      return '税后';
    }
    case 'AFTER_TAX': {
      return '扣减项';
    }
    case 'SALARY': {
      return '收入项';
    }
  }
};
const getDay = val => {
  let selectMonth;
  switch (val) {
    case 'CURRENT_MONTH':
      selectMonth = this.month;
      break;
    case 'NEXT_MONTH':
      selectMonth = this.month - -1;
      break;
    case 'LAST_MONTH':
      selectMonth = this.month - 1;
      break;
    default:
  }
  if ([1, 3, 5, 7, 8, 10, 12].includes(selectMonth)) {
    this.fullDay = 31;
  }
  if ([4, 6, 9, 11].includes(selectMonth)) {
    this.fullDay = 30;
  }
  if (selectMonth == '2') {
    if (
      (this.fullYear % 4 == 0 && this.fullYear % 100 != 0) ||
      this.fullYear % 400 == 0
    ) {
      this.fullDay = 29; //闰年2月29天
    } else {
      this.fullDay = 28; //闰年平年28天
    }
  }
};
//验证状态
const accreditStatus = val => {
  switch (val) {
    case 'SUCCESS': {
      return '验证成功';
    }
    case 'FAIL': {
      return '验证失败';
    }
    case 'WAIT_ACCREDIT': {
      return '待反馈';
    }
  }
};
//身份认证状态
const contractAuthStatus = val => {
  switch (val) {
    case 'SUCCESS': {
      return '认证通过';
    }
    case 'FAIL': {
      return '认证未通过';
    }
    case 'INIT': {
      return '未认证';
    }
    case 'STASH': {
      return '待提交';
    }
    case 'COMMIT': {
      return '待审核';
    }
  }
};
const filtersSalaryType = val => {
  switch (val) {
    case 'NO_PROVIDE': {
      return '未发放';
    }
    case 'PROVIDED': {
      return '已发放';
    }
    case 'BACK': {
      return '已撤销';
    }
  }
};
const idType = val => {
  switch (val) {
    case 'PRC_ID': {
      return '居民身份证';
    }
    case 'COMPATRIOTS_CARD': {
      return '港澳居民来往内地通行证';
    }
    case 'FORMOSA_CARD': {
      return '台湾居民来往大陆通行证';
    }
    case 'CHINA_PASSPORT': {
      return '中国护照';
    }
    case 'FOREIGN_PASSPORT': {
      return '外国护照';
    }
    case 'FORMOSA_PRC_ID': {
      return '台湾居民居住证';
    }
    case 'MACAU_PRC_ID': {
      return '港澳居民居住证';
    }
    case 'FOREIGN_PRC_ID': {
      return '外国人永久居留身份证';
    }
    case 'FOREIGN_WORK_PERMIT_A': {
      return '外国人工作许可证（A类）';
    }
    case 'FOREIGN_WORK_PERMIT_B': {
      return '外国人工作许可证（B类）';
    }
    case 'FOREIGN_WORK_PERMIT_C': {
      return '外国人工作许可证（C类）';
    }
    case 'OTHER': {
      return '其他';
    }
  }
};

const dateStyle = val => {
  if (val) {
    return val.split(' ')[0];
  }
};

const countryType = val => {
  switch (val) {
    case 'CHINA': {
      return '中国';
    }
  }
};

const reportType = val => {
  switch (val) {
    case 'FAIL': {
      return '失败';
    }
    case 'CHECK_FAILED': {
      return '校验失败';
    }
  }
};

//性别
const filterEmpSex = val => {
  switch (val) {
    case 'MALE': {
      return '男';
    }
    case 'FEMALE': {
      return '女';
    }
  }
};
//参保状态
const insuredStatus = val => {
  switch (val) {
    case 'INSURED_STOP': {
      return '已停保';
    }
    case 'INSURED_ING': {
      return '参保中';
    }
  }
};

const insuranceType = val => {
  switch (val) {
    case 'ENDOWMENT_INSURANCE': {
      return '养老保险';
    }
    case 'MEDICAL_INSURANCE': {
      return '医疗保险';
    }
    case 'UNEMPLOYMENT_INSURANCE': {
      return '失业保险';
    }
    case 'INJURY_INSURANCE': {
      return '工伤保险';
    }
    case 'BIRTH_INSURANCE': {
      return '生育保险';
    }
    case 'SERIOUS_DISEASE_TREATMENT': {
      return '大病医疗';
    }
    case 'COMPENSATORY_ENDOWMENT_INSURANCE': {
      return '补充养老保险';
    }
    case 'RESIDUAL_INSURANCE_GOLD': {
      return '残保金';
    }
    case 'COMPENSATORY_MEDICAL_INSURANCE': {
      return '补充医疗保险';
    }
    case 'COMPENSATORY_UNEMPLOYMENT_INSURANCE': {
      return '补充失业保险';
    }
    case 'ACCUMULATION_FUND': {
      return '公积金';
    }
    case 'COMPENSATORY_ACCUMULATION_FUND': {
      return '补偿公积金';
    }
    case 'TOTAL_INSURANCE': {
      return '合计';
    }
  }
};
//证件类型
const filterIdType = val => {
  switch (val) {
    case 'PRC_ID': {
      return '居民身份证';
    }
    case 'COMPATRIOTS_CARD': {
      return '港澳居民来往内地通行证';
    }
    case 'FORMOSA_CARD': {
      return '台湾居民来往大陆通行证';
    }
    case 'CHINA_PASSPORT': {
      return '中国护照';
    }
    case 'FOREIGN_PASSPORT': {
      return '外国护照';
    }
    case 'FORMOSA_PRC_ID': {
      return '台湾居民居住证';
    }
    case 'MACAU_PRC_ID': {
      return '港澳居民居住证';
    }
    case 'FOREIGN_PRC_ID': {
      return '外国人永久居留身份证';
    }
    case 'FOREIGN_WORK_PERMIT_A': {
      return '外国人工作许可证（A类）';
    }
    case 'FOREIGN_WORK_PERMIT_B': {
      return '外国人工作许可证（B类）';
    }
    case 'FOREIGN_WORK_PERMIT_C': {
      return '外国人工作许可证（C类）';
    }
    case 'OTHER': {
      return '其他';
    }
  }
};

// 任职受雇从业类型
const workType = val => {
  switch (val) {
    case 'EMPLOYEE': {
      return '雇员';
    }
    case 'OTHER': {
      return '其他';
    }
    case 'INSURANCE_SELLER': {
      return '保险营销员';
    }
    case 'INTERNSHIP_STUDENT': {
      return '实习学生（全日制学历教育）';
    }
    case 'STOCK_BROKER': {
      return '证券经纪人';
    }
  }
};
// 是否转正
const regularEmpYn = val => {
  switch (val) {
    case 'PROBATION': {
      return '试用';
    }
    case 'OFFICIAL': {
      return '转正';
    }
    case 'NO_PROBATION': {
      return '无试用期';
    }
  }
};
// 同步人员-状态
const filterSynchroStatus = val => {
  switch (val) {
    case 'AWAIT_ADD': {
      return '待新增';
    }
    case 'AWAIT_UPDATE': {
      return '待更新';
    }
    case 'SUBMITTED': {
      return '已同步';
    }
  }
};

//合同管理-展示命名规则
const nominateRule = val => {
  let statusName = '';
  let newArr = [];
  val.forEach(it => {
    switch (it) {
      case 'NAME': {
        newArr.push('+姓名');
        break;
      }
      case 'DEPARTMENT': {
        newArr.push('+部门');
        break;
      }
      case 'POSITION': {
        newArr.push('+岗位');
        break;
      }
    }
  });
  statusName = newArr.join('');
  return statusName;
};
//模板类型
const templateType = val => {
  switch (val) {
    case 'LABOUR_CONTRACT': {
      return '劳动合同';
    }
    case 'PROVE': {
      return '证明';
    }
    case 'RULES': {
      return '规章制度';
    }
    case 'OTHERS': {
      return '其他';
    }
  }
};
//合同期限单位
const contractTermUnit = val => {
  switch (val) {
    case 'YEAR': {
      return '年';
    }
    case 'MONTH': {
      return '月';
    }
    case 'DAY': {
      return '日';
    }
  }
};
//合同状态
const contractStatus = val => {
  switch (val) {
    case 'NOT_EXECUTE': {
      return '未执行';
    }
    case 'EXECUTE': {
      return '执行中';
    }
    case 'EXPIRE': {
      return '已到期';
    }
    case 'END': {
      return '已终止';
    }
    case 'REMOVE': {
      return '已解除';
    }
  }
};
//签约状态
const contractSignStatus = val => {
  switch (val) {
    case 'WAIT_SIGN':
    case 'WATING_SIGN': {
      return '未签约';
    }
    case 'SIGNING': {
      return '签约中';
    }
    case 'FINISH': {
      return '已签约';
    }
  }
};
//文件状态
const fileStatus = val => {
  switch (val) {
    case 'DRAFT': {
      return '草稿';
    }
    case 'CREATED': {
      return '已发起';
    }
    case 'WAIT_SIGN': {
      return '待签署';
    }
    case 'SIGNING': {
      return '签署中';
    }
    case 'COMPLETE': {
      return '已完成';
    }
    case 'EXPIRED': {
      return '已终止';
    }
    case 'DELETE': {
      return '已删除';
    }
    case 'CANCEL': {
      return '已废弃';
    }
    case 'ABORT': {
      return '已退回';
    }
    case 'ASYNC_CREATE_FAIL': {
      return '创建失败';
    }
    case 'WITHDRAWN': {
      return '已撤回';
    }
  }
};
//签订类型
const signType = val => {
  switch (val) {
    case 'NEW_SIGN': {
      return '新签';
    }
    case 'CONTINUE_SIGN': {
      return '续签';
    }
    case 'UPDATE_SIGN': {
      return '变更';
    }
  }
};
//使用状态
const templateStatus = val => {
  switch (val) {
    case 'ERROR': {
      return '异常';
    }
    case 'DRAFT': {
      return '草稿';
    }
    case 'ENABLED': {
      return '启用';
    }
    case 'DISABLED': {
      return '停用';
    }
  }
};
//导入任务状态
const importTaskStatus = val => {
  switch (val) {
    case 'INIT': {
      return '未开始';
    }
    case 'PROCESSING': {
      return '进行中';
    }
    case 'FINISH': {
      return '已完成';
    }
  }
};
//审核状态
export const filterAuditStatus = val => {
  switch (val) {
    case 'INIT': {
      return '未发起';
    }
    case 'AUDITING': {
      return '审批中';
    }
    case 'AUDIT_REFUSE': {
      return '审批拒绝';
    }
    case 'AUDIT_PASSES': {
      return '审批通过';
    }
    case 'AUDIT_BACK': {
      return '审批撤销';
    }
  }
};
//入职方式
export const filterEntryWay = val => {
  switch (val) {
    case 'AUDIT': {
      return '审批入职';
    }
    case 'MANUAL': {
      return '手动入职';
    }
    case 'SCANCODE': {
      return '扫码入职';
    }
  }
};
//转正类型
export const filterRegularType = val => {
  switch (val) {
    case 'FORWARD_REGULAR':
      return '提前转正';
    case 'NORMAL_REGULAR':
      return '正常转正';
    case 'DELAY_REGULAR':
      return '延后转正';
  }
};
//导入状态
export const filterImportStatus = val => {
  switch (val) {
    case 'INIT':
      return '未开始';
    case 'PROCESSING':
      return '进行中';
    case 'FINISH':
      return '已完成';
  }
};
export default {
  resetDate,
  getYearAndMonth,
  texRule,
  salaryCheckStatus,
  filterEmpType,
  idType,
  countryType,
  accreditStatus,
  contractAuthStatus,
  dateStyle,
  insuranceType,
  reportType,
  filterEmployStatus,
  filterTurnRegularStatus,
  filterRegisterFormStatus,
  filterEmpSex,
  filterIdType,
  filterEducation,
  householdRegistrationType,
  workType,
  insuredStatus,
  compareLastMonthOperation,
  regularEmpYn,
  payrollStatus,
  payrollStatusDetail,
  payrollType,
  authStatus,
  filterSynchroStatus,
  filterTaxRule,
  nominateRule,
  templateType,
  contractTermUnit,
  contractStatus,
  contractSignStatus,
  signType,
  templateStatus,
  fileStatus,
  filterConfirmStatus,
  importTaskStatus,
  filterAuditStatus,
  filterEntryWay,
  filterRegularType,
  filterImportStatus
};
