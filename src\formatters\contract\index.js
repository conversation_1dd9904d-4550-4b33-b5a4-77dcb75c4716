import {
  TemplateStatusDraft,
  TemplateStatusOpened,
  TemplateStatusStopped,
  rulesTypeText,
  rulesTypeDate,
  rulesTypeAutoIncrementNumber,
  rulesTypeRandomNumber
} from '../../services/contract/constants'
export const status2string = status => {
  if (status === TemplateStatusDraft) {
    return '草稿'
  }
  if (status === TemplateStatusOpened) {
    return '已启用'
  }
  if (status === TemplateStatusStopped) {
    return '已停用'
  }

  return status
}

export const formatTemplate = templates => {
  for (var item of templates) {
    item.status = status2string(item.status)
  }
  return templates
}

export const formatTypeTreeNodes = groups => {
  var nodes = []
  for (var group of groups) {
    var node = {
      id: group.id,
      type: 'group',
      label: group.name,
      enable: true, //分组都是可用的，类型有的可用有的停用
      // enable: group.enable ? true : false,
      children: []
    }
    nodes.push(node)
    for (var type of group.nodes) {
      node.children.push({
        id: type.id,
        groupId: group.id,
        type: 'type',
        label: type.name,
        enable: type.enable ? true : false
      })
    }
  }
  const all = {
    label: '全部类型',
    id: 'all',
    enable: true,
    children: nodes
  }

  return [all]
}

export const formatNumberRuleRule = rule => {
  if (rule.type === rulesTypeText) {
    return rule.value
  }
  if (rule.type === rulesTypeDate) {
    const len = rule.value.length
    return len === 4
      ? 'yyyy'
      : len === 6
      ? 'yyyyMM'
      : len === 8
      ? 'yyyyMMdd'
      : len === 12
      ? 'yyyyMMddHHmm'
      : len === 14
      ? 'yyyyMMddHHmmss'
      : 'yyyyMMdd'
  }
  if (rule.type === rulesTypeAutoIncrementNumber) {
    return `${rule.value}位自增流水号`
  }
  if (rule.type === rulesTypeRandomNumber) {
    return `${rule.value}位随机流水号`
  }
  throw new Error('后端没有提供此编号规则的校验!!!')
}

export const formatErrorMessageToErrorMessages = errorMessage => {
  return errorMessage.split('\n')
}

export const formatIntFillZero = (v, size) => {
  const s = v + ''
  const l = s.length
  var needFileSize = 0
  if (l < size) {
    needFileSize = size - l
  }
  const zeros = Array(needFileSize).fill('0')
  return zeros.join('') + s
}

//format 仅支持rules
//yyyy yyyyMM yyyyMMdd yyyyMMddHH yyyyMMddHHmm yyyyMMddHHmmss
const formatRuleDate = (datetime, format) => {
  if (!datetime) {
    datetime = new Date().getTime()
  }
  const current = new Date().toLocaleTimeString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })

  const ds = current.replace(' ', '').replaceAll('/', '').replaceAll(':', '')
  const l = format.length

  return ds.slice(0, l)
}
export const formatRulesPreviews = (rules = []) => {
  var r = []
  var cRules = [...rules]
  for (var c of cRules) {
    if (c.type === rulesTypeText) {
      r.push(c.value)
    }
    if (c.type === rulesTypeDate) {
      r.push(formatRuleDate(Date.now(), c.value))
    }
    if (c.type === rulesTypeAutoIncrementNumber) {
      if (c.value == 0) break
      r.push(formatIntFillZero(c.start, parseInt(c.value, 10)))
    }
    if (c.type === rulesTypeRandomNumber) {
      if (c.value == 0) break
      let random =
        Math.round(Math.random() * Math.pow(10, parseInt(c.value, 10))) + ''
      if (random.length > c.value) random = random.slice(0, c.value) + ''
      r.push(random.padStart(c.value, '0'))
    }
  }
  return r.join('')
}

export const formatFilterOtherGroup = groups => {
  return groups.filter(({ name }) => name !== '其他')
}

export const formatGroupsSortUnUpdate = groups => {
  var i = 0
  var otherIndex = null
  while (i < groups.length) {
    if (groups[i].name === '其他' || groups[i].groupName === '其他') {
      otherIndex = i
      i++
      continue
    }
    groups[i].sort = i
    i++
  }
  if (otherIndex !== null) {
    const other = groups.splice(otherIndex, 1)[0]
    other.sort = i
    groups.push(other)
  }
  return groups
}