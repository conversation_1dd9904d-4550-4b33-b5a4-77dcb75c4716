const scale2px = (scale, P) => {
  return parseFloat((scale * P).toFixed(2))
}

const formatPageFieldsPx = (pageFields, fileImageSizes) => {
  for (var pageField of pageFields) {
    const widthHeight = fileImageSizes[pageField.fileId]
    if (!widthHeight) {
      return
    }
    //避免重复转
    if (pageField.height > 1) {
      continue
    }
    pageField.height = scale2px(pageField.height, widthHeight[1])
    pageField.width = scale2px(pageField.width, widthHeight[0])
    pageField.fontSize = scale2px(pageField.fontSize, widthHeight[0])
    pageField.coordX = scale2px(pageField.coordX, widthHeight[0])
    pageField.coordY = scale2px(pageField.coordY, widthHeight[1])
  }
}

export default formatPageFieldsPx