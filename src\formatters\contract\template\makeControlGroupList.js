import deepClone from '../../../helpers/deepClone'
const px2scale = (px, P) => {
  return parseFloat((px / P).toFixed(8))
}
const makeControlGroupList = (fields, pageFields, fileImageSizes) => {
  var r = []
  for (var item of fields) {
    var controlGroup = deepClone(item)
    delete controlGroup.id
    delete controlGroup.common
    controlGroup.controlList = []
    const cpagefields = pageFields.filter(citem => citem.fieldId === item.id)
    for (var cpageField of cpagefields) {
      var control = deepClone(cpageField)
      var widthHeight = fileImageSizes[control.fileId]
      delete control.id
      delete control.fieldId
      delete control.common
      control.height = px2scale(control.height, widthHeight[1])
      control.width = px2scale(control.width, widthHeight[0])
      control.fontSize = px2scale(control.fontSize, widthHeight[0])
      control.coordX = px2scale(control.coordX, widthHeight[0])
      control.coordY = px2scale(control.coordY, widthHeight[1])
      controlGroup.controlList.push(control)
    }
    //没有任何pagefield，则不需要提交
    if (!controlGroup.controlList.length) {
      continue
    }

    r.push(controlGroup)
  }

  return r
}

export default makeControlGroupList
