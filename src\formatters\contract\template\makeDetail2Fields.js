import makeFieldId from './makeFieldId'
import deepClone from '../../../helpers/deepClone'
import assignObjectsKey from './assignObjectsKey'
import defaultField from './defaultField'

const makeDetail2Fields = makeDetail => {
  var fieldMap = {}
  var fields = []
  for (var file of makeDetail.fileList) {
    if (!file.controlGroupList) {
      continue
    }
    for (var controlGroup of file.controlGroupList) {
      const id = makeFieldId(controlGroup.name, controlGroup.signStepId)
      if (fieldMap[id]) {
        continue
      }
      var field = deepClone(defaultField)

      assignObjectsKey(field, controlGroup)
      if (makeDetail.customFieldList) {
        const isCustomControl = makeDetail.customFieldList.find(
          item => item.name === field.name
        )
        field.common = isCustomControl ? true : false
      }

      field.id = id
      fields.push(field)

      fieldMap[id] = true
    }
  }

  return fields
}
export default makeDetail2Fields
