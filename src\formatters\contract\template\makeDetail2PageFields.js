import makePageField from './makePageField'
import assignObjectsKey from './assignObjectsKey'

const _makeDetail2PageFields = file => {
  if (!file.controlGroupList) {
    return []
  }

  var r = []
  for (var controlGroup of file.controlGroupList) {
    if (!controlGroup.controlList) {
      continue
    }
    for (var control of controlGroup.controlList) {
      var pageField = makePageField({
        field: {
          name: controlGroup.name,
          signStepId: controlGroup.signStepId
        },
        fileId: file.fileId,
        pageNo: control.pageNo,
        coordX: control.coordX,
        coordY: control.coordY
      })
      //将id固化 能使用单元测试进行测试
      assignObjectsKey(pageField, control)
      r.push(pageField)
    }
  }

  return r
}

const makeDetail2PageFields = makeDetail => {
  var r = []
  for (var i = 0; i < makeDetail.fileList.length; i++) {
    if (makeDetail.fileList[i]) {
      const fPageFields = _makeDetail2PageFields(makeDetail.fileList[i])
      r.push(...fPageFields)
    }
  }

  return r
}

export default makeDetail2PageFields