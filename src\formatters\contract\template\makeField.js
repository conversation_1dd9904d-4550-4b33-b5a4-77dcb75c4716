import makeFieldId from './makeFieldId'
import deepClone from '../../../helpers/deepClone'
import assignObjectsKey from './assignObjectsKey'
import defaultField from './defaultField'
import isSignatureFieldType from '../../../components/contract/template/isSignatureFieldType'
const makeField = params => {
  const fieldId = makeFieldId(params.name, params.signStepId)
  var field = deepClone(defaultField)
  assignObjectsKey(field, params)
  field.id = fieldId

  if (!isSignatureFieldType(field.type)) {
    field.modifiable = true
  }

  return field
}

export default makeField
