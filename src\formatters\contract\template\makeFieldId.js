const stringHashcode = str => {
  var hash = 0,
    i,
    chr
  if (str.length === 0) return hash
  for (i = 0; i < str.length; i++) {
    chr = str.charCodeAt(i)
    hash = (hash << 5) - hash + chr
    hash |= 0
  }
  return hash + ''
}

const makeFieldId = (name, signStepId) => {
  //避免undefined参与计算
  signStepId = signStepId ? signStepId : ''
  return stringHashcode(name + signStepId)
}

export default makeFieldId