import deepClone from '../../../helpers/deepClone'
import makeFieldId from './makeFieldId'
import {
  FieldTypeDate,
  FieldTypeCompany,
  FieldTypePerson
} from '../../../services/contract/constants'

import defaultPageField from './defaultPageField'

const randomRange = (n, m) => {
  var num = Math.floor(Math.random() * (m - n + 1) + n)
  return num
}

var usedRandomNumbers = {}
const getPageFieldUniqueSuffixId = (n, m) => {
  const num = randomRange(1000, 99999)
  if (usedRandomNumbers[num]) {
    return getPageFieldUniqueSuffixId(n, m)
  }
  usedRandomNumbers[num] = true
  return num
}

const calcTextWidth = (text, fontSize) => {
  if (!text || !fontSize) {
    throw new Error('text and fontSize are required')
  }
  return text.length * fontSize
}
const makePageField = params => {
  const { field, fileId, pageNo, coordX, coordY } = params
  if (!field || !field.name || !fileId || !pageNo || !coordX || !coordY) {
    throw new Error(
      'newPageField params must have field.Name,fileId,pageNo,coordX,coordY' +
        ' and all are required'
    )
  }
  const fieldId = makeFieldId(field.name, field.signStepId)
  var pageField = deepClone(defaultPageField)
  const suffixId = getPageFieldUniqueSuffixId()
  pageField.id = `${fieldId}-${suffixId}`
  pageField.fieldId = fieldId
  pageField.fileId = fileId
  pageField.pageNo = pageNo
  pageField.coordX = coordX
  pageField.coordY = coordY
  if (field.type === FieldTypeDate) {
    pageField.dateFormat = 'yyyy年MM月dd日'
    pageField.width = 158
    pageField.height = 24
  }
  if (field.type === FieldTypePerson || field.type === FieldTypeCompany) {
    pageField.width = 158
    pageField.height = 158
  }
  if (field.name.includes('证件号码')) {
    pageField.width = 18 * pageField.fontSize
  }

  if (field.name.includes('手机号码')) {
    pageField.width = 11 * pageField.fontSize
  }

  const rWidth = calcTextWidth(field.name, pageField.fontSize)
  if (pageField.width < rWidth) {
    pageField.width = rWidth
  }

  return pageField
}

export default makePageField
