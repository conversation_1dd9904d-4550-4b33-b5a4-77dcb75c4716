import makeFieldId from './makeFieldId'
import deepClone from '../../../helpers/deepClone'

const selectSignStep = (field, pageField, signStep) => {
  const targetFieldId = makeFieldId(field.name, signStep.id)

  //将pageField移入
  pageField.fieldId = targetFieldId

  const existedTargetField = this.fields.find(item => item.id === targetFieldId)

  if (!existedTargetField) {
    const targetField = deepClone(field)
    targetField.signStepName = signStep.name
    targetField.signerType = signStep.signerType
    targetField.signStepId = signStep.id
    targetField.id = targetFieldId
    this.fields.push(targetField)
  }

  //移入后，原来的field下pageField数量为0，则需要删除原有的field
  const pageFieldsRemainCount = this.pageFields.filter(
    item => item.fieldId === field.id
  ).length
  if (pageFieldsRemainCount === 0) {
    const index = this.fields.findIndex(item => item.id === field.id)
    this.fields.splice(index, 1)
  }

  console.log('merged fields', this.fields)
  console.log('merged pageFields', this.pageFields)
}

export default selectSignStep
