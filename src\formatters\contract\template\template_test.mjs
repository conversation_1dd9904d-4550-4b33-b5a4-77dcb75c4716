import makeDetail2Fields from './makeDetail2Fields'
import makeDetail2PageFields from './makeDetail2PageFields'
import fs from 'fs'
import makeDetail2FieldsNormal from './testData/makeDetail2Fields/normal'
import makeDetail2FieldsNormalOut from './testData/makeDetail2Fields/normalOut'
import makeDetail2PageFieldsNormal from './testData/makeDetail2PageFields/normal'
import makeDetail2PageFieldsNormalOut from './testData/makeDetail2PageFields/normalOut'

const isNeedUpdate = process.argv.includes('--update')
console.log('--update', isNeedUpdate)

const assertEqual = (message, input, out) => {
  if (JSON.stringify(input) !== JSON.stringify(out)) {
    console.log(`❌${message} had error`)
    return
  }
  console.log(`✅${message} passed.`)
}

var out = makeDetail2Fields(makeDetail2FieldsNormal)
if (isNeedUpdate) {
  const data = JSON.stringify(out, null, 2)
  fs.writeFileSync(
    'testdata/makeDetail2Fields/normalOut.js',
    `export default ` + data
  )
}
assertEqual('makeDetail2Fields normal', out, makeDetail2FieldsNormalOut)

out = makeDetail2PageFields(makeDetail2PageFieldsNormal)
out.map(item => delete item.id)
if (isNeedUpdate) {
  const data = JSON.stringify(out, null, 2)
  fs.writeFileSync(
    'testdata/makeDetail2PageFields/normalOut.js',
    `export default ` + data
  )
}
assertEqual('makeDetail2PageFields normal', out, makeDetail2PageFieldsNormalOut)