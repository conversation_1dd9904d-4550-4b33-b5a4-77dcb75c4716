export default {
  templateId: 2,
  fieldGroupList: [
    {
      name: '合同基础信息',
      remark: '',
      fieldList: [
        {
          name: '合同编号',
          relationCode: 'CONTRACT_NO',
          relationSource: '1'
        },
        {
          name: '合同开始日期',
          relationCode: 'CONTRACT_START_DATE',
          relationSource: '1'
        },
        {
          name: '合同结束日期',
          relationCode: 'CONTRACT_END_DATE',
          relationSource: '1'
        }
      ]
    },
    {
      name: '合同公司信息',
      remark: '',
      fieldList: [
        {
          name: '合同主体名称',
          relationCode: 'COMPANY_NAME',
          relationSource: '1'
        },
        {
          name: '公司地址',
          relationCode: 'COMPANY_ADDRESS',
          relationSource: '1'
        },
        {
          name: '法人姓名',
          relationCode: 'COMPANY_LEGAL_PERSON',
          relationSource: '1'
        }
      ]
    },
    {
      name: '个人信息',
      remark: '',
      fieldList: [
        {
          name: '姓名',
          relationCode: 'PERSON_NAME',
          relationSource: '1'
        },
        {
          name: '手机号码',
          relationCode: 'PERSON_MOBILE',
          relationSource: '1'
        },
        {
          name: '证件类型',
          relationCode: 'PERSON_IDENTIFY_TYPE',
          relationSource: '1'
        },
        {
          name: '证件号码',
          relationCode: 'PERSON_IDENTIFY_NO',
          relationSource: '1'
        },
        {
          name: '部门',
          relationCode: 'PERSON_DEPARTMENT',
          relationSource: '1'
        },
        {
          name: '岗位',
          relationCode: 'PERSON_POST',
          relationSource: '1'
        }
      ]
    },
    {
      name: '关联原合同字段',
      remark: '适用于签订解约、续约、变更协议',
      fieldList: [
        {
          name: '原合同名称(含唯一文件标识)',
          relationCode: 'OLD_CONTRACT_NAME',
          relationSource: '1'
        },
        {
          name: '原合同签署日期',
          relationCode: 'OLD_CONTRACT_SIGN_DATE',
          relationSource: '1'
        }
      ]
    }
  ],
  customFieldList: [
    {
      name: '原合同名称(含唯一文件标识)',
      relationCode: 'OLD_CONTRACT_NAME',
      relationSource: '1'
    },
    {
      name: '原合同签署日期',
      relationCode: 'OLD_CONTRACT_SIGN_DATE',
      relationSource: '1'
    }
  ],
  fileList: [
    {
      fileId: 2,
      name: '合同模板测试1.pdf',
      archiveId: '825a8aa1c4ac41588e815b61f64501a9',
      size: 161292,
      archiveImageList: ['/p1.jpg', '/p2.jpg', '/p1.jpg'],
      controlGroupList: [
        {
          signStepId: '',
          signStepName: '',
          signerType: '',
          type: '4',
          name: '合同编号',
          relationCode: 'CONTRACT_NO',
          relationSource: '1',
          modifiable: false,
          writeable: false,
          writeType: '1',
          writeSort: 0,
          writeRequired: false,
          value: '',
          isCustomControl: false,
          controlList: [
            {
              fileId: '',
              pageNo: 1,
              coordX: 250,
              coordY: 262.0625,
              width: 170,
              height: 28,
              font: '',
              fontSize: 14,
              textAlign: '1',
              dateFormat: '',
              pageNumber: 1,
              __id: 0.5367777547805541
            }
          ],
          mouseX: 59,
          mouseY: 12.9375
        },
        {
          signStepId: '',
          signStepName: '',
          signerType: '',
          type: '4',
          name: '合同开始日期',
          relationCode: 'CONTRACT_START_DATE',
          relationSource: '1',
          modifiable: false,
          writeable: false,
          writeType: '1',
          writeSort: 0,
          writeRequired: false,
          value: '',
          isCustomControl: false,
          controlList: [
            {
              pageNo: 1,
              coordX: 249,
              coordY: 349.0625,
              width: 170,
              height: 28,
              font: '',
              fontSize: 14,
              textAlign: '1',
              dateFormat: '',
              pageNumber: 1,
              __id: 0.6581509737751146
            }
          ],
          mouseX: 37,
          mouseY: 16.9375
        },
        {
          signStepId: '',
          signStepName: '',
          signerType: '1',
          type: '4',
          name: '原合同名称(含唯一文件标识)',
          relationCode: 'OLD_CONTRACT_NAME',
          relationSource: '1',
          modifiable: false,
          writeable: false,
          writeType: '1',
          writeSort: 0,
          writeRequired: false,
          value: '4312',
          isCustomControl: false,
          controlList: [
            {
              pageNo: 1,
              coordX: 202,
              coordY: 97.0625,
              width: 170,
              height: 28,
              font: '',
              fontSize: 14,
              textAlign: '1',
              dateFormat: '1234',
              pageNumber: 1,
              __id: 0.10235044448631125
            }
          ],
          mouseX: 59,
          mouseY: 16.9375
        }
      ]
    },
    {
      fileId: 3,
      name: '合同模板测试2.pdf',
      archiveId: '825a8aa1c4ac41588e815b61f64501a9',
      size: 161292,
      archiveImageList: ['/p2.jpg', '/p1.jpg'],
      controlGroupList: null
    }
  ]
}