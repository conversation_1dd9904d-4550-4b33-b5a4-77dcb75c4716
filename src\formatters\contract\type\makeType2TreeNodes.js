export const makeType2TreeNodes = groups => {
  var nodes = []
  for (var group of groups) {
    var node = {
      id: group.id,
      type: 'group',
      label: group.name,
      enable: true, //分组都是可用的，类型有的可用有的停用
      // enable: group.enable ? true : false,
      children: []
    }
    nodes.push(node)
    for (var type of group.nodes) {
      node.children.push({
        id: type.id,
        groupId: group.id,
        type: 'type',
        label: type.name,
        enable: type.enable ? true : false
      })
    }
  }
  const all = {
    label: '全部类型',
    id: 'all',
    enable: true,
    children: nodes
  }

  return [all]
}