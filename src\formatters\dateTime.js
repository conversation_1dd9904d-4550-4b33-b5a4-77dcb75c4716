//options:[string|Options]
//Options: 见https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat
//string 时候为format格式 仅支持一种 yyyy MM dd HH mm ss 与后端java保持一致
//args 参见 https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/Date#syntax
const formatDateTime = (options, ...args) => {
  if (Object.prototype.toString.call(options) === '[object String]') {
    options = {
      format: options
    }
  }
  var d = new Date()
  if (Object.prototype.toString.call(args[0]) === '[object Date]') {
    d = args[0]
  } else {
    d = new Date(...args)
  }

  if (
    Object.prototype.toString.call(d) !== '[object Date]' ||
    d.toString() === 'Invalid Date'
  ) {
    throw new Error(
      'arguments error,please check ' +
        'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/Date#syntax'
    )
  }
  const format = options.format ? options.format : 'yyyy-MM-dd HH:mm:ss'
  delete options.format

  options.year = 'numeric'
  options.month = '2-digit'
  options.day = '2-digit'
  options.hour = '2-digit'
  options.minute = '2-digit'
  options.second = '2-digit'
  options.hour12 = false

  const t = d.toLocaleTimeString('zh-CN', options)
  const dateTime = t.split(' ')
  const date = dateTime[0].split('/')
  const time = dateTime[1].split(':')
  var r = format
  if (format.includes('yyyy')) {
    r = r.replace('yyyy', date[0])
  }
  if (format.includes('MM')) {
    r = r.replace('MM', date[1])
  }
  if (format.includes('dd')) {
    r = r.replace('dd', date[2])
  }
  if (format.includes('HH')) {
    r = r.replace('HH', time[0])
  }
  if (format.includes('mm')) {
    r = r.replace('mm', time[1])
  }
  if (format.includes('ss')) {
    r = r.replace('ss', time[2])
  }

  return r
}

export default formatDateTime
