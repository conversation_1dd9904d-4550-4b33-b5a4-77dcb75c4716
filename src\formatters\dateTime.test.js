import formatDateTime from './dateTime'
const inputs = [
  {
    format: 'yyyy:MM:dd HH:mm:ss',
    datetime: new Date('2022-01-01 10:01:02')
  },
  {
    format: 'yyyy:',
    datetime: '2022-01-01'
  },
  {
    format: 'yyyy-MM-dd HH:mm:ss',
    datetime: '2022/01/01 10:01:02'
  },
  {
    format: 'yyyy:MM:dd HH:mm:ss',
    datetime: '2022/01/01 10:01:02'
  },
  {
    format: 'yyyy:MM:dd HH:mm',
    datetime: '2022/01/01 10:01:02'
  },
  {
    format: 'yyyy:MM:dd HH:mm:ss',
    datetime: 1641002462000
  },
  {
    format: 'yyyy:MM:dd HH:mm:ss',
    datetime: [2022, 0, 1, 10, 1, 2]
  }
]
const expects = [
  '2022:01:01 10:01:02',
  '2022:',
  '2022-01-01 10:01:02',
  '2022:01:01 10:01:02',
  '2022:01:01 10:01',
  '2022:01:01 10:01:02',
  '2022:01:01 10:01:02'
]

describe('formatDateTime', () => {
  it('should format datetime', () => {
    inputs.forEach((input, index) => {
      var r = null
      if (Object.prototype.toString.call(input.datetime) === '[object Array]') {
        r = formatDateTime(input.format, ...input.datetime)
      } else {
        r = formatDateTime(input.format, input.datetime)
      }

      expect(r).toBe(expects[index])
    })
  })
})

// for (var index in inputs) {
//   const input = inputs[index]
//   var r = null
//   if (Object.prototype.toString.call(input.datetime) === '[object Array]') {
//     r = formatDateTime(input.format, ...input.datetime)
//   } else {
//     r = formatDateTime(input.format, input.datetime)
//   }

//   if (r !== expects[index]) {
//     console.error(`${input.format} expect ${expects[index]} got ${r}`)
//   }
// }
