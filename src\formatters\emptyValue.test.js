import { formatEmptyValue } from './emptyValue'

describe('secondsToMs', () => {
  test('formatEmptyValue should return "-" for empty string', () => {
    expect(formatEmptyValue('')).toBe('-')
  })

  test('formatEmptyValue should return "-" for undefined', () => {
    expect(formatEmptyValue(undefined)).toBe('-')
  })

  test('formatEmptyValue should return "-" for null', () => {
    expect(formatEmptyValue(null)).toBe('-')
  })

  test('formatEmptyValue should return the original value for non-empty string', () => {
    const value = 'Hello'
    expect(formatEmptyValue(value)).toBe(value)
  })

  test('formatEmptyValue should return the original value for other non-empty values', () => {
    const value = 42
    expect(formatEmptyValue(value)).toBe(value)

    const obj = { name: '<PERSON>' }
    expect(formatEmptyValue(obj)).toBe(obj)
  })
})
