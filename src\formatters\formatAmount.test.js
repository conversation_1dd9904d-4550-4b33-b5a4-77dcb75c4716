import formatAmount from './formatAmount'

describe('formatAmount', () => {
  test('should format integer amount without decimal places', () => {
    const result = formatAmount(1000)
    expect(result).toEqual('1,000.00')
  })

  test('should format amount with decimal places', () => {
    const result = formatAmount(1234.56)
    expect(result).toEqual('1,234.56')
  })

  test('should format negative amount', () => {
    const result = formatAmount(-5000.12)
    expect(result).toEqual('-5,000.12')
  })

  test('should format large integer amount', () => {
    const result = formatAmount(1234567890)
    expect(result).toEqual('1,234,567,890.00')
  })

  test('should format amount with only decimal places', () => {
    const result = formatAmount(0.12345)
    expect(result).toEqual('0.12')
  })
})
