const getEmployees = department => {
  var users = []
  if (department.userList) {
    users = users.concat(department.userList)
    for (var c of users) {
      var n = [...department.parentDepartments]
      const currentDepartment = {
        id: department.id,
        name: department.name
      }
      c.department = currentDepartment
      n.push(currentDepartment)

      c.departments = n
    }
  }
  if (department.children && department.children.length) {
    for (var c of department.children) {
      const childUsers = getEmployees(c)
      users = users.concat(childUsers)
    }
  }

  return users
}

const formatDepartmentsEmployees = rootDepartment => {
  if (!rootDepartment.parentDepartments) {
    throw new Error('only formatted rootDepartment accepted')
  }

  const users = getEmployees(rootDepartment)
  var m = {}
  var r = []
  for (var c of users) {
    if (m[c.id]) {
      continue
    }
    r.push(c)
    m[c.id] = true
  }

  return r
}

export default formatDepartmentsEmployees
