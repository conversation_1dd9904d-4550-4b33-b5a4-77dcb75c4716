import formatDepartmentsEmployees from './formatDepartmentsEmployees'
import formatRootDepartment from './formatRootDepartment'

describe('formatDepartmentsEmployees', () => {
  test('formatDepartmentsEmployees', () => {
    var rootDepartment = {
      id: 1,
      name: '部门1',
      userList: [
        {
          id: 'u001',
          name: '001'
        },
        {
          id: 'u002',
          name: '002'
        },
        {
          id: 'u0021',
          name: '0021'
        }
      ],
      children: [
        {
          id: 11,
          name: '部门11',
          userList: [
            {
              id: 'u0011',
              name: '0011'
            },
            {
              id: 'u0021',
              name: '0021'
            }
          ],
          children: [
            {
              id: 111,
              name: '部门111',
              userList: [
                {
                  id: 'u00111',
                  name: '00111'
                },
                {
                  id: 'u001',
                  name: '001'
                },
                {
                  id: 'u00211',
                  name: '00211'
                }
              ]
            }
          ]
        }
      ]
    }
    formatRootDepartment(rootDepartment)
    const result = formatDepartmentsEmployees(rootDepartment)

    // console.log(JSON.stringify(result))

    expect(result).toEqual([
      {
        id: 'u001',
        name: '001',
        department: { id: 1, name: '部门1' },
        departments: [{ id: 1, name: '部门1' }]
      },
      {
        id: 'u002',
        name: '002',
        department: { id: 1, name: '部门1' },
        departments: [{ id: 1, name: '部门1' }]
      },
      {
        id: 'u0021',
        name: '0021',
        department: { id: 1, name: '部门1' },
        departments: [{ id: 1, name: '部门1' }]
      },
      {
        id: 'u0011',
        name: '0011',
        department: { id: 11, name: '部门11' },
        departments: [
          { id: 1, name: '部门1' },
          { id: 11, name: '部门11' }
        ]
      },
      {
        id: 'u00111',
        name: '00111',
        department: { id: 111, name: '部门111' },
        departments: [
          { id: 1, name: '部门1' },
          { id: 11, name: '部门11' },
          { id: 111, name: '部门111' }
        ]
      },
      {
        id: 'u00211',
        name: '00211',
        department: { id: 111, name: '部门111' },
        departments: [
          { id: 1, name: '部门1' },
          { id: 11, name: '部门11' },
          { id: 111, name: '部门111' }
        ]
      }
    ])
  })
})
