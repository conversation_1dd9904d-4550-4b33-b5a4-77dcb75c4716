const formatDepartmentsToStringWithBackslash = departments => {
  if(!departments || !departments.length){
    return ''
  }
  var r = []
  if (departments && departments.length <= 2) {
    for (var c of departments) {
      r.push(c.name)
    }
    return r.join('/')
  }

  if (departments && departments.length > 2) {
    const l = departments.length
    r.push('...')
    const ar = departments.slice(l - 2)
    for (var c of ar) {
      r.push(c.name)
    }
  }

  return r.join('/')
}

export default formatDepartmentsToStringWithBackslash
