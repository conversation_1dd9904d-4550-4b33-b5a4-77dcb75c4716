import formatDepartmentsToStringWithBackslash from './formatDepartmentsToStringWithBackslash'

describe('formatDepartmentsToStringWithBackslash', () => {
  it('formatDepartmentsToStringWithBackslash', () => {
    const result = formatDepartmentsToStringWithBackslash([
      { name: '1' },
      { name: '2' }
    ])
    expect(result).toEqual('1/2')

    const result2 = formatDepartmentsToStringWithBackslash([
      { name: '1' },
      { name: '2' },
      { name: '3' },
      { name: '4' }
    ])

    expect(result2).toEqual('.../3/4')
  })
})
