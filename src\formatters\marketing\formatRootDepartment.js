const fillParentDepartments = (department, parentDepartments) => {
  department.parentDepartments = parentDepartments
  department.employeeTotal = department.userCount || 0
  delete department.userCount
  if (!department.children) return
  for (var c of department.children) {
    fillParentDepartments(
      c,
      parentDepartments.concat([
        {
          id: department.id,
          name: department.name
        }
      ])
    )
  }
}
//需要增补每一级的parentDepartments
const formatRootDepartment = rootDepartment => {
  rootDepartment.employeeTotal = rootDepartment?.userCount || 0
  delete rootDepartment.userCount
  rootDepartment.parentDepartments = []
  for (var c of rootDepartment.children) {
    fillParentDepartments(c, [
      {
        id: rootDepartment.id,
        name: rootDepartment.name
      }
    ])
  }

  return rootDepartment
}
export default formatRootDepartment
