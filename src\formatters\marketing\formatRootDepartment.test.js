import formatRootDepartment from './formatRootDepartment'

describe('formatRootDepartment', () => {
  test('formatRootDepartment', () => {
    const result = formatRootDepartment({
      id: 1,
      name: '部门1',
      userCount: 10,
      children: [
        {
          id: 11,
          name: '部门11',
          children: [
            {
              id: 111,
              name: '部门111',
              children: [
                {
                  id: 1111,
                  name: '部门1111'
                }
              ]
            }
          ]
        }
      ]
    })
    console.log(result)
    expect(result.parentDepartments).toEqual([])
    expect(result.employeeTotal).toEqual(10)
    expect(result.children[0].parentDepartments).toEqual([
      {
        id: 1,
        name: '部门1'
      }
    ])

    expect(result.children[0].children[0].parentDepartments).toEqual([
      {
        id: 1,
        name: '部门1'
      },
      {
        id: 11,
        name: '部门11'
      }
    ])
  })
})
