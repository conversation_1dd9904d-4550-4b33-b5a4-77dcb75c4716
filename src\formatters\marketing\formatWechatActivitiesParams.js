import deepClone from 'kit/helpers/deepClone'

import {
  ACTIVITY_PROMOTION_OFFLINE,
  ACTIVITY_PROBABILITY_TYPE,
  ACTIVITY_AVERAGE_TYPE,
  ACTIVITY_SHARE_TYPE,
  NUMBER_BOMB,
  COUPON
} from 'kit/pages/marketing/admin/constants.js'

// 将小时、分钟、秒数转换为秒
function convertToSeconds(hours, minutes, seconds) {
  const totalSeconds = hours * 3600 + minutes * 60 + seconds * 1
  return totalSeconds
}

// 秒转小时
function convertToTime(seconds) {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60
  return {
    hours: hours,
    minutes: minutes,
    seconds: remainingSeconds
  }
}

export const formatterCheckCouponsRequestParams = params => {
  const newParams = deepClone(params)

  const includesKey = [
    'collectUserItemList',
    'collectUser',
    'checkQualification',
    'groupList'
  ]

  Object.keys(newParams).forEach(key => {
    if (!includesKey.includes(key)) delete newParams[key]
  })

  return newParams
}

export const formatterCreateActivityRequestParams = params => {
  const newParams = deepClone(params)

  newParams.groupList.forEach(item => {
    item.itemList.forEach(({ sendRule }) => {
      const { hours, minutes, seconds } = sendRule
      sendRule.interval = convertToSeconds(hours, minutes, seconds)
    })
  })

  newParams?.quotaList?.forEach(item => {
    delete item.id
    item.amount = Number(item.amount)
  })

  if (!newParams.collectUser) {
    newParams.collectUserItemList = []
    newParams.checkQualification = false
  }

  newParams.groupList.forEach((item, index) => {
    delete item.id
    item.groupId = index
  })

  if (newParams.getWay !== COUPON) {
    newParams.repeatGet = true
  }

  if (newParams.getWay !== NUMBER_BOMB) {
    delete newParams.scoreRule
  } else {
    newParams.scoreRule.forEach(item => {
      item.groupId = Number(item.groupItemIndex)
      if (item.max !== null) {
        item.max = Number(item.max)
      }
      item.min = Number(item.min)
      delete item.groupItemIndex
      delete item.id
    })
  }

  if (newParams.awardShowList) {
    newParams.awardShowList.forEach((item, index) => {
      delete item.index
      const position = index + 1
      item.position = position
    })
  }

  // 等于按份数中奖得时候 设置 "匹配奖品为空时自动切换为其他商品" 为false状态
  if (newParams.winningRateType === ACTIVITY_SHARE_TYPE) {
    newParams.awardAdjustEnabled = false
  }

  const { winningRateRules, winningRateRules2 } = newParams

  ;[...newParams.winningRateRules, ...newParams.winningRateRules2].forEach(
    item => {
      delete item.groupItem
    }
  )

  newParams.winningRateRules.forEach((item, index) => {
    item.awardSeq = index + 1
    if (item.name === '无奖品组') {
      item.awardSeq = -1
    }
  })

  newParams.winningRateRules2.forEach((item, index) => {
    item.awardSeq = index + 1
    if (item.name === '无奖品组') {
      item.awardSeq = -1
    }
  })

  if (newParams.activityId) return newParams

  // 如果不是线下推广删除无用字段
  if (!newParams.promoteTypes.includes(ACTIVITY_PROMOTION_OFFLINE)) {
    delete newParams.quotaList
  }

  delete newParams.winningRateRules
  delete newParams.winningRateRules2

  if (newParams.winningRateType === ACTIVITY_SHARE_TYPE) {
    newParams.winningRateRules = winningRateRules
  }

  if (newParams.winningRateType === ACTIVITY_PROBABILITY_TYPE) {
    newParams.winningRateRules = winningRateRules2
  }

  return newParams
}

export const formatterActivityDetailResponseParams = params => {
  const newParams = deepClone(params)

  newParams.groupList.forEach(item => {
    item.itemList.forEach(({ sendRule }) => {
      const { interval } = sendRule
      const { hours, minutes, seconds } = convertToTime(interval)
      sendRule.hours = hours
      sendRule.minutes = minutes
      sendRule.seconds = seconds
    })
  })

  newParams.collectUserItemList = newParams.collectUserItemList || []
  newParams.collectUser = Boolean(newParams.collectUserItemList.length)

  newParams.quotaList = newParams.quotaList || []
  newParams.winningRateRules = newParams.winningRateRules || []
  newParams.quotaList?.forEach(item => {
    item.promoterId = item.promoter.id
    item.promoterName = item.promoter.name
  })

  if (Array.isArray(newParams.scoreRule)) {
    newParams.scoreRule.forEach(item => {
      item.groupItemIndex = item.groupId
    })
  }

  newParams.quotaList = newParams.quotaList || []

  newParams.winningRateRules2 = newParams.winningRateRules || []

  return newParams
}

export const formatterActivityRulesFormData = params => {
  const newParams = deepClone(params)
  const includesKey = [
    'ruleInfo',
    'bannerImageUrl',
    'repeatGet',
    'getLimit',
    'groupList',
    'scoreRule',
    'winningRateType',
    'winningRateRules',
    'winningRateRules2',
    'awardShowList',
    'awardAdjustEnabled',
    'noneAwardEnabled'
  ]
  Object.keys(newParams).forEach(key => {
    if (!includesKey.includes(key)) delete newParams[key]
  })

  return newParams
}
