import Money from '../models/money'
const formatMoney = money => {
  if (!(money instanceof Money)) {
    throw new Error('money is not instance of Money')
  }

  const number = parseInt(money.amount / 100)

  var country = 'zh-CN'
  if (money.currency === 'USD') {
    country = 'en-US'
  }

  const intl = new Intl.NumberFormat(country, {
    currency: money.currency,
    style: 'currency'
  })

  return intl.format(number)
}

export default formatMoney
