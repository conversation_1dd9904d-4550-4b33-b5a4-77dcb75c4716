import formatMoney from './money'
import Money from '../models/money'

describe('formatMoney', () => {
  it('should format money', () => {
    const inputs = [
      {
        money: new Money(100, 'CNY')
      },
      {
        money: new Money(100, 'USD')
      }
    ]
    const expects = ['¥1.00', '$1.00']

    inputs.forEach((input, index) => {
      const r = formatMoney(input.money)
      expect(r).toBe(expects[index])
    })
  })
})
