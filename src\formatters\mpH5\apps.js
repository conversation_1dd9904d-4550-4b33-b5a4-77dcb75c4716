const formatApps = apps => {
    console.log({apps})
  var r = []
  for (var app of apps) {
    if (app.name === '其他' || app.name === '金融专区') {
      continue
    }
    if (app.code === "EMPLOYEE") {
        app.entryVos = app.entryVos || []
        app.entryVos.push({
            "code": "INVOICES",
            "name": "我的发票",
            "icon": "invoice.png",
            "path": "/pages/salary/main",
            "order": 1,
            "type": "MP"
        })
    }
    if (app.entryVos && app.entryVos.length) {
      var entries = []
      for (var entry of app.entryVos) {
        // if (entry.name === '合同协议') {
        //   continue
        // }
        entries.push(entry)
      }

      app.entryVos = entries
    }

    r.push(app)
  }

  return r
}

export default formatApps
