const attendDailyListToDayColor = list => {
  var m = {}
  for (var c of list) {
    if (c.dateType === 'WORKDAY' && c.status === 'OUTSIDE_ATTEND') {
      m[c.attendDate] = 'green'
    } else if (
      c.dateType === 'WORKDAY' &&
      (c.status === 'BE_LATE' ||
        c.status === 'LEAVE_EARLIER' ||
        c.status === 'ABSENT_WORK')
    ) {
      m[c.attendDate] = 'red'
    } else if (c.dateType === 'WORKDAY' && c.status === 'NORMAL') {
      m[c.attendDate] = 'blue'
    } else if (c.dateType === 'WORKDAY' && c.status === 'SUPPLY_PASS') {
      m[c.attendDate] = 'blue'
    }
  }

  return m
}

export default attendDailyListToDayColor
