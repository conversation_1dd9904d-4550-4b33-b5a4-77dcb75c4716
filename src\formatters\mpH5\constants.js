export const ContractStatusWaitingSign = 'WAIT_SIGN' // 待签署
export const ContractStatusSignCompleted = 'COMPLETE' // 已完成
export const ContractStatusSigning = 'SIGN_ING' // 签署中
export const ContractStatusWaitAudit = 'WAIT_AUDIT' // 待审核
export const ContractStatusAuditing = 'AUDIT_ING' // 审核中
export const ContractStatusReturnedAbort = 'ABORT' // 被退回
export const ContractStatusReturnedCancel = 'CANCEL' // 被退回
export const ContractStatusTermination = 'TERMINATION' // 已终止
export const ContractStatusExpired = 'EXPIRED' // 已终止

export const nodeType = {
  "SIGN": "签",
  "SEAL": "公",
  "AUDIT": "审",
  "CARBON_COPY": "抄"
}

export const templateType = {
  "": '全部',
  "CONTRACT_PROTOCOL": "合同协议",
  "PROVE": "证明",
  "RULE_SYSTEM": "规章制度",
  "OTHERS": "其他"
}

export const staffTemplateType = {
  "LABOUR_CONTRACT": "劳动合同",
  "PROVE": "证明",
  "RULES": "规章制度",
  "OTHERS": "其他"
}

// 员工合同 人事下面的
export const elContractStatus = {
  "SIGNING": "签署中",
  "WAIT_SIGN":"待签署",
  "COMPLETE": "已完成"
}

export const expertConsultQuestionStatus = {
  PENDING_REPLY: "待回复",
  ALREADY_REPLY: "已回复",
  ALREADY_ASK: "已提问"
}

export const approvalTodo ={
  appCode: 'olading-sheet-service',
  appTaskCode: 'AUTOAPPROVAL_TODO'
}

export const contractTodo ={
  appCode: 'olading-hrsaas-employee',
  appTaskCode: 'WAIT_SIGNING'
}