import {
  ContractStatusWaitingSign,
  ContractStatusSignCompleted,
  ContractStatusSigning,
  ContractStatusWaitAudit,
  ContractStatusAuditing,
  ContractStatusReturnedAbort,
  ContractStatusReturnedCancel,
  ContractStatusTermination,
  ContractStatusExpired
} from './constants'

export const contractStatus2string = status => {
  if (status === ContractStatusWaitingSign) {
    return '待签署'
  }

  if (status === ContractStatusSignCompleted) {
    return '已完成'
  }

  if (status === ContractStatusSigning) {
    return '签署中'
  }

  if (status === ContractStatusWaitAudit) {
    return '待审核'
  }

  if (status === ContractStatusAuditing) {
    return '审核中'
  }

  if (status === ContractStatusReturnedAbort) {
    return '被退回'
  }

  if (status === ContractStatusReturnedCancel) {
    return '被退回'
  }

  if (status === ContractStatusTermination) {
    return '已终止'
  }

  if (status === ContractStatusExpired) {
    return '已终止'
  }

  return status
}
