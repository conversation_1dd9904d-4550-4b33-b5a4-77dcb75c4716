export const signResult = {
  NORMAL: '正常',
  BE_LATE: '迟到',
  LEAVE_EARLIER: '早退',
  ABSENT_WORK: '缺卡',
  HOOKY_WORK: '旷工',
  OUTSIDE_ATTEND: '外勤',
  OUTSIDE_WORK: '外出',
  BUSINESS_TRIP: '出差',
  OVER_TIME: '加班',
  LEAVE: '请假',
  RESTDAY: '休息日',
  SUPPLY_PASS: '补卡通过',
  SHOULD_DAYS: '应出勤天数',
  FACT_DAYS: '工作日实际出勤天数',
  REST_DAYS: '休息日出勤天数',
  CARD: '补卡申请'
}

const signResultToString = typ => {
  return signResult[typ] ? signResult[typ] : ''
}

export default signResultToString
