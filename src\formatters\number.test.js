import formatNumber from './number'

describe('formatNumber', () => {
  it('should format number', () => {
    expect(formatNumber(123456.789)).toBe('123,456.789')
  })

  it('should format number with options', () => {
    expect(formatNumber(123456.789, { maximumFractionDigits: 2 })).toBe(
      '123,456.79'
    )
  })

  it('should throw error when input is not number', () => {
    expect(() => formatNumber('abc')).toThrowError('input is not number')
  })

  it('should throw error when input is NaN', () => {
    expect(() => formatNumber(NaN)).toThrowError('input is not number')
  })
})
