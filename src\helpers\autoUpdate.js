var lastUpdateCheckedTime = 0
const getAppVersionFromHtml = html => {
  const parser = new DOMParser()
  const d = parser.parseFromString(html, 'text/html')
  return d.querySelector('meta[name="app-version"]').content
}
const _isNeedUpdate = () => {
  const now = new Date().getTime()

  if (!lastUpdateCheckedTime) {
    lastUpdateCheckedTime = now
    return false
  }

  //存在稍后更新，则10分钟后才再次提示
  var lastUpdateTipTime = localStorage.getItem(
    '__contract_pc_lastUpdateTipTime'
  )
  if (lastUpdateTipTime) {
    lastUpdateTipTime = parseInt(lastUpdateTipTime, 10)
    const passMinutes = (now - lastUpdateTipTime) / 60000
    // console.log('passed tip passMinutes', passMinutes)
    if (passMinutes < 10) {
      return false
    }
  }

  //7秒内只检查一次
  // console.log('passed time', now - lastUpdateCheckedTime)
  if (now - lastUpdateCheckedTime < 7000) {
    lastUpdateCheckedTime = now
    return false
  }

  lastUpdateCheckedTime = now

  return true
}
//$confirm 是elementUI的确认弹出框，不传则使用window.confirm
const isAppNeedUpdate = async $confirm => {
  if (!_isNeedUpdate()) {
    return false
  }

  const currentAppVersion = document.querySelector(
    'meta[name="app-version"]'
  ).content
  //dev环境下 无版本号信息
  if (!currentAppVersion) {
    return false
  }

  const r = await fetch(window.location.href)
  const html = await r.text()
  const newAppVersion = getAppVersionFromHtml(html)
  //可能没有登陆
  if (!newAppVersion) {
    return false
  }
  console.log('old version', currentAppVersion, 'new version', newAppVersion)
  if (currentAppVersion !== newAppVersion) {
    return true
  }

  return false
}

const autoUpdate = () => {
  //setTimeout是为了不阻断当前执行流
  setTimeout(async () => {
    const isNeedUpdate = await isAppNeedUpdate()
    if (!isNeedUpdate) {
      return
    }

    if (window.env && !window.env.autoUpdateConfirm) {
      if (
        window.confirm(
          '检测到新的程序版本, 请立即更新，不更新可能导致错误发生。'
        )
      ) {
        window.location.reload()
      }
      return
    }

    //从全局配置中走，每个应用可以独立配置
    window.env.autoUpdateConfirm(
      () => window.location.reload(),
      () => {
        localStorage.setItem(
          '__contract_pc_lastUpdateTipTime',
          new Date().getTime()
        )
      }
    )
  }, 5000) //5秒后再弹出，因为有些页面有loading，会争抢弹出背景，故而延时5秒
}

export default autoUpdate
