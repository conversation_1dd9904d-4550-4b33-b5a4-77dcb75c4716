export const QA = 'qa'
export const DEV = 'dev'
export const Testing114 = 'testing-114'
export const Testing156 = 'testing-156'
export const Testing46 = 'testing-46'
export const OladingProduction = 'olading-production'
export const OladingPrestage = 'olading-prestage'
export const CGBProduction = 'cgb-production'
export const CGBTesting = 'cgb-testing'

const allowedEnvs = [
  QA,
  DEV,
  Testing114,
  Testing156,
  Testing46,
  OladingProduction,
  OladingPrestage,
  CGBProduction,
  CGBTesting
]

const api = env => {
  if (env === QA) {
    return ''
  }
  if (env === Testing156) {
    return 'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api'
  }
  if (env === Testing114) {
    return 'https://114-qa.lanmaoly.com/gd/hrsaas/webapi/api'
  }
  if (env === Testing46) {
    return '/gd/hrsaas/webapi/api'
  }
  if (env === OladingProduction) {
    return '/api'
  }
  if (env === OladingPrestage) {
    return '/api'
  }

  throw new Error('apis: not supported env: ' + env)
}
const platformAPI = env => {
  if (env === QA) {
    return 'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/merchant/platform/'
  }
  if (env === Testing114) {
    return 'https://114-qa.lanmaoly.com/gd/hrsaas/webapi/api/merchant/platform/'
  }
  if (env === Testing156) {
    return 'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/merchant/platform/'
  }
  if (env === Testing46) {
    return '/gd/hrsaas/webapi/api/merchant/platform/'
  }
  if (env === OladingProduction) {
    return '/api/merchant/platform/'
  }
  if (env === OladingPrestage) {
    return '/api/merchant/platform/'
  }

  throw new Error('platformAPIs: not supported env: ' + env)
}
const ssoURL = env => {
  if (env === QA) {
    return 'https://156-dev.lanmaoly.com/gd/hrsaas/sso'
  }

  if (env === Testing114) {
    return 'https://114-qa.lanmaoly.com/gd/hrsaas/sso/login'
  }
  if (env === Testing156) {
    return 'https://156-dev.lanmaoly.com/gd/hrsaas/sso'
  }
  if (env === Testing46) {
    return '/gd/hrsaas/sso'
  }
  if (env === OladingProduction) {
    return ''
  }
  if (env === OladingPrestage) {
    return ''
  }

  throw new Error('ssoURL: not supported env: ' + env)
}
const platformStatic = env => {
  if (env === QA) {
    return 'https://156-dev.lanmaoly.com/gd/hrsaas/static/'
  }
  if (env === Testing114) {
    return 'https://114-qa.lanmaoly.com/gd/hrsaas/static/'
  }
  if (env === Testing156) {
    return 'https://156-dev.lanmaoly.com/gd/hrsaas/static/'
  }
  if (env === Testing46) {
    return '/gd/hrsaas/static/'
  }
  if (env === OladingProduction) {
    return '/'
  }
  if (env === OladingPrestage) {
    return '/'
  }

  throw new Error('platformStatics: not supported env: ' + env)
}
const platformTheme = env => {
  if (env.includes('cgb')) {
    return 'red'
  }

  return 'default'
}

const staticPath = env => {
  if (env === QA) {
    return '/'
  }

  if (env === Testing156) {
    return '/gd/hrsaas/h5/'
  }

  if (env === Testing114) {
    return '/gd/hrsaas/h5/'
  }

  if (env === Testing46) {
    return '/gd/hrsaas/h5/'
  }

  if (env === OladingProduction) {
    return '/'
  }

  if (env === OladingPrestage) {
    return '/'
  }

  throw new Error('staticPath: not supported env: ' + env)
}

const theme = env => {
  if (env === Testing156) {
    return 'red'
  }

  return 'default'
}

const staticURL = env => {
  if (env === QA) {
    return 'http://172.19.60.91:10035'
  }
  if (env === Testing156) {
    return 'https://156-dev.lanmaoly.com/gd/hrsaas/static'
  }

  if (env === Testing114) {
    return 'https://114-qa.lanmaoly.com/gd/hrsaas/static'
  }

  if (env === Testing46) {
    return '/gd/hrsaas/static'
  }

  if (env === OladingProduction) {
    return ''
  }

  if (env === OladingPrestage) {
    return ''
  }

  throw new Error('staticURL: not supported env: ' + env)
}

const isObject = obj => {
  return Object.prototype.toString.call(obj) === '[object Object]'
}

const isString = obj => {
  return Object.prototype.toString.call(obj) === '[object String]'
}

export const parseEnvFromQuery = passedConfigs => {
  if (!passedConfigs) {
    return ''
  }

  const isJSONStringReg = /^\{.*\}$/
  if (isJSONStringReg.test(passedConfigs)) {
    try {
      const env = JSON.parse(passedConfigs)
      if (!allowedEnvs.includes(env.current)) {
        throw new Error('passed env.current must in ' + allowedEnvs)
      }
    } catch (e) {
      throw e
    }
  } else if (isString(passedConfigs)) {
    if (!allowedEnvs.includes(passedConfigs)) {
      throw new Error('window.env.current must in ' + allowedEnvs)
    }
    passedConfigs = JSON.stringify({
      current: passedConfigs
    })
  }

  return passedConfigs
}

const params = new URLSearchParams(window.location.search)
var passedConfigs = params.get('env')
if (passedConfigs === 'clear') {
  localStorage.setItem('env', '')
}
if (passedConfigs && passedConfigs !== 'clear') {
  passedConfigs = parseEnvFromQuery(passedConfigs)
  localStorage.setItem('env', passedConfigs)
}

//window.env 转换为指定格式
if (isString(window.env)) {
  window.env = {
    current: window.env
  }
}

if (isObject(window.env) && !allowedEnvs.includes(window.env.current)) {
  throw new Error('window.env.current must in ', allowedEnvs)
}

var currentEnv = window.env.current

//以传入优先级最高
var passedEnv = localStorage.getItem('env')
if (passedEnv) {
  passedEnv = JSON.parse(passedConfigs)
  currentEnv = passedEnv.current
}

const deepMerge = (target, ...sources) => {
  if (!sources.length) return target

  const source = sources.shift()
  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} })
        deepMerge(target[key], source[key])
      } else {
        Object.assign(target, { [key]: source[key] })
      }
    }
  }

  return deepMerge(target, ...sources)
}

const defaultEnv = {
  current: currentEnv,
  //自动更新时候的确认逻辑
  autoUpdateConfirm: null,
  platform: {
    api: platformAPI(currentEnv),
    theme: platformTheme(currentEnv),
    static: platformStatic(currentEnv)
  },
  theme: theme(currentEnv),
  api: api(currentEnv),
  staticPath: staticPath(currentEnv),
  ssoURL: ssoURL(currentEnv),
  staticURL: staticURL(currentEnv)
}

const configs = deepMerge(defaultEnv, window.env, passedEnv)

export default configs
