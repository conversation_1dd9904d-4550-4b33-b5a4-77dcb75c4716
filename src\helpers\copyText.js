export default function copyText(text) {
  const textArea = document.createElement('textarea')
  textArea.value = text

  // Make the textarea out of view
  textArea.style.position = 'fixed'
  textArea.style.top = '0'
  textArea.style.left = '0'
  textArea.style.opacity = '0'

  document.body.appendChild(textArea)

  textArea.focus()
  textArea.select()

  try {
    const successful = document.execCommand('copy')
    const message = successful ? '文本已复制到剪贴板' : '复制失败'
    console.log(message)
  } catch (error) {
    console.error('复制操作出现错误:', error)
  }

  document.body.removeChild(textArea)
}
