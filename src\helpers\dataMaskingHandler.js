import deepClone from "./deepClone.js";
import { handleError } from "./marketingBossToken.js";
import makePlatformClient from '../services/platform/makeClient'
const platformClient = makePlatformClient()

class DesensitizationManager {
  constructor() {
    // 存储脱敏字段和对应的key
    this.desensitizedDataMap = new Map()
  }

  // 添加脱敏数据
  addData(value, key, fieldName) {
    this.desensitizedDataMap.set(value, { value, key, fieldName })
  }

  // // 递归获取所有脱敏字段的值
  // getCollectDesensitizedFields(data) {
  //   const list = []
  //   const traverseObject = (obj) => {
  //     if (Array.isArray(obj)) {
  //       obj.forEach(item => traverseObject(item));
  //     } else if (obj && typeof obj === 'object') {
  //       Object.entries(obj).forEach(([fieldName, value]) => {
  //         if (value && this.isDesensitizationField(value, fieldName)) {
  //           const data = this.desensitizedDataMap.get(value)
  //           list.push({
  //             key: data.key,
  //             value,
  //             fieldName,
  //             parentObj: obj
  //           })
  //         }
  //         traverseObject(value);
  //       });
  //     }
  //   }
  //   traverseObject(data)
  //   return list
  // }

  // // 获取所有脱敏字段的明文
  // async getDesensitizedFieldsPlainText(fields) {
  //   const params = []
  //   fields.forEach(item => {
  //     params.push({
  //       key: item.rvalue,
  //       value: item.value,
  //       fieldName: item.fieldName
  //     })
  //   })

  //   const [err, data] = await platformClient.hrSaasSalarySalaryCommonCipherDecode({
  //     body: {
  //       cipher: params
  //     }
  //   })

  //   if (err) return handleError(err)

  //   const result = data.data

  //   return result.reduce((obj, item) => {
  //     obj[item.rvalue] = item.value;
  //     return obj;
  //   }, {});
  // }

  // 设置脱敏字段为明文文本
  // setDesensitizedFieldsToPlainText(desensitizedFields, plainTexts) {
  //   desensitizedFields.forEach(item => {
  //     if (plainTexts[item.rvalue]) {
  //       item.parentObj[item.fieldName] = plainTexts[item.rvalue]
  //     }
  //   })
  // }
  // 替换脱敏字段为明文
  async replaceDesensitizedFieldsWithPlainText(params) {
    alert("静默解密已下线，请联系管理员")
    return 
    // params = deepClone(params)
    // // 找到所有脱敏字段集合
    // const desensitizedFields = this.getCollectDesensitizedFields(params)
    // if (!desensitizedFields.length) return params
    // // 获取脱敏字段的明文
    // const plainTexts = await this.getDesensitizedFieldsPlainText(desensitizedFields)
    // // 设置原数据 的脱敏字段都为明文
    // this.setDesensitizedFieldsToPlainText(desensitizedFields, plainTexts)
    // return params
  }


  // 是否为脱敏数据
  isDesensitizationField(input, fieldName) {
    const data = this.desensitizedDataMap.get(input)
    if (!fieldName || !data) return Boolean(data)
    return data.fieldName === fieldName
  }

  // 根据脱敏字段生成 新的字段 例如 nameCipher 生成 name , value => nameCipher.value 并把映射插入到map中
  generateSensitiveFieldFromCipher(data) {
    if (Array.isArray(data)) {
      // 如果是数组，则递归处理数组中的每个元素
      return data.map(item => this.generateSensitiveFieldFromCipher(item));
    } else if (typeof data === 'object' && !(data instanceof Blob)) {
      // 如果是对象，则遍历对象的属性
      for (const key in data) {
        if (key.endsWith('Cipher')) {
          // 如果属性名以'Cipher'结尾，则处理该属性
          const newFiled = key.replace('Cipher', '') // 移除'Cipher'
          const cipherData = data[key]
          if (cipherData && cipherData.rvalue) {
            data[newFiled] = cipherData.value
            this.addData(cipherData.value, cipherData.rvalue, newFiled)
          }
        }
        // 递归处理属性值，以处理可能的嵌套结构
        data[key] = this.generateSensitiveFieldFromCipher(data[key])
      }
    }

    return data;
  }
}

export const desensitizationManager = new DesensitizationManager()
console.log(desensitizationManager)


// 使用示例
//   const desensitizationManager = new DesensitizationManager();

//   将数据后端返回的脱敏数据 存放在map中 {"cellPhoneCipher":{value:12***3,key:3333},...}
//   desensitizationManager.generateSensitiveFieldFromCipher({})

//   // 假设初始化时已经添加了脱敏数据
//   desensitizationManager.addData('测*****巧', '脱敏key','字段');

//   // 当前字段是否为脱敏字段 ， 用表单校验 
//   const isDesensitizationField = desensitizationManager.isDesensitizationField('测*****巧', 'empName');
//   if (isDesensitizationField) {}

//   // 处理数据转换 ， 传入一个对象 可以把脱敏字段 自动转为明文 ， 用于提交数据
//   newParams = await desensitizationManager.replaceDesensitizedFieldsWithPlainText(params)
