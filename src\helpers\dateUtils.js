/**
 * @module dateUtils
 * 
 * @description 
 * 和时间处理相关的工具模块
 */

/**
 * 比较d1和d2两个日期的先后，以d1为标准
 */
export class DateCompare {
  /**
   * 如果参数为字符串，格式为可以转化为有效Date的格式，如 2024-04-19 00:00:00。
   * @param {String | Date} date1 
   * @param {String | Date} date2 
   */
  constructor(date1,date2) {
    this.date1 = new Date(date1);
    this.date2 = new Date(date2);
  }

  isBefore() {
    return this.date1 < this.date2;
  }

  isAfter() {
    return this.date1 > this.date2;
  }

  isEqual() {
    return this.date1.getTime() === this.date2.getTime()
  }
}

/**
 * 
 * @param {String} dateStr 以空白符分割的年月日 + ‘ ’ +时分秒 
 */
export function getYMD(dateStr) {
  try {
    return dateStr.split(/\s+/g)[0];
  } catch(e) {
    return ""
  }
}

/**
 * 返回以T连接的IOS 8601格式的日期字符串
 * @param {String} dateStr 以空白符连接日期和时间字符串
 */
export function to8601(dateStr) {
  try {
    return dateStr.replace(/\s+/g, 'T');
  } catch(e) {
    console.log(`${dateStr} is invalid date string`, e);
    return ""
  }
}

export default {
  
}