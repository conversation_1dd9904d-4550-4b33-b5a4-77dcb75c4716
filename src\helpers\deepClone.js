export const deepJSONClone = obj => {
  return JSON.parse(JSON.stringify(obj))
}
export const deepClone = obj => {
  // 如果传入的不是对象或数组，则直接返回原始值
  if (typeof obj !== 'object' || obj === null) {
    return obj
  }

  let copy
  if (Array.isArray(obj)) {
    // 处理数组
    copy = []
    for (let i = 0; i < obj.length; i++) {
      copy[i] = deepClone(obj[i])
    }
  } else {
    // 处理对象
    copy = {}
    for (let key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        copy[key] = deepClone(obj[key])
      }
    }
  }

  return copy
}

export default deepClone
