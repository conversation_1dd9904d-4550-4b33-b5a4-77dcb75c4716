export function downloadImage(url, downloadName = 'image.jpg') {
  const xhr = new XMLHttpRequest()
  xhr.open('GET', url, true)
  xhr.responseType = 'blob'
  xhr.onload = function () {
    if (xhr.status === 200) {
      const a = document.createElement('a')
      a.href = URL.createObjectURL(xhr.response)
      a.download = downloadName
      a.click()
      URL.revokeObjectURL(a.href)
    }
  }
  xhr.send()
}
