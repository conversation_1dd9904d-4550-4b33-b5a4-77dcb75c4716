import { sm2,sm3,sm4 } from 'sm-crypto'

export const randomKKKYYY = () => {
    const b = new Uint8Array(16)
    window.crypto.getRandomValues(b)

    const hex = Array.from(b).map(c => c.toString(16).padStart(2, '0')).join("")
    return hex.substr(0, 128)
}

export const sm2DoEncrypty = (key) => {
    const iv = localStorage.getItem("Hr-Name")
    const sm4Key =  sm3("o"+iv).substr(0,32)
    const encryptPk = localStorage.getItem('Hr-Id')
    const kkkk = sm4.decrypt(encryptPk, sm4Key)

    const r =  '04'+sm2.doEncrypt(key, kkkk, 1)

    return r
}
