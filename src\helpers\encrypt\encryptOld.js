import {randomKKKYYY, sm2DoEncrypty} from './encryptBase'
import { sm4 } from 'sm-crypto'

const randomKKK = randomKKKYYY()

const isObject = (obj) => {
    return Object.prototype.toString.call(obj) === '[object Object]'
}
const isArray = (obj) => {
    return Object.prototype.toString.call(obj) === '[object Array]'
}
const isString = (obj) => {
    return Object.prototype.toString.call(obj) === '[object String]'
}
const isNeedEncrypt = ()=>{
    const opened = localStorage.getItem("Hr-Op")
    if(opened && opened === "1"){
        return true
    }

    return false
}
const isJSON = (obj)=>{
    try{
        JSON.stringify(obj)
        return true
    }catch(err){
        return false
    }
}
export const encryptData =  (data) => {
    if(data && data == "{}"){
        return data
    }
    if(!isNeedEncrypt() && isString(data)){
        return data
    }
    if(!isNeedEncrypt() && isJSON(data)){
        return JSON.stringify(data)
    }
    if(!isNeedEncrypt()){
        return data
    }
    if(isObject(data) && !Object.keys(data).length){
        return JSON.stringify(data)
    }

    if(isObject(data) ||isArray(data)){
        data = JSON.stringify(data)
    }

    return sm4.encrypt(data, randomKKK)
}

const emptyURIValue = (v)=>{
    //适配axios
    if(v === null){
        return true
    }

    if(isArray(v)){
        return v.length === 0
    }


    return false
}
const signURL = (uri,params,headers)=>{
    // if(uri.includes("dProcessingFee/refundFeeInventoryList/export")){
    //     debugger
    // }
    var apiPath = "/hrsaas/webapi"
    if (window.env.apiPath) {
        apiPath = window.env.apiPath
    }
    if (window.env.api) {
        apiPath = window.env.api
    }

    if (apiPath) {
        if(!uri.includes("/api")){
            if(uri[0] ==='/'){
                uri = `${apiPath}/api${uri}`
            }else{
                uri = `${apiPath}/api/${uri}`
            }

        }
    }

    var furi = uri.replace("/api/api","/api")
    furi = furi.replace("/hrsaas/webapi","")
    try{
        const ps = new URLSearchParams()
        if(params){
            for(var key in params){
                const v = params[key]
                if(!emptyURIValue(v)){
                    ps.set(key,v)
                }
            }
        }

        const p = ps.toString()
        if(p){
            if(furi.includes("?")){
                furi = `${furi}&${p}`
            }else{
                furi = `${furi}?${p}`
            }
        }


        headers['Hr-Is'] = btoa(encryptData(furi))
    }catch(err){
        debugger
    }
}


export const fillHeaders = (config = {})=>{
    // if(config.url.includes('ent/queryRoleInfoList')){
    //     debugger
    // }
    var headers = {}
    if(config && config.headers){
        headers = config.headers
    }


    if(!headers) {
        headers = {}
    }
    if(!isNeedEncrypt()){
        return
    }

    signURL(config.url,config.params,headers)

    const iv = localStorage.getItem("Hr-Name")
    headers["Hr-Id"] = btoa(sm2DoEncrypty(randomKKK))
    headers["Hr-Ia"] = btoa(sm2DoEncrypty(randomKKKYYY()))
    headers["Hr-Ic"] = btoa(sm2DoEncrypty(randomKKKYYY()))
    headers["Hr-Ie"] = btoa(sm2DoEncrypty(randomKKKYYY()))
    headers["Hr-Name"] = iv
    headers["Hr-Pub"] = randomKKKYYY()
    headers["Hr-Sub"] = randomKKKYYY()
}


