import handleError from './handleError'

export async function exportExcel(result) {
  try {
    const [err, r] = result
    if (err) {
      handleError(err)
      return
    }
    const tmp = r.headers.get('Content-Disposition').split('filename=')
    const filename = decodeURIComponent(tmp[1])
    const blobData = await r.blob()
    const blob = new Blob([blobData])
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    document.body.appendChild(link)
    link.click()
    link.remove()
  } catch (error) {
    handleError(error)
  }
}
