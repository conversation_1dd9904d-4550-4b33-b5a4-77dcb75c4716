import { isObject, isString } from './index'
import i18ns from './i18ns'
import toLoginPage from './toLoginPage'
import { removeToken } from './token'
import { showMessage } from './showMessage'

const handleError = (err, tokenKey) => {
  if (err && err.errorCode === 2) {
    showMessage(err.message || '您的登录已过期，请重新登录', 'error')
    removeToken(tokenKey)
    setTimeout(() => toLoginPage(), 200)
    return
  }

  //重复请求不报错
  if (err && err.message === 'duplicated request') {
    console.warn(err)
    return
  }

  var msg = err
  if (isObject(err) && isString(err.message)) {
    msg = err.message
  }

  if (i18ns(msg)) {
    msg = i18ns(msg)
  }

  showMessage(msg, 'error')
}

export default handleError
