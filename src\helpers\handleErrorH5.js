import { Toast } from 'vant'
import { isObject, isString } from './index'
import i18ns from './i18ns'
import toLoginPage from './toLoginPage'
import { removeToken } from './token'

Toast.setDefaultOptions({ duration: 2000 })

const handleErrorH5 = err => {
  if (err && err.errorCode === 2) {
    Toast.fail(err.message || '您的登录已过期，请重新登录', {
      forbidClick: true
    })
    removeToken()
    setTimeout(() => toLoginPage(), 2000)
    return
  }

  //重复请求不报错
  if (err && err.message === 'duplicated request') {
    console.warn(err)
    return
  }

  var msg = err
  if (isObject(err) && isString(err.message)) {
    msg = err.message
  }

  if (i18ns(msg)) {
    msg = i18ns(msg)
  }

  Toast.fail(msg, {
    forbidClick: true
  })
}

export default handleErrorH5
