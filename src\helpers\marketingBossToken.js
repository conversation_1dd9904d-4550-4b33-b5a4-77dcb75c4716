import handleErr from './handleError'

const TOKEN_NAME = 'boss-token'

function clearAllCookies() {
  const cookies = document.cookie.split(';')

  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i]
    const eqPos = cookie.indexOf('=')
    const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
  }
}

export function getToken() {
  return localStorage.getItem(TOKEN_NAME)
}

export function setToken(value) {
  return localStorage.setItem(TOKEN_NAME, value)
}

export function removeToken() {
  localStorage.removeItem(TOKEN_NAME)
  clearAllCookies()
}

export function authorizationToken() {
  const token = getToken()
  return {
    headers: {
      Authorization: `Bearer ${token}`
    }
  }
}

export function handleError(err) {
  return handleErr(err, TOKEN_NAME)
}
