import getAllDepartmentsFromRootDepartment from './getAllDepartmentsFromRootDepartment'

//unit test for getAllDepartmentsFromRootDepartment

describe('getAllDepartmentsFromRootDepartment', () => {
  it('getAllDepartmentsFromRootDepartment', () => {
    const result = getAllDepartmentsFromRootDepartment({
      id: 1,
      children: [
        {
          id: 11,
          children: [{ id: 111 }, { id: 112 }]
        },
        {
          id: 12
        }
      ]
    })

    // console.log('result', JSON.stringify(result))

    expect(result).toEqual([
      {
        id: 1,
        children: [{ id: 11, children: [{ id: 111 }, { id: 112 }] }, { id: 12 }]
      },
      { id: 11, children: [{ id: 111 }, { id: 112 }] },
      { id: 111 },
      { id: 112 },
      { id: 12 }
    ])
  })
})
