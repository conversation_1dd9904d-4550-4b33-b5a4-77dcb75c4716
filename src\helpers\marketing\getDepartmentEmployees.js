const getDepartmentEmployees = department => {
  if (!department || !department.userList) {
    return []
  }
  var m = {}
  var r = []
  for (var c of department.userList) {
    if (m[c.id]) {
      continue
    }
    m[c.id] = true
    r.push(c)
  }
  if (!department.children) {
    return r
  }
  for (var c of department.children) {
    const childEmployees = getDepartmentEmployees(c)
    for (var c of childEmployees) {
      if (m[c.id]) {
        continue
      }
      m[c.id] = true
      r.push(c)
    }
  }

  return r
}

export default getDepartmentEmployees
