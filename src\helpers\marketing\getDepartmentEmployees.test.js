import getDepartmentEmployees from './getDepartmentEmployees'

//unit test for getDepartmentEmployees

describe('getDepartmentEmployees', () => {
  it('getDepartmentEmployees', () => {
    const result = getDepartmentEmployees({
      id: 1,
      userList: [{ id: 'u1' }, { id: 'u11' }],
      children: [
        {
          id: 11,
          userList: [{ id: 'u11' }]
        },
        {
          id: 12,
          userList: [{ id: 'u12' }]
        },
        {
          id: 13
        }
      ]
    })
    // console.log(JSON.stringify(result))
    expect(result).toEqual([{ id: 'u1' }, { id: 'u11' }, { id: 'u12' }])
  })
})
