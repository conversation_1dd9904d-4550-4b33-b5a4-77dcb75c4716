const profile = {
  merchant: {
    id: 76,
    name: '广发考勤机114测试',
    introduction: '',
    defaultLegal: null,
    openedBusiness: [
      'HR_EMPLOYEE',
      'EXPENSE_CONTROL',
      'KPI_MANAGEMENT',
      'SALARY_MANAGER',
      'CONTRACT2',
      'CONTRACT_MANAGEMENT',
      'HR',
      'BASIC_FUNCTION',
      'HRATTEND',
      'AUTOAPPROVAL',
      'ENTERPRISE_TRAINING'
    ],
    incrementBusiness: null,
    createUserId: null,
    certificate: null,
    cgbEcif: null
  },
  deptMembers: [
    {
      id: 1947,
      merchantId: 76,
      deptId: 106400,
      deptName: '子集',
      userId: 10067,
      merchantMemberId: 1052,
      name: 'lzy',
      cellPhone: '***********',
      isManager: true,
      leaderUserId: 10067,
      disabled: false,
      deleted: null
    }
  ],
  deptVos: [
    {
      id: 106400,
      parentId: 103803,
      name: '子集',
      path: [103802, 103803, 106400],
      namePath: ['组织架构', '广发考勤机114测试', '子集'],
      members: null,
      children: null,
      childrenNum: 0,
      descendantMemberNum: 24,
      deleted: null,
      memberNum: 24,
      dingDeptId: null
    }
  ],
  merchantMember: {
    id: 1052,
    merchantId: 76,
    personalId: null,
    leaderUserId: null,
    name: '李泽阳',
    userId: 10067,
    cellPhone: '***********',
    jobNumber: null,
    createTime: '2022-02-17T14:42:15',
    modifyTime: '2023-04-06T13:40:17',
    entryTime: '2022-05-01T00:00:00',
    deptMember: null,
    disabled: false,
    deleted: false,
    privileges: [
      'contract.signManage.query',
      'contract.signManage.down',
      'contract.signManage.up',
      'contract.templateManage.add',
      'contract.templateManage.query',
      'contract.templateManage.edit',
      'contract.templateManage.enable',
      'contract.templateManage.delete',
      'contract.flowManage.add',
      'contract.flowManage.query',
      'contract.flowManage.edit',
      'contract.flowManage.enable',
      'contract.flowManage.delete',
      'contract.accountManage.expire',
      'contract.accountManage.seal',
      'contract.accountManage.audit',
      'contract.accountManage.sign',
      'contract.accountManage.launch',
      'salary.compute.salaryCheck.list',
      'salary.compute.salaryCheck.addSalaryRule',
      'salary.compute.salaryCheck.updateSalaryRule',
      'salary.compute.salaryCheck.query',
      'salary.compute.salaryCheck.empList',
      'salary.compute.salaryCheck.empAdd',
      'salary.compute.salaryCheck.empDelete',
      'salary.compute.salaryCheck.empExport',
      'salary.compute.salaryCheck.empReportList',
      'salary.compute.salaryCheck.empReportEdit',
      'salary.compute.salaryCheck.empReport',
      'salary.compute.salaryCheck.empReportExport',
      'salary.compute.salaryCheck.additionList',
      'salary.compute.salaryCheck.additionDownload',
      'salary.compute.salaryCheck.socialList',
      'salary.compute.salaryCheck.socialCopy',
      'salary.compute.salaryCheck.socialImport',
      'salary.compute.salaryCheck.socialExport',
      'salary.compute.salaryCheck.salaryList',
      'salary.compute.salaryCheck.salaryImport',
      'salary.compute.salaryCheck.salaryCompute',
      'salary.compute.salaryCheck.salaryReview',
      'salary.compute.salaryCheck.salaryExport',
      'salary.compute.salaryCheck.depExport',
      'salary.compute.salaryCheck.payroll',
      'salary.compute.salaryCheck.bankOffer',
      'salary.compute.salaryCheck.providStubs',
      'salary.report.personReport.list',
      'salary.report.personReport.edit',
      'salary.report.personReport.sendReport',
      'salary.report.personReport.export',
      'salary.report.additionl.list',
      'salary.report.additionl.export',
      'salary.report.taxReport.list',
      'salary.report.taxReport.generateReport',
      'salary.report.taxReport.sendReport',
      'salary.report.taxReport.reportBack',
      'salary.report.taxReport.cancelReport',
      'salary.report.taxReport.export',
      'salary.taxpay.paytax.list',
      'salary.taxpay.paytax.sendPay',
      'salary.taxpay.paytax.downloadProtocol',
      'salary.psalaryIssuing.batch.select',
      'salary.psalaryIssuing.batch.activation',
      'salary.psalaryIssuing.batch.create',
      'salary.psalaryIssuing.batchRecord.pay',
      'salary.psalaryIssuing.batch.import',
      'salary.psalaryIssuing.batchRecord.select',
      'salary.psalaryIssuing.batchRecord.details',
      'salary.psalaryIssuing.batchRecord.continue',
      'salary.psalaryIssuing.batchRecord.delete',
      'salary.psalaryIssuing.batchRecord.export',
      'salary.psalaryIssuing.batchRecord.detailsEdit',
      'salary.psalaryIssuing.batchRecord.detailsContinue',
      'salary.psalaryIssuing.batchRecord.close',
      'salary.psalaryIssuing.order.select',
      'salary.account.psalaryAccount.select',
      'salary.account.psalaryAccount.add',
      'salary.account.psalaryAccount.open',
      'salary.account.psalaryAccount.activation',
      'salary.account.psalaryAccount.details',
      'salary.account.psalaryAccount.info',
      'salary.account.psalaryAccount.saveUpdate',
      'salary.account.psalaryAccount.withdraw',
      'salary.account.psalaryAccount.changePwd',
      'salary.account.recharge.select',
      'salary.account.recharge.export',
      'salary.account.withdraw.select',
      'salary.account.withdraw.export',
      'salary.compute.emp.list',
      'salary.compute.emp.addEmp',
      'salary.compute.emp.importEmp',
      'salary.compute.emp.updateCompany',
      'salary.compute.emp.delEmp',
      'salary.compute.emp.editEmp',
      'salary.compute.emp.editEmpCompany',
      'salary.compute.emp.export',
      'salary.social.floatEmployee.list',
      'salary.social.floatEmployee.fastFloat',
      'salary.social.floatEmployee.increaseImport',
      'salary.social.floatEmployee.decreaseImport',
      'salary.social.floatEmployee.editImport',
      'salary.social.floatEmployee.del',
      'salary.social.floatEmployee.floatExport',
      'salary.social.floatEmployee.insuredExport',
      'salary.social.floatEmployee.increase',
      'salary.social.floatEmployee.decrease',
      'salary.social.floatEmployee.editSave',
      'salary.social.ledger.list',
      'salary.social.ledger.generate',
      'salary.social.ledger.import',
      'salary.social.ledger.archive',
      'salary.social.ledger.delCompany',
      'salary.social.ledger.exportCompany',
      'salary.social.ledger.supplement',
      'salary.social.ledger.delSupplement',
      'salary.social.ledger.exportEmp',
      'salary.social.insuredProject.list',
      'salary.social.insuredProject.add',
      'salary.social.insuredProject.edit',
      'salary.social.insuredProject.del',
      'salary.compute.salaryCheck.addSalaryEmp',
      'salary.compute.salaryCheck.syncSalaryEmp',
      'salary.report.personReport.add',
      'salary.payroll.batch.list',
      'salary.payroll.batch.generateBatch',
      'salary.payroll.batch.sync',
      'salary.payroll.batch.details',
      'salary.payroll.batch.delete',
      'salary.payroll.agreement.list',
      'salary.payroll.agreement.confirm',
      'salary.payroll.syncEmp.list',
      'salary.payroll.syncEmp.add',
      'salary.payroll.syncEmp.update',
      'salary.report.additionl.download',
      'salary.report.taxReport.totalList',
      'salary.report.taxReport.batchReport',
      'salary.report.taxReport.batchBack',
      'salary.report.taxReport.batchCancel',
      'salary.report.taxReport.addReduction',
      'salary.taxpay.paytax.batchPay',
      'salary.taxpay.paytax.batchDownload',
      'salary.auth.dataAuth.list',
      'salary.auth.dataAuth.modifyState',
      'salary.auth.dataAuth.assign',
      'salary.auth.dataAuth.delete',
      'salary.auth.dataAuth.sync',
      'salary.auth.dataAuth.export',
      'salary.compute.salaryArchive.list',
      'salary.compute.salaryArchive.fixSalary',
      'salary.compute.salaryArchive.changeSalary',
      'salary.compute.salaryArchive.modify',
      'salary.compute.salaryArchive.undo',
      'salary.compute.salaryArchive.importBatch',
      'salary.compute.salaryArchive.importHis',
      'salary.compute.salaryArchive.exportTotal',
      'salary.compute.salaryArchive.exportRecent',
      'salary.compute.salaryArchive.defineItem',
      'salary.report.taxReport.taxCalc',
      'salary.report.classify.totalList',
      'salary.report.classify.import',
      'salary.report.classify.addReduction',
      'salary.report.classify.taxCalc',
      'salary.report.classify.batchReport',
      'salary.report.classify.batchBack',
      'salary.report.classify.batchCancel',
      'operator.list',
      'hrEmployee.employee.roster.list',
      'hrEmployee.employee.roster.add',
      'hrEmployee.employee.roster.addImport',
      'hrEmployee.employee.roster.batchEditAuth',
      'hrEmployee.employee.roster.export',
      'hrEmployee.employee.roster.transferPosition',
      'hrEmployee.employee.roster.delete',
      'hrEmployee.employee.roster.empDetailentry-export',
      'hrEmployee.employee.roster.empInvication',
      'hrEmployee.employee.entry.list',
      'hrEmployee.employee.entry.recently',
      'hrEmployee.employee.entry.regist',
      'hrEmployee.employee.entry.export',
      'hrEmployee.employee.entry.confirm',
      'hrEmployee.employee.entry.empInvication',
      'hrEmployee.employee.entry.QRCode',
      'hrEmployee.employee.entry.delete',
      'hrEmployee.employee.entry.back',
      'hrEmployee.employee.entry.registDetail',
      'hrEmployee.employee.turnRegular.list',
      'hrEmployee.employee.turnRegular.recently',
      'hrEmployee.employee.turnRegular.export',
      'hrEmployee.employee.turnRegular.regular',
      'hrEmployee.employee.turnRegular.adjustRegular',
      'hrEmployee.employee.leave.list',
      'hrEmployee.employee.leave.recently',
      'hrEmployee.employee.leave.waitResign',
      'hrEmployee.employee.leave.export',
      'hrEmployee.employee.leave.comfirm',
      'hrEmployee.employee.leave.adjustResign',
      'hrEmployee.employee.leave.delete',
      'hrEmployee.employee.report.list',
      'hrEmployee.employee.report.export',
      'hrEmployee.employee.template.list',
      'hrEmployee.employee.template.addEmp',
      'hrEmployee.employee.template.empDetail-PC',
      'hrEmployee.employee.template.empDetail-H5',
      'hrEmployee.employee.template.empEntry-PC',
      'hrEmployee.employee.template.empEntry-H5',
      'hrEmployee.employee.template.groupAdd',
      'hrEmployee.employee.template.groupSort',
      'hrEmployee.employee.template.groupEdit',
      'hrEmployee.employee.template.groupDelete',
      'hrEmployee.employee.template.fieldAdd',
      'hrEmployee.employee.template.fieldSort',
      'hrEmployee.employee.template.fieldEdit',
      'hrEmployee.employee.option.list',
      'hrEmployee.employee.option.enable',
      'hrEmployee.employee.option.enumSet',
      'hrEmployee.employee.option.edit',
      'hrEmployee.employee.option.add',
      'hrEmployee.init.taxSubject.list',
      'hrEmployee.init.taxSubject.save',
      'hrEmployee.init.taxSubject.delete',
      'hrEmployee.init.taxSubject.export',
      'hrContract.conManage.laborContract.list',
      'hrContract.conManage.laborContract.newSign',
      'hrContract.conManage.laborContract.batchCreate',
      'hrContract.conManage.laborContract.export',
      'hrContract.conManage.laborContract.edit',
      'hrContract.conManage.laborContract.continueSign',
      'hrContract.conManage.laborContract.updateSign',
      'hrContract.conManage.laborContract.delete',
      'hrContract.conManage.eContract.list',
      'hrContract.conManage.eContract.create',
      'hrContract.conManage.eContract.batchRemind',
      'hrContract.conManage.eContract.export',
      'hrContract.conManage.eContract.detail',
      'hrContract.conManage.eContract.sign',
      'hrContract.conManage.eContract.back',
      'hrContract.conManage.eContract.abandon',
      'hrContract.conManage.eContract.download',
      'hrContract.conManage.eContract.delete',
      'hrContract.conManage.conTemplate.list',
      'hrContract.conManage.conTemplate.add',
      'hrContract.conManage.conTemplate.edit',
      'hrContract.conManage.conTemplate.enable',
      'hrContract.conManage.conTemplate.disable',
      'hrContract.conManage.conTemplate.delete',
      'hrAttend.attendManage.group.getAttendGroupList',
      'hrAttend.attendManage.group.savaAttendGroup',
      'hrAttend.attendManage.group.deleteAttendGroup',
      'hrAttend.attendManage.group.updateAttendGroup',
      'hrAttend.attendManage.group.setAttendWorkForScheduling',
      'hrAttend.attendManage.work.getAttendWorkingList',
      'hrAttend.attendManage.work.savaWorking',
      'hrAttend.attendManage.work.updateWorking',
      'hrAttend.attendManage.work.deleteAttendWork',
      'hrAttend.attendManage.supplement.getSupplementRuleList',
      'hrAttend.attendManage.supplement.savaSupplementRule',
      'hrAttend.attendManage.supplement.updateSupplementRule',
      'hrAttend.attendManage.supplement.deleteSupplementRule',
      'hrAttend.attendManage.leave.queryLeaveInfo',
      'hrAttend.attendManage.leave.addLeave',
      'hrAttend.attendManage.leave.modifyLeaveById',
      'hrAttend.attendManage.leave.deleteLeave',
      'hrAttend.attendManage.leave.queryLeaveBalanceInfo',
      'hrAttend.attendManage.leave.updatePersonBalance',
      'hrAttend.attendManage.dailyCount.getCountOfDayByEmpIds',
      'hrAttend.attendManage.dailyCount.updateExceptionStatus',
      'hrAttend.attendManage.monthlyCount.getCountOfMonthByEmpIds',
      'hrAttend.attendManage.signTime.getSignTimeByEmpIds',
      'hrAttend.attendManage.count.getSignRecordByEmpIds',
      'hrEmployee.employee.template.fieldDel',
      'hrEmployee.employee.option.del',
      'hrAttend.attendManage.overTime.getOverTimeList',
      'hrAttend.attendManage.overTime.savaOvertimeRule',
      'hrAttend.attendManage.overTime.updateOvertimeRule',
      'hrAttend.attendManage.overTime.deleteOvertimeRule',
      'hrAttend.attendManage.overTimeDetail.getEmpOvertimeRecordList',
      'salary.compute.attendance.salaryRuleList',
      'salary.compute.attendance.salaryRuleAdd',
      'salary.compute.attendance.salaryRuleCopy',
      'salary.compute.attendance.salaryRuleEdit',
      'salary.compute.attendance.salaryRuleDel',
      'salary.compute.salaryCheck.attendanceList',
      'salary.compute.salaryCheck.attendanceUpdate',
      'salary.compute.salaryCheck.attendanceImport',
      'salary.compute.salaryCheck.attendanceExport',
      'hrEmployee.employee.changeOpt.transferList',
      'hrEmployee.employee.changeOpt.transferListExport',
      'hrEmployee.employee.changeOpt.transferRecordCancel',
      'hrEmployee.employee.changeOpt.transferRecordDelete',
      'hrEmployee.employee.changeOpt.entryList',
      'hrEmployee.employee.changeOpt.entryListExport',
      'hrEmployee.employee.changeOpt.turnList',
      'hrEmployee.employee.changeOpt.turnListExport',
      'hrEmployee.employee.changeOpt.turnRecordCancel',
      'hrEmployee.employee.changeOpt.turnRecordDelete',
      'hrEmployee.employee.changeOpt.leaveList',
      'hrEmployee.employee.changeOpt.leaveListExport',
      'tripartitePlatForm.platFormManage.query',
      'salary.compute.salaryCheck.newPayRoll',
      'salary.compute.salaryCheck.newCancel',
      'salary.newpayroll.pay.list',
      'salary.newpayroll.pay.detail',
      'salary.newpayroll.pay.cancel',
      'salary.newpayroll.pay.delete',
      'salary.newpayroll.account.list',
      'salary.newpayroll.account.add',
      'salary.newpayroll.account.enable',
      'salary.newpayroll.account.detail',
      'salary.newpayroll.apply.list',
      'salary.newpayroll.apply.check',
      'salary.newpayroll.apply.commit',
      'salary.newpayroll.apply.detail',
      'salary.newpayroll.apply.edit',
      'salary.newpayroll.pay.complete',
      'salary.newpayroll.pay.importFail',
      'salary.newpayroll.pay.comfirmPay',
      'salary.newpayroll.pay.export',
      'dept.organizational',
      'dept.query',
      'dept.userSet',
      'dept.postSet',
      'dept.batchImportUser',
      'dept.userAdd',
      'dept.userDetails',
      'dept.userEdit',
      'dept.disableUser',
      'dept.enableUser',
      'dept.AssignRole',
      'dept.deleteUser',
      'dept.editDept',
      'dept.deleteDept',
      'dept.addSubDept',
      'dept.addLable',
      'dept.editLable',
      'dept.addMember',
      'dept.moveMember',
      'train.dataStatistics.query',
      'train.trainClassification.query',
      'train.trainClassification.operate',
      'train.trainManagement.configAdd',
      'train.trainManagement.configEdit',
      'train.trainManagement.configDelete',
      'train.trainManagement.configRelease',
      'train.trainManagement.configEnd',
      'train.trainManagement.assignStudents',
      'train.trainManagement.configApply',
      'train.trainManagement.query',
      'train.trainManagement.markCompleted',
      'train.trainManagement.modifiedGrades',
      'train.lecturerManagement.configAdd',
      'train.lecturerManagement.configEdit',
      'train.lecturerManagement.configDelete',
      'train.testpaperClassification.operate',
      'train.testpaperClassification.query',
      'train.testpaperManagement.configAdd',
      'train.testpaperManagement.configEdit',
      'train.testpaperManagement.configDelete',
      'train.testpaperManagement.query',
      'train.questionbankClassification.operate',
      'train.questionbankClassification.query',
      'train.questionbankManagement.configAdd',
      'train.questionbankManagement.configEdit',
      'train.questionbankManagement.configDelete',
      'train.questionbankManagement.subjectExport',
      'train.questionbankManagement.subjectImport',
      'train.questionbankManagement.manage',
      'train.questionbankManagement.subjectAdd',
      'train.questionbankManagement.subjectEdit',
      'train.questionbankManagement.subjectDelete',
      'train.questionbankManagement.query',
      'train.spaceManagement.configAdd',
      'train.spaceManagement.configDelete',
      'train.spaceManagement.query',
      'train.studyManagement.query',
      'train.studyManagement.config',
      'hrEmployee.employee.empcare.list',
      'hrEmployee.employee.empcare.config',
      'hrEmployee.employee.empcare.sendsms',
      'kpi.performance.plan.list',
      'kpi.performance.plan.add',
      'kpi.performance.plan.confirmCheck',
      'kpi.performance.plan.confirmDetail',
      'kpi.performance.plan.scoreStart',
      'kpi.performance.plan.detail',
      'kpi.performance.plan.completeValueReplace',
      'kpi.performance.plan.delete',
      'kpi.performance.archives.list',
      'kpi.performance.archives.last',
      'kpi.performance.archives.detail',
      'kpi.performance.positionBank.list',
      'kpi.performance.positionBank.change',
      'kpi.performance.positionBank.delete',
      'kpi.performance.positionBank.add',
      'kpi.performance.indicatorBank.list',
      'kpi.performance.indicatorBank.groupAdd',
      'kpi.performance.indicatorBank.groupUpdate',
      'kpi.performance.indicatorBank.groupDelete',
      'kpi.performance.indicatorBank.indicatorAdd',
      'kpi.performance.indicatorBank.indicatorUpdate',
      'kpi.performance.indicatorBank.indicatorDelete',
      'kpi.performance.planSetting.list',
      'kpi.performance.planSetting.add',
      'kpi.performance.planSetting.delete',
      'kpi.performance.planSetting.save',
      'kpi.performance.targetMap.tree',
      'kpi.performance.targetMap.viewSubsidiary',
      'kpi.performance.targetMap.viewDepartment',
      'kpi.performance.targetMap.viewEmployee',
      'kpi.performance.myPlan.list',
      'kpi.performance.myPlan.detail',
      'kpi.performance.myTodo.list',
      'kpi.performance.myTodo.detail',
      'train.trainManagement.removeStudent',
      'salary.newpayroll.pay.invitation',
      'salary.newpayroll.account.invitation',
      'salary.merchant.payStubs.list',
      'salary.merchant.payStubs.add',
      'salary.merchant.payStubs.detail',
      'salary.merchant.payStubs.remove',
      'salary.merchant.payStubs.itemDetail',
      'salary.merchant.payStubs.edit',
      'salary.merchant.payStubs.back',
      'salary.merchant.payStubs.send',
      'salary.merchant.payStubs.itemRemove',
      'salary.merchant.setting.detail',
      'hrAttend.attendManage.faceManager.queryFaceInfo',
      'hrAttend.attendManage.faceManager.oneClickNotice',
      'hrAttend.attendManage.faceManager.inputFacePicture',
      'hrAttend.attendManage.faceManager.modifyState',
      'hrAttend.attendManage.faceManager.noticeEmployee',
      'hrAttend.attendManage.dailyCount.getTempOfDayByEmpIds',
      'hrAttend.attendManage.dailyCount.exportTempOfDayByEmpIds',
      'autoApproval.formManagement.query',
      'autoApproval.approvalData.query',
      'autoApproval.customArchives.query',
      'expense.form.fee.view',
      'expense.form.staff.view',
      'expense.pay.workbench.manage',
      'expense.pay.batch.view',
      'expense.receipt.apply.view',
      'expense.receipt.loan.view',
      'expense.receipt.reimbursement.view',
      'expense.invoiceManage.invoice.view',
      'expense.budgetManage.scheme.manage',
      'expense.budgetManage.report.view',
      'expense.feeStandard.scheme.manage',
      'expense.risk.rule.manage',
      'expense.document.feeTpl.manage',
      'expense.document.cityGroup.manage',
      'expense.document.payAccount.manage',
      'salary.newpayroll.apply.savePaySalaryApplyBatch',
      'salary.newpayroll.apply.delete',
      'merchant.log.query',
      'merchant.account.query',
      'merchant.info.query',
      'salary.compute.salaryArchive.delEmployee',
      'salary.report.taxReport.import',
      'salary.report.personReport.import',
      'salary.report.taxReport.makeCorrectionReport',
      'salary.report.taxReport.makeCorrectionCancel',
      'contract2.index.query',
      'contract2.signTask.query',
      'contract2.signTask.initiate',
      'contract2.signTask.examine',
      'contract2.signTask.urge',
      'contract2.signTask.recall',
      'contract2.signTask.download',
      'contract2.signTask.export',
      'contract2.contractManagement.query',
      'contract2.contractManagement.download',
      'contract2.contractManagement.export',
      'contract2.contractManagement.renew',
      'contract2.contractManagement.terminate',
      'contract2.contractManagement.change',
      'contract2.templateManagement.query',
      'contract2.templateManagement.add',
      'contract2.templateManagement.edit',
      'contract2.templateManagement.enable',
      'contract2.templateManagement.delete',
      'contract2.contractSet.numberManagement.manage',
      'contract2.contractSet.typeManagement.manage',
      'contract2.contractSet.flowManagement.manage'
    ],
    roles: [
      {
        id: 1,
        name: '薪酬岗'
      },
      {
        id: 2,
        name: '人事专员岗'
      },
      {
        id: 3,
        name: '人事主管岗'
      },
      {
        id: 6,
        name: '系统管理员'
      },
      {
        id: 9,
        name: '发薪岗1'
      },
      {
        id: 211,
        name: '发薪岗-智能代发'
      },
      {
        id: 324,
        name: 'HR'
      },
      {
        id: 325,
        name: 'HRD'
      },
      {
        id: 376,
        name: '合同管理岗'
      },
      {
        id: 377,
        name: '审批(旧)'
      },
      {
        id: 378,
        name: '假勤管理员'
      },
      {
        id: 550,
        name: '基础功能'
      },
      {
        id: 561,
        name: '公章签署人'
      },
      {
        id: 564,
        name: '培训岗'
      },
      {
        id: 566,
        name: '绩效岗'
      },
      {
        id: 567,
        name: '企业高管'
      },
      {
        id: 568,
        name: '部门主管'
      },
      {
        id: 598,
        name: '财务岗'
      },
      {
        id: 599,
        name: '出纳岗'
      },
      {
        id: 601,
        name: '流程管理员-审批'
      },
      {
        id: 643,
        name: '三方服务专区管理员'
      },
      {
        id: 653,
        name: '合同管理岗(钉签约新)'
      }
    ],
    merchant: null,
    isAdmin: true,
    personal: null,
    employee: null,
    bankAccounts: null,
    posts: null
  },
  user: {
    id: 10067,
    displayName: null,
    realName: '李泽阳',
    name: null,
    cellPhone: '***********',
    email: null,
    idCardNo: null,
    isAuth: true,
    defaultMerchantId: 76,
    defaultBankCardId: null,
    defaultSealId: null,
    personal: {
      id: 33541,
      merchantId: null,
      certificate: {
        id: 32337,
        ownerType: 'PERSONAL',
        ownerId: '33541',
        type: 'PRC_ID',
        number: '15262619961002271X',
        validityBegin: null,
        validityEnd: null,
        issuedBy: null,
        fileId: [],
        deleted: null
      },
      ownerType: 'USER',
      ownerId: '10067',
      name: '李泽阳',
      gender: null,
      country: null,
      domicilePlace: null,
      birthday: null,
      nation: null,
      homeCity: null,
      homeAddress: null,
      contractPhone: null,
      cellphone: null
    },
    seals: null,
    createTime: '2021-11-04T22:52:37',
    modifyTime: '2023-05-30T17:07:54',
    passwords: null
  },
  joinedMerchant: [
    {
      id: 77,
      merchantId: 5,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2021-11-05T21:11:05',
      modifyTime: '2023-04-06T12:00:00',
      entryTime: '2021-11-05T00:00:00',
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 5,
        name: '假勤114（北京）',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: ''
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 317,
      merchantId: 11,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2021-12-14T15:44:00',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: '2021-12-01T00:00:00',
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 11,
        name: '不开通钉薪税测试企业',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 356,
      merchantId: 24,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2021-12-24T13:48:24',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 24,
        name: '人资演示企业',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 1043,
      merchantId: 72,
      personalId: null,
      leaderUserId: null,
      name: 'lzy',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-02-15T10:04:16',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 72,
        name: '未认证',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 1045,
      merchantId: 74,
      personalId: null,
      leaderUserId: null,
      name: 'lzy',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-02-15T10:33:44',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 74,
        name: 's的经济',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 1052,
      merchantId: 76,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-02-17T14:42:15',
      modifyTime: '2023-04-06T13:40:17',
      entryTime: '2022-05-01T00:00:00',
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 76,
        name: '广发考勤机114测试',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 1061,
      merchantId: 81,
      personalId: null,
      leaderUserId: null,
      name: 'lzy',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-03-01T10:44:23',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 81,
        name: '第二个新创建',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 1072,
      merchantId: 83,
      personalId: null,
      leaderUserId: null,
      name: 'lzy',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-03-01T13:38:43',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 83,
        name: '第三个新创建',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 1083,
      merchantId: 86,
      personalId: null,
      leaderUserId: null,
      name: 'lzy',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-03-03T16:05:51',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 86,
        name: '长长长长长长长长长长长长长长长长23世界顶级的长129318238193324234',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 1271,
      merchantId: 226,
      personalId: null,
      leaderUserId: null,
      name: 'lzy',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-03-10T16:34:06',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 226,
        name: 'uy',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 1702,
      merchantId: 307,
      personalId: null,
      leaderUserId: null,
      name: 'lzy',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-03-31T16:04:12',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 307,
        name: 'uu',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 1860,
      merchantId: 328,
      personalId: null,
      leaderUserId: null,
      name: '泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-05-16T15:37:47',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: '2022-05-16T00:00:00',
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 328,
        name: '假勤的考勤机',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 1987,
      merchantId: 334,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-06-08T14:52:19',
      modifyTime: '2023-04-06T13:40:04',
      entryTime: '2022-06-01T00:00:00',
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 334,
        name: '回归一遍114',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 1995,
      merchantId: 336,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-06-08T16:04:15',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 336,
        name: '有没得',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 2090,
      merchantId: 378,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-07-05T15:24:07',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 378,
        name: 'test费控',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 2564,
      merchantId: 323,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-07-11T11:31:53',
      modifyTime: '2023-04-06T13:40:01',
      entryTime: '2021-07-01T00:00:00',
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 323,
        name: '费控测试企业',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 2566,
      merchantId: 400,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-07-11T16:03:05',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 400,
        name: '城市组',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 2568,
      merchantId: 364,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-07-12T11:25:01',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: '2022-07-01T00:00:00',
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 364,
        name: '新审批new',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 2611,
      merchantId: 416,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-07-15T15:18:47',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 416,
        name: '看cj改bug',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 2920,
      merchantId: 468,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-08-03T13:44:13',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 468,
        name: '新手引导',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 2922,
      merchantId: 470,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-08-03T15:29:42',
      modifyTime: '2023-04-06T13:40:17',
      entryTime: '2022-08-01T00:00:00',
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 470,
        name: '220803',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 4987,
      merchantId: 556,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2022-09-15T17:02:42',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: '2022-09-01T00:00:00',
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 556,
        name: '人事新审批接入企业',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 5532,
      merchantId: 699,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-01-09T17:22:39',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 699,
        name: '我与刘少',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 5533,
      merchantId: 700,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-01-09T17:26:32',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 700,
        name: '（测试）接手思锦',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 5548,
      merchantId: 703,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-01-12T14:57:16',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 703,
        name: '测试导入花名册同步数据',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 6532,
      merchantId: 708,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-02-15T18:12:04',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 708,
        name: '是否会接受',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 6533,
      merchantId: 709,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-02-15T18:26:46',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 709,
        name: 'test1',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 6534,
      merchantId: 710,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-02-16T09:39:04',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 710,
        name: '232323',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 78963,
      merchantId: 717,
      personalId: null,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-03-16T17:17:52',
      modifyTime: '2023-04-06T17:53:44',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 717,
        name: '123',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 79590,
      merchantId: 737,
      personalId: 34309,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-04-14T09:51:34',
      modifyTime: '2023-04-14T09:51:34',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 737,
        name: 'iuiu有',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 79596,
      merchantId: 742,
      personalId: 34316,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-04-14T13:26:15',
      modifyTime: '2023-04-14T13:26:15',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 742,
        name: '123',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 79603,
      merchantId: 748,
      personalId: 34323,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-04-18T14:19:39',
      modifyTime: '2023-04-18T14:19:39',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 748,
        name: '222',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 79604,
      merchantId: 749,
      personalId: 34324,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-04-18T14:21:06',
      modifyTime: '2023-04-18T14:21:06',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 749,
        name: '444',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 79605,
      merchantId: 750,
      personalId: 34325,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-04-18T14:21:22',
      modifyTime: '2023-04-18T14:21:22',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 750,
        name: '41',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 90083,
      merchantId: 758,
      personalId: 47841,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-05-11T10:44:58',
      modifyTime: '2023-05-11T10:44:58',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 758,
        name: 'tfyh',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 90084,
      merchantId: 759,
      personalId: 47842,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-05-11T10:47:30',
      modifyTime: '2023-05-11T10:47:30',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 759,
        name: 'ggg',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 90093,
      merchantId: 762,
      personalId: 47857,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-05-12T09:56:41',
      modifyTime: '2023-05-12T09:56:41',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 762,
        name: '初始化企业1',
        introduction: '',
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    },
    {
      id: 90097,
      merchantId: 765,
      personalId: 47862,
      leaderUserId: null,
      name: '李泽阳',
      userId: 10067,
      cellPhone: '***********',
      jobNumber: null,
      createTime: '2023-05-12T13:25:28',
      modifyTime: '2023-05-12T13:25:28',
      entryTime: null,
      deptMember: null,
      disabled: false,
      deleted: false,
      privileges: null,
      roles: null,
      merchant: {
        id: 765,
        name: '士大夫',
        introduction: null,
        defaultLegal: null,
        openedBusiness: null,
        incrementBusiness: null,
        createUserId: null,
        certificate: null,
        cgbEcif: null
      },
      isAdmin: null,
      personal: null,
      employee: null,
      bankAccounts: null,
      posts: null
    }
  ],
  loginDefaultMerchantId: 76
}

export const privileges = profile.merchantMember.privileges
export const user = profile.user
export const merchant = profile.merchant
export const joinedMerchant = profile.joinedMerchant
export const deptMembers = profile.deptMembers
export const deptVos = profile.deptVos
export const merchantMember = profile.merchantMember
export const isManager = profile.merchantMember.isManager
export const loginDefaultMerchantId = profile.loginDefaultMerchantId

export const hadPrivilege = code => {
  return privileges.includes(code)
}

export default profile
