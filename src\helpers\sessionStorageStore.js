const sessionStorageStore = {
  set(key, value) {
    sessionStorage.setItem(key, JSON.stringify(value))
  },
  remove(key) {
    sessionStorage.removeItem(key)
  },
  get(key) {
    //优先从cookie读取 避免退出登录后，再次登录新的用户，导致token未更新
    if (key === 'token') {
      if(sessionStorage.getItem(key)) return JSON.parse(sessionStorage.getItem(key))
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('__token__='))
        ?.split('=')[1]
      if (token) {
        return token
      }
    }

    var r = JSON.parse(sessionStorage.getItem(key))

    if (r === 'true') {
      return true
    } else if (r === 'false') {
      return false
    }

    return r
  }
}

export default sessionStorageStore
