const events = {
  'HRSaaS.HR.Dashboard': '人事管理/首页',
  'HRSaaS.HR.Dashboard.LeftModules': '人事管理/首页/左侧功能模块入口点击量'
}
;(function () {
  const hostsMap = {
    localhost: '127.0.0.1:9000' // 本地dev环境
  }
  const hostname = window.location.hostname || 'localhost'
  window.WAConst = window.WAConst || {}
  window.WAConst.requestUrl = hostsMap[hostname] || ''
})()
class Stats {
  //用户id、用户类型、企业id、企业类型、用户角色
  constructor(
    options = {
      userID: '',
      userType: '', //管理员和操作员
      userRole: '',
      merchantID: '',
      merchantType: ''
    }
  ) {
    this.options = options
    this.platform = '开薪易'
    this.business = '零售面客'
    this.businessNo = 1
  }
  webEvent(event, options) {
    if (typeof options === 'string') {
      const params = new URLSearchParams(options)
      var coptions = {}
      for (const [k, v] of params) {
        coptions[k] = v
      }
      options = coptions
    }

    this._uploadEvent('custom', event, options)
  }
  webPage(event, options) {
    const title = document.title
    this._uploadEvent('page', event, {
      ...options,
      title
    })
  }
  getNameFromEvent(event) {
    const category = events[event]
    if (!category) {
      console.error('not supported event:', event)
      return
    }

    return category.replace('/', '-')
  }
  _uploadEvent(type, event, options = {}) {
    //这个检查不行
    if (!window.$) {
      return
    }

    const r = sessionStorage.getItem('__stats')
    if (r) {
      const opts = JSON.parse(r)
      for (var key in opts) {
        options[key] = opts[key]
      }
    }
    const optValue = []
    for (var key in options) {
      if (!options[key]) {
        continue
      }
      optValue.push(`${key}=${options[key]}`)
    }
    if (type === 'page') {
      window.$.behaviorSender.behaviorQueue.push({
        type,
        option: {
          optValue: optValue.join(','),
          curUrl: event,
          ot: new Date().getTime()
        }
      })
    } else {
      window.$.behaviorSender.behaviorQueue.push({
        type,
        option: {
          optValue: optValue.join(','),
          action: event,
          ot: new Date().getTime()
        }
      })
    }
  }
}
export default Stats
// "事件触发时机",
// "浏览触发",
// "点击触发",
// "选择完成后触发",
// "曝光触发",
// "搜索输入后触发",
// "选择后触发",
// "输入后触发",
// "输入完成后触发"
// const TriggerType = {
//   Click: 'click',
//   Select: 'select',
//   View: 'view'
// }
