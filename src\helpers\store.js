import { isObject } from './index'

const store = {
  set(key, value) {
    if (isObject(value)) {
      value = JSON.stringify(value)
    }
    localStorage.setItem(key, value)
  },
  remove(key) {
    localStorage.removeItem(key)
  },
  get(key) {
    //优先从cookie读取 避免退出登录后，再次登录新的用户，导致token未更新
    if (key === 'token') {
      if (localStorage.getItem(key)) return localStorage.getItem(key)
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('__token__='))
        ?.split('=')[1]
      if (token) {
        return token
      }
    }

    var r = localStorage.getItem(key)

    if (r === 'true') {
      return true
    } else if (r === 'false') {
      return false
    }

    return r
  }
}

export default store
