const transferToStaticImageHref = (
  image,
  options = { wxchat: false, icon: false }
) => {
  if (options) {
    if (options.wxchat) {
      image = `/mp-weixin/contract-mp/static/images/${image}`
    } else if (options.icon) {
      image = `/icon-library/png/${image}`
    }
  }

  const staticImageHref = window.env.staticURL + image
  return staticImageHref
}

export default transferToStaticImageHref
