import 'assets/scss/base.scss';
import 'assets/scss/element-variables.scss';
import 'assets/font/iconfont.css';
import 'assets/scss/el-cover.scss';
import 'assets/scss/draggle.scss';

import 'assets/font/performance/iconfont.css'; //绩效图标库 - 更换需手动需改font-family: "iconfont-per";
import 'assets/fonts/iconfont.css';
// import 'assets/font/iconfont1.css';
import Vue from 'vue';
import Router from 'vue-router';
import math from './utils/mathjsConfig';
import './util/localStorage';
import ElementUI from 'element-ui';
import oldBusinessUi from 'olading-business-ui';
import 'olading-business-ui/lib/olading-business-ui.css';
import Vuex from 'vuex';
import fullScreenRoute from '@/router/full-screen-route';
// import oladingUI from "olading-zui"




//修改于2022年6月13日，集成导航组件
import OladingBusinessUI, {
  createDesktopApp,
  getToken,
} from '@olading/olading-business-ui';
const { toWorkBenchPage } = OladingBusinessUI;
import '@olading/olading-business-ui/dist/olading-business-ui.css';
import * as prototypeUtils from './utils/proptype'
import { wechatConfigInit } from "./utils/wechat"

import oldUI from 'olading-ui';
import 'olading-ui/lib/theme-chalk/index.css';
// import 'olading-zui/lib/style.css'

import router from './router';
import filters from 'filters';
import 'directives';
import { preventReClick } from './plugins';

import store from './store';
import performanceStore from './performance/store';

import attendanceApi from './modules/attendance/api';

import { errorScroll } from '@/utils/utils';
import { getIsOpenHROBusinessApi } from './util/hro';


/**
 * dayjs
 */
import * as dayjs from 'dayjs';

// 换肤
import changeTheme from './util/changeTheme';

import commons from '@/mixins/commons';
//解决重复点当前路由报错问题
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onReject || onResolve) {
    return originalPush.call(this, location, onResolve, onReject);
  }
  return originalPush.call(this, location).catch((err) => err);
};

Vue.use(prototypeUtils.install(prototypeUtils))
Vue.use(Router);

Vue.use(Vuex);




wechatConfigInit()

//mathjs
Vue.prototype.$math = math;

Vue.prototype.$getToken = getToken;

// 对话框和抽屉默认点击阴影处无法取消
ElementUI.Dialog.props.closeOnClickModal.default = false;
ElementUI.Drawer.props.wrapperClosable.default = false;
Vue.use(ElementUI);

Vue.use(oldBusinessUi);

Vue.use(OladingBusinessUI);

Vue.use(oldUI);

// oladingUI.setConfig({
//   baseURL:window.env.apiPath+"/",
//   token:getToken()
// }) 

// Vue.use(oladingUI)


Object.keys(filters).forEach((item) => {
  Vue.filter(item, filters[item]);
});

Vue.config.productionTip = false;

Vue.prototype.$attApi = attendanceApi;

Vue.prototype.$dayjs = dayjs;

//存储本地token
function getUrlParams(name, str) {
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
  const r = str.substr(1).match(reg);
  if (r != null) return decodeURIComponent(r[2]);
  return null;
}
let token = getUrlParams('token', window.location.search);
let encrypt = getUrlParams('encrypt', window.location.search);
if (token) {
  store.commit('SET_TOKEN', token);
  sessionStorageOther.setItem('token', token);
}
if (encrypt) {
  store.commit('SET_AUTHCODE', encrypt);
}

const theme = window.env.theme;

if (theme) {
  changeTheme(theme);
  const bodyEl = document.getElementsByTagName('body')[0]
  bodyEl.style.setProperty('--color-primary', '#' + theme);
  
  document.getElementsByTagName('body')[0].className = 't-' + theme;
  bodyEl.style.setProperty('--o-primary-color', '#' + theme)
}

import fun from './util/fun';
Vue.prototype.errorScroll = errorScroll;

// fetchGetConfig().then(res=> {

// window.__BASEURL__ = res.env.host;
// new Vue({
//   el: '#app',
//   router,
//   store,
//   performanceStore,
//   render: createElement => createElement(App)
// });
// });

function beforeRoute(to, from, next) {
  if (to.path === '/login' && getToken()) {
    toWorkBenchPage();
    return false;
  }
  next();
}

(async()=>{
  await getIsOpenHROBusinessApi();
  createDesktopApp(
    {
      basePath: router.base,
      mixins: [commons],
      menus: [],
      store,
      routes: [...fullScreenRoute],
      modules: router.options.routes,
      routerModule: {
        router: Router,
        beforeHooks: [beforeRoute],
      },
      init: ($app) => {
        // sessionStorage.setItem("token", $app.state.token);
        console.info('this...', $app.state);
      },
    },
    '#app'
  );
})()


