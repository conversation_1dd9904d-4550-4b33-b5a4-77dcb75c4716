<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>考勤机认证</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #fff;
        margin: 0;
        padding: 0;
        color: #333;
        overflow: hidden;
      }

      .container {
        margin: 0 auto;
        padding: 20px;
        height: calc(100vh - 170px);
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .title {
        height: 60px;
        display: flex;
        align-items: center;
        padding-left: 16px;
        text-align: left;
        border-bottom: 1px solid #ccc;
      }

      .section {
        margin-bottom: 20px;
      }

      .section h3 {
        font-size: 16px;
        color: #333;
        margin-bottom: 10px;
      }

      .section .input-box {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }

      .section .input-box label {
        width: 150px;
        font-size: 14px;
        color: #333;
        text-align: right;
        margin-right: 5px;
      }

      .section .input-box input {
        width: 300px;
        padding: 5px;
        font-size: 14px;
        border: 1px solid #ccc;
        border-radius: 4px;
      }

      .info-note {
        font-size: 12px;
        color: red;
        margin-left: 10px;
      }

      .footer-text {
        font-size: 12px;
        text-align: center;
        color: #666;
        margin-top: 30px;
        line-height: 1.5;
      }

      .checkbox-area {
        margin-top: 10px;
      }

      .checkbox-area label {
        font-size: 12px;
      }

      .checkbox-area a {
        color: #1890ff;
        text-decoration: none;
      }

      .buttons {
      }

      .buttons button {
        padding: 10px 30px;
        margin: 0 10px;
        border: none;
        border-radius: 4px;
        background-color: #46a0fc;
        color: #fff;
        font-size: 14px;
        cursor: pointer;
      }

      .buttons button.cancel {
        border: 1px solid #d9d9d9;
        background-color: #fff;
        color: #333;
      }

      .buttons button:hover {
        opacity: 0.9;
      }
    </style>
  </head>

  <body>
    <%=htmlWebpackPlugin.files.webpackManifest%>
    <div id="app"></div>
    
  </body>
</html>
