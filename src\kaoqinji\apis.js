const timeout = 60 * 1000;
class SimpleHTTPClient {
  constructor() {
    var uri = '';
    if (window.location.href.includes('/gd/hrsaas')) {
      uri = '/gd/hrsaas/webapi';
    } else if (window.location.href.includes('/hrsaas"')) {
      uri = '/hrsaas/webapi';
    }

    this.apiPrefix = uri;
  }
  _abortController() {
    const abortController = new AbortController();
    const timeoutAdapter = new Promise((resolve, reject) => {
      setTimeout(() => {
        reject('Timeout');
        abortController.abort();
      }, 30000);
    });

    return [abortController, timeoutAdapter];
  }
  async request(resource, options) {
    const [abortController, timeoutAdapter] = this._abortController();
    try {
      var url = resource;
      if (!url.includes('http')) {
        url = this.apiPrefix + resource;
      }
      const r = await Promise.race([
        timeoutAdapter,
        fetch(url, {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify(options.body),
          signal: abortController.signal,
        }),
      ]);
      const result = await r.json();
      if (!result.success) {
        return [result.message, null];
      }

      return [null, result];
    } catch (err) {
      return [err, null];
    }
  }
}

const client = new SimpleHTTPClient();

export const confAdd = async (body) => {
  return client.request('/api/attend/devPlatform/confAdd', {
    body,
  });
};
