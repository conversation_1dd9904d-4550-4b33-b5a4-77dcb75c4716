const urlParams = new URLSearchParams(window.location.search);
const name = urlParams.get('name');
const no = urlParams.get('no');
const token = urlParams.get('token');

var submitting = false;
function encryptData(msgString) {
  return sm2.doEncrypt(msgString, publicKey, cipherMode);
}

function decryptData(value) {
  return sm2.doDecrypt(value, privateKey, cipherMode);
}

async function submit() {
  if (submitting) {
    return;
  }

  submitting = true;
  setTimeout(() => (submitting = false), 5000);

  const agreeEl = document.getElementById('agree');
  const contactEl = document.getElementById('contact');
  const passwordEl = document.getElementById('password');
  if (!agreeEl.checked) {
    // 使用示例
    message({
      type: 'error',
      message: '请阅读且同意阿拉钉用户服务协议',
      duration: 3000, // 消息显示的持续时间，单位是毫秒
    });

    return;
  }
  if (!contactEl.value.trim()) {
    message({
      type: 'error',
      message: '请输入经办人姓名',
      duration: 3000,
    });
    return;
  }
  if (contactEl.value.trim().length > 16) {
    message({
      type: 'error',
      message: '经办人姓名最长只允许16位',
      duration: 3000,
    });
    return;
  }
  if (!passwordEl.value.trim()) {
    message({
      type: 'error',
      message: '请输入申报密码',
      duration: 3000,
    });
    return;
  }
  if (passwordEl.value.trim().length > 32) {
    message({
      type: 'error',
      message: '经办人姓名最长只允许32位',
      duration: 3000,
    });
    return;
  }
  if (!token.trim()) {
    message({
      type: 'error',
      message: '仅支持从开薪易进行申报',
      duration: 3000,
    });
    return;
  }
  const params = {
    remark: contactEl.value.trim(),
    pwd: encryptData(passwordEl.value.trim()),
    // pwd: passwordEl.value.trim(),
    taxSubName: name,
    taxPayerNo: no,
    agreed: agreeEl.checked,
    token,
  };

  const res = await verify(params);

  submitting = false;

  const [err, r] = res;
  if (err || (r && !r.success)) {
    document.getElementById('content').style.display = 'none';
    document.getElementById('failed').style.display = 'flex';

    return;
  }

  document.getElementById('content').style.display = 'none';
  document.getElementById('succeed').style.display = 'flex';
}

function showProtocol() {
  const iframe = document.createElement('iframe');
  iframe.style.border = 'none';
  iframe.style.width = '100%';
  iframe.style.height = '100%';
  iframe.src = window.location.origin + '/static/userServiceProtocol.html';
  dialog({
    confirmText: '我已阅读',
    contentEl: iframe,
  });
}
globalThis.submit = submit;
globalThis.showProtocol = showProtocol;

export default {
  submit,
  showProtocol,
};
