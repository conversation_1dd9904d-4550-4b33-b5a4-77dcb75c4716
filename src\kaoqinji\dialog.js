var lastEls = [];
const clear = () => {
  if (lastEls && lastEls.length) {
    if (lastEls[0]) {
      lastEls[0].remove();
    }
    if (lastEls[1]) {
      lastEls[1].remove();
    }
  }
};
const dialog = (options) => {
  const {
    title = '',
    contentEl = null,
    confirmText = '确定',
    onConfirm,
    onCancel,
  } = options;
  clear();
  // 创建遮罩层
  const overlay = document.createElement('div');
  lastEls[0] = overlay;
  overlay.style.position = 'fixed';
  overlay.style.top = '0';
  overlay.style.left = '0';
  overlay.style.width = '100vw';
  overlay.style.height = '100vh';
  overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
  overlay.style.display = 'flex';
  overlay.style.alignItems = 'center';
  overlay.style.justifyContent = 'center';
  overlay.style.zIndex = '9999';

  // 创建对话框容器
  const dialogBox = document.createElement('div');
  lastEls[1] = dialogBox;
  dialogBox.style.backgroundColor = '#fff';
  dialogBox.style.padding = '20px';
  dialogBox.style.borderRadius = '8px';
  dialogBox.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
  dialogBox.style.width = 'calc(100vw - 300px)';
  dialogBox.style.height = 'calc(100vh - 120px)';
  dialogBox.style.flexDirection = 'column';
  dialogBox.style.display = 'flex';
  dialogBox.style.maxWidth = '100%';
  dialogBox.style.textAlign = 'center';

  // 创建按钮容器
  const buttonContainer = document.createElement('div');
  buttonContainer.style.display = 'flex';
  buttonContainer.style.justifyContent = 'space-between';
  buttonContainer.style.marginTop = '10px';

  // 创建确定按钮
  const confirmButton = document.createElement('button');
  confirmButton.textContent = confirmText;
  confirmButton.style.flex = '1';
  confirmButton.style.marginRight = '10px';
  confirmButton.style.padding = '8px 16px';
  confirmButton.style.backgroundColor = '#409eff';
  confirmButton.style.color = '#fff';
  confirmButton.style.border = 'none';
  confirmButton.style.borderRadius = '4px';
  confirmButton.style.cursor = 'pointer';

  if (confirmButton) {
    confirmButton.onclick = () => {
      if (onConfirm) onConfirm();
      clear();
    };
  }

  // 将按钮添加到容器
  buttonContainer.appendChild(confirmButton);

  contentEl && dialogBox.appendChild(contentEl);
  dialogBox.appendChild(buttonContainer);

  // 将对话框添加到遮罩层
  overlay.appendChild(dialogBox);

  // 将遮罩层添加到页面
  document.body.appendChild(overlay);
};

export default dialog;
