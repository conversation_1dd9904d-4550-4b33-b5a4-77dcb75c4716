<template>
  <div class="attendance-device">
    <!-- 成功提示 -->
    <div
      id="succeed"
      style="
        margin: 0 auto;
        padding: 180px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        display: none;
      "
    >
      <div
        style="
          width: 60px;
          height: 60px;
          background: #4bca8b;
          border-radius: 50%;
          font-size: 48px;
          font-weight: 300;
          line-height: 60px;
          color: #fff;
          text-align: center;
        "
      >
        ✓
      </div>
      考勤设备联接信息已提交
      <p>请返回系统后点击[继续操作]即可</p>
    </div>

    <!-- 失败提示 -->
    <div
      id="failed"
      style="
        margin: 0 auto;
        padding: 180px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        display: none;
      "
    >
      <div
        style="
          width: 60px;
          height: 60px;
          background: #e6453c;
          border-radius: 50%;
          font-size: 48px;
          font-weight: 300;
          line-height: 60px;
          color: #fff;
          text-align: center;
        "
      >
        ✘
      </div>
      考勤设备联接信息提交失败
      <p>失败原因：{{ errorMessage }}</p>
      <p>
        您也可以尝试<a style="color: blue; cursor: pointer" @click="reload"
          >刷新</a
        >页面，重新提交
      </p>
    </div>

    <!-- 表单内容 -->
    <div id="content">
      <div class="title">考勤设备联接</div>
      <div class="container">
        <div style="margin: 0 auto">
          <!-- 考勤机信息 -->
          <div
            class="section"
            style="display: flex; gap: 20px; flex-direction: column"
          >
            <div class="input-box">
              <label>考勤机名称</label>
              <input
                v-model="form.name"
                type="text"
                placeholder="请输入"
                disabled
              />
            </div>
            <div class="input-box" v-show="false">
              <label>云平台地址</label>
              <input
                v-model="form.urlPrefix"
                type="text"
                placeholder="请输入"
                disabled
              />
            </div>
            <div class="input-box" v-if="isZK()">
              <label><span class="info-note">*</span>秘钥</label>
              <input
                v-model="form.staticKey"
                type="text"
                placeholder="请输入"
              />
            </div>
            <div class="input-box" v-if="isDL()">
              <label><span class="info-note">*</span>APP Key</label>
              <input
                v-model="form.platformAppKey"
                type="text"
                placeholder="请输入"
              />
            </div>
            <div class="input-box" v-if="isDL()">
              <label><span class="info-note">*</span>APP Secret</label>
              <input
                v-model="form.platformAppSecret"
                type="text"
                placeholder="请输入"
              />
            </div>
            <div
              class="checkbox-area"
              style="
                margin-left: 150px;
                display: flex;
                align-items: center;
                font-size: 14px;
              "
            >
              <input type="checkbox" id="agree" v-model="form.agreed" />
              <label for="agree"
                >阅读且同意阿拉钉
                <a
                  id="showProtocol"
                  @click="showProtocol()"
                  style="cursor: pointer"
                >
                  《用户服务协议》
                </a>
              </label>
            </div>
          </div>
        </div>

        <!-- 页脚 -->
        <div class="footer-text">
          注：当前系统页面来自于北京阿拉钉科技有限公司<br />
          COPYRIGHT © 北京阿拉钉科技有限公司 京ICP备19024749号-1 京公网安备
          11010502046854号
        </div>
      </div>

      <!-- 提交按钮 -->
      <div
        class="buttons"
        style="
          border-top: 1px solid #ccc;
          display: flex;
          align-items: center;
          height: 60px;
          justify-content: center;
        "
      >
        <button type="button" id="submit" @click="submit()">
          {{ submitting ? "提交中" : "提交" }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import message from "./message";
import dialog from "./dialog";
import { confAdd } from "./apis";

export default {
  data() {
    return {
      form: {
        agreed: false,
        name: this.getName(),
        platformType: this.getBelongPlatform(), // 平台类型：ZK-中控，DL-得力
        extNote: "", // 其他扩展数据
        urlPrefix: this.getURLPrefix(), // 平台调用地址前缀
        staticKey: "", // 中控平台-静态密钥
        platformAppKey: "", // 得力平台-appKey
        platformAppSecret: "", // 得力平台-appSecret
        merchantId: "",
        opToken: "",
      },
      submitting: false,
      errorMessage: "",
    };
  },
  methods: {
    reload() {
      window.location.reload();
    },
    // 提交表单
    async submit() {
      if (this.submitting) {
        return;
      }

      if (!this.form.agreed) {
        message({
          type: "error",
          message: "请阅读且同意阿拉钉用户服务协议",
          duration: 3000, // 消息显示的持续时间，单位是毫秒
        });
        return;
      }

      const params = new URLSearchParams(window.location.search);
      const token = params.get("token");
      if (!token) {
        message({
          type: "error",
          message: "没有操作token",
          duration: 3000, // 消息显示的持续时间，单位是毫秒
        });
        return;
      }
      this.form.opToken = token;

      this.submitting = true;
      setTimeout(() => (this.submitting = false), 5000);

      if (this.isZK() && !this.form.staticKey.trim()) {
        message({
          type: "error",
          message: "请输入中控平台-静态密钥",
          duration: 3000,
        });
        this.submitting = false;
        return;
      }

      // 校验得力平台字段
      if (this.isDL()) {
        if (!this.form.platformAppKey.trim()) {
          message({
            type: "error",
            message: "请输入APP Key",
            duration: 3000,
          });
          this.submitting = false;
          return;
        }
        if (!this.form.platformAppSecret.trim()) {
          message({
            type: "error",
            message: "请输入APP Secret",
            duration: 3000,
          });
          this.submitting = false;
          return;
        }
      }
      try {
        this.form.coId = this.merchantID();
        const [err, r] = await confAdd(this.form);
        if (err) {
          message({
            type: "error",
            message: err,
            duration: 3000,
          });
          return;
        }

        document.getElementById("content").style.display = "none";
        document.getElementById("succeed").style.display = "flex";
      } catch (e) {
        this.errorMessage = e.message;
        document.getElementById("content").style.display = "none";
        document.getElementById("failed").style.display = "flex";
      }
    },
    query() {
      const params = new URLSearchParams(window.location.search);
      return params;
    },
    isZK() {
      return this.getBelongPlatform() === "ZK";
    },
    isDL() {
      return this.getBelongPlatform() === "DL";
    },
    merchantID() {
      return this.query().get("merchantID");
    },
    getBelongPlatform() {
      if (this.query().get("belongPlatform").includes("ZK")) {
        return "ZK";
      }
      if (this.query().get("belongPlatform").includes("DL")) {
        return "DL";
      }

      throw new Error("not supported platform");
    },
    getName() {
      return this.query().get("zoneName");
    },
    getURLPrefix() {
      if (this.isDL()) {
        return "https://v2-api.delicloud.com";
      }

      return "http://192.168.100.93:8080";
    },
    showProtocol() {
      const iframe = document.createElement("iframe");
      iframe.style.border = "none";
      iframe.style.width = "100%";
      iframe.style.height = "100%";
      const href = window.location.origin + window.location.pathname;
      const index = href.indexOf("/hrsaas/xst");
      var prefix = "";
      if (index !== -1) {
        prefix = href.substring(0, index) + "/hrsaas/xst";
      } else {
        prefix = window.location.origin;
      }
      const src = prefix + "/static/userServiceProtocol.html";
      iframe.src = src;

      dialog({
        confirmText: "我已阅读",
        contentEl: iframe,
      });
    },
  },
};
</script>

<style>
/* 通用样式 */
body {
  font-family: Arial, sans-serif;
  background-color: #fff;
  margin: 0;
  padding: 0;
  color: #333;
  overflow: hidden;
}

.container {
  margin: 0 auto;
  padding: 20px;
  height: calc(100vh - 170px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.title {
  height: 60px;
  display: flex;
  align-items: center;
  padding-left: 16px;
  text-align: left;
  border-bottom: 1px solid #ccc;
  font-size: 18px;
  font-weight: bold;
}

.section {
  margin-bottom: 20px;
}

.section h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
}

.section .input-box {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.section .input-box label {
  width: 150px;
  font-size: 14px;
  color: #333;
  text-align: right;
  margin-right: 5px;
}

.section .input-box input {
  width: 300px;
  padding: 5px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.footer-text {
  font-size: 12px;
  text-align: center;
  color: #666;
  margin-top: 30px;
  line-height: 1.5;
}

.buttons button {
  padding: 10px 30px;
  margin: 0 10px;
  border: none;
  border-radius: 4px;
  background-color: #46a0fc;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
}

.buttons button:hover {
  opacity: 0.9;
}
</style>
