var lastEl = null;
const message = (options) => {
  const { type = 'info', message = '', duration = 3000 } = options;
  if (lastEl) {
    lastEl.remove();
  }
  // 创建消息提示框容器
  const box = document.createElement('div');
  lastEl = box;
  box.textContent = message;

  // 设置消息提示框的基础样式
  box.style.position = 'fixed';
  box.style.top = '20px';
  box.style.left = '50%';
  box.style.transform = 'translateX(-50%)';
  box.style.padding = '10px 20px';
  box.style.color = 'white';
  box.style.fontSize = '14px';
  box.style.borderRadius = '4px';
  box.style.zIndex = '1000';
  box.style.opacity = '1';
  box.style.transition = 'opacity 0.5s ease, top 0.5s ease';

  // 根据不同的类型设置不同的背景颜色
  switch (type) {
    case 'success':
      box.style.backgroundColor = '#67c23a'; // 绿色
      break;
    case 'warning':
      box.style.backgroundColor = '#e6a23c'; // 黄色
      break;
    case 'error':
      box.style.backgroundColor = '#f56c6c'; // 红色
      break;
    default:
      box.style.backgroundColor = '#409eff'; // 蓝色
  }

  // 将消息提示框添加到页面中
  document.body.appendChild(box);

  // 设置自动关闭
  setTimeout(() => {
    // 添加淡出效果
    box.style.opacity = '0';
    box.style.top = '0px';

    // 动画结束后移除元素
    box.addEventListener('transitionend', () => {
      box.remove();
    });
  }, duration);
};

export default message;
