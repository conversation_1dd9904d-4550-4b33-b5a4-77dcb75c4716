import router from "@/router";
import topHeader from "@/components/basic/Header";
import newHeader from "@/components/basic/newHeader";
import { mapState } from "vuex";
import * as AT from "@/store/actionTypes";
import {getToken} from "@olading/olading-business-ui";
import {
    privilegeVOList,
    privilegeGroupTreeVO,
    attendancelistDemo,
    performanceList,
    electPayList,
} from "@/utils/constData";
import { mainMenu_base } from "@/assets/js/utils/mainMenu";
const hideSideMenu = [
    "/my-performance/detail", //员工端详情
    "/my-performance/comp-detail", //个人绩效档案详情
    "/launch-assessment/launch-detail", //考核确认详情
    "/performance/setting", //新增考核计划
    "/performance/checkSetting", //查看考核计划
    "/performance/checkPlanDetail", //考核计划详情
    "/performance/checkDetail", //考核详情
    "/launch-assessment/detail", //考核确认详情
];
export default {
    components: {
        topHeader,
        newHeader,
    },
    provide() {
        return {
            reload: this.reload,
        };
    },
    data() {
        return {
            unlock: false,
            isRouterAlive: true,
            routerList: router.options.routes, //整个路由列表
            mainMenu: [],
            insertNavList: [
                {
                    businessCode: null,
                    name: "首页",
                    url: "/home",
                    index: 0,
                    icon: "iconshouye",
                },
            ],
            authPath: this.$route.path,
            userType: "",
            hidenAside: [
                "/login",
                "/selectServer",
                "/salaryCheck",
                "/salaryCal/payRoll",
                "/tax/infoCollection",
                "/addEmployee",
                "/employeeDetail",
                "/salaryCal/salaryItemDetail",
                "/attritionDetail",
                "/contract-manage/create-success",
            ],
            isMananger: false, //是否企业管理员
        };
    },
    computed: {
        ...mapState({
            isShowApp: (state) => state.isShowApp,
            isAuthorize: (state) => state.isAuthorize,
            token: (state) => state.token || state.loginStore.token,
        }),
        //显示菜单
        showSideMenu() {
            console.log(this.$route.path);
            return !hideSideMenu.includes(this.$route.path);
        },
    },
    created() {
        this.reloadFn();
        //清空分页设置
        sessionStorageOther.setItem("pagination", null);
    },
    mounted() {
        this.getInfor();
    },
    methods: {
        onLoad() {
          this.unlock = false;
            return new Promise((resolve, reject) => {
                let interval = setInterval(() => {
                  let privilegeVoList = JSON.parse(sessionStorage.getItem('xst/vuex')).privilegeVoList;
                  if (privilegeVoList.length > 0) {
                    clearInterval(interval);
                    setTimeout(() => {
                      this.unlock = true;
                      resolve(privilegeVoList);
                    }, 800);
                  }
                }, 100);
            });
        },
        getUserType() {
            this.$store
                .dispatch("homePageStore/actionGetUserInfo", {
                    token: getToken(),
                })
                .then((res) => {
                    if (res.success) {
                        this.userType = res.data ? res.data.userType : "";
                        this.$store.commit(AT.USERTYPE, this.userType);
                        if (this.userType === "PERSONAL") {
                            //所有企业信息
                            this.getTaxSubjectInfoList();
                            //设置侧边栏
                            this.setAside();
                        }
                    }
                });
        },
        handleIsMananger() {
            let _this = this;
            return new Promise((resolve, reject) => {
                _this.$store.dispatch("actionIsMananger").then((res) => {
                    _this.isMananger = res.data.mananger;
                    _this.$store.commit(AT.SET_ISMANAGER, _this.isMananger);
                    resolve(_this.isMananger);
                });
            });
        },
        async getProductEdition() {
            let res = await this.$store.dispatch("actionGetIsPaid");
            if (res.success) {
                this.$store.commit(AT.SET_PRODUCTEDITION, res.data);
            }
        },
        setAside() {
            if (!this.hidenAside.includes(this.$route.path)) {
                this.$store.commit(AT.SHOWAPP, true); //如用户手动改变路由， 需将full-screeen关闭
            }
            this.handleIsMananger().then(() => {
                //权限列表
                this.$store.dispatch("actionUserPrivilege").then((res) => {
                    let mainMenu = [];
                    if (this.isMananger) {
                        this.$store.commit(AT.SET_ISBASEMENU, true);
                        // 银行版去掉绩效
                        if (
                            window.env.server_env === "boc" ||
                            window.env.server_env === "cgb"
                        ) {
                            const index = mainMenu_base.findIndex(
                                (value) => value.businessCode === "salary.performance"
                            );
                            mainMenu_base.splice(index, 1);
                        }
                        mainMenu = mainMenu_base;
                    } else {
                        let arr = res.data.privilegeGroupTreeVO
                            ? res.data.privilegeGroupTreeVO.children
                            : null;
                        arr &&
                        arr.forEach((item) => {
                            item.children &&
                            item.children.forEach((it) => {
                                mainMenu.push(it);
                            });
                        });
                    }
                    this.mainMenu = mainMenu;
                    //把初始化菜单放到最后
                    let filterMenu = mainMenu.filter((item) => item.name != "初始化设置");
                    let initMenu = mainMenu.filter((item) => item.name === "初始化设置");
                    this.mainMenu = filterMenu.concat(initMenu);
                    // this.mainMenu = this.mainMenu.concat(performanceList);
                    // this.mainMenu = this.mainMenu.concat(electPayList);

                    let privilegeVOList =
                        res.data.privilegeVOList &&
                        res.data.privilegeVOList.map((it) => it.code);
                    this.privilegeVOList = privilegeVOList ? privilegeVOList : [];
                    this.$store.commit(AT.SET_PRIVILIGEVOLiST, this.privilegeVOList);
                    // this.$store.commit(AT.MAIN_MENU, this.mainMenu);
                });
            });
        },
        getTaxSubjectInfoList() {
            this.$store.dispatch("taxPageStore/actionTaxSubjectList").then((res) => {
                if (res.success) {
                    let taxSubjectInfoList = [
                        {
                            taxSubId: "",
                            taxSubName: "全部",
                            taxEnableYn: true,
                            employeeEnableYn: true,
                            contractEnableYn: true,
                        },
                    ].concat(res.data);
                    this.$store.commit(AT.SET_TAXSUBJECTINFOLIST, taxSubjectInfoList);
                }
            });
        },
        getCountriesList() {
            this.$store.dispatch("payMasterStore/actionGetCountries").then((res) => {
                this.$store.commit(AT.SET_COUNTRYLIST, res.data);
            });
        },
        getBankList() {
            this.$store.dispatch("payMasterStore/actionGetBanks").then((res) => {
                this.$store.commit(AT.SET_BANKLIST, res.data);
            });
        },
        getCityList() {
            this.$store.dispatch("payMasterStore/actionGetCity").then((res) => {
                this.$store.commit(AT.SET_CITYLIST, res.data);
            });
        },
        getProvinceList() {
            this.$store.dispatch("homePageStore/actioncGetProvince");
        },
        //获取区域列表
        getAreaList() {
            this.$store.dispatch("taxPaidStore/actionGetAreaList").then((res) => {
                if (res.success) {
                    let data = res.data.filter((item) => item.isenable);
                    let areaList = [{ id: "", areaName: "全部区域" }].concat(data);
                    this.$store.commit(AT.SET_AREALIST, areaList);
                }
            });
        },
        //获取部门列表
        async getDepartmentList() {
            await this.$store.dispatch("homePageStore/actioncGetDepartmentList");
        },
        reloadFn() {
            const authList = [
                "/authorizeZx",
                "/authorizeZx/agreement",
                "/selectServer",
            ];
            if (!authList.includes(this.authPath)) {
                //  （如果是个人） 获取 公司名称列表  侧边栏权限
                this.getUserType();

                // 国籍列表
                this.getCountriesList();

                // 银行列表
                this.getBankList();

                // 城市列表
                this.getCityList();

                //省份城市列表
                this.getProvinceList();

                //区域列表
                this.getAreaList();

                //部门列表
                this.getDepartmentList();

                //判断是免费版还是付费版
                this.getProductEdition();
            }
        },
        reload() {
            this.isRouterAlive = false;
            this.reloadFn();
            this.$nextTick(() => {
                this.isRouterAlive = true;
            });
        },
        getInfor() {
            this.$store.dispatch("selectServerStore/actionBindInfo").then((res) => {
                this.$store.commit(AT.SETBINDINFO, res.data.relevantMerchantVos);
                this.$store.commit(AT.SETHOMEINFO, res.data);
            });
        },
    },
};
