/**
 * @module attendDefaultConfig
 * 
 * @description
 * 考勤组打卡信息page初始化时默认显示的配置。
 * 
 * 同一时刻有多个班次考勤信息可以同时打卡，默认显示哪个班次。默认显示的班次切换按钮。这个需求产品可能会随时修改。
 */

const PREVIOUS_SHIFT = 0
const TODAY_SHIFT = 1

const defaultIndex = PREVIOUS_SHIFT;

export default {
  getDefaultIndex() {
    return defaultIndex;
  },
  /**
   * 
   * @param {AttendNetResModel} attendNetResModel 
   * 
   * @returns 返回默认班次的考勤数据，如果只有一个，直接返回。
   */
  getDefaultNetAttend(attendNetResModel) {
    const netAttendList = attendNetResModel?.getNetAttendList();
    if (!netAttendList || netAttendList.length < 1) {
      console.warn("班次考勤信息不能为空，请注意！！！");
      return
    }
    if (netAttendList.length == 1) {
      return netAttendList[0];
    }
    return netAttendList[defaultIndex];
  }
}