/**
 * @module attendGroup
 * 
 * @description 
 * 考勤组相关的逻辑 
 */

import { isObject } from 'kit/helpers/index'
import { DateCompare } from 'kit/helpers/dateUtils'

/**
 * 上下班跨天的定义：工作日24小时后为跨天。
 * 
 * 如果日期是字符串，可转化有效日期的字符串，格式如2024-04-19 00:00:00
 * @param {String | Date} workDate 考勤组的工作日信息。
 * @param {String | Date} time
 */
export function isCrossingDay(workDate, time) {
  if (!workDate || !time) {
    return false;
  }

  // 计算下一个工作日的开始时间。
  const tomorrowAtMidnight = new Date(workDate);
  tomorrowAtMidnight.setDate(tomorrowAtMidnight.getDate());
  tomorrowAtMidnight.setHours(23, 59, 59, 999);

  return new DateCompare(time, tomorrowAtMidnight).isAfter();
}

class AttendGroup {
  constructor(attendGroup) {
    if (!isObject(attendGroup)) {
      throw new Error('attendGroup is not an object')
    }
    for (var key in attendGroup) {
      this[key] = attendGroup[key]
    }
    // this.allowedOutside = true
  }
  isFreeMode() {
    return this.attendType === 'FREE'
  }
  name() {
    return this.agName
  }
  opened() {
    return this.approvalSwitch
  }
  //具有关联的审批
  hadApprovals() {
    return this.approvalResultList && this.approvalResultList.length
  }
  //具有指定的关联流程
  hadApprovalType(type) {
    if (!this.hadApprovals()) {
      return false
    }
    //FIX_ATTENDANCE 补卡  ASK_FOR_LEAVE 请假 BUSINESS_TRIP 出差 LEGWORK 外出 OVERTIME加班
    return this.getApprovalByType(type)
  }
  getApprovalByType(type) {
    if (!this.hadApprovals()) {
      return null
    }
    return this.approvalResultList.find(item => item.bizSuitType == type)
  }
  // 不考勤
  isNoJoinAttend() {
    return this.uncheckedGroup
  }
}

export default AttendGroup
