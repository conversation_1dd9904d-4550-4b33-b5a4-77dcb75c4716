{"attendId": 52, "coId": 328, "agName": "人脸真人验证", "attendType": "FIX", "peopleCount": 1, "allowedOutside": true, "outsideApproval": false, "outsideRemark": true, "outsideImages": true, "supplementRuleId": 24, "supplementRule": {"id": 24, "coId": 328, "ruleName": "默认补卡规则", "allowSupplement": true, "allowTimesLimit": false, "allowDateLimit": false, "timesLimit": 5, "dateLimit": 31, "isDefault": true, "isDeleted": false, "createdTime": "2022-05-16 15:31:07", "updatedTime": "2022-05-16 15:31:07"}, "attendPlaceList": [{"id": 143, "coId": 328, "attendId": 52, "placeName": "高碑店乡半壁店村惠河南街1111号龙源通惠大厦", "placeAlias": "", "longitude": 116.510768, "latitude": 39.904349, "errorRange": 100, "isDeleted": false, "createdTime": "2022-05-16 15:41:33", "updatedTime": "2022-05-16 15:41:33"}], "attendWifiList": [], "attendWorkingShiftResultList": [{"id": 374, "coId": 328, "attendId": 52, "shiftOrder": 0, "isRestDay": false, "workingShiftId": 1447, "isDeleted": false, "createdTime": "2022-05-16 15:42:00", "updatedTime": "2022-05-16 15:42:00", "workingShiftResult": {"id": 1447, "coId": 328, "groupName": "默认班次", "shiftCount": 1, "shiftType": "FIX", "workingMinutes": 540, "workingSwitch": false, "isDefault": true, "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06", "workingShiftDetailResultList": [{"id": 962, "coId": 328, "workingShiftId": 1447, "orderNo": 1, "shiftType": "FIX", "needClockIn": false, "workingBegin": "0900", "workingBeginAdvance": 60, "workingBeginLate": 0, "allowBeginFlex": false, "workingBeginFlexHour": 0, "workingBeginFlex": 0, "workingBeginAbsent": 30, "needClockOut": false, "workingEnd": "1800", "workingEndAdvance": 0, "workingEndLate": 360, "allowEndFlex": false, "workingEndFlexHour": 0, "workingEndFlex": 0, "workingEndAbsent": 30, "workingHours": 540, "allowRest": false, "restBegin": "1200", "restEnd": "1300", "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06"}]}}, {"id": 375, "coId": 328, "attendId": 52, "shiftOrder": 1, "isRestDay": false, "workingShiftId": 1447, "isDeleted": false, "createdTime": "2022-05-16 15:42:00", "updatedTime": "2022-05-16 15:42:00", "workingShiftResult": {"id": 1447, "coId": 328, "groupName": "默认班次", "shiftCount": 1, "shiftType": "FIX", "workingMinutes": 540, "workingSwitch": false, "isDefault": true, "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06", "workingShiftDetailResultList": [{"id": 962, "coId": 328, "workingShiftId": 1447, "orderNo": 1, "shiftType": "FIX", "needClockIn": false, "workingBegin": "0900", "workingBeginAdvance": 60, "workingBeginLate": 0, "allowBeginFlex": false, "workingBeginFlexHour": 0, "workingBeginFlex": 0, "workingBeginAbsent": 30, "needClockOut": false, "workingEnd": "1800", "workingEndAdvance": 0, "workingEndLate": 360, "allowEndFlex": false, "workingEndFlexHour": 0, "workingEndFlex": 0, "workingEndAbsent": 30, "workingHours": 540, "allowRest": false, "restBegin": "1200", "restEnd": "1300", "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06"}]}}, {"id": 376, "coId": 328, "attendId": 52, "shiftOrder": 2, "isRestDay": false, "workingShiftId": 1447, "isDeleted": false, "createdTime": "2022-05-16 15:42:00", "updatedTime": "2022-05-16 15:42:00", "workingShiftResult": {"id": 1447, "coId": 328, "groupName": "默认班次", "shiftCount": 1, "shiftType": "FIX", "workingMinutes": 540, "workingSwitch": false, "isDefault": true, "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06", "workingShiftDetailResultList": [{"id": 962, "coId": 328, "workingShiftId": 1447, "orderNo": 1, "shiftType": "FIX", "needClockIn": false, "workingBegin": "0900", "workingBeginAdvance": 60, "workingBeginLate": 0, "allowBeginFlex": false, "workingBeginFlexHour": 0, "workingBeginFlex": 0, "workingBeginAbsent": 30, "needClockOut": false, "workingEnd": "1800", "workingEndAdvance": 0, "workingEndLate": 360, "allowEndFlex": false, "workingEndFlexHour": 0, "workingEndFlex": 0, "workingEndAbsent": 30, "workingHours": 540, "allowRest": false, "restBegin": "1200", "restEnd": "1300", "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06"}]}}, {"id": 377, "coId": 328, "attendId": 52, "shiftOrder": 3, "isRestDay": false, "workingShiftId": 1447, "isDeleted": false, "createdTime": "2022-05-16 15:42:00", "updatedTime": "2022-05-16 15:42:00", "workingShiftResult": {"id": 1447, "coId": 328, "groupName": "默认班次", "shiftCount": 1, "shiftType": "FIX", "workingMinutes": 540, "workingSwitch": false, "isDefault": true, "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06", "workingShiftDetailResultList": [{"id": 962, "coId": 328, "workingShiftId": 1447, "orderNo": 1, "shiftType": "FIX", "needClockIn": false, "workingBegin": "0900", "workingBeginAdvance": 60, "workingBeginLate": 0, "allowBeginFlex": false, "workingBeginFlexHour": 0, "workingBeginFlex": 0, "workingBeginAbsent": 30, "needClockOut": false, "workingEnd": "1800", "workingEndAdvance": 0, "workingEndLate": 360, "allowEndFlex": false, "workingEndFlexHour": 0, "workingEndFlex": 0, "workingEndAbsent": 30, "workingHours": 540, "allowRest": false, "restBegin": "1200", "restEnd": "1300", "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06"}]}}, {"id": 378, "coId": 328, "attendId": 52, "shiftOrder": 4, "isRestDay": false, "workingShiftId": 1447, "isDeleted": false, "createdTime": "2022-05-16 15:42:00", "updatedTime": "2022-05-16 15:42:00", "workingShiftResult": {"id": 1447, "coId": 328, "groupName": "默认班次", "shiftCount": 1, "shiftType": "FIX", "workingMinutes": 540, "workingSwitch": false, "isDefault": true, "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06", "workingShiftDetailResultList": [{"id": 962, "coId": 328, "workingShiftId": 1447, "orderNo": 1, "shiftType": "FIX", "needClockIn": false, "workingBegin": "0900", "workingBeginAdvance": 60, "workingBeginLate": 0, "allowBeginFlex": false, "workingBeginFlexHour": 0, "workingBeginFlex": 0, "workingBeginAbsent": 30, "needClockOut": false, "workingEnd": "1800", "workingEndAdvance": 0, "workingEndLate": 360, "allowEndFlex": false, "workingEndFlexHour": 0, "workingEndFlex": 0, "workingEndAbsent": 30, "workingHours": 540, "allowRest": false, "restBegin": "1200", "restEnd": "1300", "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06"}]}}, {"id": 379, "coId": 328, "attendId": 52, "shiftOrder": 5, "isRestDay": false, "workingShiftId": 1447, "isDeleted": false, "createdTime": "2022-05-16 15:42:00", "updatedTime": "2022-05-16 15:42:00", "workingShiftResult": {"id": 1447, "coId": 328, "groupName": "默认班次", "shiftCount": 1, "shiftType": "FIX", "workingMinutes": 540, "workingSwitch": false, "isDefault": true, "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06", "workingShiftDetailResultList": [{"id": 962, "coId": 328, "workingShiftId": 1447, "orderNo": 1, "shiftType": "FIX", "needClockIn": false, "workingBegin": "0900", "workingBeginAdvance": 60, "workingBeginLate": 0, "allowBeginFlex": false, "workingBeginFlexHour": 0, "workingBeginFlex": 0, "workingBeginAbsent": 30, "needClockOut": false, "workingEnd": "1800", "workingEndAdvance": 0, "workingEndLate": 360, "allowEndFlex": false, "workingEndFlexHour": 0, "workingEndFlex": 0, "workingEndAbsent": 30, "workingHours": 540, "allowRest": false, "restBegin": "1200", "restEnd": "1300", "isDeleted": false, "createdTime": "2022-05-16 15:31:06", "updatedTime": "2022-05-16 15:31:06"}]}}, {"id": 380, "coId": 328, "attendId": 52, "shiftOrder": 6, "isRestDay": true, "workingShiftId": -1, "isDeleted": false, "createdTime": "2022-05-16 15:42:00", "updatedTime": "2022-05-16 15:42:00"}, {"id": 381, "coId": 328, "attendId": 52, "shiftOrder": 7, "isRestDay": true, "workingShiftId": -1, "isDeleted": false, "createdTime": "2022-05-16 15:42:00", "updatedTime": "2022-05-16 15:42:00"}], "isAdministrator": true, "administratorList": [{"id": 10067, "name": "泽阳"}], "approvalSwitch": false, "approvalResultList": [], "enableFaceOcr": true, "verifyFaceOcr": true}