import formatDateTime from "../../formatters/dateTime";

/**
 * @module attendNetResModel
 * 
 * @description
 * 将attend.vue考勤组page中，初始化时依赖queryAttendanceGroup接口返回的信息，转换成page组件内部方便实用的数据。
 */

// import mockAttendNetRes from "kit/mockData/queryAttendanceGroup3.json"
class AttendNetResModel {
  constructor(attendNetRes) {
    this.setNewAttendNetRes(attendNetRes);
  }

  setMock() {
    this.setNewAttendNetRes(mockAttendNetRes);
  }

  setNewAttendNetRes(attendNetRes) {
    if(!attendNetRes) return;

    this.previousNetAttend = attendNetRes?.data?.yesterdayQueryAttendanceVo;
    this.todayNetAttend = attendNetRes?.data;
  }

  /**
   * 当前班次与上一个班次，是否有相交的打卡信息。即当前时刻可以打当前班次的，也可以打上一个班次的。
   */
  hasTwoCurrentSign() {
    return this.getPreviousAttend();
  }

  getCurrentNetAttend() {
    return this.todayNetAttend;
  }

  getPreviousNetAttend() {
    return this.previousNetAttend;
  }

  /**
   * 
   * @returns 返回当前可打卡班次的考勤信息，按照时间顺序排列
   */
  getNetAttendList() {
    const list = [];
    if (this.getPreviousNetAttend()) {
      list.push(this.getPreviousNetAttend())
    }
    if (this.getCurrentNetAttend()) {
      list.push(this.getCurrentNetAttend())
    }

    return list;
  }

  /**
   * 
   * @param {*} netAttend 当前班次的考勤信息
   * @returns 班次工作日
   * 
   * 注意：！！！ 
   * 这里其实有些问题，工作日属性只和班次实例有关。和当前是否可以打卡无关。
   * 需要接口进行修改，目前接口不想改了。
   */
  getWorkDate(netAttend) {
    try {
      if( netAttend.currentSignVo &&  netAttend.currentSignVo.workDate){
        return netAttend.currentSignVo?.workDate?.split(/\s+/g)[0];
      }
   
      return formatDateTime({ format: 'yyyy-MM-dd' })
    } catch(e) {
      console.error("请求工作日异常:",e, netAttend);
      return '-'
    }
  }
}

export default AttendNetResModel