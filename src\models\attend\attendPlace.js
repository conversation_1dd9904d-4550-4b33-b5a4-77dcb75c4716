import { isObject } from 'kit/helpers/index'
// {
//   "id": 143,
//   "coId": 328,
//   "attendId": 52,
//   "placeName": "高碑店乡半壁店村惠河南街1111号龙源通惠大厦",
//   "placeAlias": "",
//   "longitude": 116.510768,
//   "latitude": 39.904349,
//   "errorRange": 100,
//   "isDeleted": false,
//   "createdTime": "2022-05-16 15:41:33",
//   "updatedTime": "2022-05-16 15:41:33"
// }
export class AttendPlace {
  constructor(attendPlace) {
    if (!isObject(attendPlace)) {
      throw new Error('attendPlace is not an object')
    }
    for (var key in attendPlace) {
      this[key] = attendPlace[key]
    }
  }
}

export default AttendPlace
