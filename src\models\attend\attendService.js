import formatDateTime from 'kit/formatters/dateTime'
import { getYMD } from 'kit/helpers/dateUtils'

const lessThan = (date1, date2) => {
  if(typeof date1 === "string") {
    date1 = date1.replace(/-/g, '/')
  }
  if(typeof date2 === "string") {
    date2 = date2.replace(/-/g, '/')
  }

  try {
    const d1 = new Date(date1)
    const d2 = new Date(date2)
    
    return d1.getTime() <= d2.getTime()
  } catch (err) {
    alert('传入了错误的日期格式')
  }
}

// 考勤服务
const AttendService = {
  buildUpdateCheckTimeCommand(attendGroup, location, checkInRecord) {
    var status = ''
    //@todo 查一下含义 workingStatus
    var workingStatus = false
    if (checkInRecord.signType == 'TO_WORK') {
      workingStatus = lessThan(checkInRecord.workingBeginLate, new Date())
    } else {
      workingStatus = lessThan(new Date(), checkInRecord.workingEndAdvance)
    }
    if (workingStatus) {
      status = checkInRecord.signType == 'TO_WORK' ? 'BE_LATE' : 'LEAVE_EARLIER'
    }

    // 非工作日或自由打卡总是为普通
    if (
      !checkInRecord.isWorkday ||
      checkInRecord.shiftType === 'FREE' ||
      !workingStatus
    ) {
      status = 'NORMAL'
    }

    // 外勤打卡
    if (
      attendGroup.allowedOutside &&
      location.isOutSide &&
      checkInRecord.isWorkday
    ) {
      status = 'OUTSIDE_ATTEND'
    }

    const command = {
      workingShiftId: checkInRecord.attendWorkingShiftId,
      workingShiftDetailId: checkInRecord.workingShiftDetailId,
      signType: checkInRecord.signType,
      attendId: checkInRecord.attendId, 
      coId: checkInRecord.coId, 
      taxsubId: checkInRecord.taxsubId, 
      deptId: checkInRecord.deptId, 
      status: status, 
      signTime: formatDateTime({ format: 'yyyy-MM-dd HH:mm:ss' }), 
      recordAddress: location.standardAddress,
      latitude: location.point.lat, 
      longitude: location.point.lng, 
      signDescription: checkInRecord.signDescription,
      signImages: checkInRecord.signImages,
      workDate: getYMD(checkInRecord.workDate)
    }

    return command
  },
  buildCheckInCommand(employee, attendGroup, location, checkInModel) {
    if (attendGroup.isFreeMode()) {
      return this._buildCheckInFreeModeCommand(
        employee,
        attendGroup,
        location,
        checkInModel
      )
    }

    return this._buildCheckInNotFreeModeCommand(
      employee,
      attendGroup,
      location,
      checkInModel
    )
  },
  _buildCheckInFreeModeCommand(employee, attendGroup, location, checkInModel) {
    var status = 'NORMAL'

    if (
      location.isOutSide &&
      attendGroup.allowedOutside &&
      checkInModel.isWorkDay
    ) {
      status = 'OUTSIDE_ATTEND'
    }

    if (!checkInModel.isWorkDay) {
      status = 'NORMAL'
    }

    return this._buildCheckInCommand(
      employee,
      attendGroup,
      location,
      checkInModel,
      status
    )
  },
  _buildCheckInNotFreeModeCommand(
    employee,
    attendGroup,
    location,
    checkInModel
  ) {
    var status = 'NORMAL'

    if (checkInModel.isWorkingDuration()) {
      if (checkInModel.goWork() && checkInModel.late()) {
        status = 'BE_LATE'
      }

      if(checkInModel.offWork() && checkInModel.leaveEarlier()) {
        status = 'LEAVE_EARLIER'
      }
      

      if (checkInModel.goWork() && checkInModel.leaveEarlier()) {
        status = 'LEAVE_EARLIER'
      }
    }

    if (
      location.isOutSide &&
      attendGroup.allowedOutside &&
      checkInModel.isWorkDay
    ) {
      status = 'OUTSIDE_ATTEND'
    }
    //休息日 可以普通打卡
    if (!checkInModel.isWorkDay) {
      status = 'NORMAL'
    }

    return this._buildCheckInCommand(
      employee,
      attendGroup,
      location,
      checkInModel,
      status
    )
  },
  _buildCheckInCommand(employee, attendGroup, location, checkInModel, status) {
    const command = {
      attendId: attendGroup.attendId,
      workingShiftId: checkInModel.workingShiftId,
      coId: employee.compId,
      taxsubId: employee.taxSubId,
      deptId: employee.departmentId,
      workingShiftDetailId: checkInModel.workingShiftDetailId,
      signType: checkInModel.signTypeEnum
        ? checkInModel.signTypeEnum
        : 'TO_WORK',
      status: status,
      signTime: formatDateTime(
        {
          format: 'yyyy-MM-dd HH:mm:ss'
        },
        new Date()
      ),
      recordAddress: location.standardAddress,
      latitude: location.point.lat,
      longitude: location.point.lng,
      signDescription: checkInModel.comment,
      //早退打卡 还需要图片
      signImages: JSON.stringify(checkInModel.images),
      workDate: getYMD(checkInModel.workDate),
    }

    return command
  }
}

export default AttendService
