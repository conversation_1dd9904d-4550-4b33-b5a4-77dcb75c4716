import { isObject } from 'kit/helpers/index'
import DateTimeService from './dateTimeService'
import { isCrossingDay } from './attendGroup'
// {
//   "workingBegin": "2023-07-28 09:00:00",
//   "workingBeginAdvance": "2023-07-28 08:00:00",
//   "workingBeginLate": "2023-07-28 09:01:00",
//   "workingBeginAbsent": "2023-07-28 09:31:00",
//   "workingEnd": "2023-07-28 18:00:00",
//   "workingEndAdvance": "2023-07-28 18:00:00",
//   "workingEndLate": "2023-07-29 00:01:00",
//   "workingEndAbsent": "2023-07-28 17:30:00",
//   "signTypeEnum": "TO_WORK",
//   "workingShiftId": 1447,
//   "workingShiftDetailId": 962,
//   "isWorkDay": true,
//   "orderNo": 1
// }
class CheckIn {
  constructor(currentSignVo) {
    if (!isObject(currentSignVo)) {
      throw new Error('currentSignVo is not an object')
    }
    for (var key in currentSignVo) {
      this[key] = currentSignVo[key]
    }

    //早退打卡需要3张图片
    this.images = []
    //早退打卡备注
    this.comment = ''
  }
  //上班
  goWork() {
    return this.signTypeEnum === 'TO_WORK'
  }
  //下班
  offWork() {
    return this.signTypeEnum === 'FROM_WORK'
  }
  workTime() {
    return this.signTypeEnum == 'TO_WORK'
      ? this.workingBegin.substr(-8, 5)
      : this.workingEnd.substr(-8, 5)
  }

  isShowCrossingDayWorkTime() {
    let workTime = '';
    if (this.goWork()) {
      workTime = this.workingBegin;
    } else if (this.offWork()) {
      workTime = this.workingEnd;
    }

    return isCrossingDay(this.workDate, workTime)
  }

  workingNotBegin() {
    return DateTimeService.lessThan(new Date(), this.workingBeginAdvance)
  }
  workingIsEnd() {
    return DateTimeService.lessThan(this.workingEndLate, new Date())
  }
  //外勤
  outSide(attendGroup, location) {
    if (location.isOutSide && attendGroup.allowedOutside && this.isWorkDay) {
      return true
    }

    return false
  }
  //上班迟到了
  late() {
    if (this.offWork() || !this.isWorkDay) {
      return false
    }

    return DateTimeService.lessThan(this.workingBeginLate, new Date())
  }
  leaveEarlier() {
    if (this.goWork()) {
      return false
    }
    return DateTimeService.lessThan(new Date(),this.workingEndAdvance)
  }

  //当前地点是否允许打卡
  locationAllowCheckIn(attendGroup, location) {
    //工日 且不在可打卡范围 且不允许外勤打卡
    if (!attendGroup.allowedOutside && location.isOutSide && this.isWorkDay) {
      return false
    }

    //处于工作状态
    if (this.isWorkingDuration()) {
      return true
    }

    return false
  }
  //当前时间允许打卡
  timeAllowCheckIn() {
    if (this.isWorkDay && !this.isWorkingDuration()) {
      return false
    }

    return true
  }
  //是正常工作时间
  isWorkingDuration() {
    if (this.goWork() && this.workingNotBegin()) {
      return false
    }
    if (this.offWork() && this.workingIsEnd()) {
      return false
    }

    return true
  }
  //定位中
  locating(location) {
    if (!location) {
      return true
    }

    return false
  }
  actionButtonColor(attendGroup, location) {
    // 不考勤
    if (attendGroup.isNoJoinAttend()) {
      return 'default'
    }
    if (attendGroup.isFreeMode()) {
      return 'default'
    }
    if (!this.locationAllowCheckIn(attendGroup, location)) {
      return 'gray'
    }

    if (this.outSide(attendGroup, location)) {
      return 'green'
    }

    if (location && location.loading) {
      return 'default'
    }

    if (this.late()) {
      return 'red'
    }

    return 'default'
  }

  action(attendGroup, location) {
    if (attendGroup.isFreeMode() || attendGroup.isNoJoinAttend()) {
      // 允许外勤打卡
      if(attendGroup.allowedOutside && this.goWork()) {
        return this.outSide(attendGroup, location) ? '外勤打卡' : '上班打卡'
      }

      if(attendGroup.allowedOutside && this.offWork()) {
        return this.outSide(attendGroup, location) ? '外勤打卡' : '下班打卡'
      }

      if (this.goWork()) {
        return '上班打卡'
      }
      
      if (this.offWork()) {
        return '下班打卡'
      }
    }

    if (location.loading) {
      return '定位中'
    }
    if (!this.isWorkingDuration()) {
      return '不可打卡'
    }
    if (this.outSide(attendGroup, location)) {
      return '外勤打卡'
    }
    if (this.late()) {
      return '迟到'
    }
    if (this.goWork()) {
      return '上班打卡'
    }
    if (this.offWork()) {
      return '下班打卡'
    }
  }
}

export default CheckIn
