import { isObject } from 'kit/helpers/index'
import signResultToString from 'kit/formatters/mpH5/signResultToString'
import { signResult } from 'kit/formatters/mpH5/signResultToString'
import DateTimeService from './dateTimeService'
import { AttendScene } from "kit/models/attend/attendDefine"
import { isCrossingDay } from './attendGroup'

const formatHoursMinutes = minutes => {
  if (!minutes) {
    return ''
  }
  var h = Math.floor(Math.abs(minutes) / 60)
  var m = Math.abs(minutes) % 60
  h = h == 0 ? '' : h + '小时'
  m = m == 0 ? '' : m + '分钟'
  return h + m
}
class CheckInRecord {
  constructor(todaySign, isLast, attendScene) {
    this.isLast = isLast
    this.attendScene = attendScene
    if (!isObject(todaySign)) {
      throw new Error('todaySign is not an object')
    }
    for (var key in todaySign) {
      this[key] = todaySign[key]
    }
    this.isWorkDay = this.isWorkday
  }
  //获取已打卡的状态
  status() { }
  //是否需要展示
  shown() {
    if (this.isWorkDay) {
      return true
    }
    //签到成功
    if (this.signResult == 'NORMAL' || this.signResult == 'SUPPLY_PASS') {
      return true
    }

    return false
  }
  //第几次打卡
  timesString() {
    return `第${this.workingShiftOrder}次`
  }
  shortStandardTime() {
    if (!this.standardTime) {
      return ''
    }

    return this.standardTime.substr(-8, 5)
  }
  //是否自由打卡模式
  isFreeMode() {
    return this.shiftType === 'FREE'
  }
  //上班
  goWork() {
    return this.signType === 'TO_WORK'
  }
  //下班
  offWork() {
    return this.signType === 'FROM_WORK'
  }
  isAttendStatsScene() {
    return this.attendScene === AttendScene.ATTEND_STATS
  }
  isShowCrossingDayStandardFlag() {
    return isCrossingDay(this.workDate, this.standardTime);
  }
  isShowCrossingDayCheckInFlag() {
    return isCrossingDay(this.workDate, this.signTime);
  }
  isNeedShowCheckInTime() {
    return this.signTime || this.signResult == 'SUPPLY_PASS'
  }
  shortCheckInTime() {
    return this.signResult == 'SUPPLY_PASS'
      ? this.standardTime.substr(-8, 5)
      : this.signTime.substr(-8, 5)
  }
  tagString(attendGroup) {
    // 不考勤
    if (attendGroup && attendGroup.isNoJoinAttend()) {
      return signResult.NORMAL
    }
    var s = signResultToString(this.signResult)
    if (this.signResult == 'BE_LATE' || this.signResult == 'LEAVE_EARLIER') {
      s = s + ' ' + formatHoursMinutes(this.durationMinutes)
    }

    return s
  }
  resultColor(attendGroup) {
    // 不考勤
    if (attendGroup && attendGroup.isNoJoinAttend()) {
      return '#4185f8'
    }
    if (
      this.signResult === 'BE_LATE' ||
      this.signResult === 'LEAVE_EARLIER' ||
      this.signResult === 'ABSENT_WORK'
    ) {
      return '#e14c46'
    }

    if (this.signResult === 'OUTSIDE_ATTEND') {
      return '#00b4b3'
    }

    return '#4185f8'
  }
  isNeedShowUpdateTimeButton() {
    if (!this.isLast) {
      return false
    }

    //是下班与上班模式
    if (!(this.signType === 'TO_WORK' || this.signType === 'FROM_WORK')) {
      return false
    }

    // 上班场景，不允许在更新打卡时间（广发银行需求）
    if(this.goWork()) {
      return false;
    }

    //todo 考勤排班id  == -1?
    if (this.attendWorkingShiftId === -1) {
      return true
    }
    //@todo 确定下各个时间的含义
    //自由打卡模式
    if (this.isFreeMode()) {
      //workingEndAdvance早退时间
      return DateTimeService.lessThan(new Date(), this.workingEndAdvance)
    }
    //固定打卡模式
    //@todo 这里的时间和lastTime的关系
    if (!this.isFreeMode()) {
      return DateTimeService.lessThan(new Date(), this.workingEndLate)
    }

    return false
  }
  //是否展示补卡按钮
  isNeedShowFixAttendButton(attendGroup) {
    //已经成功了
    if (this.fixAttendStatus === 'SUCCESS') {
      return false
    }
    //考勤组关闭了 或 没有考勤组
    if (!attendGroup || !attendGroup.approvalSwitch) {
      return false
    }
    //没有补卡的审批流程
    if (!attendGroup || !attendGroup.hadApprovalType('FIX_ATTENDANCE')) {
      return false
    }
    //有效的状态记录才可以批量打卡
    if(!this.isEffective) {
      return false
    }
    const needFixAttend =
      this.signResult === 'BE_LATE' ||
      this.signResult === 'LEAVE_EARLIER' ||
      this.signResult === 'ABSENT_WORK'

    return needFixAttend
  }
  fixAttendButtonLabel() {
    var buttonLabel = '提交补卡'
    if (this.fixAttendStatus === 'INIT') {
      buttonLabel = '补卡中'
    }
    if (this.fixAttendStatus === 'SUCCESS') {
      buttonLabel = '补卡通过'
    }

    return buttonLabel
  }
}

export default CheckInRecord
