const DateTimeService = {
  lessThan(dt1, dt2) {
    if(typeof dt1 === "string") {
        dt1 = dt1.replace(/-/g, '/')
    }
    if(typeof dt2 === "string") {
        dt2 = dt2.replace(/-/g, '/')
    }
    try {
      const d1 = new Date(dt1)
      const d2 = new Date(dt2)

      return d1.getTime() <= d2.getTime()
    } catch (err) {
      alert('传入了错误的日期格式')
    }
  }
}
export default DateTimeService
