// {
//   "compId": 328,
//   "compName": "假勤的考勤机",
//   "compEmpId": 10067,
//   "empName": "泽阳",
//   "mobile": "15010460569",
//   "postName": "的复工后即可岗位",
//   "enable": true,
//   "isAuth": true,
//   "faceStatus": 1,
//   "aiCheckStatus": 0,
//   "showRecordFaceNotify": true,
//   "aiCheckCount": 0,
//   "verifyFaceOcr": true,
//   "administrator": true
// }
class Employee {
  constructor(employee, resigned) {
    if (!employee) {
      throw new Error('employee is not an object')
    }
    this.resigned = resigned || false
    for (var key in employee) {
      this[key] = employee[key]
    }
  }
  setResigned(resigned) {
    this.resigned = resigned
  }
  shortName() {
    return this.empName.substr(-2)
  }
}

export default Employee
