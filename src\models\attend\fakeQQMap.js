var fakeQQMap = {
    maps: {}
  }
  
  class Geocoder {
    constructor(result, error) {
      this.result = result || {
        detail: {
          address: 'fakeAddress'
        }
      }
      this.error = error
      this.completeCallback = null
      this.errorCallback = null
    }
    setComplete(cb) {
      this.completeCallback = cb
    }
    setError(cb) {
      this.errorCallback = cb
    }
    getAddress(point) {
      if (!this.error) {
        this.completeCallback(this.result)
        return
      }
  
      this.errorCallback(this.error)
    }
  }
  class LatLng {
    constructor(lat, lng) {}
  }
  
  fakeQQMap.maps.Geocoder = Geocoder
  fakeQQMap.maps.LatLng = LatLng
  
  export default fakeQQMap
  