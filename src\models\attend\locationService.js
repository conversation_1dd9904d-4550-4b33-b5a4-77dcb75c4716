import {qqMap} from 'kit/services/qqMap'
/**
 * 将度转化为弧度
 * @param {degree} Number 度
 * @returns {Number} 弧度
 */
function _degreeToRad(degree) {
  return (Math.PI * degree) / 180
}
// 将v值限定在a,b之间，经度使用
function _getLoop(v, a, b) {
  while (v > b) {
    v -= b - a
  }
  while (v < a) {
    v += b - a
  }
  return v
}
// 将v值限定在a,b之间，纬度使用
function _getRange(v, a, b) {
  if (a != null) {
    v = Math.max(v, a)
  }
  if (b != null) {
    v = Math.min(v, b)
  }
  return v
}

//地球半径
const EARTHRADIUS = 6370996.81

/**
 * 计算两点之间的距离,两点坐标为经纬度
 * @param {point1} Point 点对象
 * @param {point2} Point 点对象
 * @returns {Number} 两点之间距离，单位为米
 */
const distanceOfTwoPoint = function (point1, point2) {
  point1.lng = _getLoop(point1.lng, -180, 180)
  point1.lat = _getRange(point1.lat, -74, 74)
  point2.lng = _getLoop(point2.lng, -180, 180)
  point2.lat = _getRange(point2.lat, -74, 74)

  var x1, x2, y1, y2
  x1 = _degreeToRad(point1.lng)
  y1 = _degreeToRad(point1.lat)
  x2 = _degreeToRad(point2.lng)
  y2 = _degreeToRad(point2.lat)

  return (
    EARTHRADIUS *
    Math.acos(
      Math.sin(y1) * Math.sin(y2) +
        Math.cos(y1) * Math.cos(y2) * Math.cos(x2 - x1)
    )
  )
}

const LocationService = {
  point() {
    if (window.env.current === 'dev') {
      return this.fakePoint()
    }
    return new Promise(resolve => {
      if (!("geolocation" in navigator)) {
        resolve(['获取位置失败，手机不支持定位', null])
      }
      navigator.geolocation.getCurrentPosition(
        position => {
          if (position) {
            const point = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            }
            resolve([null, point])
          } else {
            resolve(['获取位置失败', null])
          }
        },
        error => {
          var errMsg = error.message
          if (/denied/.test(errMsg)) {
            // self.attendScope = "考勤打卡需要开启位置服务【IOS-隐私-定位服务-开启】【Android-开启位置服务-开启】"
            errMsg = '当前位置服务未开启，请打开系统定位开关'
          }

          resolve([errMsg, null])
        }
      )
    })
  },
  fakePoint() {
    return new Promise(resolve => resolve([null, { lat: -1, lng: -1 }]))
  },
  pointToAddress(point) {
    if(window.env.current === 'dev'){
      return Promise.resolve([null,"fake address"])
    }

    return qqMap.pointToAddress(point)
  },
  locationStandardAddress(attendPlaces, location) {
    for (var c of attendPlaces) {
      const distance = parseInt(
        distanceOfTwoPoint(location.point, {
          lng: c.longitude,
          lat: c.latitude
        })
      )
      if (c.errorRange > distance) {
        return c.placeName
      }
    }

    return location.address
  },
  pointIsOutSideOfAttendPlaces(attendPlaces, point) {
    // return false
    if (!attendPlaces || !attendPlaces.length) {
      return false
    }

    for (var c of attendPlaces) {
      const distance = parseInt(
        distanceOfTwoPoint(point, {
          lng: c.longitude,
          lat: c.latitude
        })
      )
      if (c.errorRange > distance) {
        return false
      }
    }

    return true
  }
}

export default LocationService
