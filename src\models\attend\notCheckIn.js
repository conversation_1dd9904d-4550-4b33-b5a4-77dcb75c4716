import { isObject } from 'kit/helpers/index'
import { isCrossingDay } from './attendGroup'

class NotCheckIn {
  constructor(notSignWorkShift) {
    if (!isObject(notSignWorkShift)) {
      throw new Error('notSignWorkShift is not an object')
    }
    for (var key in notSignWorkShift) {
      this[key] = notSignWorkShift[key]
    }
  }
  goWork() {
    return this.signTypeEnum === 'TO_WORK'
  }
  offWork() {
    return this.signTypeEnum === 'FROM_WORK'
  }
  workTimesString() {
    if (this.goWork()) {
      return `第${this.orderNo}次上班时间 ${this.workingBegin.substr(-8, 5)}`
    }

    return `第${this.orderNo}次下班时间 ${this.workingEnd.substr(-8, 5)}`
  }
  isShowCrossingDayStandardFlag() {
    const standardTime = this.goWork() ? this.workingBegin : this.workingEnd;
    return isCrossingDay(this.workDate, standardTime);
  }
}

export default NotCheckIn
