const tips = [
  '相信自己！一定会有辉煌的一天!',
  '志存高远，奋发向上，走向成功！',
  '拼搏实现梦想，坚持铸就辉煌',
  '努力加油！做更好的自己！',
  '工作做的好，你会更自豪。',
  '心有所期，全力以赴，定有所成。',
  '不要忘了给自己一点赞美。',
  '坚持下去，成功就在下一个街角处等着你。',
  '锲而不舍，金石可镂！',
  '为了自己的未来，努力、拼搏、前进！',
  '凡是值得做的事，就值得做好。',
  '优秀，是一种习惯。',
  '心之所愿，无事不成。'
]
const TipService = {
  randomTip() {
    const l = tips.length
    const random = Math.random() * 1000000
    const index = Math.abs(Math.floor(random % l))
    return tips[index]
  }
}

export default TipService
