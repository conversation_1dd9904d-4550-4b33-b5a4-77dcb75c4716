import { isValidCurrency } from './valid'

class Money {
  constructor(amount, currency = 'zh-CN') {
    if (!Number.isInteger(amount)) {
      throw new Error('amount must be a integer')
    }

    if (!isValidCurrency(currency)) {
      throw new Error('currency not right, support USD and CNY only')
    }

    this.amount = amount
    this.currency = currency
  }

  equals(money) {
    return this.amount === money.amount && this.currency === money.currency
  }

  static add(money1, money2) {
    if (money1.currency !== money2.currency) {
      throw new Error('currencies must be the same')
    }
    return new Money(money1.amount + money2.amount, money1.currency)
  }

  static subtract(money1, money2) {
    if (money1.currency !== money2.currency) {
      throw new Error('currencies must be the same')
    }
    return new Money(money1.amount - money2.amount, money1.currency)
  }

  static allocate(money, ratios) {
    const total = ratios.reduce((acc, ratio) => acc + ratio, 0)
    const remainder = money.amount
    let results = []
    let allocated = 0
    for (let i = 0; i < ratios.length; i++) {
      const ratio = ratios[i]
      const share = Math.floor((ratio / total) * remainder)
      results.push(new Money(share, money.currency))
      allocated += share
    }
    results[0] = new Money(
      results[0].amount + (remainder - allocated),
      money.currency
    )
    return results
  }

  static dollar(amount) {
    return new Money(amount, 'USD')
  }

  static yuan(amount) {
    return new Money(amount, 'CNY')
  }

  toString() {
    return `${this.currency} ${this.amount}`
  }
}

export default Money
