import Money from './money'

describe('Money constructor', () => {
  test('should throw error when amount is not a integer', () => {
    expect(() => new Money('123', 'USD')).toThrow('amount must be a integer')
  })

  test('should throw error when currency is not valid', () => {
    expect(() => new Money(123, 'ABC')).toThrow(
      'currency not right, support USD and CNY only'
    )
  })

  test('should create instance when amount is a number and currency is valid', () => {
    const money = new Money(123, 'USD')
    expect(money.amount).toBe(123)
    expect(money.currency).toBe('USD')
  })
})

describe('Money equals', () => {
  test('should return true when amount and currency are the same', () => {
    const money1 = new Money(123, 'USD')
    const money2 = new Money(123, 'USD')
    expect(money1.equals(money2)).toBe(true)
  })

  test('should return false when amount is not the same', () => {
    const money1 = new Money(123, 'USD')
    const money2 = new Money(456, 'USD')
    expect(money1.equals(money2)).toBe(false)
  })

  test('should return false when currency is not the same', () => {
    const money1 = new Money(123, 'USD')
    const money2 = new Money(123, 'CNY')
    expect(money1.equals(money2)).toBe(false)
  })
})

describe('Money add', () => {
  test('should throw error when currencies are not the same', () => {
    const money1 = new Money(123, 'USD')
    const money2 = new Money(123, 'CNY')
    expect(() => Money.add(money1, money2)).toThrow(
      'currencies must be the same'
    )
  })

  test('should return a new Money instance when currencies are the same', () => {
    const money1 = new Money(123, 'USD')
    const money2 = new Money(456, 'USD')
    const result = Money.add(money1, money2)
    expect(result.amount).toBe(579)
    expect(result.currency).toBe('USD')
  })

  //add -10
  test('should return a new Money instance when currencies are the same and amount is negative', () => {
    const money1 = new Money(123, 'USD')
    const money2 = new Money(-10, 'USD')
    const result = Money.add(money1, money2)
    expect(result.amount).toBe(113)
    expect(result.currency).toBe('USD')
  })
})

describe('Money subtract', () => {
  test('should throw error when currencies are not the same', () => {
    const money1 = new Money(123, 'USD')
    const money2 = new Money(123, 'CNY')
    expect(() => Money.subtract(money1, money2)).toThrow(
      'currencies must be the same'
    )
  })

  test('should return a new Money instance when currencies are the same', () => {
    const money1 = new Money(123, 'USD')
    const money2 = new Money(456, 'USD')
    const result = Money.subtract(money1, money2)
    expect(result.amount).toBe(-333)
    expect(result.currency).toBe('USD')
  })

  //subtract -10
  test('should return a new Money instance when currencies are the same and amount is negative', () => {
    const money1 = new Money(123, 'USD')
    const money2 = new Money(-10, 'USD')
    const result = Money.subtract(money1, money2)
    expect(result.amount).toBe(133)
    expect(result.currency).toBe('USD')
  })
})

describe('Money allocate', () => {
  test('should return a new Money instance when currencies are the same', () => {
    const money = new Money(100, 'USD')
    const ratios = [1, 1, 1]
    const result = Money.allocate(money, ratios)
    expect(result[0].amount).toBe(34)
    expect(result[0].currency).toBe('USD')
    expect(result[1].amount).toBe(33)
    expect(result[1].currency).toBe('USD')
    expect(result[2].amount).toBe(33)
    expect(result[2].currency).toBe('USD')
  })

  // ratios = [1, 2]
  test('should return a new Money instance when currencies are the same and ratios = [1, 2]', () => {
    const money = new Money(100, 'USD')
    const ratios = [1, 2]
    const result = Money.allocate(money, ratios)
    expect(result[0].amount).toBe(34)
    expect(result[0].currency).toBe('USD')
    expect(result[1].amount).toBe(66)
    expect(result[1].currency).toBe('USD')
  })

  // ratios = [2, 2]
  test('should return a new Money instance when currencies are the same and ratios = [2, 2]', () => {
    const money = new Money(100, 'USD')
    const ratios = [2, 2]
    const result = Money.allocate(money, ratios)
    expect(result[0].amount).toBe(50)
    expect(result[0].currency).toBe('USD')
    expect(result[1].amount).toBe(50)
    expect(result[1].currency).toBe('USD')
  })
})

//yuan
describe('Money yuan', () => {
  test('should return a new Money instance when currencies are the same', () => {
    const result = Money.yuan(100)
    //result is instance of Money
    expect(result instanceof Money).toBe(true)
    expect(result.amount).toBe(100)
    expect(result.currency).toBe('CNY')
  })
})
