<template>
  <div class="adjust-detail">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>定调薪详情</span>
        </el-col>
      </el-row>
    </header>
    <div class="screening">
      <div class="drawer-employeeInfo">
        <div class="avatar">{{ currentItemObj.empName.substr(-2) }}</div>
        <div class="info">
          <h5>{{ currentItemObj.empName }}</h5>
          <span>{{ currentItemObj.taxSubName }}</span>
          <!-- <span style="margin-left: 10px;">{{currentItemObj.departmentName}}</span> -->
        </div>
      </div>
      <div class="adjust-info">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="定调薪信息" name="adjust"></el-tab-pane>
        </el-tabs>
        <span
          class="table-name operate-btn"
          @click="adjustSalary"
          v-if="
            tableData.length > 0 &&
            privilegeVoList.includes(
              'salary.compute.salaryArchive.changeSalary'
            ) &&
            activeName === 'adjust'
          "
        >
          调薪
        </span>
      </div>
      <div class="adjust-content" v-if="activeName === 'adjust'">
        <div v-if="tableData.length > 0">
          <div class="content" v-for="(item, index) in tableData" :key="index">
            <div class="item-contaier">
              <span>生效日期</span>
              <span>{{ item.effectiveDate }}</span>
            </div>
            <div v-for="(it, id) in item.items" :key="id" class="item-contaier">
              <span>
                {{
                  item.adjustSalaryType === "TRIAL_SALARY"
                    ? "试用期" + it.itemName
                    : it.itemName
                }}
              </span>
              <span>{{ it.itemValue }}</span>
            </div>
            <div
              v-if="item.adjustSalaryType === 'CONFIRM_SALARY'"
              class="item-contaier"
            >
              <span>备注</span>
              <span>{{ item.remark }}</span>
            </div>
            <div
              v-if="item.adjustSalaryType === 'ADJUST_SALARY'"
              class="item-contaier"
            >
              <span>调薪原因</span>
              <span>{{ item.adjustSalaryReason }}</span>
            </div>
            <div class="operation">
              <span
                class="table-name"
                v-if="index === 0"
                @click="cancel(item.adjustId)"
                >撤销</span
              >
              <span
                class="table-name"
                @click="edit(item)"
                style="margin-left: 10px"
                v-if="item.adjustSalaryType !== 'TRIAL_SALARY'"
                >编辑</span
              >
            </div>
          </div>
        </div>
        <div style="text-align: left" v-else>
          <span class="table-title">该员工暂无定调薪记录</span>
          <span
            class="table-name"
            @click="fixedSalary"
            v-if="
              privilegeVoList.includes('salary.compute.salaryArchive.fixSalary')
            "
            >定薪</span
          >
          <span class="disable-table-name" v-else>定薪</span>
        </div>
      </div>
    </div>
    <editAdjust @getList="getCustomItemsList" ref="editAdjust"></editAdjust>
    <salaryChange
      @getList="getCustomItemsList"
      ref="salaryChange"
    ></salaryChange>
    <newTaxPlayer
      ref="newTaxPlayer"
      @refresh="refresh"
      :addEmpDay="addEmpDay"
    ></newTaxPlayer>
  </div>
</template>
<script>
import { mapState } from "vuex";
import editAdjust from "./components/editAdjust";
import salaryChange from "./components/salaryChange";
import newTaxPlayer from "./components/newTaxPlayer";

export default {
  components: { editAdjust, salaryChange, newTaxPlayer },
  data() {
    return {
      tableData: [],
      empId: this.$route.query.empId,
      sel: null, //选中行
      loading: false,
      customItems: [],
      activeName: "adjust",
      addEmpDay: null,
    };
  },
  computed: {
    ...mapState("adjustSalaryStore", {
      currentItemObj: "currentItemObj",
    }),
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  created() {
    this.getCustomItemsList();
  },
  mounted() {},
  methods: {
    getList() {
      this.tableData = [];
      this.$store
        .dispatch("adjustSalaryStore/actionGetAdjustSalaryDetail", {
          empId: this.empId,
          taxSubId: this.currentItemObj.taxSubId,
        })
        .then((res) => {
          if (res.success) {
            //匹配对应code
            // console.log(this.customItems);
            let customItems = this.customItems;
            let list = res.data.list;
            let newData = list.map((i) => {
              let _i = customItems.map((c) => {
                let _obj =
                  i.items.filter((o) => o.itemCode === c.itemCode)[0] || null;
                return {
                  ...c,
                  itemValue: (_obj && _obj.itemValue) || "0",
                };
              });
              return {
                ...i,
                items: _i,
              };
            });
            this.tableData = newData;
            this.loading = false;
          }
        });
    },
    //获取自定义项目
    async getCustomItemsList() {
      let res = await this.$store.dispatch(
        "adjustSalaryStore/actionGetConfigList"
      );
      if (res.success) {
        this.getList();
        this.customItems = res.data.filter((item) => item.enable);
      }
    },
    //切换tab
    handleClick() {
      if (this.activeName === "adjust") {
        this.getCustomItemsList();
      }
    },
    //调薪
    adjustSalary() {
      let data = this.currentItemObj;
      data.adjustSalaryType = "ADJUST_SALARY";
      this.$store.commit("adjustSalaryStore/SET_CURRENTITEMOBJ", data);
      this.$refs.salaryChange.showCompany(this.tableData[0].adjustId);
    },
    //定薪
    fixedSalary() {
      let data = this.currentItemObj;
      data.adjustSalaryType = "CONFIRM_SALARY";
      this.$store.commit("adjustSalaryStore/SET_CURRENTITEMOBJ", data);
      this.$refs.salaryChange.showCompany();
    },
    //撤销定调薪
    cancel(adjustId) {
      this.$confirm("确定要撤销本员工此条定调薪记录？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        this.$store
          .dispatch("adjustSalaryStore/actionUndoAdjustDetail", adjustId)
          .then((res) => {
            if (res.success) {
              this.getCustomItemsList();
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            }
          });
      });
    },
    //编辑
    edit(item) {
      //引用类型需要拷贝一份
      let arr = JSON.parse(JSON.stringify(item));
      this.$refs.editAdjust.show(arr, this.tableData);
    },
    //新增纳税主体公司
    handleNewTaxPlayer() {
      this.currentItemObj.isEidt = false;
      this.$refs.newTaxPlayer.show(this.currentItemObj);
    },
    //刷新数据
    refresh(data) {
      this.activeName = data;
    },
    //获取任职受雇从业日期
    getEmpDay(val) {
      this.addEmpDay = val;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.adjust-detail {
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }

  .screening {
    padding: 0 20px;
    .drawer-employeeInfo {
      height: 100px;
      margin: 0 20px 0 20px;
      display: flex;
      align-items: center;
      .avatar {
        width: 50px;
        height: 50px;
        background: linear-gradient(to right, #659afe, #4180ff);
        margin-right: 16px;
        border-radius: 50%;
        text-align: center;
        line-height: 50px;
        color: #fff;
        font-size: 14px;
      }
      .info {
        color: #646a73;
        font-size: 14px;
        h5 {
          margin-bottom: 10px;
          font-size: 16px;
          color: #1f2329;
          font-weight: bold;
        }
      }
    }
    .adjust-info {
      position: relative;
      margin: 0 20px;
      .operate-btn {
        position: absolute;
        right: 0;
        top: 14px;
      }
    }
    .adjust-content {
      margin: 0 20px;
      text-align: center;
      color: #646a73;
      height: calc(100vh - 265px);
      overflow: auto;
      .content {
        border-bottom: 1px solid #dee0e3;
        padding-top: 20px;
        box-sizing: border-box;
        display: flex;
        flex-wrap: wrap;
        position: relative;
        div.item-contaier {
          width: 50%;
          margin-bottom: 20px;
          text-align: left;
          span:first-child {
            display: inline-block;
            width: 130px;
            margin-right: 20px;
            text-align: right;
          }
        }
        div.operation {
          width: auto;
          position: absolute;
          right: 10px;
        }
      }
      // span {
      //   display: inline-block;
      //   margin-left: 20px;
      //   color: #1f2329;
      // }
    }

    .table-name {
      color: $mainColor !important;
      cursor: pointer;
    }
    .table-title {
      color: #646a73 !important;
    }
  }
}
</style>
