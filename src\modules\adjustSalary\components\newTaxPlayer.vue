<template>
  <div class="salary-change">
    <right-pop
      :pop-show="popShow"
      :has-footer="true"
      :popTitle="isEidt ? '编辑纳税主体公司' : '新增纳税主体公司'"
      :popWidth="470"
    >
      <div slot="pop-content">
        <div class="drawer-employeeInfo">
          <div class="avatar">
            {{
              currentItemObj.empName ? currentItemObj.empName.substr(-2) : ""
            }}
          </div>
          <div class="info">
            <h5>{{ currentItemObj.empName }}</h5>
            <span>{{ currentItemObj.taxSubName }}</span>
            <!-- <span style="margin-left: 10px;">财务部</span> -->
          </div>
        </div>
        <div class="salary-title">
          <span>纳税主体公司</span>
        </div>
        <el-form
          label-width="150px"
          ref="submitForm"
          :model="submitForm"
          class="change-form"
        >
          <el-form-item
            prop="taxSubId"
            label="扣缴义务人"
            :rules="{
              required: true,
              message: '请选择',
              trigger: 'change'
            }"
          >
            <el-select
              v-model="submitForm.taxSubId"
              placeholder="请选择"
              filterable
              clearable
              @change="changeTaxSub"
            >
              <el-option
                v-for="(item, index) in taxSubList"
                :label="item.taxSubName"
                :value="item.taxSubId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="workerType"
            label="任职受雇从业类型"
            :rules="{
              required: true,
              message: '请选择',
              trigger: 'change'
            }"
          >
            <el-select
              v-model="submitForm.workerType"
              placeholder="请选择"
              filterable
              clearable
            >
              <el-option
                v-for="(item, index) in workerTypeOption"
                :label="item.label"
                :value="item.value"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="empDay"
            label="任职受雇从业日期"
            :rules="{
              required: true,
              message: '请填写生效日期',
              trigger: 'blur'
            }"
          >
            <el-date-picker
              v-model="submitForm.empDay"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="请选择"
              :clearable="false"
              align="center"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </el-form-item>
          <el-form-item prop="leaveDay" label="离职日期">
            <el-date-picker
              v-model="submitForm.leaveDay"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="请选择"
              :clearable="false"
              align="center"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div slot="pop-footer">
        <div class="con-footer">
          <el-button @click="handleCancel()">取消</el-button>
          <el-button type="primary" @click="handleSave">确定</el-button>
        </div>
      </div>
    </right-pop>
  </div>
</template>
<script>
import { mapState } from "vuex";
import rightPop from "@/components/basic/rightPop";
import * as constData from "@/utils/constData";
import { apiAddEmpTaxSub, apiModifyEmpTaxSub } from "../store/api";

export default {
  components: {
    rightPop
  },
  props: {
    addEmpDay: String
  },
  data() {
    return {
      popShow: { isshow: false },
      submitForm: {
        taxSubId: "", //扣缴义务人
        workerType: "", //任职受雇从业类型
        empDay: "", //任职受雇从业日期
        leaveDay: "" //离职日期
      },
      workerTypeOption: constData.workerTypeOption,
      isEidt: false,
      empId: "", //编辑id
      taxSubId: "", //无匹配的公司id
      hasTaxSub: "", //是否有匹配公司
      isEditSub: false, //编辑时是否修改了公司
      taxSubList: [],
      pickerOptions: {
        //设置禁用的入职日期
        disabledDate: time => {
          return this.addEmpDay
            ? new Date(time) < new Date(this.addEmpDay)
            : false;
        }
      }
    };
  },
  computed: {
    ...mapState("adjustSalaryStore", {
      currentItemObj: "currentItemObj"
    }),
    ...mapState({
      privilegeVoList: state => state.privilegeVoList,
      taxSubjectInfoList: state =>
        state.taxSubjectInfoList.filter(item => item.taxSubId)
    })
  },
  watch: {},
  methods: {
    show(item) {
      // 如果item有值，则为编辑
      this.submitForm = item;
      this.isEidt = item.isEidt;
      //纳税主体
      this.taxSubList = this.taxSubjectInfoList.filter(i => i.taxEnableYn);
      this.taxSubList = this.taxSubList.filter(
        i => i.accreditStatus === "SUCCESS"
      );
      //判断是否可以回显公司
      let taxSubId = this.submitForm.taxSubId;
      let filterItem = this.taxSubList.filter(
        item => item.taxSubId === taxSubId
      );
      if (filterItem.length === 0) {
        let arr = this.taxSubjectInfoList.filter(
          item => item.taxSubId === taxSubId
        );
        if (arr.length > 0) {
          this.submitForm.taxSubId = arr[0].taxSubName;
        }
        this.taxSubId = taxSubId;
        this.hasTaxSub = false;
      } else {
        this.hasTaxSub = true;
      }

      this.popShow.isshow = true;
    },
    //保存
    handleSave() {
      this.$refs.submitForm.validate(valid => {
        if (valid) {
          //判断是否是无匹配的公司
          if (!this.isEditSub && !this.hasTaxSub) {
            this.submitForm.taxSubId = this.taxSubId;
          }

          if (this.isEidt) {
            apiModifyEmpTaxSub(this.submitForm).then(res => {
              if (res.success) {
                this.$emit("refresh", "taxCompany");
                this.$message.success("操作成功");
                this.popShow.isshow = false;
              }
            });
          } else {
            apiAddEmpTaxSub(this.submitForm).then(res => {
              if (res.success) {
                this.$emit("refresh", "taxCompany");
                this.$message.success("操作成功");
                this.popShow.isshow = false;
              }
            });
          }
        } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    //取消
    handleCancel() {
      this.popShow.isshow = false;
    },
    //修改公司
    changeTaxSub() {
      this.isEditSub = true;
    }
  }
};
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";
.salary-change {
  .con-footer {
    position: absolute;
    bottom: 0px;
    text-align: center;
    background: #fff;
    width: 100%;
    padding: 15px 0px;
    border-top: 1px solid #f1f1f1;
  }
  .el-date-editor.el-input,
  .el-select {
    width: 100%;
  }
  .el-input--prefix .el-input__inner {
    padding: 14px 30px;
  }
  .change-form {
    width: 80%;
    margin: 0 auto;
    padding-bottom: 50px;
    .adjust-input {
      /deep/ .el-form-item__content {
        display: flex;
        align-items: center;
      }
      .el-icon-right {
        margin: 0 10px;
      }
    }
  }
  .el-icon-close {
    font-size: 18px;
    float: right;
    margin-top: 20px;
    cursor: pointer;
  }

  .drawer-employeeInfo {
    height: 90px;
    margin: 0 20px 0 20px;
    display: flex;
    align-items: center;
    .avatar {
      width: 50px;
      height: 50px;
      background: linear-gradient(to right, #659afe, #4180ff);
      margin-right: 16px;
      border-radius: 50%;
      text-align: center;
      line-height: 50px;
      color: #fff;
      font-size: 14px;
    }
    .info {
      color: #646a73;
      font-size: 14px;
      h5 {
        margin-bottom: 10px;
        font-size: 16px;
        color: #1f2329;
        font-weight: bold;
      }
    }
  }

  .salary-title {
    height: 44px;
    line-height: 44px;
    font-size: 14px;
    background: rgba(245, 246, 247, 1);
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;
    span.custom-item {
      color: #4F71FF;
      cursor: pointer;
    }
  }
}
</style>
