<template>
  <div class="salary-change">
    <right-pop
      :pop-show="popShow"
      :has-footer="true"
      popTitle="定调薪档案"
      :popWidth="470"
    >
      <div slot="pop-content">
        <div class="drawer-employeeInfo">
          <div class="avatar">{{ salaryItem.empName.substr(-2) }}</div>
          <div class="info">
            <h5>{{ salaryItem.empName }}</h5>
            <span>{{ salaryItem.taxSubName }}</span>
            <!-- <span style="margin-left: 10px;">财务部</span> -->
          </div>
        </div>
        <div class="salary-title">
          <span>{{
            salaryItem.adjustSalaryType === "CONFIRM_SALARY" ? "定薪" : "调薪"
          }}</span>
          <span
            class="custom-item"
            v-if="
              privilegeVoList.includes(
                'salary.compute.salaryArchive.defineItem'
              )
            "
            @click="goAdjustItem"
            >自定义定调薪项目</span
          >
        </div>
        <el-form
          label-width="120px"
          ref="changeSalaryForm"
          :model="changeSalaryForm"
          class="change-form"
          :loading="loading"
        >
          <el-form-item
            prop="effectiveDate"
            label="生效日期"
            :rules="{
              required: true,
              message: '请填写生效日期',
              trigger: 'blur',
            }"
          >
            <el-date-picker
              v-model="changeSalaryForm.effectiveDate"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="请选择"
              :clearable="false"
              align="center"
              :disabled="salaryItem.adjustSalaryType === 'CONFIRM_SALARY'"
              :picker-options="pickerOptions"
            ></el-date-picker>
            <span
              v-if="
                changeSalaryForm.effectiveDate &&
                salaryItem.adjustSalaryType === 'CONFIRM_SALARY'
              "
              class="el-form-item__error"
              style="color: #646a73"
              >默认为入职日期</span
            >
          </el-form-item>
          <!-- 自定义项目 -->
          <el-form-item
            v-for="(item, index) in changeSalaryForm.items"
            :key="index"
            :label="item.itemName"
            :class="
              salaryItem.adjustSalaryType === 'ADJUST_SALARY'
                ? 'adjust-input'
                : ''
            "
          >
            <el-input
              v-model="item.itemValue"
              type="number"
              v-if="salaryItem.adjustSalaryType === 'CONFIRM_SALARY'"
            ></el-input>
            <div
              v-if="salaryItem.adjustSalaryType === 'ADJUST_SALARY'"
              class="right-con"
            >
              <el-input
                :disabled="true"
                v-model="item.itemVal"
                type="number"
              ></el-input>
              <i class="el-icon-right"></i>
              <el-input v-model="item.itemValue" type="number"></el-input>
            </div>
          </el-form-item>
          <el-form-item
            label="备注"
            v-if="salaryItem.adjustSalaryType === 'CONFIRM_SALARY'"
          >
            <el-input
              type="textarea"
              v-model="changeSalaryForm.remark"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-else
            prop="adjustSalaryReason"
            label="调薪原因"
            :rules="{
              required: true,
              message: '请填写调薪原因',
              trigger: 'blur',
            }"
          >
            <el-input
              type="textarea"
              v-model="changeSalaryForm.adjustSalaryReason"
            ></el-input>
          </el-form-item>
          <!-- 试用期 -->
          <el-form-item
            label
            prop="isTrial"
            v-if="salaryItem.adjustSalaryType === 'CONFIRM_SALARY'"
          >
            <el-checkbox-group v-model="changeSalaryForm.isTrial">
              <el-checkbox label="填写试用期工资" name="type"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div
            v-if="
              changeSalaryForm.isTrial &&
              salaryItem.adjustSalaryType === 'CONFIRM_SALARY'
            "
          >
            <el-form-item
              v-for="(it, id) in changeSalaryForm.trialItems"
              :key="id"
              :label="'试用期' + it.itemName"
            >
              <el-input v-model="it.itemValue" type="number"></el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div slot="pop-footer">
        <div class="con-footer">
          <el-button @click="handleCancel(false)">取消</el-button>
          <el-button
            type="primary"
            :loading="confirmLoading"
            @click="handleNewBody"
            >确定</el-button
          >
        </div>
      </div>
    </right-pop>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { deepClone } from "@/utils/utils";
import rightPop from "@/components/basic/rightPop";

export default {
  components: {
    rightPop,
  },
  props: {},
  data() {
    return {
      popShow: { isshow: false },
      changeSalaryForm: {
        empId: "",
        isTrial: false, //是否填写试用期工资
        effectiveDate: "", //生效日期
        remark: "", //备注
        adjustSalaryReason: "", //调薪原因
        items: [], // 各工资项
        trialItems: [], //试用期工资项
      },
      salaryItem: {
        empName: "",
      },
      loading: false,
      confirmLoading: false,
      pickerOptions: {
        disabledDate: (time) => {
          // 如果函数里处理的数据比较麻烦,也可以单独放在一个函数里,避免data数据太臃肿
          return this.dealDisabledDate(time);
        },
      },
      effectiveDate: "", //调薪生效日期
    };
  },
  computed: {
    ...mapState("adjustSalaryStore", {
      currentItemObj: "currentItemObj",
    }),
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  watch: {},
  methods: {
    showCompany(adjustId) {
      let data = this.currentItemObj;
      this.salaryItem = data;
      //设置保存或查询需要的id参数
      if (adjustId) {
        this.changeSalaryForm.adjustId = adjustId;
      } else {
        this.changeSalaryForm.adjustId = data.adjustId;
      }
      this.changeSalaryForm.empId = data.empId;
      this.changeSalaryForm.taxSubId = data.taxSubId;
      //如果是定薪，生效日期默认值是入职日期
      if (this.salaryItem.adjustSalaryType === "CONFIRM_SALARY") {
        this.changeSalaryForm.effectiveDate = data.empDay;
        this.changeSalaryForm.remark = "";
      } else {
        this.changeSalaryForm.adjustSalaryReason = "";
      }
      this.getList();
    },
    //获取自定义项目
    getList() {
      this.$store
        .dispatch("adjustSalaryStore/actionGetConfigList")
        .then((res) => {
          if (res.success) {
            let data = res.data.filter((item) => item.enable);
            data.forEach((item) => {
              item.itemValue = "0";
            });
            this.changeSalaryForm = {
              ...this.changeSalaryForm,
              items: data,
              trialItems: deepClone(data),
            };

            //如果是调薪，需要请求详情参数
            if (this.salaryItem.adjustSalaryType === "ADJUST_SALARY") {
              this.getAdjustSalary();
            } else {
              this.$nextTick(() => {
                this.popShow.isshow = true;
              });
            }
          }
        });
    },
    //获取调薪详情
    getAdjustSalary() {
      this.loading = true;
      this.$store
        .dispatch(
          "adjustSalaryStore/actionGetAdjustSalary",
          this.changeSalaryForm.adjustId
        )
        .then((res) => {
          if (res.success) {
            let customItems = this.changeSalaryForm.items;
            this.effectiveDate = res.data.effectiveDate;
            this.changeSalaryForm.effectiveDate = ""; //调薪生效日期设置为空
            let data = res.data;
            //筛选出自定义项目中和行数据中匹配的项目
            let resultArr = customItems.map((item) => {
              let _obj = data.items.filter(
                (_item) => _item.itemCode === item.itemCode
              );
              let value;
              if (_obj[0]) {
                value = _obj[0].itemValue;
              } else {
                value = 0;
              }
              return {
                ...item,
                itemValue: value,
              };
            });
            //对当前数组增加属性
            resultArr.map((i, index) => {
              i.itemVal = i.itemValue;
              i.itemValue = i.itemValue;
              this.$set(resultArr, index, i);
            });
            this.changeSalaryForm = {
              ...this.changeSalaryForm,
              items: resultArr,
            };
            this.loading = false;
            this.$nextTick(() => {
              this.popShow.isshow = true;
            });
          }
        });
    },
    //保存
    handleNewBody() {
      this.$refs.changeSalaryForm.validate((valid) => {
        if (valid) {
          this.confirmLoading = true;
          //定调薪保存接口不一样
          if (this.salaryItem.adjustSalaryType === "CONFIRM_SALARY") {
            this.changeSalaryForm.trialItems = this.changeSalaryForm.isTrial
              ? this.changeSalaryForm.trialItems
              : [];
            this.$store
              .dispatch(
                "adjustSalaryStore/actionSaveSalary",
                this.changeSalaryForm
              )
              .then((res) => {
                if (res.success) {
                  this.confirmLoading = false;
                  this.popShow.isshow = false;
                  this.$message.success("操作成功");
                  setTimeout(() => {
                    this.$emit("getList", true);
                  }, 300);
                }
              })
              .catch((err) => {
                this.confirmLoading = false;
              });
          } else {
            this.$store
              .dispatch(
                "adjustSalaryStore/actionSaveAdjustSalary",
                this.changeSalaryForm
              )
              .then((res) => {
                if (res.success) {
                  this.confirmLoading = false;
                  this.popShow.isshow = false;
                  this.$message.success("操作成功");
                  setTimeout(() => {
                    this.$emit("getList", true);
                  }, 300);
                }
              })
              .catch((err) => {
                this.confirmLoading = false;
              });
          }
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    //取消
    handleCancel() {
      this.popShow.isshow = false;
    },
    //自定义定调薪项目
    goAdjustItem() {
      this.$router.push({
        path: "/adjust-salary/custom-item",
      });
    },
    //设置禁用的生效日期
    dealDisabledDate(time) {
      if (new Date(time).getTime() > new Date(this.effectiveDate).getTime()) {
        return false;
      } else {
        return true;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";
.salary-change {
  /*padding:0px 20px;*/
  /*margin-right: 20px;*/
  .con-footer {
    position: absolute;
    bottom: 0px;
    text-align: center;
    background: #fff;
    width: 100%;
    padding: 15px 0px;
    border-top: 1px solid #f1f1f1;
    z-index: 9;
  }
  .el-date-editor.el-input,
  .el-select {
    width: 100%;
  }
  .el-input--prefix .el-input__inner {
    padding: 14px 30px;
  }
  .change-form {
    width: 80%;
    margin: 0 auto;
    padding-bottom: 50px;
    .adjust-input {
      /deep/ .el-form-item__content {
        display: flex;
        align-items: center;
      }
      .right-con {
        display: flex;
        align-items: center;
      }
      .el-icon-right {
        margin: 0 10px;
      }
    }
  }
  .el-icon-close {
    font-size: 18px;
    float: right;
    margin-top: 20px;
    cursor: pointer;
  }

  .drawer-title {
    height: 60px;
    line-height: 60px;
    text-align: center;
    font-size: 14px;
    color: #1f2329;
    font-weight: 700;
    padding-right: 24px;
    margin: 0;
  }

  .drawer-employeeInfo {
    height: 90px;
    margin: 0 20px 0 20px;
    display: flex;
    align-items: center;
    .avatar {
      width: 50px;
      height: 50px;
      background: linear-gradient(to right, #659afe, #4180ff);
      margin-right: 16px;
      border-radius: 50%;
      text-align: center;
      line-height: 50px;
      color: #fff;
      font-size: 14px;
    }
    .info {
      color: #646a73;
      font-size: 14px;
      h5 {
        margin-bottom: 10px;
        font-size: 16px;
        color: #1f2329;
        font-weight: bold;
      }
    }
  }

  .salary-title {
    height: 44px;
    line-height: 44px;
    font-size: 14px;
    background: rgba(245, 246, 247, 1);
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;
    span.custom-item {
      color: #4f71ff;
      cursor: pointer;
    }
  }
}
</style>
