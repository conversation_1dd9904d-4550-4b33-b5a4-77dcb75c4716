<template>
  <div class="tax-company">
    <div v-for="item in listData" :key="item.id">
      <el-form :model="reportForm" label-width="130px" class="tax-company-form">
        <el-row>
          <el-col :span="12">
            <el-form-item label="扣缴义务人">
              {{ getOptionVal(item.taxSubId) }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任职受雇从业类型">
              {{ item.workerType | workType }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="任职受雇从业日期">
              {{ item.empDay }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="离职日期">{{ item.leaveDay }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item>
              <template slot="label">
                <el-tooltip effect="dark" placement="top">
                  <div slot="content">
                    展示员工的纳税主体公司在哪家用工主体公司下录入
                  </div>
                  <i class="iconfont iconwenhao"></i>
                </el-tooltip>
                企业名称
              </template>
              {{ item.companyName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作日期">{{ item.operateTime }}</el-form-item>
          </el-col>
        </el-row>
        <div class="operation">
          <span
            class="table-name"
            @click="handleDelete(item)"
            style="margin-left: 10px"
          >
            删除
          </span>
        </div>
      </el-form>
    </div>
    <span class="table-title" v-if="listData.length == 0">
      暂未添加纳税主体公司信息，请添加
    </span>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { apiTaxSubjectListByArea } from "../../tax/store/api";
import { apiGetEmpTaxSubList, apiDelEmpTaxSub } from "../store/api";
export default {
  props: {
    compEmpId: {
      type: Number,
    },
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
      taxSubjectInfoList: (state) => state.taxSubjectInfoList,
    }),
  },
  created() {
    this.getList();
    this.getAllTaxSubjectList();
  },
  data() {
    return {
      reportForm: {},
      listData: [],
      allTaxSubjectList: [], //无权限下的所有公司
    };
  },
  methods: {
    async getList() {
      let res = await apiGetEmpTaxSubList({ compEmpId: this.compEmpId });
      if (res.success) {
        this.listData = res.data;
        this.$emit(
          "getEmpDay",
          res.data.length > 0 ? res.data[0].empDay : null
        );
      }
    },
    async getAllTaxSubjectList() {
      let res = await apiTaxSubjectListByArea({ areaId: "", name: "" });
      if (res.success) {
        this.allTaxSubjectList = res.data;
      }
    },
    //删除
    handleDelete(item) {
      this.$confirm("确定删除该记录？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        apiDelEmpTaxSub({ empId: item.empId }).then((res) => {
          if (res.success) {
            this.$message.success("操作成功");
            this.getList();
          }
        });
      });
    },
    //回显下拉框值
    getOptionVal(val) {
      let filterItem = this.allTaxSubjectList.filter(
        (item) => item.taxSubId === val
      );
      return filterItem.length > 0 ? filterItem[0].taxSubName : val;
    },
    refresh() {
      this.getList();
      this.getAllTaxSubjectList();
    },
  },
};
</script>
<style lang="scss" scoped>
.tax-company {
  margin: 0 20px;
  height: calc(100vh - 265px);
  overflow: auto;
  .tax-company-form {
    border-bottom: 1px solid #dee0e3;
    position: relative;
  }
  .operation {
    position: absolute;
    right: 10px;
    top: 5px;
  }
  .table-name {
    color: #4f71ff !important;
    cursor: pointer;
  }
  .table-title {
    color: #646a73 !important;
  }
}
</style>
