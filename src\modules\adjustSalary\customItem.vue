<template>
  <div class="custom-item">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>自定义定调薪项目</span>
        </el-col>
      </el-row>
    </header>
    <div class="screening">
      <div class="staff-table">
        <el-table
          :data="tableData"
          :height="tableHeight"
          row-key="id"
          v-loading="loading"
          stripe
          :header-cell-style="{ background: '#F1F1F1' }"
          class="check-staff_table"
        >
          <el-table-column prop="itemName" label="定调薪项目名称">
            <template slot-scope="scope">
              <div
                v-if="scope.row.isSet"
                style="display: flex; align-items: center"
                ref="defInput"
              >
                <el-input
                  v-model="scope.row.itemName"
                  style="margin: 0 10px"
                ></el-input>
                <el-button size="small" @click="cancel(scope.row, scope.$index)"
                  >取消</el-button
                >
                <el-button
                  type="primary"
                  size="small"
                  @click="save(scope.row, scope.$index)"
                  >确定</el-button
                >
              </div>
              <div
                v-else
                style="padding-left: 20px; display: flex; align-items: center"
              >
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="拖动调整排序"
                  placement="top-start"
                >
                  <i class="el-icon-sort"></i>
                </el-tooltip>
                <div style="width: 120px; display: flex; align-items: center">
                  <span class="item-name" style="text-align: left">
                    {{ scope.row.itemName }}
                  </span>

                  <el-tooltip class="item" effect="dark" placement="right">
                    <div slot="content" style="line-height: 25px">
                      {{ hoverDetail(scope.row) }}
                    </div>
                    <i
                      v-if="
                        scope.row.itemName === '基本工资' ||
                        scope.row.itemName === '岗位工资'
                      "
                      class="iconfont iconwenhao"
                      style="color: #ff9b0e; font-size: 20px"
                    ></i>
                  </el-tooltip>
                </div>

                <span
                  style="margin-left: 70px"
                  class="table-name"
                  @click="edit(scope.row, scope.$index)"
                >
                  编辑
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="itemType" label="项目类型">
            <template slot-scope="scope">
              {{ customItemTypeObj[scope.row.itemType] }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="100">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.enable"
                :disabled="!scope.row.enableEdit"
                @change="changeSwitch(scope.row)"
                :active-value="true"
                :inactive-value="false"
              ></el-switch>
            </template>
          </el-table-column>
        </el-table>
        <div class="add-container">
          <span @click="addItem">
            <i class="iconfont iconadd"></i>
            新增
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Sortable from "sortablejs";
import * as SCR from "./util/constData";

export default {
  data() {
    return {
      tableData: [],
      loading: false,
      customItemTypeObj: SCR.customItemType,
      itemName: "",
      tableHeight: document.body.clientHeight - 300 + "px",
      isAllowAdd: true,
    };
  },
  computed: {},
  components: {},
  created() {
    this.getList();
  },
  mounted() {
    this.rowDrop();
    window.onresize = () => {
      return (() => {
        const clientHeight = document.body.clientHeight - 300 + "px";
        this.tableHeight = clientHeight;
      })();
    };
  },
  methods: {
    hoverDetail(row) {
      if (row.itemName === "基本工资") {
        return "员工税前的基本工资";
      }
      if (row.itemName === "岗位工资") {
        return "员工任职岗位的税前岗位工资";
      }
      return "";
    },
    getList() {
      this.loading = true;
      this.tableData = [];
      this.$store
        .dispatch("adjustSalaryStore/actionGetConfigList")
        .then((res) => {
          if (res.success) {
            this.tableData = res.data;
          }
          this.loading = false;
        });
      this.tableData.map((i) => {
        i.isSet = false; //给后台返回数据添加`isSet`标识
        return i;
      });
    },
    //编辑
    edit(row, index) {
      this.itemName = row.itemName;
      //判断是否已经保存所有操作
      for (let i of this.tableData) {
        if (i.isSet && i.adjustConfigId != row.adjustConfigId) {
          this.$message.warning("请先保存当前编辑项");
          return false;
        }
      }
      if (!row.isSet) {
        row.isSet = true;
        this.$set(this.tableData, index, row);
      }
    },
    //保存
    save(row, index) {
      if (row.itemName.length > 10) {
        this.$message.warning("最长可输入10个字符");
        return;
      }
      let data = {};
      //如果存在adjustConfigId表示是编辑，否则是新增
      if (row.adjustConfigId) {
        let { adjustConfigId, enable, itemName } = row;
        data = {
          adjustConfigId,
          enable,
          itemName,
        };
        data.isAloneSwitch = false;
        this.$store
          .dispatch("adjustSalaryStore/actionSaveSalaryModifyConfig", data)
          .then((res) => {
            if (res.code === "0000") {
              this.$message.success("操作成功");
              this.getList();
              row.isSet = false;
              this.isAllowAdd = true;
            }
          });
      } else {
        let { enable, itemName } = row;
        data = {
          enable,
          itemName,
        };
        this.$store
          .dispatch("adjustSalaryStore/actionSaveSalaryConfig", data)
          .then((res) => {
            if (res.code === "0000") {
              this.$message.success("操作成功");
              this.getList();
              row.isSet = false;
              this.isAllowAdd = true;
            }
          });
      }
    },
    //取消
    cancel(row, index) {
      if (row.adjustConfigId) {
        row.isSet = false;
        row.itemName = this.itemName;
        this.$set(this.tableData, index, row);
      } else {
        this.tableData.splice(index, 1);
      }
      this.isAllowAdd = true;
    },
    //新增
    addItem() {
      if (this.isAllowAdd) {
        this.tableData.push({
          itemName: "",
          itemType: null,
          enable: true,
          isSet: true,
        });
        this.isAllowAdd = false;
        this.$nextTick(() => {
          this.$refs.defInput.scrollIntoView();
        });
      }
    },
    //修改禁用启用
    changeSwitch(data) {
      data.enable = !data.enable; //保持switch点击前的状态
      if (data.enable) {
        this.$confirm("确认要关闭定调薪项目吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
          closeOnPressEscape: false,
        }).then(() => {
          this.handleSaveSwitch(data);
        });
      } else {
        this.handleSaveSwitch(data);
      }
    },
    //保存开启关闭操作
    handleSaveSwitch(data) {
      data.isAloneSwitch = true;
      data.enable = !data.enable;
      this.$store
        .dispatch("adjustSalaryStore/actionSaveSalaryModifyConfig", data)
        .then((res) => {
          if (res.code === "0000") {
            this.$message.success("操作成功");
            this.getList();
          }
        });
    },
    //行拖拽
    rowDrop() {
      //此时找到的元素是要拖拽元素的父容器
      const tbody = document.querySelector(".el-table__body-wrapper tbody");
      const _this = this;
      Sortable.create(tbody, {
        //指定父元素下可被拖拽的子元素
        draggable: ".el-table__row",
        onEnd({ newIndex, oldIndex }) {
          //前端处理拖拽后逻辑
          const currRow = _this.tableData.splice(oldIndex, 1)[0];
          _this.tableData.splice(newIndex, 0, currRow);
          //拖拽后的元素传给后端保存
          let sortMap = {};
          _this.tableData.forEach((item, index) => {
            sortMap[item["adjustConfigId"]] = index;
          });
          _this.$store
            .dispatch("adjustSalaryStore/actionPostSortConfig", {
              sortMap: JSON.stringify(sortMap),
            })
            .then((res) => {
              if (res.code === "0000") {
                _this.getList();
              }
            });
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";

.custom-item {
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }

  .screening {
    padding: 0 20px;

    .staff-table {
      margin-top: 20px;

      .item-name {
        display: inline-block;
        margin: 0 10px;
      }

      /deep/ .el-button--primary {
        font-size: 12px;
      }
    }

    .add-container {
      border: 1px dashed $mainColor;
      color: $mainColor;
      font-size: 16px;
      text-align: center;
      padding: 20px 0;
      margin-top: 20px;

      span {
        cursor: pointer;
      }
    }
  }

  /deep/ .el-table th:first-child {
    text-align: left !important;
    padding-left: 20px;
  }
}
</style>
