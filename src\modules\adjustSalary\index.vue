<template>
  <div class="set-adjust-salary def_per_height">
    <header class="header">
      <el-row type="flex" style="justify-content: space-between">
        <span>薪资档案</span>
        <span
          v-if="
            privilegeVoList.includes('salary.compute.salaryArchive.defineItem')
          "
          style="font-size: 14px; cursor: pointer"
          @click="$router.push('/adjust-salary/custom-item')"
        >
          <i class="el-icon-edit"></i>自定义定调薪项
        </span>
      </el-row>
    </header>
    <div class="screening">
      <div class="waitReport" v-if="isShowWaitReport">
        待定薪 {{ notConfirmCount ? notConfirmCount : 0 }} 人，
        <span @click="searchNot">查看人员</span>
        <i
          class="el-icon-close close-style"
          @click="isShowWaitReport = false"
        ></i>
      </div>
      <div class="check-staff-menu">
        <div style="margin-bottom: 20px">
          <el-button style="margin-right: 10px" @click="openScreen"
            >筛选</el-button
          >
          <el-input
            placeholder="请输入姓名\工号\证件号码"
            prefix-icon="iconiconfonticonfontsousuo1 iconfont"
            @keyup.enter.native="handleSearch"
            v-model="ruleForm.key"
            class="search-input"
          ></el-input>
        </div>

        <div class="right" style="display: flex; margin-bottom: 20px">
          <el-button
            type="primary"
            @click="handleSyncEmployee"
            :loading="syncLoading"
          >
            同步新增人员
          </el-button>
          <el-button
            type="primary"
            @click="batchAdjust"
            v-if="
              privilegeVoList.includes(
                'salary.compute.salaryArchive.importBatch'
              )
            "
          >
            批量定调薪
          </el-button>
          <el-dropdown
            trigger="click"
            class="more-operation"
            placement="bottom"
          >
            <el-button>
              更多功能
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                @click.native="batchImport('historytImport')"
                v-if="
                  privilegeVoList.includes(
                    'salary.compute.salaryArchive.importHis'
                  )
                "
              >
                历史定调薪导入
              </el-dropdown-item>
              <el-dropdown-item
                @click.native="batchImport('allExport')"
                v-if="
                  privilegeVoList.includes(
                    'salary.compute.salaryArchive.exportTotal'
                  )
                "
              >
                全部定调薪记录导出
              </el-dropdown-item>
              <el-dropdown-item
                @click.native="batchImport('newExport')"
                v-if="
                  privilegeVoList.includes(
                    'salary.compute.salaryArchive.exportRecent'
                  )
                "
              >
                最新定调薪记录导出
              </el-dropdown-item>
              <el-dropdown-item @click.native="handleExportTax">
                纳税主体公司记录导出
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <div class="staff-table">
        <el-table
          :data="list"
          class="check-staff_table"
          v-loading="loading"
          :width="screenWidth"
          :header-cell-style="{ background: '#F1F1F1' }"
          stripe
          border
        >
          <el-table-column
            type="index"
            label="序号"
            width="55"
          ></el-table-column>
          <el-table-column
            prop="empName"
            label="姓名"
            width="140"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span class="table-name" @click="handleName(scope.row)">
                {{ scope.row.empName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="工号"
            width="82"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.empNo }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="idNo"
            label="证件号码"
            min-width="200"
          ></el-table-column>
          <el-table-column
            prop="departmentName"
            label="部门"
            width="180"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span v-if="!isWechatWorkEnv">{{
                scope.row.departmentName
              }}</span>
              <span v-if="isWechatWorkEnv"
                ><WechatUserNameOrDepartmentName
                  :departmentId="scope.row.departmentName"
              /></span>
            </template>
          </el-table-column>
          <el-table-column
            prop="taxSubName"
            label="公司名称"
            width="180"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column prop="empType" label="员工类型" min-width="140">
            <template slot-scope="scope">
              {{ scope.row.empType | filterEmpType }}
            </template>
          </el-table-column>
          <el-table-column prop="empStatus" label="员工状态" min-width="140">
            <template slot-scope="scope">
              {{ scope.row.empStatus | filterEmployStatus }}
            </template>
          </el-table-column>
          <el-table-column prop="empDay" label="入职日期" min-width="140">
            <template slot-scope="scope">{{ scope.row.empDay }}</template>
          </el-table-column>
          <el-table-column
            prop="sumSalary"
            label="薪资合计"
            min-width="140"
          ></el-table-column>
          <el-table-column
            prop="effectiveDate"
            label="生效日期"
            min-width="140"
          ></el-table-column>
          <el-table-column label="操作" fixed="right" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="adjustSalary(scope.row)"
                v-if="
                  scope.row.sumSalary &&
                  privilegeVoList.includes(
                    'salary.compute.salaryArchive.fixSalary'
                  )
                "
              >
                调薪
              </el-button>
              <el-button
                type="text"
                @click="fixedSalary(scope.row)"
                v-if="
                  !scope.row.sumSalary &&
                  privilegeVoList.includes(
                    'salary.compute.salaryArchive.fixSalary'
                  )
                "
              >
                定薪
              </el-button>
              <el-button type="text" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page="ruleForm.currPage"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[20, 50, 100, 200]"
          :total="total"
          class="staff-page"
        ></el-pagination>
      </div>
    </div>
    <!-- 定调薪 -->
    <salaryChange @getList="getList" ref="salaryChange"></salaryChange>
    <!-- 筛选 -->
    <el-dialog
      title="筛选"
      :visible.sync="isShowScreening"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="720px"
      ref="screenForm"
      class="screen-dialog"
    >
      <el-form
        :model="ruleForm.queryFilterParam"
        ref="screenForm"
        label-width="180px"
        class="demo-ruleForm"
      >
        <el-form-item label="公司名称">
          <el-select
            v-model="ruleForm.queryFilterParam.taxSubIds[0]"
            placeholder="请选择"
            filterable
            clearable
          >
            <el-option
              v-for="(item, index) in taxSubList2"
              :label="item.taxSubName"
              :value="item.taxSubId"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="入职日期">
          <el-date-picker
            v-model="enterTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="最后工作日">
          <el-date-picker
            v-model="lastEmployTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <div class="shortCon">
          <el-form-item label="是否定薪">
            <radios
              ref="adjustSalary"
              radiosRef="adjustSalary"
              :screenOption="adjustSalaryOptions"
              @handleRadio="handleEnumAdjustSalaryTypes"
            ></radios>
          </el-form-item>
        </div>
        <div class="shortCon">
          <el-form-item label="员工类型">
            <radios
              ref="enumEmpType"
              radiosRef="enumEmpType"
              :screenOption="enumEmpTypeOption"
              @handleRadio="handleEnumEmpTypes"
            ></radios>
          </el-form-item>
        </div>
        <div class="shortCon">
          <el-form-item label="员工状态">
            <radios
              ref="employStatus"
              radiosRef="employStatus"
              :screenOption="employStatusOption"
              @handleRadio="handleEnumEmpStatuses"
            ></radios>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer">
        <el-button @click="resetSreen">重置</el-button>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </div>
    </el-dialog>
    <!-- 历史定调薪导入 -->
    <import-data
      ref="import"
      :radioList="radioList"
      :title="importStatus === 'batch' ? '批量定调薪' : '历史定调薪导入'"
      :apiCheck="
        importStatus === 'batch'
          ? baseUrl + '/api/hrsaas-salary/salary/adjust/batch-adjust-check'
          : baseUrl + '/api/hrsaas-salary/salary/adjust/history-adjust-check'
      "
      :apiDownloadLog="
        importStatus === 'batch'
          ? 'adjustSalaryStore/actionDownloadAdjustErrorLog'
          : 'adjustSalaryStore/actionDownloadAdjustHistoryErrorLog'
      "
      :apiDownloadTemplate="
        importStatus === 'batch'
          ? 'adjustSalaryStore/actionDownloadAdjustTemplate'
          : 'adjustSalaryStore/actionDownloadHistoryAdjustTemplate'
      "
      :downloadQueryObj="ruleForm"
      :importStatus="importStatus"
      :parameterData="parameterData"
      sendRadio="BY_ID_NO"
      @changeRadioValue="changeRadioValue"
      :impoartAction="
        importStatus === 'batch'
          ? 'adjustSalaryStore/actionAdjustImport'
          : 'adjustSalaryStore/actionHistoryAdjustImport'
      "
      @getLoading="getList"
      :uploadFileData="uploadFileData"
      :tips="
        importStatus === 'batch'
          ? '适合场景：批量导入入职员工的定薪数据和在职员工的调薪数据'
          : '适合场景：补充导入员工历史定调薪数据'
      "
    ></import-data>
    <!-- 全部定调薪记录导出 -->
    <el-dialog
      :title="'全部定调薪记录导出'"
      :visible.sync="isShowAllExport"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="630px"
      class="export-dialog"
    >
      <p style="margin: 0 0 30px 20px">请选择导出的数据范围：</p>
      <el-form
        :model="allExportForm"
        ref="allExportForm"
        label-width="140px"
        class="demo-ruleForm"
      >
        <el-form-item label="数据范围" class="data-range">
          <el-checkbox-group v-model="allExportForm.enumAdjustSalaryType">
            <el-checkbox-button
              v-for="item in exportDataRangeList"
              :label="item.value"
              :key="item.value"
              >{{ item.label }}</el-checkbox-button
            >
          </el-checkbox-group>
          <el-tooltip class="item" effect="light" placement="right">
            <div slot="content" style="line-height: 25px">
              不选择定薪和调薪时，可导出列表中查询出的员工记录
            </div>
            <i
              class="iconfont iconwenhao"
              style="margin-left: 10px; font-size: 22px"
            ></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="生效日期" style="margin-bottom: 0">
          <el-date-picker
            v-model="allExportForm.enterTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="isShowAllExport = false">取消</el-button>
        <el-button type="primary" @click="exportAll">导出</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { baseUrl } from "@/request/fetch";
import * as constData from "../paymaster/util/constData";
import radios from "@/components/tool/radios";
import salaryChange from "./components/salaryChange";
import importData from "@/components/tool/importData";
import {
  apiExportTaxSub,
  apiGetSyncEmployee,
  apiDelEmployee,
} from "./store/api";
import { isWechatWorkEnv } from "@/util/wechat";
import WechatUserNameOrDepartmentName from "../staffManage/components/wechatUserNameOrDepartmentName.vue";

export default {
  data() {
    return {
      isWechatWorkEnv: isWechatWorkEnv,
      screenWidth: document.body.clientWidth, // 屏幕尺寸宽度
      screenHeight: document.body.clientHeight - 330, // 屏幕尺寸高度
      isShowScreening: false, // 筛选弹窗是否显示
      total: 0,
      ruleForm: {
        key: "",
        currPage: 1,
        pageSize: 20,
        queryFilterParam: {
          taxSubIds: [""], //公司
          departName: "", //部门
          enterStartTime: "", //入职筛选开始时间
          enterEndTime: "", //入职筛选截止时间
          lastEmployStartTime: "", //最后工作开始时间
          lastEmployEndTime: "", //最后工作截止时间
          enumAdjustSalaryType: [], //是否定薪
          enumEmpTypes: [], //用工性质，
          enumEmpStatuses: ["ON_THE_JOB"], //员工状态
        },
      },
      enterTime: "", //入职日期数组
      lastEmployTime: "", //最后工作日数组
      hasTaxStatusIndex: "",
      list: [], // 表格数据
      loading: false,
      radioList: [
        { lable: "BY_EMP_NO", title: "通过员工工号匹配人员" },
        { lable: "BY_ID_NO", title: "通过证件号码匹配人员" },
      ],
      parameterData: {
        importType: "BY_ID_NO",
      },
      uploadFileData: {
        uuid: "",
        importType: "BY_ID_NO",
      },
      enumEmpTypeOption: constData.enumEmpTypeOption,
      employStatusOption: constData.employStatusOption,
      adjustSalaryOptions: constData.adjustSalaryOptions,
      importStatus: "",
      isShowAllExport: false,
      exportDataRangeList: [
        {
          label: "定薪",
          value: "CONFIRM_SALARY",
        },
        {
          label: "调薪",
          value: "ADJUST_SALARY",
        },
      ],
      allExportForm: {
        enumAdjustSalaryType: ["CONFIRM_SALARY", "ADJUST_SALARY"],
        enterTime: "",
      },
      notConfirmCount: 0, //未定薪人数
      isShowWaitReport: true,
      searchNotFlg: false,
      taxSubList: [],
      taxSubList2: [], //筛选用的纳税主体
      syncLoading: false,
      baseUrl: baseUrl,
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
      taxSubjectInfoList: (state) => state.taxSubjectInfoList,
      cityList: (state) => [{ code: "", name: "全部" }].concat(state.cityList),
    }),
  },
  watch: {
    isShowWaitReport(val) {
      if (val) {
        this.screenHeight = document.body.clientHeight - 300;
      } else {
        this.screenHeight = document.body.clientHeight - 240;
      }
    },
  },
  components: {
    radios,
    salaryChange,
    importData,
    WechatUserNameOrDepartmentName,
  },
  created() {
    this.getList();
    let taxSubjectInfoList = this.taxSubjectInfoList.filter((i) => i.taxSubId);
    this.taxSubList = taxSubjectInfoList.filter((i) => i.taxEnableYn);
    this.taxSubList = this.taxSubList.filter(
      (i) => i.accreditStatus === "SUCCESS"
    );
    this.taxSubList2 = this.taxSubjectInfoList.filter((i) => i.taxEnableYn);
  },
  mounted() {
    const that = this;
    window.onresize = () => {
      return (() => {
        window.screenWidth = document.body.clientWidth;
        that.screenWidth = window.screenWidth;
        this.screenHeight = document.body.clientHeight - 260;
      })();
    };
  },
  methods: {
    getList() {
      this.loading = true;
      this.$store
        .dispatch("adjustSalaryStore/actionPostAdjustEmpList", this.ruleForm)
        .then((res) => {
          if (res.success && res.data) {
            this.list = res.data.data;
            this.total = res.data.count;
            if (this.list.length > 0) {
              this.notConfirmCount = this.list[0].notConfirmCount;
            }
          }
          this.loading = false;
        });
    },
    //查询未定薪人员
    searchNot() {
      this.searchNotFlg = true;
      this.ruleForm.queryFilterParam.enumAdjustSalaryType = [
        "NOT_CONFIRM_SALARY",
      ];
      this.ruleForm.queryFilterParam.enumEmpStatuses = ["ON_THE_JOB"];
      this.handleSearch();
    },
    //打开筛选弹窗
    openScreen() {
      this.isShowScreening = true;
      this.$nextTick(() => {
        if (this.searchNotFlg) {
          this.$refs.adjustSalary.changeModula("NOT_CONFIRM_SALARY");
          this.$refs.employStatus.changeModula("ON_THE_JOB");
        }
      });
    },
    //查询
    handleSearch() {
      //入职日期
      if (this.enterTime) {
        this.ruleForm.queryFilterParam.enterStartTime = this.enterTime[0];
        this.ruleForm.queryFilterParam.enterEndTime = this.enterTime[1];
      } else {
        this.ruleForm.queryFilterParam.enterStartTime = "";
        this.ruleForm.queryFilterParam.enterEndTime = "";
      }
      //最后工作日
      if (this.lastEmployTime) {
        this.ruleForm.queryFilterParam.lastEmployStartTime =
          this.lastEmployTime[0];
        this.ruleForm.queryFilterParam.lastEmployEndTime =
          this.lastEmployTime[1];
      } else {
        this.ruleForm.queryFilterParam.lastEmployStartTime = "";
        this.ruleForm.queryFilterParam.lastEmployEndTime = "";
      }

      this.ruleForm.currPage = 1;
      this.getList();
      this.isShowScreening = false;
    },
    //筛选重置
    resetSreen() {
      for (let key in this.ruleForm.queryFilterParam) {
        if (
          ![
            "enumEmpTypes",
            "enumEmpStatuses",
            "enumAdjustSalaryType",
            "taxSubIds",
          ].includes(key)
        ) {
          this.ruleForm.queryFilterParam[key] = "";
          this.$refs.enumEmpType.changeNoType();
          this.$refs.employStatus.changeNoType();
          this.$refs.adjustSalary.changeNoType();
          this.enterTime = "";
          this.lastEmployTime = "";
        }
      }
      this.hasTaxStatusIndex = "";
      this.ruleForm.queryFilterParam.taxSubIds = [""];
      this.searchNotFlg = false;
    },
    //显示页数
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.ruleForm.currPage = 1;
      this.getList();
    },
    //翻页
    handleCurrentChange(val) {
      this.ruleForm.currPage = val;
      this.getList();
    },
    //定薪
    fixedSalary(data) {
      data.adjustSalaryType = "CONFIRM_SALARY";
      this.$store.commit("adjustSalaryStore/SET_CURRENTITEMOBJ", data);
      this.$refs.salaryChange.showCompany();
    },
    //调薪
    adjustSalary(data) {
      data.adjustSalaryType = "ADJUST_SALARY";
      this.$store.commit("adjustSalaryStore/SET_CURRENTITEMOBJ", data);
      this.$refs.salaryChange.showCompany();
    },
    //定调薪详情
    handleName(data) {
      this.$store.commit("adjustSalaryStore/SET_CURRENTITEMOBJ", data);
      this.$router.push({
        path: "/adjust-salary/adjust-detail",
        query: {
          empId: data.empId,
        },
      });
    },
    //批量定调薪
    batchAdjust() {
      this.importStatus = "batch";
      this.parameterData.importType = "BY_ID_NO";
      this.$refs.import.show();
    },
    //同步新增人员
    handleSyncEmployee() {
      this.syncLoading = true;
      apiGetSyncEmployee()
        .then((res) => {
          if (res.success) {
            this.$message.success("同步成功");
            this.getList();
          } else {
            this.$message.error("同步失败");
          }
        })
        .finally(() => {
          this.syncLoading = false;
        });
    },
    //更多操作
    batchImport(val) {
      if (val === "historytImport") {
        this.importStatus = val;
        this.$refs.import.show();
      } else if (val === "allExport") {
        this.isShowAllExport = true;
      } else {
        this.$store.dispatch(
          "adjustSalaryStore/actionExportNewAdjust",
          this.ruleForm
        );
      }
    },
    //纳税主体公司记录导出
    handleExportTax() {
      apiExportTaxSub(this.ruleForm);
    },
    //全部定调薪记录导出
    exportAll() {
      //去不定调薪记录弹窗参数
      if (this.allExportForm.enterTime) {
        this.allExportForm.effectDateStart = this.allExportForm.enterTime[0];
        this.allExportForm.effectDateEnd = this.allExportForm.enterTime[1];
      } else {
        this.allExportForm.effectDateStart = "";
        this.allExportForm.effectDateEnd = "";
      }
      //列表筛选参数
      //入职日期
      if (this.enterTime) {
        this.ruleForm.queryFilterParam.enterStartTime = this.enterTime[0];
        this.ruleForm.queryFilterParam.enterEndTime = this.enterTime[1];
      } else {
        this.ruleForm.queryFilterParam.enterStartTime = "";
        this.ruleForm.queryFilterParam.enterEndTime = "";
      }
      //最后工作日
      if (this.lastEmployTime) {
        this.ruleForm.queryFilterParam.lastEmployStartTime =
          this.lastEmployTime[0];
        this.ruleForm.queryFilterParam.lastEmployEndTime =
          this.lastEmployTime[1];
      } else {
        this.ruleForm.queryFilterParam.lastEmployStartTime = "";
        this.ruleForm.queryFilterParam.lastEmployEndTime = "";
      }
      let assignObj = Object.assign(this.ruleForm, this.allExportForm);
      this.$store.dispatch(
        "adjustSalaryStore/actionExportAllAdjust",
        assignObj
      );
      this.isShowAllExport = false;
    },
    //导入 按钮切换
    changeRadioValue(val) {
      this.parameterData.importType = val;
      this.uploadFileData.importType = val;
    },
    //用工性质
    handleEnumEmpTypes(val) {
      this.ruleForm.queryFilterParam.enumEmpTypes = val;
    },
    //员工状态
    handleEnumEmpStatuses(val) {
      this.ruleForm.queryFilterParam.enumEmpStatuses = val;
    },
    //是否定薪
    handleEnumAdjustSalaryTypes(val) {
      this.ruleForm.queryFilterParam.enumAdjustSalaryType = val;
      if (
        this.ruleForm.queryFilterParam.enumAdjustSalaryType.length === 1 &&
        this.ruleForm.queryFilterParam.enumAdjustSalaryType[0] ===
          "NOT_CONFIRM_SALARY"
      ) {
        this.searchNotFlg = true;
      } else {
        this.searchNotFlg = false;
      }
    },
    //删除
    handleDelete(row) {
      let list = [{ empId: row.empId, taxSubId: row.taxSubId }];
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        closeOnPressEscape: false,
        confirmButtonClass: "confirm-small",
      }).then(() => {
        apiDelEmployee({ list }).then((res) => {
          if (res.success) {
            this.$message.success("删除成功");
            this.getList();
          }
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.set-adjust-salary {
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }

  .check-staff-menu {
    margin-bottom: 0px;
  }

  .screening {
    padding: 0 20px;
    .waitReport {
      height: 40px;
      line-height: 40px;
      color: #909399;
      border-left: 4px solid #ff9500;
      background: #fdf7e9;
      padding-left: 20px;
      margin-top: 20px;
      position: relative;
      // margin-bottom: 20px;
      width: 100%;
      box-sizing: border-box;
      span {
        color: #4f71ff;
        cursor: pointer;
      }
      .close-style {
        position: absolute;
        top: 13px;
        right: 10px;
        cursor: pointer;
      }
    }
  }
  .staff-table {
    .check-staff_table {
      overflow-x: auto;
    }
    position: relative;
    .staff-page {
      margin-top: 10px;
      text-align: right;
    }
    .table-name {
      color: $mainColor;
      cursor: pointer;
    }
    .table-operate {
      display: flex;
      justify-content: space-between;
    }
  }

  .screening-box span:first-child {
    margin-right: 10px;
  }
}

/deep/ .screen-dialog {
  .el-dialog {
    margin-top: 10vh !important;
    .el-select {
      width: 350px;
    }
  }
}

// 全部定调薪记录导出弹窗
/deep/ .export-dialog {
  /deep/ .data-range {
    .el-form-item__content {
      display: flex;
    }
  }
}
</style>
