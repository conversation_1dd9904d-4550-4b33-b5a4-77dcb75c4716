import {
  apiPostAdjustEmpList,
  apiGetConfigList,
  apiPostSortConfig,
  apiSaveSalaryConfig,
  apiSaveSalaryModifyConfig,
  apiSaveSalary,
  apiGetAdjustSalary,
  apiSaveAdjustSalary,
  apiGetAdjustSalaryDetail,
  apiUndoAdjustDetail,
  apiSaveAdjustSalaryDetail,
  apiDownloadAdjustErrorLog,
  apiDownloadAdjustHistoryErrorLog,
  apiDownloadAdjustTemplate,
  apiDownloadHistoryAdjustTemplate,
  apiAdjustImport,
  apiHistoryAdjustImport,
  apiExportAllAdjust,
  apiExportNewAdjust,
  apiDownloadAdjustTaxErrorLog,
  apiDownloadAdjustTaxTemplate,
  apiAdjustImportTax
} from './api';

//企业人员定调薪列表
export const actionPostAdjustEmpList = ({ dispatch }, form) => {
  return apiPostAdjustEmpList(form);
};

//自定义定调薪项目列表
export const actionGetConfigList = ({ dispatch }) => {
  return apiGetConfigList();
};

//自定义定调薪项目排序
export const actionPostSortConfig = ({ dispatch }, data) => {
  return apiPostSortConfig(data);
};

//保存新增自定义定调薪项目
export const actionSaveSalaryConfig = ({ dispatch }, data) => {
  return apiSaveSalaryConfig(data);
};

//保存修改自定义定调薪项目
export const actionSaveSalaryModifyConfig = ({ dispatch }, data) => {
  return apiSaveSalaryModifyConfig(data);
};

//调薪查询
export const actionGetAdjustSalary = ({ dispatch }, id) => {
  return apiGetAdjustSalary(id);
};

//定薪
export const actionSaveSalary = ({ dispatch }, data) => {
  return apiSaveSalary(data);
};

//调薪
export const actionSaveAdjustSalary = ({ dispatch }, data) => {
  return apiSaveAdjustSalary(data);
};

//调薪查询
export const actionGetAdjustSalaryDetail = ({ dispatch }, id) => {
  return apiGetAdjustSalaryDetail(id);
};

//调薪详情撤销调薪
export const actionUndoAdjustDetail = ({ dispatch }, id) => {
  return apiUndoAdjustDetail(id);
};

//调薪详情编辑保存
export const actionSaveAdjustSalaryDetail = ({ dispatch }, data) => {
  return apiSaveAdjustSalaryDetail(data);
};

//批量定调薪校验-导入日志下载
export const actionDownloadAdjustErrorLog = ({ dispatch }, data) => {
  return apiDownloadAdjustErrorLog(data);
};

//历史定调薪校验-导入日志下载
export const actionDownloadAdjustHistoryErrorLog = ({ dispatch }, data) => {
  return apiDownloadAdjustHistoryErrorLog(data);
};

//批量定调薪-导入模板下载
export const actionDownloadAdjustTemplate = ({ dispatch }) => {
  return apiDownloadAdjustTemplate();
};

//历史定调薪-导入模板下载
export const actionDownloadHistoryAdjustTemplate = ({ dispatch }) => {
  return apiDownloadHistoryAdjustTemplate();
};

//批量定调薪导入
export const actionAdjustImport = ({ dispatch }, data) => {
  return apiAdjustImport(data);
};

//历史定调薪导入
export const actionHistoryAdjustImport = ({ dispatch }, data) => {
  return apiHistoryAdjustImport(data);
};

//全部定调薪记录导出
export const actionExportAllAdjust = ({ dispatch }, data) => {
  return apiExportAllAdjust(data);
};

//最新定调薪记录导出
export const actionExportNewAdjust = ({ dispatch }, data) => {
  return apiExportNewAdjust(data);
};

//批量定调薪校验-导入日志下载
export const actionDownloadAdjustTaxErrorLog = ({ dispatch }, data) => {
  return apiDownloadAdjustTaxErrorLog(data);
};

//批量定调薪-导入模板下载
export const actionDownloadAdjustTaxTemplate = ({ dispatch }) => {
  return apiDownloadAdjustTaxTemplate();
};

//批量定调薪导入
export const actionAdjustImportTax = ({ dispatch }, data) => {
  return apiAdjustImportTax(data);
};
