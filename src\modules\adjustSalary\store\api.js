import { fetch, fetchFile } from 'request/fetch';
import oldFetch from 'request/oldFetch';

//企业人员定调薪列表
export function apiPostAdjustEmpList(form) {
  return fetch({
    url: '/api/hrsaas-salary/salary/adjust/emp-list',
    method: 'post',
    data: form
  });
}

//自定义定调薪项目列表
export function apiGetConfigList() {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/config-list',
    method: 'get'
  });
}

//自定义定调薪项目排序
export function apiPostSortConfig(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/sort-config',
    method: 'post',
    data
  });
}

//保存新增自定义定调薪项目
export function apiSaveSalaryConfig(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/salary-config',
    method: 'post',
    data
  });
}

//编辑自定义定调薪项目
export function apiSaveSalaryModifyConfig(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/modify-config',
    method: 'post',
    data
  });
}

//调薪查询
export function apiGetAdjustSalary(id) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/adjust-salary/' + id,
    method: 'get'
  });
}

//定薪
export function apiSaveSalary(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/confirm-salary',
    method: 'post',
    data
  });
}

//调薪
export function apiSaveAdjustSalary(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/adjust-salary',
    method: 'post',
    data
  });
}

//调薪详情查询
export function apiGetAdjustSalaryDetail(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/adjust-salary-detail',
    method: 'post',
    data
  });
}

//调薪详情撤销调薪
export function apiUndoAdjustDetail(id) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/undo-adjust-detail/' + id,
    method: 'post'
  });
}

//调薪详情编辑保存
export function apiSaveAdjustSalaryDetail(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/edit-adjust-detail',
    method: 'post',
    data
  });
}

//批量定调薪校验-导入日志下载
export function apiDownloadAdjustErrorLog(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/adjust-error/' + data.uuid,
    method: 'get',
    responseType: 'blob'
  });
}

//历史定调薪校验-导入日志下载
export function apiDownloadAdjustHistoryErrorLog(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/adjust-his-error/' + data.uuid,
    method: 'get',
    responseType: 'blob'
  });
}

//批量定调薪-导入模板下载
export function apiDownloadAdjustTemplate() {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/batch-adjust/template/download',
    method: 'post',
    responseType: 'blob'
  });
}

//历史定调薪-导入模板下载
export function apiDownloadHistoryAdjustTemplate() {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/history-adjust/template/download',
    method: 'post',
    responseType: 'blob'
  });
}

//批量定调薪导入
export function apiAdjustImport(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/batch-adjust',
    method: 'post',
    data: data
  });
}

//历史定调薪导入
export function apiHistoryAdjustImport(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/history-adjust',
    method: 'post',
    data: data
  });
}

//全部定调薪记录导出
export function apiExportAllAdjust(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/export-all-adjust',
    method: 'post',
    data,
    responseType: 'blob'
  });
}

//最新定调薪记录导出
export function apiExportNewAdjust(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/export-new-adjust',
    method: 'post',
    data,
    responseType: 'blob'
  });
}

//新增纳税主体公司
export function apiAddEmpTaxSub(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/addEmpTaxSub',
    method: 'post',
    data
  });
}

//修改员工纳税主体
export function apiModifyEmpTaxSub(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/modifyEmpTaxSub',
    method: 'post',
    data
  });
}

//纳税主体公司记录
export function apiGetEmpTaxSubList(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/empTaxSubList',
    method: 'post',
    data
  });
}

//批量入导纳税主体-错误日志下载
export function apiDownloadAdjustTaxErrorLog(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/tax-sub/error-log/' + data.uuid,
    method: 'get',
    responseType: 'blob'
  });
}

//批量入导纳税主体-导入模板下载
export function apiDownloadAdjustTaxTemplate() {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/tax-subject/template/download',
    method: 'post',
    responseType: 'blob'
  });
}

//批量入导纳税主体
export function apiAdjustImportTax(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/import-tax-subject',
    method: 'post',
    data: data
  });
}

//删除员工纳税主体
export function apiDelEmpTaxSub(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/delEmpTaxSub',
    method: 'post',
    data: data
  });
}

//纳税主体公司记录导出
export function apiExportTaxSub(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/export-tax-subject',
    method: 'post',
    data: data,
    responseType: 'blob'
  });
}

//生成员工默认纳税主体
export function apiGenerateEmpTaxSub(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/generateEmpTaxSub',
    method: 'post',
    data: data
  });
}

//同步人员信息
export function apiGetSyncEmployee(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/syncEmployee',
    method: 'post',
    data: data
  });
}

//薪资档案-删除
export function apiDelEmployee(data) {
  return oldFetch({
    url: 'hrsaas-salary/salary/adjust/delEmployee',
    method: 'post',
    data: data
  });
}