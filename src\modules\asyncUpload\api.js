import oldFetch from 'request/oldFetch'

//模板下载
export function apiDownloadTemplate(data) {
  return oldFetch({
    url: 'hrsaas-salary/async/templateDownload',
    method: 'post',
    data,
    responseType: 'blob'
  });
}

//获取工资项
export function apiGetSalaryItem(id) {
  return oldFetch({
    url: 'hrsaas-salary/salary/item/salary-config/' + id,
    method: 'get'
  });
}

//确认导入
export function apiConfirmUpload(data) {
  return oldFetch({
    url: 'hrsaas-salary/async/comfirmUpload',
    method: 'post',
    data,
  });
}

//导入结果列表
export function apiGetImportTaskList(data) {
  return oldFetch({
    url: 'hrsaas-salary/async/importTaskList',
    method: 'post',
    data,
  });
}

//下载失败记录
export function apiDownloadFail(data) {
  return oldFetch({
    url: 'hrsaas-salary/async/downLoadFail',
    method: 'get',
    params: data,
    responseType: 'blob',
  });
}