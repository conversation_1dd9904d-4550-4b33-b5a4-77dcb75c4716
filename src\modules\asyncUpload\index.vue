<template>
  <div class="async-upload">
    <header>
      <span @click="$router.go(-1)" class="back-style">返回</span>
      <span class="header-line">|</span>
      <span class="header-title">{{ title }}</span>
    </header>
    <div class="main-content">
      <el-steps
        :active="active"
        align-center
        process-status="finish"
        finish-status="success"
      >
        <el-step title="上传工资条"></el-step>
        <el-step title="工资项映射"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
      <upload
        v-if="active == 0"
        @changeStep="changeStep"
        @handleUploadFile="handleUploadFile"
      ></upload>
      <mapping
        v-if="active == 1"
        @changeStep="changeStep"
        :uploadFileData="uploadFileData"
      ></mapping>
      <div v-if="active == 2">
        <div class="upload-finish">
          <i class="el-icon-success"></i>
          <p>上传完成</p>
          <span>工资条上传完成，可点击下方按钮查看上传结果</span>
        </div>
        <div class="footer">
          <el-button size="small" @click="handleShowResult">
            查看上传结果
          </el-button>
          <el-button type="primary" size="small" @click="$router.go(-1)">
            关闭
          </el-button>
        </div>
      </div>
    </div>
    <!-- 查看上传结果 -->
    <upload-result
      ref="asyncUpload"
      importType="SALARY_STUBS_IMPORT"
    ></upload-result>
  </div>
</template>
<script>
import upload from "./upload";
import mapping from "./mapping";
import uploadResult from "@/components/upload-result/index";
export default {
  props: ["title", "importType"],
  components: { upload, mapping, uploadResult },
  data() {
    return {
      isShowDialog: false,
      active: 0,
    };
  },
  methods: {
    changeStep(step) {
      this.active = step;
    },
    handleUploadFile(data) {
      this.uploadFileData = data;
    },
    //查看导入结果
    handleShowResult() {
      this.$refs.asyncUpload.handleShowResult();
    },
  },
};
</script>
<style lang="scss" scoped>
.async-upload {
  padding: 0 32px 0 32px;
  header {
    display: flex;
    align-items: center;
    height: auto !important;
    padding: 28px 0;
    box-sizing: border-box;
    border-bottom: 1px solid #eaeaea;
    .back-style {
      font-size: 16px;
    }
    .header-line {
      font-size: 22px;
      padding: 0px 18px;
      color: rgb(136, 136, 136);
    }
    .header-title {
      font-size: 24px;
    }
  }
  .el-steps {
    margin: 30px auto 50px auto;
    width: 910px;
  }
  .main-content {
    min-height: 570px;
    padding: 0;
  }
  .upload-finish {
    color: #6a6f7f;
    text-align: center;
    i {
      font-size: 72px;
    }
    p {
      font-size: 24px;
      font-weight: bold;
      color: #070f29;
      margin: 20px 0;
    }
  }
  .footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    border-top: 1px solid #ededed;
    box-sizing: border-box;
    text-align: center;
    padding: 14px 0;
    background: #fff;
    /deep/.el-button--primary {
      font-size: 12px;
    }
  }
}

/deep/.el-dialog__header {
  border-bottom: 1px solid #eaeaea;
  margin: 0 15px;
  padding: 10px 0;
  .el-dialog__title {
    font-size: 16px;
  }
  .el-dialog__headerbtn {
    top: 15px;
  }
}
/deep/.el-dialog__body {
  padding: 0;
}
.result-dialog {
  .main-content {
    height: calc(100vh - 295px);
  }
  /deep/.el-table {
    margin: 30px 20px 0 20px;
    width: calc(100% - 40px);
  }
}
</style>