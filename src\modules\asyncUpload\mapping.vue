<template>
  <div class="mapping">
    <div class="content">
      <div class="title">
        <p>工资条薪资项目</p>
        <p>系统薪资项目</p>
      </div>
      <div class="item-list">
        <div
          v-for="(item, index) in uploadFileData.fileData"
          :key="index"
          class="item"
        >
          <p class="disable">
            {{ item }}
          </p>
          <el-image :src="arrowImg"></el-image>
          <el-select v-model="selectItemList[index]" placeholder="请选择">
            <el-option
              v-for="item in systemList"
              :key="item.configCode"
              :label="item.name"
              :value="item.configCode"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <div class="footer">
      <el-button size="small" @click="handlePre">上一步</el-button>
      <el-button
        type="primary"
        size="small"
        v-prevent-re-click
        @click="handleNext"
      >
        下一步
      </el-button>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { apiGetSalaryItem, apiConfirmUpload } from "./api";
export default {
  props: ["uploadFileData"],
  computed: {
    ...mapState("salaryCalStore", {
      salaryItem: "salaryItem",
    }),
  },
  data() {
    return {
      systemList: [],
      requireList: [], //必填项列表
      selectItemList: [],
      arrowImg: require("@/assets/images/arrow.png"),
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      let id = this.salaryItem.salaryRuleId;
      let res = await apiGetSalaryItem(id);
      if (res.success) {
        this.systemList = res.data.flat();
        this.requireList = this.systemList.filter((item) => item.enable);
        //默认值处理
        this.uploadFileData.fileData.forEach((item, index) => {
          this.systemList.forEach((it) => {
            if (item === it.name) {
              this.selectItemList[index] = it.configCode;
            }
          });
        });
      }
    },
    //上一步
    handlePre() {
      this.$emit("changeStep", 0);
    },
    //下一步
    handleNext() {
      let requireName = "";
      this.requireList.forEach((item) => {
        if (!this.selectItemList.includes(item.configCode)) {
          requireName = item.name;
        }
      });
      if (requireName) {
        this.$message.error(`【${requireName}】未映射`);
        return;
      }
      let params = {
        checkId: this.$route.query.id,
        uuid: this.uploadFileData.uuid,
        asyncImportType: "SALARY_STUBS_IMPORT",
        cols: this.selectItemList,
      };
      apiConfirmUpload(params).then((res) => {
        if (res.success) {
          this.$emit("changeStep", 2);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.mapping {
  .content {
    width: 750px;
    margin: 0 auto;
    padding-right: 20px;
    box-sizing: border-box;
    .title {
      display: flex;
      font-size: 16px;
      margin-bottom: 24px;
      p:last-child {
        margin-left: 410px;
      }
    }
    .item-list {
      > div {
        display: flex;
      }
    }
    .item {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      .el-image {
        margin: 0 20px;
      }
      /deep/.el-image__inner {
        vertical-align: baseline;
      }
      .disable {
        background: #f4f4f4;
        width: 200px;
        height: 32px;
        line-height: 32px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 4px;
        font-size: 14px;
        color: #888888;
        padding-left: 16px;
        box-sizing: border-box;
      }
      /deep/ input[type="text"] {
        height: 32px;
      }
      /deep/.el-input__inner,
      /deep/.el-input__icon {
        line-height: 32px;
      }
    }
  }
  .footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    border-top: 1px solid #ededed;
    box-sizing: border-box;
    text-align: center;
    padding: 14px 0;
    background: #fff;
    /deep/.el-button--primary {
      font-size: 12px;
    }
  }
}
</style>