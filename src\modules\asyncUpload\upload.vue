<template>
  <div class="upload">
    <el-upload
      drag
      class="upload-demo"
      :headers="heanderToken"
      :action="uploadUrl"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :file-list="fileList"
      accept=".xlsx, .xls"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">拖拽文件到这里上传</div>
      <div class="el-upload__text">
        <el-button type="primary" size="mini" style="margin-top: 16px">
          选择文件
        </el-button>
      </div>
    </el-upload>
    <div class="tip-con">
      <h5>温馨提示：</h5>
      <p class="tip-title">1.整理导入文件</p>
      <p>
        您可以使用已有数据的文件，或者下载标准模板整理数据，点击
        <el-button
          size="mini"
          plain
          icon="iconfont iconxiazai"
          @click="downloadTemplate"
        >
          下载模板
        </el-button>
      </p>
      <p class="note">注：请确保匹配人员字段和必要字段有值</p>
      <p class="tip-title">2.文件相关要求</p>
      <p>请将表头置于第一行，不支持多行表头</p>
      <p>支持xlsx和xls文件，文件不超过5M</p>
    </div>
    <div class="footer">
      <el-button size="small" @click="$router.go(-1)">取消</el-button>
      <el-button
        type="primary"
        size="small"
        @click="hanldeNext"
        :disabled="!uploadFileData"
      >
        下一步
      </el-button>
    </div>
  </div>
</template>
<script>
import { apiDownloadTemplate } from "./api";
import { baseUrl } from "@/request/fetch";
import { getToken } from "@olading/olading-business-ui";
export default {
  data() {
    return {
      uploadUrl: baseUrl + "/api/hrsaas-salary/async/uploadFile",
      heanderToken: {
        Authorization: getToken(),
      },
      fileList: [],
      uploadFileData: null,
    };
  },
  methods: {
    beforeUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isxls = testmsg === "xls" || testmsg === "xlsx";
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isxls) {
        this.$message.warning("上传文件类型只能是 xls,xlsx 格式!");
        this.fileList = [];
      }
      if (!isLt5M) {
        this.$message.warning("上传文件大小不能超过 5MB!");
        this.fileList = [];
      }
      return isxls && isLt5M;
    },
    handleSuccess(res, file) {
      if (res.success) {
        let data = res.data;
        this.fileList = [file];
        this.uploadFileData = data;
        this.$emit("handleUploadFile", data);
      } else {
        this.fileList = [];
        this.$message.warning(res.message);
        this.uploadFileData = null;
        this.$emit("handleUploadFile", null);
      }
    },
    //模板下载
    async downloadTemplate() {
      let data = {
        checkId: this.$route.query.id,
        importType: "SALARY_STUBS_IMPORT",
      };
      await apiDownloadTemplate(data);
    },
    //下一步
    hanldeNext() {
      this.$emit("changeStep", 1);
    },
  },
};
</script>
<style lang="scss" scoped>
.upload {
  .upload-demo {
    width: 750px;
    margin: 0 auto 20px auto;
    /deep/.el-upload-dragger {
      width: 750px;
      height: 236px;
      .el-icon-upload {
        margin: 56px 0 20px 0;
      }
    }
  }
  .tip-con {
    width: 750px;
    padding: 20px;
    margin: 0 auto;
    background: #f6f8ff;
    border-radius: 8px;
    box-sizing: border-box;
    color: #555555;
    h5 {
      font-weight: bold;
      font-size: 20px;
      margin-bottom: 10px;
    }
    p {
      font-size: 14px;
      margin-bottom: 10px;
    }
    .tip-title {
      font-size: 16px;
      margin-top: 20px;
    }
    .note {
      font-size: 14px;
      color: #888;
    }
    /deep/.iconxiazai {
      font-size: 12px;
    }
    .el-button {
      margin-left: 10px;
      color: #4f71ff;
    }
    /deep/.el-button--mini {
      padding: 3px 10px;
      font-size: 12px;
    }
  }
  .footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    border-top: 1px solid #ededed;
    box-sizing: border-box;
    text-align: center;
    padding: 14px 0;
    background: #fff;
    /deep/.el-button--primary {
      font-size: 12px;
    }
  }
}
</style>
