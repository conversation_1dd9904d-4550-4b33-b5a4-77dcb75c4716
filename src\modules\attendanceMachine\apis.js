import { fetch, fetchFile, baseUrl } from 'request/fetch';
import { getToken } from '@olading/olading-business-ui';
//考勤组管理-删除考勤组
export const apiMachineZoneList = async (data) => {
  return fetch({
    url: '/api/attend/machine/zoneList',
    method: 'post',
    data,
  });
};
//人脸跟催
export const apiSendPersonFaceNotice = async (data) => {
  return fetch({
    url: '/api/attend/machine/sendPersonFaceNotice',
    method: 'post',
    data,
  });
};
//模板下载
export const apiPersonTemplateDownload = async () => {
  return fetchFile({
    url: '/api/attend/machine/personTemplateDownload',
    method: 'POST',
  });
};
// 人员导入
export const apiPersonImport = async (zoneId, file) => {
  const url = baseUrl + '/api/attend/machine/personImport';

  const formData = new FormData();
  formData.append('zoneId', zoneId);
  formData.append('file', file.raw);
  return window.fetch(url, {
    method: 'POST',
    headers: {
      Authorization: getToken(),
    },
    body: formData,
  });
};
//考勤机区域添加
export const apiMachineZoneAdd = async (data) => {
  return fetch({
    url: '/api/attend/machine/zoneAdd',
    method: 'post',
    data,
  });
};

//考勤人员信息同步
export const apiMachinePersonRecordSync = async (data) => {
  return fetch({
    url: '/api/attend/machine/personRecordSync',
    method: 'post',
    data,
  });
};

//考勤机区域修改
export const apiMachineZoneUpdate = async (data) => {
  return fetch({
    url: '/api/attend/machine/zoneUpdate',
    method: 'post',
    data,
  });
};

//考勤机区域删除
export const apiMachineZoneDel = async (data) => {
  return fetch({
    url: '/api/attend/machine/zoneDel',
    method: 'post',
    data,
  });
};

//获取考勤组列表
export const apiPostAttendGroupList = async (data) => {
  return fetch({
    url: '/api/attend/group/getAttendGroupList',
    method: 'post',
    data: data,
  });
};

//获取考勤组下的人员列表
export const apiMachineMerchantMemberList = async (data) => {
  return fetch({
    url: '/api/attend/machine/merchantMemberList',
    method: 'post',
    data,
  });
};
//区域下人员信息
export const apiMachineZonePersonList = async (data) => {
  return fetch({
    url: '/api/attend/machine/zonePersonList',
    method: 'post',
    data,
  });
};
//区域下新增人员
export const apiMachineZonePersonAdd = async (data) => {
  return fetch({
    url: '/api/attend/machine/zonePersonAdd',
    method: 'post',
    data,
  });
};
//区域下人员导出
export const apiMachineZonePersonExport = async (data) => {
  return fetchFile({
    url: '/api/attend/machine/personExport',
    method: 'post',
    data,
  });
};

// 区域下人员下发
export const apiMachineZonePersonSend = async (data) => {
  return fetch({
    url: '/api/attend/machine/zonePersonSend',
    method: 'post',
    data,
  });
};
//失败后 重新下发
export const apiMachineZonePersonSendRetry = async (data) => {
  return fetch({
    url: '/api/attend/machine/zonePersonSendRetry',
    method: 'post',
    data,
  });
};
//修改考勤卡号
export const apiMachineCardNumberEdit = async (data) => {
  return fetch({
    url: '/api/attend/machine/cardNumberEdit',
    method: 'post',
    data,
  });
};

//删除考勤人员
export const apiMachineZonePersonDel = async (data) => {
  return fetch({
    url: '/api/attend/machine/zonePersonDel',
    method: 'post',
    data,
  });
};

// 获取已加入的平台列表
export const apiMachineListJoinedPlatforms = async (data) => {
  return fetch({
    url: '/api/attend/machine/listJoinedPlatforms',
    method: 'post',
    data,
  });
};
