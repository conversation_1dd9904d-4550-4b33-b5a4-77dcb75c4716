<template>
  <el-select
    v-model="value"
    filterable
    placeholder="请选择"
    @change="handleChange"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
    </el-option>
  </el-select>
</template>

<script>
import { apiPostAttendGroupList } from "./apis";
export default {
  props: ["formItem"],
  inject: ["oTopSelect"],
  data() {
    return {
      value: "",
      options: [],
    };
  },
  created() {
    this.fetchAttendanceGroups();
  },
  methods: {
    handleChange(v) {
      this.$emit("input", this.formItem.prop, v);
    },
    clearField() {
      this.value = "";
      this.$emit("input", this.formItem.prop, null);
    },
    async fetchAttendanceGroups() {
      const r = await apiPostAttendGroupList({ pageSize: 10000 });
      if (r.success) {
        this.options = r.data.records.map((c) => {
          return {
            value: c.id,
            label: c.agName,
          };
        });
      }
    },
  },
};
</script>

<style></style>
