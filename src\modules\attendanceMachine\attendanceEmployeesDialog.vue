<template>
  <el-dialog title="选择人员" :visible.sync="show" width="70%">
    <div v-if="loading"><i class="el-icon-loading"></i> loading</div>
    <AttendanceEmployeesSelector
      v-else
      :attendanceGroups="attendanceGroups"
      :employees="employees"
      :selectedEmployees="selectedEmployees"
      @changeAttendanceGroup="handleAttendanceGroupChange"
      @confirm="handleConfirm"
      @cancel="close"
    />
  </el-dialog>
</template>

<script>
import { apiPostAttendGroupList, apiMachineMerchantMemberList } from "./apis";
import AttendanceEmployeesSelector from "./components/attendanceEmployeesSelector.vue";
import formatEmployee from './formatEmployee'
export default {
  components: {
    AttendanceEmployeesSelector,
  },
  props:{
    selectedEmployees: Array,
  },
  data() {
    return {
      show: false,
      attendanceGroups: [],
      employees: [],
      loading: true,
    };
  },
  methods: {
    close() {
      this.show = false;
      setTimeout(() => this.resetDialogState(), 300);
    },
    async open() {
      this.show = true;
      try {
        await this.fetchAttendanceGroups();
        await this.fetchEmployees();

        this.loading = false;
      } catch (err) {
        this.loading = false;
      }
    },

    resetDialogState() {
      this.attendanceGroups = [];
      this.employees = [];
      this.loading = true;
    },
    async fetchAttendanceGroups() {
      const r = await apiPostAttendGroupList({ pageSize: 10000 });
      if (r.success) {
        this.attendanceGroups = r.data.records.filter(c => c.allowDeviceSyncRecord).map((c) => {
          return {
            id: c.id,
            name: c.agName,
          };
        })
      }
    },
    async handleAttendanceGroupChange(attendanceGroupId) {
      await this.fetchEmployees(attendanceGroupId);
    },
    async fetchEmployees(attendanceGroupId = 0) {
      if (!attendanceGroupId) {
        attendanceGroupId = this.attendanceGroups[0].id;
      }
      const r = await apiMachineMerchantMemberList({
        attendId: attendanceGroupId,
      });
      if(r.success){
        this.employees = r.data.map(c => {
          return formatEmployee(c)
        })
        console.log(" this.employees", this.employees)
      }
    },  
    async handleConfirm(needAddEmployees) {
      this.$emit("confirm",needAddEmployees );
      this.close();
    },
  },
};
</script>

<style scoped>
/* Add styles specific to the dialog if needed, e.g., padding */
.el-dialog__body {
  padding: 0px 0px 10px 0px; /* Adjust padding around selector */
}
/* Make dialog slightly taller if content overflows */
/* Consider using el-scrollbar within the dialog body if needed */
</style>
