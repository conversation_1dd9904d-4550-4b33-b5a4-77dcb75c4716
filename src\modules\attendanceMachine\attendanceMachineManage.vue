<template>
  <o-container title="考勤机管理" class="attendanceMachinesManage" back="true">
    <!-- 筛选区域 -->
    <o-top-select
      ref="top-select"
      :formJson="topSelectFormJson"
      :immediate="true"
      labelWidth="70px"
      style="margin-top: 20px"
      @search="onSearch"
    />
    <!-- 表格区域 -->
    <o-table
      ref="list"
      :total="total"
      :sticky="true"
      :pagination="{
        fixed: true,
        offsetRight: 22,
        offsetLeft: -20,
        total: 1000,
      }"
      :showPagination="true"
      :tableData="employees"
      :tableHeader="tableHeader"
      :actionButtons="actionButtons"
      :tableHeaderActionButtons="tableHeaderActionButtons"
      emptyHeight="calc(100vh - 450px)"
      :selection="true"
      @selection-change="handleSelectionChange"
      @paginationChange="hanlePaginationChange"
    />
    <Upload
      ref="upload"
      :downloadTemplate="downloadTemplate"
      :uploadFile="uploadFile"
    />
    <AttendanceEmployeesDialog
      ref="attendanceEmployeesDialog"
      :selectedEmployees="selectedEmployees"
      @confirm="confirm"
    />
    <ChangeAttendCardNumberDialog
      ref="changeAttendCardNumberDialog"
      @confirm="handleChangeAttendCardNumber"
    />
  </o-container>
</template>
<script>
import {
  apiMachineZonePersonList,
  apiPersonTemplateDownload,
  apiSendPersonFaceNotice,
  apiMachineZonePersonExport,
  apiMachineZonePersonAdd,
  apiMachineZonePersonSend,
  apiMachineZonePersonSendRetry,
  apiMachineCardNumberEdit,
  apiMachineZonePersonDel,
  apiPersonImport,
} from "./apis.js";
import Upload from "./components/upload.vue";
import AttendanceEmployeesDialog from "./attendanceEmployeesDialog.vue";
import ChangeAttendCardNumberDialog from "./changeAttendCardNumberDialog.vue";
import formatEmployee from "./formatEmployee";
import AttendGroupsSelector from "./attendGroupsSeletor.vue";
export default {
  components: {
    Upload,
    AttendanceEmployeesDialog,
    ChangeAttendCardNumberDialog,
    AttendGroupsSelector,
  },
  computed: {
    belongPlatform() {
      return this.$route.query.belongPlatform;
    },

    zoneID() {
      return this.$route.params.id;
    },
  },
  data() {
    return {
      selectedRows: [],
      searchConditions: {},
      employees: [],
      total: 0,
      selectedEmployees: [],
      // 筛选表单配置
      topSelectFormJson: [
        {
          type: "input",
          item: {
            prop: "userName",
            label: "姓名",
            value: "",
            placeholder: "请输入姓名",
          },
        },
        {
          type: "custom",
          item: {
            prop: "attendId",
            label: "考勤组",
            value: "",
            placeholder: "请选择",
            component: AttendGroupsSelector,
          },
        },
        {
          type: "input",
          item: {
            prop: "deptName",
            label: "部门名称",
            value: "",
            placeholder: "",
          },
        },
      ],

      // 表格表头配置
      tableHeader: [
        { prop: "userName", label: "姓名" },
        { prop: "deptName", label: "部门", width: "120px" },
        { prop: "phone", label: "手机号", width: "120px" },
        { prop: "cardNumber", label: "考勤卡号", width: "120px" },
        {
          prop: "lastNoticeTime",
          label: "人脸跟催",
          width: "120px",
          formatter(row) {
            if (row.lastNoticeTime) {
              return "已通知";
            }

            return "未通知";
          },
        },
        { prop: "empStatus", label: "员工状态", width: "120px" },
        {
          prop: "sendStatus",
          label: "同步状态",
          width: "120px",
          formatter(row) {
            if (row.sendStatus === "NOT_SEND") {
              return "未下发";
            }
            if (row.sendStatus === "HAS_SEND") {
              if (row.sendResult === "SUCCESS") {
                return "下发成功";
              }
              if (row.sendResult === "FAIL") {
                return "下发失败";
              }
            }
          },
        },
      ],

      // 表格操作按钮配置
      actionButtons: [
        {
          label: "修改考勤卡号",
          type: "primary",
          ifShow: (row) =>
            !(row.sendStatus === "HAS_SEND" && row.sendResult === "SUCCESS"),
          click: (row) => this.handleEditAttendanceCard(row),
        },
        {
          label: "下发考勤机",
          type: "primary",
          style: "color:red",
          ifShow: (row) => row.sendStatus === "NOT_SEND",
          click: (row) => this.handleSend(row),
        },
        {
          label: "重新下发",
          type: "primary",
          style: "color:red",
          ifShow: (row) => row.sendResult === "FAIL",
          click: (row) => this.handleResend(row),
        },
        {
          label: "删除",
          type: "danger",
          click: (row) => this.handleDelete(row),
        },
      ],

      // 表格顶部操作按钮配置
      tableHeaderActionButtons: [
        {
          align: "right",
          type: "button",
          label: "增加员工",
          icon: "el-icon-plus",
          ifShow: () => true,
          click: () => this.$refs.attendanceEmployeesDialog.open(),
        },
        {
          align: "right",
          type: "button",
          label: "导入考勤卡号",
          icon: "el-icon-upload",
          props: {
            type: "plain",
          },
          ifShow: () => true,
          click: () => this.$refs.upload.open(),
        },
        {
          align: "right",
          label: "更多操作",
          type: "dropdown",
          style: "margin-left:10px",
          icon: "el-icon-more",
          options: [
            {
              label: "人脸录入跟催",
              ifShow: () => true, // 始终显示
              click: () => this.handleFaceRecognition(),
            },
            {
              label: "批量删除员工",
              ifShow: () => true, // 始终显示
              click: () => this.handleBatchDelete(),
            },
            {
              label: "导出当前报表",
              ifShow: () => true, // 始终显示
              click: () => this.handleExportReport(),
            },
          ],
        },
      ],
    };
  },
  mounted() {
    this.getList();
    this.getAllSelectedEmployees();
  },
  methods: {
    selectEmployees(employees) {
      var n = [...this.selectedEmployees];
      for (var c of employees) {
        if (!this.selectedEmployees.find((d) => d.id === c.id)) {
          n.push(c);
        }
      }

      this.selectedEmployees = n;
    },
    removeSelectedEmployee(employee) {
      this.selectedEmployees = this.selectedEmployees.filter(
        (c) => c.id !== employee.id
      );
    },
    async confirm(employees) {
      console.log("employees", employees);
      const req = [];
      for (var c of employees) {
        req.push({
          userId: c.userId,
          zoneId: this.zoneID,
          userName: c.name,
          userPhone: c.phone,
          // cardNumber: c.attendanceCard,
          belongPlatform: this.belongPlatform,
        });
      }
      const r = await apiMachineZonePersonAdd(req);
      if (r.success) {
        this.$message.success("添加成功");
        this.selectedEmployees = this.selectedEmployees.concat(employees);
        this.getList();
        this.selectedEmployees = [...this.selectedEmployees].concat(employees);
      }
    },
    // 搜索事件
    onSearch(conditions) {
      this.searchConditions.userName = conditions.userName;
      this.searchConditions.attendId = conditions.attendId;
      this.searchConditions.deptName = conditions.deptName;
      this.searchConditions.currPage = 1;
      this.getList();
    },
    async downloadTemplate() {
      await apiPersonTemplateDownload();
    },
    async uploadFile(file, successCallback, failCallback) {
      try {
        const r = await apiPersonImport(this.zoneID, file);
        const result = await r.json();
        if (result.success) {
          successCallback();
          this.$refs.upload.close();
          this.getList();
        } else {
          this.$message.error(result.message);
          failCallback(e.message);
        }
      } catch (e) {
        console.log(e);
        failCallback(e.message);
      }
    },
    // 获取表格数据
    async getList() {
      this.$refs.list.context.toggleTableLoading(true);
      const r = await apiMachineZonePersonList({
        belongPlatform: this.belongPlatform,
        zoneId: this.zoneID,
        userName: this.searchConditions.userName,
        deptName: this.searchConditions.deptName,
        attendId: this.searchConditions.attendId,
        currPage: this.searchConditions.currPage,
        pageSize: 10,
      });
      this.$refs.list.context.toggleTableLoading(false);

      this.total = r.data.total;
      if (r.success) {
        this.employees = r.data.list;
      }
    },
    async getAllSelectedEmployees() {
      const r = await apiMachineZonePersonList({
        belongPlatform: this.belongPlatform,
        zoneId: this.zoneID,
        pageSize: 10000,
      });
      console.log("getAllSelectedEmployees", r);
      if (r.success) {
        this.selectedEmployees = r.data.list.map((c) => {
          return formatEmployee(c);
        });
      }
    },
    async handleResend(row) {
      const r = await apiMachineZonePersonSendRetry({
        belongPlatform: this.belongPlatform,
        zoneId: this.zoneID,
        userId: row.userId,
        userName: row.userName,
        cardNumber: row.cardNumber,
      });
      if (r.success) {
        this.getList();
      }
    },
    async handleSend(row) {
      const r = await apiMachineZonePersonSend({
        belongPlatform: this.belongPlatform,
        zoneId: this.zoneID,
        userList: [
          {
            userId: row.userId,
            zoneId: row.zoneId,
            userName: row.userName,
            userPhone: row.userPhone,
            cardNumber: row.cardNumber,
          },
        ],
      });
      if (r.success) {
        this.getList();
      }
    },
    // 操作按钮事件
    handleEditAttendanceCard(row) {
      this.$refs.changeAttendCardNumberDialog.open(row);
    },
    handleDelete(row) {
      this.$confirm("确定要删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const r = await apiMachineZonePersonDel({
          belongPlatform: this.belongPlatform,
          zoneId: this.zoneID,
          cardNumberList: [row.cardNumber],
        });
        if (r.success) {
          this.$message.success("删除成功");
          this.getList();
          this.getAllSelectedEmployees();
        }
      });
    },
    async handleExportReport() {
      const r = await apiMachineZonePersonExport({
        belongPlatform: this.belongPlatform,
        zoneId: this.zoneID,
      });
    },
    async handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请先选择要删除的员工");
        return;
      }
      this.$confirm("确定要删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const r = await apiMachineZonePersonDel({
          belongPlatform: this.belongPlatform,
          zoneId: this.zoneID,
          cardNumberList: this.selectedRows.map((c) => c.cardNumber),
        });
        if (r.success) {
          this.$message.success("删除成功");
          this.getList();
          this.getAllSelectedEmployees();
        }
      });
    },
    async handleFaceRecognition() {
      const r = await apiSendPersonFaceNotice({ userIdList: [] });
      if (r.success) {
        this.$message.success("人脸录入跟催通知已发送");
      }
    },
    // 切换展开
    toggleExpand() {
      console.log("切换展开");
    },
    hanlePaginationChange({ start, limit }) {
      this.searchConditions.currPage = start;
      this.getList();
    },
    handleSelectionChange(value) {
      this.selectedRows = value;
    },
    async handleChangeAttendCardNumber(employee, cardNumber) {
      const r = await apiMachineCardNumberEdit({
        userId: employee.userId,
        belongPlatform: this.belongPlatform,
        cardNumber: cardNumber,
      });
      if (r.success) {
        this.getList();
        this.$refs.changeAttendCardNumberDialog.close();
      }
    },
  },
};
</script>
