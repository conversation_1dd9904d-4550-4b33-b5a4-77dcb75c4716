<template>
  <o-container title="考勤机管理" class="attendanceMachines">
    <!-- 筛选区域 -->
    <o-top-select
      ref="top-select"
      :formJson="topSelectFormJson"
      :immediate="true"
      labelWidth="70px"
      style="margin-top: 20px"
      @search="onSearch"
    />
    <!-- 表格区域 -->
    <o-table
      ref="o-table"
      :sticky="true"
      :showPagination="false"
      :tableData="tableData"
      :tableHeader="tableHeader"
      :actionButtons="actionButtons"
      :tableHeaderActionButtons="tableHeaderActionButtons"
      emptyHeight="calc(100vh - 450px)"
    />
  </o-container>
</template>

<script>
import {
  apiMachineZoneList,
  apiMachinePersonRecordSync,
  apiMachineZoneDel,
} from "./apis.js"; // 引入请求函数

export default {
  data() {
    return {
      searchConditions: {},
      tableData: [],
      // 筛选表单配置
      topSelectFormJson: [
        {
          type: "input",
          item: {
            prop: "zoneName",
            label: "考勤机名称",
            value: "", // 初始值为空
            placeholder: "请输入考勤机名称", // 输入框占位符
          },
        },
      ],

      // 表格表头配置
      tableHeader: [
        { prop: "zoneName", label: "考勤机名称" }, // 设备平台区域名称
        { prop: "belongPlatform", label: "考勤机型号", width: "120px" }, // 所属平台
        { prop: "createTime", label: "创建时间", width: "150px" }, // 创建时间
        { prop: "lastSyncTime", label: "最后一次同步时间", width: "180px" }, // 最后一次同步时间
        { prop: "operUserName", label: "操作人", width: "120px" }, // 操作人姓名
      ],

      // 表格操作按钮配置
      actionButtons: [
        {
          label: "考勤机管理",
          type: "primary",
          icon: "el-icon-setting",
          click: (row) => this.handleManage(row),
        },
        {
          label: "编辑",
          type: "warning",
          icon: "el-icon-edit",
          click: (row) => this.handleEdit(row),
        },
        {
          label: "删除",
          type: "danger",
          icon: "el-icon-delete",
          click: (row) => this.handleDelete(row),
        },
      ],

      // 表格顶部操作按钮配置
      tableHeaderActionButtons: [
        {
          align: "right",
          type: "button",
          label: "添加考勤机",
          icon: "el-icon-plus",
          ifShow: () => true,
          click: this.handleAdd,
        },
        {
          align: "right",
          type: "button",
          label: "同步考勤数据",
          icon: "el-icon-refresh",
          ifShow: () => true,
          click: this.handleSync,
        },
      ],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 搜索事件
    onSearch(form) {
      this.searchConditions = form;

      this.getList();
    },
    async getList(params) {
      const r = await apiMachineZoneList({
        ...this.searchConditions,
        ...params,
        currPage: 1,
        pageSize: 10000,
      });
      if (r.success) {
        this.tableData = r.data.list;
      }
    },
    // 操作按钮事件
    handleManage(row) {
      this.$router.push(
        `/attendanceMachines/${row.id}/manage?belongPlatform=${row.belongPlatform}`
      );
    },
    handleEdit(row) {
      this.$router.push(
        `/attendanceMachines/${row.id}/edit?zoneName=${row.zoneName}&belongPlatform=${row.belongPlatform}`
      );
    },
    handleDelete(row) {
      this.$confirm("确定要删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const r = await apiMachineZoneDel({
          id: row.id,
          belongPlatform: row.belongPlatform,
        });
        if (r.success) {
          this.getList();
        }
      });
    },
    isHadDL() {
      return false;
    },
    isHadZK() {
      return false;
    },
    // 顶部操作按钮事件
    handleAdd() {
      this.$router.push(
        `/attendanceMachines/new?hadDL=${this.isHadDL()}&hadZK=${this.isHadZK()}`
      );
    },
    async handleSync() {
      const r = await apiMachinePersonRecordSync({});
      if (r.success) {
        this.$message.success("考勤数据同步成功，请前往考勤报表查看");
        this.getList();
      }
    },
    handleBatchDelete() {
      console.log("批量删除");
    },
  },
};
</script>

<style scoped>
.attendanceMachines {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
}
</style>
