<template>
  <o-container :title="title" class="attendanceMachinesNew" back="true">
    <div
      style="
        height: calc(100vh - 220px);
        overflow: hidden;
        overflow-y: auto;
        padding-top: 24px;
      "
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="width: 600px"
      >
        <el-form-item label="考勤机品牌" prop="belongPlatform">
          <el-select
            v-model="form.belongPlatform"
            placeholder="请选择考勤机品牌"
            :disabled="id"
          >
            <el-option label="墒机云平台" value="ZK"></el-option>
            <el-option label="得力" value="DL"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考勤机名称" prop="zoneName">
          <el-input
            v-model="form.zoneName"
            placeholder="请输入考勤机名称"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="!id">
      <div class="actions" v-if="isNeedShowNextStep && isNeedShowActionButtons">
        <el-button @click="() => $router.back()">取消</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
      <div
        class="actions"
        v-if="!isNeedShowNextStep && isNeedShowActionButtons"
      >
        <el-button @click="() => $router.back()">取消</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </div>
    </div>
    <div v-if="id" style="text-align: center">
      <el-button @click="() => $router.back()">取消</el-button>
      <el-button type="primary" @click="edit">编辑</el-button>
    </div>
    <el-dialog title="提示" :visible.sync="dialog.goOut">
      <div>
        <p>
          考勤机设备的联接将通过第三方北京阿拉钉科技有限公司的系统页面进行操作，是否同意？
        </p>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialog.goOut = false">拒绝</el-button>
        <el-button type="primary" @click="handleAgree">同意</el-button>
      </span>
    </el-dialog>

    <el-dialog title="提示" :visible.sync="dialog.confirm">
      <div>如果您已在阿拉钉提交了考勤机联接信息，您可以继续以下操作</div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleContinue">继续操作</el-button>
      </span>
    </el-dialog>
  </o-container>
</template>

<script>
import {
  apiMachineZoneAdd,
  apiMachineZoneUpdate,
  apiMachineListJoinedPlatforms,
} from "./apis";
function base64UrlDecode(str) {
  // 替换 Base64URL 特殊字符
  str = str.replace(/-/g, "+").replace(/_/g, "/");

  // 补全 '=' 填充符（Base64 要求长度是 4 的倍数）
  while (str.length % 4) {
    str += "=";
  }

  // 解码 Base64
  return decodeURIComponent(escape(atob(str)));
}
export default {
  computed: {
    id() {
      return this.$route.params.id;
    },
    title() {
      if (this.id) {
        return "编辑考勤机";
      }
      return "新增考勤机";
    },
    rules() {
      return {
        belongPlatform: [
          { required: true, message: "请选择所属平台", trigger: "change" },
        ],
        zoneName: [
          { required: true, message: "请输入考勤机名称", trigger: "blur" },
          {
            min: 2,
            max: 100,
            message: "长度在 2 到 100 个字符",
            trigger: "blur",
          },
        ],
      };
    },
    isNeedShowActionButtons() {
      return this.form.belongPlatform != "" && this.form.zoneName != "";
    },
    isNeedShowNextStep() {
      if (this.isJoinedPlatform(this.form.belongPlatform)) {
        return false;
      }

      return true;
    },
  },
  data() {
    return {
      dialog: {
        goOut: false,
        confirm: false,
      },
      form: {
        id: this.$route.params.id,
        belongPlatform: this.$route.query.belongPlatform || "",
        zoneName: this.$route.query.zoneName || "",
      },
      joinedPlatforms: [],
    };
  },
  mounted() {
    this.fetchJoinedPlatforms();
  },
  methods: {
    isJoinedPlatform(platform) {
      return this.joinedPlatforms.includes(platform);
    },
    async fetchJoinedPlatforms() {
      const r = await apiMachineListJoinedPlatforms();
      this.joinedPlatforms = r.data;
    },
    async nextStep() {
      try {
        // 表单验证
        const valid = await this.$refs.form.validate();
        if (!valid) {
          return;
        }

        this.dialog.goOut = true;
      } catch (error) {
        console.log("表单验证失败:", error);
      }
    },
    async edit() {
      const valid = await this.$refs.form.validate();
      if (!valid) {
        return;
      }
      const r = await apiMachineZoneUpdate(this.form);
      if (r.success) {
        this.$message.success("修改成功");
        this.$router.back();
      }
    },
    async submit() {
      const valid = await this.$refs.form.validate();
      if (!valid) {
        return;
      }
      const r = await apiMachineZoneAdd(this.form);
      if (r.success) {
        this.$message.success("添加成功");
        this.$router.back();
      }
    },
    handleAgree() {
      this.dialog.goOut = false;
      this.dialog.confirm = true;
      const info = sessionStorage.getItem("xst/token");
      var merchantID = "";
      if (info) {
        try {
          merchantID = JSON.parse(base64UrlDecode(info.split(".")[1])).p.m;
        } catch (error) {
          console.error(error);
        }
      }

      window.open(
        window.env.staticPath +
          `kaoqinji.html?belongPlatform=${this.form.belongPlatform}&zoneName=${this.form.zoneName}&merchantID=${merchantID}`
      );
    },
    handleContinue() {
      this.dialog.confirm = false;
      this.submit();
    },
  },
};
</script>
<style scoped>
.new-attendance-machine {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin-left: 10px;
}

.step-content {
  margin: 40px 0;
}

.actions {
  text-align: center;
}
</style>
