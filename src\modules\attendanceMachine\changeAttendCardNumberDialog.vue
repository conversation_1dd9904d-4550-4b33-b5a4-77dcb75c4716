<template>
  <el-dialog title="考勤卡号修改" :visible.sync="shown" >
    <div style="display: flex; gap: 10px;align-items:center">
        <span style="width: 70px">考勤卡号: </span><el-input v-model="cardNumber" />
    </div>

    <div style="text-align: right; margin-top: 20px">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="$emit('confirm', employee, cardNumber)">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      cardNumber: "",
      shown: false,
      employee: {},
    };
  },
  mounted() {
  },
  methods: {
    open(employee) {
      this.employee = employee
      this.cardNumber = employee.cardNumber
      this.shown = true;
    },
    close() {
      this.shown = false;
    },
  },
};
</script>

<style></style>
