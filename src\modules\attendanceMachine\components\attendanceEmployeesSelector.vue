<template>
  <div class="attendaceEmployeesSelector" style="height: 400px">
    <div class="box" style="display: flex; gap: 10px">
      <div class="employess">
        <el-select
          v-model="selectedGroupID"
          @change="handleChange"
          value-key="id"
          style="margin-bottom: 10px"
        >
          <el-option
            v-for="group in attendanceGroups"
            :key="group.id"
            :value="group.id"
            :label="group.name"
          >
            {{ group.name }}
          </el-option>
        </el-select>
        <el-table
          ref="table"
          :data="employees"
          border
          max-height="300px"
          row-key="id"
        >
          <el-table-column prop="name" label="姓名" width="120">
          </el-table-column>

          <el-table-column prop="phone" label="手机号" width="140">
          </el-table-column>

          <el-table-column
            prop="companyName"
            label="公司名称"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column prop="status" label="员工状态" width="100">
          </el-table-column>

          <el-table-column prop="joinDate" label="入职日期" width="120">
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleAdd(scope.row)"
                v-if="isNeedShowAddBtn(scope.row)"
              >
                添加
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="selectedEmployess" style="flex: 0 0 200px">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <span>已选</span>
          <span
            >{{
              selectedEmployees.length + tmpSelectedEmployees.length
            }}人</span
          >
        </div>
        <br />
        <ul
          v-if="selectedEmployees.length || tmpSelectedEmployees.length"
          style="height: 300px; overflow: auto"
        >
          <li
            v-for="employee in selectedEmployees"
            :key="employee.id"
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 5px;
            "
            @mouseenter="$event.target.style.backgroundColor = '#f5f7fa'"
            @mouseleave="$event.target.style.backgroundColor = ''"
          >
            {{ employee.name }}
          </li>
          <li
            v-for="employee in tmpSelectedEmployees"
            :key="employee.id"
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 5px;
            "
            @mouseenter="$event.target.style.backgroundColor = '#f5f7fa'"
            @mouseleave="$event.target.style.backgroundColor = ''"
          >
            {{ employee.name }}
            <i
              class="el-icon-close"
              style="
                cursor: pointer;
                color: var(--color-primary);
                flex: 0 0 12px;
              "
              @click="removeTmpSelectedEmployee(employee)"
            />
          </li>
        </ul>
      </div>
    </div>
    <div style="text-align: right; margin-top: 20px">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="confirm"> 确定 </el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    attendanceGroups: {
      type: Array,
      default: () => [],
    },
    employees: {
      type: Array,
      default: () => [],
    },
    selectedEmployees: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      selectedGroupID: "",
      tmpSelectedEmployees: [],
    };
  },
  mounted() {
    if (this.attendanceGroups && this.attendanceGroups.length) {
      this.selectedGroupID = this.attendanceGroups[0].id;
    }
  },
  methods: {
    isNeedShowAddBtn(employee) {
      if (this.tmpSelectedEmployees.find((e) => e.id === employee.id)) {
        return false;
      }
      if (this.selectedEmployees.find((e) => e.id === employee.id)) {
        return false;
      }

      return true;
    },
    confirm() {
      this.$emit("confirm", this.tmpSelectedEmployees);
      this.tmpSelectedEmployees = [];
    },
    handleAdd(employee) {
      this.tmpSelectedEmployees.push(employee);
    },
    handleChange(v) {
      this.$emit("changeAttendanceGroup", v);
    },
    removeTmpSelectedEmployee(employee) {
      this.tmpSelectedEmployees = this.tmpSelectedEmployees.filter(
        (e) => e.id !== employee.id
      );
    },
  },
};
</script>
