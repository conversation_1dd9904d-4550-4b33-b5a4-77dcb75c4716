<template>
  <el-dialog title="批量导入" :visible.sync="shown">
    <el-steps direction="vertical">
      <el-step title="选择导入方式" status="process">
        <template slot="description">
          <el-radio-group v-model="matchMethod">
            <el-radio label="phone" :checked="true">
              通过手机号码匹配人员信息
            </el-radio>
          </el-radio-group>
        </template>
      </el-step>
      <el-step status="process" title="选择文件">
        <template slot="description">
          <div class="file-requirements">
            支持.xlsx和.xls文件，文件不超过5M，建议使用标准模板
            <el-button
              type="text"
              class="download-btn"
              @click="downloadTemplate"
            >
              下载模板
            </el-button>
          </div>
        </template>
      </el-step>
    </el-steps>
    <div v-if="selectedFile" class="file-info">
      已选择文件: {{ selectedFile.name }}
      <i
        style="
          cursor: pointer;
          position: relative;
          top: 3px;
          color: var(--color-primary);
        "
        @click="selectedFile = null"
        class="el-icon-close"
      ></i>
    </div>
    <br />
    <el-upload
      class="file-upload"
      ref="upload"
      action=""
      :auto-upload="false"
      :show-file-list="false"
      :on-change="handleFileChange"
      :before-upload="beforeUpload"
      accept=".xlsx,.xls"
    >
      <el-button size="small" type="primary">选择文件</el-button>
    </el-upload>

    <span slot="footer" class="dialog-footer">
      <el-button @click="close()"> 取消 </el-button>
      <el-button
        v-loading="uploading"
        type="primary"
        @click="handleImport"
        :disabled="!selectedFile"
      >
        导入数据
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    downloadTemplate: Function,
    uploadFile: Function,
  },
  data() {
    return {
      selectedFile: null,
      shown: false,
      uploading: false,
      matchMethod: "phone",
    };
  },
  methods: {
    open() {
      this.shown = true;
    },
    close() {
      this.selectedFile = null;
      this.shown = false;
    },
    handleDownloadTemplate() {
      if (this.downloadTemplate) {
        this.downloadTemplate();
      }
    },
    handleFileChange(file) {
      this.selectedFile = file;
    },
    beforeUpload(file) {
      const isExcel = file.name.endsWith(".xlsx") || file.name.endsWith(".xls");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isExcel) {
        this.$message.error("只能上传.xlsx或.xls格式的文件!");
        return false;
      }

      if (!isLt5M) {
        this.$message.error("文件大小不能超过5MB!");
        return false;
      }

      return true;
    },
    handleImport() {
      if (!this.selectedFile) {
        this.$message.warning("请先选择文件");
        return;
      }
      this.uploading = true;
      if (this.uploadFile) {
        this.uploadFile(
          this.selectedFile,
          () => (this.uploading = false),
          () => (this.uploading = false)
        );
      }
    },
  },
};
</script>

<style></style>
