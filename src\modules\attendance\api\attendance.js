import { fetch, fetchFile } from 'request/fetch';
const env = process.env.NODE_ENV == "development" ? '/api/attend/' : '/api/attend/'
//考勤组管理-删除考勤组
const apiPostDeleteAttendGroup = data => {
  return fetch({
    url: env + 'group/deleteAttendGroup',
    method: 'post',
    params: data
  });
};

//考勤组管理-集合列表、模糊查询
const apiPostAttendGroupList = data => {
  return fetch({
    url: env + 'group/getAttendGroupList',
    method: 'post',
    data: data
  });
};

//考勤组管理-获取考勤组详情
const apiGetQueryAttendGroup = data => {
  return fetch({
    url: env + 'group/queryAttendGroup',
    method: 'get',
    params: data
  });
};

//考勤组管理-获取排班详情
const apiPostQueryWorkPlanCycle = data => {
  return fetch({
    url: env + 'group/queryWorkPlanCycle',
    method: 'post',
    data: data
  });
};

//考勤组管理-新增/修改考勤组
const apiPostSavaOrUpdateAttendGroup = data => {
  return fetch({
    url: env + 'group/savaOrUpdateAttendGroup',
    method: 'post',
    data: data
  });
};

//考勤组管理-编辑排班-新增/修改排班日历表
const apiPostSaveOrUpdateWorkCalendar = data => {
  return fetch({
    url: env + 'group/saveOrUpdateWorkCalendar',
    method: 'post',
    data: data
  });
};

//考勤组管理-编辑排班-新增/修改排班周期
const apiPostSaveOrUpdateWorkPlanCycle = data => {
  return fetch({
    url: env + 'group/saveOrUpdateWorkPlanCycle',
    method: 'post',
    data: data
  });
};

//获取部门人员树
const apiPostOrganizationAndRel = data => {
  return fetch({
    url: env + 'group/queryOrganizationAndRel',
    method: 'post',
    data: data
  });
};

//设置考勤组班次
const apiPostSetAttendWork = data => {
  return fetch({
    url: env + 'group/setAttendWorkForScheduling',
    method: 'post',
    data: data
  });
};

//编辑排班-未排班时自由选择班次
const apiPostSaveAllowedChooseShift = data => {
  return fetch({
    url: env + 'group/saveAllowedChooseShift',
    method: 'post',
    data: data
  });
};

//编辑排班-统计班次信息
const apiPostSchedulingWorks = data => {
  return fetch({
    url: env + 'work/querySchedulingWorks',
    method: 'post',
    data: data
  });
};

//查询审批流程,旧
const apiQueryApprovalProcess = data => {
  return fetch({
    url: env + 'group/queryApprovalProcess',
    method: 'post',
    data: data
  });
};
//查询审批流程,新
const apiQueryApprovalProcessNew = (data) => {
  return fetch({
    url: env + 'group/queryApprovalProcessList',
    method: 'post',
    data: data
  })
}

//获取全局的配置
const apiGlobalConfigFetch = () => {
  return fetch({
    url: '/api/merchant/merchantNet/getGlobalConfig',
    method:'post',
  })
}


export default {
  apiPostDeleteAttendGroup,
  apiPostAttendGroupList,
  apiGetQueryAttendGroup,
  apiPostQueryWorkPlanCycle,
  apiPostSavaOrUpdateAttendGroup,
  apiPostSaveOrUpdateWorkCalendar,
  apiPostSaveOrUpdateWorkPlanCycle,
  apiPostOrganizationAndRel,
  apiPostSetAttendWork,
  apiPostSaveAllowedChooseShift,
  apiPostSchedulingWorks,
  apiQueryApprovalProcess,
  apiGlobalConfigFetch,
  apiQueryApprovalProcessNew
};
