import { fetch, fetchFile } from 'request/fetch';
const env = process.env.NODE_ENV == "development" ? '/att/api/attend/' : '/api/attend/'
//删除补卡规则
const apiPostDeleteSupplementRule = data => {
  return fetch({
    url: env + 'supplementRule/deleteSupplementRule',
    method: 'post',
    params: data
  });
};

//补卡规则列表-带模糊查询
const apiPostGetSupplementRuleList = data => {
  return fetch({
    url: env + 'supplementRule/getSupplementRuleList',
    method: 'post',
    data: data
  });
};

//获取补卡规则详情
const apiGetQuerySupplementRule = data => {
  return fetch({
    url: env + 'supplementRule/querySupplementRule',
    method: 'get',
    params: data
  });
};

//新增/修改补卡规则
const apiPostSavaOrUpdateSupplementRule = data => {
  return fetch({
    url: env + 'supplementRule/savaOrUpdateSupplementRule',
    method: 'post',
    data: data
  });
};

//获取默认补卡规则
const apiGetDefaultSupplementRule = data => {
  return fetch({
    url: env + 'supplementRule/queryDefaultSupplementRule',
    method: 'get',
    params: data
  });
};

export default {
  apiPostDeleteSupplementRule,
  apiPostGetSupplementRuleList,
  apiGetQuerySupplementRule,
  apiPostSavaOrUpdateSupplementRule,
  apiGetDefaultSupplementRule
};
