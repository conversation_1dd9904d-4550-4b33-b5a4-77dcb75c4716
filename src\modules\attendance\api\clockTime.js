import { fetch, fetchFile } from 'request/fetch';
const env = process.env.NODE_ENV == "development" ? '/att/api/attend/' : '/api/attend/'

/**
 * 获取员工打卡信息（by.徐万里）
 * @param {}
 * @returns
 */
const getSignTimeByEmpIds = data => {
  return fetch({
    url: env + 'count/signTime/getSignTimeByEmpIds',
    method: 'post',
    data: data
  });
};

/**
 * 导出打卡记录（by.王祯）
 * @param no param
 * @returns
 */
 const getExportTime  = data => {
  return fetch({
    url: env + 'count/signTime/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  });
};


export default {
  getSignTimeByEmpIds,
  getExportTime
};
