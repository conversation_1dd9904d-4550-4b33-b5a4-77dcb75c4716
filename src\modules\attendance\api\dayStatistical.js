import { fetch, fetchFile } from 'request/fetch';
const env =
  process.env.NODE_ENV == 'development' ? '/att/api/attend/' : '/api/attend/';
/**
 * 获取异常关联订单（by.徐万里）
 * @param {}
 * @returns
 */
const getExceptionOrderInfo = (data) => {
  return fetch({
    url: env + 'count/dayCount/getExceptionOrderInfo',
    method: 'post',
    params: data,
  });
};

/**
 * 获取考勤组信息（by.徐万里）
 * @param no param
 * @returns
 */
const getAttendGroupList = (data) => {
  return fetch({
    url: env + 'count/dayCount/getAttendGroupList',
    method: 'post',
    data: data,
  });
};

/**
 * 获取员工日打卡信息（by.徐万里）
 * @param {}
 * @returns
 */

const getCountOfDayByEmpIds = (data) => {
  return fetch({
    url: env + 'count/dayCount/getCountOfDayByEmpIds',
    method: 'post',
    data: data,
  });
};

/**
 * 获取部门及其对应的员工（by.徐万里）
 * @param no param
 * @returns
 */

const getDepartmentAndEmpList = (data) => {
  return fetch({
    url: env + 'count/dayCount/getDepartmentAndEmpList',
    method: 'post',
    params: data,
  });
};

/**
 * 获取打卡异常状态详情信息（by.徐万里）
 * @param {id}  记录id
 * @returns
 */
const getExceptionSignInfo = (data) => {
  return fetch({
    url: env + 'count/dayCount/getExceptionSignInfo',
    method: 'post',
    params: data,
  });
};

/**
 * 异常打卡状态修改（by.徐万里）
 * @param {attendStatusEnum}  修改后的状态
 * @param {id}  记录id
 * @param {modifyDurationMinutes}  修改后的时长
 * @returns
 */
const updateExceptionStatus = (data) => {
  return fetch({
    url: env + 'count/dayCount/updateExceptionStatus',
    method: 'post',
    data: data,
  });
};

/**
 * 获取用工主体（by.徐万里）
 * @param no param
 * @returns
 */
const getTaxSubjectByComp = () => {
  return fetch({
    url: env + 'count/dayCount/getTaxSubjectByComp',
    method: 'post',
  });
};

/**
 * 获取离职人员（by.徐万里）
 * @param no param
 * @returns
 */
const getLeaveJobPerson = () => {
  return fetch({
    url: env + 'count/dayCount/getLeaveJobPerson',
    method: 'post',
  });
};

/**
 * 导出日统计（by.王祯）
 * @param no param
 * @returns
 */
const getExportDay = (data) => {
  return fetch({
    url: env + 'count/dayCount/export',
    method: 'post',
    data: data,
    responseType: 'blob',
  });
};

const getTemperatureApi = (data) => {
  return fetch({
    url: env + 'count/dayCount/getTemperatureOfDayByEmpIds',
    method: 'post',
    data: data,
  });
};

const exportTemperatureApi = (data) => {
  return fetch({
    url: env + 'count/dayCount/exportTemperatureOfDayByEmpIds',
    method: 'post',
    data: data,
    responseType: 'blob',
  });
};

export default {
  getExceptionOrderInfo,
  getAttendGroupList,
  getCountOfDayByEmpIds,
  getDepartmentAndEmpList,
  getExceptionSignInfo,
  updateExceptionStatus,
  getTaxSubjectByComp,
  getLeaveJobPerson,
  getExportDay,
  getTemperatureApi,
  exportTemperatureApi,
};
