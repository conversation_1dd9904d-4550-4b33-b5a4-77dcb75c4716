import { fetch, fetchFile } from 'request/fetch';
const env =
  process.env.NODE_ENV == 'development' ? '/att/api/attend/' : '/api/attend/';
//人脸管理-人脸数据列表查询
const mpFaceListApi = (data) => {
  return fetch({
    url: env + 'face/queryEmpFace',
    method: 'post',
    data: data,
  });
};

//人脸管理-修改状态
const updateCheckStatusApi = (data) => {
  return fetch({
    url: env + 'face/updateCheckStatus',
    method: 'post',
    data: data,
  });
};

//人脸管理-上传人脸
const uploadFaceApi = (data) => {
  return fetch({
    url: env + 'face/uploadFace',
    method: 'post',
    data: data,
  });
};

//人脸管理-获取一键通知录入的信息
const getNotifyInfoApi = (data) => {
  return fetch({
    url: env + 'face/getNotifyInfo',
    method: 'post',
    data: data,
  });
};

//人脸管理-通知员工录入
const sendNotifyApi = (data) => {
  return fetch({
    url: env + 'face/sendNotify',
    method: 'post',
    data: data,
  });
};

//一键通知录入
const sendNotifyAllApi = (data) => {
  return fetch({
    url: env + 'face/sendNotifyAll',
    method: 'post',
    data: data,
  });
};

//获取人脸基本信息
const getEmpFaceApi = (data) => {
  return fetch({
    url: env + 'face/getEmpFace',
    method: 'get',
    params: data,
  });
};



export default {
  mpFaceListApi,
  updateCheckStatusApi,
  uploadFaceApi,
  getNotifyInfoApi,
  sendNotifyApi,
  sendNotifyAllApi,
  getEmpFaceApi
};
