import { fetch, fetchFile } from 'request/fetch';
const env = process.env.NODE_ENV == "development" ? '/att/api/attend/' : '/api/attend/'

//新增假期
const apiPostAddLeave = data => {
  return fetch({
    url: env + 'leave/addLeave',
    method: 'post',
    data: data
  });
};

//删除假期
const apiPostDeleteLeave = data => {
  return fetch({
    url: env + 'leave/deleteLeave',
    method: 'post',
    params: data
  });
};

//修改假期
const apiPostModifyLeave = data => {
  return fetch({
    url: env + 'leave/modifyLeaveById',
    method: 'post',
    data: data
  });
};

//获取单个假期详情
const apiPostLeaveInfoById = data => {
  return fetch({
    url: env + 'leave/getLeaveInfoById',
    method: 'post',
    params: data
  });
};

//查询假期分类-模糊查询
const apiPostQueryLeaveInfo = data => {
  return fetch({
    url: env + 'leave/queryLeaveInfo',
    method: 'post',
    data: data
  });
};

//假期余额-获取假期列表
const apiPostGetLeaveList = data => {
  return fetch({
    url: env + 'leave/balance/getLeaveList',
    method: 'post',
    data: data
  });
};

//假期余额-查询假期余额/模糊查询
const apiPostQueryLeaveBalanceInfo = data => {
  return fetch({
    url: env + 'leave/balance/queryLeaveBalanceInfo',
    method: 'post',
    data: data
  });
};

//假期余额-查询个人假期余额使用记录
const apiPostQueryPersonBalanceReco = data => {
  return fetch({
    url: env + 'leave/balance/queryPersonBalanceReco',
    method: 'post',
    data: data
  });
};

//假期余额-修改个人假期余额
const apiPostUpdatePersonBalance = data => {
  return fetch({
    url: env + 'leave/balance/updatePersonBalance',
    method: 'post',
    data: data
  });
};

//编辑首次参加工作日期
const apiPostUpdateFirstWorkDate = data => {
  return fetch({
    url: env + 'leave/balance/updateUserFirstWorkDate',
    method: 'post',
    params: data
  });
};

//导出员工假期信息
const apiPostexportUser = data => {
  return fetch({
    url: env + 'leave/balance/exportUserLeaveBalanceReport',
    method: 'post',
    data: data,
    responseType: 'blob'
  });
};

//批量修改假期余额上传
const apiPostUploadBalanceBatch = data => {
  return fetch({
    url: env + 'leave/balance/editBalanceBatch',
    method: 'post',
    data: data
  });
};

export default {
  apiPostAddLeave,
  apiPostDeleteLeave,
  apiPostQueryLeaveInfo,
  apiPostGetLeaveList,
  apiPostQueryLeaveBalanceInfo,
  apiPostQueryPersonBalanceReco,
  apiPostUpdatePersonBalance,
  apiPostLeaveInfoById,
  apiPostModifyLeave,
  apiPostUpdateFirstWorkDate,
  apiPostexportUser,
  apiPostUploadBalanceBatch
};
