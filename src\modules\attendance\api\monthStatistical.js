import { fetch, fetchFile } from 'request/fetch';
const env = process.env.NODE_ENV == "development" ? '/att/api/attend/' : '/api/attend/'
/**
 * 获取员工月统计信息（by.徐万里）
 * @param {}
 * @returns
 */
const getCountOfMonthByEmpIds = data => {
  return fetch({
    url: env + 'count/monthCount/getCountOfMonthByEmpIds',
    method: 'post',
    data: data
  });
};

/**
 * 获取异常信息详情（by.徐万里）
 * @param {}
 * @returns
 */
const getExceptionRecoInfo = data => {
  return fetch({
    url: env + 'count/monthCount/getExceptionRecoInfo',
    method: 'post',
    data: data
  });
};

/**
 * 获取需审批异常记录（by.徐万里）
 * @param {}
 * @returns
 */

const getExceptionRecoOrderInfo = data => {
  return fetch({
    url: env + 'count/monthCount/getExceptionRecoOrderInfo',
    method: 'post',
    data: data
  });
};

/**
 * 导出月统计（by.王祯）
 * @param no param
 * @returns
 */
 const getExportMon  = data => {
  return fetch({
    url: env + 'count/monthCount/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  });
};

export default {
  getCountOfMonthByEmpIds,
  getExceptionRecoInfo,
  getExceptionRecoOrderInfo,
  getExportMon
};
