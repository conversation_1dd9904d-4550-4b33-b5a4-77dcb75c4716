import { fetch, fetchFile } from 'request/fetch'
const env = process.env.NODE_ENV == "development" ? '/att/api/attend/' : '/api/attend/'
/**
 * 获取员工原始打卡信息（by.徐万里）
 * @param {}
 * @returns
 */
const getSignRecordByEmpIds = (data) => {
  return fetch({
    url: env + 'count/signRecord/getSignRecordByEmpIds',
    method: 'post',
    data: data
  })
}

/**
 * 导出原始记录（by.王祯）
 * @param no param
 * @returns
 */
 const getExportRec  = data => {
  return fetch({
    url: env + 'count/signRecord/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  });
};

export default {
  getSignRecordByEmpIds,
  getExportRec
}