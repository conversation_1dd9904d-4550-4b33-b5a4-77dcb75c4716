import { fetch, fetchFile } from 'request/fetch'
const env = process.env.NODE_ENV == "development" ? '/api/attend/' : '/api/attend/'
//班次管理-班次列表/模糊查询
const apiPostAttendWorkingList = (data) => {
  return fetch({
    url: env + 'work/getAttendWorkingList',
    method: 'post',
    data: data
  })
}

//班次管理-班次详情
const apigetQueryAttendWork = (data) => {
  return fetch({
    url: env + 'work/queryAttendWork',
    method: 'get',
    params: data
  })
}

//班次管理-班次新增/修改
const apiPostSavaOrUpdateWorking = (data) => {
  return fetch({
    url: env + 'work/savaOrUpdateWorking',
    method: 'post',
    data: data
  })
}

//班次管理-删除班次
const apiPostDeleteAttendWork = (data) => {
  return fetch({
    url: env + 'work/deleteAttendWork',
    method: 'post',
    params: data
  })
}

//获取默认班次
const apiGetDefaultWorkingShift = (data) => {
  return fetch({
    url: env + 'work/queryDefaultWorkingShift',
    method: 'get',
    params: data
  })
}

//检查班次是否可修改/删除
const apiCheckWorkingShift = (data) => {
  return fetch({
    url: env + 'work/checkWorkingShiftModifications',
    method: 'post',
    params: data
  })
}

export default {
  apiPostAttendWorkingList,
  apigetQueryAttendWork,
  apiPostSavaOrUpdateWorking,
  apiPostDeleteAttendWork,
  apiGetDefaultWorkingShift,
  apiCheckWorkingShift
}