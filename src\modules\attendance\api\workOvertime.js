import { fetch, fetchFile } from 'request/fetch'
const env = process.env.NODE_ENV == "development" ? '/att/api/attend/' : '/api/attend/'
//加班列表-模糊查询
const apiGetOverTimeList = (data) => {
  return fetch({
    url: env + 'overtime/getOverTimeList',
    method: 'post',
    data: data
  })
}

//加班规则-详情
const apigetQueryOvertimeRule = (data) => {
  return fetch({
    url: env + 'overtime/queryOvertimeRule',
    method: 'get',
    params: data
  })
}

//加班规则-新增/修改
const apiSavaOrUpdateOvertimeRule = (data) => {
  return fetch({
    url: env + 'overtime/savaOrUpdateOvertimeRule',
    method: 'post',
    data: data
  })
}

//加班规则-删除规则
const apiPostDeleteOvertimeRule = (data) => {
  return fetch({
    url: env + 'overtime/deleteOvertimeRule',
    method: 'post',
    params: data
  })
}

//加班规则-新增获取法定节假日
const apiSpecialDaytemplate = (data) => {
    return fetch({
      url: env + 'overtime/specialDaytemplate',
      method: 'get',
      params: data
    })
  }

  //获取默认加班规则
const getDefaultOvertimeRule = (data) => {
    return fetch({
      url: env + 'overtime/queryDefaultOvertimeRule',
      method: 'get',
      params: data
    })
  }

//获取加班-调休时长下拉框数据
const apiGetShiftLeaveList = (data) => {
  return fetch({
    url: env + 'leave/getShiftLeaveList',
    method: 'post',
    data: data
  })
}

//加班明细列表
const getOverTimeDetailList = (data) => {
    return fetch({
      url: env + 'overtime/getEmpOvertimeRecordList',
      method: 'post',
      data: data
    })
}
  
//加班明细导出
const getExportOverTime = (data) => {
  return fetch({
    url: env + 'overtime/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export default {
    apiGetOverTimeList,
    apigetQueryOvertimeRule,
    apiSavaOrUpdateOvertimeRule,
    apiPostDeleteOvertimeRule,
    apiSpecialDaytemplate,
    getDefaultOvertimeRule,
    apiGetShiftLeaveList,
    getOverTimeDetailList,
    getExportOverTime
}