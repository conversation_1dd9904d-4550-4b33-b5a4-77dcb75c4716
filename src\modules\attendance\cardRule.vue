<template>
  <div class="def_per_height">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span>补卡管理</span>
        </el-col>
      </el-row>
    </header>
    <div class="content">
      <div class="add-search">
        <div class="search-box">
          <el-input
            v-model.trim="key"
            placeholder="请输入补卡规则名称"
            maxLength="30"
            :clearable="true"
            @change="handleSearch"
          >
            <el-button slot="append" @click="handleSearch">搜索</el-button>
          </el-input>
        </div>
        <el-button type="primary" @click="handleAdd">+新增补卡规则</el-button>
      </div>
      <el-table
        :header-cell-style="{ background: '#F1F1F1' }"
        :data="tableData"
        v-loading="loading"
        border
      >
        <el-table-column label="补卡规则名称" prop="ruleName" min-width="100">
          <template slot-scope="scope">
            <p>
              {{ scope.row.ruleName }}
              <el-button v-if="scope.row.isDefault" type="text">默认</el-button>
            </p>
          </template>
        </el-table-column>
        <el-table-column label="规则内容" prop="number" min-width="70">
          <template slot-scope="scope">
            <p>{{ scope.row.allowSupplement ? "允许补卡" : "不允许补卡" }}</p>
            <div v-if="scope.row.allowSupplement">
              <p v-show="scope.row.allowTimesLimit">
                每月可提交{{ scope.row.timesLimit }}次补卡
              </p>
              <p v-show="!scope.row.allowTimesLimit">补卡次数不限制</p>
              <p v-show="!scope.row.allowDateLimit">补卡时间不限制</p>
              <p v-show="scope.row.allowDateLimit">
                可申请过去{{ scope.row.dateLimit }}天内的补卡
              </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="应用范围" prop="attendanceType" min-width="70">
          <template slot-scope="scope">
            <el-tooltip class="item" effect="dark" placement="top">
              <div slot="content">
                <p style="max-width: 200px">
                  {{
                    scope.row.isDefault
                      ? "全公司(设置了其他补卡规则的除外)"
                      : scope.row.cardRange
                  }}
                </p>
              </div>
              <p class="cardRange">
                {{
                  scope.row.isDefault
                    ? "全公司(设置了其他补卡规则的除外)"
                    : scope.row.cardRange
                }}
              </p>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="operation" min-width="100">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="handleEdit(scope.row.id, scope.row.isDefault)"
              >编辑</el-button
            >
            <el-button
              v-show="!scope.row.isDefault"
              type="text"
              size="mini"
              @click="handleDelete(scope.row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="prev, pager, next, sizes, jumper"
          :total="total"
          background
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currPage: 1,
      tableData: [],
      total: 0,
      coId: 0,
      currPage: 1,
      id: 0,
      key: "",
      pageSize: 10,
      queryEndTime: "",
      queryStartTime: "",
      loading: false,
      screenHeight: document.body.clientHeight - 300, //表格自适应高度
    };
  },
  created() {
    this.getRuleList();
  },
  methods: {
    getRuleList(type) {
      this.loading = true;
      let params = {
        currPage: type === "search" ? 1 : this.currPage,
        pageSize: this.pageSize,
        key: this.key,
      };
      this.$attApi.apiPostGetSupplementRuleList(params).then((res) => {
        this.loading = false;
        if (res.success) {
          const DATA = res.data.records;
          this.tableData = res.data.records;
          this.total = res.data.total;
          DATA.forEach((item) => {
            if (item.attendGroupVoList.length !== 0) {
              let arrName = [];
              item.attendGroupVoList.forEach((val) => {
                arrName.push(val.agName);
              });
              item.cardRange = arrName.toString();
            }
          });
        }
      });
    },
    handleEdit(id, isDefault) {
      this.$router.push({
        path: "/attendance/addRule",
        query: {
          id: id,
          isDefault: isDefault,
        },
      });
    },
    //删除补卡规则
    handleDelete(delId) {
      console.log(delId);
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          this.$attApi
            .apiPostDeleteSupplementRule({ id: delId })
            .then((res) => {
              if (res.success) {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                this.getRuleList();
              }
            });
        })
        .catch(() => {});
    },
    handleSearch() {
      this.getRuleList("search");
    },
    handleAdd() {
      this.$router.push("/attendance/addRule");
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getRuleList();
    },
    handleCurrentChange(val) {
      this.currPage = val;
      this.getRuleList();
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  border-bottom: 1px solid #ededed;
}
.add-search {
  display: flex;
  margin-bottom: 20px;
  justify-content: space-between;
}
.content {
  padding: 22px;
  .cardRange {
    line-height: 50px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
  .add-search .search-box {
    width: 400px;
  }
}
.pagination {
  padding: 22px 0 22px 22px;
  text-align: right;
}
</style>
