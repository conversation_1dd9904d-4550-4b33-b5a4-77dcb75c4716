<template>
  <div class="addOvertime">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="goBack" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>{{ overTimeTitle }}</span>
        </el-col>
      </el-row>
    </header>
    <div class="content">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item
          label="规则名称："
          prop="ruleName"
          label-width="95px"
          class="overtimeName"
        >
          <el-input
            v-model.trim="form.ruleName"
            placeholder="请输入规则名称"
          ></el-input>
          <span class="hint-info">最多30个字符（中英文或数字）</span>
        </el-form-item>
        <!-- <h3 class="title">基本信息：</h3>
        <el-form-item label="适用范围">
          <el-select v-model="form.useRange" :popper-append-to-body="false">
            <el-option label="全公司" value="COMPANY"></el-option>
          </el-select>
        </el-form-item> -->
        <h3 class="title">加班规则：</h3>
        <div class="dateType">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="工作日" name="first">
              <el-form ref="form" :model="form1" label-width="130px">
                <el-form-item label="允许加班">
                  <el-switch
                    v-model="form1.allowedOvertime"
                    onselectstart="return false"
                  ></el-switch>
                </el-form-item>
                <el-form-item label="计算方式" v-show="form1.allowedOvertime">
                  <el-radio
                    v-model="form1.overtimeCalcRule"
                    label="APPROVAL_DURATION"
                    >按审批时长计算</el-radio
                  >
                </el-form-item>
                <el-divider></el-divider>
                <div v-show="form1.allowedOvertime">
                  <el-form-item label="加班时间设置">
                    <el-switch
                      v-model="form1.allowedOvertimeSetting"
                      onselectstart="return false"
                    ></el-switch>
                    <div class="restTime" v-show="form1.allowedOvertimeSetting">
                      <el-checkbox label="" v-model="form1.allowIgnoreOvertime"
                        ><span @click.prevent>
                          当天加班少于
                          <el-input-number
                            v-model="form1.ignoreOvertime"
                            controls-position="right"
                            :min="0"
                            :max="720"
                          ></el-input-number>
                          分钟，不计入加班
                        </span></el-checkbox
                      >
                      <el-checkbox label="" v-model="form1.allowMaxOvertime"
                        ><span @click.prevent>
                          当天加班时长最多不能超过
                          <el-input-number
                            v-model="form1.maxOvertime"
                            controls-position="right"
                            :min="0"
                            :max="720"
                          ></el-input-number>
                          分钟</span
                        ></el-checkbox
                      >
                      <el-checkbox label="" v-model="form1.allowStartOvertime"
                        ><span @click.prevent>
                          下班
                          <el-input-number
                            v-model="form1.startOvertime"
                            controls-position="right"
                            :min="0"
                            :max="180"
                          ></el-input-number>
                          分钟后开始计入加班</span
                        >
                      </el-checkbox>
                      <div
                        class="restRange"
                        v-for="(val, index) in num1"
                        :key="index"
                      >
                        <el-checkbox
                          label=""
                          v-model="
                            form1.currRestPeriodVoList[index]
                              .allowedDeductRestTime
                          "
                        >
                        </el-checkbox>
                        <span> 设置休息时段 休息时间开始</span>
                        <span>
                          <el-time-picker
                            v-model="
                              form1.currRestPeriodVoList[index].startTime
                            "
                            @change="handleSelect"
                            format="HH:mm"
                            :append-to-body="false"
                            :editable="false"
                            :clearable="false"
                          >
                          </el-time-picker>
                          休息时间结束
                          <el-time-picker
                            v-model="form1.currRestPeriodVoList[index].endTime"
                            @change="handleSelect"
                            format="HH:mm"
                            :append-to-body="false"
                            :editable="false"
                            :clearable="false"
                          >
                          </el-time-picker
                        ></span>
                        <span
                          v-if="index + 1 === form1.currRestPeriodVoList.length"
                        >
                          <el-button type="text" @click="addWorkBal(1)"
                            >新增</el-button
                          >
                          <el-button
                            type="text"
                            @click="delWorkBal(1)"
                            v-show="form1.currRestPeriodVoList.length > 1"
                            >删除</el-button
                          >
                        </span>
                      </div>
                    </div>
                  </el-form-item>
                  <el-divider></el-divider>
                  <el-form-item label="记为调休或加班费">
                    <el-switch
                      v-model="form1.allowedLeaveOrPay"
                      onselectstart="return false"
                    ></el-switch>
                    <div class="overtimePay" v-show="form1.allowedLeaveOrPay">
                      <el-radio-group v-model="form1.leaveOrPayType">
                        <el-radio label="OVERTIME_PAY">记为加班费</el-radio>
                        <el-radio label="COMPENSATORY_LEAVE" v-if="isExistLeave"
                          >记为
                          <el-select v-model="form1.toDaysOffHolidayId">
                            <el-option
                              :label="overTimes"
                              :value="holidayId"
                            ></el-option>
                          </el-select>
                          时长
                        </el-radio>
                        <el-radio v-else label="" :disabled="!isExistLeave">
                          <span
                            >记为调休时长
                            <el-button
                              type="text"
                              @click="$router.push('/attendance/holidayType')"
                              >去创建调休假</el-button
                            ></span
                          >
                        </el-radio>

                        <!-- <el-radio :label="9"
                      >员工申请加班时，可以选择记为加班费或者
                      <el-select v-model="form1.pay3">
                        <el-option label="调休" value="1"></el-option>
                      </el-select>
                      时长
                    </el-radio> -->
                      </el-radio-group>
                    </div>
                  </el-form-item>
                  <el-divider></el-divider>
                </div>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="休息日" name="second">
              <el-form ref="form" :model="form1" label-width="130px">
                <el-form-item label="允许加班">
                  <el-switch
                    v-model="form2.allowedOvertime"
                    onselectstart="return false"
                  ></el-switch>
                </el-form-item>
                <el-form-item label="计算方式" v-show="form2.allowedOvertime">
                  <el-radio
                    v-model="form2.overtimeCalcRule"
                    label="APPROVAL_DURATION"
                    >按审批时长计算</el-radio
                  >
                </el-form-item>
                <el-divider></el-divider>
                <div v-if="form2.allowedOvertime">
                  <el-form-item label="加班时间设置">
                    <el-switch
                      v-model="form2.allowedOvertimeSetting"
                      onselectstart="return false"
                    ></el-switch>
                    <div class="restTime" v-show="form2.allowedOvertimeSetting">
                      <el-checkbox label="" v-model="form2.allowIgnoreOvertime"
                        ><span @click.prevent>
                          当天加班少于
                          <el-input-number
                            v-model="form2.ignoreOvertime"
                            controls-position="right"
                            :min="0"
                            :max="720"
                          ></el-input-number>
                          分钟，不计入加班
                        </span></el-checkbox
                      >
                      <el-checkbox label="" v-model="form2.allowMaxOvertime"
                        ><span @click.prevent>
                          当天加班时长最多不能超过
                          <el-input-number
                            v-model="form2.maxOvertime"
                            controls-position="right"
                            :min="0"
                            :max="720"
                          ></el-input-number>
                          分钟</span
                        ></el-checkbox
                      >
                      <!-- <el-checkbox label="" v-model="form2.allowStartOvertime"
                        ><span @click.prevent
                          >下班<el-input
                            v-model="form2.startOvertime"
                          ></el-input
                          >分钟后开始计入加班</span
                        ></el-checkbox
                      > -->
                      <div
                        class="restRange"
                        v-for="(val, index) in num2"
                        :key="index"
                      >
                        <el-checkbox
                          label=""
                          v-model="
                            form2.currRestPeriodVoList[index]
                              .allowedDeductRestTime
                          "
                        >
                        </el-checkbox>
                        <span>
                          设置休息时段 休息时间开始
                          <el-time-picker
                            v-model="
                              form2.currRestPeriodVoList[index].startTime
                            "
                            :picker-options="{}"
                            @change="handleSelect"
                            format="HH:mm"
                            :append-to-body="false"
                            :editable="false"
                            :clearable="false"
                          >
                          </el-time-picker>
                          休息时间结束
                          <el-time-picker
                            v-model="form2.currRestPeriodVoList[index].endTime"
                            :picker-options="{}"
                            @change="handleSelect"
                            format="HH:mm"
                            :append-to-body="false"
                            :editable="false"
                            :clearable="false"
                          >
                          </el-time-picker
                        ></span>
                        <span
                          v-if="index + 1 === form2.currRestPeriodVoList.length"
                        >
                          <el-button type="text" @click="addWorkBal(2)"
                            >新增</el-button
                          >
                          <el-button
                            type="text"
                            @click="delWorkBal(2)"
                            v-show="form2.currRestPeriodVoList.length > 1"
                            >删除</el-button
                          >
                        </span>
                      </div>
                    </div>
                  </el-form-item>
                  <el-divider></el-divider>
                  <el-form-item label="记为调休或加班费">
                    <el-switch
                      v-model="form2.allowedLeaveOrPay"
                      onselectstart="return false"
                    ></el-switch>
                    <div class="overtimePay" v-show="form2.allowedLeaveOrPay">
                      <el-radio-group v-model="form2.leaveOrPayType">
                        <el-radio label="OVERTIME_PAY">记为加班费</el-radio>
                        <el-radio label="COMPENSATORY_LEAVE" v-if="isExistLeave"
                          >记为
                          <el-select v-model="form2.toDaysOffHolidayId">
                            <el-option
                              :label="overTimes"
                              :value="holidayId"
                            ></el-option>
                          </el-select>
                          时长
                        </el-radio>
                        <el-radio v-else label="" :disabled="!isExistLeave">
                          <span
                            >记为调休时长
                            <el-button
                              type="text"
                              @click="$router.push('/attendance/holidayType')"
                              >去创建调休假</el-button
                            ></span
                          >
                        </el-radio>
                        <!-- <el-radio :label="9"
                      >员工申请加班时，可以选择记为加班费或者
                      <el-select v-model="form2.pay3">
                        <el-option label="调休" value="1"></el-option>
                      </el-select>
                      时长
                    </el-radio> -->
                      </el-radio-group>
                    </div>
                  </el-form-item>
                  <el-divider></el-divider>
                </div>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="节假日" name="third">
              <el-form ref="form" :model="form3" label-width="130px">
                <!-- <el-form-item label="法定节假日">
                  <div class="specialDay">
                    <span v-for="(val, index) in specialDay" :key="index">
                      {{
                        val.specialDayName +
                          ": " +
                          val.beginDate +
                          " 至 " +
                          val.endDate
                      }}
                    </span>
                  </div>
                </el-form-item> -->
                <el-form-item label="允许加班">
                  <el-switch
                    v-model="form3.allowedOvertime"
                    onselectstart="return false"
                  ></el-switch>
                </el-form-item>
                <el-form-item label="计算方式" v-show="form3.allowedOvertime">
                  <el-radio
                    v-model="form3.overtimeCalcRule"
                    label="APPROVAL_DURATION"
                    >按审批时长计算</el-radio
                  >
                </el-form-item>
                <el-divider></el-divider>
                <div v-if="form3.allowedOvertime">
                  <el-form-item label="加班时间设置">
                    <el-switch
                      v-model="form3.allowedOvertimeSetting"
                      onselectstart="return false"
                    ></el-switch>
                    <div class="restTime" v-show="form3.allowedOvertimeSetting">
                      <el-checkbox label="" v-model="form3.allowIgnoreOvertime"
                        ><span @click.prevent>
                          当天加班少于
                          <el-input-number
                            v-model="form3.ignoreOvertime"
                            controls-position="right"
                            :min="0"
                            :max="720"
                          ></el-input-number>
                          分钟，不计入加班
                        </span></el-checkbox
                      >
                      <el-checkbox label="" v-model="form3.allowMaxOvertime"
                        ><span @click.prevent>
                          当天加班时长最多不能超过
                          <el-input-number
                            v-model="form3.maxOvertime"
                            controls-position="right"
                            :min="0"
                            :max="720"
                          ></el-input-number>
                          分钟</span
                        ></el-checkbox
                      >
                      <!-- <el-checkbox label="" v-model="form3.allowStartOvertime"
                        ><span @click.prevent
                          >下班<el-input
                            v-model="form3.startOvertime"
                          ></el-input
                          >分钟后开始计入加班</span
                        ></el-checkbox
                      > -->
                      <div
                        class="restRange"
                        v-for="(val, index) in num3"
                        :key="index"
                      >
                        <el-checkbox
                          label=""
                          v-model="
                            form3.currRestPeriodVoList[index]
                              .allowedDeductRestTime
                          "
                        >
                        </el-checkbox>
                        <span>
                          设置休息时段 休息时间开始
                          <el-time-picker
                            v-model="
                              form3.currRestPeriodVoList[index].startTime
                            "
                            :picker-options="{}"
                            @change="handleSelect"
                            format="HH:mm"
                            :append-to-body="false"
                            :editable="false"
                            :clearable="false"
                          >
                          </el-time-picker>
                          休息时间结束
                          <el-time-picker
                            v-model="form3.currRestPeriodVoList[index].endTime"
                            :picker-options="{}"
                            @change="handleSelect"
                            format="HH:mm"
                            :append-to-body="false"
                            :editable="false"
                            :clearable="false"
                          >
                          </el-time-picker
                        ></span>
                        <span
                          v-if="index + 1 === form3.currRestPeriodVoList.length"
                        >
                          <el-button type="text" @click="addWorkBal(3)"
                            >新增</el-button
                          >
                          <el-button
                            type="text"
                            @click="delWorkBal(3)"
                            v-show="form3.currRestPeriodVoList.length > 1"
                            >删除</el-button
                          >
                        </span>
                      </div>
                    </div>
                  </el-form-item>
                  <el-divider></el-divider>
                  <el-form-item label="记为调休或加班费">
                    <el-switch
                      v-model="form3.allowedLeaveOrPay"
                      onselectstart="return false"
                    ></el-switch>
                    <div class="overtimePay" v-show="form3.allowedLeaveOrPay">
                      <el-radio-group v-model="form3.leaveOrPayType">
                        <el-radio label="OVERTIME_PAY">记为加班费</el-radio>
                        <el-radio label="COMPENSATORY_LEAVE" v-if="isExistLeave"
                          >记为
                          <el-select v-model="form3.toDaysOffHolidayId">
                            <el-option
                              :label="overTimes"
                              :value="holidayId"
                            ></el-option>
                          </el-select>
                          时长
                        </el-radio>
                        <el-radio v-else label="" :disabled="!isExistLeave">
                          <span
                            >记为调休时长
                            <el-button
                              type="text"
                              @click="$router.push('/attendance/holidayType')"
                              >去创建调休假</el-button
                            ></span
                          >
                        </el-radio>
                        <!-- <el-radio :label="9"
                      >员工申请加班时，可以选择记为加班费或者
                      <el-select v-model="form3.pay3">
                        <el-option label="调休" value="1"></el-option>
                      </el-select>
                      时长
                    </el-radio> -->
                      </el-radio-group>
                    </div>
                  </el-form-item>
                  <el-divider></el-divider>
                </div>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
        <el-form-item label="最小计时单位">
          <div class="timeUnit">
            <el-select v-model="form.overtimeStatisticsUnit">
              <el-option
                v-for="val in unitOptions"
                :key="val.value"
                :label="val.label"
                :value="val.value"
              ></el-option>
            </el-select>
            <p class="tip">
              按分钟，手机端提交加班时，可以选择到分钟；考勤按小时统计报表（精确到小数点后两位）
            </p>
            <p class="tip">
              计算规则：员工加班时长如实计算例如：加班1.32小时=1.32小时
            </p>
          </div>
        </el-form-item>
        <el-form-item label="日时长折算">
          <div class="timesReduce">
            <el-input v-model.trim="form.overtimeStatisticsCeilHour"></el-input>
            小时 =
            <el-input v-model.trim="form.overtimeStatisticsCeilDay"></el-input>
            天
            <p class="tip">切换单位或加班转调休时使用</p>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="saveBefore">保存</el-button>
    </div>
  </div>
</template>

<script>
import { checkOvertime } from "../util/validate";
import { debounce } from "../util/debounce";
export default {
  data() {
    return {
      isChangeLeave: false, //判断回显时-调休假期是否被删除
      overTimes: "",
      isExistLeave: false, //是否存在余额调休
      num1: 1,
      num2: 1,
      num3: 1,
      holidayId: "",
      form: {
        ruleName: "", //规则名称
        overtimeStatisticsUnit: "HOUR", //最小计时单位
        overtimeStatisticsCeilHour: 8, //日折算小时
        overtimeStatisticsCeilDay: 1, //日折算天数
      },
      activeName: "first",
      overTimeTitle: "新增加班规则",
      specialDay: [], //法定节假日集合
      defalutRestList: [
        //默认休息时间段
        {
          allowedDeductRestTime: false,
          endTime: new Date(2021, 1, 1, 0, 0, 0),
          startTime: new Date(2021, 1, 1, 0, 0, 0),
        },
      ],
      form1: {
        ruleType: "WORKDAY", //节日类型
        allowedOvertime: true, //开关-允许加班
        overtimeCalcRule: "APPROVAL_DURATION", //计算方式-审批时长
        allowedOvertimeSetting: true, //开关-扣除休息时间
        allowedLeaveOrPay: true, //开关-记为调休加班费
        allowIgnoreOvertime: true, //选中-当天加班少于
        allowMaxOvertime: true, //选中-当天加班时长最多
        allowStartOvertime: true, //选中-下班计入加班
        ignoreOvertime: 30, //当天加班少于分钟
        maxOvertime: 720, //当天加班最多不能超过分钟
        startOvertime: 0, //下班多少分钟后开始计入加班
        leaveOrPayType: "OVERTIME_PAY", //当前选中调休加班方式
        toDaysOffHolidayId: "", //记为调休时长-id
        currRestPeriodVoList: [
          {
            allowedDeductRestTime: false,
            endTime: new Date(2021, 1, 1, 0, 0, 0),
            startTime: new Date(2021, 1, 1, 0, 0, 0),
          },
        ],
      },
      form2: {
        ruleType: "RESTDAY", //节日类型
        allowedOvertime: true, //开关-允许加班
        overtimeCalcRule: "APPROVAL_DURATION", //计算方式-审批时长
        allowedOvertimeSetting: true, //开关-扣除休息时间
        allowedLeaveOrPay: true, //开关-记为调休加班费
        allowIgnoreOvertime: true, //选中-当天加班少于
        allowMaxOvertime: true, //选中-当天加班时长最多
        // allowStartOvertime: false, //选中-下班计入加班
        ignoreOvertime: 30, //当天加班少于分钟
        maxOvertime: 720, //当天加班最多不能超过分钟
        // startOvertime: 0, //下班多少分钟后开始计入加班
        leaveOrPayType: "OVERTIME_PAY", //当前选中调休加班方式
        toDaysOffHolidayId: "",
        currRestPeriodVoList: [
          {
            allowedDeductRestTime: false,
            endTime: new Date(2021, 1, 1, 0, 0, 0),
            startTime: new Date(2021, 1, 1, 0, 0, 0),
          },
        ],
      },
      form3: {
        ruleType: "HOLIDAY", //节日类型
        allowedOvertime: true, //开关-允许加班
        overtimeCalcRule: "APPROVAL_DURATION", //计算方式-审批时长
        allowedOvertimeSetting: true, //开关-扣除休息时间
        allowedLeaveOrPay: true, //开关-记为调休加班费
        allowIgnoreOvertime: true, //选中-当天加班少于
        allowMaxOvertime: true, //选中-当天加班时长最多
        // allowStartOvertime: false, //选中-下班计入加班
        ignoreOvertime: 30, //当天加班少于分钟
        maxOvertime: 720, //当天加班最多不能超过分钟
        // startOvertime: 0, //下班多少分钟后开始计入加班
        leaveOrPayType: "OVERTIME_PAY", //当前选中调休加班方式
        toDaysOffHolidayId: "",
        currRestPeriodVoList: [
          {
            allowedDeductRestTime: false,
            endTime: new Date(2021, 1, 1, 0, 0, 0),
            startTime: new Date(2021, 1, 1, 0, 0, 0),
          },
        ],
        // specialDayReqList: [] //节假日集合
      },
      rules: {
        ruleName: [
          {
            required: true,
            validator: checkOvertime,
            trigger: "blur",
          },
        ],
      },
      unitOptions: [
        // { label: "分钟", value: "MINUTE" },
        // { label: "半小时", value: "HAlf_AN_HOUR" },
        { label: "小时", value: "HOUR" },
        // { label: "半天", value: "HALF_A_DAY" },
        // { label: "天", value: "DAY" }
      ],
    };
  },

  watch: {},

  created() {
    // this.getSpecialDay();
    this.getShiftLeaveList();
  },
  methods: {
    //校验加班规则开启-至少选中一项
    testCheck(val) {
      let arr = [],
        ischeck = [];
      arr.push(val.allowIgnoreOvertime);
      arr.push(val.allowMaxOvertime);
      arr.push(val.allowStartOvertime);
      val.currRestPeriodVoList.map((item) => {
        arr.push(item.allowedDeductRestTime);
      });
      ischeck = arr.filter((item) => {
        return item;
      });
      return ischeck.length ? false : true;
    },
    //校验输入框不为空
    testValue(val) {
      if (!val && val !== 0) {
        return true;
      }
      return false;
    },
    //校验输入框
    checkInput() {
      let rule = /^[0-9]*$/;
      let work =
        this.testValue(this.form1.ignoreOvertime) ||
        this.testValue(this.form1.maxOvertime) ||
        this.testValue(this.form1.startOvertime);
      let rest =
        this.testValue(this.form2.ignoreOvertime) ||
        this.testValue(this.form2.maxOvertime);
      let holiday =
        this.testValue(this.form3.ignoreOvertime) ||
        this.testValue(this.form3.maxOvertime);
      let isEmpty =
        this.testValue(this.form.overtimeStatisticsCeilHour) ||
        this.testValue(this.form.overtimeStatisticsCeilDay);
      let unit =
        rule.test(this.form.overtimeStatisticsCeilHour) &&
        rule.test(this.form.overtimeStatisticsCeilDay);
      console.log(work);
      if (work) {
        this.$message.error("工作日加班时间设置输入不能为空");
        return false;
      }
      if (rest) {
        this.$message.error("休息日加班时间输入不能为空");
        return false;
      }
      if (holiday) {
        this.$message.error("节假日加班时间输入不能为空");
        return false;
      }
      if (isEmpty) {
        this.$message.error("日时长折算输入不能为空");
        return false;
      }
      if (!unit) {
        this.$message.error("日时长折算请输入整数");
        return false;
      }
      if (this.form1.allowedOvertimeSetting && this.testCheck(this.form1)) {
        this.$message.error("工作日加班时长设置必须至少选中一项");
        return false;
      }
      if (this.form2.allowedOvertimeSetting && this.testCheck(this.form2)) {
        this.$message.error("休息日加班时长设置必须至少选中一项");
        return false;
      }
      if (this.form3.allowedOvertimeSetting && this.testCheck(this.form3)) {
        this.$message.error("节假日加班时长设置必须至少选中一项");
        return false;
      }
      return true;
    },
    //获取时长下拉数据
    getShiftLeaveList() {
      this.$attApi.apiGetShiftLeaveList().then((res) => {
        if (res.success) {
          if (JSON.stringify(res.data) == "{}") {
            this.isExistLeave = false;
          } else {
            this.isExistLeave = true;
            this.form1.toDaysOffHolidayId = Number(Object.keys(res.data)[0]);
            this.form2.toDaysOffHolidayId = Number(Object.keys(res.data)[0]);
            this.form3.toDaysOffHolidayId = Number(Object.keys(res.data)[0]);
            this.holidayId = Number(Object.keys(res.data)[0]);
            this.overTimes = Object.values(res.data)[0];
          }
          if (this.$route.query.id) {
            this.overTimeTitle = "编辑加班规则";
            this.editOvertime();
          }
        }
      });
    },
    //获取法定节假日
    getSpecialDay() {
      this.$attApi.apiSpecialDaytemplate().then((res) => {
        if (res.success) {
          this.specialDay = res.data;
          this.form3.specialDayReqList = res.data;
        }
      });
    },
    //编辑回显
    editOvertime() {
      this.$attApi
        .apigetQueryOvertimeRule({ id: this.$route.query.id })
        .then((res) => {
          if (res.success) {
            const DATA = res.data;
            const typeList = res.data.overtimeRuleDetailResultList;
            this.form = {
              id: DATA.id,
              ruleName: DATA.ruleName,
              overtimeStatisticsUnit: DATA.overtimeStatisticsUnit,
              overtimeStatisticsCeilHour: DATA.overtimeStatisticsCeilHour,
              overtimeStatisticsCeilDay: DATA.overtimeStatisticsCeilDay,
            };
            //处理回显时间格式
            typeList.forEach((val) => {
              delete val.coId;
              delete val.orId;
              if (val.restPeriodVoList) {
                val.restPeriodVoList.forEach((it) => {
                  it.endTime = new Date(
                    2021,
                    1,
                    1,
                    it.endTime.slice(0, 2),
                    it.endTime.slice(2)
                  );

                  it.startTime = new Date(
                    2021,
                    1,
                    1,
                    it.startTime.slice(0, 2),
                    it.startTime.slice(2)
                  );
                });
              }

              //判断调休旧假期是否被删除
              if (val.toDaysOffHolidayId && this.holidayId) {
                this.isChangeLeave =
                  this.holidayId == val.toDaysOffHolidayId ? false : true;
              }

              switch (val.ruleType) {
                case "WORKDAY":
                  this.form1 = val;
                  this.form1.toDaysOffHolidayId = this.holidayId;
                  this.num1 = val.restPeriodVoList
                    ? val.restPeriodVoList.length
                    : 1;
                  this.form1.currRestPeriodVoList = val.restPeriodVoList
                    ? val.restPeriodVoList
                    : this.defalutRestList;

                  delete this.form1.restPeriodVoList;
                  console.log(this.form1);
                  break;
                case "RESTDAY":
                  this.form2 = val;
                  this.form2.toDaysOffHolidayId = this.holidayId;
                  this.num2 = val.restPeriodVoList
                    ? val.restPeriodVoList.length
                    : 1;
                  this.form2.currRestPeriodVoList = val.restPeriodVoList
                    ? val.restPeriodVoList
                    : this.defalutRestList;
                  delete this.form2.restPeriodVoList;
                  break;
                case "HOLIDAY":
                  this.form3 = val;
                  this.form3.toDaysOffHolidayId = this.holidayId;
                  this.num3 = val.restPeriodVoList
                    ? val.restPeriodVoList.length
                    : 1;
                  this.form3.currRestPeriodVoList = val.restPeriodVoList
                    ? val.restPeriodVoList
                    : this.defalutRestList;
                  delete this.form3.restPeriodVoList;
                  break;
              }
            });
          }
        });
    },
    handleSelect(val) {
      console.log(val);
    },
    addWorkBal(type) {
      let init = {
        allowedDeductRestTime: false,
        endTime: new Date(2021, 1, 1, 0, 0, 0),
        startTime: new Date(2021, 1, 1, 0, 0, 0),
      };
      switch (type) {
        case 1:
          this.form1.currRestPeriodVoList.push(init);
          this.num1++;
          break;
        case 2:
          this.form2.currRestPeriodVoList.push(init);
          this.num2++;
          break;
        case 3:
          this.form3.currRestPeriodVoList.push(init);
          this.num3++;
          break;
      }
    },
    delWorkBal(type) {
      switch (type) {
        case 1:
          console.log(this.form1);
          let len = this.form1.currRestPeriodVoList.length;
          console.log(this.form1);
          this.form1.currRestPeriodVoList.splice(len - 1, len);
          this.num1--;
          break;
        case 2:
          this.form2.currRestPeriodVoList.splice(
            this.form2.currRestPeriodVoList.length - 1,
            this.form2.currRestPeriodVoList.length
          );
          this.num2--;
          break;
        case 3:
          this.form3.currRestPeriodVoList.splice(
            this.form3.currRestPeriodVoList.length - 1,
            this.form3.currRestPeriodVoList.length
          );
          this.num3--;
          break;
      }
    },
    //标准时间格式转换成时间格式
    formatDateTime(inputTime) {
      let date = new Date(inputTime);
      let h = date.getHours().toString().padStart(2, "0");
      let m = date.getMinutes().toString().padStart(2, "0");
      return h + m;
    },
    getTime(type) {
      let arr = [];
      type.forEach((val, index) => {
        arr.push({
          allowedDeductRestTime: val.allowedDeductRestTime,
          startTime: this.formatDateTime(val.startTime),
          endTime: this.formatDateTime(val.endTime),
        });
      });
      return arr;
    },
    //处理休息时间段参数格式
    dealRestTime() {
      this.form1.restPeriodVoList = this.getTime(
        this.form1.currRestPeriodVoList
      );
      this.form2.restPeriodVoList = this.getTime(
        this.form2.currRestPeriodVoList
      );
      this.form3.restPeriodVoList = this.getTime(
        this.form3.currRestPeriodVoList
      );
      //
    },
    type(data) {
      let dist = {
        "[object Array]": "array",
        "[object Object]": "object",
        "[object Number]": "number",
        "[object Function]": "function",
        "[object String]": "string",
        "[object Null]": "null",
        "[object Undefined]": "undefined",
      };

      return dist[Object.prototype.toString.call(data)];
    },
    //对象深拷贝
    deepCopy(data) {
      let newData;
      if (this.type(data) === "array") {
        newData = [];
        data.map((item, index) => {
          newData[index] = this.deepCopy(item);
        });
      } else if (this.type(data) === "object") {
        newData = {};
        Object.keys(data).map((item) => {
          newData[item] = this.deepCopy(data[item]);
        });
      } else {
        newData = data;
      }
      return newData;
    },
    //节日类型切换
    handleClick() {},
    //校验调休假期是否发生变更
    checkChangeLeave() {
      if (
        this.form1.leaveOrPayType === "COMPENSATORY_LEAVE" ||
        this.form2.leaveOrPayType === "COMPENSATORY_LEAVE" ||
        this.form3.leaveOrPayType === "COMPENSATORY_LEAVE"
      ) {
        return true;
      }
      return false;
    },
    //判断是否假期发生变更
    saveBefore() {
      if (this.checkChangeLeave() && this.isChangeLeave) {
        this.$confirm(
          `该加班规则下记为调休时长的假期类型发生变更，保存后将按新的调休假期【${this.overTimes}】记录时长`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            closeOnClickModal: false,
            closeOnPressEscape: false,
          }
        )
          .then(() => {
            this.save();
          })
          .catch(() => {});
      } else {
        this.save();
      }
    },
    save: debounce(
      function () {
        console.log(this.form1);
        this.$refs.form.validate((valid) => {
          if (valid && this.checkInput()) {
            this.dealRestTime();
            let params = {},
              overtimeRuleDetailReqList = [];
            this.form1.toDaysOffHolidayId =
              this.form1.leaveOrPayType === "OVERTIME_PAY"
                ? ""
                : this.holidayId;
            this.form2.toDaysOffHolidayId =
              this.form2.leaveOrPayType === "OVERTIME_PAY"
                ? ""
                : this.holidayId;
            this.form3.toDaysOffHolidayId =
              this.form3.leaveOrPayType === "OVERTIME_PAY"
                ? ""
                : this.holidayId;

            overtimeRuleDetailReqList.push(this.form1);
            overtimeRuleDetailReqList.push(this.form2);
            overtimeRuleDetailReqList.push(this.form3);
            params = {
              isDefault: this.$route.query.id
                ? this.$route.query.isDefault
                : false,
              ...this.form,
              overtimeRuleDetailReqList,
            };
            let newParams = this.deepCopy(params);
            console.log(newParams);
            delete newParams.overtimeRuleDetailReqList[0].currRestPeriodVoList;
            delete newParams.overtimeRuleDetailReqList[1].currRestPeriodVoList;
            delete newParams.overtimeRuleDetailReqList[2].currRestPeriodVoList;

            this.$attApi.apiSavaOrUpdateOvertimeRule(newParams).then((res) => {
              if (res.success) {
                this.$message({
                  type: "success",
                  message: "保存成功!",
                });
                this.$router.go(-1);
              }
              console.log(this.form1);
            });
          } else {
            this.$nextTick(() => {
              this.errorScroll(
                document.querySelectorAll("div.el-form-item__error")
              );
            });
          }
        });
      },
      2000,
      true
    ),
    cancel() {
      console.log("取消");
      this.$router.go(-1);
    },
    goBack() {
      this.$confirm("离开当前页面会丢失未保存的修改信息, 确定离开吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        this.$router.go(-1);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.addOvertime {
  .header {
    border-bottom: 1px solid #ededed;
  }
  .content {
    height: calc(100vh - 200px);
    overflow: auto;
    padding: 20px 20px 0 20px;
    .overtimeName {
      /deep/ .el-form-item__label {
        color: #333;
        font-size: 14px;
      }
    }
    .hint-info {
      color: #888888;
    }
    .title {
      padding: 0 0 22px 12px;
      font-size: 14px;
    }
    .dateType {
      padding-left: 12px;
      .el-divider--horizontal {
        margin-left: 130px;
      }
      .el-tabs__nav-wrap::after {
        display: none;
      }
      .el-radio {
        margin: 12px 0 0 0 !important;
        display: block;
      }
      .restTime {
        .el-checkbox {
          margin-bottom: 5px;
        }
        .el-input {
          width: 70px;
          height: 10px;
        }
        .restRange {
          .el-input {
            width: 180px;
          }
          .el-checkbox {
            display: inline-block;
            width: 20px;
          }
        }
        /deep/ .el-input-number.is-controls-right .el-input-number__increase {
          top: 2px;
        }
      }
      .overtimePay {
        .el-select {
          width: 120px;
          margin-top: 5px;
        }
      }
      .specialDay {
        width: 500px;
        span {
          display: block;
        }
        // span:nth-child(odd) {
        //   margin-right: 40px;
        // }
      }
    }
    .timeUnit {
      .el-select {
        width: 200px;
        margin-bottom: 10px;
      }
      .tip {
        color: #888888;
        line-height: 20px;
      }
    }
    .timesReduce {
      padding-bottom: 10px;
      .el-input {
        width: 70px;
      }
      .tip {
        color: #888888;
      }
    }
    .el-select,
    .el-input {
      width: 300px;
    }
    .el-checkbox {
      display: block;
      width: 60px;
    }
  }
  .viewPage {
    pointer-events: none;
    /deep/ .el-input__suffix-inner {
      pointer-events: none;
    }
  }
  .footer {
    position: fixed;
    bottom: 0;
    width: calc(100% - 223px);
    padding: 20px 0 20px 0px;
    border-top: 1px solid #e5e5e5;
    background: #fff;
    text-align: center;
  }
}
</style>
