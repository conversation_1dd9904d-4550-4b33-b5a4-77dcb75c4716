<template>
  <div class="addCardRule">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="goBack" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>{{ $route.query.id ? "编辑补卡规则" : "新增补卡规则" }}</span>
        </el-col>
      </el-row>
    </header>
    <div class="cardPage">
      <el-form :rules="rules" ref="form" :model="form" label-width="110px">
        <el-form-item label="补卡规则名称" prop="ruleName">
          <div class="ruleName">
            <el-input
              v-model.trim="form.ruleName"
              placeholder="请输入补卡规则名称"
            ></el-input>
            <span style="width:350px;margin-left:20px;color:#888888"
              >最多30个字符(中英文或数字)</span
            >
          </div>
        </el-form-item>
        <el-form-item label="补卡设置">
          <div class="ruleSet">
            <el-checkbox v-model="form.allowSupplement"
              >允许补卡（勾选后，员工可发起补卡）</el-checkbox
            >
            <div class="rule-child">
              <el-checkbox
                v-model="form.allowTimesLimit"
                v-if="form.allowSupplement"
              >
                限制补卡次数：每月可提交
                <el-input v-model="form.timesLimit"></el-input>
                次补卡 （按自然月计算）
              </el-checkbox>
              <el-checkbox
                v-model="form.allowDateLimit"
                v-if="form.allowSupplement"
              >
                限制补卡时间：可申请过去
                <el-input v-model="form.dateLimit"></el-input>
                天内的补卡 （填写0天，则只能发起当天的补卡）
              </el-checkbox>
            </div>
          </div>
        </el-form-item>
        <el-form-item> </el-form-item>
      </el-form>
    </div>
    <div class="mian-footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </div>
  </div>
</template>
<script>
import { checkCardRule } from "../util/validate";
import { debounce } from "../util/debounce";
export default {
  data() {
    return {
      checked: true,
      isClockAgain: false,
      form: {
        ruleName: "", //补卡规则名称
        allowSupplement: true, //允许补卡
        allowDateLimit: true, //允许时间限制
        allowTimesLimit: true, //允许次数限制
        dateLimit: 0, //补卡时间
        timesLimit: 0 //补卡次数
      },
      rules: {
        ruleName: [
          {
            required: true,
            validator: checkCardRule,
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    if (this.$route.query.id) {
      this.getCardDetail();
    }
  },
  methods: {
    //校验补卡次数时间
    checkNumber() {
      let rule = new RegExp('^[0-9]*$');
      if (this.form.allowSupplement) {
        if (!rule.test(this.form.timesLimit)) {
          this.$message.error("补卡次数请输入数字");
          return false;
        }
        if (rule.test(this.form.timesLimit) && this.form.timesLimit > 999) {
          this.$message.error("补卡次数最大值不能超过999次");
          this.form.timesLimit = 999;
          return false;
        }
        if (!rule.test(this.form.dateLimit)) {
          this.$message.error("补卡时间请输入数字");
          return false;
        }
        if (rule.test(this.form.dateLimit) && this.form.dateLimit > 180) {
          this.$message.error("补卡时间最大值不能超过180天");
          this.form.dateLimit = 180;
          return false;
        }
        return true;
      }
      return true;
    },
    //保存页面
    save: debounce(
      function() {
        if (!this.form.allowSupplement) {
          this.form.dateLimit = "";
          this.form.timesLimit = "";
          this.form.allowDateLimit = false;
          this.form.allowTimesLimit = false;
        }
        let params = {
          ...this.form,
          isDefault: this.$route.query.id ? this.$route.query.isDefault : false
        };
        this.$refs.form.validate(valid => {
          if (valid) {
            if (!this.checkNumber()) return;
            this.$attApi.apiPostSavaOrUpdateSupplementRule(params).then(res => {
              if (res.success) {
                this.$message({
                  type: "success",
                  message: "保存成功!"
                });
                this.$router.go(-1);
              }
            });
          } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
        });
      },
      2000,
      true
    ),
    //回显补卡规则
    getCardDetail() {
      this.$attApi
        .apiGetQuerySupplementRule({ id: this.$route.query.id })
        .then(res => {
          if (res.success) {
            const rs = res.data;
            this.form.allowDateLimit = rs.allowDateLimit;
            this.form.allowSupplement = rs.allowSupplement;
            this.form.allowTimesLimit = rs.allowTimesLimit;
            this.form.ruleName = rs.ruleName;
            this.form.timesLimit = rs.timesLimit;
            this.form.dateLimit = rs.dateLimit;
            this.form.id = rs.id;
          }
        });
    },
    cancel() {
      this.$router.go(-1);
    },
    goBack() {
      this.$confirm("离开当前页面会丢失未保存的修改信息, 确定离开吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false
      }).then(() => {
        this.$router.go(-1);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.addCardRule {
  .header {
    border-bottom: 1px solid #ededed;
  }
  .cardPage {
    padding: 20px;
    .ruleName {
      /deep/ .el-input {
        width: 300px;
      }
    }
  }
  .ruleSet {
    display: flex;
    flex-direction: column;
    width: 50px;
    /deep/ .el-input {
      width: 60px;
      height: 30px;
    }
    .rule-child {
      margin-left: 25px;
      display: flex;
      flex-direction: column;
    }
  }

  .mian-footer {
    position: fixed;
    bottom: 0;
    width: calc(100% - 223px);
    padding: 20px 0 20px 0px;
    border-top: 1px solid #e5e5e5;
    background: #fff;
    text-align: center;
    z-index: 99;
  }
}
</style>
