<template>
  <div>
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="goBack" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>{{ $route.query.id ? "编辑班次" : "新增班次" }}</span>
        </el-col>
      </el-row>
    </header>
    <div class="page-mian">
      <el-form :model="form" ref="form" :rules="rules" label-width="130px">
        <el-form-item label="班次名称" prop="groupName">
          <div class="shift-name">
            <el-input
              v-model.trim="form.groupName"
              placeholder="请输入班次名称"
            ></el-input>
            <span style="width:350px;margin-left:20px;color:#888888"
              >最多30个字符(中英文或数字)</span
            >
          </div>
        </el-form-item>
        <h3>打卡时段<span class="hint-info">按班次一天内上下班的次数</span></h3>
        <el-form-item label="一天内上下班次数">
          <div style="width: 50px">
            <el-radio-group
              v-model="shiftCount"
              class="work-num"
              @change="handleChange"
            >
              <el-radio label="1">一天一次上下班</el-radio>
              <el-radio label="2">一天两次上下班</el-radio>
              <el-radio label="3">一天三次上下班</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
        <h3 class="time-form" v-if="shiftCount === '1'">考勤时间形式</h3>
        <el-select
          v-model="shiftType"
          v-if="shiftCount === '1'"
          :popper-append-to-body="false"
        >
          <el-option label="当前班次 固定时间" value="FIX"></el-option>
          <!-- <el-option label="当前班次 弹性时间" value="FLEX"></el-option> -->
        </el-select>
        <div class="twoContent">
          <div class="tipTable">
            <table>
              <tr>
                <td>上下班时间</td>
                <td>打卡限制</td>
              </tr>
              <tr>
                <td>
                  第一次上班
                  <el-time-picker
                    v-model="workingBegin[0]"
                    format="HH:mm"
                    value-format="HH:mm:ss"
                    :picker-options="{
                      selectableRange: `'00:00:00 '- ${workingEnd[0]}`
                    }"
                    placeholder="选择时间"
                    :append-to-body="false"
                    :editable="false"
                    :clearable="false"
                    @change="handleSelect"
                  >
                  </el-time-picker>
                </td>
                <td>
                  <p class="top-p">
                    <span class="left">最早可提前</span>
                    <el-input
                      v-model="workingBeginAdvance[0]"
                      style="width:100px"
                      @blur="handleBeginAdvance(workingBeginAdvance[0], 0)"
                    ></el-input
                    >
                    <span class="rights">分钟进行上班打卡</span>
                  </p>
                  <p>
                    <span class="left">晚到超过</span>
                    <el-input
                      v-model="workingBeginLate[0]"
                      style="width:100px"
                      @blur="
                        handleBeginLate(
                          workingBeginLate[0],
                          workingBeginAbsent[0],
                          0
                        )
                      "
                    ></el-input>
                    <span class="rights">分钟记为迟到</span>
                  </p>
                  <p>
                    <span class="left">晚到超过</span>
                    <el-input
                      v-model="workingBeginAbsent[0]"
                      style="width:100px"
                      @blur="
                        handleBeginAbsent(
                          workingBeginAbsent[0],
                          workingBeginLate[0],
                          0
                        )
                      "
                    ></el-input>
                    <span class="rights">分钟记为半天缺卡（半天旷工）</span>
                  </p>
                </td>
              </tr>
              <tr>
                <td>
                  第一次下班
                  <el-time-picker
                    v-model="workingEnd[0]"
                    format="HH:mm"
                    value-format="HH:mm:ss"
                    :picker-options="{
                      selectableRange: `${
                        workingBegin[0]
                      } - this.shiftCount=='1'?'23:59:59':${workingBegin[1]}`
                    }"
                    placeholder="选择时间"
                    :editable="false"
                    :clearable="false"
                    :append-to-body="false"
                    @change="handleSelect"
                  >
                  </el-time-picker>
                </td>
                <td>
                  <p class="top-p">
                    <span class="left">最晚可延后</span>
                    <el-input
                      v-model="workingEndLate[0]"
                      style="width:100px"
                      @blur="handleEndLate(workingEndLate[0], 0)"
                    ></el-input>
                    <span class="rights">分钟进行下班打卡</span>
                  </p>
                  <p>
                    <span class="left">早于下班时间</span>
                    <el-input
                      v-model="workingEndAdvance[0]"
                      style="width:100px"
                      @blur="
                        handleEndAdvance(
                          workingEndAdvance[0],
                          workingEndAbsent[0],
                          0
                        )
                      "
                    ></el-input>
                    <span class="rights">分钟打卡记为早退</span>
                  </p>
                  <p>
                    <span class="left">早于下班时间</span>
                    <el-input
                      v-model="workingEndAbsent[0]"
                      style="width:100px"
                      @blur="
                        handleEndAbsent(
                          workingEndAbsent[0],
                          workingEndAdvance[0],
                          0
                        )
                      "
                    ></el-input>
                    <span class="rights">分钟打卡记为半天缺卡（半天旷工）</span>
                  </p>
                </td>
              </tr>
              <tr v-show="shiftCount !== '1'">
                <td>
                  第二次上班
                  <el-time-picker
                    v-model="workingBegin[1]"
                    format="HH:mm"
                    value-format="HH:mm:ss"
                    :picker-options="{
                      selectableRange: `${workingEnd[0]} - ${workingEnd[1]}`
                    }"
                    placeholder="选择时间"
                    @change="handleSelect"
                    :append-to-body="false"
                    :editable="false"
                    :clearable="false"
                  >
                  </el-time-picker>
                </td>
                <td>
                  <p>
                    <span class="left">最早可提前</span>
                    <el-input
                      v-model="workingBeginAdvance[1]"
                      style="width:100px"
                      @blur="handleBeginAdvance(workingBeginAdvance[1], 1)"
                    ></el-input
                    >
                    <span class="rights">分钟进行上班打卡</span>
                  </p>
                  <p>
                    <span class="left">晚到超过</span>
                    <el-input
                      v-model="workingBeginLate[1]"
                      style="width:100px"
                      @blur="
                        handleBeginLate(
                          workingBeginLate[1],
                          workingBeginAbsent[1],
                          1
                        )
                      "
                    ></el-input
                    >
                    <span class="rights">分钟记为迟到</span>
                  </p>
                  <p>
                    <span class="left">晚到超过</span>
                    <el-input
                      v-model="workingBeginAbsent[1]"
                      style="width:100px"
                      @blur="
                        handleBeginAbsent(
                          workingBeginAbsent[1],
                          workingBeginLate[1],
                          1
                        )
                      "
                    ></el-input
                    >
                    <span class="rights">分钟记为半天缺卡（半天旷工）</span>
                  </p>
                </td>
              </tr>
              <tr v-show="shiftCount !== '1'">
                <td>
                  第二次下班
                  <el-time-picker
                    v-model="workingEnd[1]"
                    format="HH:mm"
                    value-format="HH:mm:ss"
                    :picker-options="{
                      selectableRange: `${
                        workingBegin[1]
                      } - this.shiftCount=='2'?'23:59:59':${workingBegin[2]}`
                    }"
                    placeholder="选择时间"
                    @change="handleSelect"
                    :append-to-body="false"
                    :editable="false"
                    :clearable="false"
                  >
                  </el-time-picker>
                </td>
                <td>
                  <p>
                    <span class="left">最晚可延后</span>
                    <el-input
                      v-model="workingEndLate[1]"
                      style="width:100px"
                      @blur="handleEndLate(workingEndLate[1], 1)"
                    ></el-input
                    >
                    <span class="rights">分钟进行下班打卡</span>
                  </p>
                  <p>
                    <span class="left">早于下班时间</span>
                    <el-input
                      v-model="workingEndAdvance[1]"
                      style="width:100px"
                      @blur="
                        handleEndAdvance(
                          workingEndAdvance[1],
                          workingEndAbsent[1],
                          1
                        )
                      "
                    ></el-input
                    >
                    <span class="rights">分钟打卡记为早退</span>
                  </p>
                  <p>
                    <span class="left">早于下班时间</span>
                    <el-input
                      v-model="workingEndAbsent[1]"
                      style="width:100px"
                      @blur="
                        handleEndAbsent(
                          workingEndAbsent[1],
                          workingEndAdvance[1],
                          1
                        )
                      "
                    ></el-input
                    >
                    <span class="rights">分钟打卡记为半天缺卡（半天旷工）</span>
                  </p>
                </td>
              </tr>
              <tr v-show="shiftCount === '3'">
                <td>
                  第三次上班
                  <el-time-picker
                    v-model="workingBegin[2]"
                    format="HH:mm"
                    value-format="HH:mm:ss"
                    :picker-options="{
                      selectableRange: `${workingEnd[1]} - ${workingEnd[2]}`
                    }"
                    placeholder="选择时间"
                    @change="handleSelect"
                    :append-to-body="false"
                    :editable="false"
                    :clearable="false"
                  >
                  </el-time-picker>
                </td>
                <td>
                  <p>
                    <span class="left">最早可提前</span>
                    <el-input
                      v-model="workingBeginAdvance[2]"
                      style="width:100px"
                      @blur="handleBeginAdvance(workingBeginAdvance[2], 2)"
                    ></el-input
                    >
                    <span class="rights">分钟进行上班打卡</span>
                  </p>
                  <p>
                    <span class="left">晚到超过</span>
                    <el-input
                      v-model="workingBeginLate[2]"
                      style="width:100px"
                      @blur="
                        handleBeginLate(
                          workingBeginLate[2],
                          workingBeginAbsent[2],
                          2
                        )
                      "
                    ></el-input
                    >
                    <span class="rights">分钟记为迟到</span>
                  </p>
                  <p>
                    <span class="left">晚到超过</span>
                    <el-input
                      v-model="workingBeginAbsent[2]"
                      style="width:100px"
                      @blur="
                        handleBeginAbsent(
                          workingBeginAbsent[2],
                          workingBeginLate[2],
                          2
                        )
                      "
                    ></el-input
                    >
                    <span class="rights">分钟记为半天缺卡（半天旷工）</span>
                  </p>
                </td>
              </tr>
              <tr v-show="shiftCount === '3'">
                <td>
                  第三次下班
                  <el-time-picker
                    v-model="workingEnd[2]"
                    format="HH:mm"
                    value-format="HH:mm:ss"
                    :picker-options="{
                      selectableRange: `${workingBegin[2]} - '23:59:59'`
                    }"
                    placeholder="选择时间"
                    @change="handleSelect"
                    :append-to-body="false"
                    :editable="false"
                    :clearable="false"
                  >
                  </el-time-picker>
                </td>
                <td>
                  <p>
                    <span class="left">最晚可延后</span>
                    <el-input
                      v-model="workingEndLate[2]"
                      style="width:100px"
                      @blur="handleEndLate(workingEndLate[2], 2)"
                    ></el-input
                    >
                    <span class="rights">分钟进行下班打卡</span>
                  </p>
                  <p>
                    <span class="left">早于下班时间</span>
                    <el-input
                      v-model="workingEndAdvance[2]"
                      style="width:100px"
                      @blur="
                        handleEndAdvance(
                          workingEndAdvance[2],
                          workingEndAbsent[2],
                          2
                        )
                      "
                    ></el-input
                    >
                    <span class="rights">分钟打卡记为早退</span>
                  </p>
                  <p>

                    <span class="left">早于下班时间</span>
                    <el-input
                      v-model="workingEndAbsent[2]"
                      style="width:100px"
                      @blur="
                        handleEndAbsent(
                          workingEndAbsent[2],
                          workingEndAdvance[2],
                          2
                        )
                      "
                    ></el-input
                    >
                    <span class="rights">分钟打卡记为半天缺卡（半天旷工）</span>
                  </p>
                </td>
              </tr>
            </table>
          </div>
        </div>
        <el-form-item label="工作时长">
          <span
            >{{ Math.floor(workingMinutes / 60) }}时{{
              workingMinutes % 60
            }}分</span
          >
        </el-form-item>
        <el-form-item
          label="弹性时间"
          v-if="shiftType === 'FLEX' && shiftCount === '1'"
        >
          <div class="flexTime">
            <p>
              <el-checkbox v-model="allowBeginFlex">
                上班最多可晚到：<el-input
                  v-model="workingBeginFlexHour"
                ></el-input
                >小时 <el-input v-model="workingBeginFlex"></el-input>分钟
                上班晚到几分钟，下班须晚走几分钟
              </el-checkbox>
            </p>
            <p>
              <el-checkbox v-model="allowEndFlex">
                下班最多可早走：<el-input
                  v-model="workingEndFlexHour"
                ></el-input
                >小时 <el-input v-model="workingEndFlex"></el-input>分钟
                上班早到几分钟，下班可早走几分钟
              </el-checkbox>
            </p>
          </div>
        </el-form-item>
        <el-form-item label="休息时间" v-if="shiftCount === '1'">
          <el-checkbox v-model="allowRest"></el-checkbox>
          <span>
            休息时间开始
            <el-time-picker
              v-model="restBegin"
              format="HH:mm"
              value-format="HH:mm:ss"
              :picker-options="{
                selectableRange: `${workingBegin[0]} - ${workingEnd[0]}`
              }"
              placeholder="选择时间"
              :editable="false"
              :clearable="false"
            >
            </el-time-picker>
          </span>
          <span>
            休息时间结束
            <el-time-picker
              v-model="restEnd"
              format="HH:mm"
              value-format="HH:mm:ss"
              :picker-options="{
                selectableRange: `${workingBegin[0]} - ${workingEnd[0]}`
              }"
              placeholder="选择时间"
              :editable="false"
              :clearable="false"
            >
            </el-time-picker>
          </span>
        </el-form-item>
        <!-- <h3 class="time-form">其它规则</h3>
        <div class="">
          <el-checkbox> 晚走次日晚到</el-checkbox>
          <span class="">该规则不支持排班制和自由班制</span>
        </div>
        <el-form-item label="规则1">
          <span>
            下班晚走
            <el-input-number v-model="num" controls-position="right" @change="handleChange" :min="1" :max="10"></el-input-number>
            小时
          </span>
          <span>
            次日可晚到
            <el-input-number v-model="num" controls-position="right" @change="handleChange" :min="1" :max="10"></el-input-number>
            小时，并且不算迟到
          </span>
          <el-button type="text">新增</el-button>
          <el-button type="text">删除</el-button>
        </el-form-item>        -->
      </el-form>
    </div>
    <div class="mian-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSaveBefore">保存</el-button>
    </div>
    <!-- 班次生效时间弹窗 -->
    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
      <span>请选择考勤规则生效时间</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="tomorrow">明日生效</el-button>
        <!-- <el-button type="primary" @click="immediate">立即生效</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { checkShift } from "../util/validate";
import { debounce } from "../util/debounce";
export default {
  data() {
    return {
      dialogVisible: false, //点击保存生效弹框
      isRepeatTime: false, //是否重复班次时间
      shiftCount: "1", //班次数量
      value: "",
      shiftNum: "1",
      input: "1",
      shiftType: "FIX", //班次类型
      detailId: ["", "", ""],
      workingBegin: ["09:00:00", "", ""], //上班时间
      workingBeginAdvance: [60, 30, 30], //上班提前打卡时间
      workingBeginLate: [0, 0, 0], //上班迟到打卡时间
      workingBeginAbsent: [30, 30, 30], //上班缺卡打卡时间
      workingEnd: ["18:00:00", "", ""], //下班时间
      workingEndLate: [360, 480, 480], //下班延后打卡时间
      workingEndAdvance: [0, 0, 0], //下班早退打卡时间
      workingEndAbsent: [30, 30, 30], //下班缺卡打卡时间
      restBegin: "12:00:00", //休息开始时间
      restEnd: "13:00:00", //休息结束时间
      allowRest: false, //是否开启休息时间段
      workingSwitch: true, //时长调休
      allowBeginFlex: false, //上班打卡规则是否生效
      allowEndFlex: false, //下班打卡规则是否生效
      workingBeginFlexHour: "1", //上班弹性小时
      workingBeginFlex: "0", //上班弹性分钟
      workingEndFlexHour: "1", //下班弹性小时
      workingEndFlex: "0", //下班弹性分钟
      defaultHour: "1",
      defaultmm: "0",
      form: {
        groupName: "" //班次组名称
      },
      rules: {
        groupName: [
          {
            required: true,
            validator: checkShift,
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    if (this.$route.query.id) {
      this.isRepeatTime = false;
      this.getShiftEdit();
    }
  },
  computed: {
    //工作总时长
    workingMinutes() {
      switch (this.shiftCount) {
        case "1":
          return this.getWorkHours(0);
          break;
        case "2":
          return this.getWorkHours(1) + this.getWorkHours(0);
          break;
        case "3":
          return (
            this.getWorkHours(2) + this.getWorkHours(1) + this.getWorkHours(0)
          );
          break;
      }
    }
  },
  watch: {
    shiftType(val) {
      if (val === "FIX") {
        this.clearFlexTime();
      }
    },
    shiftCount(val) {
      if (val != "1") {
        this.clearFlexTime();
        this.shiftType = "FIX";
      }
    }
  },
  methods: {
    //校验弹性时间极限值
    checkFlexRange() {
      let start = this.workingBegin[0].split(":");
      let workingBegin = parseInt(start[0] * 60) + parseInt(start[1]);
      let end = this.workingEnd[0].split(":");
      let workingEnd = parseInt(end[0] * 60) + parseInt(end[1]);

      let maxFlex = 24 * 60 - workingEnd;
      let currFlex =
        parseInt(this.workingBeginFlexHour) * 60 +
        parseInt(this.workingBeginFlex);
      let maxHour = Math.floor(maxFlex / 60);
      let maxMin = maxFlex % 60;

      let maxFlexEnd = workingBegin;
      let currFlexEnd =
        parseInt(this.workingEndFlexHour) * 60 + parseInt(this.workingEndFlex);
      let maxHourEnd = Math.floor(maxFlexEnd / 60);
      let maxMinEnd = maxFlexEnd % 60;

      if (this.allowBeginFlex) {
        if (maxFlex < currFlex) {
          this.$message.error(
            `弹性上班时间最大上限不能超过${maxHour}小时${maxMin}分钟`
          );
          return false;
        }
      }
      if (this.allowEndFlex) {
        if (maxFlexEnd < currFlexEnd) {
          this.$message.error(
            `弹性下班时间最大上限不能超过${maxHourEnd}小时${maxMinEnd}分钟`
          );
          return false;
        }
      }
      return true;
    },
    //清空弹性时间记录
    clearFlexTime() {
      this.allowBeginFlex = false;
      this.allowEndFlex = false;
      this.workingBeginFlexHour = 1;
      this.workingBeginFlex = 0;
      this.workingEndFlexHour = 1;
      this.workingEndFlex = 0;
    },
    //计算上下班工作时长
    getWorkHours(index) {
      if (this.workingBegin[index] && this.workingEnd[index]) {
        let start1 = this.workingBegin[index].split(":");
        let workingBegin = parseInt(start1[0] * 60) + parseInt(start1[1]);
        let end1 = this.workingEnd[index].split(":");
        let workingEnd = parseInt(end1[0] * 60) + parseInt(end1[1]);
        let start2 = this.restBegin.split(":");
        let restBegin = parseInt(start2[0] * 60) + parseInt(start2[1]);
        let end2 = this.restEnd.split(":");
        let restEnd = parseInt(end2[0] * 60) + parseInt(end2[1]);
        let time = "";
        if (this.allowRest && this.shiftCount === "1") {
          if (
            restBegin >= workingBegin &&
            restBegin <= workingEnd &&
            restEnd <= workingEnd &&
            restEnd >= restBegin
          ) {
            time = restBegin - workingBegin + (workingEnd - restEnd);
          } else {
            // this.$message.error("休息时间段必须在上下班时间内");
            this.restBegin = this.workingBegin[0];
            this.restEnd = this.workingEnd[0];
            time = 0;
          }
        } else {
          time = workingEnd - workingBegin;
        }
        return time;
      } else {
        return 0;
      }
    },
    //弹性时间校验
    checkFlexTime() {
      let rule = new RegExp('^[0-9]*$');
      let arrFlex = [
        this.workingBeginFlexHour,
        this.workingBeginFlex,
        this.workingEndFlex,
        this.workingEndFlexHour
      ];
      let arrFlexResult = arrFlex.filter(val => {
        return !rule.test(val) || (val !== 0 && !val);
      });
      if (this.shiftCount == "1" && this.shiftType == "FLEX") {
        if (arrFlexResult.length === 0) {
          return true;
        } else {
          this.$message.error("弹性时间不能为空且仅支持数字");
          return false;
        }
      } else {
        return true;
      }
    },
    //打卡时间限制
    checkClockTime() {
      let arrBegin = this.workingBeginAdvance
        .concat(this.workingBeginLate)
        .concat(this.workingBeginAbsent);
      let arrEnd = this.workingEndAdvance
        .concat(this.workingEndLate)
        .concat(this.workingEndAbsent);
      let arrs = arrBegin.concat(arrEnd);
      let rule = new RegExp('^[0-9]*$');
      let result = arrs.filter(val => {
        return !rule.test(val);
      });
      if (result.length === 0) {
        return true;
      } else {
        this.$message.error("打卡限制时间请输入数字");
        return false;
      }
    },
    //编辑回显
    getShiftEdit() {
      this.$attApi
        .apigetQueryAttendWork({ id: this.$route.query.id })
        .then(res => {
          if (res.success) {
            const rs = res.data;
            const flex = rs.workingShiftDetailResultList[0];
            this.form.groupName = rs.groupName;
            this.shiftCount = String(rs.shiftCount);
            this.shiftType = rs.shiftType;
            this.restBegin =
              flex.restBegin.slice(0, 2) +
              ":" +
              flex.restBegin.slice(2) +
              ":00";
            this.restEnd =
              flex.restEnd.slice(0, 2) + ":" + flex.restEnd.slice(2) + ":00";
            rs.workingShiftDetailResultList.forEach((val, index) => {
              this.detailId[index] = val.id;
              let begin = val.workingBegin
                ? val.workingBegin.slice(0, 2) +
                  ":" +
                  val.workingBegin.slice(2) +
                  ":00"
                : "";
              this.$set(this.workingBegin, index, begin);
              this.workingBeginAbsent[index] = val.workingBeginAbsent;
              this.workingBeginAdvance[index] = val.workingBeginAdvance;
              this.workingBeginLate[index] = val.workingBeginLate;
              let end = val.workingEnd
                ? val.workingEnd.slice(0, 2) +
                  ":" +
                  val.workingEnd.slice(2) +
                  ":00"
                : "";
              this.$set(this.workingEnd, index, end);
              this.workingEndAbsent[index] = val.workingEndAbsent;
              this.workingEndAdvance[index] = val.workingEndAdvance;
              this.workingEndLate[index] = val.workingEndLate;
            });
            if (rs.shiftCount === 1) {
              this.allowBeginFlex = flex.allowBeginFlex;
              this.allowEndFlex = flex.allowEndFlex;
              this.allowRest = flex.allowRest;
              this.workingBeginFlexHour = flex.workingBeginFlexHour;
              this.workingBeginFlex = flex.workingBeginFlex;
              this.workingEndFlexHour = flex.workingEndFlexHour;
              this.workingEndFlex = flex.workingEndFlex;
            }
          }
        });
    },
    //上下班参数
    getparams(index) {
      let par = {
        orderNo: index + 1,
        // id: this.detailId[index],
        shiftType: this.shiftType,
        workingBeginAbsent: this.workingBeginAbsent[index],
        workingBeginAdvance: this.workingBeginAdvance[index],
        workingBeginLate: this.workingBeginLate[index],
        workingEndAbsent: this.workingEndAbsent[index],
        workingEndAdvance: this.workingEndAdvance[index],
        workingEndLate: this.workingEndLate[index]
      };
      return par;
    },
    //保存弹框
    handleSaveBefore() {
      if (this.$route.query.id) {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.dialogVisible = true;
          } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
        })
      } else {
        this.handleSave();
      }
    },
    //明日生效
    tomorrow() {
      this.handleSave(false);
    },
    //立即生效
    immediate() {
      this.handleSave(true);
    },
    //提交保存新增班次
    handleSave: debounce(
      function(type) {
        let workShiftTotal = [
          {
            ...this.getparams(0),
            restBegin:
              this.restBegin.split(":")[0] + this.restBegin.split(":")[1],
            restEnd: this.restEnd.split(":")[0] + this.restEnd.split(":")[1],
            workingBegin:
              this.workingBegin[0].split(":")[0] +
              this.workingBegin[0].split(":")[1],
            workingEnd:
              this.workingEnd[0].split(":")[0] +
              this.workingEnd[0].split(":")[1],
            allowBeginFlex: this.allowBeginFlex,
            allowEndFlex: this.allowEndFlex,
            allowRest: this.allowRest,
            workingBeginFlexHour: this.workingBeginFlexHour,
            workingBeginFlex: this.workingBeginFlex,
            workingEndFlexHour: this.workingEndFlexHour,
            workingEndFlex: this.workingEndFlex,
            workingHours: this.getWorkHours(0)
          },
          {
            ...this.getparams(1),
            workingHours: this.getWorkHours(1),
            allowRest: false,
            workingBegin:
              this.shiftCount !== "1"
                ? this.workingBegin[1].split(":")[0] +
                  this.workingBegin[1].split(":")[1]
                : "",
            workingEnd:
              this.shiftCount !== "1"
                ? this.workingEnd[1].split(":")[0] +
                  this.workingEnd[1].split(":")[1]
                : ""
          },
          {
            ...this.getparams(2),
            workingHours: this.getWorkHours(2),
            allowRest: false,
            workingBegin:
              this.shiftCount === "3"
                ? this.workingBegin[2].split(":")[0] +
                  this.workingBegin[2].split(":")[1]
                : "",
            workingEnd:
              this.shiftCount === "3"
                ? this.workingEnd[2].split(":")[0] +
                  this.workingEnd[2].split(":")[1]
                : ""
          }
        ];
        let params = {
          isDefault: this.$route.query.id ? this.$route.query.isDefault : false,
          effectNow: this.$route.query.id ? type : true,
          groupName: this.form.groupName,
          id: this.$route.query.id ? this.$route.query.id : "",
          shiftCount: this.shiftCount,
          shiftType: this.shiftType,
          taxsubId: 0,
          workingMinutes: this.workingMinutes,
          workingShiftDetailReqList: this.getCurrShift(workShiftTotal),
          workingSwitch: true
        };
        this.$refs.form.validate(valid => {
          if (valid) {
            this.handleSelect();
            if (
              !this.isRepeatTime &&
              this.checkClockTime() &&
              this.checkFlexTime() &&
              this.checkFlexRange()
            ) {
              this.$attApi.apiPostSavaOrUpdateWorking(params).then(res => {
                if (res.success) {
                  this.$message({
                    type: "success",
                    message: "保存成功!"
                  });
                  this.$router.go(-1);
                }
              });
            }
          } else {
            this.$nextTick(()=>{
              this.errorScroll(document.querySelectorAll("div.el-form-item__error"))
            })
          }
        });
      },
      2000,
      true
    ),
    //根据班次数量，传班次详情
    getCurrShift(workShiftTotal) {
      let arr = [];
      switch (this.shiftCount) {
        case "1":
          arr.push(workShiftTotal[0]);
          return arr;
          break;
        case "2":
          arr.push(workShiftTotal[0]);
          arr.push(workShiftTotal[1]);
          return arr;
          break;
        case "3":
          return workShiftTotal;
          break;
      }
    },
    handleCancel() {
      this.$router.go(-1);
    },
    goBack() {
      this.$confirm("离开当前页面会丢失未保存的修改信息, 确定离开吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false
      }).then(() => {
        this.$router.go(-1);
      });
    },
    //一天上下班次数切换
    handleChange() {
      switch (this.shiftCount) {
        case "1":
          (this.workingBegin = ["09:00:00", "", ""]),
            (this.workingEnd = ["18:00:00", "", ""]);
          this.workingBeginAdvance = [60, 30, 30]; //上班提前打卡时间
          this.workingBeginLate = [0, 0, 0]; //上班迟到打卡时间
          this.workingBeginAbsent = [30, 30, 30]; //上班缺卡打卡时间
          this.workingEndLate = [360, 480, 480]; //下班延后打卡时间
          this.workingEndAdvance = [0, 0, 0]; //下班早退打卡时间
          this.workingEndAbsent = [30, 30, 30]; //下班缺卡打卡时间
          break;
        case "2":
          (this.workingBegin = ["09:00:00", "13:00:00", ""]),
            (this.workingEnd = ["12:00:00", "18:00:00", ""]);
          this.workingBeginAdvance = [60, 30, 30]; //上班提前打卡时间
          this.workingBeginLate = [0, 0, 0]; //上班迟到打卡时间
          this.workingBeginAbsent = [30, 30, 30]; //上班缺卡打卡时间
          this.workingEndLate = [30, 360, 480]; //下班延后打卡时间
          this.workingEndAdvance = [0, 0, 0]; //下班早退打卡时间
          this.workingEndAbsent = [30, 30, 30]; //下班缺卡打卡时间
          break;
        case "3":
          (this.workingBegin = ["09:00:00", "13:00:00", "19:00:00"]),
            (this.workingEnd = ["12:00:00", "18:00:00", "20:00:00"]);
          this.workingBeginAdvance = [60, 30, 30]; //上班提前打卡时间
          this.workingBeginLate = [0, 0, 0]; //上班迟到打卡时间
          this.workingBeginAbsent = [30, 30, 30]; //上班缺卡打卡时间
          this.workingEndLate = [30, 30, 240]; //下班延后打卡时间
          this.workingEndAdvance = [0, 0, 0]; //下班早退打卡时间
          this.workingEndAbsent = [30, 30, 30]; //下班缺卡打卡时间
          break;
      }
    },
    //判断数组是否重复
    isRepeat(arr) {
      var hash = {};
      for (var i in arr) {
        if (hash[arr[i]]) {
          return true;
        }
        // 不存在该元素，则赋值为true，可以赋任意值，相应的修改if判断条件即可
        hash[arr[i]] = true;
      }
      return false;
    },
    handleSelect() {
      let arr = [];
      for (let i = 0; i < this.workingBegin.length; i++) {
        if (this.workingBegin[i]) {
          arr.push(this.workingBegin[i]);
        }
      }
      for (let j = 0; j < this.workingEnd.length; j++) {
        if (this.workingEnd[j]) {
          arr.push(this.workingEnd[j]);
        }
      }
      if (this.isRepeat(arr)) {
        this.$message.error("两个时间点不能设置为同一时间");
        this.isRepeatTime = true;
      } else {
        this.isRepeatTime = false;
      }
    },
    handleHour(data) {
      if (data == "") {
        this.workingBeginFlexHour = 1;
      }
      if (data == "") {
        this.workingEndFlexHour = 1;
      }
    },
    handleMm(data) {
      if (data == "") {
        this.workingBeginFlex = 0;
      }
      if (data == "") {
        this.workingEndFlex = 0;
      }
    },
    //提前上班
    handleBeginAdvance(data, code) {
      data = data * 1;
      if (code == 0) {
        let start1 = this.workingBegin[0].split(":");
        let workingBegin = parseInt(start1[0] * 60) + parseInt(start1[1]);
        if (data > workingBegin) {
          this.$message.warning(
            "最早可提前打卡时间不能早于00:00，因此当前可设置最大值为" +
              workingBegin +
              "分钟"
          );
          this.$set(this.workingBeginAdvance, 0, workingBegin);
        }
      } else {
        let start1 = this.workingBegin[code].split(":");
        let workingBegin = parseInt(start1[0] * 60) + parseInt(start1[1]);
        let end1 = this.workingEnd[code - 1].split(":");
        let workingEnd = parseInt(end1[0] * 60) + parseInt(end1[1]);
        if (data > workingBegin - workingEnd - this.workingEndLate[0]) {
          this.$message.warning(
            "最早可提前打卡时间不能早于上次下班时间,因此当前可设置最大值为" +
              (workingBegin - workingEnd - this.workingEndLate[code - 1])
          );
          this.$set(
            this.workingBeginAdvance,
            code,
            workingBegin - workingEnd - this.workingEndLate[code - 1]
          );
        }
      }
    },
    //上班迟到
    handleBeginLate(data, item, code) {
      data = data * 1;
      item = item * 1;
      console.log(data >= item);
      if (data >= item) {
        this.$message.warning("迟到的时长不能大于等于旷工的时长");
        this.$set(this.workingBeginLate, code, item - 1);
      }
    },
    //上班旷工
    handleBeginAbsent(data, item, code) {
      data = data * 1;
      item = item * 1;
      this.$set(this.workingBeginAbsent, code, data == 0 ? 1 : data);
      let start1 = this.workingBegin[code].split(":");
      let workingBegin = parseInt(start1[0] * 60) + parseInt(start1[1]);
      let end1 = this.workingEnd[code].split(":");
      let workingEnd = parseInt(end1[0] * 60) + parseInt(end1[1]);
      if (data > workingEnd - workingBegin) {
        this.$message.warning("旷工的时长不能大于工作的时长");
        this.$set(this.workingBeginAbsent, code, workingEnd - workingBegin);
      }
      if (data <= item) {
        this.$message.warning("迟到的时长不能大于等于旷工的时长");
        this.$set(this.workingBeginAbsent, code, item + 1);
      }
    },
    //下班延后打卡
    handleEndLate(data, code) {
      data = data * 1;
      let end1 = this.workingEnd[code].split(":");
      let workingEnd = parseInt(end1[0] * 60) + parseInt(end1[1]);
      let dayWorking = parseInt(24 * 60);
      if (this.shiftCount == code + 1) {
        if (data > dayWorking - workingEnd) {
          this.$message.warning(
            "最晚可打卡时间不能晚于当日24:00，当前可设置最大值为" +
              (dayWorking - workingEnd) +
              "分钟"
          );
          this.$set(this.workingEndLate, code, dayWorking - workingEnd);
        }
      } else {
        let start1 = this.workingBegin[code + 1].split(":");
        let workingBegin = parseInt(start1[0] * 60) + parseInt(start1[1]);
        if (data > workingBegin - workingEnd - this.workingBeginAdvance[1]) {
          this.$message.warning(
            "最早可提前打卡时间不能早于上次下班时间,因此当前可设置最大值为" +
              (workingBegin - workingEnd - this.workingBeginAdvance[code + 1])
          );
          this.$set(
            this.workingEndLate,
            code,
            workingBegin - workingEnd - this.workingBeginAdvance[code + 1]
          );
        }
      }
    },
    //下班早退
    handleEndAdvance(data, item, code) {
      data = data * 1;
      item = item * 1;
      if (data >= item) {
        this.$message.warning("早退的时长不能大于等于旷工的时长");
        this.$set(this.workingEndAdvance, code, item - 1);
      }
    },
    //下班缺卡
    handleEndAbsent(data, item, code) {
      data = data * 1;
      item = item * 1;
      this.$set(this.workingEndAbsent, code, data == 0 ? 1 : data);
      let start1 = this.workingBegin[code].split(":");
      let workingBegin = parseInt(start1[0] * 60) + parseInt(start1[1]);
      let end1 = this.workingEnd[code].split(":");
      let workingEnd = parseInt(end1[0] * 60) + parseInt(end1[1]);
      if (data > workingEnd - workingBegin) {
        this.$message.warning("旷工的时长不能大于工作的时长");
        this.$set(this.workingEndAbsent, code, workingEnd - workingBegin);
      }
      if (data <= item) {
        this.$message.warning("早退的时长不能大于等于旷工的时长");
        this.$set(this.workingEndAbsent, code, item + 1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.header {
  border-bottom: 1px solid #ededed;
}
.page-mian {
  padding: 20px;
  height: calc(100vh - 230px);
  overflow: auto;
  .shift-name {
    width: 500px;
    display: flex;
  }
  h3 > span {
    font-size: 14px;
    color: #888888;
    padding-left: 20px;
  }
  .work-num {
    display: flex;
    flex-direction: column;
  }
  .time-form {
    padding-bottom: 20px;
  }
  .twoContent {
    .tipTable {
      text-align: center;
      padding: 15px 0;
      width: 700px;
      table {
        border-collapse: collapse;
        border-spacing: 0;
        border: 1px solid #cbcbcb;
        margin-top: 15px;
        tr {
          text-align: center;
        }
        td {
          text-align: left;
          height: 30px;
          padding-left: 10px;
          border: 1px solid #cbcbcb;
          p{
            margin-bottom: 10px;
            .left{
              display: inline-block;
              width: 100px;
              text-align: right;
              margin-right: 10px;
              line-height: 40px;
            }
            .rights{
              display: inline-block;
              width: 230px;
              text-align: left;
              margin-left: 10px;
              line-height: 40px;
            }
          }
          .top-p{
            margin-top: 10px;
          }
        }
      }
    }
  }
}
.search-box {
  width: 700px;
  display: flex;
  line-height: 40px;
  margin-bottom: 10px;
  padding-top: 10px;
}
.mian-model {
  width: 98%;
  background-color: white;
  margin-top: 10px;
  padding-top: 5px;
  padding-bottom: 10px;
}
.model-title {
  display: flex;
  font-size: 20px;
  border-bottom: 1px solid #ededed;
  margin-bottom: 10px;
}
.info-peo {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}
.shift-bn {
  display: flex;
  flex-direction: row-reverse;
}
.mian-footer {
  position: fixed;
  bottom: 0;
  width: calc(100% - 223px);
  padding: 20px 0 20px 0px;
  border-top: 1px solid #e5e5e5;
  background: #fff;
  text-align: center;
}

.flexTime {
  /deep/ .el-input {
    width: 100px;
  }
}
/deep/ .dialog-footer {
  position: absolute;
  display: flex;
  flex-direction: row;
  bottom: 10px;
  right: 20px;
}
/deep/ .el-dialog__body {
  padding: 20px 20px 40px;
}
</style>
