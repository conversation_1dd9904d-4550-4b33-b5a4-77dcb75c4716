<template>
  <!-- 更改补卡规则  -->
  <div class="changeDialog">
    <el-dialog
      title="选择补卡规则"
      :visible.sync="openVisible"
      width="800px"
      @close="close"
      class="changeDialog"
    >
      <span class="ruleContent">
        <!-- <el-input v-model="key" placeholder="请输入班次名称"></el-input>
          <el-button type="primary" @click="handleShiftSerach">搜索</el-button> -->
        <span class="addRule"
          >若没有符合的规则，可以去<el-button
            @click="$router.push('/attendance/cardRule')"
            type="text"
            >新增规则</el-button
          ></span
        >
        <span class="search-box">
          <el-input
            v-model.trim="key"
            placeholder="请输入补卡规则名称"
            maxLength="30"
            :clearable="true"
            @change="handleSearch"
          ></el-input>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </span>
        <el-table
          :header-cell-style="{ background: '#F1F1F1' }"
          :cell-style="{ height: '94px' }"
          :data="shiftList"
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column width="180">
            <template slot-scope="scope">
              <el-radio
                :label="scope.row.id"
                v-model="templateRadio"
                @change="getTemplateRow(scope.$index, scope.row)"
                >{{ scope.row.date }}</el-radio
              >
            </template>
          </el-table-column>
          <el-table-column
            label="补卡规则名称"
            prop="ruleName"
            min-width="100"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <p>
                {{ scope.row.ruleName }}
                <el-button v-if="scope.row.isDefault" type="text"
                  >默认</el-button
                >
              </p>
            </template>
          </el-table-column>
          <el-table-column label="规则内容" prop="number" min-width="70">
            <template slot-scope="scope">
              <p>{{ scope.row.allowSupplement ? "允许补卡" : "不允许补卡" }}</p>
              <div v-if="scope.row.allowSupplement">
                <p v-show="scope.row.allowTimesLimit">
                  每月可提交{{ scope.row.timesLimit }}次补卡
                </p>
                <p v-show="!scope.row.allowTimesLimit">补卡次数不限制</p>
                <p v-show="!scope.row.allowDateLimit">补卡时间不限制</p>
                <p v-show="scope.row.allowDateLimit">
                  可申请过去{{ scope.row.dateLimit }}天内的补卡
                </p>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currPage"
            :page-size="pageSize"
            layout="prev, pager, next, sizes, jumper"
            :total="total"
            background
          >
          </el-pagination>
        </div>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: ["defaultRule"],
  data() {
    return {
      openVisible: false,
      currPage: 1,
      pageSize: 10,
      total: 0,
      key: "",
      shiftList: [],
      currChooseRule: "", //当前选中补卡规则
      templateRadio: this.defaultRule.id,
    };
  },
  watch: {
    defaultRule(val) {
      this.templateRadio = val.id;
    },
  },
  created() {},
  methods: {
    openCardRule() {
      this.openVisible = true;
      this.getRuleList();
    },
    //获取规则列表
    getRuleList(type) {
      let params = {
        currPage: type === "search" ? 1 : this.currPage,
        pageSize: this.pageSize,
        key: this.key,
      };
      this.$attApi.apiPostGetSupplementRuleList(params).then((res) => {
        this.shiftList = res.data.records;
        this.total = res.data.total;
      });
    },
    //获取选中班次
    getTemplateRow(index, row) {
      if (row) {
        this.currChooseRule = row;
      }
    },
    handleSearch() {
      this.getRuleList("search");
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getRuleList();
    },
    handleCurrentChange(val) {
      this.currPage = val;
      this.getRuleList();
    },
    close() {
      this.templateRadio = this.defaultRule.id;
      this.key = "";
      this.currChooseRule = "";
    },
    cancel() {
      this.openVisible = false;
      this.templateRadio = this.defaultRule.id;
      this.key = "";
      this.currChooseRule = "";
    },
    confirm() {
      this.currChooseRule = this.currChooseRule
        ? this.currChooseRule
        : this.defaultRule;
      this.$emit("changeDefaultRule", this.currChooseRule);
      this.openVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.ruleContent {
  /deep/ .el-table {
    height: 350px;
    overflow-y: auto;
  }
  .el-table::before {
    display: none;
  }
  .search-box {
    display: flex;
    float: right;
    /deep/ .el-input {
      width: 300px;
    }
  }
  .pagination {
    padding: 20px 22px 20px 0;
    text-align: right;
  }
}
</style>
