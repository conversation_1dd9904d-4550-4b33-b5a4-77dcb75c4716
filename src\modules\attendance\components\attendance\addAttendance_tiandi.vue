<template>
  <div class="add-attendance">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span class="back-style" @click="goBack">返回</span>
          <span class="header-line">|</span>
          <span>{{ $route.query.id ? "编辑考勤组" : "新增考勤组" }}</span>
        </el-col>
      </el-row>
    </header>
    <div class="page-mian">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        class="demo-form-inline"
        label-width="110px"
      >
        <el-form-item label="考勤组名称" prop="agName">
          <div class="attendance-name">
            <el-input
              v-model="form.agName"
              placeholder="请输入考勤组名称"
            ></el-input>
            <span class="hint-info" style="width: 350px; margin-left: 20px"
              >最多30个字符(中英文或数字)</span
            >
          </div>
        </el-form-item>
        <h3 style="">基本信息</h3>
        <el-form-item label="参与考勤人员" :required="true">
          <el-button type="primary" @click="addClick('NEED_ATTEND')">
            + 添加
          </el-button>
          <el-button
            v-for="item in currRightNeedData"
            :key="item.id"
            style="margin: 5px 5px 0 0"
          >
            {{ item.name }}
          </el-button>
        </el-form-item>
        <!-- <el-form-item label="无需考勤人员">
          <el-button type="primary" @click="addClick('NO_ATTEND')"
            >+ 添加</el-button
          >
          <el-button v-for="item in currRightNoData" :key="item.id">{{
            item.name
          }}</el-button>
        </el-form-item> -->
        <el-form-item label="考勤组负责人">
          <el-button type="primary" @click="addClick('ATTEND_ADMIN')">
            + 添加
          </el-button>
          <el-button
            v-for="item in currRightAdminData"
            :key="item.id"
            style="margin: 5px 5px 0 0"
          >
            {{ item.name }}
          </el-button>
          <span class="hint-info" style="width: 350px; margin-left: 20px"
            >可以在手机端查看考勤组打卡统计信息</span
          >
        </el-form-item>
        <el-form-item label="考勤组时区">
          <el-select
            v-model="form.timeZone"
            placeholder="请选择"
            :popper-append-to-body="false"
          >
            <el-option label="中国标准时间" value="2021"></el-option>
          </el-select>
          <span class="hint-info" style="width: 350px; margin-left: 20px"
            >该时区当前时间 {{ currentDate }}</span
          >
        </el-form-item>
        <h3 style="">考勤组类型</h3>
        <el-form-item label="考勤组类型" :required="true">
          <div>
            <div>
              <el-radio
                v-model="form.attendType"
                label="FIX"
                :disabled="isDisabled"
              >
                固定班制：所有人按相同时间打卡
              </el-radio>
              <span class="hint-info"
                >适用于：IT、金融、文化传媒、政府/事业单位、教育培训等行业</span
              >
            </div>
            <div>
              <el-radio
                v-model="form.attendType"
                label="SCHEDULING"
                :disabled="isDisabled"
              >
                排班制：不同人员按照各自的排班打卡
              </el-radio>
              <span class="hint-info"
                >适用于：餐饮、制造、物流贸易、客户服务、医院等行业</span
              >
            </div>
            <div>
              <el-radio
                v-model="form.attendType"
                label="FREE"
                :disabled="isDisabled"
              >
                自由班制：所有人无时间限制，随时打卡，只计算旷工
              </el-radio>
              <span class="hint-info"
                >适用于：班次没有规律、装修、家政、物流等计算工作时长的行业</span
              >
            </div>
          </div>
        </el-form-item>
        <el-form-item
          v-show="form.attendType === 'FIX'"
          label="工作日设置"
          :required="true"
        >
          <div v-if="total.length !== 0">
            <span>快捷设置班次</span>
            <span style="padding: 0 10px">{{ currChooseShift.groupName }}</span>
            <span>
              {{ currChooseShift.attTime[0] }}
              {{ currChooseShift.attTime[1] }}
              {{ currChooseShift.attTime[2] }}
            </span>
            <el-button
              type="text"
              @click="
                changeFlight(
                  currChooseShift.index,
                  currChooseShift.workingShiftId
                )
              "
            >
              更改班次
            </el-button>
            <el-table
              ref="multipleTable"
              :data="tableData"
              tooltip-effect="dark"
              style="width: 800px"
              border
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column label="工作日" width="120">
                <template slot-scope="scope">{{ scope.row.date }}</template>
              </el-table-column>
              <el-table-column prop="shift" label="班次" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span v-if="multipleSelection.indexOf(scope.row) !== -1">
                    {{ scope.row.groupName }}
                    {{ scope.row.attTime[0] }}
                    {{ scope.row.attTime[1] }}
                    {{ scope.row.attTime[2] }}
                  </span>
                  <span v-else>休息</span>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="操作" width="200">
                <template
                  v-if="multipleSelection.indexOf(scope.row) !== -1"
                  slot-scope="scope"
                >
                  <el-button
                    type="text"
                    @click="viewShiftDetail(scope.row.workingShiftId)"
                  >
                    详情
                  </el-button>
                  <el-button
                    type="text"
                    @click="
                      changeFlight(scope.row.index, scope.row.workingShiftId)
                    "
                  >
                    更改班次
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-button
            v-else
            type="text"
            @click="$router.push('/attendance/addShift')"
          >
            创建班次
          </el-button>
        </el-form-item>
        <el-form-item v-show="form.attendType === 'FREE'" label="工作日设置">
          <div>
            <el-checkbox-group v-model="checkedDays">
              <el-checkbox v-for="val in days" :key="val" :label="val">
                {{ val }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <el-form :model="form1" label-width="110px">
          <el-form-item v-if="form.attendType === 'FREE'" label="打卡时段设置">
            <el-time-picker
              v-model="form1.beginSignTime"
              format="HH:mm"
              value-format="HH:mm:ss"
              :picker-options="{
                selectableRange: `'00:00:00' - ${form1.endSignTime}`,
              }"
              :editable="false"
              :clearable="false"
              @change="handleSelect"
            >
            </el-time-picker>
            -
            <el-time-picker
              v-model="form1.endSignTime"
              format="HH:mm"
              value-format="HH:mm:ss"
              :picker-options="{
                selectableRange: `${form1.beginSignTime} - '23:59:00'`,
              }"
              :editable="false"
              :clearable="false"
              @change="handleSelect"
            >
            </el-time-picker>
          </el-form-item>
        </el-form>

        <el-form-item v-if="form.attendType === 'FREE'" label="缺卡设置">
          <div>
            <el-checkbox v-model="form.allowedMissingAttend">
              工作日不打卡计为缺卡
            </el-checkbox>
          </div>
        </el-form-item>
        <el-form-item
          v-if="form.attendType !== 'SCHEDULING'"
          label="自动排休设置"
        >
          <div class="rest-setting">
            <el-checkbox v-model="form.allowedHolidayCalender" label="1">
              按照
            </el-checkbox>
            <el-select
              v-model="form.holidayCalender"
              placeholder="请选择"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in optionTime"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <span>自动排休</span>
            <!-- <el-button type="text" @click="viewCalendar"
              >查看排休日历</el-button
            > -->
          </div>
        </el-form-item>

        <el-form-item
          v-if="attendanceType !== '2'"
          label="打卡方式:"
          :required="true"
        >
          <!-- 位置打卡 -->

          <el-checkbox v-model="form.enablePlace">位置打卡</el-checkbox>
        </el-form-item>
        <el-form-item
          v-if="attendanceType !== '2' && form.enablePlace"
          style="marginleft: 65px"
          label="位置"
          :required="form.attendType === 'FREE' ? false : true"
        >
          <div v-for="(val, index) in addressList" :key="index">
            <span
              ><i class="el-icon-location"></i
              >{{ val.placeAlias ? val.placeAlias : val.placeName }}</span
            >
            <el-select
              v-model="addressRange[index]"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="(val, index) in locationRanges"
                :key="index"
                :value="val.value"
                :label="val.label"
              ></el-option>
            </el-select>
            <el-button type="text" @click="addressView(val, index)">
              查看
            </el-button>
            <el-button type="text" @click="deleteView(index)">删除</el-button>
          </div>
          <div>
            <el-button type="primary" @click="handleMapSdk">+ 添加</el-button>
            <span class="hint-info" style="padding: 0 32px 0 20px"
              >可添加多个打卡地点，并设置有效打卡时间范围</span
            >
          </div>
        </el-form-item>
        <!-- 考勤机打卡 -->
        <el-form-item v-if="cgb">
          <div class="field-clock">
            <el-checkbox
              v-if="isShowFacePunchCard"
              v-model="form.enableFaceOcr"
            >
              考勤机人脸识别打卡
            </el-checkbox>

            <div v-show="form.enableFaceOcr" class="outsideCard">
              <!-- <span style="line-height: 20px">考勤照片真人验证</span> -->
              <div>
                <el-checkbox v-model="form.verifyFaceOcr">
                  员工首次上传考勤机人脸照片，开启真人验证
                </el-checkbox>
                <p class="hint-info-temperature hint-info">
                  开启验证后，员工需要进行身份证照片和人脸视频认证
                </p>
              </div>
              <!-- <span style="line-height: 20px; margin-top: 10px"
                >考勤体温监测</span
              > -->
              <el-checkbox
                v-model="form.notifyHighTemperature"
                style="width: 150px"
              >
                开启异常体温提醒
              </el-checkbox>
              <span class="hint-info-temperature"
                >当员工体温达到
                <el-select
                  v-model="temperature.int"
                  class="input-temperature"
                  placeholder=""
                  :disabled="!form.notifyHighTemperature"
                >
                  <el-option
                    v-for="item in temperatureInt"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option
                ></el-select>
                .
                <el-select
                  v-model="temperature.dec"
                  class="input-temperature"
                  placeholder=""
                  :disabled="!form.notifyHighTemperature"
                >
                  <el-option
                    v-for="item in temperatureDec"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>

                ℃及以上时，通知考勤组负责人</span
              >
            </div>
          </div>
        </el-form-item>

        <h3>其它设置</h3>
        <el-form-item label="外勤打卡">
          <div class="field-clock">
            <el-checkbox
              v-model="form.allowedOutside"
              :disabled="
                !form.enablePlace ||
                (form.attendType === 'FREE' && !addressList.length
                  ? true
                  : false)
              "
            >
              选中后，范围外允许打卡
            </el-checkbox>
            <span style="color: #888"> 启用外勤打卡，需先开启位置打卡</span>
            <p
              v-if="form.attendType === 'FREE' && !addressList.length"
              class="hint-info"
            >
              自由班制打卡地址未添加时，无需外勤打卡
            </p>
            <div
              v-show="
                form.allowedOutside ||
                (form.attendType === 'FREE' && addressList.length)
              "
              class="outsideCard"
            >
              <!-- <el-checkbox v-model="form.outsideApproval"
                >外勤打卡需审批</el-checkbox
              > -->
              <el-checkbox v-model="form.outsideRemark">
                外勤打卡需添加备注
              </el-checkbox>
              <el-checkbox v-model="form.outsideImages">
                外勤打卡需拍照
              </el-checkbox>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="加班">
          <div class="overtime">
            <span style="padding-right: 10px">{{
              defaultOvertime.ruleName
            }}</span>
            <el-button type="text" @click="changeOvertime">更改</el-button>

            <div
              v-for="val in defaultOvertime.overtimeRuleDetailResultList"
              :key="val.id"
              class="child"
            >
              <p v-if="val.ruleType === 'WORKDAY'">
                工作日：{{
                  val.allowedOvertime
                    ? "加班必须审批，加班时长以审批时长为准"
                    : "不允许加班"
                }}
              </p>
              <p v-if="val.ruleType === 'RESTDAY'">
                休息日：{{
                  val.allowedOvertime
                    ? "加班必须审批，加班时长以审批时长为准"
                    : "不允许加班"
                }}
              </p>
              <p v-if="val.ruleType === 'HOLIDAY'">
                节假日：{{
                  val.allowedOvertime
                    ? "加班必须审批，加班时长以审批时长为准"
                    : "不允许加班"
                }}
              </p>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="补卡">
          <div class="overtime">
            <span style="padding-right: 10px">{{
              defaultRule.allowSupplement ? "允许补卡" : "不允许补卡"
            }}</span>
            <span style="padding-right: 10px">
              {{ defaultRule.ruleName }}
            </span>
            <el-button type="text" @click="changeCardRule">更改</el-button>
            <div v-show="defaultRule.allowSupplement" class="child">
              <p v-if="defaultRule.allowTimesLimit">
                每月可提交{{ defaultRule.timesLimit }}次补卡
              </p>
              <p v-else>补卡次数不限制</p>
            </div>
            <div v-show="defaultRule.allowSupplement" class="child">
              <p v-if="defaultRule.allowDateLimit">
                可申请过去{{ defaultRule.dateLimit }}天内的补卡
              </p>
              <p v-else>补卡时间不限制</p>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="默认审批申请">
          <div class="overtime">
            <span style="padding-right: 10px">默认申请单</span>
            <el-button type="text" @click="handleArr">更改</el-button>
            <div class="child">
              <p>请假、出差、外出、补卡申请、加班、换班</p>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="mian-footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="saveBefore">保存</el-button>
    </div>
    <MapTianDiTu 
     ref="mapSdk"
      :viewMapDetail="viewMapDetail"
      @getAddressList="getAddressList"
    />
    <!-- 添加考勤人员 -->
    <div class="addPerson">
      <el-dialog
        title="选择参与考勤人员"
        :visible.sync="openNeedAttend"
        width="600px"
        @close="closeDialog"
      >
        <span class="dialogContent">
          <div v-loading="loading" class="left">
            <el-input
              v-model="keyNeed"
              autocomplete="off"
              placeholder="部门名称/成员姓名"
            ></el-input>
            <i class="el-icon-search"></i>
            <el-tree
              ref="treeNeed"
              :data="data"
              show-checkbox
              node-key="id"
              :filter-node-method="filterNode"
              :default-expand-all="true"
              :default-expanded-keys="[0, 1, 2, 3]"
              :default-checked-keys="currLeftNeedData"
              :props="{
                label: 'name',
              }"
              @check="clickNeedCheck"
            >
              <span slot-scope="{ data }">
                <!-- <el-tooltip class="item" effect="dark" :content="data.name" placement="top-start"> -->
                <span class="show-ellipsis">{{ data.name }}</span>
                <!-- </el-tooltip> -->
              </span>
            </el-tree>
          </div>
          <i class="divider"></i>
          <ul class="right">
            <li v-for="(item, index) in rightNeedList" :key="item.id">
              <span>{{ item.name }}</span>
              <i
                class="el-icon-close"
                @click="handleDelete('NEED_ATTEND', item, index)"
              ></i>
            </li>
          </ul>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelPerson('NEED_ATTEND')">取 消</el-button>
          <el-button type="primary" @click="confirmPerson('NEED_ATTEND')"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </div>
    <!-- 添加无需考勤人员 -->
    <!-- <div class="addPerson">
      <el-dialog
        title="选择无需打卡人员"
        :visible.sync="openNoAttend"
        width="600px"
      >
        <span class="dialogContent">
          <div class="left">
            <el-input
              v-model="keyNo"
              autocomplete="off"
              placeholder="部门名称/成员姓名"
            ></el-input>
            <i class="el-icon-search" @click="handleSearch('NO_ATTEND')"></i>
            <el-tree
              ref="treeNo"
              :data="data"
              show-checkbox
              node-key="id"
              :filter-node-method="filterNode"
              :default-expand-all="true"
              :default-expanded-keys="[0, 1, 2, 3]"
              :default-checked-keys="currLeftNoData"
              :props="{
                label: 'name'
              }"
              @check="clickNoCheck"
            >
            </el-tree>
          </div>
          <i class="divider"></i>
          <ul class="right">
            <li v-for="(item, index) in rightNoList" :key="item.id">
              <span>{{ item.name }}</span>
              <i
                class="el-icon-close"
                @click="handleDelete('NO_ATTEND', item.id, index)"
              ></i>
            </li>
          </ul>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelPerson('NO_ATTEND')">取 消</el-button>
          <el-button type="primary" @click="confirmPerson('NO_ATTEND')"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </div> -->
    <!-- 添加考勤负责人 -->
    <div class="addPerson">
      <el-dialog
        title="添加考勤负责人"
        :visible.sync="openAdminAttend"
        width="600px"
        @close="closeDialog"
      >
        <span class="dialogContent">
          <div v-loading="loading" class="left">
            <el-input
              v-model="keyAdmin"
              placeholder="部门名称/成员姓名"
              autocomplete="off"
            ></el-input>
            <i class="el-icon-search"></i>
            <el-tree
              ref="treeAdmin"
              :data="data"
              show-checkbox
              node-key="id"
              :filter-node-method="filterNode"
              :default-expand-all="true"
              :default-expanded-keys="[0, 1, 2, 3]"
              :default-checked-keys="currLeftAdminData"
              :props="{
                label: 'name',
              }"
              @check="clickAdminCheck"
            >
              <span slot-scope="{ data }" class="custom-tree-node">
                <span :title="data.name" class="show-ellipsis">{{
                  data.name
                }}</span>
              </span>
            </el-tree>
          </div>
          <i class="divider"></i>
          <ul class="right">
            <li v-for="(item, index) in rightAdminList" :key="item.id">
              <span>{{ item.name }}</span>
              <i
                class="el-icon-close"
                @click="handleDelete('ATTEND_ADMIN', item, index)"
              ></i>
            </li>
          </ul>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelPerson('ATTEND_ADMIN')">取 消</el-button>
          <el-button type="primary" @click="confirmPerson('ATTEND_ADMIN')"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </div>
    <!-- 更改班次  -->
    <div class="changeDialog">
      <el-dialog
        title="选择班次"
        :visible.sync="changeVisible"
        width="800px"
        class="changeDialog"
      >
        <span class="dialogContent">
          <el-input
            v-model.trim="key"
            placeholder="请输入班次名称"
            :clearable="true"
            @change="handleShiftSerach"
          ></el-input>
          <el-button type="primary" @click="handleShiftSerach">搜索</el-button>
          <el-table
            :header-cell-style="{ background: '#F1F1F1' }"
            :data="shiftList"
            style="width: 100%; margin-top: 20px"
          >
            <el-table-column width="180">
              <template slot-scope="scope">
                <el-radio
                  v-model="templateRadio"
                  :label="scope.row.id"
                  @change="getTemplateRow(scope.$index, scope.row)"
                  >{{ scope.row.date }}</el-radio
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="groupName"
              label="班次名称"
              width="180"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column prop="attTime" label="考勤时间">
              <template slot-scope="scope">
                <span>{{ scope.row.attTime[0] }}</span>
                <span>{{ scope.row.attTime[1] }}</span>
                <span>{{ scope.row.attTime[2] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
              :current-page="currPage"
              :page-size="pageSize"
              layout="prev, pager, next, sizes, jumper"
              :total="total"
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="changeVisible = false">取 消</el-button>
          <el-button type="primary" @click="shiftConfirm(shiftIndex)"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </div>
    <!-- 查看排休日历  -->
    <div class="changeDialog">
      <el-dialog
        title="排休日历"
        :visible.sync="calendarVisible"
        width="800px"
        class="changeDialog"
      >
        <span class="dialogContent">
          <el-calendar v-model="currentMonth"> </el-calendar>
        </span>
      </el-dialog>
    </div>
    <!-- 加班规则 -->
    <Overtime
      ref="overtime"
      :defaultRule="defaultOvertime"
      @changeOvertimeRule="changeOvertimeRule"
    ></Overtime>
    <!-- 补卡规则 -->
    <CardRule
      ref="cardRule"
      :defaultRule="defaultRule"
      @changeDefaultRule="changeDefaultRule"
    ></CardRule>
    <!-- 查看班次详情 -->
    <ShiftDetail ref="shiftDetail" :currId="shiftDetailId"></ShiftDetail>
    <!-- 人员或班次生效时间弹窗 -->
    <div class="saveChoose">
      <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
        <span>请选择本次人员或班次变动的生效时间</span>
        <span slot="footer">
          <!-- <el-button @click="tomorrow">明日生效</el-button> -->
          <el-button type="primary" @click="tomorrow">明日生效</el-button>
          <!-- <el-button type="primary" @click="immediate">立即生效</el-button> -->
        </span>
      </el-dialog>
    </div>
    <div class="dialogApprover">
      <el-dialog :visible.sync="dialogApprover" width="600px">
        <div
          slot="title"
          class="header-title"
          style="display: flex; font-size: 15px"
        >
          <span style="margin-right: 150px"> 默认申请单 </span>
          <span>若需变更流程，则需到审批流程中去设置</span>
        </div>
        <div class="diaApproval">
          <div class="diaApproval-container" style="padding-left: 82px">
            <span class="left-side">
              <span style="font-size: 18px">申请：</span>
              <el-switch
                v-model="approvalSwitchCopy"
                active-color="#4F71FF"
                inactive-color="#ff4949"
                onselectstart="return false"
                @change="handleSelectApp"
              >
              </el-switch>
            </span>
            <span
              v-if="(!useOldWorkFlow && showRightSide) || useOldWorkFlow"
              class="right-side"
            >
              <span class="hover-pointer" @click="handleAdd">
                <i class="el-icon-circle-plus-outline"></i>
                <span class="refresh">新增审批</span>
              </span>
            </span>
          </div>
          <el-checkbox
            v-model="approvalResultListCopy[0].isEnable"
            :disabled="leaveList && leaveList.length !== 0 ? false : true"
            @change="changeLick"
          >
            <div class="approvalList">
              <span>默认请假审批单：</span>
              <el-select
                v-model="approvalResultListCopy[0].processId"
                :placeholder="
                  leaveList && leaveList.length !== 0
                    ? '请选择'
                    : '您没有已创建的请假审批单'
                "
                style="width: 300px"
                :disabled="leaveList && leaveList.length !== 0 ? false : true"
              >
                <el-option
                  v-for="item in leaveList"
                  :key="item.processId"
                  :label="item.processName"
                  :value="item.processId"
                >
                </el-option>
              </el-select>
            </div>
          </el-checkbox>
          <el-checkbox
            v-model="approvalResultListCopy[1].isEnable"
            :disabled="businessList && businessList.length !== 0 ? false : true"
            @change="changeLick"
          >
            <div class="approvalList">
              <span>默认出差审批单：</span>
              <el-select
                v-model="approvalResultListCopy[1].processId"
                :placeholder="
                  businessList && businessList.length !== 0
                    ? '请选择'
                    : '您没有已创建的出差审批单'
                "
                style="width: 300px"
                :disabled="
                  businessList && businessList.length !== 0 ? false : true
                "
              >
                <el-option
                  v-for="item in businessList"
                  :key="item.processId"
                  :label="item.processName"
                  :value="item.processId"
                >
                </el-option>
              </el-select>
            </div>
          </el-checkbox>
          <el-checkbox
            v-model="approvalResultListCopy[2].isEnable"
            :disabled="legworkList && legworkList.length !== 0 ? false : true"
            @change="changeLick"
          >
            <div class="approvalList">
              <span>默认外出审批单：</span>
              <el-select
                v-model="approvalResultListCopy[2].processId"
                :placeholder="
                  legworkList && legworkList.length !== 0
                    ? '请选择'
                    : '您没有已创建的外出审批单'
                "
                style="width: 300px"
                :disabled="
                  legworkList && legworkList.length !== 0 ? false : true
                "
              >
                <el-option
                  v-for="item in legworkList"
                  :key="item.processId"
                  :label="item.processName"
                  :value="item.processId"
                >
                </el-option>
              </el-select>
            </div>
          </el-checkbox>
          <el-checkbox
            v-model="approvalResultListCopy[3].isEnable"
            :disabled="fixList && fixList.length !== 0 ? false : true"
            @change="changeLick"
          >
            <div class="approvalList">
              <span>默认补卡审批单：</span>
              <el-select
                v-model="approvalResultListCopy[3].processId"
                :placeholder="
                  fixList && fixList.length !== 0
                    ? '请选择'
                    : '您没有已创建的补卡审批单'
                "
                style="width: 300px"
                :disabled="fixList && fixList.length !== 0 ? false : true"
              >
                <el-option
                  v-for="item in fixList"
                  :key="item.processId"
                  :label="item.processName"
                  :value="item.processId"
                >
                </el-option>
              </el-select>
            </div>
          </el-checkbox>
          <el-checkbox
            v-model="approvalResultListCopy[4].isEnable"
            :disabled="overtimeList && overtimeList.length !== 0 ? false : true"
            @change="changeLick"
          >
            <div class="approvalList">
              <span>默认加班审批单：</span>
              <el-select
                v-model="approvalResultListCopy[4].processId"
                :placeholder="
                  overtimeList && overtimeList.length !== 0
                    ? '请选择'
                    : '您没有已创建的补加班审批单'
                "
                style="width: 300px"
                :disabled="
                  overtimeList && overtimeList.length !== 0 ? false : true
                "
              >
                <el-option
                  v-for="item in overtimeList"
                  :key="item.processId"
                  :label="item.processName"
                  :value="item.processId"
                >
                </el-option>
              </el-select>
            </div>
          </el-checkbox>
          <div style="margin-top: 20px">
            温馨提示：设置完成后，在手机端打打卡界面，点击申请，即可选取对应的假勤审批单发起审批,申请不勾选时，手机端申请按钮不展示
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogApprover = false">取 消</el-button>
          <el-button type="primary" @click="handleApproval">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import MapTianDiTu from "./mapTianDiTu";
import ShiftDetail from "./shiftDetail";
import CardRule from "./addAttcomponents/changeCardRule";
import Overtime from "./addAttcomponents/changeOvertime";
import { checkName } from "../../util/validate";
import { debounce } from "../../util/debounce";
import { apiUserPrivilege } from "@/store/api";
import { mapState } from "vuex";
const environmentConfig = window.env.environmentConfig;
export default {
  components: {
    MapTianDiTu,
    ShiftDetail,
    CardRule,
    Overtime,
  },
  data() {
    return {
      cgb: window.env.server_env === "cgb",
      samePersonArr: [], //同一考勤人员 - 在多个部门id
      sameAdminArr: [], //考勤负责人 - id
      loading: false,
      overtimeName: "", //加班规则名称
      effectNow: true, //考勤组生效时间
      editApprovalList: [], //回显审批关联-用于比较是否发生变动
      editTableData: [], //回显班次对象集合-用于比较是否发生变动
      editNeedkeys: [], //回显考勤人员id集合-用于比较是否发生变动
      currentDate: new Date().toLocaleDateString(), //当前时区时间
      viewMapDetail: "", //查看打卡地点
      defaultRule: "", //默认补卡规则
      defaultOvertime: "", //默认加班规则
      viewDetail: false, //查看班次详情
      shiftDetailId: "", //班次详情id
      currChooseShift: {
        //快捷设置选中班次
        groupName: "",
        attTime: [],
        index: 0,
      },
      shiftIndex: "", //当前更改工作日id
      templateRadio: "", //当前选中班次id
      templateSelection: "", //当前选中班次node
      addressList: [], //打卡地点列表
      addressRange: [], //位置打卡范围列表
      currPage: 1,
      pageSize: 10,
      total: 0,
      shiftList: [], //班次列表
      key: "", //人员查询关键字
      keyNeed: "", //考勤人员搜索key
      keyNo: "", //无需考勤key
      keyAdmin: "", //考勤组负责人key
      form: {
        supplementRuleId: "", //补卡规则id
        overtimeRuleId: "", //加班规则id
        agName: "", //考勤组名称
        allowedOutside: false, //允许外勤打卡

        enablePlace: window.env.server_env === "cgb" ? false : true, //是否启用位置打卡
        enableFaceOcr: false, //是否启用考勤机打卡
        notifyHighTemperature: false, //是否开启异常体温提醒
        minTemperature: null, //常体温提醒设置的最低报警温度
        verifyFaceOcr: false, //员工首次上传考勤机人脸照片，是否开启真人验证

        outsideApproval: false, //允许外勤审批
        outsideImages: false, //允许外勤拍照
        outsideRemark: false, //允许外勤备注
        timeZone: "2021", //考勤组时区
        attendType: "FIX", //考勤组类型
        allowedHolidayCalender: true, //是否自动排休设置
        holidayCalender: "china", //自动排休日历按照
        allowedMissingAttend: false, //缺卡设置
      },

      form1: {
        beginSignTime: "00:00:00", //打卡时段开始时间
        endSignTime: "23:59:00", //打卡时段结束时间
      },
      editForm1: {},
      editHolidayCalender: false,
      currLeftNeedData: [], //当前左侧已选考勤
      currLeftNoData: [], //当前左侧已选无需考勤
      currLeftAdminData: [], //当前左侧已选考勤负责人
      isDisabled: false, //是否禁用考勤组类型
      attendanceType: "1",
      mapVisible: false,
      currentMonth: new Date(),
      calendarVisible: false,
      currentPage: 4,
      addTitle: "",
      openNeedAttend: false, //考勤人员弹框
      openNoAttend: false, //无需考勤人员弹框
      openAdminAttend: false, //考勤组负责人弹框
      changeTitle: "",
      changeVisible: false,
      currentTime: "",
      input: "",
      isEdit: this.$route.query.id ? true : false, //是否编辑状态
      rightNeedList: [], //考勤右侧数据
      rightNoList: [], //无需考勤右侧数据
      rightAdminList: [], //考勤负责人右侧数据
      currRightNeedData: [], //当前已选考勤数据
      currRightNoData: [], //当前已选无需考勤数据
      currRightAdminData: [], //当前已选考勤负责人数据
      freeSetShiftId: "", //自由班次 - shiftOrder为0的 班次id
      days: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
      checkedDays: ["周一", "周二", "周三", "周四", "周五"],
      value1: new Date(2016, 9, 10, 18, 40),
      value2: new Date(2016, 9, 10, 18, 40),
      options: [
        {
          value: "选项1",
          label: "中国标准时间",
        },
      ],
      optionTime: [
        {
          value: "china",
          label: "中国大陆法定节假日",
        },
        // {
        //   value: "Japan",
        //   label: "日本法定节假日"
        // }
      ],
      value: "",
      radio: "",
      tableData: [
        {
          date: "周一",
          index: 1,
          groupName: "",
          attTime: [],
        },
        {
          date: "周二",
          index: 2,
          groupName: "",
          attTime: [],
        },
        {
          date: "周三",
          index: 3,
          groupName: "",
          attTime: [],
        },
        {
          date: "周四",
          index: 4,
          groupName: "",
          attTime: [],
        },
        {
          date: "周五",
          index: 5,
          groupName: "",
          attTime: [],
        },
        {
          date: "周六",
          index: 6,
          groupName: "",
          attTime: [],
        },
        {
          date: "周日",
          index: 7,
          groupName: "",
          attTime: [],
        },
      ],
      locationRanges: [
        { value: 100, label: "100米" },
        { value: 200, label: "200米" },
        { value: 300, label: "300米" },
        { value: 400, label: "400米" },
        { value: 500, label: "500米" },
        { value: 600, label: "600米" },
        { value: 700, label: "700米" },
        { value: 800, label: "800米" },
        { value: 900, label: "900米" },
        { value: 1000, label: "1000米" },
        { value: 2000, label: "2000米" },
        { value: 3000, label: "3000米" },
      ],
      multipleSelection: [],
      checked: true,
      isClockAgain: true,
      dialogVisible: false, //人员或班次生效弹框
      deleteIndex: "", //需要删除的位置
      deleteAddress: "",
      data: [], //考勤人员
      dialogApprover: false,
      leaveList: [], //请假列表
      businessList: [], //出差列表
      legworkList: [], //外出列表
      overtimeList: [], //加班列表
      fixList: [], //补卡列表
      approvalSwitch: true,
      approvalSwitchCopy: true,
      approvalResultListCopy: [
        {
          // attendId: 0,
          bizSuitType: "ASK_FOR_LEAVE",
          coId: 0,
          // formId: 0,
          id: "",
          isEnable: false,
          processId: "",
          processName: "",
        },
        {
          // attendId: 0,
          bizSuitType: "BUSINESS_TRIP",
          coId: 0,
          // formId: 0,
          id: "",
          isEnable: false,
          processId: "",
          processName: "",
        },
        {
          // attendId: 0,
          bizSuitType: "LEGWORK",
          coId: 0,
          // formId: 0,
          id: "",
          isEnable: false,
          processId: "",
          processName: "",
        },
        {
          // attendId: 0,
          bizSuitType: "FIX_ATTENDANCE",
          coId: 0,
          // formId: 0,
          id: "",
          isEnable: false,
          processId: "",
          processName: "",
        },
        {
          // attendId: 0,
          bizSuitType: "OVERTIME",
          coId: 0,
          // formId: 0,
          id: "",
          isEnable: false,
          processId: "",
          processName: "",
        },
      ],
      approvalResultList: [
        {
          // attendId: 0,
          bizSuitType: "ASK_FOR_LEAVE",
          coId: 0,
          // formId: 0,
          id: "",
          isEnable: false,
          processId: "",
          processName: "",
        },
        {
          // attendId: 0,
          bizSuitType: "BUSINESS_TRIP",
          coId: 0,
          // formId: 0,
          id: "",
          isEnable: false,
          processId: "",
          processName: "",
        },
        {
          // attendId: 0,
          bizSuitType: "LEGWORK",
          coId: 0,
          // formId: 0,
          id: "",
          isEnable: false,
          processId: "",
          processName: "",
        },
        {
          // attendId: 0,
          bizSuitType: "FIX_ATTENDANCE",
          coId: 0,
          // formId: 0,
          id: "",
          isEnable: false,
          processId: "",
          processName: "",
        },
        {
          // attendId: 0,
          bizSuitType: "OVERTIME",
          coId: 0,
          // formId: 0,
          id: "",
          isEnable: false,
          processId: "",
          processName: "",
        },
      ],
      rules: {
        agName: [
          {
            required: true,
            validator: checkName,
            trigger: "blur",
          },
        ],

        beginSignTime: [
          {
            required: false,
            message: "请选择生效日期",
            trigger: "change",
          },
        ],
        endSignTime: [
          {
            required: false,
            message: "请选择生效日期",
            trigger: "change",
          },
        ],
      },
      temperatureInt: Array.from({ length: 5 }, (_, i) => 36 + i),
      temperatureDec: Array.from({ length: 10 }, (_, i) => i),
      temperature: {
        int: 37,
        dec: 2,
      },
      showRightSide: false,
      approvalLinkUrl: "" /**新建 跳转到新建单据url */,
      linkUrl: "" /**新建 跳转到旧审批 */,
      useOldWorkFlow: false /*是否使用老的审批流*/,
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
    // 根据权限点判断是否显示考勤机人脸识别打卡
    isShowFacePunchCard() {
      return (
        this.cgb &&
        this.privilegeVoList.includes(
          "hrAttend.attendManage.dailyCount.getTempOfDayByEmpIds"
        )
      );
    },
  },
  watch: {
    keyNeed(val) {
      this.$refs.treeNeed.filter(val);
    },
    keyNo(val) {
      this.$refs.treeNo.filter(val);
    },
    keyAdmin(val) {
      this.$refs.treeAdmin.filter(val);
    },
    addressList(val) {
      for (let index in val) {
        this.addressRange[index] = this.addressRange[index]
          ? this.addressRange[index]
          : 100;
      }
      if (!val.length) {
        this.form.allowedOutside = false;
        this.form.outsideRemark = false;
        this.form.outsideImages = false;
      }

      if (this.form.attendType === "FREE" && !this.$route.query.id) {
        this.form.allowedOutside = val.length ? true : false;
      }
    },
    "form.attendType"(val) {
      if (this.form.attendType === "FREE" && !this.$route.query.id) {
        this.form.allowedOutside = this.addressList.length ? true : false;
      }
    },
    //是否开启位置打卡
    "form.enablePlace"(val) {
      // this.form.allowedOutside = val;
      if (!val) {
        this.form.allowedOutside = false;
        this.form.outsideRemark = false;
        this.form.outsideImages = false;
        this.addressList = [];
      }
    },
  },

  created() {
    this.getIfUseWorkFlow().finally(() => {
      this.getShiftList();
      // if (!this.$route.query.id) {
      //   for (let i = 0; i < this.approvalResultListCopy.length; i++) {
      //     this.approvalResultListCopy[i].isEnable = true;
      //   }
      // }
      this.getApprover();
      if (!this.isEdit) this.handleEchoCache();
      this.approvalLinkUrl = environmentConfig.approval
        ? environmentConfig.approval
        : `http://${window.location.host}`;
      this.linkUrl = environmentConfig.approve
        ? environmentConfig.approve
        : `http://${window.location.host}`;
      this.checkPermission();
    });
  },

  methods: {
    /**
     *
     * @param {接口传入的列表} list
     */
    handleLeftSideEffect(list) {
      const ids = list.map(({ id }) => id);
      this.approvalResultListCopy.forEach((e) => {
        if (!ids.includes(e.id)) e.isEnable = false;
      });
    },
    fixElementBug() {
      this.$nextTick(() => {
        this.$el.querySelectorAll(".approvalList").forEach((elem) => {
          elem.querySelector(".el-input__suffix-inner").style.pointerEvents =
            "none";
        });
      });
    },
    async getIfUseWorkFlow() {
      const res = await this.$attApi.apiGlobalConfigFetch();
      this.useOldWorkFlow =
        res.data.getGlobaleConfig.useOldWorkflow != null &&
        res.data.getGlobaleConfig.useOldWorkflow == "true";
    },
    handleAdd() {
      if (this.useOldWorkFlow)
        return window.open(
          `${this.linkUrl}/formList?app=WORKFLOW&token=${this.$getToken()}`,
          "_blank"
        );
      window.open(
        `${
          this.approvalLinkUrl
        }/receipt?app=AUTOAPPROVAL&from_app=HRATTEND&token=${this.$getToken()}`,
        "_blank"
      );
    },
    async checkPermission() {
      try {
        let ret = await apiUserPrivilege();
        if (ret.success) {
          this.privilege = ret.data.privilegeGroupTreeVO;
          this.showRightSide = this.privilege.children.find(({ children }) => {
            return (
              !!children &&
              !!children.find(
                ({ businessCode }) =>
                  businessCode == "autoApproval.formManagement"
              )
            );
          });
        }
      } catch (err) {
        console.warn("拉取权限错误", err);
      }
    },
    handleEchoCache() {
      this.form = {
        ...this.form,
        ...this.$store.state.cacheAttendance,
      };
      if (this.form.minTemperature) {
        const arr = String(this.form.minTemperature).split(".");
        this.temperature = {
          int: Number(arr[0]),
          dec: Number(arr[1]) || 0,
        };
      }
    },

    changeLick() {
      if (
        this.approvalResultListCopy.every((item) => {
          return item.isEnable == false;
        })
      ) {
        this.approvalSwitchCopy = false;
      } else {
        this.approvalSwitchCopy = true;
      }
    },
    handleSelectApp() {
      for (let i = 0; i < this.approvalResultListCopy.length; i++) {
        switch (i) {
          case 0:
            if (this.leaveList && this.leaveList.length !== 0) {
              if (this.approvalSwitchCopy == false) {
                this.approvalResultListCopy[i].isEnable = false;
              } else {
                this.approvalResultListCopy[i].isEnable = true;
              }
            }
            break;
          case 1:
            if (this.businessList && this.businessList.length !== 0) {
              if (this.approvalSwitchCopy == false) {
                this.approvalResultListCopy[i].isEnable = false;
              } else {
                this.approvalResultListCopy[i].isEnable = true;
              }
            }
            break;
          case 2:
            if (this.legworkList && this.legworkList.length !== 0) {
              if (this.approvalSwitchCopy == false) {
                this.approvalResultListCopy[i].isEnable = false;
              } else {
                this.approvalResultListCopy[i].isEnable = true;
              }
            }
            break;
          case 3:
            if (this.fixList && this.fixList.length !== 0) {
              if (this.approvalSwitchCopy == false) {
                this.approvalResultListCopy[i].isEnable = false;
              } else {
                this.approvalResultListCopy[i].isEnable = true;
              }
            }
            break;
          case 4:
            if (this.overtimeList && this.overtimeList.length !== 0) {
              if (this.approvalSwitchCopy == false) {
                this.approvalResultListCopy[i].isEnable = false;
              } else {
                this.approvalResultListCopy[i].isEnable = true;
              }
            }
            break;
        }
      }
    },
    handleList(list, index) {
      if (list && list.length == 0) {
        this.approvalResultListCopy[index].isEnable = false;
      } else {
        this.approvalResultListCopy[index].isEnable = true;
      }
      this.approvalResultListCopy[index].processName = list[0]
        ? list[0].processName
        : "";
      if (this.useOldWorkFlow)
        this.approvalResultListCopy[index].processId = list[0]
          ? list[0].processId
          : "";
      this.approvalResultListCopy[index].coId = list[0] ? list[0].coId : "";
      if (!this.useOldWorkFlow)
        this.approvalResultListCopy[index].defaultProcess = list[0]
          ? list[0].defaultProcess
          : "";
      if (!this.editOrAdd())
        this.approvalResultListCopy[index].processId = list[0]
          ? list[0].defaultProcess
          : "";
    },
    editOrAdd() {
      return !!this.$route.query.id;
    },
    transDefaultProcessToProcessId(approvalResultList = []) {
      if (this.useOldWorkFlow) return approvalResultList;
      return approvalResultList.map((item) => ({
        ...item,
        processId: item.defaultProcess,
      }));
    },
    transProcessIdToDefaultProcess(approvalResultList = []) {
      if (this.useOldWorkFlow) return approvalResultList;
      return approvalResultList.map((item) => ({
        ...item,
        defaultProcess: item.processId,
      }));
    },
    async getApprover() {
      const cb = (res) => {
        for (let i = 0; i < res.data.length; i++) {
          if (res.data[i] && res.data[i].approvalResultList.length) {
            res.data[i].approvalResultList =
              this.transDefaultProcessToProcessId(
                res.data[i].approvalResultList
              );
            switch (res.data[i].bizSuitType) {
              case "ASK_FOR_LEAVE":
                this.leaveList = res.data[i].approvalResultList;
                this.handleList(this.leaveList, 0);
                break;
              case "BUSINESS_TRIP":
                this.businessList = res.data[i].approvalResultList;
                this.handleList(this.businessList, 1);
                break;
              case "LEGWORK":
                this.legworkList = res.data[i].approvalResultList;
                this.handleList(this.legworkList, 2);
                break;
              case "FIX_ATTENDANCE":
                this.fixList = res.data[i].approvalResultList;
                this.handleList(this.fixList, 3);
                break;
              case "OVERTIME":
                this.overtimeList = res.data[i].approvalResultList;
                this.handleList(this.overtimeList, 4);
                break;
              default:
                break;
            }
          }
        }
        if (
          !this.leaveList.length &&
          !this.businessList.length &&
          !this.legworkList.length &&
          !this.fixList.length &&
          !this.overtimeList.length
        ) {
          this.approvalSwitchCopy = false;
        }
        if (this.$route.query.id) {
          this.isDisabled = true;
          this.getAttendWorkList();
        } else {
          this.getDefaultRule();
          this.getDefaultShift();
          this.getDefaultOvertimeRule();
        }
      };
      let res;
      if (this.useOldWorkFlow) {
        res = await this.$attApi.apiQueryApprovalProcess();
      } else {
        res = await this.$attApi.apiQueryApprovalProcessNew();
      }
      cb(res);
    },
    handleArr() {
      this.dialogApprover = true;
      this.fixElementBug();
    },
    handleApproval() {
      this.approvalResultListCopy = this.transProcessIdToDefaultProcess(
        this.approvalResultListCopy
      );
      this.approvalSwitch = JSON.parse(JSON.stringify(this.approvalSwitchCopy));
      this.approvalResultList = JSON.parse(
        JSON.stringify(this.approvalResultListCopy)
      );
      this.dialogApprover = false;
    },
    handleApp(item) {
      for (let i = 0; i < this.approvalResultListCopy.length; i++) {
        if (item.bizSuitType == this.approvalResultListCopy[i].bizSuitType) {
          this.approvalResultListCopy[i] = item;
        }
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      if (data.name) {
        return data.name.indexOf(value) !== -1;
      } else {
        return false;
      }
    },
    handleSwitch(list, index) {
      if (!list.length) {
        this.approvalResultListCopy[index].isEnable = false;
      }
    },
    //编辑-获取考勤组详情
    async getAttendWorkList() {
      const res = await this.$attApi.apiGetQueryAttendGroup({
        id: this.$route.query.id,
      });
      if (res.success) {
        const DATA = res.data;
        let shiftChoose = [];
        this.approvalSwitchCopy = DATA.approvalSwitch;

        if (DATA.approvalResultList && DATA.approvalResultList.length > 0) {
          DATA.approvalResultList = this.transDefaultProcessToProcessId(
            DATA.approvalResultList
          );
          for (let i = 0; i < DATA.approvalResultList.length; i++) {
            if (DATA.approvalResultList[i]) {
              switch (DATA.approvalResultList[i].bizSuitType) {
                case "ASK_FOR_LEAVE":
                  this.approvalResultListCopy[0].processId =
                    DATA.approvalResultList[i].processId;
                  this.approvalResultListCopy[0].coId =
                    DATA.approvalResultList[i].coId;
                  this.approvalResultListCopy[0].id =
                    DATA.approvalResultList[i].id;
                  this.approvalResultListCopy[0].isEnable =
                    DATA.approvalResultList[i].isEnable;
                  break;
                case "BUSINESS_TRIP":
                  this.approvalResultListCopy[1].processId =
                    DATA.approvalResultList[i].processId;
                  this.approvalResultListCopy[1].coId =
                    DATA.approvalResultList[i].coId;
                  this.approvalResultListCopy[1].id =
                    DATA.approvalResultList[i].id;
                  this.approvalResultListCopy[1].isEnable =
                    DATA.approvalResultList[i].isEnable;
                  break;
                case "LEGWORK":
                  this.approvalResultListCopy[2].processName =
                    DATA.approvalResultList[i].processName;
                  this.approvalResultListCopy[2].processId =
                    DATA.approvalResultList[i].processId;
                  this.approvalResultListCopy[2].coId =
                    DATA.approvalResultList[i].coId;
                  this.approvalResultListCopy[2].id =
                    DATA.approvalResultList[i].id;
                  this.approvalResultListCopy[2].isEnable =
                    DATA.approvalResultList[i].isEnable;
                  break;
                case "FIX_ATTENDANCE":
                  this.approvalResultListCopy[3].processId =
                    DATA.approvalResultList[i].processId;
                  this.approvalResultListCopy[3].coId =
                    DATA.approvalResultList[i].coId;
                  this.approvalResultListCopy[3].id =
                    DATA.approvalResultList[i].id;
                  this.approvalResultListCopy[3].isEnable =
                    DATA.approvalResultList[i].isEnable;
                  break;
                case "OVERTIME":
                  this.approvalResultListCopy[4].processId =
                    DATA.approvalResultList[i].processId;
                  this.approvalResultListCopy[4].coId =
                    DATA.approvalResultList[i].coId;
                  this.approvalResultListCopy[4].id =
                    DATA.approvalResultList[i].id;
                  this.approvalResultListCopy[4].isEnable =
                    DATA.approvalResultList[i].isEnable;
                  break;
                default:
                  break;
              }
            }
          }
          //用于比较-编辑关联审批表单变动
          this.editApprovalList = DATA.approvalResultList;
        }
        this.handleSelectApp();
        if (!this.useOldWorkFlow)
          this.handleLeftSideEffect(DATA.approvalResultList);
        if (
          !this.leaveList.length &&
          !this.businessList.length &&
          !this.legworkList.length &&
          !this.fixList.length &&
          !this.overtimeList.length
        ) {
          this.approvalSwitchCopy = false;
        }
        this.handleSwitch(this.leaveList, 0);
        this.handleSwitch(this.businessList, 1);
        this.handleSwitch(this.legworkList, 2);
        this.handleSwitch(this.fixList, 3);
        this.handleSwitch(this.overtimeList, 4);
        //基本信息回显
        this.form = {
          supplementRuleId: DATA.supplementRuleId,
          overtimeRuleId: DATA.overtimeRuleId,
          agName: DATA.agName,
          allowedOutside: DATA.allowedOutside,

          enablePlace: DATA.enablePlace,
          enableFaceOcr: DATA.enableFaceOcr,
          notifyHighTemperature: DATA.notifyHighTemperature,
          verifyFaceOcr: DATA.verifyFaceOcr,
          minTemperature: DATA.minTemperature,
          outsideApproval: DATA.outsideApproval,
          outsideImages: DATA.outsideImages,
          outsideRemark: DATA.outsideRemark,
          timeZone: String(DATA.timeZone),
          attendType: DATA.attendType,
          allowedHolidayCalender: DATA.allowedHolidayCalender,
          holidayCalender: DATA.holidayCalender,
          allowedMissingAttend: DATA.allowedMissingAttend,
        };

        if (DATA.minTemperature) {
          const arr = String(DATA.minTemperature).split(".");
          this.temperature = {
            int: Number(arr[0]),
            dec: Number(arr[1]) || 0,
          };
        }

        this.effectNow = DATA.effectNow;
        //补卡规则回显
        this.defaultRule = DATA.supplementRuleResult;
        //加班规则回显
        this.defaultOvertime = DATA.overtimeRuleResult;
        //位置打卡回显
        this.addressList = DATA.attendPlaceResultList;
        this.addressList.forEach((val) => {
          this.addressRange.push(val.errorRange);
        });

        //人员集合回显
        Object.keys(DATA.relListMap).forEach((type) => {
          switch (type) {
            case "NEED_ATTEND":
              DATA.relListMap[type].forEach((val) => {
                val.name = val.targetName;
                val.parentId = val.deptId;
                val.id = val.targetId;
                this.currRightNeedData.push(val);
                this.rightNeedList.push(val);
                this.currLeftNeedData.push(val.deptId + "-" + val.targetId);
                this.editNeedkeys.push(val.targetId);
              });
              break;
            case "NO_ATTEND":
              DATA.relListMap[type].forEach((val) => {
                val.name = val.targetName;
                val.parentId = val.deptId;
                val.id = val.targetId;
                this.currRightNoData.push(val);
                this.rightNoList.push(val);
                this.currLeftNoData.push(val.targetId);
              });
              break;
            case "ATTEND_ADMIN":
              DATA.relListMap[type].forEach((val) => {
                val.name = val.targetName;
                val.parentId = val.deptId;
                val.id = val.targetId;
                this.currRightAdminData.push(val);
                this.rightAdminList.push(val);
                this.currLeftAdminData.push(val.deptId + "-" + val.targetId);
              });
              break;
          }
        });
        //工作日班次设置回显/ 0 -- 快捷设置班次； 1-7 -- 周一到周日；
        let editArr = [];
        DATA.attendWorkingShiftResultList.forEach((val) => {
          this.tableData.forEach((it) => {
            if (val.shiftOrder !== 0 && val.shiftOrder === it.index) {
              //非休息-记录当前对应班次信息
              it.id = val.id;
              if (!val.isRestDay && val.workingShiftResult) {
                it.groupName = val.workingShiftResult.groupName;
                val.workingShiftResult.workingShiftDetailResultList.forEach(
                  (el, index) => {
                    if (el.workingBegin && el.workingEnd) {
                      it.attTime[index] =
                        this.insertStr(el.workingBegin) +
                        "-" +
                        this.insertStr(el.workingEnd);
                    }
                  }
                );
                it.workingShiftId = val.workingShiftResult.id;
                it.id = val.id; // 主键id
                it.isRestDay = val.isRestDay;
                //休息日班次信息无
              } else if (val.isRestDay || val.workingShiftId === -1) {
                it.id = val.id;
                it.workingShiftId = this.currChooseShift.workingShiftId;
                it.groupName = this.currChooseShift.groupName;
                it.attTime = this.currChooseShift.attTime;
                it.isRestDay = val.isRestDay;
              }
              //非休息勾选
              if (!val.isRestDay) {
                shiftChoose.push(it); //是否休息-勾选
                this.$refs.multipleTable.toggleRowSelection(it, true);
              } else {
                this.$refs.multipleTable.toggleRowSelection(it, false);
              }
            } else if (val.shiftOrder === 0) {
              if (val.workingShiftResult) {
                this.currChooseShift.groupName =
                  val.workingShiftResult.groupName;
                val.workingShiftResult.workingShiftDetailResultList.forEach(
                  (el, index) => {
                    if (el.workingBegin && el.workingEnd) {
                      this.currChooseShift.attTime[index] =
                        this.insertStr(el.workingBegin) +
                        "-" +
                        this.insertStr(el.workingEnd);
                    }
                  }
                );
                this.currChooseShift.workingShiftId = val.workingShiftResult.id;
              }

              this.currChooseShift.id = val.id; //主键id
            }
          });

          //回显完整班次集合-用于比较班次编辑是否变动
          editArr.push({
            // id: val.id,
            shiftOrder: val.shiftOrder,
            workingShiftId: val.workingShiftId,
            isRestDay: val.isRestDay,
          });
          this.editTableData = editArr;
        });
        this.multipleSelection = shiftChoose;

        //自由班制回显工作日设置
        let freeArr = [];
        if (DATA.attendType === "FREE") {
          this.form1.beginSignTime =
            DATA.beginSignTime.slice(0, 2) +
            ":" +
            DATA.beginSignTime.slice(2) +
            ":00";
          this.form1.endSignTime =
            DATA.endSignTime.slice(0, 2) +
            ":" +
            DATA.endSignTime.slice(2) +
            ":00";
          DATA.attendWorkingShiftResultList.forEach((val) => {
            if (!val.isRestDay && val.shiftOrder) {
              freeArr.push(this.days[val.shiftOrder - 1]);
            }
            if (!val.shiftOrder) {
              this.freeSetShiftId = val.workingShiftId;
            }
          });

          //校验自由班制是否发生变动
          this.editForm1.beginSignTime = this.form1.beginSignTime;
          this.editForm1.endSignTime = this.form1.endSignTime;
          this.editHolidayCalender = DATA.allowedHolidayCalender;
          this.editCheckDays = freeArr;

          this.checkedDays = freeArr;
        }
      }
    },
    //获取默认补卡规则
    getDefaultRule() {
      this.$attApi.apiGetDefaultSupplementRule({}).then((res) => {
        if (res.success) {
          this.defaultRule = res.data;
          this.form.supplementRuleId = res.data.id;
        }
      });
    },
    //获取默认班次信息
    getDefaultShift() {
      this.$attApi.apiGetDefaultWorkingShift({}).then((res) => {
        if (res.success) {
          console.log(res, "rterteuyruetruetruetruetruet");
          const DATA = res.data;
          this.currChooseShift = {
            ...this.currChooseShift,
            groupName: DATA.groupName,
            workingShiftId: DATA.id,
          };
          DATA.workingShiftDetailResultList.forEach((its, index) => {
            if (its.workingBegin && its.workingEnd) {
              this.currChooseShift.attTime[index] =
                this.insertStr(its.workingBegin) +
                "-" +
                this.insertStr(its.workingEnd);
            }
          });
          this.tableData.forEach((val) => {
            val.groupName = this.currChooseShift.groupName;
            val.attTime = this.currChooseShift.attTime;
            val.workingShiftId = this.currChooseShift.workingShiftId;
            if (val.index <= 5) {
              this.$refs.multipleTable.toggleRowSelection(val, true);
            }
          });
        }
      });
    },
    //获取默认加班规则
    getDefaultOvertimeRule() {
      this.$attApi.getDefaultOvertimeRule().then((res) => {
        if (res.success) {
          this.defaultOvertime = res.data;
          this.form.overtimeRuleId = res.data.id;
        }
      });
    },
    //更改加班规则
    changeOvertime() {
      this.$refs.overtime.openOvertime();
    },
    //更改补卡规则
    changeCardRule() {
      this.$refs.cardRule.openCardRule();
    },
    //更改默认补卡规则回显
    changeDefaultRule(val) {
      this.defaultRule = val;
      this.form.supplementRuleId = val.id;
    },
    //更改默认加班规则回显
    changeOvertimeRule(val) {
      this.defaultOvertime = val;
      this.form.overtimeRuleId = val.id;
    },
    // 重新设置人员树 的key -id
    setPersonKey(data, type) {
      data.forEach((val) => {
        if (val.children && val.children.length) {
          this.setPersonKey(val.children);
        }
        if (!val.isDept) {
          val.id = val.parentId + "-" + val.id;
        }
      });
    },
    //获取考勤人员
    getPersonList(type) {
      let params = {
        id: this.$route.query.id ? this.$route.query.id : "",
        relType: type,
        key: this.key,
      };
      this.$attApi.apiPostOrganizationAndRel(params).then((res) => {
        this.loading = false;
        if (res.success) {
          this.data = res.data;
          if (this.data && this.data.length) {
            this.setPersonKey(this.data);
          }
          switch (type) {
            case "NEED_ATTEND":
              let arrNeed = [],
                isSame = [];
              setTimeout(() => {
                this.currLeftNeedData.forEach((key) => {
                  this.getCheckPerson(this.data, key, "choose", "need");
                  isSame = arrNeed.filter((val) => {
                    return val.id.split("-")[1] === key.split("-")[1];
                  });
                  if (!isSame.length) {
                    arrNeed.push(this.$refs.treeNeed.getNode(key).data);
                  }
                });
                this.$refs.treeNeed.setCheckedKeys(this.samePersonArr);
                this.rightNeedList = arrNeed;
              }, 0);

              console.log(this.data);
              break;

            case "NO_ATTEND":
              let arrNo = [];
              this.currLeftNoData.forEach((key) => {
                arrNo.push(this.$refs.treeNo.getNode(key).data);
              });
              this.rightNoList = arrNo;
              break;
            case "ATTEND_ADMIN":
              let arrAdmin = [],
                isAdminSame = [];
              setTimeout(() => {
                this.currLeftAdminData.forEach((key) => {
                  this.getCheckPerson(this.data, key, "choose", "admin");
                  isAdminSame = arrAdmin.filter((val) => {
                    return val.id.split("-")[1] === key.split("-")[1];
                  });
                  if (!isAdminSame.length) {
                    arrAdmin.push(this.$refs.treeAdmin.getNode(key).data);
                  }
                });
                this.$refs.treeAdmin.setCheckedKeys(this.sameAdminArr);
                this.rightAdminList = arrAdmin;
              }, 0);
              break;
          }
        }
      });
    },
    //班次列表
    getShiftList(type) {
      let params = {
        currPage: type === "search" ? 1 : this.currPage,
        pageSize: this.pageSize,
        key: this.key,
      };
      this.$attApi.apiPostAttendWorkingList(params).then((res) => {
        if (res.success) {
          const DATA = res.data;
          this.total = DATA.total;
          this.shiftList = DATA.records;
          this.shiftList.forEach((val) => {
            val.attTime = [];
            val.workingTimeListVoList.forEach((its, index) => {
              if (its.workingBegin && its.workingEnd) {
                val.attTime[index] =
                  this.insertStr(its.workingBegin) +
                  "-" +
                  this.insertStr(its.workingEnd);
              }
            });
          });
        }
      });
    },
    //拼接时间范围显示
    insertStr(str) {
      return str.slice(0, 2) + ":" + str.slice(2);
    },
    //已被勾选工作日集合
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSelect() {
      if (this.form1.beginSignTime === this.form1.endSignTime) {
        this.$message.error("打卡时段设置不能同一时间");
        return false;
      }
      return true;
    },

    //遍历树 - 同一人员多个部门
    getCheckPerson(data, targetId, type, arr) {
      data.map((val) => {
        if (val.children && val.children.length) {
          this.getCheckPerson(val.children, targetId, type, arr);
        }
        switch (type) {
          case "choose":
            if (
              !val.isDept &&
              targetId.split("-")[1] === val.id.split("-")[1] &&
              arr.indexOf(val.id) == -1 &&
              !val.isDept
            ) {
              if (arr == "need") {
                this.samePersonArr.push(val.id);
              } else {
                this.sameAdminArr.push(val.id);
              }
            }
            break;
          case "delete":
            if (arr == "need") {
              this.samePersonArr.forEach((item) => {
                if (targetId.split("-")[1] === item.split("-")[1]) {
                  this.samePersonArr = this.samePersonArr.filter((it) => {
                    return it.split("-")[1] !== item.split("-")[1];
                  });
                }
              });
            } else {
              this.sameAdminArr.forEach((item) => {
                if (targetId.split("-")[1] === item.split("-")[1]) {
                  this.sameAdminArr = this.sameAdminArr.filter((it) => {
                    return it.split("-")[1] !== item.split("-")[1];
                  });
                }
              });
            }
        }
      });
    },

    getChildren(data, type) {
      data.map((val) => {
        if (val.isDept && val.children && val.children.length) {
          this.getChildren(val.children, type);
        } else {
          if (type == "need") {
            this.samePersonArr = this.samePersonArr.filter((item) => {
              return val.id.split("-")[1] !== item.split("-")[1];
            });
            this.rightNeedList = this.rightNeedList.filter((item) => {
              return val.id.split("-")[1] !== item.id.split("-")[1];
            });
          } else {
            this.sameAdminArr = this.sameAdminArr.filter((item) => {
              return val.id.split("-")[1] !== item.split("-")[1];
            });
            this.rightAdminList = this.rightAdminList.filter((item) => {
              return val.id.split("-")[1] !== item.id.split("-")[1];
            });
          }
        }
      });
    },

    //选择考勤
    clickNeedCheck(val, checks) {
      let ischeck = checks.checkedKeys.indexOf(val.id) == -1 ? false : true;
      let checkNodes = this.$refs.treeNeed.getCheckedNodes(true);
      if (ischeck || (!ischeck && val.isDept)) {
        this.samePersonArr = [];
        let arr = [],
          newArr = [],
          rightArr = [];
        arr = checkNodes.filter((val) => {
          return !val.isDept && !val.children;
        });
        arr.forEach((item) => {
          if (newArr.indexOf(item.name) == -1) {
            newArr.push(item.name);
            rightArr.push(item);
          }
        });
        this.rightNeedList = rightArr;
        checkNodes.map((item) => {
          this.getCheckPerson(this.data, item.id, "choose", "need");
        });
        this.$refs.treeNeed.setCheckedKeys(this.samePersonArr);
        if (!ischeck && val.isDept && val.children.length) {
          this.getChildren(val.children, "need");
          this.$refs.treeNeed.setCheckedKeys(this.samePersonArr);
        }
      } else if (!ischeck && !val.isDept) {
        this.samePersonArr = this.samePersonArr.filter((item) => {
          return val.id.split("-")[1] !== item.split("-")[1];
        });
        this.rightNeedList = this.rightNeedList.filter((item) => {
          return val.id.split("-")[1] !== item.id.split("-")[1];
        });
        this.$refs.treeNeed.setCheckedKeys(this.samePersonArr);
      }

      if (ischeck && val.isOtherUsing) {
        let showArr = this.rightNeedList.filter((val) => {
          return val.isOtherUsing;
        });
        if (showArr.length !== 0) {
          let nameArr = [];
          showArr.forEach((val) => {
            nameArr.push(val.name);
          });
          this.$confirm(
            `人员：${nameArr}已在其他的考勤组，创建完成后，将被移至本考勤组?`,
            "提示",
            {
              confirmButtonText: "知道了",
              showClose: false,
              showCancelButton: false,
              type: "warning",
              closeOnClickModal: false,
              closeOnPressEscape: false,
            }
          );
        }
      }
    },

    //选择无需考勤
    clickNoCheck() {
      let checkNodes = this.$refs.treeNo.getCheckedNodes(true);
      this.rightNoList = checkNodes.filter((val) => {
        return !val.children;
      });
    },
    //选择考勤负责人
    clickAdminCheck(val, checks) {
      let ischeck = checks.checkedKeys.indexOf(val.id) == -1 ? false : true;
      let checkNodes = this.$refs.treeAdmin.getCheckedNodes(true);
      if (ischeck || (!ischeck && val.isDept)) {
        this.sameAdminArr = [];
        let arr = [],
          newArr = [],
          rightArr = [];
        arr = checkNodes.filter((val) => {
          return !val.isDept && !val.children;
        });
        arr.forEach((item) => {
          if (newArr.indexOf(item.name) == -1) {
            newArr.push(item.name);
            rightArr.push(item);
          }
        });
        this.rightAdminList = rightArr;
        checkNodes.map((item) => {
          this.getCheckPerson(this.data, item.id, "choose", "admin");
        });
        this.$refs.treeAdmin.setCheckedKeys(this.sameAdminArr);
        if (!ischeck && val.isDept && val.children.length) {
          this.getChildren(val.children);
          this.$refs.treeAdmin.setCheckedKeys(this.sameAdminArr, "admin");
        }
      } else if (!ischeck && !val.isDept) {
        this.sameAdminArr = this.sameAdminArr.filter((item) => {
          return val.id.split("-")[1] !== item.split("-")[1];
        });
        this.rightAdminList = this.rightAdminList.filter((item) => {
          return val.id.split("-")[1] !== item.id.split("-")[1];
        });
        this.$refs.treeAdmin.setCheckedKeys(this.sameAdminArr);
      }
    },
    //获取打卡地点列表
    getAddressList(val) {
      this.addressList.push(val);
    },
    deleteView(index) {
      this.deleteIndex = index;
      this.deleteAddress = this.addressList[this.deleteIndex].placeAlias
        ? this.addressList[this.deleteIndex].placeAlias
        : this.addressList[this.deleteIndex].placeName;
      this.$confirm(`确认删除：${this.deleteAddress}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          this.addressDelete();
        })
        .catch(() => {});
    },
    //删除打卡地点
    addressDelete() {
      this.addressList.splice(this.deleteIndex, 1);
    },
    //更改班次
    changeFlight(index, id) {
      this.changeVisible = true;
      this.templateRadio = id;
      this.shiftIndex = index;
    },
    //获取选中班次
    getTemplateRow(index, row) {
      this.templateSelection = row;
    },
    //保存班次选择--- 0:快捷设置班次
    shiftConfirm(type) {
      this.changeVisible = false;
      if (this.templateSelection) {
        if (type === 0) {
          this.currChooseShift.groupName = this.templateSelection.groupName;
          this.currChooseShift.attTime = this.templateSelection.attTime;
          this.currChooseShift.workingShiftId = this.templateSelection.id;
          this.tableData.forEach((val) => {
            val.groupName = this.templateSelection.groupName;
            val.attTime = this.templateSelection.attTime;
            val.workingShiftId = this.templateSelection.id;
            if (val.index <= 5) {
              this.$refs.multipleTable.toggleRowSelection(val, true);
            }
          });
        } else {
          this.tableData.forEach((val) => {
            if (val.index === this.shiftIndex) {
              val.groupName = this.templateSelection.groupName;
              val.attTime = this.templateSelection.attTime;
              val.workingShiftId = this.templateSelection.id;
            }
          });
        }
        this.templateSelection = "";
      }
    },
    //查看班次详情
    viewShiftDetail(id) {
      this.shiftDetailId = id;
      this.$refs.shiftDetail.openViewDetail();
    },
    handleShiftSerach() {
      this.getShiftList("search");
    },
    //关闭弹窗清空记录
    closeDialog() {
      this.keyNeed = "";
      this.keyAdmin = "";
    },
    //添加考勤人员
    addClick(val) {
      switch (val) {
        case "NEED_ATTEND":
          this.samePersonArr = [];
          this.openNeedAttend = true;
          this.loading = true;
          this.getPersonList("NEED_ATTEND");
          break;
        case "NO_ATTEND":
          this.openNoAttend = true;
          this.getPersonList("NO_ATTEND");
          break;
        case "ATTEND_ADMIN":
          this.sameAdminArr = [];
          this.openAdminAttend = true;
          this.loading = true;
          this.getPersonList("ATTEND_ADMIN");
          break;
      }
    },
    //删除人员已选
    handleDelete(type, item, index) {
      switch (type) {
        case "NEED_ATTEND":
          this.rightNeedList.splice(index, 1);
          this.getCheckPerson(this.data, item.id, "delete", "need");
          this.$refs.treeNeed.setCheckedKeys(this.samePersonArr);
          break;
        case "NO_ATTEND":
          this.rightNoList.splice(index, 1);
          this.$refs.treeNo.setChecked(delId);
          break;
        case "ATTEND_ADMIN":
          this.rightAdminList.splice(index, 1);
          this.getCheckPerson(this.data, item.id, "delete", "admin");
          this.$refs.treeAdmin.setCheckedKeys(this.sameAdminArr);
          break;
      }
    },
    //确定人员选择
    confirmPerson(val) {
      switch (val) {
        case "NEED_ATTEND":
          this.currLeftNeedData = [];
          this.openNeedAttend = false;
          let checkNodes = this.$refs.treeNeed.getCheckedNodes(true);
          let currKeys = checkNodes.filter((val) => {
            return !val.children;
          });
          currKeys.forEach((val) => {
            this.currLeftNeedData.push(val.id);
          });
          this.currRightNeedData = this.rightNeedList;
          break;
        case "NO_ATTEND":
          this.openNoAttend = false;
          this.currLeftNoData = this.$refs.treeNo.getCheckedKeys(true);
          this.currRightNoData = this.rightNoList;
          break;
        case "ATTEND_ADMIN":
          this.openAdminAttend = false;
          this.currLeftAdminData = [];
          let checkAdminNodes = this.$refs.treeAdmin.getCheckedNodes(true);
          let currAdminKeys = checkAdminNodes.filter((val) => {
            return !val.children;
          });
          currAdminKeys.forEach((val) => {
            this.currLeftAdminData.push(val.id);
          });
          this.currRightAdminData = this.rightAdminList;
          break;
      }
    },
    //取消人员选择
    cancelPerson(val) {
      switch (val) {
        case "NEED_ATTEND":
          this.openNeedAttend = false;
          break;
        case "NO_ATTEND":
          this.openNoAttend = false;
          break;
        case "ATTEND_ADMIN":
          this.openAdminAttend = false;
          break;
      }
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getShiftList();
    },
    handleCurrentChange(val) {
      this.currPage = val;
      this.getShiftList();
    },
    //查看位置打卡地点
    addressView(val, index) {
      val.errorRange = this.addressRange[index];
      this.viewMapDetail = JSON.parse(JSON.stringify(val));
      this.$refs.mapSdk.open('view');
    },
    //查看排休日历
    viewCalendar() {
      this.calendarVisible = true;
    },
    handleMapSdk() {
      this.viewMapDetail = {
        placeAlias: "",
        placeName: "高碑店乡半壁店村惠河南街1111号龙源通惠大厦",
        longitude: "116.50448",
        latitude: "39.90308",
        errorRange: "100",
      };
      this.$refs.mapSdk.open();
    },
    //返回
    goBack() {
      this.$confirm("离开当前页面会丢失未保存的修改信息, 确定离开吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        this.$router.go(-1);
      });
    },
    //传参人员集合
    saveRelList() {
      let arrResult = [];
      if (!this.currRightNeedData.length)
        return this.$message.error("请选择需要考勤的人员或部门");

      this.currRightNeedData.forEach((val) => {
        arrResult.push({
          relType: "NEED_ATTEND",
          targetId:
            String(val.id).indexOf("-") == -1 ? val.id : val.id.split("-")[1],
          targetName: val.name,
          targetType: "EMPLOYEE",
          deptId: val.parentId,
          taxsubId: val.taxsubId,
        });
      });
      this.currRightNoData.forEach((val) => {
        arrResult.push({
          relType: "NO_ATTEND",
          targetId: val.id,
          targetName: val.name,
          targetType: "EMPLOYEE",
          deptId: val.parentId,
          taxsubId: val.taxsubId,
        });
      });
      this.currRightAdminData.forEach((val) => {
        arrResult.push({
          relType: "ATTEND_ADMIN",
          targetId:
            String(val.id).indexOf("-") == -1 ? val.id : val.id.split("-")[1],
          targetName: val.name,
          targetType: "EMPLOYEE",
          deptId: val.parentId,
          taxsubId: val.taxsubId,
        });
      });
      return arrResult;
    },
    //工作日班次集合
    saveShiftList() {
      let arrResult = [];
      this.tableData.forEach((val) => {
        arrResult.push({
          id: this.isEdit ? val.id : "",
          shiftOrder: val.index,
          workingShiftId:
            this.multipleSelection.indexOf(val) !== -1
              ? val.workingShiftId
              : -1,
          isRestDay: this.multipleSelection.indexOf(val) !== -1 ? false : true,
        });
      });
      return arrResult;
    },
    //比较工作日班次集合
    equalShiftList() {
      let arrResult = [];
      this.tableData.forEach((val) => {
        arrResult.push({
          // id: this.isEdit ? val.id : "",
          shiftOrder: val.index,
          workingShiftId:
            this.multipleSelection.indexOf(val) !== -1
              ? val.workingShiftId
              : -1,
          isRestDay: this.multipleSelection.indexOf(val) !== -1 ? false : true,
        });
      });
      return arrResult;
    },
    //位置打卡集合
    savePlaceList() {
      this.addressList.forEach((val, index) => {
        val.errorRange = this.addressRange[index];
      });
      return this.addressList;
    },
    //编辑-比较考勤组人员集合是否变动
    equalPerson() {
      if (this.currLeftNeedData.length == this.editNeedkeys.length) {
        let arr = [];
        this.currLeftNeedData.forEach((val) => {
          arr.push(val.split("-")[1]);
        });
        if (arr.sort().toString() == this.editNeedkeys.sort().toString()) {
          return true;
        }
      }
      return false;
    },
    //编辑-比较班次集合是否变动
    equalShift() {
      if (this.form.attendType !== "FIX") {
        return true;
      }
      let currShift = [
        {
          // id: this.isEdit ? this.currChooseShift.id : "",
          shiftOrder: 0,
          workingShiftId: this.isRestDay
            ? -1
            : this.currChooseShift.workingShiftId,
          isRestDay: false,
        },
        ...this.equalShiftList(),
      ];
      let isEqual = [],
        editShift = [];
      editShift = JSON.parse(JSON.stringify(this.editTableData));
      if (!editShift.length) {
        return false;
      }
      return this.equalObject(currShift, editShift);
    },
    //编辑-比较审批关联是否变动
    equalApproval() {
      let curr = [],
        edit = [];
      if (this.approvalResultList.length) {
        this.approvalResultList.map((val) => {
          if (val.isEnable) {
            curr.push({
              bizSuitType: val.bizSuitType,
              isEnable: val.isEnable,
              processId: val.processId,
              id: val.id,
            });
          }
        });
      }
      if (this.editApprovalList.length) {
        this.editApprovalList.map((val) => {
          edit.push({
            bizSuitType: val.bizSuitType,
            isEnable: val.isEnable,
            processId: val.processId,
            id: val.id,
          });
        });
      }
      if (edit.length === 0 && curr.length === 0) {
        return true;
      }
      if (!edit.length) {
        return false;
      }
      return this.equalObject(
        JSON.parse(JSON.stringify(curr)),
        JSON.parse(JSON.stringify(edit))
      );
    },
    equalObject(currData, editData) {
      for (let i = 0; i < currData.length; i++) {
        let curr = currData[i];
        let obj = editData[i];
        let propsCurr = Object.getOwnPropertyNames(currData[i]);
        let propsCompare = Object.getOwnPropertyNames(editData[i]);
        if (propsCurr.length != propsCompare.length) {
          return false;
        }
        for (let j = 0; j < propsCurr.length; j++) {
          let propName = propsCurr[j];
          if (curr[propName] !== obj[propName]) {
            return false;
          }
        }
      }
      return true;
    },
    //比较自由班制相关字段是否发生变化
    equalFree() {
      if (
        this.form1.beginSignTime !== this.editForm1.beginSignTime ||
        this.form1.endSignTime !== this.editForm1.endSignTime
      ) {
        return false;
      }
      if (this.form.allowedHolidayCalender !== this.editHolidayCalender) {
        return false;
      }
      if (this.checkedDays.length !== this.editCheckDays.length) {
        return false;
      } else if (this.checkedDays.length === this.editCheckDays.length) {
        let flag = true;
        this.checkedDays.forEach((val) => {
          if (this.editCheckDays.indexOf(val) === -1) {
            flag = false;
          }
        });
        return flag;
      }
      return true;
    },
    //保存弹框
    saveBefore() {
      // this.equalFree();

      this.$refs["form"].validate((valid) => {
        if (valid) {
          //必选打卡方式
          if (
            this.form.attendType !== "FREE" &&
            !this.form.enableFaceOcr &&
            !this.form.enablePlace
          )
            return this.$message.error("请选择打卡方式！");

          //开启了位置打卡必选打卡地点
          if (
            this.form.attendType !== "FREE" &&
            this.form.enablePlace &&
            !this.addressList.length
          )
            return this.$message.error("请添加打卡位置!");

          if (this.$route.query.id && this.form.attendType === "FIX") {
            this.dialogVisible =
              this.equalPerson() && this.equalShift() ? false : true;
            if (!this.dialogVisible) {
              this.save();
            }
          } else if (this.$route.query.id && this.form.attendType === "FREE") {
            this.dialogVisible =
              this.equalPerson() && this.equalFree() ? false : true;
            if (!this.dialogVisible) {
              this.save();
            }
          } else {
            if (this.form.attendType !== "SCHEDULING") {
              this.dialogVisible = true;
            } else {
              this.save();
            }
          }
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    //明日生效
    tomorrow() {
      this.save(1);
    },
    //立即生效
    immediate() {
      this.save(2);
    },
    //自由班制-处理班次集合传参
    dealFreeShift(data) {
      let rest = [];
      this.days.forEach((val, index) => {
        if (this.checkedDays.indexOf(val) === -1) {
          rest.push(index + 1);
        }
      });
      data.forEach((val) => {
        if (rest.indexOf(val.shiftOrder) !== -1) {
          val.workingShiftId = -1;
          val.isRestDay = true;
        } else if (rest.indexOf(val.shiftOrder) === -1 && val.isRestDay) {
          val.workingShiftId = data[0].workingShiftId;
          val.isRestDay = false;
        }
      });
      return data;
    },
    filterProcessIdOrDefaultProcessByUseOldWorlFlow(approvalResultList = []) {
      try {
        return approvalResultList.reduce((v, t) => {
          this.useOldWorkFlow ? delete t.defaultProcess : delete t.processId;
          v.push(t);
          return v;
        }, []);
      } catch (err) {
        return approvalResultList;
      }
    },
    //保存页面
    save: debounce(
      function (type) {
        this.approvalResultList = JSON.parse(
          JSON.stringify(this.approvalResultListCopy)
        );
        this.approvalSwitch = JSON.parse(
          JSON.stringify(this.approvalSwitchCopy)
        );
        for (let i = this.approvalResultList.length - 1; i >= 0; i--) {
          if (
            this.approvalResultList[i].isEnable == false &&
            this.approvalResultList[i].processId == ""
          ) {
            this.approvalResultList.splice(i, 1);
          }
        }
        if (!this.approvalSwitch) {
          this.approvalResultList = [];
        }

        //是否开启了考勤机打卡
        if (this.form.enableFaceOcr) {
          if (this.form.notifyHighTemperature) {
            this.form.minTemperature = Number(
              this.temperature.int + "." + this.temperature.dec
            );
          }
        }
        let params = {
          effectNow: type ? (type == 1 ? false : true) : this.effectNow,
          id: this.isEdit ? this.$route.query.id : "",
          ...this.form,

          beginSignTime:
            this.form1.beginSignTime.split(":")[0] +
            this.form1.beginSignTime.split(":")[1],
          endSignTime:
            this.form1.endSignTime.split(":")[0] +
            this.form1.endSignTime.split(":")[1],
          approvalResultList:
            this.filterProcessIdOrDefaultProcessByUseOldWorlFlow(
              this.approvalResultList
            ),
          approvalSwitch: this.approvalSwitch,
          //人员集合
          attendRelReqList: this.saveRelList(),
          //班次集合
          attendWorkingShiftReqList:
            this.form.attendType !== "SCHEDULING"
              ? [
                  {
                    id: this.isEdit ? this.currChooseShift.id : "",
                    shiftOrder: 0,
                    workingShiftId: this.isRestDay
                      ? -1
                      : this.form.attendType == "FREE" && this.isEdit
                      ? this.freeSetShiftId
                      : this.currChooseShift.workingShiftId,
                    isRestDay: false,
                  },
                  ...this.saveShiftList(),
                ]
              : [],
          //位置打卡集合
          attendPlaceReqList: this.savePlaceList(),
        };
        if (this.form.attendType === "FREE") {
          params.attendWorkingShiftReqList = this.dealFreeShift(
            params.attendWorkingShiftReqList
          );
          if (!this.handleSelect()) {
            return;
          }
        }
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.$attApi.apiPostSavaOrUpdateAttendGroup(params).then((res) => {
              if (res.success) {
                this.$message({
                  type: "success",
                  message: "保存成功!",
                });

                const { notifyHighTemperature, minTemperature, verifyFaceOcr } =
                  this.form;
                this.$store.commit("CACHEATTEND", {
                  notifyHighTemperature,
                  minTemperature,
                  verifyFaceOcr,
                });

                this.$router.push("/attendance/workAttendance");
              }
            });
          } else {
            this.$nextTick(() => {
              this.errorScroll(
                document.querySelectorAll("div.el-form-item__error")
              );
            });
          }
        });
      },
      3000,
      true
    ),
    //取消页面
    cancel() {
      this.$router.push("/attendance/workAttendance");
    },
  },
};
</script>

<style lang="scss" scoped>
.add-attendance {
  overflow: hidden;
  height: 100%;
  width: 100%;
  .header {
    border-bottom: 1px solid #ededed;
  }
  .page-mian {
    height: calc(100vh - 220px);
    overflow: auto;
    padding: 20px 20px 0 20px;
    box-sizing: border-box;
    .hint-info {
      color: #888888;
    }
    .hint-info-temperature {
      line-height: 20px;
      margin-left: 25px;
    }
    .input-temperature {
      width: 68px;
      height: 32px;
    }
  }
  .el-table::before {
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
  }
  .el-table--group::after,
  .el-table--border::after,
  .el-table::before {
    display: none;
  }
  .attendance-name {
    width: 500px;
    display: flex;
  }
  .mian-model {
    width: 98%;
    background-color: white;
    margin-top: 10px;
    padding-top: 5px;
    padding-bottom: 10px;
  }
  .model-title {
    font-size: 20px;
    border-bottom: 1px solid #ededed;
    margin-bottom: 10px;
  }
  .info-peo {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
  }
  .add-bn {
    margin-left: 80px;
  }
  .Time-mian {
    margin-left: 80px;
  }
  .Time-info {
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .type-check {
    display: flex;
    border-bottom: 1px solid #ededed;
  }
  .title-shift {
    display: flex;
    justify-content: space-between;
  }
  .dialogContent {
    .divider {
      width: 1px;
      height: 68%;
      background: #dddddd;
      position: absolute;
      left: 50%;
      top: 16%;
    }
    .el-input {
      width: 240px;
    }
    .el-icon-search {
      position: relative;
      right: 30px;
      color: #909399;
    }
    .left {
      .show-ellipsis {
        display: block;
        width: 110px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .right {
      float: right;
      width: 250px;
      height: 270px;
      overflow-y: auto;
      overflow-x: hidden;
      li {
        position: relative;
        height: 30px;
        line-height: 30px;
        background: #d9eafc;
        padding: 0 20px 0 5px;
        margin-bottom: 5px;
        width: 210px;
        white-space: nowrap;
        overflow-x: hidden;
        text-overflow: ellipsis;
        .el-icon-close {
          position: absolute;
          right: 5px;
          top: 8px;
          color: #909399;
          cursor: pointer;
        }
      }
    }
  }

  .addPerson {
    /deep/ .el-dialog__body {
      height: 244px;
      padding: 10px 20px 30px;
    }
    /deep/ .el-dialog {
      height: 400px;
      .el-tree {
        width: 280px;
        height: 230px;
        overflow: auto;
      }
    }
  }
  /deep/ .dialog-footer {
    position: absolute;
    text-align: right;
    display: flex;
    flex-direction: row;
    bottom: 10px;
    right: 20px;
  }
  .changeDialog {
    /deep/ .el-dialog {
      height: 600px;
    }
    /deep/ .el-table {
      overflow: auto;
      height: 320px;
    }
  }
  .pagination {
    padding: 22px 0 0 22px;
    // position: absolute;
    text-align: right;
    bottom: 40px;
    right: 0;
  }
  /deep/ .el-calendar-table .el-calendar-day {
    height: 64px;
  }
  .field-clock {
    .outsideCard {
      display: flex;
      margin-left: 20px;
      flex-direction: column;
    }
  }
  .overtime {
    .child {
      padding-left: 20px;
    }
  }

  .mian-footer {
    position: fixed;
    bottom: 0;
    width: calc(100% - 223px);
    height: 35px;
    padding: 20px 0 20px 0px;
    border-top: 1px solid #e5e5e5;
    background: #fff;
    text-align: center;
    z-index: 99;
    .btn {
      margin: 0 auto;
    }
  }
  .saveChoose /deep/ .el-dialog__body {
    padding: 20px 22px 40px;
  }
  .dialogApprover {
    /deep/.el-dialog {
      height: 540px;
    }
    .diaApproval {
      width: 80%;
      height: 380px;
      margin: 0 auto;
      .diaApproval-container {
        display: flex;
        justify-content: space-between;
        padding-right: 10px;
        .right-side {
          color: #4f71ff;
        }
      }
    }
  }
  .approvalList {
    display: flex;
    width: 100%;
    height: 40px;
    line-height: 40px;
  }
  .diaApproval .el-checkbox {
    margin-top: 20px;
    .el-checkbox__input {
      .el-checkbox__inner {
        margin: 0 auto;
      }
    }
  }
}

.t-c81930 {
  .add-attendance .dialogContent .right {
    li {
      background: #fcf2f2;
    }
  }
}

/deep/ .el-checkbox__input.is-checked + .el-checkbox__label,
/deep/ .el-radio__input.is-checked + .el-radio__label {
  color: #555555 !important;
}
.hover-pointer {
  cursor: pointer;
}
</style>
