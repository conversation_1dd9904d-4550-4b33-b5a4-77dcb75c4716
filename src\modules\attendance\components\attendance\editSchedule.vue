<template>
  <div class="addHoliday">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>编辑排班</span>
        </el-col>
      </el-row>
    </header>
    <div class="content">
      <el-form ref="form" label-width="100px">
        <el-form-item label="考勤班次">
          <el-button type="primary" @click="addShift">+ 添加排班班次</el-button>
          <el-button
            v-for="item in showShiftList"
            :key="item.id"
            style="margin: 5px 5px 0 0"
            >{{ item.groupName }}</el-button
          >
        </el-form-item>
        <!-- <el-form-item label="未排班设置">
          <el-checkbox v-model="allowedChooseShift" @change="setChooseShift"
            >未排班时，员工可选择班次打卡</el-checkbox
          >
        </el-form-item> -->
        <el-form-item label="排班周期">
          <el-button
            @click="handleClickSet"
            type="text"
            size="small"
            v-if="!isShowCycleTable"
            >点击设置</el-button
          >
          <el-table
            :data="tableData"
            border
            style="width: 720px"
            :header-cell-style="{ background: '#F5F7FA' }"
            v-else
          >
            <el-table-column label="周期名称" prop="name" width="180">
            </el-table-column>
            <el-table-column prop="shift" label="班次">
              <template slot-scope="scope">
                <el-tooltip class="item" effect="dark" placement="top">
                  <div slot="content">
                    <p style="max-width: 200px">
                      {{ scope.row.shift }}
                    </p>
                  </div>
                  <p class="shiftRange">
                    {{ scope.row.shift }}
                  </p>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="周期天数" prop="day" width="180">
            </el-table-column>
            <el-table-column width="160" label="操作">
              <template slot-scope="scope">
                <el-button
                  @click="handleClickSet(scope.row)"
                  type="text"
                  size="small"
                  >设置</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="排班设置">
          <div class="schedule-save">
            <el-button type="primary" @click="save">保存</el-button>
          </div>
          <Roaster
            :scheduleList="scheduleList"
            :showShiftList="showShiftList"
            :chooseDay="chooseDay"
            :form="form"
            :cycleList="cycleList"
            @updateList="updateList"
            @updateAllList="updateAllList"
            :shiftList="shiftList"
          ></Roaster>
        </el-form-item>
        <el-form-item label="">
          <RoasterStatic
            ref="rosterStatic"
            :params="rosterParams"
            :shiftList="shiftList"
          ></RoasterStatic>
        </el-form-item>
      </el-form>
    </div>

    <!-- 更改班次  -->
    <div class="changeDialog">
      <el-dialog
        title="选择班次"
        :visible.sync="changeVisible"
        width="800px"
        class="changeDialog"
      >
        <span class="dialogContent">
          <!-- <span class="shift-search"
            ><el-input v-model="key" placeholder="请输入班次名称"></el-input
          ></span>
          <el-button type="primary" @click="handleSearch">搜索</el-button> -->
          <el-table
            ref="multipleTable"
            :header-cell-style="{ background: '#F1F1F1' }"
            tooltip-effect="dark"
            :data="shiftList"
            @selection-change="handleSelectionChange"
            style="
              width: 100%;
              margin-top: 20px;
              height: 384px;
              overflow-y: auto;
            "
          >
            <el-table-column type="selection" width="180"> </el-table-column>
            <el-table-column
              prop="groupName"
              label="班次名称"
              width="180"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column prop="attTime" label="考勤时间">
              <template slot-scope="scope">
                <span>{{ scope.row.attTime[0] }}</span>
                <span>{{ scope.row.attTime[1] }}</span>
                <span>{{ scope.row.attTime[2] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <!-- <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currPage"
              :page-size="pageSize"
              layout="prev, pager, next, jumper"
              :total="total"
              background
            >
            </el-pagination>
          </div> -->
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="changeVisible = false">取 消</el-button>
          <el-button type="primary" @click="isSetAttendShift">确 定</el-button>
        </span>
      </el-dialog>
    </div>
    <!-- 设置排班周期 -->
    <div class="setting">
      <el-dialog title="设置排班周期" :visible.sync="openVisible" width="600px">
        <span class="set-content">
          <el-form ref="form" :model="form" label-width="100px">
            <el-form-item label="周期名称">
              <el-input
                v-model="form.cycleName"
                placeholder="请输入名称（最多6个字符中文或英文"
              ></el-input>
            </el-form-item>
            <el-form-item label="每个周期天数">
              <el-input-number
                v-model="form.cycleDays"
                controls-position="right"
                @change="handleChange"
                :min="2"
                :max="31"
              ></el-input-number>
            </el-form-item>
            <el-form-item
              :label="'第' + (index + 1) + '天'"
              v-for="(item, index) in form.cycleDays"
              :key="index"
            >
              <el-select
                v-model="chooseDay[index]"
                placeholder="请选择"
                :popper-append-to-body="false"
                clearable
              >
                <el-option label="休息" value="rest"> </el-option>
                <el-option
                  v-for="item in showShiftList"
                  :key="item.id"
                  :label="item.groupName + ' ' + item.attTime"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <p style="margin-left: 100px">
              设置后，请到考勤组编辑排班页面，点击单元格进行排班
            </p>
          </el-form>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancleScheduleCycle">取 消</el-button>
          <el-button type="primary" @click="setScheduleCycle">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import Roaster from "./roaster";
import RoasterStatic from "./rosterStatic";
export default {
  components: {
    Roaster,
    RoasterStatic,
  },
  data() {
    return {
      rosterParams: {},
      isShowCycleTable: false, //是否显示排班周期表格
      shiftListTotal: [], //一次请求班次列表总数量
      allowedChooseShift: false, //未排班设置是否勾选
      scheduleList: [], // 排班设置
      cycleList: [], // 周期下班次列表
      currChooseShift: [], //当前设置班次nodes
      workingShiftIds: [], //当前设置班次id
      showShiftList: [], //页面渲染班次列表
      chooseDay: ["rest", "rest", "rest"], //排班周期选择的天数
      form: {
        cycleName: "周期三天",
        cycleDays: "3",
        attendId: this.$route.query.id,
      },
      key: "",
      currPage: 1,
      pageSize: 5,
      total: "",
      shiftList: [], //排班班次列表
      changeVisible: false,
      name: "周期天",
      shift: "班次一 ~ 班次二 ~ 班次三",
      day: "3天",
      tableData: [
        {
          name: "周期三天",
          shift: "休息 ~ 休息 ~ 休息",
          day: "3天",
        },
      ],
      radio: "1",
      value: "",
      openVisible: false,
      date: "2021-01",
      currentPage: "1",
    };
  },
  created() {
    this.getShiftList();
    this.getCurrentTime();
  },
  methods: {
    // 更新排班设置参数
    updateList(obj) {
      let { rowIndex, colIndex, id, name, workDay, list } = obj;
      let day,
        temp,
        other = this.scheduleList[rowIndex].workPlanCalendarResultList;
      if (!list.length) {
        day = colIndex + 1 < 10 ? "0" + (colIndex + 1) : colIndex + 1;
        temp = other[colIndex];
        temp.workDay = workDay + "-" + day + " 00:00:00";
        temp.id = temp.id > -1 ? temp.id : (temp.id = "");
        if (name === "休息") {
          temp.workingShiftId = -1;
        } else {
          temp.workingShiftId = id;
        }
      } else {
        list.forEach((v, i) => {
          day =
            colIndex + 1 + i < 10 ? "0" + (colIndex + 1 + i) : colIndex + 1 + i;
          temp = other[colIndex + i];
          if (colIndex + 1 + i > other.length) return;
          temp.workDay = workDay + "-" + day + " 00:00:00";
          temp.id = temp.id > -1 ? temp.id : (temp.id = "");
          if (v === "rest") {
            temp.workingShiftId = -1;
          } else {
            temp.workingShiftId = v;
          }
        });
      }
    },

    // 修改日期更新所有的数据
    updateAllList(val) {
      this.date = val;
      this.getScheduleDetail();
    },

    // 获取当前年-月
    getCurrentTime() {
      const date = new Date();
      const year = date.getFullYear();
      let month = date.getMonth();
      month = month + 1;
      month = month < 10 ? "0" + month : month;
      this.date = `${year}-${month}`;
    },

    //班次列表
    getShiftList(val) {
      let params = {
        currPage: 1,
        pageSize: 1000000,
        key: this.key,
      };
      this.$attApi.apiPostAttendWorkingList(params).then((res) => {
        if (res.success) {
          this.total = res.data.total;
          this.shiftList = res.data.records;
          this.shiftList.forEach((val) => {
            val.attTime = [];
            val.workingTimeListVoList.forEach((its, index) => {
              if (its.workingBegin && its.workingEnd) {
                val.attTime[index] =
                  this.insertStr(its.workingBegin) +
                  "-" +
                  this.insertStr(its.workingEnd);
              }
            });
          });
          this.getScheduleDetail();
        }
      });
    },
    //拼接时间范围显示
    insertStr(str) {
      return str.slice(0, 2) + ":" + str.slice(2);
    },
    //设置未排班设置勾选
    setChooseShift() {
      let params = {
        allowedChooseShift: this.allowedChooseShift,
        id: this.$route.query.id,
      };
      this.$attApi.apiPostSaveAllowedChooseShift(params).then((res) => {
        if (res.success) {
          this.getScheduleDetail();
        }
      });
    },
    //是否展示被删班次弹框
    isSetAttendShift() {
      let shiftNameArr = [];
      this.showShiftList.forEach((val) => {
        if (this.currChooseShift.indexOf(val) == -1) {
          shiftNameArr.push(val.groupName);
        }
      });
      if (shiftNameArr.length) {
        this.$confirm(
          `确定从考勤组移除班次：${shiftNameArr}，移除后，排班周期将自动去掉该班次，但不影响排班表中已排的班次`,
          "移除班次",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            closeOnClickModal: false,
            closeOnPressEscape: false,
          }
        )
          .then(() => {
            this.setAttendShift();
          })
          .catch(() => {});
      } else {
        this.setAttendShift();
      }
    },
    //设置考勤班次
    setAttendShift() {
      let params = {
        attendId: this.$route.query.id,
        workingShiftIds: this.workingShiftIds,
      };
      this.$attApi.apiPostSetAttendWork(params).then((res) => {
        if (res.success) {
          this.changeVisible = false;
          this.showShiftList = this.currChooseShift;
          this.getScheduleDetail();
        }
      });
    },
    //取消设置排班周期
    cancleScheduleCycle() {
      this.openVisible = false;
      this.getScheduleDetail();
    },
    //设置排班周期
    setScheduleCycle() {
      let params = {
        ...this.form,
        workPlanCycleDetailReqList: [],
      };
      for (let i = 0; i < this.form.cycleDays; i++) {
        params.workPlanCycleDetailReqList.push({
          cycleOrder: i,
          attendId: this.$route.query.id,
          isRestDay: this.chooseDay[i] === "rest" ? true : false,
          workingShiftId: this.chooseDay[i] === "rest" ? -1 : this.chooseDay[i],
        });
      }
      this.$attApi.apiPostSaveOrUpdateWorkPlanCycle(params).then((res) => {
        if (res.success) {
          this.openVisible = false;
          this.$message({
            type: "success",
            message: "设置成功!",
          });
          this.getScheduleDetail();
        }
      });
    },
    //获取当前勾选班次集合
    handleSelectionChange(val) {
      let arrIds = [];
      let arrCurr = [];
      if (val.length !== 0) {
        val.forEach((item) => {
          if (arrIds.indexOf(item.id) == -1) {
            arrIds.push(item.id);
          }
          if (arrCurr.indexOf(item) == -1) {
            arrCurr.push(item);
          }
        });
      }
      this.workingShiftIds = arrIds;
      this.currChooseShift = arrCurr;
    },
    //获取排班详情回显
    getScheduleDetail() {
      let params = {
        attendId: this.$route.query.id,
        queryMonth: this.date + "-01",
      };
      this.$attApi.apiPostQueryWorkPlanCycle(params).then((res) => {
        if (res.success) {
          const DATA = res.data;
          const workCycle = res.data.workPlanCycleResult;
          //考勤班次回显
          this.showShiftList = [];
          this.shiftList.forEach((val) => {
            if (DATA.attendWorkIds.indexOf(val.id) !== -1) {
              this.showShiftList.push(val);
            }
          });
          //勾选回显班次
          if (this.changeVisible) {
            this.showShiftList.forEach((val) => {
              this.$refs.multipleTable.toggleRowSelection(val, true);
            });
          }
          //未排班设置回显
          this.allowedChooseShift = DATA.allowedChooseShift;
          // 排班设置
          this.scheduleList = DATA.attendRelResultList;
          //更新排班班次统计请求参数
          if (DATA.attendRelResultList) {
            this.rosterParams = params;
          }
          //排班周期回显
          if (workCycle) {
            this.isShowCycleTable = true;
            this.form = {
              cycleDays: workCycle.cycleDays,
              cycleName: workCycle.cycleName,
              attendId: workCycle.attendId,
              id: workCycle.id,
            };
            this.tableData[0].name = workCycle.cycleName;
            this.tableData[0].day = workCycle.cycleDays + "天";

            let shift = "";
            this.cycleList = [];
            workCycle.workPlanCycleDetailResultList.forEach((val, index) => {
              this.chooseDay[index] =
                val.workingShiftId !== -1 ? val.workingShiftId : "rest";
              if (val.workingShiftId !== -1) {
                shift += this.getShiftName(val.workingShiftId) + " ~ ";
                this.cycleList.push(this.getShiftName(val.workingShiftId));
              } else {
                shift += "休息" + " ~ ";
                this.cycleList.push("休息");
              }
            });
            this.tableData[0].shift = shift.substring(
              0,
              shift.lastIndexOf("~")
            );
          } else {
            this.isShowCycleTable = false;
            this.chooseDay = ["rest", "rest", "rest"];
            this.form.cycleName = "周期三天";
            this.form.cycleDays = "3";
            delete this.form.id;
          }
        }
      });
    },
    //根据id，获取班次名称
    getShiftName(workingShiftId) {
      let rs = [];
      this.showShiftList.forEach((val, index) => {
        if (val.id === workingShiftId) {
          rs = val.groupName;
        }
      });
      return rs;
    },
    //添加排班班次
    addShift() {
      this.changeVisible = true;
      this.$nextTick(() => {
        this.showShiftList.forEach((val) => {
          this.$refs.multipleTable.toggleRowSelection(val, true);
        });
      });
    },
    //设置排班周期
    handleClickSet() {
      this.openVisible = true;
    },
    handleSearch() {
      this.getShiftList();
    },
    //周期天数变化
    handleChange() {
      //新增天数，增加默认值-休息
      for (let i = 0; i < this.form.cycleDays; i++) {
        if (!this.chooseDay[i]) {
          this.chooseDay[i] = "rest";
        }
      }
    },

    // 排班设置保存数据
    save() {
      this.scheduleList.forEach((v) => {
        v.workPlanCalendarReqList = v.workPlanCalendarResultList;
        delete v.workPlanCalendarResultList;
        delete v.createdTime;
        delete v.updatedTime;
      });
      this.$attApi
        .apiPostSaveOrUpdateWorkCalendar(this.scheduleList)
        .then((res) => {
          if (res.success) {
            this.$message({
              type: "success",
              message: "保存成功!",
            });
            this.getScheduleDetail();
          }
        });
    },
    cancel() {},
    handleSizeChange(val) {
      this.pageSize = val;
      this.getShiftList();
    },
    handleCurrentChange(val) {
      this.currPage = val;
      this.getShiftList();
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.addHoliday {
  height: calc(100vh - 60px);
  padding: 0;
  .header {
    border-bottom: 1px solid #ededed;
  }
  .content {
    padding: 20px 20px 20px 0;
    .shiftRange {
      line-height: 50px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
    .schedule-save {
      position: absolute;
      left: 400px;
    }
  }
  .footer {
    padding: 0 0 20px 160px;
  }
  /deep/ .setting .el-dialog {
    height: 500px;
    font-size: 12px;
    .el-dialog__body {
      padding-bottom: 0;
      height: 350px;
      overflow: auto;
    }
    .el-input {
      width: 250px;
    }
    .el-input-number {
      width: 250px;
    }
  }
  .changeDialog {
    /deep/ .el-dialog {
      height: 580px;
      .shift-search .el-input {
        width: 250px;
      }
    }
    /deep/ .dialog-footer {
      position: absolute;
      display: flex;
      flex-direction: row;
      bottom: 15px;
      right: 20px;
    }
    .pagination {
      float: right;
      padding: 22px;
    }
  }
}
</style>
