<template>
  <div class="mapDialog">
    <el-dialog
      title="添加打卡地址"
      :visible.sync="openVisible"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @open="initMap"
    >
      <span class="mapContent">
        <el-autocomplete
          v-model="value"
          style="width:600px;margin-bottom:10px"
          popper-class="autoAddressClass"
          :fetch-suggestions="getResult"
          :trigger-on-focus="false"
          placeholder="详细地址"
          clearable
          :disabled="isDisabled"
          @select="handleSelect"
        >
          <template slot-scope="{ item }">
            <div style="display:flex;align-items: center;">
              <i class="el-icon-search fl mgr10" />
              <div style="overflow:hidden;flex:1;width:0;">
                <div class="title">{{ item.name }}</div>
                <span class="address ellipsis" style="display: block;padding-left: 10px;">{{ item.address }}</span>
              </div>
            </div>
          </template>
        </el-autocomplete>
        <div v-if="openVisible" id="container"></div>
        <div
          style="width: 400px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;display: flex;align-items: center;margin-top: 10px;"
        >
          <i class="el-icon-location" style="margin-right: 4px;"></i>
          <div style="display: flex">
            <span>考勤地址：</span>
            <AutoEllipsisTooltip
              style="max-width: 400px"
              :content="address"
            />
          </div>
        </div>
        <p class="location">
          经纬度坐标：{{ addrPoint.lng }}，{{ addrPoint.lat }}
        </p>
        <p class="placeAlias">
          别名：<el-input
            v-model="placeAlias"
            placeholder="请输入"
            :disabled="isDisabled"
          ></el-input>
        </p>
      </span>
      <span v-show="!isDisabled" slot="footer" class="dialog-footer">
        <el-button @click="openVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAddressInfo } from './utils.js';
import AutoEllipsisTooltip from "@/components/autoEllipsisTooltip.vue";


export default {
  components: {
    AutoEllipsisTooltip,
  },
  props: ["viewMapDetail"],
  data() {
    return {
      isDisabled: false, //是否禁用弹框
      openVisible: false,
      value: "",
      addrPoint: {
        // 详细地址经纬度
        lng: "",
        lat: ""
      },
      map: "",
      mk: "",
      placeAlias: "", //地址别名
      // longitude: '116.403874', //经度
      // latitude: '39.914889', //维度
      address: "", //地址
      errorRange: "",
      circle: "",
      searchService: null,
      center: ""
    };
  },
  watch: {
    viewMapDetail: {
      deep: true,
      immediate: true,
      handler: function(val) {
        console.log(val);
        this.placeAlias = val.placeAlias;
        this.address = val.placeName;
        this.addrPoint.lng = Number(val.longitude);
        this.addrPoint.lat = Number(val.latitude);
        this.errorRange = Number(val.errorRange);
      }
    }
  },

  methods: {
    openDialog() {
      this.openVisible = true;
      this.isDisabled = false;
    },
    open() {
      this.openVisible = true;
      this.isDisabled = false;
    },
    //查看
    openViewDialog() {
      this.openVisible = true;
      this.isDisabled = true;
    },
    initMap() {
      let that = this
      setTimeout(() => {
        if (
          this.addrPoint.lng == "116.510768" &&
          this.addrPoint.lat == "39.904349"
        ) {
          this.value = "";
        }
        this.center = new qq.maps.LatLng(
          this.addrPoint.lat,
          this.addrPoint.lng
        );
        this.map = new qq.maps.Map("container", {
          center: this.center, // 地图的中心地理坐标。
          zoom: 16 // 地地图缩放
        });
        this.mk = new qq.maps.Marker({
          // 标记的位置
          position: this.center,
          map: this.map
        });
        this.circle = new qq.maps.Circle({
          map: this.map,
          center: this.center,
          radius: this.errorRange,
          strokeWeight: 2,
          strokeOpacity: 0.8,
          color: "rgba(41,91,255,0.16)",
          showBorder: true,
          borderColor: "rgba(41,91,255,1)",
          borderWidth: 2
        });
        const _this = this;
        if (!this.isDisabled) {
          qq.maps.event.addListener(_this.map, "click", function(event) {
              console.log(event,'event')

              getAddressInfo(event.latLng.lat, event.latLng.lng)
              .then((address) => {
                  that.address = address.address
              })
              .catch((error) => {
                  console.error('获取地址时出错:', error);
              });

              event.location = event.latLng
            _this.createdMarker(event); // 提取经纬度的方法
          });
        }
      });
    },

    getResult(value, cb) {
      const poiText = this.value;
      const pageSize = 20;
      const output = "jsonp"
      const pageIndex = 1;
      const apiKey = 'TLEBZ-EZJWG-WX6QW-IOWRD-BT2EE-MSBFW';

      const url = `https://apis.map.qq.com/ws/place/v1/suggestion?output=${output}&keyword=${encodeURIComponent(poiText)}&page_size=${pageSize}&page_index=${pageIndex}&key=${apiKey}&callback=handleResponse`;

      // 创建一个全局回调函数
      window.handleResponse = function(data) {
        cb(data.data || []);
        // 完成后删除回调函数以防内存泄漏
        delete window.handleResponse;
      };

      // 创建一个 script 标签并添加到 document
      const script = document.createElement('script');
      script.src = url;
      script.onerror = function() {
        console.error('请求出错');
        cb([]);
      };
      document.body.appendChild(script);
    },

    handleSelect(event) {
      this.createdMarker(event);
    },
    createZuoBiao(myLatitude, myLongitude) {
      alert()
      return new qq.maps.LatLng(myLatitude, myLongitude);
    },
    createdMarker(event) {
      if (this.mk) {
        this.deleteOverlays(); // 删除marker的方法
      }
      this.addrPoint.lat = event.location.lat;
      this.addrPoint.lng = event.location.lng;
      this.center = new qq.maps.LatLng(this.addrPoint.lat, this.addrPoint.lng);
      // 创建标识
      this.initMap();
      this.address = event.address
    },
    // 删除marker 标识
    deleteOverlays() {
      this.mk.setMap(null);
      this.circle.setMap(null);
    },
    geocoder() {
      const _this = this; // 修正下边this指向的问题
      return new qq.maps.Geocoder({
        complete: function(result) {
          // 通过result参数 可以拿到当前点击的位置的详细地址，展开运算符，获取里边的参数
          const {
            city,
            district,
            province,
            street,
            streetNumber,
            town,
            village
          } = { ...result["detail"]["addressComponents"] };
          _this.address = `${province}${city}${district}${street}${town}${village}${streetNumber}`; // 参数拼接赋值给搜索框
          _this.value = _this.address;
        }
      });
    },
    confirm() {
      this.openVisible = false;
      let address = {
        placeName: this.address,
        placeAlias: this.placeAlias,
        longitude: this.addrPoint.lng,
        latitude: this.addrPoint.lat
      };
      this.$emit("getAddressList", address);
    }
  }
};
</script>
<style lang="scss" scoped>
.mapContent {
  .el-input {
    width: 300px;
    margin-bottom: 10px;
  }
  p {
    padding-top: 15px;
  }
  .location {
    padding-left: 17px;
  }
  .placeAlias {
    position: absolute;
    right: 20px;
    bottom: 90px;
  }
}
#container {
  overflow: hidden;
  width: 100%;
  height: 300px;
  margin: 0;
}
.mapDialog ::v-deep .el-dialog {
  height: 590px;
}
::v-deep .anchorBL {
  display: none;
}
.active {
  pointer-events: none;
}
#container div div div span {
  display: none !important;
}
</style>
