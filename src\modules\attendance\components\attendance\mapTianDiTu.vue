<template>
  <el-dialog
    title="添加打卡地址"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
  >
    <el-select
      v-if="mode === 'edit'"
      search
      placeholder="请输入地址"
      filterable
      remote
      v-model="hotPointID"
      @change="onSelectChange"
      value-key="name"
      :loading="loading"
      :remote-method="search"
      style="width: 60%;margin-bottom:10px;"
    >
      <el-option
        :key="index"
        :value="option.hotPointID"
        :label="option.name"
        v-for="(option, index) in options"
      >
      </el-option>
    </el-select>
    <div
      id="tianditu"
      style="width: 750px; height: 300px; margin-bottom: 20px;"
    ></div>
    <div style="display: flex; margin-top: 10px">
      <div style="flex: 1;display:flex; " v-if="address && address.name">
        <i class="el-icon-location" style="margin-top:3px;margin-right:4px;"></i> 
        <div>
            <span>考勤地址：{{ address.name }}</span>
            <span>经纬度坐标：{{ address.lng }}, {{ address.lat }}</span>
        </div>
      </div>
       <div style="flex: 0 0 340px; display: flex; align-items: center">
        <span style="flex: 0 0 60px">别名：</span>
        <el-input
          v-model="address.alias"
          placeholder="请输入"
          :disabled="mode === 'view'"
        />
      </div>
    </div>
    <span slot="footer" class="dialog-footer" v-show="mode === 'edit'">
      <el-button @click="close()">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>


export default {
  props: {
    viewMapDetail: Object | String,
  },
  watch: {
    visible(show){
      if(!show) return 
      this.initMap()
    },
    address: {
      deep: true,
      immediate: true,
      handler(val) {
        this.drawMarker();
      },
    },
    viewMapDetail: {
      deep: true,
      immediate: true,
      handler: function (val) {
        const address = {
          alias: val.placeAlias,
          name: val.placeName,
          lng: Number(val.longitude),
          lat: Number(val.latitude),
          range: Number(val.errorRange),
        };
        this.address = address;
        if (!this.options.length && this.mode === 'view') {
          this.options.push(address);
        }
      },
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      address: {
        name: "",
        lng: 0,
        lat: 0,
        alias: "",
        range: 500,
      },
      map : null,
      localSearch : null,
      geocode : null,
      timeoutHandler : null,
      hotPointID:"",
      options: [],
      mode: "edit", //支持 edit view  表示编辑模式 与 查看模式
    };
  },
  methods: {
    async initMap() {
      if(this.map) return 
      await this.$nextTick()
      this.map = new T.Map("tianditu");
      this.map.centerAndZoom(new T.LngLat(116.40769, 39.89945), 12);
      this.map.enableDrag();

      var cp = new T.CoordinatePickup(this.map, { 
        callback: (...args)=>{
          this.resetSelectOptions()
          this.getLngLat(...args)
        }
      })
      
      cp.addEvent();
      
      const control = new T.Control.Zoom();
      this.map.addControl(control);

      this.localSearch = new T.LocalSearch(this.map, {
        pageCapacity: 20,
        onSearchComplete: this.parseSearchResult,
      });
      this.geocode = new T.Geocoder();
    },
    onSelectChange(val){
      const addressInfo = this.options.find(item=>item.hotPointID === val)
      this.address = {...addressInfo}
    },
    getAddressName(lngLat){
      return new Promise(resolve =>{
         this.geocode.getLocation(lngLat,result=>{
          const status = result.status
          const msg = result.msg
          const addressName = result.resultObj.formatted_address
          if(status != 0) {
            resolve([{
              code:status,
              msg:[`服务器返回状态异常：status = ${status}`,`服务器返回信息：${msg}`]
            }]) 
            return 
          }

          resolve([null,addressName]) 
        });
      })
    },
    submit() {
      this.close();
      let address = {
        placeName: this.address.name,
        placeAlias: this.address.alias ,
        longitude: this.address.lng,
        latitude: this.address.lat,
        errorRange: this.address.range,
      };
      this.$emit("getAddressList", address);
    },
    parseSearchResult(result) {
      this.loading = false;
      if(!result.pois) {
        const { keyWord , area } = result
        const [ lng,lat ] = area.lonlat.split(",")
        this.options = [{
          name:keyWord,
          lng,
          lat,
          hotPointID:Math.random()
        }]
        return 
      }
      
      if (result && result.pois) {
        this.options = result.pois.map((item) => {
          const tmp = item.lonlat.split(",");
          return {
            name: item.name,
            lng: tmp[0],
            lat: tmp[1],
            hotPointID:item.hotPointID
          };
        });
      }
    },
    search(query) {
      this.loading = true;
      if (this.timeoutHandler) {
        clearTimeout(this.timeoutHandler);
        return;
      }

      this.timeoutHandler = setTimeout(() => {
        this.localSearch.search(query);
        clearTimeout(this.timeoutHandler);
        this.timeoutHandler = null;
      }, 300);
    },
    async getLngLat(lngLat){
      if(this.mode === 'view') return 
      this.address.lat = lngLat.lat
      this.address.lng = lngLat.lng
      const [errMsg,address] = await this.getAddressName(lngLat)
      if(errMsg) return console.error(errMsg)
      this.address.name = address;
      this.drawMarker()
    },
    drawMarker() {
      const { map } = this
      if (!map) {
        setTimeout(() => this.drawMarker(), 300);
        return;
      }

      const range = this.address.range ? this.address.range : 100
      var level = 17
      if (range >= 300 && range<=500){
        level = 15
      }
      if (range >= 600 && range<=1000){
        level = 13
      }
      if (range >= 2000){
        level = 12
      }
      map.clearOverLays();
      const lngLat = new T.LngLat(this.address.lng, this.address.lat);
      map.centerAndZoom(lngLat, level);
      var marker = new T.Marker(lngLat);
      //向地图上添加标注
      map.addOverLay(marker);
      marker.disableDragging();
      if(this.mode === 'edit'){
        marker.enableDragging();
      }
     
      const circle = new T.Circle(
        lngLat,
        range,
        {
          color: "blue",
          weight: 5,
          opacity: 0.5,
          fillColor: "#FFFFFF",
          fillOpacity: 0.5,
          lineStyle: "solid",
        }
      );

      map.addOverLay(circle);

      marker.on("dragend", async (e) => {
        this.resetSelectOptions()
        const point = e.lnglat;
        this.address.lng = point.lng;
        this.address.lat = point.lat;
        circle.setCenter(point);
        const [errMsg,address] = await this.getAddressName(point)
        if(errMsg) return console.error(errMsg)
        this.address.name = address;
      });
    },
    open(mode) {
      if (!mode) {
        mode = "edit";
      }
      this.mode = mode;
      this.visible = true;
    },
    close() {
      this.visible = false;
      this.resetSelectOptions()
    },
    resetSelectOptions(){
      this.hotPointID = ""
      this.options = []
    }
  },
};
</script>

<style>
</style>