<template>
  <div>
    <div class="shiftSet">
      <el-date-picker
        v-model="date"
        type="month"
        placeholder="选择日期"
        @change="changeDate"
      >
      </el-date-picker>
      <!--<div>
          <el-button type="primary">导入排班</el-button>
          <el-button>导出排班</el-button>
      </div>-->
    </div>
    <el-table
      :data="dayList"
      border
      :header-cell-style="{ background: '#F5F7FA' }"
      :cell-class-name="tableCellClassName"
      @cell-click="cellClick"
    >
      <el-table-column prop="name" label="姓名" width="80" fixed>
      </el-table-column>
      <el-table-column
        v-for="val in dayHeader"
        :key="val.label"
        :prop="`name${val.label}`"
        :label="getWeek(val.label)"
        width="120"
      >
      </el-table-column>
    </el-table>

    <el-dialog
      title="选择"
      :visible.sync="dialogVisible"
      custom-class="check-shift"
      width="30%"
    >
      <el-tabs type="border-card">
        <el-tab-pane label="按天排班">
          <el-row>
            <el-col :span="24">
              <div
                class="day-option"
                v-for="v in shiftLists"
                :key="v.id"
                @click="checkOption(v.groupName, v.id, 0)"
              >
                {{ v.groupName }}
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="按周期排班">
          <el-row>
            <el-col :span="24">
              <div
                class="day-option"
                v-for="v in [form]"
                :key="v.id"
                @click="checkOption(cycleList, 1)"
              >
                {{ v.cycleName }}
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "roaster",
  props: {
    // 日历表数据
    scheduleList: {
      type: Array,
      default: () => []
    },
    // 班次列表
    shiftList: {
      type: Array,
      default: () => []
    },
    // 当前设置班次列表
    showShiftList: {
      type: Array,
      default: () => []
    },
    // 周期名称
    form: {
      type: Object,
      default: () => {}
    },
    // 周期详情
    chooseDay: {
      type: Array,
      default: () => []
    },
    // 周期下班次列表
    cycleList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dayHeader: [],
      dayList: [], // 日历列表
      dialogVisible: false,
      date: "2021-01",
      rowIndex: null, // 列表行索引
      colIndex: null, // 列表列索引
      shiftLists: [] // 按日排班列表
    };
  },
  watch: {
    scheduleList: {
      handler(val) {
        if (val.length) {
          this.getDayHeader();
          this.getDayList();
        }
      },
      deep: true
    },

    showShiftList(val) {
      if (val.length) {
        this.getShiftList();
      }
    }
  },
  created() {
    this.getCurrentTime();
  },
  methods: {
    // 日期修改回调
    changeDate(val) {
      let date = new Date(val);
      let y = date.getFullYear();
      let m =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      this.date = `${y}-${m}`;
      this.$emit("updateAllList", this.date);
    },

    // 获取当前年-月
    getCurrentTime() {
      const date = new Date();
      const year = date.getFullYear();
      let month = date.getMonth();
      month = month + 1;
      month = month < 10 ? "0" + month : month;
      this.date = `${year}-${month}`;
    },

    // 获取日排班列表
    getShiftList() {
      this.shiftLists = [
        ...this.showShiftList,
        ...[
          {
            groupName: "休息"
          }
        ]
      ];
    },

    // 获取列表头部信息
    getDayHeader() {
      this.dayHeader = this.scheduleList[0].workPlanCalendarResultList.map(
        v => {
          return {
            label: v.cycleOrder
          };
        }
      );
    },

    // 按天/按周期选中回调
    checkOption(name, id, mark) {
      if (arguments.length === 2) mark = id;
      let obj = this.dayList[this.rowIndex],
        list = [],
        arr = [],
        arrResult = [],
        total = "",
        leng = this.chooseDay.length;
      if (!mark) {
        obj["name" + this.colIndex] = name;
      } else {
        arr = [...this.chooseDay];
        arr.length = this.dayHeader.length - this.colIndex + 1;
        total = Math.ceil((this.dayHeader.length - this.colIndex) / leng);
        for (let num = 1; num <= total; num++) {
          arr.copyWithin(leng * num, 0, leng);
        }
        arr.forEach((v, i) => {
          if (this.colIndex + i > this.dayHeader.length) return;
          obj["name" + (this.colIndex + i)] = name[i];
        });
        list = arr;
      }
      this.$set(this.dayList, this.rowIndex, obj);
      this.$emit("updateList", {
        rowIndex: this.rowIndex,
        colIndex: this.colIndex - 1,
        workDay: this.date,
        id,
        name,
        list
      });
      this.dialogVisible = false;
    },

    // 为行列赋索引值
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      row.index = rowIndex;
      column.index = columnIndex;
    },

    // 获取列表全部信息
    getDayList() {
      this.scheduleList.forEach((v, i) => {
        this.dayList[i] = {};
        this.dayList[i].name = v.targetName;
        if (v.workPlanCalendarResultList) {
          let temp = v.workPlanCalendarResultList;
          temp.forEach(x => {
            this.dayList[i]["name" + x.cycleOrder] = this.getName(
              x.workingShiftId
            );
          });
          this.$set(this.dayList, i, this.dayList[i]);
        }
      });
    },

    // 获取星期几
    getWeek(mark) {
      mark = mark < 10 ? "0" + mark : mark;
      let date = new Date(this.date + "-" + mark).getDay();
      switch (date) {
        case 1:
          return mark + "(周一)";
          break;
        case 2:
          return mark + "(周二)";
          break;
        case 3:
          return mark + "(周三)";
          break;
        case 4:
          return mark + "(周四)";
          break;
        case 5:
          return mark + "(周五)";
          break;
        case 6:
          return mark + "(周六)";
          break;
        case 0:
          return mark + "(周日)";
          break;
      }
    },

    // 根据班次id获取班次名称
    getName(val) {
      let name;
      if (!val) return "未排班";
      if (val === -1) return "休息";
      for (let i in this.shiftList) {
        if (this.shiftList[i].id === val) {
          name = this.shiftList[i].groupName;
          break;
        }
      }
      return name;
    },

    // 单元格点击事件
    cellClick(row, column, cell, event) {
      this.rowIndex = row.index;
      this.colIndex = column.index;
      // let currTime = new Date(new Date().toLocaleDateString());
      if (
        new Date(this.date + "-" + this.colIndex).getTime() <
        new Date().getTime()
      ) {
        this.$message({
          type: "warning",
          message: "当天之前的日期(包含当天)不能进行排班"
        });
        return;
      }
      this.dialogVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.shiftSet {
  display: flex;
  width: 720px;
  justify-content: space-between;
  margin-bottom: 20px;
  .el-date-editor {
    width: 300px;
  }
}

/deep/ .el-dialog__header {
  border-bottom: none;
}

/deep/ .el-table {
  td > .cell {
    cursor: pointer;
  }
  tr td:first-child {
    pointer-events: none;
  }
}

.day-option {
  border-bottom: 1px solid #eee;
}
</style>
