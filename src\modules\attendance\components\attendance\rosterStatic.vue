<template>
  <el-table
    :data="dayList"
    :header-cell-style="{ background: '#F5F7FA' }"
    border
  >
    <el-table-column prop="name" label="" width="80" fixed></el-table-column>
    <el-table-column
      v-for="val in dayHeader"
      :key="val.label"
      :prop="`name${val.label}`"
      :label="getWeek(val.label)"
      width="120"
    >
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  props: {
    // 请求参数
    params: {
      type: Object,
      default: () => {}
    },
    // 班次列表
    shiftList: {
      type: Array,
      default: () => []
    }
  },

  watch: {
    params(val) {
      this.date = val.queryMonth.substring(0, 7);
      this.getRosterStatic(val);
    }
  },

  data() {
    return {
      shiftStatic: [], //班次统计信息
      dayHeader: [], //表头列表
      dayList: [],
      date: "2021-01"
    };
  },

  methods: {
    getRosterStatic(val) {
      this.$attApi.apiPostSchedulingWorks(val).then(res => {
        if (res.success) {
          this.shiftStatic = res.data;
          this.getDayHeader();
          this.getDayList();
        }
      });
    },

    // 获取列表头部信息
    getDayHeader() {
      this.dayHeader = this.shiftStatic[0].workCountList.map(v => {
        return {
          label: v.cycleOrder
        };
      });
    },

    // 获取列表全部信息
    getDayList() {
      this.dayList = [];
      this.shiftStatic.forEach((v, i) => {
        this.dayList[i] = {};
        this.dayList[i].name = this.getName(v.workingShiftId);
        if (v.workCountList) {
          let temp = v.workCountList;
          temp.forEach(x => {
            this.dayList[i]["name" + x.cycleOrder] = x.workCount;
          });
          this.$set(this.dayList, i, this.dayList[i]);
        }
      });
    },

    // 获取星期几
    getWeek(mark) {
      mark = mark < 10 ? "0" + mark : mark;
      let date = new Date(this.date + "-" + mark).getDay();
      switch (date) {
        case 1:
          return mark + "(周一)";
          break;
        case 2:
          return mark + "(周二)";
          break;
        case 3:
          return mark + "(周三)";
          break;
        case 4:
          return mark + "(周四)";
          break;
        case 5:
          return mark + "(周五)";
          break;
        case 6:
          return mark + "(周六)";
          break;
        case 0:
          return mark + "(周日)";
          break;
      }
    },

    // 根据班次id获取班次名称
    getName(val) {
      let name;
      if (val === -1) return "休息";
      for (let i in this.shiftList) {
        if (this.shiftList[i].id === val) {
          name = this.shiftList[i].groupName;
          break;
        }
      }
      return name;
    }
  }
};
</script>

<style lang="scss" scoped></style>
