<template>
  <div class="viewDetail">
    <el-dialog
      title="班次详情"
      custom-class="special-dialog"
      :visible.sync="openShiftDetail"
      top="5vh"
      width="800px"
    >
      <div class="shift-detail" :style="'height:' + dialogHeight + 'px'">
          <div class="page-mian">
            <el-form
              :model="form"
              ref="form"
              :rules="rules"
              label-width="130px"
            >
              <el-form-item label="班次名称" prop="groupName">
                <div class="shift-name">
                  <el-input
                    v-model.trim="form.groupName"
                    placeholder="请输入班次名称"
                  ></el-input>
                  <span style="width:350px;margin-left:20px"
                    >最多30个字符(中英文或数字)</span
                  >
                </div>
              </el-form-item>
              <h3>打卡时段<span>按班次一天内上下班的次数</span></h3>
              <el-form-item label="一天内上下班次数">
                <div class="work-num">
                  <el-radio v-model="shiftCount" label="1"
                    >一天一次上下班</el-radio
                  >
                  <el-radio v-model="shiftCount" label="2"
                    >一天两次上下班</el-radio
                  >
                  <el-radio v-model="shiftCount" label="3"
                    >一天三次上下班</el-radio
                  >
                </div>
              </el-form-item>
              <h3 class="time-form" v-if="shiftCount === '1'">考勤时间形式</h3>
              <el-select
                v-model="shiftType"
                v-if="shiftCount === '1'"
                :popper-append-to-body="false"
                clearable
              >
                <el-option label="当前班次 固定时间" value="FIX"></el-option>
                <el-option label="当前班次 弹性时间" value="FLEX"></el-option>
              </el-select>
              <div class="twoContent">
                <div class="tipTable">
                  <table>
                    <tr>
                      <td>上下班时间</td>
                      <td>打卡限制</td>
                    </tr>
                    <tr>
                      <td>
                        第一次上班
                        <el-time-picker
                          v-model="workingBegin[0]"
                          format="HH:mm"
                          value-format="HH:mm:ss"
                          :picker-options="{
                            selectableRange: `00:00:00 - ${
                              workingEnd[0] ? workingEnd[0] : '23:59:59'
                            }`
                          }"
                          placeholder="选择时间"
                        >
                        </el-time-picker>
                      </td>
                      <td>
                        <p>
                          最早可提前<el-input
                            v-model="workingBeginAdvance[0]"
                            style="width:100px"
                          ></el-input
                          >分钟进行上班打卡
                        </p>
                        <p>
                          晚到超过<el-input
                            v-model="workingBeginLate[0]"
                            style="width:100px"
                          ></el-input
                          >分钟记为迟到
                        </p>
                        <p>
                          晚到超过<el-input
                            v-model="workingBeginAbsent[0]"
                            style="width:100px"
                          ></el-input
                          >分钟记为半天缺卡（半天旷工）
                        </p>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        第一次下班
                        <el-time-picker
                          v-model="workingEnd[0]"
                          format="HH:mm"
                          value-format="HH:mm:ss"
                          :picker-options="{
                            selectableRange: `${
                              workingBegin[0] ? workingBegin[0] : '00:00:00'
                            } - '23:59:59'`
                          }"
                          placeholder="选择时间"
                        >
                        </el-time-picker>
                      </td>
                      <td>
                        <p>
                          最晚可延后<el-input
                            v-model="workingEndLate[0]"
                            style="width:100px"
                          ></el-input
                          >分钟进行下班打卡
                        </p>
                        <p>
                          早于下班时间<el-input
                            v-model="workingEndAdvance[0]"
                            style="width:100px"
                          ></el-input
                          >分钟打卡记为早退
                        </p>
                        <p>
                          早于下班时间<el-input
                            v-model="workingEndAbsent[0]"
                            style="width:100px"
                          ></el-input
                          >分钟打卡记为半天缺卡（半天旷工）
                        </p>
                      </td>
                    </tr>
                    <tr v-show="shiftCount !== '1'">
                      <td>
                        第二次上班
                        <el-time-picker
                          v-model="workingBegin[1]"
                          format="HH:mm"
                          value-format="HH:mm:ss"
                          :picker-options="{
                            selectableRange: `00:00:00 - ${
                              workingEnd[1] ? workingEnd[1] : '23:59:59'
                            }`
                          }"
                          placeholder="选择时间"
                        >
                        </el-time-picker>
                      </td>
                      <td>
                        <p>
                          最早可提前<el-input
                            v-model="workingBeginAdvance[1]"
                            style="width:100px"
                          ></el-input
                          >分钟进行上班打卡
                        </p>
                        <p>
                          晚到超过<el-input
                            v-model="workingBeginLate[1]"
                            style="width:100px"
                          ></el-input
                          >分钟记为迟到
                        </p>
                        <p>
                          晚到超过<el-input
                            v-model="workingBeginAbsent[1]"
                            style="width:100px"
                          ></el-input
                          >分钟记为半天缺卡（半天旷工）
                        </p>
                      </td>
                    </tr>
                    <tr v-show="shiftCount !== '1'">
                      <td>
                        第二次下班
                        <el-time-picker
                          v-model="workingEnd[1]"
                          format="HH:mm"
                          value-format="HH:mm:ss"
                          :picker-options="{
                            selectableRange: `${
                              workingBegin[1] ? workingBegin[1] : '00:00:00'
                            } - '23:59:59'`
                          }"
                          placeholder="选择时间"
                        >
                        </el-time-picker>
                      </td>
                      <td>
                        <p>
                          最晚可延后<el-input
                            v-model="workingEndLate[1]"
                            style="width:100px"
                          ></el-input
                          >分钟进行下班打卡
                        </p>
                        <p>
                          早于下班时间<el-input
                            v-model="workingEndAdvance[1]"
                            style="width:100px"
                          ></el-input
                          >分钟打卡记为早退
                        </p>
                        <p>
                          早于下班时间<el-input
                            v-model="workingEndAbsent[1]"
                            style="width:100px"
                          ></el-input
                          >分钟打卡记为半天缺卡（半天旷工）
                        </p>
                      </td>
                    </tr>
                    <tr v-show="shiftCount === '3'">
                      <td>
                        第三次上班
                        <el-time-picker
                          v-model="workingBegin[2]"
                          format="HH:mm"
                          value-format="HH:mm:ss"
                          :picker-options="{
                            selectableRange: `00:00:00 - ${
                              workingEnd[2] ? workingEnd[2] : '23:59:59'
                            }`
                          }"
                          placeholder="选择时间"
                        >
                        </el-time-picker>
                      </td>
                      <td>
                        <p>
                          最早可提前<el-input
                            v-model="workingBeginAdvance[2]"
                            style="width:100px"
                          ></el-input
                          >分钟进行上班打卡
                        </p>
                        <p>
                          晚到超过<el-input
                            v-model="workingBeginLate[2]"
                            style="width:100px"
                          ></el-input
                          >分钟记为迟到
                        </p>
                        <p>
                          晚到超过<el-input
                            v-model="workingBeginAbsent[2]"
                            style="width:100px"
                          ></el-input
                          >分钟记为半天缺卡（半天旷工）
                        </p>
                      </td>
                    </tr>
                    <tr v-show="shiftCount === '3'">
                      <td>
                        第三次下班
                        <el-time-picker
                          v-model="workingEnd[2]"
                          format="HH:mm"
                          value-format="HH:mm:ss"
                          :picker-options="{
                            selectableRange: `${
                              workingBegin[2] ? workingBegin[2] : '00:00:00'
                            } - '23:59:59'`
                          }"
                          placeholder="选择时间"
                        >
                        </el-time-picker>
                      </td>
                      <td>
                        <p>
                          最晚可延后<el-input
                            v-model="workingEndLate[2]"
                            style="width:100px"
                          ></el-input
                          >分钟进行下班打卡
                        </p>
                        <p>
                          早于下班时间<el-input
                            v-model="workingEndAdvance[2]"
                            style="width:100px"
                          ></el-input
                          >分钟打卡记为早退
                        </p>
                        <p>
                          早于下班时间<el-input
                            v-model="workingEndAbsent[2]"
                            style="width:100px"
                          ></el-input
                          >分钟打卡记为半天缺卡（半天旷工）
                        </p>
                      </td>
                    </tr>
                  </table>
                </div>
              </div>
              <el-form-item label="工作时长">
          <span
            >{{ Math.floor(workingMinutes / 60) }}时{{
              workingMinutes % 60
            }}分</span
          >
              </el-form-item>
              <el-form-item
                label="弹性时间"
                v-if="shiftType === 'FLEX' && shiftCount === '1'"
              >
                <div class="flexTime">
                  <p>
                    <el-checkbox v-model="allowBeginFlex">
                      上班最多可晚到：<el-input
                        v-model="workingBeginFlexHour"
                      ></el-input
                      >小时 <el-input v-model="workingBeginFlex"></el-input>分钟
                      上班晚到几分钟，下班须晚走几分钟
                    </el-checkbox>
                  </p>
                  <p>
                    <el-checkbox v-model="allowEndFlex">
                      下班最多可早走：<el-input
                        v-model="workingEndFlexHour"
                      ></el-input
                      >小时 <el-input v-model="workingEndFlex"></el-input>分钟
                      上班早到几分钟，下班可早走几分钟
                    </el-checkbox>
                  </p>
                </div>
              </el-form-item>
              <el-form-item label="休息时间" v-if="shiftCount === '1'">
                <el-checkbox v-model="allowRest"></el-checkbox>
                <span>
                  休息时间开始
                  <el-time-picker
                    v-model="restBegin"
                    format="HH:mm"
                    value-format="HH:mm:ss"
                    :picker-options="{
                      selectableRange: `00:00:00 - ${
                        restEnd ? restEnd : '23:59:59'
                      }`
                    }"
                    placeholder="选择时间"
                  >
                  </el-time-picker>
                </span>
                <span>
                  休息时间结束
                  <el-time-picker
                    v-model="restEnd"
                    format="HH:mm"
                    value-format="HH:mm:ss"
                    :picker-options="{
                      selectableRange: `${
                        restBegin ? restBegin : '00:00:00'
                      } - '23:59:59'`
                    }"
                    placeholder="选择时间"
                  >
                  </el-time-picker>
                </span>
              </el-form-item>
              <!-- <h3 class="time-form">其它规则</h3>
                <div class="">
                <el-checkbox> 晚走次日晚到</el-checkbox>
                <span class="">该规则不支持排班制和自由班制</span>
                </div>
                <el-form-item label="规则1">
                <span>
                    下班晚走
                    <el-input-number v-model="num" controls-position="right" @change="handleChange" :min="1" :max="10"></el-input-number>
                    小时
                </span>
                <span>
                    次日可晚到
                    <el-input-number v-model="num" controls-position="right" @change="handleChange" :min="1" :max="10"></el-input-number>
                    小时，并且不算迟到
                </span>
                <el-button type="text">新增</el-button>
                <el-button type="text">删除</el-button>
                </el-form-item>        -->
            </el-form>
          </div>
        </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: ['currId'],
  data() {
    const checkShift = (r, v, c) => {
      let rule = new RegExp('^[\u4e00-\u9fa50-9a-zA-Z]*$');
      if (!v) return c(new Error("班次名称不能为空"));
      if (v.length > 30) return c(new Error("最多可输入30个字符"));
      if (!rule.test(v))
        return c(new Error("输入的班次名称格式不正确，支持中文，字母，数字"));
      c();
    };
    return {
      dialogHeight: 300,  // 对话框内容高度
      openShiftDetail: false,
      shiftCount: "1", //班次数量
      value: "",
      shiftNum: "1",
      input: "1",
      shiftType: "FIX", //班次类型
      detailId: ["", "", ""],
      workingBegin: ["09:00:00", "", ""], //上班时间
      workingBeginAdvance: ["60", "", ""], //上班提前打卡时间
      workingBeginLate: ["0", "", ""], //上班迟到打卡时间
      workingBeginAbsent: ["30", "", ""], //上班缺卡打卡时间
      workingEnd: ["18:00:00", "", ""], //下班时间
      workingEndAdvance: ["0", "", ""], //下班早退打卡时间
      workingEndLate: ["480", "", ""], //下班延后打卡时间
      workingEndAbsent: ["30", "", ""], //下班缺卡打卡时间
      duration: "", //工作时长
      restBegin: "12:00:00", //休息开始时间
      restEnd: "13:00:00", //休息结束时间
      allowRest: false, //是否开启休息时间段
      workingSwitch: true, //时长调休
      allowBeginFlex: false, //上班打卡规则是否生效
      allowEndFlex: false, //下班打卡规则是否生效
      workingBeginFlexHour: "", //上班弹性小时
      workingBeginFlex: "", //上班弹性分钟
      workingEndFlexHour: "", //下班弹性小时
      workingEndFlex: "", //下班弹性分钟
      form: {
        groupName: "" //班次组名称
      },
      rules: {
        groupName: [
          {
            required: true,
            validator: checkShift,
            trigger: "blur"
          }
        ]
      }
    };
  },
  watch: {
      currId(val) {
          console.log(val)
          this.getShiftEdit()
      }
  },
  created() {
    if (this.currId) {
      this.getShiftEdit();
    }
  },
  mounted() {
    this.setDialogHeight();
  },
  computed: {
    //工作总时长
    workingMinutes() {
      switch (this.shiftCount) {
        case "1":
          return this.getWorkHours(0);
          break;
        case "2":
          return this.getWorkHours(1) + this.getWorkHours(0);
          break;
        case "3":
          return (
            this.getWorkHours(2) + this.getWorkHours(1) + this.getWorkHours(0)
          );
          break;
      }
    }
  },
  methods: {
    //计算上下班工作时长
    getWorkHours(index) {
      if (this.workingBegin[index] && this.workingEnd[index]) {
        let start1 = this.workingBegin[index].split(":");
        let workingBegin = parseInt(start1[0] * 60) + parseInt(start1[1]);
        let end1 = this.workingEnd[index].split(":");
        let workingEnd = parseInt(end1[0] * 60) + parseInt(end1[1]);
        let start2 = this.restBegin.split(":");
        let restBegin = parseInt(start2[0] * 60) + parseInt(start2[1]);
        let end2 = this.restEnd.split(":");
        let restEnd = parseInt(end2[0] * 60) + parseInt(end2[1]);
        let time = "";
        if (this.allowRest && this.shiftCount === "1") {
          if (
            restBegin >= workingBegin &&
            restBegin <= workingEnd &&
            restEnd <= workingEnd &&
            restEnd >= restBegin
          ) {
            time = restBegin - workingBegin + (workingEnd - restEnd);
          } else {
            // this.$message.error("休息时间段必须在上下班时间内");
            this.restBegin = this.workingBegin[0];
            this.restEnd = this.workingEnd[0];
            time = 0;
          }
        } else {
          time = workingEnd - workingBegin;
        }
        return time;
      } else {
        return 0;
      }
    },

    // 设置对话框高度
    setDialogHeight() {
      this.dialogHeight = this.$el.offsetTop * 75 / 100;
    },

    openViewDetail() {
        this.openShiftDetail = true
    },
    //编辑回显
    getShiftEdit() {
      this.$attApi
        .apigetQueryAttendWork({ id: this.currId })
        .then(res => {
          if (res.success) {
            const rs = res.data;
            const flex = rs.workingShiftDetailResultList[0];
            this.form.groupName = rs.groupName;
            this.shiftCount = String(rs.shiftCount);
            this.shiftType = rs.shiftType;
            this.restBegin =
              flex.restBegin.slice(0, 2) +
              ":" +
              flex.restBegin.slice(2) +
              ":00";
            this.restEnd =
              flex.restEnd.slice(0, 2) + ":" + flex.restEnd.slice(2) + ":00";
            rs.workingShiftDetailResultList.forEach((val, index) => {
              this.detailId[index] = val.id;
              let begin = val.workingBegin
                ? val.workingBegin.slice(0, 2) +
                  ":" +
                  val.workingBegin.slice(2) +
                  ":00"
                : "";
              this.$set(this.workingBegin, index, begin);
              this.workingBeginAbsent[index] = val.workingBeginAbsent;
              this.workingBeginAdvance[index] = val.workingBeginAdvance;
              this.workingBeginLate[index] = val.workingBeginLate;
              let end = val.workingEnd
                ? val.workingEnd.slice(0, 2) +
                  ":" +
                  val.workingEnd.slice(2) +
                  ":00"
                : "";
              this.$set(this.workingEnd, index, end);
              this.workingEndAbsent[index] = val.workingEndAbsent;
              this.workingEndAdvance[index] = val.workingEndAdvance;
              this.workingEndLate[index] = val.workingEndLate;
            });
            if (rs.shiftCount === 1) {
              this.allowBeginFlex = flex.allowBeginFlex;
              this.allowEndFlex = flex.allowEndFlex;
              this.allowRest = flex.allowRest;
              this.workingBeginFlexHour = flex.workingBeginFlexHour;
              this.workingBeginFlex = flex.workingBeginFlex;
              this.workingEndFlexHour = flex.workingEndFlexHour;
              this.workingEndFlex = flex.workingEndFlex;
            }
          }
        });
    },
    //上下班参数
    getparams(index) {
      console.log(index);
      let par = {
        orderNo: index + 1,
        id: this.detailId[index],
        shiftType: this.shiftType,
        workingBeginAbsent: this.workingBeginAbsent[index],
        workingBeginAdvance: this.workingBeginAdvance[index],
        workingBeginLate: this.workingBeginLate[index],
        workingEndAbsent: this.workingEndAbsent[index],
        workingEndAdvance: this.workingEndAdvance[index],
        workingEndLate: this.workingEndLate[index]
      };
      return par;
    },
    //提交保存新增班次
    handleSave() {
      let params = {
        coId: 0,
        deptId: 0,
        groupName: this.form.groupName,
        id: this.$route.query.id ? this.$route.query.id : 0,
        shiftCount: this.shiftCount,
        shiftType: this.shiftType,
        taxsubId: 0,
        workingShiftDetailReqList: [
          {
            ...this.getparams(0),
            restBegin:
              this.restBegin.split(":")[0] + this.restBegin.split(":")[1],
            restEnd: this.restEnd.split(":")[0] + this.restEnd.split(":")[1],
            workingBegin:
              this.workingBegin[0].split(":")[0] +
              this.workingBegin[0].split(":")[1],
            workingEnd:
              this.workingEnd[0].split(":")[0] +
              this.workingEnd[0].split(":")[1],
            allowBeginFlex: this.allowBeginFlex,
            allowEndFlex: this.allowEndFlex,
            allowRest: this.allowRest,
            workingBeginFlexHour: this.workingBeginFlexHour,
            workingBeginFlex: this.workingBeginFlex,
            workingEndFlexHour: this.workingEndFlexHour,
            workingEndFlex: this.workingEndFlex,
            workingHours: this.workingHours
          },
          {
            ...this.getparams(1),
            workingBegin:
              this.shiftCount !== "1"
                ? this.workingBegin[1].split(":")[0] +
                  this.workingBegin[1].split(":")[1]
                : "",
            workingEnd:
              this.shiftCount !== "1"
                ? this.workingEnd[1].split(":")[0] +
                  this.workingEnd[1].split(":")[1]
                : ""
          },
          {
            ...this.getparams(2),
            workingBegin:
              this.shiftCount === "3"
                ? this.workingBegin[2].split(":")[0] +
                  this.workingBegin[2].split(":")[1]
                : "",
            workingEnd:
              this.shiftCount === "3"
                ? this.workingEnd[2].split(":")[0] +
                  this.workingEnd[2].split(":")[1]
                : ""
          }
        ],
        workingSwitch: true
      };
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$attApi.apiPostSavaOrUpdateWorking(params).then(res => {
            if (res.success) {
              this.$message({
                type: "success",
                message: "保存成功!"
              });
              this.$router.go(-1);
            }
          });
        } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    handleCancel() {
      this.$router.go(-1);
    },
    goBack() {
      this.$confirm("离开当前页面会丢失未保存的修改信息, 确定离开吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.$router.go(-1);
      });
    },
    handleBack() {
      this.$router.go(-1);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleChange() {}
  }
};
</script>

<style lang="scss" scoped>
.shift-detail .el-form{
  overflow-y: auto;
  pointer-events: none;
  /deep/ .el-input__suffix-inner {
    pointer-events: none;
  }
}
.header {
  border-bottom: 1px solid #ededed;
}
.page-mian {
  padding: 20px 20px 0 20px;
  height: 100%;
  overflow: auto;
  .shift-name {
    width: 500px;
    display: flex;
  }
  h3 > span {
    font-size: 14px;
    color: #888888;
    padding-left: 20px;
  }
  .work-num {
    display: flex;
    flex-direction: column;
  }
  .time-form {
    padding-bottom: 20px;
  }
  .twoContent {
    .tipTable {
      text-align: center;
      padding: 15px 0;
      width: 700px;
      table {
        border-collapse: collapse;
        border-spacing: 0;
        border: 1px solid #cbcbcb;
        margin-top: 15px;
        tr {
          text-align: center;
        }
        td {
          text-align: left;
          height: 30px;
          padding-left: 10px;
          border: 1px solid #cbcbcb;
        }
      }
    }
  }
}
.search-box {
  width: 700px;
  display: flex;
  line-height: 40px;
  margin-bottom: 10px;
  padding-top: 10px;
}
.mian-model {
  width: 98%;
  background-color: white;
  margin-top: 10px;
  padding-top: 5px;
  padding-bottom: 10px;
}
.model-title {
  display: flex;
  font-size: 20px;
  border-bottom: 1px solid #ededed;
  margin-bottom: 10px;
}
.info-peo {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}
.shift-bn {
  display: flex;
  flex-direction: row-reverse;
}
.mian-footer {
  padding: 10px 0 0 100px;
}
.flexTime {
  /deep/ .el-input {
    width: 100px;
  }
}
</style>
