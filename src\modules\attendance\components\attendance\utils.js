/**
 * 根据经纬度获取地址信息
 * @param {number} latitude 纬度
 * @param {number} longitude 经度
 * @returns {Promise<object>} 返回地址信息的 Promise
 */
export function getAddressInfo(latitude, longitude) {
  return new Promise((resolve, reject) => {
    const apiKey = 'TLEBZ-EZJWG-WX6QW-IOWRD-BT2EE-MSBFW'
    const output = 'jsonp'
    const url = `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=${apiKey}&output=${output}&get_poi=1&callback=handleGeocoderResponse`

    // 创建一个全局回调函数
    window.handleGeocoderResponse = function (response) {
      if (response.status === 0) {
        resolve(response.result)
      } else {
        reject(new Error(response.message || '获取地址信息失败'))
      }
      // 完成后删除回调函数以防内存泄漏
      delete window.handleGeocoderResponse
    }

    // 创建一个 script 标签并添加到 document
    const script = document.createElement('script')
    script.src = url
    script.onerror = function () {
      reject(new Error('请求出错'))
    }
    document.body.appendChild(script)

    // 设置超时
    setTimeout(() => {
      reject(new Error('请求超时'))
      delete window.handleGeocoderResponse
    }, 5000) // 5秒超时
  })
}
