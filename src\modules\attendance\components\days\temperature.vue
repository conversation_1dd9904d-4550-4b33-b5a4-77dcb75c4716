<template>
  <div class="temperature" ref="contain">
    <div class="filterBox" ref="searchForm">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="日期:">
          <el-date-picker
            v-model="date"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="changeTimeGetDate"
            format="yyyy 年 MM 月 dd 日"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions0"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="选择人员:">
          <el-select
            v-model="selectPerson"
            placeholder="活动区域"
            @change="handleSelect"
          >
            <el-option
              v-for="item in peopleList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="choosePeople + ':'"
          v-show="selectPerson == 'attendance'"
        >
          <el-button @click="openVisible = true" v-if="namelist.length > 0">
            <p class="className">已选择{{ namelist.length }}个考勤组</p>
            <p
          /></el-button>
          <el-button @click="openVisible = true" v-if="namelist.length == 0">
            + 请选择{{ choosePeople }}</el-button
          >
        </el-form-item>
        <el-form-item v-if="firstrowShow" v-show="selectPerson !== 'quit'">
          <div class="check">
            <el-checkbox v-model="checked">{{ description }}</el-checkbox>
          </div>
        </el-form-item>

        <div class="flex-box">
          <div class="left">
            <el-form-item v-if="!firstrowShow" v-show="selectPerson !== 'quit'">
              <div class="check">
                <el-checkbox v-model="checked">{{ description }}</el-checkbox>
              </div>
            </el-form-item>
            <el-form-item label="体温状态:">
              <el-select v-model="send.temperatureStatus">
                <el-option
                  v-for="(val, key) in temperatureStatus"
                  :key="key"
                  :value="key"
                  :label="val"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="最高体温:">
              <el-select
                style="width: 80px"
                v-model="send.minTemperature"
                placeholder=""
              >
                <el-option
                  v-for="item in temperatureRange"
                  :key="item"
                  :value="item"
                  :label="item + '°'"
                >
                </el-option>
              </el-select>
              ~
              <el-select
                style="width: 80px"
                v-model="send.maxTemperature"
                placeholder=""
              >
                <el-option
                  v-for="item in temperatureRange"
                  :key="item"
                  :value="item"
                  :label="item + '°'"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>

          <div class="right">
            <el-button type="primary" @click="handleQueryClick">查询</el-button>
            <el-button @click="handleResetClick">重置</el-button>
            <el-button
              v-if="
                privilegeVoList.includes(
                  'hrAttend.attendManage.dailyCount.exportTempOfDayByEmpIds'
                )
              "
              @click="getExportDay"
              >导出</el-button
            >
          </div>
        </div>
      </el-form>
    </div>
    <div class="statisticalList">
      <old-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        :headerData="headerData"
        @sort-change="sortChange"
        :isShowPagination="true"
        :pageOptions="pageOptions"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
      >
        <template slot="departmentNames" slot-scope="scope">
          <p class="tooltip">{{ scope.msg.row.departmentNames }}</p>
        </template>

        <template slot="workDate" slot-scope="scope">
          <p class="tooltip">{{ scope.msg.row.workDate }}</p>
        </template>
        <template slot="temperatureStatus" slot-scope="scope">
          <span
            :class="scope.msg.row.temperatureStatus == '正常' ? 'green' : 'red'"
            >{{ scope.msg.row.temperatureStatus }}</span
          >
        </template>

        <template
          v-for="(item, index) in tableTitles"
          :slot="item"
          slot-scope="scope"
        >
          <span
            v-if="index == 0"
            :class="[
              { green: scope.msg.row.maxTemperatureStatus == 1 },
              { red: scope.msg.row.maxTemperatureStatus == 2 },
            ]"
          >
            {{ scope.msg.row[item] ? scope.msg.row[item] : "--" }}
          </span>

          <span
            v-if="index == 1"
            :class="[
              { green: scope.msg.row.minTemperatureStatus == 1 },
              { red: scope.msg.row.minTemperatureStatus == 2 },
            ]"
          >
            {{ scope.msg.row[item] ? scope.msg.row[item] : "--" }}
          </span>

          <span
            v-if="index > 1"
            :class="[
              { green: scope.msg.row[item] && scope.msg.row[item].status == 1 },
              { red: scope.msg.row[item] && scope.msg.row[item].status == 2 },
            ]"
            >{{ scope.msg.row[item] && scope.msg.row[item].temperature }}</span
          >
        </template>
      </old-table>
    </div>
    <!-- 选择人员 -->
    <div class="choosePerson">
      <el-dialog
        v-if="openVisible"
        :title="choosePeople"
        :visible.sync="openVisible"
        width="600px"
      >
        <span class="dialogContent">
          <div class="left">
            <el-input
              v-model="inputValue"
              :placeholder="choosePeople"
              @input="search"
            ></el-input>
            <div class="left-content">
              <el-checkbox-group v-model="checkAttPerson">
                <el-checkbox
                  v-for="val in inputValue
                    ? filterAttendPersons
                    : attendPersons"
                  :key="val.id"
                  :label="val"
                  >{{ val.name }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
          </div>
          <i class="divider"></i>

          <ul class="right">
            <li v-for="item in checkAttPerson" :key="item.id">
              <span>{{ item.name }}</span>
              <i class="el-icon-close" @click="removeItem(item.id)"></i>
            </li>
          </ul>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="clearInputValue">取 消</el-button>
          <el-button type="primary" @click="userChecked(selectPerson)"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </div>
    <DayStatisFilterDialog ref="child"></DayStatisFilterDialog>
  </div>
</template>

<script>
import DayStatisFilterDialog from "../dayStatisFilterDialog";
import headerData from "../../util/headerData";
import { tableTitles } from "../../util/constData";
import { mapState } from "vuex";

const array2str = (arr) => {
  if (!arr || !arr.length) return "--";
  return arr.join("，");
};

function temperatureRange() {
  let j = 41,
    arr = [];
  for (let i = 10; i >= 0; i--) {
    arr.push(j);
    j -= 0.5;
  }
  return arr;
}

const sendInit = () => {
  return {
    attendIds: [],
    empIds: [],
    empStatus: ["ON_THE_JOB"],
    currPage: 1,
    pageSize: 10,
    isCompany: true, //是否为全公司
    outAttendGroup: true, //考勤组是否已选中
    taxSubIds: [], //用工主体ID
    minTemperature: "",
    maxTemperature: "",
    temperatureStatus: "2",
    endDate: "",
    startDate: "",
  };
};

export default {
  components: {
    DayStatisFilterDialog,
  },
  data() {
    return {
      tableTitles,
      temperatureStatus: {
        "": "全部",
        1: "正常",
        2: "异常",
      },
      send: sendInit(),
      temperatureRange: temperatureRange(),
      pickerOptions0: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6;
        },
      },

      loading: false,
      description: "离开考勤组的人员",
      exceptionTitle: "", // 异常状态标题
      pagination: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },

      inputValue: "",
      checkAttPerson: [],
      attendPersons: [], // 考勤组
      filterAttendPersons: [], // 过滤后的考勤组

      openVisible: false,
      selectPerson: "attendance",
      choosePeople: "考勤组",
      peopleList: [
        {
          label: "考勤组",
          value: "attendance",
        },
      ],

      rightList: [],

      checked: true,
      date: "",

      tableHeight: "",
      namelist: [], //名称数组
      headerData,
      tableData: [],

      pageOptions: {
        currPage: 1, //当前页码
        total: 10, //数据总数
        pageSize: 10, //每页显示条数
        pageSizes: [10, 20, 30, 40, 50, 100], //每页显示个数选择器选项设置
      },
      firstrowShow: document.body.clientWidth > 1320,
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  watch: {
    // 是否选中离职人员
    checked(val) {
      if (val) {
        this.send.outAttendGroup = true;
      } else {
        this.send.outAttendGroup = false;
      }
      this.send.empStatus = ["ON_THE_JOB"];
    },

    // 人员选择
    selectPerson(val) {
      this.choosePeople = "考勤组";
      this.description = "离开考勤组的人员";
      this.send.taxSubIds = [];
      this.checkAttPerson = [];
      this.send.isCompany = false;
      this.getAttendGroupList();
      this.checked = false;
    },
  },
  created() {
    this.getMouthEndStart();
    this.getAttendGroupList();
  },
  mounted() {
    this.getList();

    window.onresize = () => {
      return (() => {
        this.firstrowShow = document.body.clientWidth > 1320;
      })();
    };
  },

  methods: {
    // 获取当月月初和月末
    getMouthEndStart() {
      var nowDate = new Date();
      var cloneNowDate = new Date();
      var fullYear = nowDate.getFullYear();
      var month = nowDate.getMonth() + 1;
      var endOfMonth = new Date(fullYear, month, 0).getDate();
      function getFullDate(targetDate) {
        var D, y, m, d;
        if (targetDate) {
          D = new Date(targetDate);
          y = D.getFullYear();
          m = D.getMonth() + 1;
          d = D.getDate();
        } else {
          y = fullYear;
          m = month;
          d = date;
        }
        m = m > 9 ? m : "0" + m;
        d = d > 9 ? d : "0" + d;
        return y + "-" + m + "-" + d;
      }
      this.send.endDate = getFullDate(Date.now() - 8.64e6); //当月最后一天
      this.send.startDate = getFullDate(cloneNowDate.setDate(1)); //当月第一天
      this.date = [this.send.startDate, this.send.endDate];
    },
    handleSelect() {
      this.namelist = [];
    },
    // 树过滤
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    // 搜索树信息
    search() {
      this.filterAttendPersons = this.attendPersons.filter((v) =>
        v.name.includes(this.inputValue)
      );
    },

    handleResetClick() {
      this.pageOptions.currPage = 1;
      this.namelist = [];
      this.checkAttPerson = [];
      this.checked = false;
      this.send = sendInit();
      this.getMouthEndStart();

      this.getList();
    },
    handleQueryClick() {
      if (
        this.send.minTemperature &&
        this.send.maxTemperature &&
        this.send.minTemperature > this.send.maxTemperature
      )
        return this.$message.error(
          "最高体温查询范围不正确，最高值需要大于等于最小值"
        );
      this.pageOptions.currPage = 1;
      this.getList();
    },

    // 人员选择确定
    userChecked(val) {
      // this.pagination.currentPage = 1;
      this.send.attendIds = this.getUserSet(this.checkAttPerson, "id");
      this.namelist = this.getUserSet(this.checkAttPerson, "name");
      this.clearInputValue();
    },

    // 对话框关闭
    clearInputValue() {
      this.openVisible = false;
      this.inputValue = "";
    },

    // 人员选择获取人员id集合
    getUserSet(list, attr) {
      let newList = [];
      for (let i = 0; i < list.length; i++) {
        if (list[i].userResults) {
          newList = newList.concat(list[i].userResults.map((v) => v[attr]));
          continue;
        }
        newList = newList.concat(list[i][attr]);
      }
      return newList;
    },

    // 修改时间后更新数据
    changeTimeGetDate(newVal) {
      if (!newVal) {
        this.$message.error("请输入日期");
        return;
      }
      if (newVal) {
        if (
          new Date(newVal[1]).getTime() - new Date(newVal[0]).getTime() >
          30 * 24 * 3600 * 1000
        ) {
          this.$message({
            type: "error",
            message: "加班日期结束时间比开始时间不能多于31天, 请重新选择",
          });
          this.date = [];
          return;
        }
      }
      this.send.startDate = newVal[0];
      this.send.endDate = newVal[1];
    },

    //分页size切换
    handleSizeChange(val) {
      console.log("当前size", val);
      this.pageOptions.pageSize = val;
      this.pageOptions.currPage = 1;
      this.getList();
    },

    //页码切换
    handleCurrentChange(val) {
      console.log("当前页码", val);
      this.pageOptions.currPage = val;
      this.getList();
    },

    clickCheck(val, ischeck) {
      if (this.rightList.some((item) => item.id === val.id)) {
        this.removeItem(val.id);
      } else {
        this.rightList.push(val);
      }
    },

    // 将返回的后台数据拼接成规范的表格数据
    transformBaseData(list) {
      if ((list && !list.length) || !list) return;

      return list.map((it) => {
        it.departmentNames = array2str(it.departmentNames);
        it.workDate = it.workingShiftResult.workDate;
        it.temperatureStatus =
          this.temperatureStatus[it.temperatureStatus] || "--";

        if (it.signList.length) {
          const tip = ["", "first", "second", "third"];
          for (const el of it.signList) {
            it[
              `${
                tip[el.workingShiftOrder]
              }-${el.signTypeEnum.toLowerCase()}-signTime`
            ] = el.signTime || "--";
            it[
              `${
                tip[el.workingShiftOrder]
              }-${el.signTypeEnum.toLowerCase()}-temperature`
            ] = {
              temperature: el.temperature ? el.temperature : "--",
              status: el.temperatureStatus || 0,
            };
          }
        }
        return it;
      });
    },

    // 获取考勤组数据
    async getAttendGroupList() {
      const { data } = await this.$attApi.getAttendGroupList({
        onlyEnableFaceOcr: true,
      });
      this.attendPersons = data.organizeAndUserResults;
    },

    //体温监测列表
    async getList() {
      this.loading = true;
      this.send = {
        ...this.send,
        currPage: this.pageOptions.currPage,
        pageSize: this.pageOptions.pageSize,
      };

      try {
        const { data, success } = await this.$attApi.getTemperatureApi(
          this.send
        );

        if (success) {
          this.pageOptions.total = data.total;
          this.tableData = this.transformBaseData(data.records);
        }
      } finally {
        this.loading = false;
      }
    },

    removeItem(id) {
      this.checkAttPerson = this.checkAttPerson.filter(
        (item) => item.id !== id
      );
    },

    // 排序事件
    sortChange({ column, prop, order }) {
      // console.log(column, prop, order);
      // this.send.asc = order == "ascending" ? true : false;
      // this.send.orderBy = prop;
      // this.handleQueryClick();
    },

    currnetChange(val, node) {},

    //导出
    async getExportDay() {
      if (!this.date) {
        this.$message.error("请选择导出日期");
        return;
      }

      await this.$attApi.exportTemperatureApi(this.send).then((res) => {
        let content = res;
        let blob = new Blob([content], { type: "application/vnd.x-xlsx" });
        if ("download" in document.createElement("a")) {
          const link = document.createElement("a");
          link.download =
            "每日体温汇总-" +
            this.date[0].split("-").join("") +
            "-" +
            this.date[1].split("-").join("") +
            "考勤统计.xlsx";
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          document.body.appendChild(link);
          link.click();
          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);
        } else {
          navigator.msSaveBlob(blob);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.temperature {
  .filterBox {
    padding: 22px 20px 0 20px;
    .flex-box {
      display: flex;
      justify-content: space-between;
    }
  }
  /deep/ .el-range-editor.el-input__inner input {
    width: 130px;
  }
  .statisticalList {
    padding: 0 22px;
    position: relative;
    .el-table--scrollable-x .el-table__body-wrapper {
      overflow-x: auto;
      z-index: 9999;
    }

    /deep/ .el-dialog__body {
      padding-top: 10px;
    }
  }
  .dialogContent {
    .divider {
      width: 1px;
      height: 68%;
      background: #ddd;
      position: absolute;
      left: 50%;
      top: 16%;
    }
    .el-input {
      width: 240px;
      height: 40px;
      padding-bottom: 10px;
    }
    .el-icon-search {
      position: relative;
      right: 30px;
      color: #909399;
    }
    .left {
      height: 270px;
      width: 280px;
      .left-content {
        height: 222px;
        overflow-y: auto;
        overflow-x: hidden;
      }
      /deep/ .el-checkbox-group {
        display: flex;
        flex-direction: column;
        .el-checkbox {
          padding-bottom: 5px;
          display: flex;
          align-items: center;
          .el-checkbox__label {
            width: 230px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
      .show-ellipsis {
        display: block;
        width: 180px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .right {
      width: 250px;
      height: 270px;
      overflow: auto;
      li {
        position: relative;
        height: 30px;
        line-height: 30px;
        background: #d9eafc;
        padding: 0 20px 0 5px;
        margin-bottom: 5px;
        width: 220px;
        white-space: nowrap;
        overflow-x: hidden;
        text-overflow: ellipsis;
        .el-icon-close {
          position: absolute;
          right: 5px;
          top: 8px;
          color: #909399;
          cursor: pointer;
        }
      }
    }
  }

  .className {
    width: 200px;
    overflow: hidden;
  }
  .choosePerson {
    /deep/ .el-dialog {
      height: 400px;
    }
    /deep/ .el-dialog__body {
      padding: 10px 20px 30px;
      height: 244px;
    }
    /deep/ .dialog-footer {
      position: absolute;
      display: flex;
      flex-direction: row;
      bottom: 10px;
      right: 20px;
    }
  }

  .tooltip {
    line-height: 50px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
  .red {
    color: #d6342a;
  }
  .green {
    color: #41bd5a;
  }
}
</style>
