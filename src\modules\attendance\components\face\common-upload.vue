<template>
  <div
    class="common-upload"
    :class="fileListLength == limit || !isEdit ? 'upload-disable' : ''"
  >
    <el-upload
      ref="upload"
      :action="imageUrl"
      :headers="headerToken"
      list-type="picture-card"
      accept=".jpg,.jpeg,.png"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-change="handleChange"
      :on-error="handleError"
      :limit="limit"
      :style="{ width: providewidth, height: provideheight }"
      drag
      :on-exceed="handleExceed"
    >
      <div>
        <div class="update-limit">
          <img src="../../../../assets/images/pic_upload.png" alt="" />
        </div>
      </div>

      <div slot="file" slot-scope="{ file }">
        <div>
          <div class="update-limit other-limit">
            <img
              :style="{ width: providewidth, height: provideheight }"
              class="el-upload-list__item-thumbnail"
              :src="file.url"
              alt=""
            />
          </div>
        </div>
        <span class="el-upload-list__item-actions">
          <span
            class="el-upload-list__item-preview"
            @click="handlePictureCardPreview(file)"
          >
            <i class="el-icon-view"></i>
          </span>

          <span
            v-if="isEdit"
            class="el-upload-list__item-delete"
            @click="handleRemove(file)"
          >
            <i class="el-icon-delete"></i>
          </span>
        </span>
      </div>
    </el-upload>

    <el-dialog
      append-to-body
      width="740px"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
    >
      <div class="show-image">
        <img :src="dialogImageUrl" alt />
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { getToken } from "@olading/olading-business-ui";
import { baseUrl } from "@/request/fetch";
export default {
  name: "commonUpload",
  props: {
    title: {
      type: String,
    },
    index: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
    isEdit: {
      type: Boolean,
    },

    imageUrl: {
      type: String,
      default: baseUrl + "/api/merchant/archive/upload2",
    },
    providewidth: {
      default: "176px",
    },
    provideheight: {
      default: "210px",
    },
  },
  data() {
    return {
      dialogVisible: false,
      fileListLength: 0,
      limit: 1,
      dialogImageUrl: "",
      headerToken: {
        Authorization: getToken(),
      },
    };
  },
  watch: {
    fileList(val) {
      this.fileListLength = val.length;
    },
  },
  created() {
    this.fileListLength = this.fileList.length;
  },

  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },

    handleRemove(file) {
      let list = this.fileList.filter((v) => v.fileId !== file.fileId);
      this.$emit("handleRemove", file, list, this.index);
    },

    beforeUpload(file) {
      console.log(1);
      let _this = this;
      return new Promise((resolve, reject) => {
        const isImg =
          file.type === "image/jpeg" ||
          file.type === "image/jpg" ||
          file.type === "image/png";
        let arr = file.name.split(".");

        const isSize = file.size / 1024 / 1024 < 10;
        if (!isImg) {
          this.$message.error("上传失败，仅支持JPG、PNG、JPEG格式的图片!");
          this.fileListLength = 0;
          reject();
        }

        if (!isSize) {
          this.$message.error("上传图片大小不能超过10MB");
          this.fileListLength = 0;
          reject();
        }

        if (
          arr[arr.length - 1] === "jpg" ||
          arr[arr.length - 1] === "png" ||
          arr[arr.length - 1] === "jpeg"
        ) {
          let image = new Image(),
            resultBlob = "";
          image.src = URL.createObjectURL(file);
          image.onload = () => {
            // 调用方法获取blob格式，方法写在下边
            resultBlob = _this.compressUpload(image);
            resolve(resultBlob);
          };
          image.onerror = () => {
            reject();
          };
        } else {
          resolve();
        }
      });
    },

    /* 图片压缩方法-canvas压缩 */
    compressUpload(image) {
      let canvas = document.createElement("canvas");
      let ctx = canvas.getContext("2d");
      let { width } = image,
        { height } = image;
      canvas.width = width;
      canvas.height = height;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(image, 0, 0, width, height);

      // 进行最小压缩0.1
      let compressData = canvas.toDataURL("image/jpeg", 0.1);

      // 压缩后调用方法进行base64转Blob，方法写在下边
      let blobImg = this.dataURItoBlob(compressData);
      return blobImg;
    },

    /* base64转Blob对象 */
    dataURItoBlob(data) {
      let byteString;
      if (data.split(",")[0].indexOf("base64") >= 0) {
        byteString = atob(data.split(",")[1]);
      } else {
        byteString = unescape(data.split(",")[1]);
      }
      let mimeString = data.split(",")[0].split(":")[1].split(";")[0];
      let ia = new Uint8Array(byteString.length);
      for (let i = 0; i < byteString.length; i += 1) {
        ia[i] = byteString.charCodeAt(i);
      }
      return new Blob([ia], { type: mimeString });
    },

    handleChange(file, fileList) {
      this.fileListLength = fileList.length;
    },

    handleSuccess(res, file, fileList) {
      file.fileId = file.response.data.archiveId;
      this.$emit("handleSuccess", res.data, file, fileList, this.index);
    },

    handleError() {
      this.$message.error("文件上传失败");
    },

    handleExceed(files, fileList) {
      this.$message.warning(`本次上传限制为${this.limit}个文件`);
    },
  },
};
</script>
<style lang="scss" scoped>
// @import "../../../assets/scss/helpers.scss";
.common-upload {
  height: auto;
  display: flex;

  ::v-deep {
    .el-upload-list__item,
    .el-upload {
      // width: 176px;
      // height: 210px;
      display: inline-block;
    }
    .el-upload-list__item {
      transition: none !important;
    }
    .el-upload--picture-card {
      border: 1px dashed #c0ccda;
      background: #fff;
      .el-upload-dragger {
        width: 100%;
        height: 100%;
        border: none;
      }
    }
    .upload-special-title {
      height: 24px;
      line-height: 24px;
      font-weight: 500;
      font-size: 14px;
      color: #6a6f7f;
      &.other-title {
        display: inline-block;
        width: 100%;
        line-height: 54px;
        text-align: center;
      }
    }
    .upload-special-other-title {
      font-weight: 500;
      font-size: 14px;
      color: #6a6f7f;
    }
    .el-tooltip {
      height: 100%;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
    }
    .update-limit {
      width: 100%;
      height: 100%;
      // border: 1px dashed #cccccc;
      border-radius: 8px;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .p-content {
    margin-bottom: 10px;
  }
}
.show-image {
  margin: 0 auto;
  width: 300px;
  img {
    width: 100%;
    height: 100%;
  }
}
.upload-disable {
  /deep/.el-upload--picture-card {
    display: none;
  }
}
</style>
