<template>
  <el-dialog width="540px" :visible.sync="show" :close-on-click-modal="false">
    <div class="show-image">
      <img :src="url" alt />
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: ["url"],
  data() {
    return {
      show: false,
      detail: {},
    };
  },
};
</script>

<style lang="scss" scoped>
.show-image {
  margin: 0 auto;
  width: 300px;
  height: 370px;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
