<template>
  <el-dialog title="修改状态" :visible.sync="show" width="740px">
    <div class="modify">
      <div v-if="detail.idCard" class="modify-left">
          <common-upload
          :fileList="[{ url: detail.idCard }]"
          :isEdit="false"
          providewidth="150px"
          provideheight="95px"
        ></common-upload>

        
      </div>

      <div class="modify-middle" :class="detail.idCard ? 'm-r-50' : 'm-r-80'">
        <!-- <img :src="detail.faceUrl" alt="" /> -->
        <common-upload
          :fileList="[{ url: detail.faceUrl }]"
          :isEdit="false"
          :providewidth="detail.idCard ? '180px' : '210px'"
          :provideheight="detail.idCard ? '220px' : '260px'"
        ></common-upload>
      </div>
      <div class="modify-right">
        <table>
          <tr v-for="it in table" :key="it.key">
            <td :class="{ 'm-t-30': it.sty }">{{ it.label }}</td>
            <td :class="{ 'm-t-30': it.sty }">
              <span
                :class="{
                  red: it.key == 'faceStatus' && detail[it.key] == '异常',
                }"
                >{{ detail[it.key] }}</span
              >
            </td>
          </tr>
        </table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleConfirmClick(false)">核验不通过</el-button>
      <el-button type="primary" @click="handleConfirmClick(true)"
        >核验通过</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import { debounce } from "../../util/debounce";

import commonUpload from "./common-upload";

export default {
  components: {
    commonUpload,
  },

  data() {
    return {
      show: false,
      detail: {},
      table: [
        { label: "员工", key: "empName" },
        { label: "考勤组", key: "agName" },
        { label: "部门", key: "departmentNames" },
        { label: "岗位", key: "postNames" },
        { label: "上级", key: "reportTos" },
        { label: "人脸状态", key: "faceStatus", sty: true },
        { label: "录入时间", key: "uploadTime" },
      ],
    };
  },
  watch: {
    detail(val){
      console.log(val)
    }
  },


  methods: {
    handleConfirmClick: debounce(
      async function (type) {
        const send = {
          id: this.detail.id,
          pass: type,
        };
        const { success, message } = await this.$attApi.updateCheckStatusApi(
          send
        );
        if (success) {
          this.show = false;
          this.$emit("updateList");
        }
      },
      1000,
      true
    ),
  },
};
</script>
<style lang="scss" scoped>
.modify {
  display: flex;
  justify-content: center;
  padding: 50px 0;
  &-left {
    width: 220px;
    height: 220px;
    margin-right: 15px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;

    background: rgba(247, 248, 250, 0.7);
  }

  &-middle {
    img {
      border-radius: 4px;
      width: 100%;
      height: 100%;
    }
  }
  .m-r-50 {
    width: 180px;
    height: 220px;
    margin-right: 45px;
  }
  .m-r-80 {
    width: 210px;
    height: 260px;
    margin-right: 80px;
  }

  &-right {
    h1 {
      font-size: 14px;
    }
    table {
      width: 250px;
      font-size: 14px;
      .m-t-30 {
        padding-top: 30px;
      }
      td {
        padding-bottom: 12px;
      }
      td:first-child {
        color: #6a6f7f;
        text-align: left;
        width: 80px;
      }
      td:last-child {
        color: #070f29;
      }
    }
    .idCard {
      width: 280px;
      height: 120px;
      border: 1px solid #687087;
    }
  }
}
.red {
  color: #d6342a;
}

/deep/.el-dialog__footer {
  border-top: 1px solid #eaeaea;
}
</style>

