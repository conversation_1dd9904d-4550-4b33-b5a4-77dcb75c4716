<template>
  <el-dialog :visible.sync="show" width="400px" class="send">
    <h1><i class="el-icon-warning"></i>确定发送录入通知吗？</h1>

    <p class="content">
      系统将给考勤人脸信息异常的员工发送小程序通知，本次将通知{{
        count
      }}人，请提前通知员工登录小程序。
    </p>

    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="show = false">取消</el-button>
      <el-button size="mini" type="primary" @click="handleConfirmClick"
        >确认</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import { debounce } from "../../util/debounce";
export default {
  data() {
    return {
      count: 0,
      show: false,
      detail: {},
    };
  },

  mounted() {},
  methods: {
    handleConfirmClick: debounce(
      async function () {
        const { success, message } = await this.$attApi.sendNotifyAllApi();
        if (success) {
          this.$message.success("通知员工录入成功");
          this.show = false;
          this.$emit("updateList");
        }
      },
      1000,
      true
    ),
  },
};
</script>
<style lang="scss" scoped>
/deep/.el-dialog__header {
  display: none;
}
.send {
  h1 {
    color: #070f29;
    font-size: 16px;
    margin-bottom: 20px;
    .el-icon-warning {
      color: #e6a23c;
      font-size: 18px;
    }
  }
  .content {
    color: #606266;
    font-size: 14px;
  }
}
</style>
