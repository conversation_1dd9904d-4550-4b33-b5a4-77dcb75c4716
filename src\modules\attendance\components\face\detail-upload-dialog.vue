<template>
  <el-dialog
    title="上传人脸照片"
    :visible.sync="show"
    width="740px"
    :before-close="handleCancelClick"
  >
    <div class="upload">
      <p>员工：{{ detail.empName }}（{{ detail.departmentNames }}）</p>
      <common-upload
        class="upload-img"
        :fileList="fileList"
        :isEdit="true"
        @handleSuccess="handleSuccess"
        @handleRemove="handleRemove"
      ></common-upload>
      <p><i>*</i> 请上传五官清晰的正面照片</p>
      <p class="tips">支持JPG、JPEG、PNG格式的图片，大小不得超过10MB</p>
      <!-- <p>
        <i>*</i> 人脸状态：
        <el-radio-group v-model="pass">
          <el-radio :label="true">通过</el-radio>
          <el-radio :label="false">不通过</el-radio>
        </el-radio-group>
      </p> -->
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancelClick">取 消</el-button>
      <el-button type="primary" @click="handleConfirmClick">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import commonUpload from "./common-upload.vue";
import { debounce } from "../../util/debounce";
import { baseUrl } from "@/request/fetch";
export default {
  components: {
    commonUpload,
  },

  data() {
    return {
      baseUrl,
      show: false,
      limit: 1,
      pass: true,
      fileList: [],
      detail: {},
      businessLicense: require("../../../../assets/images/taxImg.png"),
    };
  },
  // watch: {
  //   detail: {
  //     handler(newVal, oldVal) {
  //       console.log(newVal);
  //     },
  //     deep: true,
  //   },
  // },

  mounted() {
    console.log(this.detail);
  },
  methods: {
    handleSuccess(data) {
      console.log("file", data);
      this.fileList = [data];
    },
    handleRemove(file, fileList) {
      this.fileList = [];
    },
    handleConfirmClick: debounce(
      async function () {
        if (!this.fileList.length) return this.$message.error("请上传正面照！");
        const send = {
          pass: this.pass,
          id: this.detail.id,
          faceID: this.fileList[0].archiveId,
          faceUrl: this.fileList[0].url,
        };
        const { success, message } = await this.$attApi.uploadFaceApi(send);
        if (success) {
          this.handleCancelClick();
          this.$emit("updateList");
        }
      },
      1000,
      true
    ),

    handleCancelClick() {
      this.fileList = [];
      this.pass = true;
      this.show = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  &-img {
    margin: 20px 0;
  }
  p {
    font-size: 14px;
    color: #070f29;
    i {
      color: #d6342a;
    }
  }
  .tips {
    font-size: 12px;
    color: #888888;
    margin: 10px auto 30px;
  }
}
/deep/.el-dialog__footer {
  border-top: 1px solid #eaeaea;
}
</style>
