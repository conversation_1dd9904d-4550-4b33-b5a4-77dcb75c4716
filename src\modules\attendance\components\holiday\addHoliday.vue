<template>
  <div class="addHoliday">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="goBack" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>{{ holidayTitle }}</span>
        </el-col>
      </el-row>
    </header>
    <div class="content">
      <div :class="{ viewPage: editDisabled }">
        <el-form ref="form" :model="form" :rules="rules" label-width="140px">
          <el-form-item label="假期名称" prop="leaveName">
            <div v-if="!editDisabled">
              <el-input
                v-model.trim="form.leaveName"
                placeholder="请输入假期名称"
              ></el-input>
              <span class="hint-info">最多30个字符（中英文或数字）</span>
            </div>
            <span v-else>{{ form.leaveName }}</span>
          </el-form-item>
          <h3 class="title">基本信息：</h3>
          <el-form-item label="适用范围">
            <el-select v-if="!editDisabled" v-model="form.type" :popper-append-to-body="false" :class="editDisabled ?'clear-arrows':''">
              <el-option label="全公司" value="COMPANY"></el-option>
            </el-select>
            <span v-else>{{ form.type === 'COMPANY' ? '全公司' : '' }}</span>
          </el-form-item>
          <el-form-item label="新员工请假">
            <el-select v-if="!editDisabled" v-model="form.whenCanLeave" :popper-append-to-body="false" :class="editDisabled ?'clear-arrows':''">
              <el-option label="入职即可请假" value="ENTRY"></el-option>
              <el-option
                label="转正后才可请假"
                value="REGULAR_WORKER"
              ></el-option>
              <el-option label="自定义" value="FREE"></el-option>
            </el-select>
            <span v-else>{{ form.whenCanLeave == 'ENTRY' ? '入职即可请假' : form.whenCanLeave === 'REGULAR_WORKER' ? '转正后才可请假' : '自定义' }}</span>
          </el-form-item>
          <el-form-item v-if="form.whenCanLeave === 'FREE'">
            <div class="freeTime">
              <span>入职时长＜</span>
              <el-select
                v-model="form.whenCanLimit"
                :popper-append-to-body="false"
                :disabled="editDisabled"
              >
                <el-option
                  v-for="item in options"
                  :label="item.name"
                  :value="item.value"
                  :key="item.value"
                ></el-option>
              </el-select>
              <span>时，不可申请此假期</span>
            </div>
          </el-form-item>
          <h3 class="title">请假时长核算规则：</h3>
          <el-form-item label="最小请假单位">
            <div v-if="!editDisabled">
              <el-select v-model="form.leaveUnit" :popper-append-to-body="false" :class="editDisabled ?'clear-arrows':''">
                <el-option label="天" value="DAY"></el-option>
                <el-option label="半天" value="HALF_DAY"></el-option>
                <el-option label="小时" value="HOUR"></el-option>
              </el-select>
              <span class="hint-info" v-if="form.leaveUnit == 'DAY'"
                >员工以天为最小单位选择时间，考勤报表按天</span
              >
              <span class="hint-info" v-if="form.leaveUnit == 'HALF_DAY'"
                >员工以半天为最小单位选择时间，考勤报表按半天</span
              >
              <span class="hint-info" v-if="form.leaveUnit == 'HOUR'"
                >员工以小时为最小单位选择时间，考勤报表按小时</span
              >
            </div>
            <span v-else>{{ form.leaveUnit === 'DAY' ? '天' : form.leaveUnit === 'HALF_DAY' ? '半天' : '小时' }}</span>
          </el-form-item>
          <!-- <el-form-item v-if="form.leaveUnit == 'HALF_DAY'" label="统计方式">
            <el-select v-model="form.statisticsWay" disabled>
              <el-option label="天" value="DAY"></el-option>
            </el-select>
            <span class="hint-info">月度统计展示单位：按小时/天 显示</span>
          </el-form-item> -->
          <el-form-item label="请假时长核算">
            <div v-if="!editDisabled">
              <el-select
                v-model="form.leaveCountType"
                :popper-append-to-body="false"
                :class="editDisabled ?'clear-arrows':''"
              >
                <el-option
                  label="按工作日计算请假时长"
                  value="WORK_DAY"
                ></el-option>
                <el-option
                  label="按自然日计算请假时长"
                  value="NATURAL_DAY"
                ></el-option>
              </el-select>
              <span class="hint-info" v-if="form.leaveCountType === 'WORK_DAY'"
                >请假时段中不包含员工的休息日，例如未排班的双休日、法定节假日</span
              >
              <span class="hint-info" v-if="form.leaveCountType === 'NATURAL_DAY'"
                >请假时段中包含的休息日，也会计入请假天数</span
              >
            
            </div>
            <span v-else>{{ form.leaveCountType === 'WORK_DAY' ? '按工作日计算请假时长' : '按自然日计算请假时长' }}</span>
          </el-form-item>
          <h3 class="title">
            设置员工假期余额
            <el-switch v-model="isSetBalance" :disabled="isEdit"></el-switch>
          </h3>
          <el-form-item label="余额类型">
            {{ isSetBalance ? "限制余额" : "不限制余额" }}
          </el-form-item>
          <div v-if="isSetBalance">
            <el-form-item label="余额发放方式">
              <el-select
                v-model="form.howToRelease"
                :popper-append-to-body="false"
                @change="methodOfBalance"
                :disabled="editDisabled"
              >
                <el-option label="每年自动发放一次" value="YEAR"></el-option>
                <el-option label="每月自动发放一次" value="MONTH"></el-option>
                <el-option label="手动发放" value="MANUAL"></el-option>
                <el-option
                  label="加班时长自动放入余额用于调休"
                  value="WORK_TIME"
                  @click="leaveTip"
                  :disabled="isDisabled"
                ></el-option>
                <el-tooltip
                  v-if="isDisabled"
                  class="item"
                  effect="dark"
                  content="此规则只能显示在一个假期上"
                  placement="top"
                >
                  <span class="el-icon-warning-outline tip-icon"></span>
                </el-tooltip>
              </el-select>
              <span class="hint-info" v-if="form.howToRelease == 'YEAR'"
                >自动按每年固定时间发放假期余额</span
              >
              <span class="hint-info" v-if="form.howToRelease == 'MONTH'"
                >自动按每月固定时间发放假期余额</span
              >
              <span class="hint-info" v-if="form.howToRelease == 'MANUAL'"
                >保存成功后，可在假期余额中手动导入余额</span
              >
              <span class="hint-info" v-if="form.howToRelease == 'WORK_TIME'"
                ><span>已设置加班时长计算规则</span>
                <span class="viewOvertime">
                <el-button
                  type="text"
                  @click="goLook"
                  >查看</el-button
                >
              </span></span
              >
            </el-form-item>
            <el-form-item label="发放日期" v-if="overTime">
              <div
                class="smallInput"
                v-if="
                  form.howToRelease === 'MONTH' ||
                    form.howToRelease === 'WORK_TIME'
                "
              >
                每月<el-input v-model="form.whenToRelease" :disabled="editDisabled"></el-input>号
              </div>
              <div>
                <el-select
                  v-model="form.whenReleaseType"
                  v-if="form.howToRelease === 'YEAR'"
                  :popper-append-to-body="false"
                  :disabled="editDisabled"
                >
                  <el-option label="每年1月1日" value="NEW_YEAR_DAY"></el-option>
                  <!-- <el-option label="每年员工入职日" value="ENTRY_DAY"></el-option>
                      <el-option label="自定义" value="YEAR_FREE"></el-option> -->
                </el-select>
                <!-- <div
                  class="dateInput"
                  v-if="form.whenReleaseType === 'YEAR_FREE'"
                >
                  <span>每年</span>
                  <el-date-picker
                    v-model="form.whenToRelease"
                    format="MM-dd"
                    value-format="MM-dd"
                    placeholder="选择日期"
                  >
                  </el-date-picker>
                  <span>发放</span>
                </div> -->
              </div>
            </el-form-item>
            <el-form-item label="额度配置规则" v-if="overTime">
              <el-select
                v-model="form.leaveRuleType"
                placeholder="请选择活动区域"
                :popper-append-to-body="false"
                @change="toggleArea"
                :disabled="editDisabled"
              >
                <el-option label="固定额度" value="FIX"></el-option>
                <el-option
                  label="按社会工龄（参加工作总年限）"
                  value="WORK_YEAR"
                  v-show="form.howToRelease === 'YEAR'"
                ></el-option>
                <el-option
                  label="按司龄（在本公司服务年限）"
                  value="ENTRY_YEAR"
                  v-show="form.howToRelease === 'YEAR'"
                ></el-option>
                <el-option
                  label="社会工龄配额与司龄配额相加"
                  value="BOTH_SUM"
                  v-show="form.howToRelease === 'YEAR'"
                ></el-option>
                <el-option
                  label="取社会工龄配额与司额配额的较大值"
                  value="BOTH_MAX"
                  v-show="form.howToRelease == 'YEAR'"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="工龄配额"
              v-if="
                form.leaveRuleType == 'WORK_YEAR' ||
                  form.leaveRuleType == 'BOTH_SUM' ||
                  form.leaveRuleType == 'BOTH_MAX'
              "
            >
              <ul class="entryBalance">
                <li
                  v-for="(item, index) in workBalList"
                  :key="item"
                  :class="{ entryOne: index === 1 }"
                >
                  工龄{{ index === 0 ? "＜" : "≥"
                  }}{{ index === 0 ? "1" : index }}年，享有<el-input
                    v-model="workBalInput[index]"
                    :disabled="editDisabled"
                  ></el-input
                  >{{ form.leaveUnit === "HOUR" ? "小时" : "天" }}假期
                  <span v-if="index + 1 === workBalList">
                    <el-button type="text" @click="addWorkBal">新增</el-button>
                    <el-button
                      type="text"
                      @click="delWorkBal"
                      v-show="workBalList > 1"
                      >删除</el-button
                    >
                  </span>
                </li>
              </ul>
            </el-form-item>
            <el-form-item
              label="司龄配额"
              v-if="
                form.leaveRuleType == 'ENTRY_YEAR' ||
                  form.leaveRuleType == 'BOTH_SUM' ||
                  form.leaveRuleType == 'BOTH_MAX'
              "
            >
              <ul class="entryBalance">
                <li
                  v-for="(item, index) in entryBalList"
                  :key="item"
                  :class="{ entryOne: index === 1 }"
                >
                  司龄{{ index === 0 ? "＜" : "≥"
                  }}{{ index === 0 ? "1" : index }}年，享有<el-input
                    v-model="entryBalInput[index]"
                    :disabled="editDisabled"
                  ></el-input
                  >{{ form.leaveUnit === "HOUR" ? "小时" : "天" }}假期
                  <span v-if="index + 1 === entryBalList">
                    <el-button type="text" @click="addEntryBal">新增</el-button>
                    <el-button
                      type="text"
                      @click="delEntryBal"
                      v-show="entryBalList > 1"
                      >删除</el-button
                    >
                  </span>
                </li>
              </ul>
            </el-form-item>
            <el-form-item
              :label="form.leaveRuleType === 'FIX' ? '额度' : ''"
              v-if="overTime"
            >
              <div class="smallInput" v-if="form.howToRelease === 'YEAR'">
                <div v-show="form.leaveRuleType === 'FIX'">
                  <span v-show="form.leaveRuleType === 'FIX'">每人每年发放 </span>
                  <el-input
                    v-model="form.releaseBalance"
                    @blur="verifyRelease(form.releaseBalance)"
                    :disabled="editDisabled"
                  ></el-input>
                  <span>{{ form.leaveUnit === "HOUR" ? "小时" : "天" }}</span>
                </div>
                <el-checkbox
                  v-model="form.lessThanOneYearRound"
                  v-if="form.howToRelease === 'YEAR'"
                  :disabled="editDisabled"
                  >按实际工作时间时长发放余额</el-checkbox
                >
                <p v-show="form.lessThanOneYearRound" class="tips">
                  例如：员工全年有 20 天假期，当实际工作了 31
                  天时，可用的假期余额为 1 天（计算方式为 31/365*20=1.7）
                </p>
              </div>
              <div
                class="smallInput"
                v-if="
                  form.howToRelease === 'MONTH' ||
                    form.howToRelease === 'WORK_TIME'
                "
              >
                <span>每人每月发放</span>
                <el-input
                  v-model="form.releaseBalance"
                  @blur="verifyRelease(form.releaseBalance)"
                  :disabled="editDisabled"
                ></el-input>
                <span>{{ form.leaveUnit === "HOUR" ? "小时" : "天" }}</span>
              </div>
            </el-form-item>
            <el-form-item
              label="发放规则"
              v-show="form.howToRelease === 'WORK_TIME'"
            >
              加班时长自动计入调休余额
            </el-form-item>
            <el-form-item label="有效期">
              <el-select
                v-model="form.expireDateType"
                placeholder=""
                @change="overTimeArea"
                :popper-append-to-body="false"
                :disabled="editDisabled"
              >
                <!-- <el-option
                  label="自发放日起一周年"
                  value="ONE_YEAR"
                  v-show="form.howToRelease === 'YEAR'"
                ></el-option> -->
                <el-option
                  label="自发放日起一个月"
                  value="ONE_MONTH"
                  v-show="form.howToRelease === 'MONTH'"
                ></el-option>
                <el-option
                  label="按发放日起12个月"
                  value="ONE_YEAR"
                  v-show="
                    form.howToRelease === 'MANUAL' || form.howToRelease === 'YEAR'
                  "
                ></el-option>
                <el-option
                  label="按自然年(1月1日-12月31日)"
                  value="NATURAL_YEAR"
                  v-show="form.howToRelease === 'MANUAL'"
                ></el-option>
                <el-option
                  label="每年固定时间作废"
                  value="YEAR_FIX_DATE"
                  v-show="form.howToRelease === 'WORK_TIME'"
                ></el-option>
                <el-option
                  label="加班多少天后作废"
                  value="OVER_TIME_DATE"
                  v-show="form.howToRelease === 'WORK_TIME'"
                ></el-option>
              </el-select>
              <div
                class="dateInput"
                v-show="form.expireDateType === 'YEAR_FIX_DATE'"
              >
                每年
                <el-select v-model="everyMonth" @change="toggleOption" :disabled="editDisabled">
                  <el-option
                    v-for="val in 12"
                    :key="val"
                    :label="val + '月'"
                    :value="val"
                  ></el-option>
                </el-select>
                <el-select v-model="everyDate" @change="toggleOption" :disabled="editDisabled">
                  <el-option
                    v-for="val in dateTotal"
                    :key="val"
                    :label="val + '日'"
                    :value="val"
                  ></el-option>
                </el-select>
                后作废
              </div>
              <div
                class="dateInput"
                v-show="form.expireDateType === 'OVER_TIME_DATE'"
              >
                加班<el-input v-model="form.expireDateValue" :disabled="editDisabled"></el-input>天后作废
              </div>
              <el-checkbox
                v-model="form.postponeDayChecked"
                v-show="form.expireDateType !== 'OVER_TIME_DATE'"
                :disabled="editDisabled"
                >允许延长有效期</el-checkbox
              >
              <div
                class="balances"
                v-show="
                  form.postponeDayChecked &&
                    form.expireDateType !== 'OVER_TIME_DATE'
                "
              >
                <span>超过有效期后，余额保留 </span>
                <el-input
                  v-model="form.postponeDay"
                  @blur="verifyPoneDay(form.postponeDay)"
                  :disabled="editDisabled"
                ></el-input>
                <el-select
                  v-model="form.postponeTimeUnit"
                  :popper-append-to-body="false"
                  @change="toggleOption"
                  :disabled="editDisabled"
                >
                  <el-option label="月" value="MONTH"></el-option>
                  <el-option label="天" value="DAY"></el-option>
                </el-select>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>

    <div class="footer" v-show="!editDisabled">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </div>
  </div>
</template>

<script>
import { checkHoliday } from "../../util/validate";
import { debounce } from "../../util/debounce";
export default {
  data() {
    return {
      everyMonth: 12,
      everyDate: 1,
      overTimeDate: 1, //加班多少天作废
      currentYear: new Date().getFullYear(), //获取当前年份
      releaseAble: true, //校验余额输入是否合法
      poneDayAble: true, //校验余额输入是否合法
      holidayTitle: "新增假期", //title
      editDisabled: false, //是否禁用假期类型编辑页面
      isDisabled: false, //是否禁选加班时长用于调休
      entryBalList: 7, //司龄配额列表
      workBalList: 7, //工龄配额列表
      entryBalInput: [5, 6, 7, 8, 9, 10, 11], //司龄输入值
      workBalInput: [5, 6, 7, 8, 9, 10, 11], //工龄输入值
      value1: "",
      isEdit: false, //是否可编辑（设置假期余额）
      isSetBalance: false, //是否设置假期余额
      form: {
        whenCanLimit: 0, // 自定义
        leaveName: "", //假期名称
        type: "COMPANY", //适用范围
        whenCanLeave: "ENTRY", //新员工请假
        leaveUnit: "DAY", //最小请假单位
        statisticsWay: "DAY",
        leaveCountType: "WORK_DAY", //请假时长核算
        howToRelease: "YEAR", //余额发放方式
        whenToRelease: "1", //发放日期
        leaveRuleType: "FIX", //额度配置规则
        releaseBalance: 1, //额度
        expireDateType: "ONE_YEAR", //有效期
        postponeDayChecked: false, //允许延长有效期
        userBalanceType: 1, //是否设置假期余额
        lessThanOneYearRound: false, //是否按实际工作时长发放
        postponeDay: 0, //延期余额
        postponeTimeUnit: "DAY", //延期单位
        whenReleaseType: "NEW_YEAR_DAY",
        expireDateValue: 0 //余额调休-有效期值
      },
      options: [
        {
          name: "0个月",
          value: 0
        },
        {
          name: "1个月",
          value: 1
        },
        {
          name: "2个月",
          value: 2
        },
        {
          name: "3个月",
          value: 3
        },
        {
          name: "4个月",
          value: 4
        },
        {
          name: "5个月",
          value: 5
        },
        {
          name: "6个月",
          value: 6
        },
        {
          name: "7个月",
          value: 7
        },
        {
          name: "8个月",
          value: 8
        },
        {
          name: "9个月",
          value: 9
        },
        {
          name: "10个月",
          value: 10
        },
        {
          name: "11个月",
          value: 11
        },
        {
          name: "12个月",
          value: 12
        }
      ],
      rules: {
        leaveName: [
          {
            required: true,
            validator: checkHoliday,
            trigger: "blur"
          }
        ]
      }
    };
  },
  computed: {
    //余额方式-加班调休
    overTime() {
      return (
        this.form.howToRelease !== "MANUAL" &&
        this.form.howToRelease !== "WORK_TIME"
      );
    },
    //根据月数计算天数
    dateTotal() {
      return new Date(this.currentYear, this.everyMonth, 0).getDate();
    }
  },
  watch: {
    "form.howToRelease"(val) {
      if (!this.isEdit) {
        switch (val) {
          case "YEAR":
            this.form.expireDateType = "ONE_YEAR";
            this.form.whenReleaseType = "NEW_YEAR_DAY";
            this.form.whenToRelease = "";
            this.form.leaveRuleType = "FIX";
            break;
          case "MONTH":
            this.form.expireDateType = "ONE_MONTH";
            this.form.whenReleaseType = "MONTH_DAY";
            this.form.leaveRuleType = "FIX";
            if (!this.$route.query.id) {
              this.form.whenToRelease = "1";
            }
            break;
          case "MANUAL":
            this.form.expireDateType = "ONE_YEAR";
            this.form.leaveRuleType = "FIX";
            break;
          case "WORK_TIME":
            this.form.expireDateType = "YEAR_FIX_DATE";
            break;
        }
      }
    },
    //切换额度配置-回显默认规则数量
    "form.leaveRuleType"(val) {
      if (!this.$route.query.id) {
        this.entryBalList = 7;
        this.workBalList = 7;
      }
    },
    //工龄配置数量变化
    workBalList(val) {
      //新增数量，增加默认值-3
      for (let i = 0; i < val; i++) {
        if (!this.workBalInput[i]) {
          this.workBalInput[i] = i + 5;
        }
      }
    },
    //司龄配置数量变化
    entryBalList(val) {
      //新增数量，增加默认值-3
      for (let i = 0; i < val; i++) {
        if (!this.entryBalInput[i]) {
          this.entryBalInput[i] = i + 5;
        }
      }
    }
  },
  created() {
    this.checkIsDisabled();
    if (this.$route.query.id) {
      this.editDetail();
    }
  },
  methods: {
    //回显
    editDetail() {
      this.isEdit = true;
      this.editDisabled = true;
      this.holidayTitle = "查看假期";
      this.$attApi
        .apiPostLeaveInfoById({ id: this.$route.query.id })
        .then(res => {
          this.form = res.data;
          if (this.form.expireDateType == "YEAR_FIX_DATE") {
            let value = this.form.expireDateValue.split("-");
            this.everyMonth = Number(value[0]);
            this.everyDate = Number(value[1]);
          }
          this.isSetBalance = res.data.userBalanceType ? true : false;
          this.workBalInput = res.data.workBalanceRule
            ? res.data.workBalanceRule.split("-")
            : [];
          this.entryBalInput = res.data.entryBalanceRule
            ? res.data.entryBalanceRule.split("-")
            : [];
          this.entryBalList = this.entryBalInput.length;
          this.workBalList = this.workBalInput.length;
        });
    },

    //加班时长自动放入调休弹窗
    leaveTip() {
      this.$confirm("此规则只能使用在一个假期上", "说明", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false
      });
    },

    // 选择活动区域回调
    toggleArea() {
      this.entryBalInput = [5, 6, 7, 8, 9, 10, 11];
      this.workBalInput = [5, 6, 7, 8, 9, 10, 11];
    },

    //有效期选择回调
    overTimeArea() {
      this.everyMonth = 12;
      this.everyDate = 1;
      this.form.postponeDayChecked = false;
      this.form.postponeDay = 0;
      this.form.postponeTimeUnit = "DAY";
    },

    // 超过有效期后，月天切换
    toggleOption() {
      this.form.postponeDay = 0;
    },

    //校验有效期、余额额度
    checkVerify() {
      return (
        this.verifyRelease(this.form.releaseBalance) &&
        this.verifyPoneDay(this.form.postponeDay)
      );
    },

    // 超过有效期后，余额延长时间校验
    verifyPoneDay(value) {
      // let message = "";
      value = parseInt(value);
      if (isNaN(value)) {
        // message = "请输入数字";
        this.$message.warning("有效期余额请输入数字");
        return false;
        // this.form.postponeDay = "";
      } else if (value < 0) {
        // message = "有效期余额不能为负数";
        this.$message.warning("有效期余额不能为负数");
        return false;
        // this.form.postponeDay = "";
      } else if (this.form.postponeTimeUnit === "MONTH") {
        if (value > 180) {
          // message = "有效期余额最多为180个月";
          this.$message.warning("有效期余额最多为180个月");
          return false;
          // this.form.postponeDay = 180;
        }
      } else {
        if (value > 5400) {
          // message = "有效期余额最多为5400天";
          this.$message.warning("有效期余额最多为5400天");
          return false;
          // this.form.postponeDay = 5400;
        }
      }
      return true;
      // if(message) {
      //   this.$message({
      //     type: "warning",
      //     message: message
      //   })
      // } else {
      //   this.form.postponeDay = value;
      // }
    },

    // 余额发放方式
    methodOfBalance() {
      this.form.releaseBalance = 1;
    },

    // 校验假期额度
    verifyRelease(value) {
      // let message = "";
      if(this.form.leaveUnit !== 'HALF_DAY') {
        value = parseInt(value);
      }else {
        value = parseFloat(value);
      }
      if (isNaN(value)) {
        // message = "请输入数字";
        this.$message.warning("额度请输入数字");
        // this.form.releaseBalance = 1;
        return false;
      } else if (this.form.leaveUnit !== 'HALF_DAY' && value < 1) {
        // message = "余额至少为1天";
        this.$message.warning("余额至少为1天");
        return false;
        // this.form.releaseBalance = 1;
      }else if (this.form.leaveUnit === 'HALF_DAY' && value % 0.5 !== 0) {
        this.$message.warning("余额至少为0.5天或0.5的倍数");
        this.form.releaseBalance = 0.5;
        return false;
      } else {
        if (
          this.form.leaveUnit === "HOUR" &&
          this.form.howToRelease === "YEAR"
        ) {
          if (value > 8784) {
            // message = "每年发放最多为8784个小时";
            this.$message.warning("每年发放最多为8784个小时");
            return false;
            // this.form.releaseBalance = 8784;
          }
        } else if (
          this.form.leaveUnit === "HOUR" &&
          this.form.howToRelease === "MONTH"
        ) {
          if (value > 744) {
            // message = "每月发放最多为744个小时";
            this.$message.warning("每月发放最多为744个小时");
            return false;
            // this.form.releaseBalance = 744;
          }
        } else if (
          this.form.leaveUnit === "DAY" &&
          this.form.howToRelease === "YEAR"
        ) {
          if (value > 366) {
            // message = "每年发放最多为366天";
            this.$message.warning("每年发放最多为366天");
            return false;
            // this.form.releaseBalance = 366;
          }
        } else if (
          this.form.leaveUnit === "DAY" &&
          this.form.howToRelease === "MONTH"
        ) {
          if (value > 31) {
            // message = "每月发放最多为31天";
            this.$message.warning("每月发放最多为31天");
            return false;
            // this.form.releaseBalance = 31;
          }
        }
      }
      return true;
      // if(message) {
      //   this.$message({
      //     type: "warning",
      //     message: message
      //   })
      // } else {
      //   this.form.releaseBalance = value;
      //   return true
      // }
    },

    //检查假期类型是否已有加班余额用于调休
    checkIsDisabled() {
      this.$store.state.holidayTotal.forEach(val => {
        if (val.howToRelease === "WORK_TIME") {
          return (this.isDisabled = true);
        }
      });
    },
    handleChange(val) {
      console.log(val);
    },
    //增加工龄
    addEntryBal() {
      this.entryBalList++;
    },
    //删除工龄
    delEntryBal() {
      this.entryBalList--;
      this.entryBalInput = this.entryBalInput.slice(0, this.entryBalList);
      console.log(this.entryBalInput);
    },
    //增加司龄
    addWorkBal() {
      this.workBalList++;
    },
    //删除司龄
    delWorkBal() {
      this.workBalList--;
      this.workBalInput = this.workBalInput.slice(0, this.workBalList);
    },
    //检查额度配置规则
    checkLeaveRule(val) {
      switch (val) {
        case "WORK_YEAR":
          this.form.workBalanceRule = this.workBalInput.join("-");
          break;
        case "ENTRY_YEAR":
          this.form.entryBalanceRule = this.entryBalInput.join("-");
          break;
        case "BOTH_SUM":
          this.form.workBalanceRule = this.workBalInput.join("-");
          this.form.entryBalanceRule = this.entryBalInput.join("-");
          break;
        case "BOTH_MAX":
          this.form.workBalanceRule = this.workBalInput.join("-");
          this.form.entryBalanceRule = this.entryBalInput.join("-");
          break;
      }
    },
    save: debounce(
      function() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.form.userBalanceType = this.isSetBalance ? 1 : 0;
            this.checkLeaveRule(this.form.leaveRuleType);
            console.log(this.checkVerify());
            if (!this.checkVerify()) {
              return;
            }
            if (this.form.expireDateType == "YEAR_FIX_DATE") {
              this.form.expireDateValue =
                this.everyMonth + "-" + this.everyDate;
            }
            console.log(this.form.expireDateValue);
            if (this.$route.query.id) {
              let data = {
                ...this.form,
                id: this.$route.query.id
              };
              this.$attApi.apiPostModifyLeave(data).then(res => {
                if (res.success) {
                  this.$message({
                    type: "success",
                    message: "修改成功!"
                  });
                  this.$router.go(-1);
                }
              });
            } else {
              this.$attApi.apiPostAddLeave(this.form).then(res => {
                if (res.success) {
                  this.$message({
                    type: "success",
                    message: "保存成功!"
                  });
                  this.$router.go(-1);
                }
              });
            }
          } else {
            this.$nextTick(()=>{
              this.errorScroll(document.querySelectorAll("div.el-form-item__error"))
            })
          }
        });
      },
      2000,
      true
    ),
    cancel() {
      console.log("取消");
      this.$router.go(-1);
    },
    goBack() {
      if (!this.editDisabled) {
        this.$confirm(
          "离开当前页面会丢失未保存的修改信息, 确定离开吗?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            closeOnClickModal: false,
            closeOnPressEscape: false
          }
        ).then(() => {
          this.$router.go(-1);
        });
      } else {
        this.$router.go(-1);
      }
    },
    goLook() {
      let routerData = this.$router.resolve({path:'/attendance/workOvertime'})
      window.open(routerData.href,'_blank')
    }
  }
};
</script>

<style lang="scss" scoped>
.addHoliday {
  .header {
    border-bottom: 1px solid #ededed;
  }
  .content {
    height: calc(100vh - 230px);
    overflow: auto;
    padding: 20px 20px 2px 20px;
    .hint-info {
      color: #888888;
    }
    .title {
      padding: 0 0 22px 0;
    }
    .clear-arrows {
      /deep/ .el-input__suffix {
        display: none;
      }
    }
    .balances {
      display: flex;
      flex-direction: row;
      width: 700px;
      .el-input-number {
        margin: 0 10px;
      }
      .el-select,
      .el-input {
        width: 70px;
        padding-left: 2px;
      }
    }
    .el-select,
    .el-input {
      width: 300px;
    }
    .el-checkbox {
      display: block;
      width: 60px;
    }
    .entryBalance {
      .el-input {
        width: 100px;
        padding: 0 10px;
      }
      .entryOne .el-input {
        padding-left: 12px;
      }
    }
    .dateInput {
      margin-top: 10px;
      .el-select {
        width: 100px;
        margin: 0 4px;
      }
      .el-input {
        width: 70px;
        margin: 0 10px;
      }
    }
    .smallInput {
      /deep/ .el-input {
        width: 60px;
        padding: 0 5px;
      }
      .tips {
        color: #c1c5cd;
      }
    }
    .freeTime {
      .el-select {
        width: 100px;
      }
    }
    .tip-icon {
      position: absolute;
      cursor: pointer;
      right: 30px;
      bottom: 16px;
    }
    .viewOvertime {
      pointer-events: all;
    }
  }
  .viewPage {
    height: auto;
    pointer-events: none;
    /deep/ .el-input__suffix-inner {
      pointer-events: none;
    }
  }
  .footer {
    position: fixed;
    bottom: 0;
    width: calc(100% - 223px);
    padding: 20px 0 20px 0px;
    border-top: 1px solid #e5e5e5;
    background: #fff;
    text-align: center;
    z-index: 99;
  }
}
</style>
