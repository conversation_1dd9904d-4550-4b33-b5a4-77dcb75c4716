<template>
  <div class="excelBatch">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="handleBack" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>Excel批量修改</span>
        </el-col>
      </el-row>
    </header>
    <div class="content">
      <el-timeline>
        <el-timeline-item placement="top">
          <el-card>
            <div class="one">
              <h3>下载员工假期余额模版</h3>
              <el-button type="primary" @click="handelDownload"
                >下载模版</el-button
              >
              <!-- <el-button>查看发放规则</el-button> -->
            </div>
            <div class="oneContent">
              <h4>选择需导入的假期</h4>
              <el-checkbox-group v-model="leaveIds">
                <el-checkbox
                  :label="item.id"
                  v-for="(item, index) in banHeadList"
                  :key="index"
                  >{{ item.leaveName }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
          </el-card>
        </el-timeline-item>
        <el-timeline-item placement="top">
          <el-card>
            <div class="two">
              <h3>在模版中输入修改后余额</h3>
              <h4>
                示例
                员工方菲当前剩余年假12天，本年需新增3天，增加后年假一共15天，则应在Excel中输入15
              </h4>
            </div>
            <div class="twoContent">
              <div class="tipTable">
                <span>当前剩余</span>
                <table>
                  <tr>
                    <td>员工</td>
                    <td>年假（当前剩余天）</td>
                  </tr>
                  <tr>
                    <td>方菲</td>
                    <td>12</td>
                  </tr>
                </table>
              </div>
              <div class="arrow">-----></div>
              <div class="tipTable">
                <span>Excel应输入</span>
                <table>
                  <tr>
                    <td>员工</td>
                    <td>年假（当前剩余天）</td>
                  </tr>
                  <tr>
                    <td>方菲</td>
                    <td>15</td>
                  </tr>
                </table>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
        <el-timeline-item placement="top">
          <el-card>
            <div class="three">
              <h3>请选择填好的Excel文件</h3>
              <el-upload
                class="upload"
                ref="upload"
                action=""
                :http-request="uploadFile"
                :on-remove="handleRemove"
                :on-change="handleChange"
                :file-list="fileList"
                accept=".xls, .xlsx"
                :auto-upload="false"
              >
                <el-button
                  slot="trigger"
                  size="small"
                  type="primary"
                  v-show="isShow"
                  >选择模板</el-button
                >
                <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
              </el-upload>
              <span class="tip" v-show="isShow">未选择任何文件</span>
            </div>
          </el-card>
        </el-timeline-item>
        <el-timeline-item placement="top">
          <el-card>
            <div class="one">
              <h3>上传修改好的员工假期余额</h3>
              <el-button @click="submitUpload">上传</el-button>
            </div>
            <!-- <div class="fourContent">
                            <h4>注意事项：</h4>
                            <p>修改后的入职时间，会同步到员工假期余额和钉钉管理后台-通讯录-组织架构2个地方</p>
                        </div>                  -->
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      fileList: [],
      isShow: true,
      empIds: this.$store.state.currChooseUser,
      isAllChoosen: this.$route.params.isAllChoosen,
      empName: this.$route.params.empName,
      leaveIds: [], //选中需要导入的假期
      banHeadList: this.$store.state.banHeadList, //余额表头列表
      currYear: new Date().getFullYear(),
    };
  },
  created() {
    console.log(this.$route);
    this.banHeadList = this.$store.state.banHeadList.filter((val) => {
      return val.hasBalanceLimit;
    });
  },
  methods: {
    handleBack() {
      this.$store.commit("CURRCHOOSEUSER", []);
      this.$router.push({
        path: "/attendance/holidayBalance",
      });
    },
    handleChange(file, fileList) {
      console.log(file);
      this.isShow = false;
      // this.isDisabled = fileList.length !== 0 ? true : false;
    },
    //模版下载
    handelDownload() {
      console.log(this.empIds);
      if (!this.empIds || (this.empIds && this.empIds.length === 0)) {
        this.$message({
          type: "error",
          message: "请返回上一页选择需要导入的人员!",
        });
        return;
      } else if (this.leaveIds.length === 0) {
        this.$message({
          type: "error",
          message: "请选择需要导入的假期!",
        });
        return;
      }
      this.$attApi
        .apiPostexportUser({
          empIds: this.empIds,
          leaveIds: this.leaveIds,
          // isAllChoosen: this.isAllChoosen,
          // empName: this.empName
        })
        .then((res) => {
          console.log("res", res);
          let content = res;
          let blob = new Blob([content], { type: "application/vnd.ms-excel" });
          if ("download" in document.createElement("a")) {
            const link = document.createElement("a");
            link.download = this.currYear + "员工假期余额导出模板.xlsx";
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            document.body.appendChild(link);
            link.click();
            URL.revokeObjectURL(link.href);
            document.body.removeChild(link);
          } else {
            navigator.msSaveBlob(blob);
          }
        });
    },
    //上传模板
    uploadFile(params) {
      console.log(params);
      let form = new FormData();
      form.append("file", params.file);
      this.$attApi.apiPostUploadBalanceBatch(form).then((res) => {
        if (res.success) {
          this.$message({
            type: "success",
            message: "上传成功!",
          });
          this.$router.go(-1);
        }
      });
    },
    submitUpload() {
      if (this.isShow) {
        this.$message({
          type: "warning",
          message: "请选择模板后再上传",
        });
      }
      this.$refs.upload.submit();
    },
    handleRemove() {
      this.isShow = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.excelBatch {
  .header {
    border-bottom: 1px solid #ededed;
  }
  .content {
    width: 80%;
    padding: 20px 0 0 20px;
    .one {
      display: flex;
      flex-direction: row;
      align-items: center;
      h3 {
        margin-right: 20px;
      }
    }
    .oneContent {
      display: flex;
      flex-direction: row;
      margin: 15px 0 0 50px;
      h4 {
        width: 140px;
        margin-right: 20px;
      }
    }
    .two {
      h4 {
        margin-top: 10px;
      }
    }
    .twoContent {
      display: flex;
      flex-direction: row;
      .tipTable {
        text-align: center;
        padding-top: 15px;
        table {
          border-collapse: collapse;
          border-spacing: 0;
          border: 1px solid #cbcbcb;
          margin-top: 15px;
          tr {
            text-align: center;
          }
          td {
            width: 140px;
            height: 30px;
            border: 1px solid #cbcbcb;
          }
        }
      }
      .arrow {
        padding: 72px 20px 0;
      }
    }
    .three {
      display: flex;
      flex-direction: row;
      align-items: center;
      h3 {
        margin-right: 20px;
      }
      .tip {
        margin-left: 10px;
      }
      /deep/ .el-upload-list__item:first-child {
        margin-top: -15px;
      }
      /deep/ .el-upload-list__item.is-ready,
      .el-upload-list__item.is-uploading {
        display: block;
      }
    }
  }
}
</style>
