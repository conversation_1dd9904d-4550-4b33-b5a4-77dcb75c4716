<template>
  <div class="personBalance">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>假期余额</span>
        </el-col>
      </el-row>
    </header>
    <div class="content">
      <div class="personInfo">
        <div class="name">{{ personBalance.name.substring(0, 4) }}</div>
        <div class="info">
          <p>{{ personBalance.name }}的假期使用记录</p>
          <p>
            {{ personBalance.department }} {{ personBalance.hiredDate }}入职
          </p>
        </div>
      </div>
      <div class="useRecord" v-loading="loading">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane
            :label="
              item.leaveName +
              (item.leaveUnitEnum === 'HOUR' ? '(小时)' : '(天)')
            "
            :name="String(index)"
            v-for="(item, index) in banHeadList"
            :key="index"
          >
            <div v-if="item.hasBalanceLimit">
              <p class="curBalance">
                <span class="balance">当前余额</span>
                <span class="time"
                  >{{ currBalance }}
                  {{ item.leaveUnitEnum === "HOUR" ? "小时" : "天" }}</span
                >
                <span class="edit" @click="editBal(item.leaveUnitEnum)"
                  >修改余额</span
                >
                <span class="rule" @click="viewRule(item.id)">查看规则</span>
              </p>
              <el-table
                :data="tableData"
                border
                style="width: 100%"
                :height="height"
              >
                <el-table-column prop="createTime" label="时间" width="180">
                </el-table-column>
                <el-table-column prop="opUserId" label="操作者" width="180">
                </el-table-column>
                <el-table-column prop="changeReco" label="变更记录">
                </el-table-column>
                <el-table-column prop="balanceChangeReco" label="余额变更">
                </el-table-column>
                <el-table-column
                  prop="opReason"
                  label="理由"
                  :show-overflow-tooltip="true"
                >
                </el-table-column>
              </el-table>
            </div>
            <div class="noLimit" v-else>未设置规则，无法计算假期余额</div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <el-dialog
      title="修改假期余额"
      :visible.sync="openVisible"
      @close="close"
      width="600px"
    >
      <span class="dialogContent">
        <div class="edit">
          <span>修改余额</span>
          <el-select
            v-model="isAdd"
            :popper-append-to-body="false"
            @change="handleOption"
          >
            <el-option label="增加" value="1"></el-option>
            <el-option label="减少" value="0"></el-option>
          </el-select>
          <el-input-number
            v-model="changeBalance"
            controls-position="right"
            @change="handleChange"
            :min="0"
            :max="max"
          ></el-input-number>
          <span>{{ leaveUnit === "HOUR" ? "小时" : "天" }}</span>
        </div>
        <div class="edit">
          <span>修改理由</span>
          <el-input
            type="textarea"
            :rows="2"
            placeholder="修改原因最多输入200字"
            :maxlength="200"
            show-word-limit
            v-model="opReason"
          >
          </el-input>
        </div>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currBalance: "",
      activeName: 0,
      openVisible: false,
      changeBalance: 0,
      isAdd: "1",
      opReason: "",
      leaveUnit: "HOUR",
      banHeadList: this.$store.state.banHeadList, //余额表头列表
      tableData: [], //操作记录数据
      personBalance: this.$store.state.personBalance, //store存储当前参数
      height: document.body.clientHeight - 400 + "px",
      loading: false,
    };
  },
  computed: {
    max() {
      return this.isAdd === "1"
        ? this.leaveUnit === "HOUR"
          ? 1440
          : 180
        : this.currBalance;
    },
  },
  created() {
    this.getBalanceRecord();
  },
  methods: {
    // 增加或者减少余额
    handleOption(val) {
      if (val === "0") {
        this.changeBalance = 0;
      } else {
        this.changeBalance = 0;
      }
    },

    //获取假期余额使用记录
    getBalanceRecord() {
      if (this.personBalance.leaveBalanceVos.length === 0) {
        return;
      }
      let params = {
        empId: this.personBalance.empId, //员工id
        empName: this.personBalance.name, //员工姓名
        leaveId: this.banHeadList[this.activeName].id, //假期标识
      };
      this.loading = true;
      this.$attApi.apiPostQueryPersonBalanceReco(params).then((res) => {
        this.loading = false;
        this.currBalance = res.data && res.data.balance;
        this.tableData = res.data && res.data.infoVos;
      });
    },
    //清楚输入框
    clearForm() {
      this.isAdd = "1";
      this.changeBalance = 0;
      this.opReason = "";
    },
    cancel() {
      this.openVisible = false;
    },
    //右上角关闭×
    close() {
      this.clearForm();
    },
    //修改余额
    editBal(type) {
      this.openVisible = true;
      this.leaveUnit = type;
    },
    //查看规则
    viewRule(id) {
      this.$router.push({
        path: "/attendance/holidayAdd",
        query: {
          id: id,
        },
      });
    },
    save() {
      let params = {
        compId: this.personBalance.compId,
        empId: this.personBalance.empId,
        leaveId: this.personBalance.leaveBalanceVos[this.activeName].leaveId,
        isAdd: Number(this.isAdd),
        leaveUnit: this.leaveUnit,
        changeBalance: this.changeBalance,
        opReason: this.opReason,
      };
      this.$attApi.apiPostUpdatePersonBalance(params).then((res) => {
        if (res.success) {
          this.openVisible = false;
          this.getBalanceRecord();
        }
      });
    },
    handleClick(tab) {
      this.activeName = tab.index;
      this.getBalanceRecord();
    },
    handleChange(val) {
      let newVal = String(val);
      if (newVal.indexOf(".") !== -1) {
        this.changeBalance = newVal.substring(0, newVal.indexOf(".") + 3);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.personBalance {
  .header {
    border-bottom: 1px solid #ededed;
  }
  .content {
    padding: 20px 20px 0;

    .personInfo {
      display: flex;
      width: 100%;
      height: 60px;
      flex-direction: row;
      align-items: center;
      background: #eeeeee;
      margin-bottom: 10px;
      .name {
        min-width: 20px;
        height: 40px;
        color: #fff;
        background: #4f71ff;
        border-radius: 4px;
        text-align: center;
        line-height: 40px;
        margin: 0 10px 0;
        padding: 0 10px;
      }
    }
    .curBalance {
      padding-bottom: 15px;
      span {
        margin-right: 10px;
        color: #909399;
      }
      .time {
        font-weight: 600;
        color: #606266;
      }
      .edit,
      .rule {
        color: #4f71ff;
        cursor: pointer;
      }
    }
    .noLimit {
      text-align: center;
      margin-top: 50px;
    }
  }
  .dialogContent {
    .edit {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-bottom: 20px;
      span {
        margin-right: 10px;
      }
    }
    .el-input-number {
      width: 145px;
      margin: 0 10px 0;
    }
    .el-textarea {
      width: 350px;
    }
  }
  .footer {
    padding: 0 0 20px 160px;
  }
}
</style>
