<template>
  <el-dialog title="自定义列表显示字段" :visible.sync="openVisible" width="50%">
    <el-divider></el-divider>
    <div class="content">
      <el-form ref="form" label-width="100px">
        <el-form-item label="">
          <span class="username">姓名</span>
        </el-form-item>
        <el-form-item label="基本信息:">
          <ul>
            <li v-for="item in monthStatis.basicInfo" :key="item">
              {{ item }}
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="班次信息:">
          <ul>
            <li v-for="item in monthStatis.shiftInfo" :key="item">
              {{ item }}
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="出勤统计:">
          <ul class="lengthSta">
            <li v-for="item in monthStatis.attendSta" :key="item">
              {{ item }}
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="异常统计:">
          <ul class="lengthSta">
            <li v-for="item in monthStatis.abnormalSta" :key="item">
              {{ item }}
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="请假:">
          <ul>
            <li v-for="item in monthStatis.leave" :key="item">{{ item }}</li>
          </ul>
        </el-form-item>
        <el-form-item label="加班:">
          <ul class="lengthSta">
            <li v-for="item in monthStatis.overtime[0]" :key="item">
              {{ item }}
            </li>
          </ul>
          <ul class="lengthSta">
            <li v-for="item in monthStatis.overtime[1]" :key="item">
              {{ item }}
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="每日考勤结果:">
          <p class="attendResult">每日考勤结果</p>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="openVisible = false">取 消</el-button>
      <el-button type="primary" @click="openVisible = false">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { monthStatis } from "../util/constData";
export default {
  data() {
    return {
      openVisible: false,
      monthStatis: monthStatis,
    };
  },
  watch: {},
  methods: {
    openDialog() {
      this.openVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: 600px;
  overflow: auto;
  ul li,
  .username,
  .attendResult {
    display: inline-block;
    width: 60px;
    padding: 0 10px;
    line-height: 30px;
    margin-right: 5px;
    text-align: center;
    font-size: 12px;
    color: #4F71FF;
    border: 1px solid #deebfe;
    border-radius: 4px;
    background-color: #eff5fe;
  }
  .clockInfo {
    li {
      width: 75px;
    }
    span {
      font-size: 12px;
    }
  }
  .lengthSta li {
    width: 105px;
  }
  .attendResult {
    width: 105px;
  }
}
/deep/ .el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  .el-dialog__body {
    flex: 1;
    overflow: auto;
    padding-top: 0;
  }
  .el-divider--horizontal {
    margin: 0 0 20px;
  }
  .el-tag {
    width: 60px;
    height: 30px;
    padding: 0;
    text-align: center;
  }
}
</style>