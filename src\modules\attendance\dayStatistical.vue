<template>
  <div class="dayStatistical">
    <header class="header main-title">
      <el-tabs
        v-if="isShowTemperature"
        v-model="activeName"
      >
        <el-tab-pane label="每日考勤统计" name="STATISTICAL"></el-tab-pane>
        <el-tab-pane label="每日体温监测" name="TEMPERATURE"></el-tab-pane>
      </el-tabs>

      <el-row v-else type="flex">
        <el-col :span="12">
          <span>每日统计</span>
        </el-col>
      </el-row>
    </header>
    <statistical v-show="activeName === 'STATISTICAL'"></statistical>
    <div v-if="isShowTemperature">
      <temperature v-show="activeName === 'TEMPERATURE'"></temperature>
    </div>
  </div>
</template>
<script>
import statistical from "./components/days/statistical";
import temperature from "./components/days/temperature";
import { mapState } from "vuex";
export default {
  components: {
    statistical,
    temperature,
  },

  data() {
    return {
      cgb: window.env.server_env === "cgb",
      activeName: "STATISTICAL",
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
    isShowTemperature(){
      return this.cgb &&
            this.privilegeVoList.includes(
            'hrAttend.attendManage.dailyCount.getTempOfDayByEmpIds'
      )
    }
  },
};
</script>

<style lang="scss" scoped>
.dayStatistical {
  /*height: calc(100vh - 80px);*/
}
</style>
