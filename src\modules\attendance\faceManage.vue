<template>
  <div class="face">
    <header class="header">
      <el-row type="flex" style="justify-content: space-between">
        <span>人脸管理</span>
        <div>
          <el-button
            v-if="
              privilegeVoList.includes(
                'hrAttend.attendManage.faceManager.oneClickNotice'
              )
            "
            type="primary"
            @click="handleSendClick"
            >一键通知录入</el-button
          >
        </div>
      </el-row>
    </header>

    <section class="query">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="选择人员:">
          <el-select
            v-model="selectPerson"
            placeholder="请选择考勤组"
            @change="handleSelect"
          >
            <el-option
              v-for="item in peopleList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="choosePeople + ':'"
          v-show="selectPerson == 'attendance'"
        >
          <el-button @click="openVisible = true" v-if="namelist.length > 0">
            <p class="className">已选择{{ namelist.length }}个考勤组</p>
            <p
          /></el-button>
          <el-button @click="openVisible = true" v-if="namelist.length == 0">
            + 请选择{{ choosePeople }}</el-button
          >
        </el-form-item>

        <el-form-item label="人员:">
          <el-input
            v-model="send.empKeyword"
            style="width: 250px"
            placeholder="请输入姓名/手机号/证件号码"
          ></el-input>
        </el-form-item>
        <br />
        <div class="flex-box">
          <div class="left">
            <el-form-item label="人脸状态:">
              <el-select v-model="send.faceStatus" placeholder="请选择考勤组">
                <el-option
                  v-for="(val, key) in queryStatus.faceStatus"
                  :key="key"
                  :value="key"
                  :label="val"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="AI验证:">
              <el-select
                v-model="send.aiCheckStatus"
                placeholder="请选择考勤组"
              >
                <el-option
                  v-for="(val, key) in queryStatus.aiCheckStatus"
                  :key="key"
                  :value="key"
                  :label="val"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="验证方式:">
              <el-select v-model="send.checkWay" placeholder="请选择考勤组">
                <el-option
                  v-for="(val, key) in queryStatus.checkWayStatus"
                  :key="key"
                  :value="key"
                  :label="val"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>

          <div class="right">
            <el-button type="primary" @click="handleQueryClick">查询</el-button>
            <el-button @click="handleResetClick">重置</el-button>
          </div>
        </div>
      </el-form>
    </section>

    <section>
      <old-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        :headerData="headerData"
        :height="tableHeight"
        :isShowOperation="true"
        :operaOptions="operaOptions"
        :typeOptions="typeOptions"
        @operaClick="handleOperaClick"
        @sort-change="sortChange"
        :isShowPagination="true"
        :pageOptions="pageOptions"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
      >
        <template slot="faceStatus" slot-scope="scope">
          <span
            :class="[
              { green: scope.msg.row.faceStatus == '正常' },
              { red: scope.msg.row.faceStatus == '异常' },
              { yellow: scope.msg.row.faceStatus == '未录入' },
            ]"
          >
            {{ scope.msg.row.faceStatus }}
          </span>
        </template>

        <template slot="aiCheckStatus" slot-scope="scope">
          <span
            :class="[
              { green: scope.msg.row.aiCheckStatus === '已通过' },
              { red: scope.msg.row.aiCheckStatus === '未通过' },
            ]"
          >
            {{ scope.msg.row.aiCheckStatus }}
          </span>
        </template>

        <template slot="faceUrl" slot-scope="scope">
          <span v-if="scope.msg.row.faceUrl == '--'">--</span>
          <img
            v-else
            class="img"
            :src="scope.msg.row.faceUrl"
            alt=""
            @click="handleShowImg(scope.msg.row.faceUrl)"
          />
        </template>
      </old-table>
    </section>

    <!-- 选择人员 -->
    <section class="choosePerson">
      <el-dialog
        :title="choosePeople"
        v-if="openVisible"
        :visible.sync="openVisible"
        width="600px"
      >
        <span class="dialogContent">
          <div class="left">
            <el-input
              v-model="inputValue"
              :placeholder="choosePeople"
              @input="search"
            ></el-input>
            <!-- <i class="el-icon-search" @click="search"></i> -->
            <div class="left-content">
              <el-checkbox-group v-model="checkAttPerson">
                <el-checkbox
                  v-for="val in inputValue
                    ? filterAttendPersons
                    : attendPersons"
                  :key="val.id"
                  :label="val"
                  >{{ val.name }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
          </div>
          <i class="divider"></i>
          <ul class="right">
            <li v-for="item in checkAttPerson" :key="item.id">
              <span>{{ item.name }}</span>
              <i class="el-icon-close" @click="removeItem(item.id)"></i>
            </li>
          </ul>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="clearInputValue">取 消</el-button>
          <el-button type="primary" @click="userChecked">确 定</el-button>
        </span>
      </el-dialog>
    </section>

    <!-- 导入弹框 -->
    <detail-modify-dialog ref="detail-modify-dialog" @updateList="getList" />
    <!-- 修改弹框 -->
    <detail-upload-dialog ref="detail-upload-dialog" @updateList="getList" />
    <!-- 一键导入弹框 -->
    <detail-send-dialog ref="detail-send-dialog" @updateList="getList" />
    <!-- 图片弹框 -->
    <detail-image-dialog ref="detail-image-dialog" :url="imgUrl" />
  </div>
</template>
<script>
import { mapState } from "vuex";
import { queryStatus } from "./util/constData";
import detailModifyDialog from "./components/face/detail-modify-dialog";
import detailUploadDialog from "./components/face/detail-upload-dialog";
import detailSendDialog from "./components/face/detail-send-dialog";
import detailImageDialog from "./components/face/detail-image-dialog";

const sendInit = () => {
  return {
    aiCheckStatus: "", //AI验证
    attendIds: [], //考勤组id集合
    empIds: [], //员工集合
    empKeyword: "", //人员关键词
    checkWay: "", //验证方式,
    faceStatus: "", //人脸状态
    orderBy: "",
    asc: false,
    currPage: 1,
    pageSize: 10,
  };
};

const array2str = (arr) => {
  if (!arr || !arr.length) return "--";
  return arr.join("，");
};

export default {
  components: {
    detailModifyDialog,
    detailUploadDialog,
    detailSendDialog,
    detailImageDialog,
  },
  data() {
    return {
      queryStatus,
      loading: false,
      openVisible: false,
      peopleList: [
        {
          label: "考勤组",
          value: "attendance",
        },
      ],
      send: sendInit(),
      inputValue: "",
      selectPerson: "attendance",
      attendPersons: [], // 考勤组
      checkAttPerson: [],
      choosePeople: "考勤组",
      namelist: [], //名称数组
      name: "",
      imgUrl: "",
      tableData: [],
      tableHeight: "",
      headerData: [
        { title: "姓名", label: "empName", width: 150, align: "left" },
        { title: "部门", label: "departmentNames", width: 150, align: "left" },
        { title: "岗位", label: "postNames", width: 150, align: "left" },
        { title: "上级", label: "reportTos", width: 150, align: "left" },
        {
          title: "人脸状态",
          label: "faceStatus",
          slot: "faceStatus",
          width: 130,
          align: "left",
          sortable: "custom",
        },
        {
          title: "AI验证",
          label: "aiCheckStatus",
          slot: "aiCheckStatus",
          width: 120,
          align: "left",
          sortable: "custom",
        },
        {
          title: "验证方式",
          label: "checkWay",
          width: 130,
          align: "left",
          sortable: "custom",
        },
        { title: "所属考勤组", label: "agName", width: 200, align: "left" },
        {
          title: "人脸照片",
          label: "faceUrl",
          slot: "faceUrl",
          width: 150,
          align: "left",
        },
        {
          title: "录入时间",
          label: "uploadTime",
          width: 200,
          align: "left",
          sortable: "custom",
        },
        {
          title: "验证时间",
          label: "checkTime",
          width: 200,
          align: "left",
          sortable: "custom",
        },
        { title: "更新人", label: "updateBy", width: 150, align: "left" },
      ],
      operaOptions: {
        title: "操作", //名称
        width: 280, //宽度
        fixed: "right", // right - 固定在右侧
        align: "left",
        buttonList: [
          //按钮列表
          {
            title: "上传人脸照片",
            isShow: (row, btn) => {
              return this.privilegeVoList.includes(
                "hrAttend.attendManage.faceManager.inputFacePicture"
              )
                ? true
                : false;
            },
          },

          {
            title: "修改状态",
            isShow: (row, btn) => {
              return this.privilegeVoList.includes(
                "hrAttend.attendManage.faceManager.modifyState"
              ) &&
                (row.faceStatus == "正常" || row.faceStatus == "异常")
                ? true
                : false;
            },
          },

          {
            title: "通知员工录入",
            isShow: (row, btn) => {
              return this.privilegeVoList.includes(
                "hrAttend.attendManage.faceManager.noticeEmployee"
              ) &&
                (row.faceStatus == "未录入" ||
                  (row.faceStatus == "异常" && row.aiCheckCount < 3))
                ? true
                : false;
            },
          },
        ],
      },
      typeOptions: {
        width: 80,
        label: "序号",
        type: "index", // type类型支持：selection - 多选， index - 序号
      },
      pageOptions: {
        currPage: 1, //当前页码
        total: 10, //数据总数
        pageSize: 10, //每页显示条数
        pageSizes: [10, 20, 30, 40, 50, 100], //每页显示个数选择器选项设置
      },
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },

  mounted() {
    this.getAttendGroupList();
    this.getList();
    // window.onresize = () => {
    //   return (() => {
    //     const clientHeight = document.body.clientHeight - 360 + "px";
    //     this.tableHeight = clientHeight;
    //   })();
    // };
  },
  methods: {
    // 获取考勤组数据
    async getAttendGroupList() {
      const {
        data: { organizeAndUserResults },
      } = await this.$attApi.getAttendGroupList({ onlyEnableFaceOcr: true });
      this.attendPersons = organizeAndUserResults;
    },

    async getList() {
      this.loading = true;
      this.send = {
        ...this.send,
        currPage: this.pageOptions.currPage,
        pageSize: this.pageOptions.pageSize,
      };
      try {
        const { data, success } = await this.$attApi.mpFaceListApi(this.send);
        if (success) {
          this.tableData = this.handleList(data.records);
          this.pageOptions.total = data.total;
        }
      } finally {
        this.loading = false;
      }
    },
    handleList(arr) {
      if (!arr) return [];
      return arr.map((it) => {
        it.departmentNames = array2str(it.departmentNames);
        it.postNames = array2str(it.postNames);
        it.reportTos = array2str(it.reportTos);
        it.faceStatus = queryStatus["faceStatus"][it.faceStatus];
        if (it.faceStatus === "未录入") {
          it.checkWay = "--";
          it.aiCheckStatus = "--";
        } else {
          it.checkWay = queryStatus["checkWayStatus"][it.checkWay];
          it.aiCheckStatus = queryStatus["aiCheckStatus"][it.aiCheckStatus];
        }

        for (const { label } of this.headerData) {
          if (it[label] == null || it[label] === "") {
            it[label] = "--";
          }
        }
        return it;
      });
    },

    handleSelect() {},

    //分页size切换
    handleSizeChange(val) {
      this.pageOptions.pageSize = val;
      this.pageOptions.currPage = 1;
      this.getList();
    },

    //页码切换
    handleCurrentChange(val) {
      this.pageOptions.currPage = val;
      this.getList();
    },

    async handleOperaClick(btn, row, { $index }) {
      switch (btn) {
        case "上传人脸照片":
          this.$refs["detail-upload-dialog"].detail = row;
          this.$refs["detail-upload-dialog"].show = true;
          break;

        case "修改状态": {
          let {
            data: { idCardFrontageImgUrl },
          } = await this.$attApi.getEmpFaceApi({ userId: row.userId });
          this.$refs["detail-modify-dialog"].detail = {
            ...row,
            idCard: idCardFrontageImgUrl,
          };
          this.$refs["detail-modify-dialog"].show = true;
          break;
        }

        case "通知员工录入":
          this.sendNotify(row.userId);
          break;
      }
    },

    async sendNotify(userId) {
      const { success, message } = await this.$attApi.sendNotifyApi({
        userId: userId,
      });
      if (success) {
        this.$message.success("通知员工录入成功");
        this.getList();
      } else {
        // this.$message.error(message);
      }
    },

    // 搜索树信息
    search() {
      this.filterAttendPersons = this.attendPersons.filter((v) =>
        v.name.includes(this.inputValue)
      );
    },
    // 人员选择确定
    userChecked() {
      this.send.attendIds = this.getUserSet(this.checkAttPerson, "id");
      this.namelist = this.getUserSet(this.checkAttPerson, "name");
      this.clearInputValue();
    },

    // 人员选择获取人员id集合
    getUserSet(list, attr) {
      let newList = [];
      for (let i = 0; i < list.length; i++) {
        if (list[i].userResults) {
          newList = newList.concat(list[i].userResults.map((v) => v[attr]));
          continue;
        }
        newList = newList.concat(list[i][attr]);
      }
      return newList;
    },

    removeItem(id) {
      this.checkAttPerson = this.checkAttPerson.filter(
        (item) => item.id !== id
      );
    },
    // 对话框关闭
    clearInputValue() {
      this.openVisible = false;
      this.inputValue = "";
    },

    async handleSendClick() {
      const {
        data: { count },
      } = await this.$attApi.getNotifyInfoApi();
      if (count == 0) return this.$message.error("当前没有人员需要发送通知");

      this.$refs["detail-send-dialog"].show = true;
      this.$refs["detail-send-dialog"].count = count;
    },

    // 排序事件
    sortChange({ column, prop, order }) {
      this.send.asc = order == "ascending" ? true : false;
      this.send.orderBy = prop;
      this.handleQueryClick();
    },

    handleQueryClick() {
      this.pageOptions.currPage = 1;
      this.getList();
    },

    handleResetClick() {
      this.pageOptions.currPage = 1;
      this.namelist = [];
      this.checkAttPerson = [];
      this.send = sendInit();
      this.getList();
    },
    //查看原图
    handleShowImg(url) {
      if (!url) return false;
      this.imgUrl = url;
      this.$refs["detail-image-dialog"].show = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.face {
  /*height: calc(100vh - 80px);*/
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }
  .query {
    padding: 22px 20px 0 20px;
    .flex-box {
      display: flex;
      justify-content: space-between;
    }
  }
  .dialogContent {
    .divider {
      width: 1px;
      height: 68%;
      background: #ddd;
      position: absolute;
      left: 50%;
      top: 16%;
    }
    .el-input {
      width: 240px;
      height: 40px;
      padding-bottom: 10px;
    }
    .el-icon-search {
      position: relative;
      right: 30px;
      color: #909399;
    }
    .left {
      height: 270px;
      width: 280px;
      .left-content {
        height: 222px;
        overflow-y: auto;
        overflow-x: hidden;
      }
      /deep/ .el-checkbox-group {
        display: flex;
        flex-direction: column;
        .el-checkbox {
          padding-bottom: 5px;
          display: flex;
          align-items: center;
          .el-checkbox__label {
            width: 230px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
      .show-ellipsis {
        display: block;
        width: 180px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .right {
      width: 250px;
      height: 270px;
      overflow: auto;
      li {
        position: relative;
        height: 30px;
        line-height: 30px;
        background: #d9eafc;
        padding: 0 20px 0 5px;
        margin-bottom: 5px;
        width: 220px;
        white-space: nowrap;
        overflow-x: hidden;
        text-overflow: ellipsis;
        .el-icon-close {
          position: absolute;
          right: 5px;
          top: 8px;
          color: #909399;
          cursor: pointer;
        }
      }
    }
  }
  .choosePerson {
    /deep/ .el-dialog {
      height: 400px;
    }
    /deep/ .el-dialog__body {
      padding: 10px 20px 30px;
      height: 244px;
    }
    /deep/ .dialog-footer {
      position: absolute;
      display: flex;
      flex-direction: row;
      bottom: 10px;
      right: 20px;
    }
  }
  .img {
    width: 28px;
    height: 34px;
    border-radius: 2px;
    cursor: pointer;
  }
  .red {
    color: #d6342a;
  }
  .green {
    color: #41bd5a;
  }
  .yellow {
    color: #ff9500;
  }
  /deep/.el-button--small {
    // font-size: 14px;
  }
}
</style>
