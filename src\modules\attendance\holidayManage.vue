<template>
  <div class="holiday">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span>假期余额</span>
        </el-col>
      </el-row>
    </header>
    <div class="tabs">
      <el-form :inline="true" @submit.native.prevent>
        <el-form-item label="选择人员:">
          <div class="search-box">
            <el-input
              placeholder="请输入姓名"
              v-model.trim="searchPerson"
              maxLength="30"
              @change="handleSearch"
              :clearable="true"
            >
              <el-button slot="append" @click="handleSearch"> 搜索 </el-button>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleBulkEdit">Excel批量修改余额</el-button>
        </el-form-item>
      </el-form>
      <!-- <p class="totalNum">
            <el-checkbox v-model="isAllChoosen">所有页选择</el-checkbox>
            <span class="chosen">当前已选择{{ chooseTotal }}人</span>
          </p> -->
      <div class="holidayList">
        <el-table
          :header-cell-style="{ background: '#F1F1F1' }"
          :data="banList"
          ref="banTable"
          row-key="empId"
          @selection-change="handleSelectionChange"
          @select="currentRow"
          style="width: 100%"
          v-loading="loading1"
        >
          <el-table-column
            type="selection"
            :reserve-selection="true"
            width="55"
          >
          </el-table-column>
          <el-table-column
            prop="name"
            fixed="left"
            label="姓名"
            min-width="180"
          >
          </el-table-column>
          <el-table-column label="部门" min-width="180">
            <template slot-scope="scope">
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
                v-if="scope.row.departments"
              >
                <div slot="content">
                  <p v-for="(val, index) in scope.row.departments" :key="index">
                    {{ val }}
                  </p>
                </div>
                <p class="tooltip">
                  <span
                    v-for="(val, index) in scope.row.departments"
                    :key="index"
                    >{{ index === 0 ? "" + val : "/" + val }}</span
                  >
                </p>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="hiredDate"
            label="入职时间(司龄)"
            min-width="180"
          >
            <template slot-scope="scope">
              {{ scope.row.hiredDate ? scope.row.hiredDate : "未设置" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="firstWorkTime"
            label="首次工作时间(工龄)"
            min-width="180"
          >
            <template slot-scope="scope">
              {{ scope.row.firstWorkTime ? scope.row.firstWorkTime : "未设置" }}
            </template>
          </el-table-column>
          <el-table-column
            v-for="(val, index) in banHeadList"
            :key="val.id"
            :label="
              val.leaveName + (val.leaveUnitEnum === 'HOUR' ? '(小时)' : '(天)')
            "
            min-width="180"
          >
            <template slot-scope="scope">
              {{
                scope.row.leaveBalanceVos.length !== 0 &&
                scope.row.leaveBalanceVos[index] &&
                scope.row.leaveBalanceVos[index].hasBalanceLimit
                  ? scope.row.leaveBalanceVos[index].balance
                    ? scope.row.leaveBalanceVos[index].balance
                    : "——"
                  : "不限制余额"
              }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="openDetails(scope.row)"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleBanSizeChange"
          @current-change="handleBanCurrentChange"
          :current-page="banCurrentPage"
          :page-sizes="[10, 20, 30, 40]"
          layout="prev, pager, next, sizes, jumper"
          :total="banTotal"
          :page-size="banPageSize"
          background
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentRowId: "", //当前选中行empid
      chooseTotal: 0, //假期余额已选人员数量
      isAllChoosen: false, //是否全选所有页
      searchPerson: "", //搜索人员
      activeName: "type",
      banCurrentPage: 1,
      banPageSize: 10,
      banTotal: 0,
      banHeadList: [], //余额表头列表
      banList: [], //余额数据列表
      currChooseUser: [], //当前选中批量修改
      loading1: false,
    };
  },
  watch: {
    //所有页选择
    // isAllChoosen(val) {
    //   if (val) {
    //     this.chooseTotal = this.banTotal;
    //     this.getAllSelection();
    //   }
    // },
    chooseTotal(val) {
      // if (val < this.banTotal) {
      //   this.isAllChoosen = false;
      // }
    },
    //table重新布局
    banList() {
      console.log(222);
      this.doLayout();
    },
  },

  async created() {
    await this.getBalHeadList();
    this.getBalanceList();
    this.getTotalList();
  },

  methods: {
    doLayout() {
      this.$nextTick(() => {
        this.$refs.banTable.doLayout();
      });
    },
    //更换所有行选中状态
    getAllSelection() {
      this.$nextTick(() => {
        this.$refs.banTable.toggleAllSelection();
      });
    },
    //获取假期总数
    getTotalList() {
      let params = {
        currPage: 1,
        pageSize: 1000000,
      };
      this.$attApi.apiPostQueryLeaveInfo(params).then((res) => {
        if (res.success) {
          this.$store.commit("HOLIDAYTOTAL", res.data.records);
        }
      });
    },
    //获取假期余额表头列表
    getBalHeadList() {
      this.$attApi.apiPostGetLeaveList({}).then((res) => {
        if (res.success) {
          this.banHeadList = res.data;
          this.$store.commit("BANHEADLIST", res.data);
        }
      });
    },
    //获取假期余额数据列表
    getBalanceList(type) {
      this.loading1 = true;
      let params = {
        currentPage: type === "search" ? 1 : this.banCurrentPage,
        pageSize: this.banPageSize,
        empName: this.searchPerson,
      };
      this.$attApi.apiPostQueryLeaveBalanceInfo(params).then((res) => {
        this.loading1 = false;
        if (res.success) {
          this.banList = res.data.records;
          this.banTotal = res.data.total;
          // if (this.isAllChoosen) {
          //   this.chooseTotal = res.data.total;
          //   this.getAllSelection();
          // }
        }
      });
    },
    handleSearch() {
      this.getBalanceList("search");
    },

    //假期余额分页size
    handleBanSizeChange(val) {
      this.banPageSize = val;
      this.getBalanceList(val);
    },
    //假期余额分页切换
    handleBanCurrentChange(val) {
      this.banCurrentPage = val;
      this.getBalanceList(val);
    },
    //假期类型编辑
    handleEdit(id) {
      this.$router.push({
        path: "/attendance/holidayAdd",
        query: {
          id: id,
        },
      });
    },
    //删除假期
    handleDelete(row) {
      this.$confirm(`确定删除${row.leaveName}？删除后不可恢复`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          this.$attApi.apiPostDeleteLeave({ id: row.id }).then((res) => {
            if (res.success) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getTypeList();
            }
          });
        })
        .catch(() => {});
    },
    //当前选中行
    currentRow(val, row) {
      this.currentRowId = row.empId;
      if (row && this.currChooseUser.indexOf(row.empId) !== -1) {
        console.log(111);
        this.isAllChoosen = false;
        this.chooseTotal = this.currChooseUser.length;
        // this.getAllSelection();
      }
    },
    handleSelectionChange(val, row) {
      console.log(val);
      console.log(val);
      // this.chooseTotal = val.length;
      let arr = [];
      val.forEach((item) => {
        // if (this.currChooseUser.indexOf(item.empId) == -1) {
        //   arr.push(item.empId);
        // }
        arr.push(item.empId);
      });
      this.currChooseUser = arr;
    },
    //批量修改
    handleBulkEdit() {
      if (this.currChooseUser.length === 0) {
        this.$message({
          type: "error",
          message: "请勾选需要修改人员!",
        });
        return;
      }

      this.$store.commit("CURRCHOOSEUSER", this.currChooseUser);

      // this.currChooseUser = this.isAllChoosen ? this.currChooseUser : [];
      this.$router.push({
        name: "attendance.excelBatch",
        // params: {
        //   empIds: this.currChooseUser,
        //   // isAllChoosen: this.isAllChoosen,
        //   // empName: this.searchPerson
        // },
      });
    },
    openDetails(row) {
      console.log(row);
      this.$store.commit("PERSONBALANCE", row);
      this.$router.push({
        path: "/attendance/personBalance",
      });
    },
  },

  //批量修改之后返回假期余额页面
  beforeRouteEnter(to, from, next) {
    let fromPath = from.path;
    next((vm) => {
      if (
        fromPath === "/attendance/excelBatch" ||
        fromPath === "/attendance/personBalance"
      ) {
        vm.activeName = "balance";
        vm.getBalanceList();
      } else {
        vm.activeName = "type";
      }
    });
  },
};
</script>

<style lang="scss" scoped>
.holiday {
  /*height: calc(100vh - 80px);*/
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }
  /deep/ .tabs {
    padding: 20px 20px 0 20px;
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__item,
    .el-button {
      font-weight: normal;
    }
    .balance .el-form-item {
      margin-bottom: 0;
    }
    .balance .el-form-item:last-of-type {
      float: right;
      margin-right: 0;
    }
    .balance {
      .el-table {
        .el-table__fixed {
          height: auto !important;
          bottom: 15px !important;
        }
        .el-table__fixed::before {
          display: none;
        }
      }
      .totalNum {
        padding-top: 10px;
        /deep/ .el-checkbox__label {
          color: #606266;
        }
        .chosen {
          padding-left: 10px;
        }
      }
    }
  }
  .holidayList {
    padding: 10px 0 22px 0;
  }
  .pagination {
    float: right;
    padding: 20px 22px 20px 0;
  }
  .search-box {
    display: flex;
    .el-input {
      margin-right: 10px;
    }
  }
  /deep/ .el-table__empty-block {
    max-width: 100%;
    padding-right: 100%;
  }
  .tooltip {
    line-height: 50px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}
</style>
