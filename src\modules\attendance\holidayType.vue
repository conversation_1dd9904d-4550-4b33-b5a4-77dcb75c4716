<template>
  <div class="holiday">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span>假期类型</span>
        </el-col>
      </el-row>
    </header>
    <div class="tabs">
      <el-button @click="$router.push('/attendance/holidayAdd')" type="primary"
        >+ 新增假期</el-button
      >
      <div class="holidayList">
        <el-table
          :header-cell-style="{ background: '#F1F1F1' }"
          :data="typeList"
          style="width: 100%"
          v-loading="loading"
        >
          <el-table-column prop="leaveName" label="假期名称" width="180">
          </el-table-column>
          <el-table-column prop="leaveUnit" label="请假单位" width="180">
          </el-table-column>
          <el-table-column prop="leaveCountType" label="计算请假时长方式">
          </el-table-column>
          <el-table-column prop="howToRelease" label="余额规则">
            <template slot-scope="scope">
              {{
                scope.row.howToRelease ? scope.row.howToRelease : "不限制余额"
              }}
            </template>
          </el-table-column>
          <el-table-column prop="type" label="适用范围"> </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleEdit(scope.row.id)"
                >查看</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleTypeSizeChange"
          @current-change="handleTypeCurrentChange"
          :current-page="typeCurrentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="typePageSize"
          layout="prev, pager, next, sizes, jumper"
          :total="typeTotal"
          background
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentRowId: "", //当前选中行empid
      chooseTotal: 0, //假期余额已选人员数量
      isAllChoosen: false, //是否全选所有页
      searchPerson: "", //搜索人员
      activeName: "type",
      typeCurrentPage: 1,
      typePageSize: 10,
      typeTotal: 0,
      typeList: [], //假期类型列表数据
      currChooseUser: [], //当前选中批量修改
      matchData: [
        { label: "DAY", value: "天" },
        { label: "HALF_DAY", value: "半天" },
        { label: "HOUR", value: "小时" },
        { label: "WORK_DAY", value: "按工作日计算请假时长" },
        { label: "NATURAL_DAY", value: "按自然日计算请假时长" },
        { label: "YEAR", value: "每年自动发放一次" },
        { label: "MONTH", value: "每月自动发放一次" },
        { label: "MANUAL", value: "手动发放" },
        { label: "WORK_TIME", value: "加班时长自动放入余额用于调休" },
        { label: "COMPANY", value: "全公司" },
      ],
      loading: false,
    };
  },
  watch: {
    //所有页选择
    // isAllChoosen(val) {
    //   if (val) {
    //     this.chooseTotal = this.banTotal;
    //     this.getAllSelection();
    //   }
    // },
    chooseTotal(val) {
      // if (val < this.banTotal) {
      //   this.isAllChoosen = false;
      // }
    },
  },
  created() {
    this.getTypeList();
    this.getTotalList();
  },

  methods: {
    doLayout() {
      this.$nextTick(() => {
        this.$refs.banTable.doLayout();
      });
    },
    //更换所有行选中状态
    getAllSelection() {
      this.$nextTick(() => {
        this.$refs.banTable.toggleAllSelection();
      });
    },
    //获取假期类型列表
    getTypeList() {
      this.loading = true;
      let params = {
        currPage: this.typeCurrentPage,
        pageSize: this.typePageSize,
      };
      this.$attApi.apiPostQueryLeaveInfo(params).then((res) => {
        this.loading = false;
        if (res.success) {
          this.typeList = res.data.records;
          this.typeTotal = res.data.total;
          this.matchList();
        }
      });
      this.getTotalList();
    },
    //获取假期总数
    getTotalList() {
      let params = {
        currPage: 1,
        pageSize: 1000000,
      };
      this.$attApi.apiPostQueryLeaveInfo(params).then((res) => {
        if (res.success) {
          this.$store.commit("HOLIDAYTOTAL", res.data.records);
        }
      });
    },

    //匹配返回字段
    matchList() {
      this.matchData.forEach((item) => {
        this.typeList.forEach((val) => {
          switch (item.label) {
            case val.leaveUnit:
              val.leaveUnit = item.value;
              break;
            case val.leaveCountType:
              val.leaveCountType = item.value;
              break;
            case val.howToRelease:
              val.howToRelease = item.value;
              break;
            case val.type:
              val.type = item.value;
          }
        });
      });
    },

    //假期类型分页size
    handleTypeSizeChange(val) {
      this.typePageSize = val;
      this.getTypeList(val);
    },
    //假期类型分页切换
    handleTypeCurrentChange(val) {
      this.typeCurrentPage = val;
      this.getTypeList(val);
    },

    //假期类型编辑
    handleEdit(id) {
      this.$router.push({
        path: "/attendance/holidayAdd",
        query: {
          id: id,
        },
      });
    },
    //删除假期
    handleDelete(row) {
      this.$confirm(`确定删除${row.leaveName}？删除后不可恢复`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          this.$attApi.apiPostDeleteLeave({ id: row.id }).then((res) => {
            if (res.success) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getTypeList();
            }
          });
        })
        .catch(() => {});
    },
    //当前选中行
    currentRow(val, row) {
      this.currentRowId = row.empId;
      if (row && this.currChooseUser.indexOf(row.empId) !== -1) {
        console.log(111);
        this.isAllChoosen = false;
        this.chooseTotal = this.currChooseUser.length;
        // this.getAllSelection();
      }
    },
    handleSelectionChange(val, row) {
      console.log(val);
      console.log(val);
      // this.chooseTotal = val.length;
      let arr = [];
      val.forEach((item) => {
        // if (this.currChooseUser.indexOf(item.empId) == -1) {
        //   arr.push(item.empId);
        // }
        arr.push(item.empId);
      });
      this.currChooseUser = arr;
    },
    //批量修改
    handleBulkEdit() {
      if (this.currChooseUser.length === 0) {
        this.$message({
          type: "error",
          message: "请勾选需要修改人员!",
        });
        return;
      }
      // this.currChooseUser = this.isAllChoosen ? this.currChooseUser : [];
      this.$router.push({
        name: "attendance.excelBatch",
        params: {
          empIds: this.currChooseUser,
          // isAllChoosen: this.isAllChoosen,
          // empName: this.searchPerson
        },
      });
    },
    openDetails(row) {
      console.log(row);
      this.$store.commit("PERSONBALANCE", row);
      this.$router.push({
        path: "/attendance/personBalance",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.holiday {
  // height: calc(100vh - 80px);
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }
  /deep/ .tabs {
    padding: 20px 20px 0 20px;
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__item,
    .el-button {
      font-weight: normal;
    }
    .balance .el-form-item {
      margin-bottom: 0;
    }
    .balance .el-form-item:last-of-type {
      float: right;
      margin-right: 0;
    }
    .balance {
      .el-table {
        .el-table__fixed {
          height: auto !important;
          bottom: 15px !important;
        }
        .el-table__fixed::before {
          display: none;
        }
      }
      .totalNum {
        padding-top: 10px;
        /deep/ .el-checkbox__label {
          color: #606266;
        }
        .chosen {
          padding-left: 10px;
        }
      }
    }
  }
  .holidayList {
    padding: 10px 0 22px 0;
  }
  .pagination {
    float: right;
  }
  .search-box {
    display: flex;
    .el-input {
      margin-right: 10px;
    }
  }
  /deep/ .el-table__empty-block {
    max-width: 100%;
    padding-right: 100%;
  }
  .tooltip {
    line-height: 50px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}
</style>
