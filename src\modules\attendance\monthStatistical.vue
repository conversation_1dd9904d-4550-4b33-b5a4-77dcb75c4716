<template>
  <div class="dayStatistical def_per_height" ref="contain">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span>月度统计</span>
        </el-col>
      </el-row>
    </header>
    <div class="filterBox" ref="searchForm">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="日期:">
          <el-date-picker
            v-model="date"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            format="yyyy 年 MM 月 dd 日"
            @change="changeTimeGetDate"
            :picker-options="pickerOptions0"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="选择人员:">
          <el-select
            v-model="selectPerson"
            placeholder="活动区域"
            @change="handleSelect"
          >
            <el-option
              v-for="item in peopleList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="choosePeople + ':'"
          v-show="selectPerson == 'attendance'"
        >
          <el-button @click="openVisible = true" v-if="namelist.length > 0">
            <p class="className">已选择{{ namelist.length }}个考勤组</p>
            <p
          /></el-button>
          <el-button @click="openVisible = true" v-if="namelist.length == 0">
            + 请选择{{ choosePeople }}</el-button
          >
        </el-form-item>
        <el-form-item v-show="selectPerson !== 'quit'">
          <div class="check">
            <el-checkbox v-model="checked">{{ description }}</el-checkbox>
            <el-checkbox v-model="checkedDing">钉钉考勤组人员</el-checkbox>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button @click="getExportMon">导出</el-button>
        </el-form-item>
      </el-form>
      <!--      <el-form>-->
      <!--        <el-button @click="openFilterDialog">+ 自定义列表显示字段</el-button>-->
      <!--        <el-button>导出</el-button>-->
      <!--      </el-form>-->
    </div>
    <div class="statisticalList">
      <el-table
        :data="tableData"
        class="table-fixed"
        style="width: 100%"
        v-loading="loading"
        ref="table"
        :header-cell-style="{ background: '#F1F1F1' }"
      >
        <el-table-column
          prop="empName"
          label="姓名"
          width="120"
          :show-overflow-tooltip="true"
          fixed
        >
        </el-table-column>
        <el-table-column label="基本信息">
          <!-- <el-table-column
            prop="taxSubName"
            label="用工主体"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column> -->
          <el-table-column label="部门" width="120">
            <template slot-scope="scope">
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
                v-if="scope.row.departmentNames"
              >
                <div slot="content">
                  <p
                    v-for="(val, index) in scope.row.departmentNames"
                    :key="index"
                  >
                    {{ val }}
                  </p>
                </div>
                <p class="tooltip">
                  <span
                    v-for="(val, index) in scope.row.departmentNames"
                    :key="index"
                    >{{ index === 0 ? "" + val : "/" + val }}</span
                  >
                </p>
              </el-tooltip>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="empNo"
            label="工号"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column> -->
          <el-table-column
            prop="postName"
            label="岗位"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="reportTo"
            label="上级"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table-column>
        <el-table-column label="班次信息" prop="workingShiftResult">
          <el-table-column
            prop="agName"
            label="考勤组"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="timeZone"
            label="时区"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <!-- <el-table-column prop="workingDateTime" label="班次" width="200" :show-overflow-tooltip="true">
          </el-table-column> -->
        </el-table-column>
        <el-table-column label="出勤统计">
          <el-table-column
            prop="mustTurnOutCount"
            label="应出勤天数"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="workDayOutCount"
            label="工作日出勤天数"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="restDayOutCount"
            label="休息日出勤天数"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="mustWorkLength"
            label="应出勤时长(分钟)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="realWorkLength"
            label="实际出勤时长(分钟)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="balanceWorkLength"
            label="计薪工作时长(分钟)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="overTimeLength"
            label="加班工作时长(分钟)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table-column>
        <el-table-column label="异常统计">
          <el-table-column
            prop="lateCount"
            label="迟到次数(次)"
            width="200"
            :show-overflow-tooltip="true"
          >
            <!-- <template slot-scope="scope">
              <div
                class="abnormal"
                @click="handleClick(scope.row, '迟到')"
              >
                {{ scope.row.lateCount }}
              </div>
            </template> -->
          </el-table-column>
          <el-table-column
            prop="lateLength"
            label="迟到时长(分钟)"
            width="200"
            :show-overflow-tooltip="true"
          >
            <!-- <template slot-scope="scope">
              <div
                class="abnormal"
                @click="handleClick(scope.row, '迟到')"
              >
                {{ scope.row.lateLength }}
              </div>
            </template> -->
          </el-table-column>
          <el-table-column
            prop="leaveEarlyCount"
            label="早退次数(次)"
            width="200"
            :show-overflow-tooltip="true"
          >
            <!-- <template slot-scope="scope">
              <div
                class="abnormal"
                @click="handleClick(scope.row, '早退')"
              >
                {{ scope.row.lateLength }}
              </div>
            </template> -->
          </el-table-column>
          <el-table-column
            prop="leaveEarlyLength"
            label="早退时长(分钟)"
            width="200"
            :show-overflow-tooltip="true"
          >
            <!-- <template slot-scope="scope">
              <div
                class="abnormal"
                @click="handleClick(scope.row, '早退')"
              >
                {{ scope.row.lateLength }}
              </div>
            </template> -->
          </el-table-column>
          <!-- <el-table-column
            prop="absentLateCount"
            label="旷工迟到次数(次)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="absentLateLength"
            label="旷工迟到时长(分钟)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="absentLeaveEarlyCount"
            label="旷工早退次数(次)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="absentLeaveEarlyLength"
            label="旷工早退时长(分钟)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column> -->
          <el-table-column
            prop="beginWorkCount"
            label="上班缺卡次数(次)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="endWorkCount"
            label="下班缺卡次数(次)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="absentCount"
            label="缺勤"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="businessTripTimeLength"
            label="出差时长(分钟)"
            width="200"
            :show-overflow-tooltip="true"
          >
            <!-- <template slot-scope="scope">
              <div
                class="abnormal"
                @click="handleClick(scope.row, '出差')"
              >
                {{ scope.row.lateLength }}
              </div>
            </template> -->
          </el-table-column>
          <el-table-column
            prop="supplementCount"
            label="补卡次数(次)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="outsideAttendCount"
            label="外勤次数(次)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="outsideAttendLength"
            label="外出时长(分钟)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="changeWorkTimeLength"
            label="换班天数"
            width="200"
            :show-overflow-tooltip="true"
          >
            <!-- <template slot-scope="scope">
              <div
                class="abnormal"
                @click="handleClick(scope.row, '换班')"
              >
                {{ scope.row.lateLength }}
              </div>
            </template> -->
          </el-table-column>
        </el-table-column>
        <el-table-column label="请假" v-if="banHeadList.length">
          <el-table-column
            v-for="(val, index) in banHeadList"
            :key="val.id"
            min-width="120"
            :show-overflow-tooltip="true"
            :prop="`leaveBalance${index}`"
          >
            <template slot="header">
              <el-popover trigger="hover" placement="top">
                <span>{{
                  val.leaveName +
                  (val.leaveUnitEnum === "HOUR" ? "(小时)" : "(天)")
                }}</span>
                <div
                  slot="reference"
                  style="
                    width: 120px;
                    padding: 0px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  "
                >
                  <span>{{
                    val.leaveName +
                    (val.leaveUnitEnum === "HOUR" ? "(小时)" : "(天)")
                  }}</span>
                </div>
              </el-popover>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="加班时长(转加班费)">
          <el-table-column
            prop="workDayOverTimeToBalance"
            label="工作日(小时)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="restDayOverTimeToBalance"
            label="休息日(小时)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="leaveDayOverTimeToBalance"
            label="节假日(小时)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table-column>
        <el-table-column label="加班时长(转调休)">
          <el-table-column
            prop="workDayOverTimeToLeave"
            label="工作日(小时)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="restDayOverTimeToLeave"
            label="休息日(小时)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="leaveDayOverTimeToLeave"
            label="节假日(小时)"
            width="200"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table-column>
        <el-table-column label="每日统计">
          <el-table-column
            v-for="(v, i) in headerInfo"
            :key="v.signDate"
            :prop="'signOfDayInfoResults' + i"
            :label="v.signDate"
            width="150"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination" ref="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="100"
        layout="prev, pager, next, sizes, jumper"
        :total="pagination.total"
        background
      >
      </el-pagination>
    </div>
    <!-- 选择人员 -->
    <div class="choosePerson">
      <el-dialog
        :title="choosePeople"
        v-if="openVisible"
        :visible.sync="openVisible"
        width="600px"
      >
        <span class="dialogContent">
          <div class="left">
            <el-input
              v-model="inputValue"
              :placeholder="choosePeople"
              @input="search"
            ></el-input>
            <i class="el-icon-search" @click="search"></i>
            <div v-if="selectPerson === 'attendance'" class="left-content">
              <el-checkbox-group v-model="checkAttPerson">
                <el-checkbox
                  v-for="val in inputValue
                    ? filterAttendPersons
                    : attendPersons"
                  :label="val"
                  :key="val.id"
                  >{{ val.name }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
            <div v-if="selectPerson === 'department'" class="left-content">
              <el-tree
                :data="departmentList"
                show-checkbox
                ref="treeDepartment"
                node-key="id"
                :props="defaultProps"
                :filter-node-method="filterNode"
                @check="clickCheck"
                @current-change="currnetChange"
              >
                <span slot-scope="{ data }">
                  <span class="show-ellipsis">{{ data.name }}</span>
                </span>
              </el-tree>
            </div>
            <div v-if="selectPerson === 'main'" class="left-content">
              <el-checkbox-group v-model="checkMain">
                <el-checkbox
                  v-for="val in inputValue ? filterEmployments : employments"
                  :label="val"
                  :key="val.taxSubId"
                  >{{ val.taxSubName }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
            <div v-if="selectPerson === 'quit'" class="left-content">
              <el-checkbox-group v-model="checkAlready">
                <el-checkbox
                  v-for="val in inputValue
                    ? alreadyFilterPerson
                    : alreadyPerson"
                  :label="val"
                  :key="val.empId"
                  >{{ val.empName }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
          </div>
          <i class="divider"></i>
          <ul class="right">
            <li
              v-for="item in selectPerson === 'attendance'
                ? checkAttPerson
                : selectPerson === 'main'
                ? checkMain
                : selectPerson === 'quit'
                ? checkAlready
                : rightList"
              :key="item.id"
            >
              <span>{{
                selectPerson === "main"
                  ? item.taxSubName
                  : selectPerson === "quit"
                  ? item.empName
                  : item.name
              }}</span>
              <i
                class="el-icon-close"
                @click="
                  removeItem(
                    selectPerson === 'main'
                      ? item.taxSubId
                      : selectPerson === 'quit'
                      ? item.empId
                      : item.id
                  )
                "
              ></i>
            </li>
          </ul>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="clearInputValue">取 消</el-button>
          <el-button type="primary" @click="userChecked(selectPerson)"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </div>
    <monthStatisFilterDialog ref="child"></monthStatisFilterDialog>
    <!-- 异常打卡 -->
    <el-dialog
      :title="exceptionTitle"
      :visible.sync="abnormalVisible"
      v-if="abnormalVisible"
      width="800px"
    >
      <span class="abnormalContent">
        <div class="personInfo">
          <div class="name">{{ person.substring(0, 4) }}</div>
          <div class="info">
            <p>{{ person }}的打卡记录</p>
            <p>{{ department }}</p>
          </div>
        </div>
        <template v-if="exceptionTitle === '迟到' || exceptionTitle === '早退'">
          <el-table
            :header-cell-style="{ background: '#F1F1F1' }"
            :data="abnormalDetailInfo"
            style="width: 100%; margin-top: 20px"
          >
            <el-table-column prop="workDateTime" label="考勤时间" width="180">
            </el-table-column>
            <el-table-column prop="signDateTime" label="打卡时间">
            </el-table-column>
            <el-table-column prop="signResult" label="打卡结果">
              <template slot-scope="scope">
                <span>{{ getSignResult(scope.row.signResult) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="attendWorkShift"
              label="考勤班次"
            ></el-table-column>
          </el-table>
        </template>
        <template v-if="exceptionTitle === '出差'">
          <el-table
            :header-cell-style="{ background: '#F1F1F1' }"
            :data="abnormalDetailInfo"
            style="width: 100%; margin-top: 20px"
          >
            <el-table-column
              prop="orderMessageInfo"
              label="审批单信息"
              width="180"
            >
            </el-table-column>
            <el-table-column prop="attendTimeLength" label="考勤时长">
            </el-table-column>
            <el-table-column prop="attendStartAndEndTime" label="考勤起止时间">
            </el-table-column>
            <el-table-column prop="attendMessage" label="关联考勤信息">
            </el-table-column>
            <el-table-column prop="orderTimeLength" label="审批单时长">
            </el-table-column>
            <el-table-column prop="orderStartAndEndTime" label="审批单起止时间">
            </el-table-column>
            <el-table-column prop="attendShift" label="考勤班次">
            </el-table-column>
          </el-table>
        </template>
        <template v-if="exceptionTitle === '换班'">
          <el-table
            :header-cell-style="{ background: '#F1F1F1' }"
            :data="abnormalDetailInfo"
            style="width: 100%; margin-top: 20px"
          >
            <el-table-column prop="workDate" label="审批单信息" width="180">
            </el-table-column>
            <el-table-column prop="signTime" label="申请人"> </el-table-column>
            <el-table-column prop="attendStatusEnum" label="替班人">
            </el-table-column>
            <el-table-column prop="recordAddress" label="换班日期">
            </el-table-column>
            <el-table-column prop="recordAddress" label="还班日期">
            </el-table-column>
          </el-table>
        </template>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import monthStatisFilterDialog from "./components/monthStatisFilterDialog";
export default {
  components: {
    monthStatisFilterDialog,
  },
  data() {
    return {
      loading: false,
      pickerOptions0: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6;
        },
      },
      banHeadList: [], //请假表头列表
      description: "离开考勤组的人员",
      exceptionTitle: "", // 异常状态标题
      pagination: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      abnormalRadio: "1",
      lateTime: "",
      abnormalVisible: false, // 打卡异常弹框
      checkAttPerson: [],
      checkQuitPerson: [],
      checkMain: [],
      inputValue: "",
      checkAlready: [], // 选中已离职人员
      alreadyPerson: [], // 已离职员工
      alreadyFilterPerson: [], // 过滤后的已离职员工
      attendPersons: [], // 考勤组
      filterAttendPersons: [], // 过滤后的考勤组
      employments: [], // 用工主体
      filterEmployments: [], // 过滤后的用工主体
      outAttendGroup: false, // 考勤组是否已选中
      tableHeight: document.body.clientHeight - 290, // 表格自适应高度
      openVisible: false,
      selectPerson: "attendance",
      choosePeople: "考勤组",
      peopleList: [
        // {
        //   label: "全公司",
        //   value: "company"
        // },
        // {
        //   label: "用工主体",
        //   value: "main"
        // },
        {
          label: "考勤组",
          value: "attendance",
        },
        // {
        //   label: "部门/人员",
        //   value: "department"
        // },
        // {
        //   label: "从已离职人员选取",
        //   value: "quit"
        // }
      ],
      departmentList: [],
      rightList: [],
      defaultProps: {
        children: "userResults",
        label: "name",
      },
      checked: false,
      checkedDing: false,
      date: "",
      tableData: [],
      abnormalDetailInfo: [], // 异常记录具体信息
      person: "", // 打卡人员
      department: "", // 打卡人员所属部门
      list: [], // 选中人员ID
      attendIds: [], // 考勤组ID
      taxSubIds: [], // 用工主体ID
      empStatus: ["ON_THE_JOB"], // 离职状态
      isLateTime: true, // 是否可输入迟到/早退时间
      headerInfo: [], // 个人统计头部信息
      currentRow: [], // 选中当前行数据
      isCompany: true, //是否为全公司
      namelist: [], //名称数组
    };
  },
  watch: {
    // 检测打卡选项
    abnormalRadio(newVal) {
      if (newVal === "NORMAL" || newVal === "ABSENT_WORK") {
        this.lateTime = "";
        this.isLateTime = true;
      } else if (newVal === "BE_LATE" || newVal === "LEAVE_EARLIER") {
        this.isLateTime = false;
      }
    },

    // 是否选中离职人员
    checked(val) {
      if (this.selectPerson !== "attendance") {
        if (val) {
          this.empStatus = ["ON_THE_JOB", "DIMISSION"];
        } else {
          this.empStatus = ["ON_THE_JOB"];
        }
        this.outAttendGroup = false;
        if (this.selectPerson === "department") {
          this.rightList = [];
          this.getDepartmentAndEmpList();
          this.userChecked("department");
          return;
        }
      } else {
        if (val) {
          this.outAttendGroup = true;
        } else {
          this.outAttendGroup = false;
        }
        this.empStatus = ["ON_THE_JOB"];
      }

      this.getCountOfDayByEmpIds();
    },

    // 是否选中钉钉
    async checkedDing(val) {
      await this.getBalHeadList();
      this.getCountOfDayByEmpIds();
    },

    // 人员选择
    selectPerson(val) {
      switch (val) {
        case "company":
          this.description = "离职人员";
          this.list = [];
          this.attendIds = [];
          this.rightList = [];
          this.checkAttPerson = [];
          this.taxSubIds = [];
          this.checkMain = [];
          this.isCompany = true;
          this.getCountOfDayByEmpIds();
          break;
        case "main":
          this.choosePeople = "用工主体";
          this.description = "离职人员";
          this.list = [];
          this.attendIds = [];
          this.rightList = [];
          this.checkAttPerson = [];
          this.isCompany = false;
          this.getEmployment();
          break;
        case "attendance":
          this.choosePeople = "考勤组";
          this.description = "离开考勤组的人员";
          this.list = [];
          this.taxSubIds = [];
          this.checkAttPerson = [];
          this.checkMain = [];
          this.isCompany = false;
          this.getAttendGroupList();
          break;
        case "department":
          this.choosePeople = "部门/人员";
          this.description = "离职员工";
          this.taxSubIds = [];
          this.attendIds = [];
          this.checkMain = [];
          this.rightList = [];
          this.isCompany = false;
          this.getDepartmentAndEmpList();
          break;
        case "quit":
          this.choosePeople = "离职人员";
          this.description = "已离职员工";
          this.taxSubIds = [];
          this.attendIds = [];
          this.checkMain = [];
          this.rightList = [];
          this.isCompany = false;
          this.getAlreadyPeople();
          break;
      }
      this.checked = false;
    },
  },
  created() {
    this.getBalHeadList();
    this.getMouthEndStart();
    this.getAttendGroupList();
  },
  mounted() {
    this.getCountOfDayByEmpIds();
    this.$refs["table"].doLayout();
  },
  updated() {
    this.$refs["table"].doLayout();
  },
  methods: {
    // 获取当月月初和月末
    getMouthEndStart() {
      var nowDate = new Date();
      var cloneNowDate = new Date();
      var fullYear = nowDate.getFullYear();
      var month = nowDate.getMonth() + 1;
      var endOfMonth = new Date(fullYear, month, 0).getDate();
      function getFullDate(targetDate) {
        var D, y, m, d;
        if (targetDate) {
          D = new Date(targetDate);
          y = D.getFullYear();
          m = D.getMonth() + 1;
          d = D.getDate();
        } else {
          y = fullYear;
          m = month;
          d = date;
        }
        m = m > 9 ? m : "0" + m;
        d = d > 9 ? d : "0" + d;
        return y + "-" + m + "-" + d;
      }
      this.endDate = getFullDate(Date.now() - 8.64e6); //当月最后一天
      this.startDate = getFullDate(cloneNowDate.setDate(1)); //当月第一天
      this.date = [this.startDate, this.endDate];
    },
    handleSelect() {
      this.namelist = [];
    },
    // 树过滤
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    // 搜索树信息
    search() {
      if (this.selectPerson === "attendance") {
        this.filterAttendPersons = this.attendPersons.filter((v) =>
          v.name.includes(this.inputValue)
        );
      } else if (this.selectPerson === "main") {
        this.filterEmployments = this.employments.filter((v) =>
          v.taxSubName.includes(this.inputValue)
        );
      } else if (this.selectPerson === "department") {
        this.$refs.treeDepartment.filter(this.inputValue);
      } else {
        this.alreadyFilterPerson = this.alreadyPerson.filter((v) =>
          v.empName.includes(this.inputValue)
        );
      }
    },

    //获取假期余额表头列表
    async getBalHeadList() {
      let form = new FormData();
      form.append("platform", this.checkedDing ? "DING_TALK" : "OLADING");
      await this.$attApi.apiPostGetLeaveList(form).then((res) => {
        if (res.success) {
          this.banHeadList = res.data;
        }
      });
    },

    // 获取列表自定义高度
    // getTableHeight() {
    //   this.tableHeight = this.$refs.contain.offsetHeight - this.$refs.pagination.offsetHeight - this.$refs.searchForm.offsetHeight - 70;
    // },

    // 人员选择确定
    userChecked(val) {
      this.pagination.currentPage = 1;
      switch (val) {
        case "department":
          this.isCompany = false;
          this.attendIds = [];
          this.taxSubIds = [];
          this.list = this.getUserSet(this.rightList, "id");
          this.namelist = this.getUserSet(this.rightList, "name");
          this.getCountOfDayByEmpIds();
          break;
        case "attendance":
          this.isCompany = false;
          this.list = [];
          this.taxSubIds = [];
          this.attendIds = this.getUserSet(this.checkAttPerson, "id");
          this.namelist = this.getUserSet(this.checkAttPerson, "name");
          this.getCountOfDayByEmpIds();
          break;
        case "main":
          this.isCompany = false;
          this.list = [];
          this.attendIds = [];
          this.taxSubIds = this.getUserSet(this.checkMain, "taxSubId");
          this.namelist = this.getUserSet(this.checkMain, "taxSubName");
          this.getCountOfDayByEmpIds();
          break;
        case "quit":
          this.isCompany = false;
          this.attendIds = [];
          this.taxSubIds = [];
          this.list = this.getUserSet(this.checkAlready, "empId");
          this.namelist = this.getUserSet(this.checkAlready, "empName");
          this.getCountOfDayByEmpIds();
          break;
      }
      this.clearInputValue();
    },

    // 对话框关闭
    clearInputValue() {
      this.openVisible = false;
      this.inputValue = "";
    },

    // 人员选择获取人员id集合
    getUserSet(list, attr) {
      let newList = [];
      for (let i = 0; i < list.length; i++) {
        if (list[i].userResults) {
          newList = newList.concat(list[i].userResults.map((v) => v[attr]));
          continue;
        }
        newList = newList.concat(list[i][attr]);
      }
      return newList;
    },

    // 修改时间后更新数据
    changeTimeGetDate(newVal) {
      if (!newVal) {
        this.$message.error("请输入日期");
        return;
      }
      if (newVal) {
        if (
          new Date(newVal[1]).getTime() - new Date(newVal[0]).getTime() >
          30 * 24 * 3600 * 1000
        ) {
          this.$message({
            type: "error",
            message: "开始日期至结束日期最长时长为31天，请重新选择",
          });
          this.date = [];
          return;
        }
      }
      (this.pagination.currentPage = 1), this.getCountOfDayByEmpIds();
    },

    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getCountOfDayByEmpIds();
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.getCountOfDayByEmpIds();
      console.log(`当前页: ${val}`);
    },
    //弹出筛选框
    openFilterDialog() {
      this.$refs.child.openDialog();
    },
    //选择人员弹框
    chooseType(type) {
      console.log(type);
    },
    clickCheck(val, ischeck) {
      console.log("val", val, "check", ischeck);
      if (this.rightList.some((item) => item.id === val.id)) {
        this.removeItem(val.id);
      } else {
        this.rightList.push(val);
      }
    },
    //打卡异常初次弹框
    handleClick(row, title) {
      this.currentRow = row;
      this.exceptionTitle = title;
      this.person = row.empName;
      this.department = row.departmentName;
      this.getAbnormalData(row, title);
    },

    // 获取打卡异常状态记录信息
    async getAbnormalData(row, title) {
      let str;
      switch (title) {
        case "迟到":
          str = "BE_LATE";
          break;
        case "早退":
          str = "LEAVE_EARLIER";
          break;
        case "出差":
          str = "BUSINESS_TRIP";
          break;
      }
      let param = {
          attendStatus: str,
          empId: this.currentRow.compEmpId,
          startDate: this.date[0],
          endDate: this.date[1],
        },
        res;
      if (title === "迟到" || title === "早退") {
        res = await this.$attApi.getExceptionRecoInfo(param);
      } else {
        res = await this.$attApi.getExceptionRecoOrderInfo(param);
      }
      this.abnormalDetailInfo = res.data;
      this.abnormalVisible = true;
    },

    // 将返回的后台数据拼接成规范的表格数据
    transformBaseData(list) {
      let newList = [];
      if ((list && !list.length) || !list) return;
      list.forEach((v, i) => {
        newList[i] = {};
        for (let item in v) {
          if (typeof v[item] !== "object") {
            newList[i][item] = v[item];
          } else if (Array.isArray(v[item])) {
            //判断部门集合
            if (item === "departmentNames") {
              newList[i][item] = v[item];
            } else {
              v[item].forEach((j, k) => {
                for (let q in j) {
                  if (Array.isArray(j[q])) {
                    let str = "";
                    j[q].forEach((w) => {
                      if (w.signTime) {
                        str += w.attendStatus + "(" + w.signTime + "),";
                      } else {
                        if (w.attendStatus) {
                          str += w.attendStatus + ",";
                        }
                      }
                    });
                    console.log(str);
                    newList[i][q + k] = str && str.substr(0, str.length - 1);
                    continue;
                  }
                  newList[i][q + k] = j[q];
                }
              });
            }
          } else {
            for (let x in v[item]) {
              newList[i][x] = v[item][x];
            }
          }
        }
      });
      return newList;
    },

    // 获取用工主体数据
    async getEmployment() {
      const { data } = await this.$attApi.getTaxSubjectByComp();
      this.employments = data;
    },

    // 获取考勤组数据
    async getAttendGroupList() {
      const { data } = await this.$attApi.getAttendGroupList();
      this.attendPersons = data.organizeAndUserResults;
    },

    // 获取部门/员工
    async getDepartmentAndEmpList() {
      let send = {
        empStatus: "",
      };
      if (String(this.empStatus) !== String(["ON_THE_JOB"])) {
        send.empStatus = "DIMISSION";
      }
      const { data } = await this.$attApi.getDepartmentAndEmpList(send);
      this.departmentList = data.organizeAndUserResults;
    },

    // 获取已离职人员
    async getAlreadyPeople() {
      const { data } = await this.$attApi.getLeaveJobPerson();
      this.alreadyPerson = data;
    },

    // 获取员工日打卡信息
    async getCountOfDayByEmpIds() {
      this.loading = true;
      let send = {
        currPage: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        empIds: this.list,
        taxSubIds: this.taxSubIds,
        attendIds: this.attendIds,
        empStatus: this.empStatus,
        outAttendGroup: this.outAttendGroup,
        startDate: this.date ? this.date[0] : "",
        endDate: this.date ? this.date[1] : "",
        isCompany: this.isCompany,
        platform: this.checkedDing ? "DING_TALK" : "OLADING",
      };
      // const send = {
      //   currPage: 1,
      //   empIds: [59027,69380],
      //   pageSize: 100,
      //   startDate: "2021-03-20 01:48:45",
      //   endDate: "2021-03-29 01:48:45"
      // };

      const { data } = await this.$attApi.getCountOfMonthByEmpIds(send);
      this.loading = false;
      if (data) {
        this.pagination.total = data.total;
        this.tableData = this.transformBaseData(data.records);
        this.headerInfo =
          data.records && data.records.length
            ? data.records[0].signOfDayResults
            : [];
      } else {
        this.pagination.total = 0;
        this.tableData = [];
      }
    },
    removeItem(id) {
      if (this.selectPerson === "department") {
        this.rightList = this.rightList.filter((item) => item.id !== id);
        this.$refs.treeDepartment.setCheckedNodes(this.rightList);
      } else if (this.selectPerson === "main") {
        this.checkMain = this.checkMain.filter((item) => item.taxSubId !== id);
      } else if (this.selectPerson === "quit") {
        this.checkAlready = this.checkAlready.filter(
          (item) => item.empId !== id
        );
      } else {
        this.checkAttPerson = this.checkAttPerson.filter(
          (item) => item.id !== id
        );
      }
    },
    currnetChange(val, node) {
      console.log(val);
      console.log(node);
    },
    getSignResult(data) {
      switch (data) {
        case "NORMAL":
          return "正常";
        case "BE_LATE":
          return "迟到";
        case "LEAVE_EARLIER":
          return "早退";
        case "ABSENT_WORK":
          return "缺卡";
        case "HOOKY_WORK":
          return "旷工";
        case "OUTSIDE_ATTEND":
          return "外勤";
        case "OUTSIDE_WORK":
          return "外出";
        case "BUSINESS_TRIP":
          return "出差";
        case "OVER_TIME":
          return "加班";
        case "LEAVE":
          return "请假";
        case "SUPPLY_PASS":
          return "补卡通过";
        case "RESTDAY":
          return "休息日";
      }
    },
    async getExportMon() {
      if (!this.date) {
        this.$message.error("请选择导出日期");
        return;
      }
      let send = {
        currPage: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        empIds: this.list,
        taxSubIds: this.taxSubIds,
        attendIds: this.attendIds,
        empStatus: this.empStatus,
        outAttendGroup: this.outAttendGroup,
        startDate: this.date[0],
        endDate: this.date[1],
        isCompany: this.isCompany,
      };
      await this.$attApi.getExportMon(send).then((res) => {
        let content = res;
        let blob = new Blob([content], { type: "application/vnd.x-xls" });
        if ("download" in document.createElement("a")) {
          const link = document.createElement("a");
          link.download =
            "月度汇总-" +
            this.date[0].split("-").join("") +
            "-" +
            this.date[1].split("-").join("") +
            "考勤统计.xlsx";
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          document.body.appendChild(link);
          link.click();
          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);
        } else {
          navigator.msSaveBlob(blob);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.dayStatistical {
  // height: 100%;
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }
  .filterBox {
    padding: 18px 20px 0 20px;
    .check {
      display: flex;
      flex-direction: column;
      .el-checkbox {
        height: 20px;
        line-height: 20px;
      }
    }
    /deep/ .el-range-editor.el-input__inner input {
      width: 130px;
    }
  }
  .statisticalList {
    padding: 0 22px;
    .el-table--scrollable-x .el-table__body-wrapper {
      overflow-x: auto;
      z-index: 9999;
    }
  }
  .pagination {
    float: right;
    padding: 18px 22px 18px 0;
  }
  /deep/ .el-dialog__body {
    padding-top: 10px;
  }
  .dialogContent {
    .divider {
      width: 1px;
      height: 68%;
      background: #dddddd;
      position: absolute;
      left: 50%;
      top: 16%;
    }
    .el-input {
      width: 240px;
      height: 40px;
      padding-bottom: 10px;
    }
    .el-icon-search {
      position: relative;
      right: 30px;
      color: #909399;
    }
    .left {
      height: 270px;
      width: 280px;
      .left-content {
        height: 222px;
        overflow-y: auto;
        overflow-x: hidden;
      }
      /deep/ .el-checkbox-group {
        display: flex;
        flex-direction: column;
        .el-checkbox {
          padding-bottom: 5px;
          display: flex;
          align-items: center;
          .el-checkbox__label {
            width: 230px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
      .show-ellipsis {
        display: block;
        width: 180px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .right {
      width: 250px;
      height: 270px;
      overflow: auto;
      li {
        position: relative;
        height: 30px;
        line-height: 30px;
        background: #d9eafc;
        padding: 0 20px 0 5px;
        margin-bottom: 5px;
        width: 220px;
        white-space: nowrap;
        overflow-x: hidden;
        text-overflow: ellipsis;
        .el-icon-close {
          position: absolute;
          right: 5px;
          top: 8px;
          color: #909399;
          cursor: pointer;
        }
      }
    }
  }
  .abnormalContent {
    .personInfo {
      display: flex;
      width: 100%;
      height: 60px;
      flex-direction: row;
      align-items: center;
      background: #eeeeee;
      margin-bottom: 10px;
      .name {
        min-width: 20px;
        height: 40px;
        color: #fff;
        background: #4f71ff;
        border-radius: 4px;
        text-align: center;
        line-height: 40px;
        margin: 0 10px 0;
        padding: 0 10px;
      }
    }
  }
  .abnormal {
    color: #b8741a;
    text-decoration: underline;
    cursor: pointer;
  }
  .choosePerson {
    /deep/ .el-dialog {
      height: 400px;
    }
    /deep/ .el-dialog__body {
      padding: 10px 20px 30px;
      height: 244px;
    }
    /deep/ .dialog-footer {
      position: absolute;
      display: flex;
      flex-direction: row;
      bottom: 10px;
      right: 20px;
    }
  }
  .className {
    width: 200px;
    overflow: hidden;
  }

  .tooltip {
    line-height: 50px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}
</style>
