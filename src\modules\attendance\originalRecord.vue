<template>
  <div class="dayStatistical def_per_height" ref="contain">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span>原始记录</span>
        </el-col>
      </el-row>
    </header>
    <div class="filterBox" ref="searchForm">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="日期:">
          <el-date-picker
            v-model="date"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            format="yyyy 年 MM 月 dd 日"
            @change="changeTimeGetDate"
            :picker-options="pickerOptions0"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="选择人员:">
          <el-select
            v-model="selectPerson"
            placeholder="活动区域"
            @change="handleSelect"
          >
            <el-option
              v-for="item in peopleList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="choosePeople + ':'"
          v-show="selectPerson == 'attendance'"
        >
          <el-button @click="openVisible = true" v-if="namelist.length > 0">
            <p class="className">已选择{{ namelist.length }}个考勤祖</p>
            <p
          /></el-button>
          <el-button @click="openVisible = true" v-if="namelist.length == 0">
            + 请选择{{ choosePeople }}</el-button
          >
        </el-form-item>
        <el-form-item v-show="selectPerson !== 'quit'">
          <el-checkbox v-model="checked">{{ description }}</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button @click="getExportRec">导出</el-button>
        </el-form-item>
      </el-form>
      <!--      <el-form>-->
      <!--        <el-button @click="openFilterDialog">+ 自定义列表显示字段</el-button>-->
      <!--        <el-button>导出</el-button>-->
      <!--      </el-form>-->
    </div>
    <div class="statisticalList">
      <el-table
        class="table-fixed"
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        ref="table"
        :header-cell-style="{ background: '#F1F1F1' }"
      >
        <el-table-column
          prop="empName"
          label="姓名"
          width="120"
          fixed
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column label="基本信息">
          <!-- <el-table-column
            prop="taxSubName"
            label="用工主体"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column> -->
          <el-table-column prop="departmentName" label="部门" width="120">
            <template slot-scope="scope">
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
                v-if="scope.row.departmentNames"
              >
                <div slot="content">
                  <p
                    v-for="(val, index) in scope.row.departmentNames"
                    :key="index"
                  >
                    {{ val }}
                  </p>
                </div>
                <p class="tooltip">
                  <span
                    v-for="(val, index) in scope.row.departmentNames"
                    :key="index"
                    >{{ index === 0 ? "" + val : "/" + val }}</span
                  >
                </p>
              </el-tooltip>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="empNo"
            label="工号"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column> -->
          <el-table-column
            prop="postName"
            label="岗位"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="reportTo"
            label="上级"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table-column>
        <el-table-column label="打卡信息">
          <el-table-column
            prop="signDate"
            label="日期"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="agName"
            label="考勤组"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="timeZone"
            label="时区"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="workDate"
            label="考勤时间"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="signTime"
            label="打卡时间"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="signResult"
            label="打卡结果"
            width="120"
            :show-overflow-tooltip="true"
          >
            <!-- <template slot-scope="scope">
              <span>{{ getSignResult(scope.row.signResult) }}</span>
            </template> -->
          </el-table-column>
          <el-table-column
            prop="recordAddress"
            label="打卡地址"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="temperature"
            label="体温（℃）"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="signDescription"
            label="打卡备注"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            v-for="(v, i) in headerInfo"
            :key="i"
            :prop="'signImages' + i"
            :label="'打卡图片' + (i + 1)"
            width="120"
          >
            <template slot-scope="scope">
              <div class="clockImage">
                <a
                  :href="scope.row['signImages' + i]"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img :src="scope.row['signImages' + i]" alt="" />
                </a>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="deviceImei"
            label="打卡设备信息"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table-column>
        <el-table-column label="管理员修改信息">
          <el-table-column
            prop="managerDescription"
            label="管理员修改备注"
            width="120"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination" ref="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="100"
        layout="prev, pager, next, sizes, jumper"
        :total="pagination.total"
        background
      >
      </el-pagination>
    </div>
    <!-- 选择人员 -->
    <div class="choosePerson">
      <el-dialog
        :title="choosePeople"
        v-if="openVisible"
        :visible.sync="openVisible"
        width="600px"
      >
        <span class="dialogContent">
          <div class="left">
            <el-input
              v-model="inputValue"
              :placeholder="choosePeople"
              @input="search"
            ></el-input>
            <i class="el-icon-search" @click="search"></i>
            <div v-if="selectPerson === 'attendance'" class="left-content">
              <el-checkbox-group v-model="checkAttPerson">
                <el-checkbox
                  v-for="val in inputValue
                    ? filterAttendPersons
                    : attendPersons"
                  :label="val"
                  :key="val.id"
                  >{{ val.name }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
            <div v-if="selectPerson === 'department'" class="left-content">
              <el-tree
                :data="departmentList"
                show-checkbox
                ref="treeDepartment"
                node-key="id"
                :props="defaultProps"
                :filter-node-method="filterNode"
                @check="clickCheck"
                @current-change="currnetChange"
              >
              </el-tree>
            </div>
            <div v-if="selectPerson === 'main'" class="left-content">
              <el-checkbox-group v-model="checkMain">
                <el-checkbox
                  v-for="val in inputValue ? filterEmployments : employments"
                  :label="val"
                  :key="val.taxSubId"
                  >{{ val.taxSubName }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
            <div v-if="selectPerson === 'quit'" class="left-content">
              <el-checkbox-group v-model="checkAlready">
                <el-checkbox
                  v-for="val in inputValue
                    ? alreadyFilterPerson
                    : alreadyPerson"
                  :label="val"
                  :key="val.empId"
                  >{{ val.empName }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
          </div>
          <i class="divider"></i>
          <ul class="right">
            <li
              v-for="item in selectPerson === 'attendance'
                ? checkAttPerson
                : selectPerson === 'main'
                ? checkMain
                : selectPerson === 'quit'
                ? checkAlready
                : rightList"
              :key="item.id"
            >
              <span>{{
                selectPerson === "main"
                  ? item.taxSubName
                  : selectPerson === "quit"
                  ? item.empName
                  : item.name
              }}</span>
              <i
                class="el-icon-close"
                @click="
                  removeItem(
                    selectPerson === 'main'
                      ? item.taxSubId
                      : selectPerson === 'quit'
                      ? item.empId
                      : item.id
                  )
                "
              ></i>
            </li>
          </ul>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="clearInputValue">取 消</el-button>
          <el-button type="primary" @click="userChecked(selectPerson)"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </div>
    <monthStatisFilterDialog ref="child"></monthStatisFilterDialog>
  </div>
</template>

<script>
import monthStatisFilterDialog from "./components/monthStatisFilterDialog";

export default {
  components: {
    monthStatisFilterDialog,
  },
  data() {
    return {
      loading: false,
      pickerOptions0: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6;
        },
      },
      description: "离开考勤组的人员",
      pagination: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      abnormalRadio: "1",
      lateTime: "",
      checkAttPerson: [],
      checkQuitPerson: [],
      checkMain: [],
      inputValue: "",
      checkAlready: [], // 选中已离职人员
      alreadyPerson: [], // 已离职员工
      alreadyFilterPerson: [], // 过滤后的已离职员工
      attendPersons: [], // 考勤组
      filterAttendPersons: [], // 过滤后的考勤组
      employments: [], // 用工主体
      filterEmployments: [], // 过滤后的用工主体
      outAttendGroup: false, // 考勤组是否已选中
      tableHeight: document.body.clientHeight - 300, // 表格自适应高度
      openVisible: false,
      selectPerson: "attendance",
      choosePeople: "考勤组",
      peopleList: [
        // {
        //   label: "全公司",
        //   value: "company"
        // },
        // {
        //   label: "用工主体",
        //   value: "main"
        // },
        {
          label: "考勤组",
          value: "attendance",
        },
        // {
        //   label: "部门/人员",
        //   value: "department"
        // },
        // {
        //   label: "从已离职人员选取",
        //   value: "quit"
        // }
      ],
      departmentList: [],
      rightList: [],
      defaultProps: {
        children: "userResults",
        label: "name",
      },
      checked: false,
      date: "",
      tableData: [],
      list: [], // 选中人员ID
      attendIds: [], // 考勤组ID
      taxSubIds: [], // 用工主体ID
      empStatus: ["ON_THE_JOB"], // 离职状态
      isLateTime: true, // 是否可输入迟到/早退时间
      headerInfo: [], // 个人统计头部信息
      currentRow: [], // 选中当前行数据
      maxImages: null, // 记录打卡图片最大数量
      isCompany: true, //是否为全公司
      namelist: [], //名称数组
    };
  },
  watch: {
    // 检测打卡选项
    abnormalRadio(newVal) {
      if (newVal === "NORMAL" || newVal === "ABSENT_WORK") {
        this.lateTime = "";
        this.isLateTime = true;
      } else if (newVal === "BE_LATE" || newVal === "LEAVE_EARLIER") {
        this.isLateTime = false;
      }
    },

    // 是否选中离职人员
    checked(val) {
      if (this.selectPerson !== "attendance") {
        if (val) {
          this.empStatus = ["ON_THE_JOB", "DIMISSION"];
        } else {
          this.empStatus = ["ON_THE_JOB"];
        }
        this.outAttendGroup = false;
        if (this.selectPerson === "department") {
          this.rightList = [];
          this.getDepartmentAndEmpList();
          this.userChecked("department");
          return;
        }
      } else {
        if (val) {
          this.outAttendGroup = true;
        } else {
          this.outAttendGroup = false;
        }
        this.empStatus = ["ON_THE_JOB"];
      }

      this.getCountOfDayByEmpIds();
    },

    // 人员选择
    selectPerson(val) {
      switch (val) {
        case "company":
          this.description = "离职人员";
          this.list = [];
          this.attendIds = [];
          this.rightList = [];
          this.checkAttPerson = [];
          this.taxSubIds = [];
          this.checkMain = [];
          this.isCompany = true;
          this.getCountOfDayByEmpIds();
          break;
        case "main":
          this.choosePeople = "用工主体";
          this.description = "离职人员";
          this.list = [];
          this.attendIds = [];
          this.rightList = [];
          this.checkAttPerson = [];
          this.isCompany = false;
          this.getEmployment();
          break;
        case "attendance":
          this.choosePeople = "考勤组";
          this.description = "离开考勤组的人员";
          this.list = [];
          this.taxSubIds = [];
          this.checkAttPerson = [];
          this.checkMain = [];
          this.isCompany = false;
          this.getAttendGroupList();
          break;
        case "department":
          this.choosePeople = "部门/人员";
          this.description = "离职员工";
          this.taxSubIds = [];
          this.attendIds = [];
          this.checkMain = [];
          this.rightList = [];
          this.isCompany = false;
          this.getDepartmentAndEmpList();
          break;
        case "quit":
          this.choosePeople = "离职人员";
          this.description = "已离职员工";
          this.taxSubIds = [];
          this.attendIds = [];
          this.checkMain = [];
          this.rightList = [];
          this.isCompany = false;
          this.getAlreadyPeople();
          break;
      }
      this.checked = false;
    },

    //table重新布局
    tableData() {
      console.log(222);
      this.doLayout();
    },
  },
  created() {
    this.getMouthEndStart();
    this.getAttendGroupList();
  },
  mounted() {
    this.getCountOfDayByEmpIds();
    // this.getTableHeight();
  },
  methods: {
    doLayout() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },

    // 获取当月月初和月末
    getMouthEndStart() {
      var nowDate = new Date();
      var cloneNowDate = new Date();
      var fullYear = nowDate.getFullYear();
      var month = nowDate.getMonth() + 1;
      var endOfMonth = new Date(fullYear, month, 0).getDate();
      function getFullDate(targetDate) {
        var D, y, m, d;
        if (targetDate) {
          D = new Date(targetDate);
          y = D.getFullYear();
          m = D.getMonth() + 1;
          d = D.getDate();
        } else {
          y = fullYear;
          m = month;
          d = date;
        }
        m = m > 9 ? m : "0" + m;
        d = d > 9 ? d : "0" + d;
        return y + "-" + m + "-" + d;
      }
      this.endDate = getFullDate(Date.now() - 8.64e6); //当月最后一天
      this.startDate = getFullDate(cloneNowDate.setDate(1)); //当月第一天
      this.date = [this.startDate, this.endDate];
    },
    created() {
      this.getMouthEndStart();
      this.getAttendGroupList();
    },
    // 树过滤
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    // 搜索树信息
    search() {
      if (this.selectPerson === "attendance") {
        this.filterAttendPersons = this.attendPersons.filter((v) =>
          v.name.includes(this.inputValue)
        );
      } else if (this.selectPerson === "main") {
        this.filterEmployments = this.employments.filter((v) =>
          v.taxSubName.includes(this.inputValue)
        );
      } else if (this.selectPerson === "department") {
        this.$refs.treeDepartment.filter(this.inputValue);
      } else {
        this.alreadyFilterPerson = this.alreadyPerson.filter((v) =>
          v.empName.includes(this.inputValue)
        );
      }
    },

    // 获取列表自定义高度
    getTableHeight() {
      this.tableHeight =
        this.$refs.contain.offsetHeight -
        this.$refs.pagination.offsetHeight -
        this.$refs.searchForm.offsetHeight -
        70;
    },

    // 打卡状态回显
    punchStatus(status) {
      switch (status) {
        case "NORMAL":
          return "正常";
          break;
        case "BE_LATE":
          return "迟到";
          break;
        case "LEAVE_EARLIER":
          return "早退";
          break;
        case "ABSENT_WORK":
          return "缺卡";
          break;
        case "HOOKY_WORK":
          return "旷工";
          break;
        case "OUTSIDE_ATTEND":
          return "外勤";
          break;
        case "OUTSIDE_WORK":
          return "外出";
          break;
        case "BUSINESS_TRIP":
          return "出差";
          break;
        case "OVER_TIME":
          return "加班";
          break;
        case "LEAVE":
          return "请假";
          break;
        case "SUPPLY_PASS":
          return "补卡通过";
          break;
        case "RESTDAY":
          return "休息日";
          break;
      }
    },

    // 人员选择确定
    userChecked(val) {
      this.pagination.currentPage = 1;
      switch (val) {
        case "department":
          this.isCompany = false;
          this.attendIds = [];
          this.taxSubIds = [];
          this.list = this.getUserSet(this.rightList, "id");
          this.namelist = this.getUserSet(this.rightList, "name");
          this.getCountOfDayByEmpIds();
          break;
        case "attendance":
          this.isCompany = false;
          this.list = [];
          this.taxSubIds = [];
          this.attendIds = this.getUserSet(this.checkAttPerson, "id");
          this.namelist = this.getUserSet(this.checkAttPerson, "name");
          this.getCountOfDayByEmpIds();
          break;
        case "main":
          this.isCompany = false;
          this.list = [];
          this.attendIds = [];
          this.taxSubIds = this.getUserSet(this.checkMain, "taxSubId");
          this.namelist = this.getUserSet(this.checkMain, "taxSubName");
          this.getCountOfDayByEmpIds();
          break;
        case "quit":
          this.isCompany = false;
          this.attendIds = [];
          this.taxSubIds = [];
          this.list = this.getUserSet(this.checkAlready, "empId");
          this.namelist = this.getUserSet(this.checkAlready, "empName");
          this.getCountOfDayByEmpIds();
          break;
      }
      this.clearInputValue();
    },

    // 对话框关闭
    clearInputValue() {
      this.openVisible = false;
      this.inputValue = "";
    },

    // 人员选择获取人员id集合
    getUserSet(list, attr) {
      let newList = [];
      for (let i = 0; i < list.length; i++) {
        if (list[i].userResults) {
          newList = newList.concat(list[i].userResults.map((v) => v[attr]));
          continue;
        }
        newList = newList.concat(list[i][attr]);
      }
      return newList;
    },

    // 修改时间后更新数据
    changeTimeGetDate(newVal) {
      if (!newVal) {
        this.$message.error("请输入日期");
        return;
      }
      if (newVal) {
        if (
          new Date(newVal[1]).getTime() - new Date(newVal[0]).getTime() >
          30 * 24 * 3600 * 1000
        ) {
          this.$message({
            type: "error",
            message: "开始日期至结束日期最长时长为31天，请重新选择",
          });
          this.date = [];
          return;
        }
      }
      (this.pagination.currentPage = 1), this.getCountOfDayByEmpIds();
    },

    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getCountOfDayByEmpIds();
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.getCountOfDayByEmpIds();
      console.log(`当前页: ${val}`);
    },
    //弹出筛选框
    openFilterDialog() {
      this.$refs.child.openDialog();
    },
    //选择人员弹框
    chooseType(type) {
      console.log(type);
    },

    clickCheck(val, ischeck) {
      console.log("val", val, "check", ischeck);
      if (this.rightList.some((item) => item.id === val.id)) {
        this.removeItem(val.id);
      } else {
        this.rightList.push(val);
      }
    },

    // 将返回的后台数据拼接成规范的表格数据
    transformBaseData(list) {
      let newList = [];
      if ((list && !list.length) || !list) return;
      list.forEach((v, i) => {
        newList[i] = {};
        for (let item in v) {
          if (typeof v[item] !== "object") {
            newList[i][item] = v[item];
          } else if (item === "departmentNames") {
            newList[i][item] = v[item];
          } else {
            for (let x in v[item]) {
              if (Array.isArray(v[item][x])) {
                if (v[item][x].length > this.headerInfo.length) {
                  this.headerInfo = v[item][x];
                }
                v[item][x].forEach((j, k) => {
                  newList[i][x + k] = j;
                });
                continue;
              }
              newList[i][x] = v[item][x];
            }
          }
        }
      });
      return newList;
    },

    // 获取用工主体数据
    async getEmployment() {
      const { data } = await this.$attApi.getTaxSubjectByComp();
      this.employments = data;
    },

    // 获取考勤组数据
    async getAttendGroupList() {
      const { data } = await this.$attApi.getAttendGroupList();
      this.attendPersons = data.organizeAndUserResults;
    },

    // 获取部门/员工
    async getDepartmentAndEmpList() {
      let send = {
        empStatus: "",
      };
      if (String(this.empStatus) !== String(["ON_THE_JOB"])) {
        send.empStatus = "DIMISSION";
      }
      const { data } = await this.$attApi.getDepartmentAndEmpList(send);
      this.departmentList = data.organizeAndUserResults;
    },

    // 获取已离职人员
    async getAlreadyPeople() {
      const { data } = await this.$attApi.getLeaveJobPerson();
      this.alreadyPerson = data;
    },
    async getAlreadyPeople() {
      const { data } = await this.$attApi.getLeaveJobPerson();
      this.alreadyPerson = data;
    },
    handleSelect() {
      this.namelist = [];
    },
    // 获取员工日打卡信息
    async getCountOfDayByEmpIds() {
      this.loading = true;
      let send = {
        currPage: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        empIds: this.list,
        taxSubIds: this.taxSubIds,
        attendIds: this.attendIds,
        empStatus: this.empStatus,
        outAttendGroup: this.outAttendGroup,
        startDate: this.date ? this.date[0] : "",
        endDate: this.date ? this.date[1] : "",
        isCompany: this.isCompany,
      };
      // const send = {
      //   attendIds: [],
      //   currPage: 1,
      //   empIds: [69380],
      //   empStatus: [],
      //   endDate: "2021-03-29",
      //   pageSize: 20,
      //   startDate: "2021-03-20"
      // }

      const { data } = await this.$attApi.getSignRecordByEmpIds(send);
      this.loading = false;
      if (data) {
        this.pagination.total = data.total;
        this.tableData = this.transformBaseData(data.records);
      } else {
        this.pagination.total = 0;
        this.tableData = [];
      }
    },

    // 移除选中人员/部门/考勤组/用工主体
    removeItem(id) {
      if (this.selectPerson === "department") {
        this.rightList = this.rightList.filter((item) => item.id !== id);
        this.$refs.treeDepartment.setCheckedNodes(this.rightList);
      } else if (this.selectPerson === "main") {
        this.checkMain = this.checkMain.filter((item) => item.taxSubId !== id);
      } else if (this.selectPerson === "quit") {
        this.checkAlready = this.checkAlready.filter(
          (item) => item.empId !== id
        );
      } else {
        this.checkAttPerson = this.checkAttPerson.filter(
          (item) => item.id !== id
        );
      }
    },
    currnetChange(val, node) {
      console.log(val);
      console.log(node);
    },
    getSignResult(data) {
      switch (data) {
        case "NORMAL":
          return "正常";
        case "BE_LATE":
          return "迟到";
        case "LEAVE_EARLIER":
          return "早退";
        case "ABSENT_WORK":
          return "缺卡";
        case "HOOKY_WORK":
          return "旷工";
        case "OUTSIDE_ATTEND":
          return "外勤";
        case "OUTSIDE_WORK":
          return "外出";
        case "BUSINESS_TRIP":
          return "出差";
        case "OVER_TIME":
          return "加班";
        case "LEAVE":
          return "请假";
        case "SUPPLY_PASS":
          return "补卡通过";
        case "RESTDAY":
          return "休息日";
      }
    },
    async getExportRec() {
      if (!this.date) {
        this.$message.error("请选择导出日期");
        return;
      }
      let send = {
        currPage: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        empIds: this.list,
        taxSubIds: this.taxSubIds,
        attendIds: this.attendIds,
        empStatus: this.empStatus,
        outAttendGroup: this.outAttendGroup,
        startDate: this.date[0],
        endDate: this.date[1],
        isCompany: this.isCompany,
      };
      await this.$attApi.getExportRec(send).then((res) => {
        let content = res;
        let blob = new Blob([content], { type: "application/vnd.x-xls" });
        if ("download" in document.createElement("a")) {
          const link = document.createElement("a");
          link.download =
            "原始记录表-" +
            this.date[0].split("-").join("") +
            "-" +
            this.date[1].split("-").join("") +
            "考勤统计.xlsx";
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          document.body.appendChild(link);
          link.click();
          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);
        } else {
          navigator.msSaveBlob(blob);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.dayStatistical {
  // height: 100%;
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }
  .filterBox {
    padding: 20px 20px 0 20px;
    /deep/ .el-range-editor.el-input__inner input {
      width: 130px;
    }
  }
  .statisticalList {
    padding: 0 22px;
    .el-table--scrollable-x .el-table__body-wrapper {
      overflow-x: auto;
      z-index: 9999;
    }
    .clockImage {
      display: flex;
      flex-direction: row;
      justify-content: center;
      img {
        width: auto;
        height: auto;
        max-width: 100px;
        max-height: 100px;
      }
    }
  }
  .pagination {
    float: right;
    padding: 20px 22px 20px 0;
  }
  /deep/ .el-dialog__body {
    padding-top: 10px;
  }
  .dialogContent {
    .divider {
      width: 1px;
      height: 68%;
      background: #dddddd;
      position: absolute;
      left: 50%;
      top: 16%;
    }
    .el-input {
      width: 240px;
      height: 40px;
      padding-bottom: 10px;
    }
    .el-icon-search {
      position: relative;
      right: 30px;
      color: #909399;
    }
    .left {
      height: 270px;
      width: 280px;
      .left-content {
        height: 222px;
        overflow-y: auto;
        overflow-x: hidden;
      }
      /deep/ .el-checkbox-group {
        display: flex;
        flex-direction: column;
        .el-checkbox {
          padding-bottom: 5px;
          display: flex;
          align-items: center;
          .el-checkbox__label {
            width: 230px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
    .right {
      width: 250px;
      height: 270px;
      overflow: auto;
      li {
        position: relative;
        height: 30px;
        line-height: 30px;
        background: #d9eafc;
        padding: 0 20px 0 5px;
        margin-bottom: 5px;
        width: 220px;
        white-space: nowrap;
        overflow-x: hidden;
        text-overflow: ellipsis;
        .el-icon-close {
          position: absolute;
          right: 5px;
          top: 8px;
          color: #909399;
          cursor: pointer;
        }
      }
    }
  }
  .abnormalContent {
    .personInfo {
      display: flex;
      width: 100%;
      height: 60px;
      flex-direction: row;
      align-items: center;
      background: #eee;
      margin-bottom: 10px;
      .name {
        width: auto;
        height: 100%;
        background: #4f71ff;
        border-radius: 4px;
        text-align: center;
        margin: 0 10px 0;
      }
    }
  }
  .abnormal {
    color: #b8741a;
    text-decoration: underline;
    cursor: pointer;
  }
  .choosePerson {
    /deep/ .el-dialog {
      height: 400px;
    }
    /deep/ .el-dialog__body {
      padding: 10px 20px 30px;
      height: 244px;
    }
    /deep/ .dialog-footer {
      position: absolute;
      display: flex;
      flex-direction: row;
      bottom: 10px;
      right: 20px;
    }
  }

  .tooltip {
    line-height: 50px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}
.className {
  width: 200px;
  overflow: hidden;
}
</style>
