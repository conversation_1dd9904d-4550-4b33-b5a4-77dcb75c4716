<template>
  <div class="overtimeDetail def_per_height">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span>加班明细</span>
        </el-col>
      </el-row>
    </header>
    <div class="content">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="日期:">
          <el-date-picker
            v-model="date"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="changeTimeGetDate"
            format="yyyy 年 MM 月 dd 日"
            value-format="yyyy-MM-dd"
            :pickerOptions="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="选择人员:">
          <el-button @click="openPerson" v-if="namelist.length > 0">
            <p class="className">已选择{{ namelist.length }}个人员</p>
            <p
          /></el-button>
          <el-button @click="selectPerson" v-if="namelist.length == 0">
            + 请选择人员</el-button
          >
        </el-form-item>
        <span class="exportBtn">
          <el-button @click="exportFile">导出</el-button>
        </span>
      </el-form>
      <el-table
        :header-cell-style="{ background: '#F1F1F1' }"
        :data="tableData"
        v-loading="loading"
        border
      >
        <el-table-column
          label="姓名"
          prop="empName"
          min-width="100"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column label="部门" min-width="100">
          <template slot-scope="scope">
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              v-if="scope.row.deptNames"
            >
              <div slot="content">
                <p v-for="(val, index) in scope.row.deptNames" :key="index">
                  {{ val }}
                </p>
              </div>
              <p class="tooltip">
                <span
                  v-for="(val, index) in scope.row.deptNames"
                  :key="index"
                  >{{ index === 0 ? "" + val : "/" + val }}</span
                >
              </p>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="加班日期"
          prop="startDate"
          min-width="70"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{
              scope.row.detailList.length && scope.row.detailList[0].startDate
                ? scope.row.detailList[0].startDate.substring(0, 10)
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column label="加班总时长" prop="value" min-width="70">
          <template slot-scope="scope">
            {{
              (scope.row.detailList.length
                ? scope.row.detailList[0].value
                : "") +
              getUnit(
                scope.row.detailList.length
                  ? scope.row.detailList[0].leaveUnit
                  : ""
              )
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="加班时长（记加班费）"
          prop="payeeDuration"
          min-width="70"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{
              scope.row.payeeDuration +
              getUnit(
                scope.row.detailList.length
                  ? scope.row.detailList[0].leaveUnit
                  : ""
              )
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="加班时长（记调休）"
          prop="holidayDuration"
          min-width="70"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{
              scope.row.holidayDuration +
              getUnit(
                scope.row.detailList.length
                  ? scope.row.detailList[0].leaveUnit
                  : ""
              )
            }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="prev, pager, next, sizes, jumper"
          :total="total"
          background
        >
        </el-pagination>
      </div>
    </div>
    <!-- 选择人员 -->
    <div class="choosePerson">
      <el-dialog
        title="请选择部门或人员"
        v-if="openVisible"
        :visible.sync="openVisible"
        width="600px"
      >
        <span class="dialogContent">
          <div class="left" v-loading="loading1">
            <el-input
              v-model="inputValue"
              placeholder="请选择部门或人员"
              @input="search"
            ></el-input>
            <i class="el-icon-search" @click="search"></i>

            <div class="left-content">
              <el-tree
                :data="departmentList"
                show-checkbox
                ref="treeDepartment"
                node-key="id"
                :props="defaultProps"
                :filter-node-method="filterNode"
                @check="clickCheck"
                @current-change="currentChange"
              >
                <span slot-scope="{ data }">
                  <span class="show-ellipsis">{{ data.name }}</span>
                </span>
              </el-tree>
            </div>
          </div>
          <i class="divider"></i>
          <ul class="right">
            <li v-for="item in rightList" :key="item.id">
              <span>{{ item.name }}</span>
              <i class="el-icon-close" @click="removeItem(item.id)"></i>
            </li>
          </ul>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="clearInputValue">取 消</el-button>
          <el-button type="primary" @click="userChecked">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6;
        },
      },
      openVisible: false,
      tableData: [],
      total: 0,
      currPage: 1,
      key: "",
      pageSize: 10,
      queryEndTime: "",
      queryStartTime: "",
      loading: false,
      loading1: false, //树状弹框加载
      screenHeight: document.body.clientHeight - 300, //表格自适应高度
      inputValue: "", //人员搜索
      date: "", //加班日期
      empIds: [], //选择人员id集合
      namelist: [], //选中人员名称集合
      departmentList: [],
      rightList: [], //右侧选择人员部门
      defaultProps: {
        children: "userResults",
        label: "name",
      },
      checked: false,
      tableData: [],
    };
  },

  created() {
    this.getMouthEndStart();
    this.getDetailList();
  },
  methods: {
    //获取时长单位
    getUnit(type) {
      switch (type) {
        case "HOUR":
          return "小时";
          break;
        case "MINUTES":
          return "分钟";
          break;
      }
    },
    // 树过滤
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 搜索树信息
    search() {
      this.$refs.treeDepartment.filter(this.inputValue);
    },
    selectPerson() {
      this.openVisible = true;
      this.loading1 = true;
      this.inputValue = "";
      this.$attApi.getDepartmentAndEmpList({ empStatus: "" }).then((res) => {
        this.loading1 = false;
        if (res.success) {
          this.departmentList = res.data.organizeOrUserResults;
        }
      });
    },
    clickCheck(val, ischeck) {
      console.log("val", val, "check", ischeck);
      this.rightList = ischeck.checkedNodes.filter((val) => {
        return !val.hasChildren;
      });
      //   if (this.rightList.some(item => item.id === val.id)) {
      //     this.removeItem(val.id);
      //   } else {
      // this.rightList.push(val);
      //   }
    },
    currentChange(val, node) {
      console.log(val);
      console.log(node);
    },
    removeItem(id) {
      this.rightList = this.rightList.filter((item) => item.id !== id);
      this.$refs.treeDepartment.setCheckedNodes(this.rightList);
    },
    // 对话框关闭
    clearInputValue() {
      this.openVisible = false;
      this.inputValue = "";
    },
    openPerson() {
      this.openVisible = true;
      this.$nextTick(() => {
        this.$refs.treeDepartment.setCheckedNodes(this.rightList);
      });
    },

    // 人员选择确定
    userChecked(val) {
      this.attendIds = [];
      this.taxSubIds = [];
      this.isCompany = false;
      this.empIds = this.getUserSet(this.rightList, "id");
      this.namelist = this.getUserSet(this.rightList, "name");
      this.openVisible = false;
      this.inputValue = "";
      this.getDetailList();
    },
    // 人员选择获取人员id集合
    getUserSet(list, attr) {
      let newList = [];
      for (let i = 0; i < list.length; i++) {
        if (list[i].userResults && list[i].userResults.length) {
          newList = newList.concat(list[i].userResults.map((v) => v[attr]));
          continue;
        }
        newList = newList.concat(list[i][attr]);
      }
      return newList;
    },
    // 获取当月月初和月末
    getMouthEndStart() {
      var nowDate = new Date();
      var cloneNowDate = new Date();
      var fullYear = nowDate.getFullYear();
      var month = nowDate.getMonth() + 1;
      var endOfMonth = new Date(fullYear, month, 0).getDate();
      function getFullDate(targetDate) {
        var D, y, m, d;
        if (targetDate) {
          D = new Date(targetDate);
          y = D.getFullYear();
          m = D.getMonth() + 1;
          d = D.getDate();
        } else {
          y = fullYear;
          m = month;
          d = date;
        }
        m = m > 9 ? m : "0" + m;
        d = d > 9 ? d : "0" + d;
        return y + "-" + m + "-" + d;
      }
      this.endDate = getFullDate(Date.now() - 8.64e6); //当月最后一天
      this.startDate = getFullDate(cloneNowDate.setDate(1)); //当月第一天
      this.date = [this.startDate, this.endDate];
    },
    getDetailList(type) {
      console.log(this.date);
      this.loading = true;
      let params = {
        currPage: type === "search" ? 1 : this.currPage,
        empIds: this.empIds,
        pageSize: this.pageSize,
        start: this.date ? this.date[0] : "",
        end: this.date ? this.date[1] : "",
      };
      this.$attApi.getOverTimeDetailList(params).then((res) => {
        this.loading = false;
        if (res.success) {
          const DATA = res.data.records;
          this.tableData = res.data.records;
          this.total = res.data.total;
        }
      });
    },

    handleSelect() {
      this.namelist = [];
    },
    //导出
    exportFile() {
      if (!this.date) {
        this.$message.error("请选择导出日期");
        return;
      }
      let params = {
        currPage: this.currPage,
        empIds: this.empIds,
        pageSize: this.pageSize,
        start: this.date[0],
        end: this.date[1],
      };
      this.$attApi.getExportOverTime(params).then((res) => {
        let content = res;
        let blob = new Blob([content], { type: "application/vnd.x-xls" });
        if ("download" in document.createElement("a")) {
          const link = document.createElement("a");
          link.download =
            "加班明细-" +
            this.date[0].split("-").join("") +
            "-" +
            this.date[1].split("-").join("") +
            "统计详情.xlsx";
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          document.body.appendChild(link);
          link.click();
          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);
        } else {
          navigator.msSaveBlob(blob);
        }
      });
    },
    // 修改时间后更新数据
    changeTimeGetDate(newVal) {
      if (!newVal) {
        this.$message.error("请输入日期");
        return;
      }
      if (
        new Date(newVal[1]).getTime() - new Date(newVal[0]).getTime() >
        30 * 24 * 3600 * 1000
      ) {
        this.$message({
          type: "error",
          message: "加班日期结束时间比开始时间不能多于31天, 请重新选择",
        });
        this.date = [];
        return;
      }
      this.date = newVal;
      this.currPage = 1;
      this.getDetailList();
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.getDetailList();
    },
    handleCurrentChange(val) {
      this.currPage = val;
      this.getDetailList();
    },
  },
};
</script>

<style lang="scss" scoped>
.overtimeDetail {
  .content {
    padding: 20px 20px 0 20px;
    .exportBtn {
      float: right;
      .el-button {
        width: 150px;
      }
    }
    // padding: 22px;
    .cardRange {
      line-height: 50px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
    .pagination {
      float: right;
      padding: 22px 0 22px 22px;
    }
    /deep/ .el-range-editor.el-input__inner input {
      width: 130px;
    }
  }
  .choosePerson {
    /deep/ .el-dialog {
      height: 400px;
    }
    /deep/ .el-dialog__body {
      padding: 10px 20px 30px;
      height: 244px;
    }
    /deep/ .dialog-footer {
      position: absolute;
      display: flex;
      flex-direction: row;
      bottom: 10px;
      right: 20px;
    }
    .dialogContent {
      .divider {
        width: 1px;
        height: 68%;
        background: #ddd;
        position: absolute;
        left: 50%;
        top: 16%;
      }
      .el-input {
        width: 240px;
        height: 40px;
        padding-bottom: 10px;
      }
      .el-icon-search {
        position: relative;
        right: 30px;
        color: #909399;
      }
      .left {
        height: 280px;
        width: 280px;
        .left-content {
          height: 222px;
          overflow-y: auto;
          overflow-x: hidden;
        }
        /deep/ .el-checkbox-group {
          display: flex;
          flex-direction: column;
          .el-checkbox {
            padding-bottom: 5px;
            display: flex;
            align-items: center;
            .el-checkbox__label {
              width: 230px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
        .show-ellipsis {
          display: block;
          width: 180px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .right {
        width: 250px;
        height: 270px;
        overflow-y: auto;
        li {
          position: relative;
          height: 30px;
          line-height: 30px;
          background: #d9eafc;
          padding: 0 10px 0 5px;
          margin-bottom: 5px;
          width: 220px;
          white-space: nowrap;
          overflow-x: hidden;
          text-overflow: ellipsis;
          .el-icon-close {
            position: absolute;
            right: 5px;
            top: 8px;
            color: #909399;
            cursor: pointer;
          }
        }
      }
    }
  }
  .tooltip {
    line-height: 50px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}
.header {
  border-bottom: 1px solid #ededed;
}
</style>
