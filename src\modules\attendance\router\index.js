const workAttendance = () => import('../workAttendance');
const shift = () => import('../shift');
const workOvertime = () => import('../workOvertime');
const overtimeDetail = () => import('../overtimeDetail');
const cardRule = () => import('../cardRule');

const holidayBalance = () => import('../holidayManage'); //假期余额
const holidayType = () => import('../holidayType'); //假期类型

const holidayAdd = () => import('../components/holiday/addHoliday');
const dayStatistical = () => import('../dayStatistical');
const monthStatistical = () => import('../monthStatistical');
const clockTime = () => import('../clockTime');
const originalRecord = () => import('../originalRecord');
const addAttendance = () => import('../components/attendance/addAttendance');
// const addAttendanceTianDi = () => import('../components/attendance/addAttendance_tiandi');
const addShift = () => import('../components/addShift');
const addRule = () => import('../components/addRule');
const excelBatch = () => import('../components/holiday/excelBatch');
const personBalance = () => import('../components/holiday/personBalance');
const editSchedule = () => import('../components/attendance/editSchedule');
const addOvertime = () => import('../components/addOvertime');
const faceManage = () => import('../faceManage'); //人脸管理

export default [
  {
    path: '/attendance/workAttendance',
    component: workAttendance,
    meta: {
      businessCode: 'hrAttend.attendManage.group',
      icon: 'icondaka',
    },
  },
  {
    path: '/attendance/shift',
    component: shift,
    meta: {
      businessCode: 'hrAttend.attendManage.work',
      icon: 'icondaka',
    },
  },

  {
    path: '/attendance/face',
    component: faceManage,
    meta: {
      businessCode: 'hrAttend.attendManage.faceManager',
      icon: 'icondaka',
    },
  },
  {
    path: '/attendance/cardRule',
    component: cardRule,
    meta: {
      businessCode: 'hrAttend.attendManage.supplement',
      icon: 'icondaka',
    },
  },
  {
    path: '/attendance/workOvertime',
    component: workOvertime,
    meta: {
      businessCode: 'hrAttend.attendManage.overTime',
      icon: 'icondaka',
    },
  },
  {
    path: '/attendance/overtimeDetail',
    component: overtimeDetail,
    meta: {
      businessCode: 'hrAttend.attendManage.overTimeDetail',
      icon: 'icondaka',
    },
  },
  {
    path: '/attendance/holidayBalance',
    component: holidayBalance,
    meta: {
      businessCode: 'hrAttend.attendManage.leave',
      icon: 'icondaka',
    },
  },

  {
    path: '/attendance/holidayType',
    component: holidayType,
    meta: {
      businessCode: 'hrAttend.attendManage.leave',
      icon: 'icondaka',
    },
  },

  {
    path: '/attendance/holidayAdd',
    component: holidayAdd,
    meta: {
      // businessCode: 'hrAttend.attendManage.holidayAdd',
    },
  },
  {
    path: '/attendance/dayStatistical',
    component: dayStatistical,
    meta: {
      businessCode: 'hrAttend.attendManage.dailyCount',
      icon: 'icondaka',
    },
  },
  {
    path: '/attendance/monthStatistical',
    component: monthStatistical,
    meta: {
      businessCode: 'hrAttend.attendManage.monthlyCount',
      icon: 'icondaka',
    },
  },
  {
    path: '/attendance/clockTime',
    component: clockTime,
    meta: {
      businessCode: 'hrAttend.attendManage.signTime',
      icon: 'icondaka',
    },
  },
  {
    path: '/attendance/originalRecord',
    component: originalRecord,
    meta: {
      businessCode: 'hrAttend.attendManage.count',
      icon: 'icondaka',
    },
  },
  {
    path: '/attendance/addAttendance',
    // component:  window.env.server_env === "prod" ? addAttendanceTianDi : addAttendance,
    component:addAttendance,
    meta: {
      // businessCode: 'hrAttend.attendManage.components.addAttendance',
    },
  },
  {
    path: '/attendance/addShift',
    component: addShift,
    meta: {
      // businessCode: 'hrAttend.attendManage.components.addShift',
    },
  },
  {
    path: '/attendance/addRule',
    component: addRule,
    meta: {
      // businessCode: 'hrAttend.attendManage.components.addRule',
    },
  },
  {
    path: '/attendance/excelBatch',
    component: excelBatch,
    name: 'attendance.excelBatch',
    meta: {
      // businessCode: 'hrAttend.attendManage.components.excelBatch',
    },
  },
  {
    path: '/attendance/personBalance',
    component: personBalance,
    name: 'attendance.personBalance',
    meta: {
      // businessCode: 'hrAttend.attendManage.components.personBalance',
    },
  },
  {
    path: '/attendance/editSchedule',
    component: editSchedule,
    meta: {
      // businessCode: 'hrAttend.attendManage.components.editSchedule',
    },
  },
  {
    path: '/attendance/addOvertime',
    component: addOvertime,
    meta: {
      // businessCode: 'hrAttend.attendManage.overTime.savaOvertimeRule',
    },
  },
];
