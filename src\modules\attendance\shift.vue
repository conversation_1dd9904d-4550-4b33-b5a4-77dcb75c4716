<template>
  <div class="def_per_height">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span>班次管理</span>
        </el-col>
      </el-row>
    </header>
    <div class="content">
      <div class="add-search">
        <div class="search-box">
          <el-input
            v-model.trim="key"
            placeholder="请输入班次名称"
            maxLength="30"
            :clearable="true"
            @change="handleSearch"
          >
            <el-button slot="append" @click="handleSearch">搜索</el-button>
          </el-input>
        </div>
        <el-button type="primary" @click="handleAdd">+新增班次</el-button>
      </div>
      <el-table
        :data="tableData"
        border
        v-loading="loading"
        :header-cell-style="{ background: '#F1F1F1' }"
      >
        <el-table-column label="班次名称" prop="groupName" min-width="100">
          <template slot-scope="scope">
            <p>
              {{ scope.row.groupName }}
              <el-button v-if="scope.row.isDefault" type="text">默认</el-button>
            </p>
          </template>
        </el-table-column>
        <el-table-column label="考勤时间" prop="attendanceTime" min-width="200">
          <template slot-scope="scope">
            <span>{{ scope.row.attTime[0] }}</span>
            <span>{{ scope.row.attTime[1] }}</span>
            <span>{{ scope.row.attTime[2] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="operation" min-width="100">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleEdit(scope.row.id, scope.row.isDefault)"
              >编辑</el-button
            >
            <el-button
              v-show="!scope.row.isDefault"
              size="mini"
              type="text"
              @click="handleDelete(scope.row.id, scope.row.groupName)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="prev, pager, next, sizes, jumper"
          :total="total"
          background
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      key: "",
      pageSize: 10,
      total: 0,
      currPage: 1,
      tableData: [],
      loading: false,
      screenHeight: document.body.clientHeight - 300, //表格自适应高度
    };
  },
  created() {
    this.getAttendWorkingList();
  },
  methods: {
    //班次搜索
    handleSearch() {
      this.getAttendWorkingList("search");
    },
    //编辑班次
    handleEdit(delId, isDefault) {
      this.$attApi.apiCheckWorkingShift({ id: delId }).then((res) => {
        if (res.success) {
          if (!res.data) {
            this.$router.push({
              path: "/attendance/addShift",
              query: {
                id: delId,
                isDefault: isDefault,
              },
            });
            return;
          }
          this.$confirm(`${res.data}，确定编辑吗?`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            closeOnClickModal: false,
            closeOnPressEscape: false,
          })
            .then(() => {
              this.$router
                .push({
                  path: "/attendance/addShift",
                  query: {
                    id: delId,
                    isDefault: isDefault,
                  },
                })
                .catch((err) => {
                  this.$message({
                    type: "error",
                    message: err.response.data.message,
                  });
                });
            })
            .catch(() => {});
        }
      });
    },
    //删除班次
    handleDelete(delId, name) {
      this.$attApi.apiCheckWorkingShift({ id: delId }).then((res) => {
        if (res.success) {
          this.$confirm(
            `${
              res.data
                ? res.data + "，无法完成删除操作。"
                : "确定要删除班次【" + name + "】吗？"
            }`,
            "提示",
            {
              confirmButtonText: res.data ? "我知道了" : "确定",
              showCancelButton: res.data ? false : true,
              cancelButtonText: "取消",
              type: "warning",
              closeOnClickModal: false,
              closeOnPressEscape: false,
            }
          )
            .then(() => {
              if (res.data) return;
              this.$attApi
                .apiPostDeleteAttendWork({ id: delId })
                .then((res) => {
                  if (res.success) {
                    this.$message({
                      type: "success",
                      message: "删除成功!",
                    });
                    this.getAttendWorkingList();
                  }
                })
                .catch((err) => {
                  this.$message({
                    type: "error",
                    message: err.response.data.message,
                  });
                });
            })
            .catch(() => {});
        }
      });
    },
    handleAdd() {
      this.$router.push("/attendance/addShift");
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getAttendWorkingList();
    },
    handleCurrentChange(val) {
      this.currPage = val;
      this.getAttendWorkingList();
    },
    //拼接时间范围显示
    insertStr(str) {
      return str.slice(0, 2) + ":" + str.slice(2);
    },
    //获取班次列表/搜索
    getAttendWorkingList(type) {
      this.loading = true;
      let params = {
        currPage: type === "search" ? 1 : this.currPage,
        pageSize: this.pageSize,
        key: this.key,
      };
      this.$attApi.apiPostAttendWorkingList(params).then((res) => {
        this.loading = false;
        if (res.success) {
          this.tableData = res.data.records;
          this.total = res.data.total;
          this.tableData.forEach((val) => {
            val.attTime = [];
            val.workingTimeListVoList.forEach((its, index) => {
              if (its.workingBegin && its.workingEnd) {
                val.attTime[index] =
                  this.insertStr(its.workingBegin) +
                  "-" +
                  this.insertStr(its.workingEnd);
              }
            });
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  border-bottom: 1px solid #ededed;
}
.content {
  padding: 22px;
  .add-search {
    display: flex;
    margin-bottom: 20px;
    justify-content: space-between;
    .search-box {
      width: 400px;
    }
  }
}

.pagination {
  float: right;
  padding: 22px 0 22px 22px;
}
</style>
