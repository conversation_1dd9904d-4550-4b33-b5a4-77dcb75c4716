//每日统计-自定义列表
export const dayStatis = {
  basicInfo: ['部门', '工号', '职位', '上级'],
  shiftInfo: ['日期', '考勤组', '时区', '班次'],
  clockInfo: [
    '打卡时间',
    '打卡结果',
    '打卡原始记录',
    '打卡地址',
    '打卡备注',
    '设备信息',
  ],
  lengthSta: [
    '应出勤时长(分钟)',
    '实际出勤时长(分钟)',
    '计薪时长(分钟)',
    '迟到时长(分钟)',
    '早退时长(分钟)',
    '旷工时长(分钟)',
  ],
  abnormalSta: [
    '迟到次数(次)',
    '旷工迟到次数(次)',
    '早退次数(次)',
    '旷工早退次数(次)',
    '上班缺卡次数(次)',
    '下班缺卡次数(次)',
    '出差时长(分钟)',
    '外出时长(分钟)',
    '关联审批单',
  ],
  leave: [
    '年假(天)',
    '事假(小时)',
    '病假(小时)',
    '婚假(天)',
    '产假(天)',
    '陪产假(天)',
    '例假(半天)',
    '丧假(天)',
    '调休(小时)',
  ],
  overtime: [
    ['工作日(转加班费)', '休息日(转加班费)', '节假日(分钟)'],
    ['工作日(转调休)', '休息日(转调休)', '节假日(转调休)'],
  ],
};

//月度统计-自定义列表
export const monthStatis = {
  basicInfo: ['部门', '工号', '职位', '上级'],
  shiftInfo: ['考勤组', '时区', '班次'],
  attendSta: [
    '应出勤天数',
    '工作日出勤天数',
    '休息日出勤天数',
    '应出勤时长(分钟)',
    '计薪工作时长(分钟)',
    '加班工作时长(分钟)',
  ],
  abnormalSta: [
    '迟到次数(次)',
    '迟到时长(分钟)',
    '早退次数(次)',
    '早退时长(分钟)',
    '旷工迟到次数(次)',
    '旷工迟到时长(分钟)',
    '旷工早退次数(次)',
    '旷工早退时长(分钟)',
    '上班缺卡次数(次)',
    '下班缺卡次数(次)',
    '缺勤',
    '出差时长(分钟)',
    '补卡次数(次)',
    '外勤次数',
    '外出时长(分钟)',
    '换班天数',
  ],
  leave: [
    '年假(天)',
    '事假(小时)',
    '病假(小时)',
    '婚假(天)',
    '产假(天)',
    '陪产假(天)',
    '例假(半天)',
    '丧假(天)',
    '调休(小时)',
  ],
  overtime: [
    ['工作日(转加班费)', '休息日(转加班费)', '节假日(分钟)'],
    ['工作日(转调休)', '休息日(转调休)', '节假日(转调休)'],
  ],
};

export const queryStatus = {
  faceStatus: {
    '': '全部',
    0: '正常',
    1: '未录入',
    2: '异常',
  },
  checkWayStatus: {
    '': '全部',
    0: '无',
    1: '管理员修改',
    2: 'AI验证',
  },
  aiCheckStatus: {
    '': '全部',
    0: '无',
    1: '已通过',
    2: '未通过',
  },
};

//需要特殊处理的表头
export const tableTitles = [
  'maxTemperature',
  'minTemperature',
  'first-to_work-temperature',
  'first-from_work-temperature',
  'second-to_work-temperature',
  'second-from_work-temperature',
  'third-to_work-temperature',
  'third-from_work-temperature',
];
