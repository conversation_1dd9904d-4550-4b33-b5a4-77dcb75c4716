//新增假期-有效期
const verifyPoneDay = (r, v, c) => {
  // let rule = /^[\u4e00-\u9fa50-9a-zA-Z]*$/;
  // if (!v) return c(new Error("假期名称不能为空"));
  // if (v.length > 30) return c(new Error("最多可输入30个字符"));
  // if (!rule.test(v)) return c(new Error("输入的假期名称格式不正确，支持中文，字母，数字"));
  // c();
  //   v = parseInt(v);
  //   if (isNaN(v)) {
  //     return c(new Error("有效期余额请输入数字"))
  //   } else if (v < 0) {
  //     return c(new Error("有效期余额不能为负数")
  //   } else if((this.form.postponeTimeUnit === "MONTH")) {
  //     if(v > 180) {
  //       return c(new Error("有效期余额最多为180个月")
  //     }
  //   } else {
  //     if (v > 5400) {
  //       return c(new Error("有效期余额最多为5400天")
  //     }
  //   }
  //  c()
};

//假期名称
const checkHoliday = (r, v, c) => {
  let rule = new RegExp('^[\u4e00-\u9fa50-9a-zA-Z]*$');
  if (!v) return c(new Error('假期名称不能为空'));
  if (v.length > 30) return c(new Error('最多可输入30个字符'));
  if (!rule.test(v))
    return c(new Error('输入的假期名称格式不正确，支持中文，字母，数字'));
  c();
};
//班次名称
const checkShift = (r, v, c) => {
  let rule = new RegExp('^[\u4e00-\u9fa50-9a-zA-Z]*$');
  if (!v) return c(new Error('班次名称不能为空'));
  if (v.length > 30) return c(new Error('最多可输入30个字符'));
  if (!rule.test(v))
    return c(new Error('输入的班次名称格式不正确，支持中文，字母，数字'));
  c();
};

//新增补卡规则
const checkCardRule = (r, v, c) => {
  let rule = new RegExp('^[\u4e00-\u9fa50-9a-zA-Z]*$');
  if (!v) return c(new Error('补卡规则名称不能为空'));
  if (v.length > 30) return c(new Error('最多可输入30个字符'));
  if (!rule.test(v))
    return c(new Error('输入的补卡规则名称格式不正确，支持中文，字母，数字'));
  c();
};

//考勤组名称
const checkName = (r, v, c) => {
  let rule = new RegExp('^[\u4e00-\u9fa50-9a-zA-Z]*$');
  if (!v) return c(new Error('考勤组名称不能为空'));
  if (v.length > 30) return c(new Error('最多可输入30个字符'));
  if (!rule.test(v))
    return c(new Error('输入的考勤组名称格式不正确，支持中文，字母，数字'));
  c();
};

//加班规则名称
const checkOvertime = (r, v, c) => {
  let rule = new RegExp('^[\u4e00-\u9fa50-9a-zA-Z]*$');
  if (!v) return c(new Error('规则名称不能为空'));
  if (v.length > 30) return c(new Error('最多可输入30个字符'));
  if (!rule.test(v))
    return c(new Error('输入的规则名称格式不正确，支持中文，字母，数字'));
  c();
};

export {
  checkHoliday,
  checkShift,
  checkCardRule,
  checkName,
  verifyPoneDay,
  checkOvertime
};
