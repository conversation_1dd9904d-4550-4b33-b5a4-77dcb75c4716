<template>
  <div class="def_per_height">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span>考勤组管理</span>
        </el-col>
      </el-row>
    </header>
    <div class="content">
      <div class="add-search">
        <div class="search-box">
          <el-input
            v-model.trim="key"
            placeholder="请输入考勤组名称"
            :clearable="true"
            maxLength="30"
            @change="handleSearch"
          >
            <el-button slot="append" @click="handleSearch">搜索</el-button>
          </el-input>
        </div>
        <el-button
          v-if="
            privilegeVoList.includes(
              'hrAttend.attendManage.group.savaAttendGroup'
            )
          "
          type="primary"
          @click="handleAdd"
          >+新增考勤组</el-button
        >
      </div>
      <el-table
        :header-cell-style="{ background: '#F1F1F1' }"
        :data="tableData"
        border
        v-loading="loading"
      >
        <el-table-column
          label="考勤组名称"
          prop="agName"
          min-width="100"
        ></el-table-column>
        <el-table-column
          label="人数"
          prop="peopleCount"
          min-width="70"
        ></el-table-column>
        <el-table-column label="类型" prop="attendType" min-width="70">
          <template slot-scope="scope">
            {{
              scope.row.attendType === "FIX"
                ? "固定班制"
                : scope.row.attendType === "FREE"
                ? "自由班制"
                : "排班制"
            }}
          </template>
        </el-table-column>
        <el-table-column label="考勤时间" prop="showList" min-width="200">
          <template slot-scope="scope">
            <el-row>
              <el-col :span="24" v-for="v in scope.row.showList" :key="v.id">
                <span
                  v-show="
                    scope.row.attendType === 'FIX' ||
                    scope.row.attendType === 'FREE'
                  "
                  class="first"
                  >{{ v.week }}</span
                >
                <span class="first" v-show="v.description">{{
                  v.description
                }}</span>
                <span class="first">{{ v.time }}</span>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="operation" min-width="100">
          <template slot-scope="scope">
            <el-button
              v-if="
                privilegeVoList.includes(
                  'hrAttend.attendManage.group.updateAttendGroup'
                )
              "
              type="text"
              size="mini"
              @click="modifyRule(scope.row.id)"
              >修改规则</el-button
            >
            <el-button
              v-if="
                privilegeVoList.includes(
                  'hrAttend.attendManage.group.setAttendWorkForScheduling'
                ) && scope.row.attendType === 'SCHEDULING'
              "
              type="text"
              size="mini"
              @click="editSchedule(scope.row.id)"
              >编辑排班</el-button
            >
            <el-button
              v-if="
                privilegeVoList.includes(
                  'hrAttend.attendManage.group.deleteAttendGroup'
                )
              "
              type="text"
              size="mini"
              @click="handleDelete(scope.row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="prev, pager, next, sizes, jumper"
          :total="total"
          background
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      currPage: 1,
      pageSize: 10,
      key: "",
      total: 0,
      tableData: [],
      loading: false,
      screenHeight: document.body.clientHeight - 300, //表格自适应高度
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  created() {
    this.getAttendanceList();
  },
  methods: {
    aaa(val) {
      console.log(val);
    },
    //获取考勤列表
    getAttendanceList(type) {
      this.loading = true;
      let params = {
        currPage: type === "search" ? 1 : this.currPage,
        pageSize: this.pageSize,
        key: this.key,
      };
      this.$attApi.apiPostAttendGroupList(params).then((res) => {
        this.loading = false;
        if (res.success) {
          let DATA = res.data.records;
          // 数据处理
          DATA.forEach((v) => {
            v.showList = [];
            if (
              v.attendWorkingShiftResultList &&
              v.attendWorkingShiftResultList.length
            ) {
              let x = v.attendWorkingShiftResultList;
              x.forEach((y) => {
                //固定班制-班次时间处理
                if (v.attendType === "FIX" || v.attendType === "FREE") {
                  let temp;
                  if (!y || !y.shiftOrder) return;
                  temp = this.getWeek(y.shiftOrder);
                  if (y.isRestDay)
                    v.showList.push({
                      id: x.id,
                      week: temp,
                      description: "休息",
                      time: "",
                    });
                  //固定班制-班次信息
                  if (!y.isRestDay && y.workingShiftResult) {
                    let z = y.workingShiftResult;
                    if (z.workingShiftDetailResultList) {
                      let w = z.workingShiftDetailResultList,
                        str = "";
                      w.forEach((m) => {
                        if (m.workingBegin && m.workingEnd) {
                          let i = m.workingBegin.split("");
                          i.splice(2, 0, ":");
                          let j = m.workingEnd.split("");
                          j.splice(2, 0, ":");
                          str += i.join("") + "-" + j.join("") + " ";
                        }
                      });
                      v.showList.push({
                        id: x.id,
                        week: temp,
                        description: v.attendType === "FREE" ? "" : z.groupName,
                        time: str,
                      });
                    }
                  }
                }
                //排班制-班次时间处理
                if (v.attendType === "SCHEDULING") {
                  if (!y) return;
                  let z = y.workingShiftResult;
                  if (z && z.workingShiftDetailResultList) {
                    let w = z.workingShiftDetailResultList,
                      str = "";
                    w.forEach((m) => {
                      if (m.workingBegin && m.workingEnd) {
                        let i = m.workingBegin.split("");
                        i.splice(2, 0, ":");
                        let j = m.workingEnd.split("");
                        j.splice(2, 0, ":");
                        str += i.join("") + "-" + j.join("") + " ";
                      }
                    });
                    v.showList.push({
                      id: x.id,
                      description: z.groupName,
                      time: str,
                    });
                  }
                }
              });
            }
          });
          this.tableData = res.data.records;
          this.total = res.data.total;
        }
      });
    },

    // 获取星期几
    getWeek(date) {
      switch (date) {
        case 1:
          return "周一";
          break;
        case 2:
          return "周二";
          break;
        case 3:
          return "周三";
          break;
        case 4:
          return "周四";
          break;
        case 5:
          return "周五";
          break;
        case 6:
          return "周六";
          break;
        case 7:
          return "周日";
          break;
      }
    },

    //拼接时间范围显示
    insertStr(str) {
      return str.slice(0, 2) + ":" + str.slice(2);
    },
    handleSearch() {
      this.getAttendanceList("search");
    },
    handleAdd() {
      this.$router.push("/attendance/addAttendance");
    },
    editSchedule(editId) {
      this.$router.push({
        path: "/attendance/editSchedule",
        query: {
          id: editId,
        },
      });
    },
    //删除考勤组
    handleDelete(delId) {
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          this.$attApi.apiPostDeleteAttendGroup({ id: delId }).then((res) => {
            if (res.success) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getAttendanceList();
            }
          });
        })
        .catch(() => {});
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getAttendanceList();
    },
    handleCurrentChange(val) {
      this.currPage = val;
      this.getAttendanceList();
    },
    //修改规则
    modifyRule(id) {
      this.$router.push({
        path: "/attendance/addAttendance",
        query: { id: id },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  border-bottom: 1px solid #ededed;
}
.content {
  padding: 22px;
  .add-search {
    display: flex;
    margin-bottom: 20px;
    justify-content: space-between;
    .search-box {
      width: 400px;
    }
  }
}
.pagination {
  float: right;
  padding: 22px 0 22px 22px;
}

.first {
  margin-right: 8px;
}

.el-row {
  text-align: left;
}
</style>
