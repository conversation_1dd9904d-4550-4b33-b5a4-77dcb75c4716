<template>
  <div class="def_per_height">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span>加班管理</span>
        </el-col>
      </el-row>
    </header>
    <div class="content">
      <div class="add-search">
        <div class="search-box">
          <el-input
            v-model.trim="key"
            placeholder="请输入加班规则名称"
            :maxLength="30"
            :clearable="true"
            @change="handleSearch"
          >
            <el-button slot="append" @click="handleSearch">搜索</el-button>
          </el-input>
        </div>
        <el-button type="primary" @click="handleAdd">+新增加班规则</el-button>
      </div>
      <el-table
        :header-cell-style="{ background: '#F1F1F1' }"
        :data="tableData"
        v-loading="loading"
        border
      >
        <el-table-column label="加班规则名称" prop="ruleName" min-width="100">
          <template slot-scope="scope">
            <p>
              {{ scope.row.ruleName }}
              <el-button v-if="scope.row.isDefault" type="text">默认</el-button>
            </p>
          </template>
        </el-table-column>
        <el-table-column label="规则内容" prop="number" min-width="150">
          <template slot-scope="scope">
            <div
              v-for="val in scope.row.overtimeRuleDetailResultList"
              :key="val.id"
            >
              <p v-if="val.ruleType === 'WORKDAY'">
                工作日：{{
                  val.allowedOvertime
                    ? "加班必须审批，加班时长以审批时长为准"
                    : "不允许加班"
                }}
              </p>
              <p v-if="val.ruleType === 'RESTDAY'">
                休息日：{{
                  val.allowedOvertime
                    ? "加班必须审批，加班时长以审批时长为准"
                    : "不允许加班"
                }}
              </p>
              <p v-if="val.ruleType === 'HOLIDAY'">
                节假日：{{
                  val.allowedOvertime
                    ? "加班必须审批，加班时长以审批时长为准"
                    : "不允许加班"
                }}
              </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="应用范围" prop="overtimeRange" min-width="100">
          <template slot-scope="scope">
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              v-if="scope.row.attendNames && scope.row.attendNames.length"
            >
              <div slot="content">
                <p style="max-width: 200px">
                  {{ scope.row.overtimeRange }}
                </p>
              </div>

              <p class="overtimeRange">
                {{ scope.row.overtimeRange }}
              </p>
            </el-tooltip>
            <span v-else>未使用</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="operation" min-width="80">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="handleEdit(scope.row.id, scope.row.isDefault)"
              >编辑</el-button
            >
            <el-button
              v-show="!scope.row.isDefault"
              type="text"
              size="mini"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="prev, pager, next, sizes, jumper"
          :total="total"
          background
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currPage: 1,
      tableData: [],
      total: 0,
      coId: 0,
      currPage: 1,
      id: 0,
      key: "",
      pageSize: 10,
      queryEndTime: "",
      queryStartTime: "",
      loading: false,
      screenHeight: document.body.clientHeight - 300, //表格自适应高度
    };
  },
  created() {
    this.getOvertimeList();
  },
  methods: {
    getOvertimeList(type) {
      this.loading = true;
      let params = {
        currPage: type === "search" ? 1 : this.currPage,
        pageSize: this.pageSize,
        key: this.key,
      };
      this.$attApi.apiGetOverTimeList(params).then((res) => {
        this.loading = false;
        if (res.success) {
          const DATA = res.data.records;
          this.tableData = res.data.records;
          this.total = res.data.total;
          DATA.forEach((item) => {
            if (item.attendNames && item.attendNames.length) {
              let arrName = [];
              item.attendNames.forEach((val) => {
                arrName.push(val);
              });
              item.overtimeRange = arrName.toString();
            }
          });
        }
      });
    },
    handleEdit(id, isDefault) {
      this.$router.push({
        path: "/attendance/addOvertime",
        query: {
          id: id,
          isDefault: isDefault,
        },
      });
    },
    //删除加班规则
    handleDelete(row) {
      this.$confirm(`确定删除加班规则【${row.ruleName}】吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          this.$attApi.apiPostDeleteOvertimeRule({ id: row.id }).then((res) => {
            if (res.success) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getOvertimeList();
            }
          });
        })
        .catch(() => {});
    },
    handleSearch() {
      this.getOvertimeList("search");
    },
    handleAdd() {
      this.$router.push("/attendance/addOvertime");
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getOvertimeList();
    },
    handleCurrentChange(val) {
      this.currPage = val;
      this.getOvertimeList();
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  border-bottom: 1px solid #ededed;
}
.add-search {
  display: flex;
  margin-bottom: 20px;
  justify-content: space-between;
}
.content {
  padding: 22px;
  .overtimeRange {
    line-height: 50px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
  .add-search .search-box {
    width: 400px;
  }
}
.pagination {
  float: right;
  padding: 22px 0 22px 22px;
}
</style>
