<template>
  <div class="authorizeZx">
    <header class="header">
      <span>阿拉钉</span>
      <span class="line">|</span>
      <span>人力资源科技服务平台</span>
    </header>
    <div class="home-content">
      <div class="form-con">
        <div class="tip">提交后，阿拉钉专属客服将在3个工作日内联系您</div>
        <el-form
          :model="authForm"
          ref="authForm"
          label-width="80px"
          label-position="left"
        >
          <el-form-item
            label="公司名称"
            prop="corpName"
            :rules="[
              { required: true, message: '请输入公司名称', trigger: 'blur' },
            ]"
          >
            <el-input v-model="authForm.corpName" disabled></el-input>
          </el-form-item>
          <el-form-item
            label="联系人"
            prop="contacts"
            :rules="[
              { required: true, message: '请输入联系人', trigger: 'blur' },
            ]"
          >
            <el-input
              v-model="authForm.contacts"
              placeholder="请输入联系人"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="联系方式"
            prop="contactMobile"
            :rules="[
              { required: true, message: '请输入联系方式', trigger: 'blur' },
              {
                validator: validateTell,
                message: '请输入正确手机号',
                trigger: 'blur',
              },
            ]"
          >
            <el-input
              v-model="authForm.contactMobile"
              placeholder="请输入联系方式"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="授权码"
            prop="authCode"
            :rules="[
              { required: true, message: '请输入授权码', trigger: 'blur' },
              { validator: validateAuthCode, trigger: 'blur' },
            ]"
          >
            <el-input
              v-model="authForm.authCode"
              placeholder="输入开薪易应用市场生成的6位授权码"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="confirm-btn" @click="handleSubmit">提交</div>
        <div class="agree-style">
          <el-checkbox v-model="agree" class="check-style"></el-checkbox
          >已阅读并同意<a @click="goAgree" style="cursor: pointer"
            >《授权服务协议》</a
          >
        </div>
      </div>
    </div>
    <div class="footer-tip">
      COPYRIGHT © 北京阿拉钉科技有限公司 京ICP备19024749号-1
    </div>
    <el-dialog
      title=""
      :visible.sync="isShowTip"
      width="300px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="dialog-con">
        <p><i class="el-icon-success"></i><strong>授权成功</strong></p>
        <p>正在前往阿拉钉服务</p>
      </div>
    </el-dialog>
  </div>
</template>
<script>
const environmentConfig = window.env.environmentConfig;
import { mapState } from "vuex";
import * as AT from "@/store/actionTypes";
import { validateTell, validateAuthCode } from "@/util/validate";
export default {
  components: {},
  data() {
    return {
      authForm: {
        contacts: "",
        contactMobile: "",
        authCode: "",
        corpId: "",
        corpName: "",
        nonce: "",
        timestamp: "",
        userId: "",
      },
      toUrl: "",
      agree: true,
      isShowTip: false,
      isAuthSuccess: true,
      failReason: "",
      validateTell: validateTell,
      validateAuthCode: validateAuthCode,
    };
  },
  computed: {
    ...mapState({
      authcode: (state) => state.authcode,
    }),
  },
  created() {
    this.$store.commit(AT.SHOWAPP, false);
    this.toUrl = environmentConfig.sso ? environmentConfig.sso : `http://${window.location.host}`;
  },
  mounted() {
    // 授权码解码
    this.decryptData();
  },
  methods: {
    decryptData() {
      this.$store
        .dispatch("authorizeZxStore/actionDecryptData", {
          data: this.authcode,
        })
        .then((res) => {
          if (res.success) {
            let data = res.data;
            this.authForm.corpId = data.corpId;
            this.authForm.corpName = data.corpName;
            this.authForm.nonce = data.nonce;
            this.authForm.timestamp = data.timestamp;
            this.authForm.userId = data.userId;
          }
        });
    },
    handleSubmit() {
      this.authForm.citicData = this.authcode;
      if (this.agree) {
        this.$refs.authForm.validate((valid) => {
          if (valid) {
            this.$store
              .dispatch("authorizeZxStore/actionVerifyAuthCode", this.authForm)
              .then((res) => {
                if (res.success) {
                  this.isShowTip = true;
                  setTimeout(() => {
                    this.isShowTip = false;
                    window.open(this.toUrl, "_self");
                  }, 3000);
                }
              });
          } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
        });
      } else {
        this.$message.warning("请勾选已阅并同意");
      }
      // console.log(this.data)
    },
    goAgree() {
      window.open("/authorizeZx/agreement", "_blank");
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.authorizeZx {
  height: calc(100vh);
  .header {
    padding: 0 20px;
    height: 61px;
    background-color: #1c2024;
    color: #fff;
    line-height: 61px;
    font-size: 17px;
    width: 100%;
    box-sizing: border-box;
    .line {
      margin: 0 10px;
      background-color: #1c2024;
      color: #fff;
      font-size: 17px;
      font-weight: bold;
    }
  }
  .home-content {
    width: 620px;
    min-height: 450px;
    border-color: #4f71ff #dcdfe6 #dcdfe6 #dcdfe6;
    border-width: 2px 1px 1px 1px;
    border-style: solid;
    margin: 40px auto;
    background: #fff;
    -webkit-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12),
      0 0 6px 0 rgba(0, 0, 0, 0.04);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
  }
  .form-con {
    width: 400px;
    margin: 40px auto;
    .tip {
      margin-bottom: 30px;
      text-align: center;
    }
  }
  .confirm-btn {
    width: 400px;
    margin: 0 auto;
    height: 38px;
    line-height: 38px;
    background: #4f71ff;
    color: #fff;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
  }
  .agree-style {
    text-align: center;
    margin-top: 10px;
    .check-style {
      margin-right: 10px;
    }
  }
  .dialog-con {
    margin-top: -30px;
    text-align: center;
    p {
      line-height: 30px;
    }
    p:first-child {
      font-size: 17px;
    }
  }
  .el-icon-success,
  .el-icon-warning {
    font-size: 18px;
  }
  .footer-tip {
    text-align: center;
    margin-top: 10px;
  }
}
</style>
