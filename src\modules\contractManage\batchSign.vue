<template>
  <div class="sign-page">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style"> 返回 </span>
          <span class="header-line">|</span>
          <span>批量签署</span>
        </el-col>
      </el-row>
    </header>
    <div class="content-con">
      <el-steps
        :active="Number(active)"
        class="step-style"
        align-center
        finish-status="success"
      >
        <el-step title="确认文件信息"></el-step>
        <el-step title="选择签署印章"></el-step>
      </el-steps>
    </div>
    <component :is="currentComponent" @updateActive="updateActive"></component>
  </div>
</template>
<script>
import confirmFile from "./components/batchSign/confirmFile";
import selectSign from "./components/batchSign/selectSign";

export default {
  components: {
    confirmFile,
    selectSign,
  },
  data() {
    return {
      fileList: [],
      fileData: [],
      taskNo: "",
      mode: this.$route.query.mode ? this.$route.query.mode : "add",
      active: 0,
      uploadFileName: "", //上传文件名称
      isShowUpload: true,
      currentComponent: "confirmFile",
    };
  },
  watch: {
    active(val) {
      switch (val) {
        case 0:
          this.currentComponent = "confirmFile";
          break;
        case 1:
          this.currentComponent = "selectSign";
          break;
      }
    },
  },
  created() {},
  methods: {
    updateActive(active) {
      console.log(active);
      this.active = active;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~assets/scss/helpers";
.content-con {
  margin-top: 32px;
  /deep/.el-steps {
    margin: 20px auto;
  }
  .basic-info {
    width: 500px;
    padding-top: 20px;
    margin: 0 auto;
    .el-select {
      width: 100%;
    }
  }
  /deep/ .el-steps {
    max-width: 800px;
  }

  /deep/ .is-process {
    font-size: 14px;
    font-weight: 400;
    color: $mainColor;
    .el-step__icon.is-text {
      background: $mainColor;
      border: 1px solid $mainColor;
      color: #fff;
    }
  }
  /deep/ .is-success {
    font-size: 14px;
    color: #777c94;
    .el-step__icon.is-text {
      border: 1px solid $mainColor;
      color: $mainColor;
    }
  }
  /deep/ .is-wait {
    font-size: 14px;
    color: #777c94;
    .el-step__icon.is-text {
      border: 1px solid #777c94;
      color: #777c94;
    }
  }
  /deep/ .el-step__line {
    margin: 0 50px !important;
    background: #cbced8;
  }
}
</style>
