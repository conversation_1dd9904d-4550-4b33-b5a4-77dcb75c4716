<template>
  <div class="confirm-file">
    <div class="container">
      <p class="num">您共 {{ batchSignList.length }} 个合同可以批量签署</p>
      <el-table
        :data="batchSignList"
        :width="screenWidth"
        :header-cell-style="{ background: '#F1F1F1' }"
        stripe
        border
      >
        <el-table-column
          prop="contractName"
          label="文件名称"
          width="350"
          fixed="left"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span class="table-name" @click="handleView(scope.row)">
              {{ scope.row.contractName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="contractFileType" label="文件类型">
          <template slot-scope="scope">
            {{ scope.row.contractFileType | templateType }}
          </template>
        </el-table-column>
        <el-table-column
          prop="taxSubName"
          label="合同主体单位"
          width="180px"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="signerList"
          label="签署人"
          width="270px"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <div class="sign-person">
              <div
                class="flex"
                v-for="(item, index) in scope.row.signerList"
                :key="index"
              >
                <span v-if="item.sealUser" class="flex">
                  <span class="qy f-c"
                    >企
                    <span
                      class="status"
                      :class="[
                        { finshed: item.signerStatus == 'FINISHED' },
                        { wait: item.signerStatus == 'WAIT_SIGN' },
                        { cannot: item.signerStatus == 'CANNOT_SIGN' },
                      ]"
                    ></span
                  ></span>
                  {{ item.name }}
                </span>
                <span v-else class="flex">
                  <span class="gr f-c"
                    >个
                    <span
                      class="status"
                      :class="[
                        { finshed: item.signerStatus == 'FINISHED' },
                        { wait: item.signerStatus == 'WAIT_SIGN' },
                        { cannot: item.signerStatus == 'CANNOT_SIGN' },
                      ]"
                    ></span
                  ></span>
                  {{ item.name }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createUser" label="发起人"></el-table-column>
        <el-table-column
          prop="startDate"
          label="发起日期"
          min-width="120px"
        ></el-table-column>

        <el-table-column fixed="right" label="操作" width="170">
          <template slot-scope="scope">
            <el-button
              type="text"
              style="margin-left: 10px"
              @click="handleView(scope.row)"
              >文件详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="buttonCon">
      <el-button @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" @click="next">下一步</el-button>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";

import { apiGetViewUrl } from "../../store/api";

export default {
  data() {
    return {
      screenWidth: document.body.clientWidth, // 屏幕尺寸宽度
      isShowSignDrawer: false, //查看签署侧弹窗
      signProcessInfo: {},
      steps: [],
      copyList: [],
      currentStep: 0,
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
    ...mapState("contractManageStore", {
      batchSignList: "batchSignList",
    }),
  },
  created() {},
  methods: {
    next() {
      if (!this.batchSignList.length)
        return this.$message.error("待签署合同列表为空，无法进入下一步");
      this.$emit("updateActive", 1);
    },
    //显示签署人
    getSigner(data) {
      let arr = [];
      data.map((item) => {
        if (item.sealUser) {
          if (item.name) {
            arr.push(`企:${item.name ? item.name : ""}`);
          }
        } else {
          arr.push(`个:${item.name ? item.name : ""}`);
        }
      });
      return arr.join("  ");
    },

    //查看
    async handleView(data) {
      let res = await apiGetViewUrl({ contractId: data.id });
      if (res.success) {
        window.open(res.data.url);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers";
.confirm-file {
  .container {
    padding: 20px 20px 96px 20px;
  }
  .num {
    font-size: 12px;
    color: #46485a;
    margin-bottom: 12px;
  }

  .sign-person {
    display: flex;
    .flex {
      display: flex;
      align-items: center;
      margin-right: 15px;
    }
    .qy,
    .gr {
      width: 16px;
      height: 16px;
      border-radius: 4px;
      font-size: 12px;
      margin-right: 5px;
      position: relative;
    }

    .qy {
      background: #f5f7ff;
      color: #4f71ff;
    }
    .gr {
      color: #e59900;
      background: #fffaf0;
    }
    .status {
      width: 8px;
      height: 8px;
      position: absolute;
      right: -2px;
      bottom: -1px;
    }

    .finshed {
      background: url(../../../../assets/images/select1.png) no-repeat;
      background-size: 100% 100%;
    }
    .wait {
      background: url(../../../../assets/images/select2.png) no-repeat;
      background-size: 100% 100%;
    }
    .cannot {
      background: url(../../../../assets/images/select3.png) no-repeat;
      background-size: 100% 100%;
    }
  }
  .buttonCon {
    text-align: center;
    position: fixed;
    bottom: 0;
    z-index: 999;
    width: 100%;
    background: #fff;
    padding: 20px 0px;
    border-top: 1px solid #f3f3f3;
    box-shadow: 0 -4px 8px 0 rgba(203, 206, 216, 0.16);
    .el-button:first-child {
      margin-left: -285px;
    }
    .el-button {
      width: 92px;
      padding: 10px 20px;
      border-radius: 8px;
    }
  }
}
</style>
