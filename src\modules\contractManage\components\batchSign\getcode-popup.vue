<template>
  <el-dialog
    title="验证身份"
    class="app-dialog"
    :visible.sync="popVisible"
    @close="handleClose"
    :close-on-click-modal="false"
    width="500px"
    :show-close="false"
  >
    <el-form
      :model="telFrom"
      :rules="rules"
      ref="telFrom"
      class="demo-ruleForm"
    >
      <el-form-item>
        <i class="icon tel-icon">
          <!-- <img src="../../../assets/images/tel-icon.png" alt /> -->
        </i>
        手机号:{{ cellPhone | hideCellPhone }}
      </el-form-item>
      <el-form-item prop="phoneCode" class="getValidContainer">
        <el-input
          type="verifyEmailCode"
          v-model="telFrom.phoneCode"
          placeholder="请输入验证码"
          maxlength="6"
        ></el-input>
        <div>
          <el-button
            :type="type"
            :disabled="!show"
            @click="getVerifyCode"
            class="getValidCode"
          >
            <span v-show="show">获取验证码</span>
            <span v-show="!show" class="count">{{ count }}s后重新发送</span>
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <slot name="footer-btn" />
      <el-button class="cancel-btn" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" @click="handleSubmit"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
const TIME_COUNT = 60;
import { batchSignPrepare } from "../../store/api";

export default {
  filters: {
    hideCellPhone: (val) => {
      //隐藏手机号
      let statusName = "";
      if (val) {
        statusName =
          val.substring(0, 3) + "****" + val.substring(val.length - 4);
      }
      return statusName;
    },
  },

  data() {
    var validatetelCode = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    return {
      popVisible: false,
      cellPhone: "",
      telFrom: {
        phoneCode: "",
      },
      rules: {
        phoneCode: [{ validator: validatetelCode, trigger: "change" }],
      },
      show: true,
      count: "",
      type: "primary",
      loading: false,
      ids: [],
    };
  },

  mounted() {},

  methods: {
    handleClose() {
      this.count = "";
      this.telFrom.phoneCode = "";
      this.popVisible = false;
      this.$refs["telFrom"].resetFields();
    },
    handleSubmit() {
      this.$refs["telFrom"].validate((valid) => {
        if (valid) {
          this.$emit("confirm", this.telFrom.phoneCode);
        } else {
          console.log("error submit!!");
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
          return false;
        }
      });
    },

    //验证码倒计时
    send() {
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.show = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.show = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
    },
    //获取短信验证码
    async getVerifyCode() {
      this.telFrom.phoneCode = "";
      this.$refs["telFrom"].resetFields();
      try {
        const { data, success, message } = await batchSignPrepare({
          contractIds: this.ids,
        });
        if (success) {
          this.$emit("getCode", data.code);
        } else {
          this.$message.error(message);
        }
      } finally {
        this.send();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers";
.app-dialog {
  ::v-deep {
    .el-dialog__body {
      flex: 1;
      overflow: auto;
    }
  }

  .tel-icon {
    float: left;
    height: 23px;
    margin: 10px 10px 0 0;
    img {
      display: block;
      height: 100%;
    }
  }
  .mytextArea {
    .el-textarea__inner {
      height: 90px;
    }
  }
  //获取验证码
  .getValidContainer {
    position: relative;
    .getValidCode {
      position: absolute;
      right: 20px;
      top: 0px;
      color: $mainColor !important;
      cursor: pointer;
      border: none;
      padding: 0px;
      top: 14px;
      background: #fff !important;
      .count {
        color: #cbced8;
      }
    }

    .getValidCode:hover {
      color: #ff7200;
      background-color: #fff;
      border-color: #fff;
    }
  }

  ::v-deep .el-dialog__headerbtn {
    display: none;
  }

  ::v-deep .el-dialog__title {
    -moz-user-select: none;
    -o-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #24262a;
    margin-left: 24px;
  }
  ::v-deep .el-dialog__header {
    position: relative;
    margin: 0;
    padding: 10px 0;
    border-bottom: 1px solid #eef0f4;
  }
  ::v-deep .el-dialog__headerbtn {
    width: 60px;
    position: absolute;
    top: 2px;
    right: 0;
    bottom: 0;
  }
  ::v-deep .el-button {
    width: 92px;
    padding: 10px 20px;
    border-radius: 8px;
  }
}
</style>
