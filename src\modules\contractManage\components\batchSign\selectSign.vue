<template>
  <div v-loading="loading" class="select-sign">
    <div class="container">
      <div v-for="(item, index) in company" :key="index" class="company">
        <span class="info-title">{{ item.taxSubName }}</span>
        <div class="flex">
          <div
            v-for="(sign, i) in item.signPhotos"
            :key="i"
            class="company-item"
            :class="{ current: sign.defaultYn }"
            @click="handleSelectClick(item.signPhotos, sign)"
          >
            <img :src="sign.photo.url" alt="" />
            <i class="iconfont icontag_select"></i>
          </div>
        </div>
      </div>

      <div v-if="signer.length" class="signer">
        <span class="info-title">个人签章</span>
        <div
          v-for="(item, index) in signer"
          :key="index"
          class="signer-item current"
          :class="{ current: item.defaultYn }"
        >
          <img :src="item.photo.url" alt="" />
          <i class="iconfont icontag_select"></i>
        </div>
      </div>
    </div>

    <div class="buttonCon">
      <el-button @click="prev">上一步</el-button>
      <el-button :loading="submitLoading" type="primary" @click="submit"
        >提交签署</el-button
      >
    </div>

    <getcode-popup
      ref="getcode"
      @confirm="confirm"
      @getCode="getCode"
    ></getcode-popup>
  </div>
</template>
<script>
import { mapState } from "vuex";
import * as constData from "@/utils/constData";

import getcodePopup from "./getcode-popup";

import {
  getPhotosByContract,
  getBatchSignConfirm,
  batchSignPrepare,
} from "../../store/api";

export default {
  components: {
    getcodePopup,
  },
  data() {
    return {
      dialogFormVisible: false,
      loading: false,
      submitLoading: false,
      signer: [], //个人签章
      company: [], //企业签章
      ids: [],
      mobile: "", //手机号
      code: "", //后端需要的识别码
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
    ...mapState("contractManageStore", {
      batchSignList: "batchSignList",
    }),
  },
  created() {
    this.getPhotosByContract();
  },
  methods: {
    prev() {
      this.$emit("updateActive", 0);
    },
    async getPhotosByContract() {
      console.log(this.batchSignList);
      this.loading = true;
      this.ids = this.batchSignList.map((it) => it.id);

      try {
        const { success, data } = await getPhotosByContract({
          contractIds: this.ids,
        });
        if (success) {
          this.signer = data.signerPhotos;
          this.company = data.companyPhotos;
        }
      } finally {
        this.loading = false;
      }
    },
    getCode(code) {
      this.code = code;
    },

    async submit() {
      try {
        const { data, success, message } = await batchSignPrepare({
          contractIds: this.ids,
        });
        if (success) {
          this.mobile = data.mobile;
          this.code = data.code;
          const cmt = this.$refs.getcode;
          cmt.popVisible = true;
          cmt.cellPhone = this.mobile;
          cmt.ids = this.ids;
          cmt.send();
        } else {
          this.$message.error(message);
        }
      } finally {
      }
    },
    //批量签署确认
    async confirm(phoneCode) {
      const arr = [];
      this.company.forEach((item) => {
        const res = item.signPhotos.filter((it) => it.defaultYn);
        const obj = {
          taxSubId: item.taxSubId,
          archiveId: res[0].photo.archiveId,
        };
        arr.push(obj);
      });

      const send = {
        contractIds: this.ids,
        code: this.code,
        verificationCode: phoneCode,
        batchSignPhotos: arr,
      };

      console.log(send);
      try {
        const cmt = this.$refs.getcode;
        cmt.loading = true;
        const res = await getBatchSignConfirm(send);
        if (res.success) {
          this.$message.success("签署成功");
          this.$router.go(-1);
        }
      } finally {
        const cmt = this.$refs.getcode;
        cmt.loading = false;
      }
    },
    handleSelectClick(item, sign) {
      console.log(item, sign);
      item.forEach((el) => {
        el.defaultYn = false;
      });
      sign.defaultYn = true;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers";
.select-sign {
  .container {
    padding: 20px 20px 96px 20px;
  }
  .info-title {
    font-weight: 500;
    font-size: 16px;
    margin: 20px;
    color: #070f29;
    line-height: 18px;
    display: flex;
    align-items: center;
    &::before {
      content: "";
      display: inline-block;
      width: 3px;
      height: 14px;
      background-color: $mainColor;
      border-radius: 1px;
      margin-right: 10px;
    }
  }
  .company {
    .flex {
      display: flex;
    }
    &-item {
      width: 180px;
      height: 180px;
      background: #ffffff;
      border: 1px solid #eef0f4;
      border-radius: 7px;
      padding: 30px;
      box-sizing: border-box;
      margin-left: 32px;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .signer {
    margin-top: 60px;
    &-item {
      width: 180px;
      height: 86px;
      border: 1px solid $mainColor;
      border-radius: 5px;
      padding: 20px 40px;
      box-sizing: border-box;
      margin-left: 32px;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .icontag_select {
    position: absolute;
    right: -1px;
    bottom: -1px;
    margin: 0;
    display: none;
  }

  .current {
    color: $mainColor;
    border-color: $mainColor;
    .icontag_select {
      display: block;
    }
  }

  .buttonCon {
    text-align: center;
    position: fixed;
    bottom: 0;
    z-index: 999;
    width: 100%;
    background: #fff;
    padding: 20px 0px;
    border-top: 1px solid #f3f3f3;
    box-shadow: 0 -4px 8px 0 rgba(203, 206, 216, 0.16);
    .el-button:first-child {
      margin-left: -285px;
    }
    .el-button {
      width: 92px;
      padding: 10px 20px;
      border-radius: 8px;
    }
  }
}
</style>
