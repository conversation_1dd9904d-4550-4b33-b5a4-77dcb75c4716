<template>
  <div class="template-choose">
    <el-input v-model.trim="searchCondition.key" @keyup.enter.native="getList" placeholder="请输入姓名/工号/手机号"
      class="search-template-input"></el-input>
    <el-select v-model="searchCondition.contractType" placeholder="请选择合同类型" @change="getList" filterable clearable>
      <el-option v-for="(item, index) in contractTypeList" :label="item.optionEnumName" :value="item.optionEnumCode"
        :key="index"></el-option>
    </el-select>
    <el-radio-group v-model="searchCondition.signType" size="medium" @change="getList">
      <el-radio-button label="">全部</el-radio-button>
      <el-radio-button label="NEW_SIGN">新签</el-radio-button>
      <el-radio-button label="CONTINUE_SIGN">续签</el-radio-button>
      <el-radio-button label="UPDATE_SIGN">变更</el-radio-button>
    </el-radio-group>
    <el-radio-group v-model="searchCondition.contractStatus" size="medium" @change="getList">
      <el-radio-button label="">全部</el-radio-button>
      <el-radio-button label="NOT_EXECUTE">未执行</el-radio-button>
      <el-radio-button label="EXECUTE">执行中</el-radio-button>
      <el-radio-button label="EXPIRE">已到期</el-radio-button>
      <el-radio-button label="END">已终止</el-radio-button>
      <el-radio-button label="REMOVE">已解除</el-radio-button>
    </el-radio-group>
    <!-- 模板列表 -->
    <el-main v-loading="loading">
      <div v-if="list.length" class="list">
        <div class="item" v-for="(item, index) in list" :class="{
          active: currentSelectItems.map(it => it.id).includes(item.id)
        }" @click="handleSelectItem(item)" :key="index">
          <p>
            <span class="name">{{ item.empName }}</span>
            <span>{{ item.contractNo }}</span>
          </p>
          <p>
            <span class="type" style="margin-right: 0;">
              {{ getContractType(item.contractType) }}
            </span>
            {{ item.signType | signType }}
            {{ item.contractStatus | contractStatus }}
            {{
                `${item.contractStartDate ? item.contractStartDate : ""}至${item.contractEndDate ? item.contractEndDate : ""
                }`
            }}
          </p>
          <i :class="{
            redColor: currentSelectItems.map(it => it.id).includes(item.id),
            'el-icon-success': true
          }"></i>
        </div>
        <!-- <img
          src="../../../assets/images/noUser.png"
          width="150"
          class="no-template"
          v-if="noInfo"
        /> -->
      </div>
      <div v-else class="empty-tips">
        <img src="../../../assets/images/empty.png" alt="">
        <h3>暂无数据，请先对员工发起劳动合同新签/续签</h3>
        <h4>操作步骤：</h4>
        <span>
          1.在合同管理->劳动合同管理中，发起新签/续签/变更，添加合同基本信息
        </span>
        <br>
        <span>
          2.人事发起签约，员工和企业签署人签约盖章。
        </span>
      </div>
    </el-main>
    <div class="footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave" :disabled="isSubmitDisabled">确定</el-button>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { apiGetSignContractRecord } from "../store/api";
export default {
  props: {
    isBatch: {
      type: Boolean,
      default: false
    },
    taxSubId: {
      type: Number
    }
  },
  data() {
    return {
      searchCondition: {
        key: "",
        contractType: "",
        signType: "",
        contractStatus: "",
        contractSubId: this.taxSubId,
        templateId: ""
      },
      list: [],
      loading: false,
      currentSelectItems: [],
      noInfo: false
    };
  },
  computed: {
    isSubmitDisabled() {
      return this.currentSelectItems.length > 0 ? false : true;
    },
    ...mapState("contractManageStore", {
      contractTypeList: "contractTypeList",
      chooseRecordData: "chooseRecordData",
      chooseTemplateData: "chooseTemplateData"
    })
  },
  created() {
    if (this.chooseTemplateData) {
      this.searchCondition.templateId = this.chooseTemplateData.id;
    }
    this.currentSelectItems =
      this.chooseRecordData.length > 0 ? this.chooseRecordData : [];
    this.getList();
  },
  methods: {
    async getList() {
      this.loading = true;
      let res = await apiGetSignContractRecord(this.searchCondition);
      if (res.success) {
        this.list = res.data;
        if (!res.data) {
          this.noInfo = true;
        } else {
          this.noInfo = false;
        }
      }
      this.loading = false;
    },
    handleSelectItem(item) {
      let arr = JSON.parse(JSON.stringify(this.currentSelectItems));
      if (this.isBatch) {
        let index = "";
        arr.forEach((it, idx) => {
          if (it.id === item.id) {
            index = idx;
          }
        });
        if (index !== "") {
          arr.splice(index, 1);
        } else {
          arr.push(item);
        }
      } else {
        arr = [item];
      }
      this.currentSelectItems = arr;
    },
    handleSave() {
      if (this.isBatch) {
        if (this.currentSelectItems.length < 2) {
          this.$message.warning("请至少选择2条记录");
          return;
        }
      }
      this.$store.commit(
        "contractManageStore/SET_CHOOSERECORDDATA",
        this.currentSelectItems
      );
      this.$parent.popShow.isshow = false;
    },
    handleCancel() {
      this.$parent.popShow.isshow = false;
    },
    //获取合同类型
    getContractType(val) {
      let filter = this.contractTypeList.filter(
        item => item.optionEnumCode === val
      );
      return filter.length > 0 ? filter[0].optionEnumName : "";
    }
  }
};
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/helpers";

.template-choose {
  height: calc(100vh - 130px);

  .search-template-input {
    margin-bottom: 10px;
    width: 400px;
  }

  .item {
    position: relative;
    cursor: pointer;
    border: 1px solid #d8d8d8;
    padding: 8px;
    margin: 10px 0;
    height: 50px;
    width: 420px;

    >p {
      overflow: hidden;
      height: 24px;
      line-height: 24px;
      color: #333;
      display: flex;
      align-items: center;
      font-size: 14px;
    }

    >p:nth-child(2) {
      margin-top: 5px;
      color: #999;
    }

    &:hover {
      border: 1px solid $mainColor;
    }

    &.active {
      border: 1px solid $mainColor;
      box-shadow: 0px 0px 20px #f1f6ff;
    }

    i {
      font-size: 26px;
      position: absolute;
      right: 10px;
      top: 20px;
      color: #333;
    }

    .redColor {
      color: $mainColor;
    }

    .name {
      font-weight: 600;
      display: inline-block;
      margin-right: 60px;
      width: 140px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .type {
      width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .no-template {
    display: inline-block;
    margin-left: 120px;
    margin-top: 40px;
  }

  .footer {
    box-sizing: border-box;
    width: 100%;
    height: 60px;
    line-height: 60px;
    box-shadow: 10px -2px 35px 0px rgba(222, 214, 214, 1);
    position: absolute;
    left: 0;
    bottom: 0;
    background: #fff;
    text-align: right;

    .el-button--primary {
      margin-right: 20px;
    }
  }

  .el-radio-group {
    display: block;
    margin-top: 10px;
  }
}

.el-main {
  padding: 0;
}
.list {
  padding: 30px;
}

.empty-tips {
  img {
    // text-align: center;
    margin-left: 120px;
  }
  h3 {
    margin-top: 20px;
    font-size: 16px;
    margin-bottom: 20px;
    color: #24262A;
    text-align: center;
  }
  h4 {
    color: #46485A;
    margin-bottom: 5px;
  }
  span {
    font-size: 14px;
    line-height: 30px;
    color: #46485A;
  }
  padding: 50px 30px;
}
</style>
