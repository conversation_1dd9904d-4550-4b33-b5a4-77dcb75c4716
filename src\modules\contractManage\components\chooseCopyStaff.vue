<template>
  <div class="staff-choose">
    <el-input
      v-model.trim="searchForm.key"
      @keyup.enter.native="handleSearch"
      placeholder="请输入姓名/工号/手机号"
      class="search-staff-input"
    ></el-input>
    <el-radio-group
      v-model="searchForm.empStatus"
      size="medium"
      @change="handleSearch"
    >
      <el-radio-button label="ON_THE_JOB">在职</el-radio-button>
      <el-radio-button label="DIMISSION">离职</el-radio-button>
      <el-radio-button label="RETIRED">退休</el-radio-button>
    </el-radio-group>
    <el-radio-group
      v-model="searchForm.empNatue"
      size="medium"
      @change="handleSearch"
    >
      <el-radio-button label="">全部</el-radio-button>
      <el-radio-button label="FULL_TIME">全职</el-radio-button>
      <el-radio-button label="PART_TIME">兼职</el-radio-button>
      <el-radio-button label="PRACTICE">实习</el-radio-button>
      <el-radio-button label="LABOUR">劳务</el-radio-button>
      <el-radio-button label="RE_EMPLOY">退休返聘</el-radio-button>
    </el-radio-group>
    <el-radio-group
      v-model="searchForm.turnRegularStatus"
      size="medium"
      @change="handleSearch"
    >
      <el-radio-button label="">全部</el-radio-button>
      <el-radio-button label="PROBATION">试用</el-radio-button>
      <el-radio-button label="OFFICIAL">转正</el-radio-button>
      <el-radio-button label="NO_PROBATION">无试用期</el-radio-button>
    </el-radio-group>
    <!-- 人员列表 -->
    <el-main v-loading="loading">
      <div class="staff-list">
        <div
          v-for="(item, index) in staffList"
          :key="index"
          :class="[
            'staff-item',
            {
              active: currentSelectVal
              ? currentSelectVal === item.compEmpId
              : currentSelectItem.empRecordId === item.empRecordId,
            },
            { disableStyle: !item.platformUserId || item.platformDisabledYn || disabledUserList.includes(item.compEmpId) },
          ]"
          @click="handleSelectItem(item)"
        >
          <p>
            <span class="staff-name">{{ item.empName }}</span>
            <span class="staff-mobile">{{ item.mobile }}</span>
          </p>
          <p>
            <span>
              {{ item.empStatus | filterEmployStatus }}
              {{ item.empNatue | filterEmpType }}
              {{ item.turnRegularStatus | filterTurnRegularStatus }}
            </span>
            <span
              >{{ item.taxSubName }}&nbsp;&nbsp;{{ item.departmentName }}</span
            >
          </p>
          <i
            :class="[
              currentSelectVal
              ? currentSelectVal === item.compEmpId ? 'redColor el-icon-success' : 'iconfont iconyuanxingweixuanzhong before-checked'
              : currentSelectItem.empRecordId === item.empRecordId?'redColor el-icon-success':'iconfont iconyuanxingweixuanzhong before-checked'
            ]"
          ></i>
        </div>
        <el-pagination
          small
          v-show="total > 0"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[20, 50, 100, 200]"
          :total="total"
          :pager-count="5"
          class="staff-page"
        ></el-pagination>
        <img
          src="../../../assets/images/noUser.png"
          width="150"
          class="no-template"
          v-if="noInfo"
        />
      </div>
    </el-main>
    <div class="footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        @click="handleSave"
        :disabled="isSubmitDisabled"
      >
        确定
      </el-button>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { apiContractSignUserList } from "../store/api";
export default {
  name: 'chooseCopyStaff',
  props: {
    operateType: {
      type: String,
    },
    alreadyCopyUserId: {
      type: Array,
      default: () => [],
    },
    currentClickSign: {
      type: [Number, String]
    }
  },
  data() {
    return {
      loading: false,
      searchForm: {
        key: "",
        currPage: 1,
        pageSize: 20,
        empStatus: "ON_THE_JOB",
        empNatue: "",
        turnRegularStatus: "",
      },
      total: 0,
      staffList: [],
      currentSelectItem: [],
      noInfo: false,
      currentSelectVal: this.currentClickSign
    };
  },
  computed: {
    disabledUserList() {
      return this.alreadyCopyUserId.filter(v => v !== this.currentClickSign)
    },
    isSubmitDisabled() {
      return this.currentSelectVal ? this.currentSelectVal === 'string' : !this.currentSelectItem.empRecordId;
    },
    ...mapState("contractManageStore", {
      chooseCopyStaffData: "chooseCopyStaffData",
    }),
  },
  created() {
    this.currentSelectItem = this.chooseCopyStaffData
      ? this.chooseCopyStaffData
      : [];
    this.getList();
  },
  methods: {
    async getList() {
      this.loading = true;
      this.searchForm.rangeList = [];
      this.searchForm.operateType = this.operateType;
      let res = await apiContractSignUserList(this.searchForm);
      if (res.success) {
        this.staffList = res.data.records;
        this.total = res.data.total;
        if (!res.data || res.data.records.length === 0) {
          this.noInfo = true;
        } else {
          this.noInfo = false;
        }
      }
      this.loading = false;
    },
    handleSelectItem(item) {
      if (!item.platformUserId || item.platformDisabledYn) {
        this.$message.warning("该员工帐号异常，无法发起签约");
        return;
      }
      if (this.disabledUserList.includes(item.compEmpId)) {
        this.$message.warning("抄送人已选择过该人员，不可重复选择");
        return;
      }
      if (this.currentClickSign) {
        this.currentSelectVal = this.currentSelectVal === item.compEmpId
          ? 'string'
          : item.compEmpId
      }
      if (item.empRecordId === this.currentSelectItem.empRecordId) {
        this.currentSelectItem = [];
      } else {
        this.currentSelectItem = item;
      }
    },
    handleSave() {
      this.$store.commit(
        "contractManageStore/SET_CHOOSECOPYSTAFFDATA",
        this.currentSelectItem
      );
      this.$parent.popShow.isshow = false;
    },
    handleCancel() {
      this.$parent.popShow.isshow = false;
    },
    handleSearch() {
      this.currPage = 1;
      this.getList();
    },
    //显示页数
    handleSizeChange(val) {
      this.searchForm.pageSize = val;
      this.searchForm.currPage = 1;
      this.getList();
    },
    //翻页
    handleCurrentChange(val) {
      this.searchForm.currPage = val;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";
.el-main {
  padding: 0;
  overflow: inherit;
}
.staff-list {
  text-align: center;
}
.staff-choose {
  .search-staff-input {
    padding: 0;
    margin-bottom: 10px;
  }
  .staff-item {
    position: relative;
    cursor: pointer;
    border: 1px solid #d8d8d8;
    padding: 10px;
    margin: 10px 0;
    height: 50px;
    > p {
      overflow: hidden;
      height: 24px;
      line-height: 24px;
      font-size: 12px;
      color: #999;
      display: flex;
      align-items: center;
      span:first-child {
        display: inline-block;
        margin-right: 60px;
        width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      span:last-child {
        display: inline-block;
        width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    > p:nth-child(2) {
      margin-top: 5px;
    }
    &:hover {
      border: 1px solid $mainColor;
    }
    &.active {
      border: 1px solid $mainColor;
      box-shadow: 0px 0px 20px #f1f6ff;
    }
    i {
      font-size: 26px;
      position: absolute;
      right: 10px;
      top: 20px;
      color: #333;
    }
    .redColor {
      color: $mainColor;
    }
    .before-checked{
      margin-right: 10px;
      color: #dcdfe6;
    }
    .staff-name,
    .staff-mobile {
      font-size: 14px;
      color: #333333;
      font-weight: 600;
    }
  }
  .disableStyle {
    > p {
      color: #d8d8d8;
    }
    &:hover {
      border: 1px solid #d8d8d8;
    }
    i {
      color: #d8d8d8;
    }
    .staff-name,
    .staff-mobile {
      color: #d8d8d8;
    }
  }
  .no-template {
    display: inline-block;
    margin-left: 120px;
    margin-top: 40px;
  }
  .footer {
    box-sizing: border-box;
    width: 100%;
    height: 60px;
    line-height: 60px;
    box-shadow: 10px -2px 35px 0px rgba(222, 214, 214, 1);
    position: absolute;
    left: 0;
    bottom: 0;
    background: #fff;
    text-align: right;
    .el-button--primary {
      margin-right: 20px;
    }
  }
  .el-radio-group {
    margin-bottom: 10px;
  }
}
.staff-page {
  /deep/ .el-pagination__jump {
    margin-left: 10px;
    font-size: 12px;
  }
  /deep/ .el-pagination__total {
    font-size: 12px;
  }
  /deep/.el-pagination__sizes .el-input .el-input__inner {
    font-size: 12px;
  }
}
</style>
