<template>
  <div class="staff-choose">
    <el-input
      v-model.trim="searchForm.key"
      @keyup.enter.native="getList"
      placeholder="请输入姓名/手机号"
      class="search-staff-input"
    ></el-input>
    <!-- 人员列表 -->
    <el-main v-loading="loading">
      <div class="staff-list">
        <div
          class="staff-item"
          v-for="(item, index) in staffList"
          :class="[
            'staff-item',
            {
              active: styleActive === item.platformUserId,
            },
            { disableStyle: !item.platformUserId || item.platformDisabledYn },
          ]"
          @click="handleSelectItem(item)"
          :key="index"
        >
          <p>
            <span class="staff-name">{{ item.empName }}</span>
            <span class="staff-mobile">{{ item.mobile }}</span>
          </p>
          <p>
            <span>
              {{ item.authYn ? "已认证" : "未认证" }}
            </span>
            <span>{{ item.role }}</span>
          </p>
          <i
            :class="[
              styleActive === item.platformUserId?'redColor el-icon-success':'iconfont iconyuanxingweixuanzhong before-checked'
            ]"
          ></i>
        </div>
        <!-- <img
          src="../../../assets/images/noUser.png"
          width="150"
          class="no-template"
          v-if="noInfo"
        /> -->
        <div v-if="noInfo" class="no-template-tips">
          <div class="icon-img">
            <img src="../../../assets/images/empty.png" alt="">
          </div>
          <div class="tips-content">
            <h2>未设置企业公章签署人</h2>
            <p>请在组织架构->组织管理中，添加或设置人员角色为公章签署人</p>
          </div>
        </div>
      </div>
    </el-main>
    <div class="footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave" :disabled="!this.currentSelectItem.platformUserId"
        >确定</el-button
      >
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { apiContractSealUserList } from "../store/api";
export default {
  name: 'chooseSealStaff',
  data() {
    return {
      loading: false,
      searchForm: {
        key: "",
      },
      staffList: [],
      currentSelectItem: {},
      noInfo: false,
      styleActive:''
    };
  },
  computed: {
    ...mapState("contractManageStore", {
      chooseSealStaffData: "chooseSealStaffData",
      flowStep:"flowStep"
    }),
  },
  created() {
    this.currentSelectItem.platformUserId = this.chooseSealStaffData
      ? this.chooseSealStaffData.platformUserId
      : "";
    this.getList();
    setTimeout(()=>{
      this.compareStaff()
    },300)
  },
  methods: {
    compareStaff(){
      this.flowStep.steps.map(item=>{
        this.staffList.map(staff=>{
          if(item.compEmpId == staff.platformUserId){
            this.styleActive = item.compEmpId
            this.$set(this.currentSelectItem, 'platformUserId', item.compEmpId)
          }
        })
      })
    },
    async getList() {
      this.loading = true;
      let res = await apiContractSealUserList(this.searchForm);
      if (res.success) {
        this.staffList = res.data;
        if (!res.data || res.data.length === 0) {
          this.noInfo = true;
        } else {
          this.noInfo = false;
        }
      }
      this.loading = false;
    },
    handleSelectItem(item) {
      if (!item.platformUserId || item.platformDisabledYn) {
        this.$message.warning("该员工帐号异常，无法发起签约");
        return;
      }
      if (item.platformUserId === this.currentSelectItem.platformUserId) {
        this.currentSelectItem = {};
        this.styleActive = '';
      } else {
        this.currentSelectItem = item;
        this.styleActive = item.platformUserId
      }
    },
    handleSave() {
      this.$store.commit(
        "contractManageStore/SET_CHOOSESEALSTAFFDATA",
        this.currentSelectItem
      );
      this.$parent.popShow.isshow = false;
    },
    handleCancel() {
      this.$parent.popShow.isshow = false;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";
.el-main {
  padding: 0;
}
.staff-list {
  text-align: center;
      .no-template-tips {
      height: 250px;
      margin-top: 50px;
      .icon-img{
        text-align: center;
        img{
          width: 200px;
          height: 115px;
        }
      }
      .tips-content{
        margin-top: 30px;
        h2 {
          height: 16px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 16px;
          color: #24262A;
          text-align: center;
          line-height: 16px;
          margin-bottom: 20px;
        }
        p {
          // width: 396px;
          text-align: center;
          height: 14px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #46485A;
          letter-spacing: 0;
          line-height: 14px;
        }
      }
    }
}
.staff-choose {
  .search-staff-input {
    padding: 0;
    margin-bottom: 10px;
  }
  .staff-item {
    position: relative;
    cursor: pointer;
    border: 1px solid #d8d8d8;
    padding: 10px;
    margin: 10px 0;
    height: 50px;
    > p {
      overflow: hidden;
      height: 24px;
      line-height: 24px;
      font-size: 12px;
      color: #999;
      display: flex;
      align-items: center;
      span:first-child {
        display: inline-block;
        margin-right: 60px;
        width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      span:last-child {
        display: inline-block;
        width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    > p:nth-child(2) {
      margin-top: 5px;
    }
    &:hover {
      border: 1px solid $mainColor;
    }
    &.active {
      border: 1px solid $mainColor;
      box-shadow: 0 0 20px #f1f6ff;
    }
    i {
      font-size: 26px;
      position: absolute;
      right: 10px;
      top: 20px;
      color: #333;
    }
    .redColor {
      color: $mainColor;
    }
    .before-checked {
      margin-right: 10px;
      color: #dcdfe6;
    }
    .staff-name,
    .staff-mobile {
      font-size: 14px;
      color: #333;
      font-weight: 600;
    }
  }
  .disableStyle {
    > p {
      color: #d8d8d8;
    }
    &:hover {
      border: 1px solid #d8d8d8;
    }
    i {
      color: #d8d8d8;
    }
    .staff-name,
    .staff-mobile {
      color: #d8d8d8;
    }
  }
  .no-template {
    display: inline-block;
    margin-top: 40px;
  }
  .footer {
    box-sizing: border-box;
    width: 100%;
    height: 60px;
    line-height: 60px;
    box-shadow: 10px -2px 35px 0px rgba(222, 214, 214, 1);
    position: absolute;
    left: 0;
    bottom: 0;
    background: #fff;
    text-align: right;
    .el-button--primary {
      margin-right: 20px;
    }
  }
  .el-radio-group {
    margin-bottom: 10px;
  }
}
</style>
