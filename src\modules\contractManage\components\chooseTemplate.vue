<template>
  <div class="template-choose">
    <el-input
      v-model.trim="searchCondition.templateName"
      @keyup.enter.native="getList"
      placeholder="请输入模板名称"
      class="search-template-input"
    ></el-input>
    <el-radio-group
      v-model="searchCondition.templateType"
      size="medium"
      @change="getList"
    >
      <el-radio-button label="" :disabled="isBatchSign">全部</el-radio-button>
      <el-radio-button label="LABOUR_CONTRACT">劳动合同</el-radio-button>
      <el-radio-button label="PROVE" :disabled="isBatchSign">
        证明
      </el-radio-button>
      <el-radio-button label="RULES" :disabled="isBatchSign">
        规章制度
      </el-radio-button>
      <el-radio-button label="OTHERS" :disabled="isBatchSign">
        其他
      </el-radio-button>
    </el-radio-group>
    <!-- 模板列表 -->
    <el-main v-loading="loading">
      <div class="template-list">
        <div
          class="template-item"
          v-for="(item, index) in templateList"
          :class="{ active: currentSelectTemplate.id === item.id }"
          @click="handleSelectItem(item)"
          :key="index"
        >
          <p>
            <el-tooltip :content="item.templateName" :disabled="item.templateName.length > 15 ? false : true">
              <span class="template-name">{{ item.templateName }}</span>
            </el-tooltip>
            <span>{{ item.createdTime }}</span>
          </p>
          <p class="template-type" :title="`${item.taxSubName.join(',')}`">
            {{ item.templateType | templateType }}
            {{ item.taxSubName.join(",") }}
          </p>
          <i
            :class="[
              currentSelectTemplate.id === item.id?'redColor el-icon-success':'iconfont iconyuanxingweixuanzhong before-checked'
            ]"
          ></i>
        </div>
        <!-- <img
          src="../../../assets/images/noUser.png"
          width="150"
          class="no-template"
          v-if="noInfo"
        /> -->
        <div v-if="noInfo" class="no-template-tips">
           <div class="icon-img">
            <img src="../../../assets/images/empty.png" alt="">
           </div>
           <div class="tips-content">
             <h2>未设置企业公章签署人</h2>
             <p>请在组织架构->组织管理中，添加或设置人员角色为公章签署人</p>
          </div>
        </div>
      </div>
    </el-main>
    <div class="footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        @click="handleSave"
        :disabled="isSubmitDisabled"
      >
        确定
      </el-button>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { apiGetContractTemplate } from "../store/api";
export default {
  props: ["taxSubId", "isBatchSign"],
  data() {
    return {
      searchCondition: {
        templateName: "",
        templateType: "",
        taxSubId: this.taxSubId,
      },
      templateList: [],
      loading: false,
      currentSelectTemplate: {},
      noInfo: false,
    };
  },
  computed: {
    isSubmitDisabled() {
      return this.currentSelectTemplate.id == "" ? true : false;
    },
    ...mapState("contractManageStore", {
      chooseTemplateData: "chooseTemplateData",
    }),
  },
  created() {
    this.currentSelectTemplate = this.chooseTemplateData
      ? this.chooseTemplateData
      : "";
    this.searchCondition.templateType = this.isBatchSign
      ? "LABOUR_CONTRACT"
      : "";
    this.getList();
  },
  methods: {
    async getList() {
      this.loading = true;
      let res = await apiGetContractTemplate(this.searchCondition);
      if (res.success) {
        this.templateList = res.data;
        if (!res.data || res.data.length === 0) {
          this.noInfo = true;
        } else {
          this.noInfo = false;
        }
      }
      this.loading = false;
    },
    handleSelectItem(item) {
      this.currentSelectTemplate = item;
    },
    handleSave() {
      this.$store.commit(
        "contractManageStore/SET_CHOOSETEMPLATEDATA",
        this.currentSelectTemplate
      );
      this.$parent.popShow.isshow = false;
    },
    handleCancel() {
      this.$parent.popShow.isshow = false;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/helpers";
.el-main {
  padding: 0;
}
.template-choose {
  .search-template-input {
    margin-bottom: 10px;
    width: 400px;
  }
  .template-item {
    position: relative;
    cursor: pointer;
    border: 1px solid #d8d8d8;
    padding: 10px;
    margin: 10px 20px 10px 0;
    height: 50px;
    border-radius: 4px;
    width: 400px;
    > p {
      overflow: hidden;
      height: 24px;
      line-height: 24px;
      color: #999;
      display: flex;
      align-items: center;
      font-size: 14px;
      justify-content: space-between;
    }
    > p:nth-child(2) {
      margin-top: 5px;
    }
    &:hover {
      border: 1px solid $mainColor;
    }
    &.active {
      border: 1px solid $mainColor;
      box-shadow: 0px 0px 20px #f1f6ff;
    }
    i {
      font-size: 20px;
      position: absolute;
      right: 20px;
      top: 35px;
      color: #A8ACBA;
      margin-right: 0;
    }
    .redColor {
      color: $mainColor;
    }
    .template-name {
      font-size: 14px;
      color: #333333;
      font-weight: 600;
      display: inline-block;
      margin-right: 60px;
      width: 210px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .template-type {
      display: block;
      width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .template-list{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    .no-template-tips {
      height: 250px;
      margin-top: 50px;
      .icon-img{
        text-align: center;
        img{
          width: 200px;
          height: 115px;
        }
      }
      .tips-content{
        margin-top: 30px;
        h2 {
          height: 16px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 16px;
          color: #24262A;
          text-align: center;
          line-height: 16px;
          margin-bottom: 20px;
        }
        p {
          // width: 396px;
          text-align: center;
          height: 14px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #46485A;
          letter-spacing: 0;
          line-height: 14px;
        }
      }
    }
  }
  .footer {
    box-sizing: border-box;
    width: 100%;
    height: 60px;
    line-height: 60px;
    box-shadow: 10px -2px 35px 0px rgba(222, 214, 214, 1);
    position: absolute;
    left: 0;
    bottom: 0;
    background: #fff;
    text-align: right;
    .el-button--primary {
      margin-right: 20px;
    }
  }
}
</style>
