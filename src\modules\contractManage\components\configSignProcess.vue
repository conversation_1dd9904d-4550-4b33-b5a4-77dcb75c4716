<template>
  <div class="config-sign-process">
    <section>
      <!-- 配置流程 -->
      <div>
        <p class="title"><span>配置流程</span></p>
        <div v-if="chooseTemplateData">
          <div class="process-content">
            <span style="color: red">*</span>
            <span class="process-name">签署顺序</span>
            <div class="process-step">
              <div
                class="auditing"
                v-for="(item, index) in flowStep.steps"
                :key="index"
              >
                <h3>
                  {{ item.stepName }}
                </h3>
                <!-- 选择人员 -->
                <div class="choosePerson">
                  <p
                    class="chooseAgin"
                    :class="{
                      'no-allowed':
                        (item.operate === 'SIGN' &&
                          chooseTemplateData.templateType ===
                            'LABOUR_CONTRACT') ||
                        (item.operate === 'SEAL' &&
                          form.silenceSignYn === 'true'),
                    }"
                    :title="item.compEmpName"
                    @click="showSearchStaff(item, index, true)"
                  >
                    <span class="choose-more-person">
                      <i
                        class="icon iconfont"
                        :class="[
                          item.operate === 'SEAL' &&
                          form.silenceSignYn === 'true'
                            ? 'attention'
                            : '',
                        ]"
                        v-if="
                          !item.compEmpName &&
                          (item.operate !== 'SIGN' ||
                            chooseTemplateData.templateType !==
                              'LABOUR_CONTRACT')
                        "
                        >&#xe60b;
                      </i>
                      <span v-if="item.compEmpName" class="user-name">
                        {{ item.compEmpName }}
                        <i
                          class="el-icon-close"
                          @click.stop="closeUser(item)"
                        ></i>
                      </span>
                      <span
                        v-else
                        :class="[
                          (item.operate === 'SIGN' &&
                            chooseTemplateData.templateType ===
                              'LABOUR_CONTRACT') ||
                          (item.operate === 'SEAL' &&
                            form.silenceSignYn === 'true')
                            ? 'attention'
                            : '',
                        ]"
                      >
                        {{
                          item.operate === "SIGN" &&
                          chooseTemplateData.templateType === "LABOUR_CONTRACT"
                            ? "请直接选择合同记录"
                            : "选择人员"
                        }}
                      </span>
                    </span>
                  </p>
                </div>
                <div
                  v-show="isShowTips && !item.compEmpName"
                  style="color: red"
                >
                  <span v-if="item.stepName === '企业签署方'"
                    >请选择企业签署方人员</span
                  >
                  <span v-else>请选择个人签署方人员</span>
                </div>
              </div>
            </div>
          </div>
          <div
            class="process-content"
            v-show="flowStep.carbonCopyList.length > 0"
          >
            <span class="process-name">抄送</span>
            <div class="process-step">
              <div
                class="auditing"
                v-for="(item, index) in flowStep.carbonCopyList"
                :key="index"
              >
                <h3>
                  {{ item.stepName }}
                </h3>
                <!-- 选择人员 -->
                <div class="choosePerson">
                  <p class="chooseAgin">
                    <span
                      class="choose-copy-person"
                      @click.stop="showSearchStaff(item, index)"
                    >
                      <i class="icon iconfont" v-if="!item.compEmpName"
                        >&#xe60b;</i
                      >
                      <span v-if="item.compEmpName" class="user-name">
                        {{ item.compEmpName }}
                        <i
                          class="el-icon-close"
                          @click.stop="closeUser(item)"
                        ></i>
                      </span>
                      <span v-else> 选择人员 </span>
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 填充域 -->
      <div>
        <p class="title">
          <span> 填充域 </span>
        </p>
        <el-tabs v-model="activeNum" v-if="chooseTemplateData">
          <el-tab-pane
            :label="item.stepName"
            v-for="(item, index) in flowStep.steps"
            :key="index"
          ></el-tab-pane>
        </el-tabs>

        <update-fill-field
          v-if="chooseTemplateData"
          class="fill-field"
          :isBatch="isBatch"
          :operateType="flowStep.steps[activeNum].operate"
          :newTableData="newTableData"
          @change="onChange"
        ></update-fill-field>
      </div>
    </section>

    <right-pop
      :pop-show="popShow"
      :has-footer="false"
      popTitle="选择人员"
      :popWidth="500"
    >
      <div slot="pop-content">
        <choose-seal-staff
          v-if="operateType === 'SEAL'"
          ref="chooseSealStaff"
        ></choose-seal-staff>
        <choose-copy-staff
          v-if="operateType === 'CARBON_COPY'"
          :alreadyCopyUserId="alreadyCopyUserId"
          :currentClickSign="currentClickSign"
          ref="chooseCopyStaff"
        ></choose-copy-staff>
        <choose-sign-staff
          v-if="operateType === 'SIGN'"
          :alreadyUsedUserId="alreadyUsedUserId"
          :currentClickSign="currentClickSign"
          ref="chooseSignStaff"
          :operateType="operateType"
          :isBatch="isBatch"
          :rangeList="
            chooseTemplateData && operateType === 'SIGN'
              ? chooseTemplateData.taxSubId
              : []
          "
        ></choose-sign-staff>
      </div>
    </right-pop>
  </div>
</template>

<script>
import { mapState } from "vuex";
import rightPop from "@/components/basic/rightPop";
import chooseSealStaff from "./chooseSealStaff";
import chooseSignStaff from "./chooseSignStaff";
import chooseCopyStaff from "./chooseCopyStaff";
import updateFillField from "./updateFillField";
import { apiGetRelationValue } from "../store/api";
export default {
  name: "configSignProcess",
  props: {
    isReadonly: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
    },
  },
  components: {
    rightPop,
    chooseSealStaff,
    chooseSignStaff,
    chooseCopyStaff,
    updateFillField,
  },
  computed: {
    newTableData(){
      const tableData = this.flowStep.steps[this.activeNum].filedList
          ? this.flowStep.steps[this.activeNum].filedList.filter(
              (v) =>
              v.fieldType !== 'DATE' &&
              v.fieldType !== 'SEAL' &&
              v.fieldType !== 'SIGN'
          )
          : []

      tableData.forEach(item=>{
        this.$set(item,"isEdit",false)
      })
      return tableData
    },
    isBatch() {
      return this.form.signWay === "MULTI";
    },
    ...mapState("contractManageStore", {
      chooseSealStaffData: "chooseSealStaffData",
      chooseSignStaffData: "chooseSignStaffData",
      chooseCopyStaffData: "chooseCopyStaffData",
      chooseTemplateData: "chooseTemplateData",
      chooseRecordData: "chooseRecordData",
    }),
    alreadyCopyUserId() {
      return this.flowStep.carbonCopyList.map((x) => x.compEmpId);
    },
    alreadyUsedUserId() {
      return this.flowStep.steps.length
        ? this.flowStep.steps
            .filter((v) => v.operate === "SIGN" && v.compEmpId)
            .map((x) => x.compEmpId)
        : [];
    },
  },
  watch: {
    chooseSealStaffData(val) {
      val && this.comfirSealStaff();
    },
    chooseSignStaffData() {
      if (this.chooseSignStaffData) {
        this.comfirSignStaff();
      }
    },
    chooseCopyStaffData() {
      if (this.chooseCopyStaffData) {
        this.comfirCopyStaff();
      }
    },
    chooseRecordData() {
      this.handleRecordChange();
    },
    "flowStep.steps": {
      handler(list) {
        let flag = false;
        list.forEach((v) => {
          if (v.operate === "SEAL") {
            flag = true;
          }
        });
        this.$emit("hiddenStatic", flag);
      },
      deep: true,
    },
    "form.silenceSignYn": {
      handler(val) {
        if (val === "true") {
          let enterprise = this.flowStep.steps.filter(
            (v) => v.operate === "SEAL"
          );
          this.closeUser(enterprise[0]);
        }
      },
    },
  },
  data() {
    return {
      currentClickSign: "",
      flowStep: {
        steps: [
          {
            operate: "SEAL",
            stepName: "企业签署方",
            compEmpName: "",
            compEmpId: "",
            filedList: [],
            stepUserList: [],
          },
          {
            operate: "SIGN",
            stepName: "个人签署方",
            compEmpName: "",
            compEmpId: "",
            filedList: [],
            stepUserList: [],
          },
        ],
        carbonCopyList: [],
      },
      activeNum: 0,
      popShow: { isshow: false },
      currentIndex: "",
      showAddCopy: true,
      operateType: "",
      isShowTips: false, // 是否显示企业/个人签署方提示语
    };
  },
  created() {
    this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
  },
  methods: {
    onChange(tableData){
      tableData.forEach((item,index)=>{
        this.flowStep.steps[this.activeNum].filedList[index].relationValue = item.relationValue
      })
    },
    // 签署删除选中的用户
    closeUser(item) {
      this.currentClickSign = "";
      item.compEmpId = "";
      item.compEmpName = "";
      this.isShowTips = false;
    },
    //显示员工查询弹窗
    showSearchStaff(item, index, mark = false) {
      if (mark) {
        if (
          item.operate === "SIGN" &&
          this.chooseTemplateData.templateType === "LABOUR_CONTRACT"
        ) {
          return;
        }
      }
      if (item.operate === "SEAL" && this.form.silenceSignYn === "true") {
        // this.$message.warning('')
        return;
      }
      this.currentIndex = index;
      this.operateType = item.operate;
      this.currentClickSign = item.compEmpId;
      if (item.operate === "SIGN") {
        if (this.chooseTemplateData.length === 0) {
          this.$message.warning("请先选择模板适用范围");
          return;
        }
      }
      if (!item.compEmpId) {
        if (item.operate === "SIGN") {
          this.$store.commit("contractManageStore/SET_CHOOSESIGNSTAFFDATA", []);
        } else if (item.operate === "SEAL") {
          this.$store.commit(
            "contractManageStore/SET_CHOOSESEALSTAFFDATA",
            null
          );
        } else if (item.operate === "CARBON_COPY") {
          this.$store.commit(
            "contractManageStore/SET_CHOOSECOPYSTAFFDATA",
            null
          );
        }
      }
      this.popShow.isshow = true;
    },
    //确认企业签署方
    comfirSealStaff() {
      let currentIndex = this.currentIndex;
      let compEmpName = this.chooseSealStaffData
        ? this.chooseSealStaffData.empName
        : "";
      let compEmpId = this.chooseSealStaffData
        ? this.chooseSealStaffData.platformUserId
        : "";
      //发起签约需要的格式
      let stepUserList = [
        {
          compEmpName,
          compEmpId,
          empContractId: "",
        },
      ];
      this.$set(this.flowStep.steps[currentIndex], "compEmpName", compEmpName);
      this.$set(this.flowStep.steps[currentIndex], "compEmpId", compEmpId);
      this.$set(
        this.flowStep.steps[currentIndex],
        "stepUserList",
        stepUserList
      );
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
      this.getStaffField();
    },
    //确认个人签署方
    comfirSignStaff() {
      if (this.currentIndex === "") {
        return;
      }
      let currentIndex = this.currentIndex;
      let compEmpName = null;
      let compEmpId = null;
      //发起签约需要的格式
      let stepUserList = [];
      let nameArr = [];
      let idArr = [];
      if (this.chooseSignStaffData.length > 0) {
        this.chooseSignStaffData.map((item) => {
          nameArr.push(item.empName);
          idArr.push(item.compEmpId);
          stepUserList.push({
            empRecordId: item.empRecordId,
            compEmpName: item.empName,
            compEmpId: item.compEmpId,
            empContractId: "",
          });
        });
        compEmpName = nameArr.join(",");
        compEmpId = idArr.join(",");
      }
      this.$set(this.flowStep.steps[currentIndex], "compEmpName", compEmpName);
      this.$set(this.flowStep.steps[currentIndex], "compEmpId", compEmpId);
      this.$set(
        this.flowStep.steps[currentIndex],
        "stepUserList",
        stepUserList
      );
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
      this.getStaffField();
    },
    //确认抄送人
    comfirCopyStaff() {
      let currentIndex = this.currentIndex;
      let compEmpName = this.chooseCopyStaffData.empName;
      let compEmpId = this.chooseCopyStaffData.compEmpId;
      let empRecordId = this.chooseCopyStaffData.empRecordId;
      //发起签约需要的格式
      let stepUserList = [
        {
          empRecordId,
          compEmpName,
          compEmpId,
          empContractId: "",
        },
      ];
      this.$set(
        this.flowStep.carbonCopyList[currentIndex],
        "compEmpName",
        compEmpName
      );
      this.$set(
        this.flowStep.carbonCopyList[currentIndex],
        "compEmpId",
        compEmpId
      );
      this.$set(
        this.flowStep.carbonCopyList[currentIndex],
        "stepUserList",
        stepUserList
      );
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
    },
    //合同记录回显个人签署方
    handleRecordChange() {
      this.flowStep.steps.map((item) => {
        if (item.operate === "SIGN") {
          item.compEmpName = "";
          item.compEmpId = "";
          item.stepUserList = [];
          if (this.chooseRecordData.length > 0) {
            let arr = [];
            let idArr = [];
            this.chooseRecordData.map((it) => {
              arr.push(it.empName);
              idArr.push(it.compEmpId);
              item.stepUserList.push({
                empRecordId: it.empRecordId,
                compEmpName: it.empName,
                compEmpId: it.compEmpId,
                empContractId: it.id,
              });
            });
            item.compEmpName = arr.join(",");
            item.compEmpId = idArr.join(",");
          }
        }
      });
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
      this.getStaffField();
    },
    //查询签署人关联信息项值
    async getStaffField() {
      let taxSubId = this.form.taxSubId;
      this.flowStep.steps.map((item) => {
        let contractId =
          item.stepUserList.length > 0
            ? item.stepUserList[0].empContractId
            : null;
        let empRecordId =
          item.stepUserList.length > 0
            ? item.stepUserList[0].empRecordId
            : null;
        if (
          (item.operate === "SEAL" || !this.isBatch) &&
          taxSubId &&
          (item.operate === "SEAL" || item.compEmpId)
        ) {
          apiGetRelationValue({
            compEmpId: item.compEmpId,
            operateType: item.operate,
            relationGroup: item.filedList
              ? item.filedList.filter(
                  (v) =>
                    v.fieldType !== "DATE" &&
                    v.fieldType !== "SEAL" &&
                    v.fieldType !== "SIGN"
                )
              : [],
            taxSubId,
            contractId,
            empRecordId,
          }).then((res) => {
            if (res.success) {
              let relationGroup = res.data.relationGroup;
              item.filedList = relationGroup;
            }
          });
        }
      });
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/helpers";
.no-allowed {
  cursor: not-allowed;
}
.user-name {
  max-width: 85%;
  box-sizing: border-box;
  padding: 0 30px 0 16px;
  display: inline-block;
  background: #f1f1f1;
  font-size: 14px;
  color: #070f29 !important;
  border-radius: 16px;
  height: 32px;
  line-height: 32px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  position: relative;
  .el-icon-close {
    position: absolute;
    top: 10px;
    right: 6px;
  }
}
.title {
  font-size: 18px;
  margin: 20px 0;
  display: flex;
  align-items: center;
}
.title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 14px;
  background-color: $mainColor;
  border-radius: 3px;
  margin-right: 8px;
}
.config-sign-process {
  margin-top: 20px;
  background: #fff;
  padding: 0 20px 0 0;
  .process-name {
    font-size: 16px;
  }
  .process-content {
    .title-content {
      color: #333;
      font-size: 18px;
    }
  }
  .process-step {
    background: #fff;
    margin: 20px 0;
    padding: 20px 9px;
    border: 1px solid #e6e6e6;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    min-height: 90px;
    h3 {
      height: 40px;
      line-height: 40px;
      background: #fff5da;
      position: relative;
    }
    .auditing {
      width: 204px;
      height: 122px;
      float: left;
      padding: 0 10px;
      text-align: center;
      .choosePerson {
        p {
          background: #fff;
          border: 1px solid #f4f4f4;
        }
        p.chooseDirection {
          height: 30px;
          line-height: 30px;
        }
        .chooseAgin {
          position: relative;
          span {
            color: $mainColor;
          }
          .changeStaff {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 60px;
            line-height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .choose-more-person {
            width: 100%;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            .attention {
              color: #6a6f7f;
            }
            .icon {
              margin-right: 5px;
            }
          }
          .choose-copy-person {
            width: 100%;
            height: 60px;
            display: flex;
            cursor: pointer;
            justify-content: center;
            align-items: center;
            .icon {
              margin-right: 5px;
            }
          }
        }
      }
      .chooseNewPerson {
        p {
          height: 84px;
          line-height: 84px;
        }
      }
    }
    .el-dropdown {
      align-self: center;
    }
  }
  .el-icon-info {
    margin-left: 5px;
  }
}
</style>
