<template>
  <div class="config-template-process">
    <section>
      <!-- 配置流程 -->
      <div>
        <p class="title"><span>配置流程</span></p>
        <div>
          <div class="process-content">
            <span style="color: red">*</span>
            <span class="process-name">签署顺序</span>
            <div class="process-step">
              <div
                class="auditing"
                v-for="(item, index) in flowStep.steps"
                :key="index"
              >
                <h3 v-if="item.operate == 'SIGN'">
                  个人签署方{{ personStep.length > 1 ? item.signIndex + 1 : '' }}
                  <i
                    class="el-icon-error remove-icon"
                    @click="deleteBtn(item, index)"
                    v-if="tempId ? !tempId : !isReadonly"
                  ></i>
                </h3>
                <h3 v-if="item.operate == 'SEAL'">
                  企业签署方
                  <i
                    class="el-icon-error remove-icon"
                    @click="deleteBtn(item, index)"
                    v-if="tempId ? !tempId : !isReadonly"
                  ></i>
                </h3>
                <!-- 选择人员 -->
                <div class="choosePerson">
                  <p
                    class="chooseAgin"
                    :title="
                      item.operate == 'SIGN' &&
                      baseForm.templateType === 'LABOUR_CONTRACT'
                        ? '劳动合同类模板不支持直接选择个人签署方'
                        : item.compEmpName
                    "
                  >
                    <span
                      class="changeStaff"
                    >
                      <template>
                        <el-radio-group v-model="item.radioVal" :disabled="item.operate === 'SIGN' && baseForm.templateType === 'LABOUR_CONTRACT'" @change="radioChange">
                        <el-tooltip v-if="item.operate === 'SEAL'" placement="top" content="所有使用该模板的合同，由现在选择的人员进行公章签署">
                          <el-radio label="1">现在选择</el-radio>
                        </el-tooltip>
                         <el-radio v-if="item.operate === 'SIGN'" label="1">现在选择</el-radio>
                        <el-tooltip v-if="item.operate === 'SEAL'" placement="top" content="由发起人选择企业公章签署人">
                            <el-radio label="2">由发起人选择</el-radio>
                        </el-tooltip>
                         <el-radio v-if="item.operate === 'SIGN'" label="2">由发起人选择</el-radio>
                        </el-radio-group>
                      </template>
                    </span>
                    <span
                      class="changeStaff"
                    >
                      <span
                        class="choose-more-person"
                        v-if="item.radioVal === '1'
                              || (item.operate === 'SIGN'
                              && baseForm.templateType === 'LABOUR_CONTRACT')"
                              @click="showSearchSeal(item, index)"
                      >
                           <i
                             class="icon iconfont"
                             v-if="!item.compEmpName
                                  && (item.operate !== 'SIGN'
                                  || baseForm.templateType !== 'LABOUR_CONTRACT')"
                           >&#xe60b;
                          </i>
                        <span v-if="item.compEmpName" class="user-name">
                          {{ item.compEmpName }}
                          <i class="el-icon-close" @click.stop="closeUser(item)"></i>
                        </span>
                        <span v-else @click.stop="showSearchSeal(item, index)" :class="[item.operate === 'SIGN'
                              && baseForm.templateType === 'LABOUR_CONTRACT'?'attention':'']">
                          {{ item.operate === "SIGN"
                              && baseForm.templateType === "LABOUR_CONTRACT"
                              ? "劳动合同类模板不支持直接选择个人签署方"
                              : "选择人员" }}
                        </span>
                      </span>
                    </span>
                  </p>
                </div>
                <!-- 左右移动图标显示 -->
                <p class="move-icon" v-if="flowStep.steps.length != 1 && !tempId">
                  <i
                    class="icon iconfont move-left"
                    @click="moveLeft(index)"
                    title="调整顺序"
                    v-if="index != 0"
                  >&#xe668;</i>
                  <i
                    class="icon iconfont move-right"
                    @click="moveRight(index)"
                    title="调整顺序"
                    v-if="index != flowStep.steps.length - 1"
                  >&#xe668;</i>
                </p>
              </div>
              <!-- 只有'个人签署方'，隐藏下拉，默认点击图标新增个人签署方 -1.14 -->
              <template v-if="!showAddSeal">
                <i
                  class="el-icon-circle-plus-outline addBtn"
                  v-if="tempId ? !tempId : !isReadonly"
                  @click="handleCommand('SIGN')"
                ></i>
              </template>
              <!-- 图标-添加新节点 -->
              <el-dropdown v-else @command="handleCommand" placement="bottom-start">
                <span class="el-dropdown-link">
                  <i
                    class="el-icon-circle-plus-outline addBtn"
                    v-if="tempId ? !tempId : !isReadonly"
                  ></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="SEAL" v-show="showAddSeal">
                    企业签署方
                  </el-dropdown-item>
                  <el-dropdown-item command="SIGN">个人签署方</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div class="process-content">
            <span class="process-name">抄送</span>
            <div class="process-step">
              <div
                class="auditing"
                v-for="(item, index) in flowStep.carbonCopyList"
                :key="index"
              >
                <h3>
                  {{ item.stepName }}
                  {{ flowStep.carbonCopyList.length > 1 ? index + 1 : '' }}
                  <i
                    class="el-icon-error remove-icon"
                    @click="deleteBtn(item, index)"
                  ></i>
                </h3>
                <!-- 选择人员 -->
                <div class="choosePerson">
                  <p class="chooseAgin">
                    <span
                      class="changeStaff"
                    >
                      <template>
                        <el-radio-group v-model="item.radioVal" @change="radioChange">
                          <el-tooltip placement="top" content="所有使用该模板的合同，抄送给现在选择的人员">
                            <el-radio label="1">现在选择</el-radio>
                          </el-tooltip>
                          <el-tooltip placement="top" content="由发起人选择抄送人员">
                            <el-radio label="2">由发起人选择</el-radio>
                          </el-tooltip>
                        </el-radio-group>
                      </template>
                    </span>
                    <span
                      class="changeStaff"
                    >
                      <span class="choose-copy-person" v-if="item.radioVal == '1'" @click="showSearchStaff(item, index)">
                        <i
                          class="icon iconfont"
                          v-if="!item.compEmpName"
                        >&#xe60b;
                        </i>
                        <span v-if="item.compEmpName" class="user-name">
                          {{ item.compEmpName }}
                          <i class="el-icon-close" @click.stop="closeUser(item)"></i>
                        </span>
                        <span v-else @click.stop="showSearchStaff(item, index)">
                          选择人员
                        </span>
                      </span>
                    </span>
                  </p>
                </div>
              </div>
              <!-- 图标-添加新节点 -->
              <i
                class="el-icon-circle-plus-outline addBtn"
                @click="handleAddCopy"
              ></i>
            </div>
          </div>
        </div>
      </div>
      <!-- 填充域 -->
<!--      <div>-->
<!--        <p class="title">-->
<!--          填充域-->
<!--          <el-tooltip-->
<!--            content="请先配置签署人的文本控件，若此项为空，在设置签署位置时页面左侧将无自定义控件可供拖拽"-->
<!--            placement="top"-->
<!--          >-->
<!--            <i class="el-icon-info"></i>-->
<!--          </el-tooltip>-->
<!--        </p>-->
<!--        <el-tabs v-model="activeName">-->
<!--          <el-tab-pane-->
<!--            :label="item.stepName"-->
<!--            v-for="(item, index) in flowStep.steps"-->
<!--            :key="index"-->
<!--          ></el-tab-pane>-->
<!--        </el-tabs>-->
<!--        &lt;!&ndash; 填充域 &ndash;&gt;-->
<!--        <fill-field-->
<!--          class="fill-field"-->
<!--          ref="fillField"-->
<!--          v-if="flowStep.steps.length > 0"-->
<!--          :operateType="flowStep.steps[activeName].operate"-->
<!--          :tableData="-->
<!--            flowStep.steps[activeName].filedList-->
<!--              ? flowStep.steps[activeName].filedList-->
<!--              : []-->
<!--          "-->
<!--          @getFillData="getFillData"-->
<!--        ></fill-field>-->
<!--      </div>-->
    </section>

    <right-pop
      :pop-show="popShow"
      :has-footer="false"
      popTitle="选择人员"
      :popWidth="500"
    >
      <div slot="pop-content">
        <choose-seal-staff
          v-if="operateType === 'SEAL'"
          ref="chooseSealStaff"
        ></choose-seal-staff>
        <choose-copy-staff
          v-if="operateType === 'CARBON_COPY'"
          :alreadyCopyUserId="alreadyCopyUserId"
          :currentClickSign="currentClickSign"
          ref="chooseCopyStaff"
        ></choose-copy-staff>
        <choose-sign-staff
          ref="chooseSignStaff"
          :alreadyUsedUserId="alreadyUsedUserId"
          :currentClickSign="currentClickSign"
          v-if="operateType === 'SIGN'"
          :operateType="operateType"
          :rangeList="operateType === 'SIGN' ? baseForm.taxSubId : []"
        ></choose-sign-staff>
      </div>
    </right-pop>
  </div>
</template>

<script>
import { mapState } from "vuex";
import rightPop from "@/components/basic/rightPop";
import chooseSealStaff from "./chooseSealStaff";
import chooseSignStaff from "./chooseSignStaff";
import chooseCopyStaff from "./chooseCopyStaff";
import fillField from "./fillField";
export default {
  name: 'configTemplateProcess',
  props: {
    tempId: [Number, String],
    isReadonly: {
      type: Boolean,
      default: false,
    },
    baseForm: {
      type: Object,
      default: {},
    },
    updateFlowStep: Boolean
  },
  components: {
    rightPop,
    chooseSealStaff,
    chooseSignStaff,
    chooseCopyStaff,
    fillField,
  },
  computed: {
    ...mapState("contractManageStore", {
      chooseSealStaffData: "chooseSealStaffData",
      chooseSignStaffData: "chooseSignStaffData",
      chooseCopyStaffData: "chooseCopyStaffData",
    }),
    showAddSeal() {
      return (
        this.flowStep.steps.filter((item) => item.operate === "SEAL").length ===
        0
      );
    },
    alreadyUsedUserId() {
      return this.flowStep.steps.length
        ? this.flowStep.steps.filter(v => v.operate === 'SIGN' && v.compEmpId).map(x => x.compEmpId)
        : []
    },

    alreadyCopyUserId() {
      return this.flowStep.carbonCopyList.map(x => x.compEmpId)
    },
    personStep() {
      return this.flowStep.steps.filter(v => v.operate === 'SIGN')
    }
  },
  watch: {
    chooseSealStaffData(val) {
      val && this.comfirSealStaff();
    },
    chooseSignStaffData() {
      if (this.chooseSignStaffData.length) {
        this.comfirSignStaff();
      }
    },
    chooseCopyStaffData() {
      if (this.chooseCopyStaffData) {
        this.comfirCopyStaff();
      }
    },
    flowStep: {
      handler() {
        this.filterSign();
      },
      deep: true,
      immediate: true,
    },
    updateFlowStep(val) {
      // val && this.initFlowStep()
    }
  },
  data() {
    return {
      flowStep: {
        steps: [
          {
            operate: "SIGN",
            stepName: "个人签署方",
            compEmpName: "",
            compEmpId: "",
            filedList: [],
            radioVal: '2'  // 现在选择 | 签约选择
          },
          {
            operate: "SEAL",
            stepName: "企业签署方",
            compEmpName: "",
            compEmpId: "",
            filedList: [],
            radioVal: '1'  // 现在选择 | 签约选择
          }
        ],
        carbonCopyList: [],
      },
      activeName: 0,
      popShow: { isshow: false },
      currentIndex: "",
      showAddCopy: true,
      operateType: "",
      currentClickSign: '',  // 个人签署方当前点击的人员
    };
  },
  created() {
    this.filterSign();
    this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
  },
  methods: {
    radioChange() {
      // if (item.compEmpName) {
      //   this.closeUser(item);
      // }
      this.$forceUpdate();
    },
    // 整理更新数据
    initStepData() {
      let steps = this.flowStep.steps;
      steps.forEach(v => {
        if (v.compEmpName) {
          v.radioVal = '1';
        } else {
          v.radioVal = '2';
        }
      })
      this.flowStep.carbonCopyList.forEach(item=>{
        item.compEmpName?item.radioVal = '1':item.radioVal = '2'
      })
    },
    // 初始化签章顺序
    initFlowStep() {
      this.flowStep.steps = [
        {
          operate: "SIGN",
          stepName: "个人签署方",
          compEmpName: "",
          compEmpId: "",
          filedList: [],
          radioVal: '2'  // 现在选择 | 签约选择
        },
        {
          operate: "SEAL",
          stepName: "企业签署方",
          compEmpName: "",
          compEmpId: "",
          filedList: [],
          radioVal: '1'  // 现在选择 | 签约选择
        }
      ]
    },

    // 签署删除选中的用户
    closeUser(item) {
      this.currentClickSign = '';
      item.compEmpId = '';
      item.compEmpName = '';
      // if (item.operate === 'CARBON_COPY') {
      //   const index = this.flowStep.carbonCopyList.findIndex(v => v.compEmpName === item.compEmpName);
      //   this.$set(this.flowStep.carbonCopyList[index], "compEmpName", '');
      //   this.$set(this.flowStep.carbonCopyList[index], "compEmpId", '');
      // } else {
      //   const index = this.flowStep.steps.findIndex(v => v.compEmpName === item.compEmpName);
      //   this.$set(this.flowStep.steps[index], "compEmpName", '');
      //   this.$set(this.flowStep.steps[index], "compEmpId", '');
      // }
    },

    // showSearchStaff方法是否调用
    showSearchSeal(item, index) {
      if (this.isReadonly && item.operate !== 'SEAL') return;
      this.showSearchStaff(item, index)
    },

    // 显示员工查询弹窗
    showSearchStaff(item, index) {
      // debugger
      this.currentIndex = index;
      this.operateType = item.operate;
      this.currentClickSign = item.compEmpId;
      if (item.operate === "SIGN") {
        if (this.baseForm.taxSubId.length === 0) {
          this.$message.warning("请先选择模板适用范围");
          return;
        }
      }
      if (!item.compEmpId) {
        if (item.operate === "SIGN") {
          this.$store.commit("contractManageStore/SET_CHOOSESIGNSTAFFDATA", []);
        } else if (item.operate === "SEAL") {
          this.$store.commit("contractManageStore/SET_CHOOSESEALSTAFFDATA", null);
        } else if (item.operate === "CARBON_COPY") {
          this.$store.commit(
            "contractManageStore/SET_CHOOSECOPYSTAFFDATA",
            null
          );
        }
      }
      this.popShow.isshow = true;
    },

    //确认企业签署方
    comfirSealStaff() {
      let currentIndex = this.currentIndex;
      let compEmpName = this.chooseSealStaffData.empName;
      let compEmpId = this.chooseSealStaffData.platformUserId;
      this.$set(this.flowStep.steps[currentIndex], "compEmpName", compEmpName);
      this.$set(this.flowStep.steps[currentIndex], "compEmpId", compEmpId);
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
    },
    //确认个人签署方
    comfirSignStaff() {
      let currentIndex = this.currentIndex;
      let compEmpName = this.chooseSignStaffData[0].empName;
      let compEmpId = this.chooseSignStaffData[0].compEmpId;
      let empRecordId = this.chooseSignStaffData[0].empRecordId;
      this.$set(this.flowStep.steps[currentIndex], "compEmpName", compEmpName);
      this.$set(this.flowStep.steps[currentIndex], "compEmpId", compEmpId);
      this.$set(this.flowStep.steps[currentIndex], "empRecordId", empRecordId);
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
    },
    //确认抄送人
    comfirCopyStaff() {
      let currentIndex = this.currentIndex;
      let compEmpName = this.chooseCopyStaffData.empName;
      let compEmpId = this.chooseCopyStaffData.compEmpId;
      this.$set(
        this.flowStep.carbonCopyList[currentIndex],
        "compEmpName",
        compEmpName
      );
      this.$set(
        this.flowStep.carbonCopyList[currentIndex],
        "compEmpId",
        compEmpId
      );
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
    },
    //新增签署
    handleCommand(command) {
      if (command === "SEAL") {
        this.flowStep.steps.push({
          operate: "SEAL",
          stepName: "企业签署方",
          compEmpName: "",
          compEmpId: "",
          filedList: [],
          radioVal: '1'  // 现在选择 | 签约选择
        });
        this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
      } else {
        let arr = this.flowStep.steps.filter((it) => it.operate == "SIGN");
        let index = 0;
        this.flowStep.steps.forEach((v, i) => {
          if(v.operate === 'SIGN') {
            index = i;
          }
        })
        if (arr.length < 9) {
          this.flowStep.steps.splice(index + 1,0, {
            operate: "SIGN",
            stepName: "个人签署方",
            compEmpName: "",
            compEmpId: "",
            filedList: [],
            radioVal: '2'  // 现在选择 | 签约选择
          });
          this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
        } else {
          this.$message.warning("最多可以添加9个个人签署方");
          return;
        }
      }
    },
    //新增抄送
    handleAddCopy() {
      this.flowStep.carbonCopyList.push({
        operate: "CARBON_COPY",
        stepName: "抄送人",
        compEmpName: "",
        compEmpId: "",
        filedList: [],
        radioVal: "2"// 现在选择 | 签约选择
      });
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
    },
    // 向左移动
    moveLeft(index) {
      let arr = [...this.flowStep.steps];
      let temp = this.flowStep.steps[index - 1];
      arr[index - 1] = arr[index];
      arr[index] = temp;
      this.flowStep.steps = arr;
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
    },
    //向右移动
    moveRight(index) {
      let arr = [...this.flowStep.steps];
      let temp = this.flowStep.steps[index + 1];
      arr[index + 1] = arr[index];
      arr[index] = temp;
      this.flowStep.steps = arr;
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
    },
    //删除步骤
    deleteBtn(item, index) {
      if (item.operate === "CARBON_COPY") {
        this.flowStep.carbonCopyList.splice(index, 1);
      } else {
        this.flowStep.steps.splice(index, 1);
      }
      this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
    },
    //过滤公章签署和手写签署
    filterSign() {
      let list = this.flowStep.steps
        .filter((it) => it.operate == "SIGN");
      list.forEach((item, index) => {
          item.signIndex = index;
          if (list.length > 1) {
            item.stepName = `个人签署方${item.signIndex + 1}`;
          } else {
            item.stepName = `个人签署方`
          }
        });
    },
    //填充域
    // getFillData(val) {
    //   this.$set(this.flowStep.steps[this.activeName], "filedList", val);
    //   this.$store.commit("contractManageStore/SET_FLOWSTEP", this.flowStep);
    // },
    //判断填充域名称唯一性
    // checkName(val) {
    //   let arr = [];
    //   this.flowStep.steps.map((item) => {
    //     if (item.filedList) {
    //       item.filedList.map((i) => {
    //         arr.push(i.fieldName);
    //       });
    //     }
    //   });
    //   let filter = arr.filter((item) => item === val);
    //   if (filter.length >= 2) {
    //     return false;
    //   } else {
    //     return true;
    //   }
    // },
  },
};
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/helpers";
.user-name {
  box-sizing: border-box;
  padding: 0 30px 0 16px;
  display: inline-block;
  background: #F1F1F1;
  font-size: 14px;
  color: #070F29;
  border-radius: 16px;
  height: 32px;
  line-height: 32px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  position: relative;
  .el-icon-close {
    position: absolute;
    top: 10px;
    right: 6px;
  }
}
.title {
  font-size: 18px;
  margin: 20px 0;
  display: flex;
  align-items: center;
}
.title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 14px;
  background-color: $mainColor;
  border-radius: 3px;
  margin-right: 8px;
}
.config-template-process {
  margin-top: 20px;
  background: #fff;
  padding: 0 20px 0 0;
  .process-name {
    font-size: 16px;
  }
  .process-content {
    .title-content {
      color: #333;
      font-size: 18px;
    }
  }
  .process-step {
    background: #fff;
    margin: 20px 0;
    padding: 20px 9px;
    border: 1px solid #e6e6e6;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    .addBtn {
      align-self: center;
      font-size: 48px;
      cursor: pointer;
      color: #9EA5BD;
    }
    h3 {
      height: 40px;
      line-height: 40px;
      background: #fff5da;
      position: relative;
      border-radius: 4px 4px 0 0;
      .remove-icon {
        font-size: 18px;
        position: absolute;
        right: -9px;
        top: -9px;
        cursor: pointer;
        color: #cfcfcf;
      }
    }
    .move-icon {
      background: #fff;
      font-size: 20px;
      overflow: hidden;
      padding: 5px;
      border: 1px solid #f4f4f4;
      border-top: none;
      color: #e5e5e5;
      &:hover {
        color: $mainColor;
      }
      .move-left {
        float: left;
        cursor: pointer;
      }
      .move-right {
        float: right;
        cursor: pointer;
      }
    }
    .auditing {
      width: 240px;
      float: left;
      padding: 0 10px;
      text-align: center;
      margin-bottom: 20px;
      border-radius: 4px;
      .choosePerson {
        p {
          padding: 0 20px;
          box-sizing: border-box;
          background: #fff;
          border: 1px solid #f4f4f4;
          cursor: not-allowed;
        }
        p.chooseDirection {
          height: 30px;
          line-height: 30px;
        }
        .chooseAgin {
          min-height: 100px;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          align-items: center;
          .changeStaff {
            width: 100%;
            height: 40px;
            color: $mainColor;
            line-height: 40px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            .el-radio {
              margin: 0 5px 0 0!important;
              font-weight: 400 !important;
            }
          }
          .choose-more-person {
            width: 100%;
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            .attention{
              color: #6A6F7F;
            }
          }
          .choose-copy-person {
            width: 100%;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px 0;
            .iconfont {
              margin-right: 5px;
            }
          }
        }
      }
      .chooseNewPerson {
        p {
          height: 84px;
          line-height: 84px;
        }
      }
    }
    .el-dropdown {
      align-self: center;
    }
  }
  .el-icon-info {
    margin-left: 5px;
  }
}
</style>
