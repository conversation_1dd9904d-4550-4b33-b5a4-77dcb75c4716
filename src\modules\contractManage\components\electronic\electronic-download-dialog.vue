<template>
  <el-dialog
    title="批量下载提示"
    class="app-dialog"
    :visible.sync="isShow"
    :closeOnClickModal="false"
    :closeOnPressEscape="false"
    :width="width"
    top="30vh"
  >
    <div class="close" @click="handleCancelClick">
      <img src="@/assets/images/close.png" alt="" />
    </div>
    <div class="dialog-content">
      <p class="title">
        <i class="iconfont-per icon-jingshi-qiangtishi1"></i>
        <span v-if="status.downLoad == 'NOT'">无可下载合同</span>
        <span v-else
          >您有 {{ status.m }} 份已签署合同可下载，确认要下载吗？</span
        >
      </p>

      <p class="content">
        <span v-if="status.downLoad == 'PORTION'"
          >您选择的文件有<span class="num"> {{ status.n }} </span
          >份不可下载，请检查是否全部签署方都已完成签署</span
        >
        <span v-else>仅全部签署方都完成签署后，可以进行下载</span>
      </p>
    </div>
    <div slot="footer" class="dialog-footer">
      <slot name="footer-btn" />
      <el-button
        v-if="status.downLoad !== 'NOT'"
        class="cancel-btn"
        @click="handleCancelClick"
        >取消</el-button
      >

      <el-button
        :loading="loading"
        type="primary"
        @click="handleConfirmClick(status.downLoad)"
      >
        {{ status.downLoad == "NOT" ? "我知道了" : "下载" }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  apiExportContractCheckdata,
  apiExportContract,
  apiExportContractCancel,
} from "../../store/api";

export default {
  name: "app-dialog",
  data() {
    return {
      isShow: false,
      width: "420px",
      status: {
        downLoad: "ALL", //ALL 全部下载 PORTION 部分 //NOT 不可下载
        m: 0, //可下载数量
        n: 0, //不可下载数量
      },
      ids: [],
      loading: false,
      fileKey: "",
    };
  },
  watch: {
    isShow(val) {
      if (val) this.handleCheckdata();
    },
  },

  methods: {
    async handleCheckdata() {
      if (!this.ids.length) return;
      const {
        data: { fileKey },
        success,
      } = await apiExportContractCheckdata({
        contractIds: this.ids,
      });
      if (success) {
        this.fileKey = fileKey;
      }
    },

    async handleConfirmClick(status) {
      if (status == "NOT") {
        this.isShow = false;
      } else {
        this.loading = true;
        try {
          const { success } = await apiExportContract({
            fileKey: this.fileKey,
          });
        } finally {
          this.loading = false;
          this.isShow = false;
        }
      }
    },

    async handleCancelClick() {
      if (this.fileKey) {
        await apiExportContractCancel({ fileKey: this.fileKey });
      }
      this.isShow = false;
      setTimeout(() => {
        this.status = {
          downLoad: "",
          m: 0,
          n: 0,
        };
      }, 500);
    },
  },
};
</script>

<style lang="scss" scoped>
.app-dialog {
  ::v-deep {
    .el-dialog__body {
      flex: 1;
      overflow: auto;
    }
  }

  .close {
    position: absolute;
    right: 6px;
    top: 2px;
    width: 48px;
    height: 40px;
    // border: 1px solid red;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
      width: 16px;
      height: 16px;
      display: block;
    }
  }

  .dialog-content {
    .title {
      font-weight: 500;
      font-size: 14px;
      color: #24262a;
      .icon-jingshi-qiangtishi1 {
        color: #e59900;
        margin-right: 8px;
        position: relative;
        top: 2px;
        font-size: 18px;
      }
    }
    .content {
      font-size: 14px;
      color: #777c94;
      margin: 8px 0 0 30px;
      .num {
        color: #feab05;
        font-weight: 500;
      }
    }
  }

  ::v-deep .el-dialog__headerbtn {
    display: none;
  }

  ::v-deep .el-dialog__title {
    -moz-user-select: none;
    -o-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #24262a;
    margin-left: 24px;
  }
  ::v-deep .el-dialog__header {
    position: relative;
    margin: 0;
    padding: 10px 0;
    border-bottom: 1px solid #eef0f4;
  }
  ::v-deep .el-dialog__headerbtn {
    width: 60px;
    position: absolute;
    top: 2px;
    right: 0;
    bottom: 0;
  }
  ::v-deep .el-button {
    width: 92px;
    padding: 10px 20px;
    border-radius: 8px;
  }
}
</style>
