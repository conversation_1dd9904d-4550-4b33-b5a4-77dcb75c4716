<template>
  <el-dialog
    title="批量签署提示"
    class="app-dialog"
    :visible.sync="isShow"
    :closeOnClickModal="false"
    :closeOnPressEscape="false"
    :width="sign.status.includes('ONE') ? '420px' : '520px'"
    top="30vh"
  >
    <div class="close" @click="handleCancelClick">
      <img src="@/assets/images/close.png" alt="" />
    </div>
    <div class="dialog-content">
      <p class="title">
        <i class="iconfont-per icon-jingshi-qiangtishi1"></i> 您有 {{ sign.m }}
        份文件可签署，确认要批量签署吗？
      </p>
      <p v-if="sign.n > 0" class="content">
        您选择的文件有<span class="num"> {{ sign.n }} </span
        >份无法签署，请检查您是否是当前签署人，并且文件为签署中状态
      </p>
      <div v-if="!sign.status.includes('ONE')">
        <p class="tips">
          温馨提示：<br />
          1.请确保您已阅读并知晓所选签署任务的全部内容<br />
          2.点击签署后，系统会在文档的指定位置预设选中签章<br />
          3.一次意愿验证将完成所选签署任务的全部签署，请谨慎操作
        </p>
        <p class="checked">
          <el-checkbox v-model="checked" /><span
            >我已阅读并知晓所选文档的的内容</span
          >
        </p>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <slot name="footer-btn" />
      <el-button class="cancel-btn" @click="handleCancelClick">取消</el-button>
      <el-button
        type="primary"
        :disabled="!sign.status.includes('ONE') && !checked"
        @click="handleConfirmClick"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
import { apiStartSign } from "../../store/api";

export default {
  name: "app-dialog",

  data() {
    return {
      isShow: false,
      checked: false,
      sign: {
        status: "ALL", //ALL-ONE 全部1文件 ALL全部 PORTION-ONE 部分1文件  PORTION 部分
        m: 0, //可签署数量
        n: 0, //不可签署数量
      },
      ids: [],
    };
  },

  methods: {
    //签署
    async handleSign(data) {
      let params = {
        clientSource: "PC",
        contractId: data.id,
        silenceSignYn: data.silenceSignYn,
      };
      let res = await apiStartSign(params);
      if (res.success) {
        window.open(`${res.data.pcUrl}?token=${res.data.token}`);
        this.handleCancelClick();
      }
    },

    handleConfirmClick() {
      if (this.ids.length == 1) {
        this.handleSign(this.ids[0]);
      } else {
        this.$store.commit("contractManageStore/SET_BATCHSIGN", this.ids);
        this.$router.push("/contract-manage/batch-sign");
      }
    },
    handleCancelClick() {
      this.isShow = false;
      setTimeout(() => {
        this.sign = {
          status: "",
          m: 0,
          n: 0,
        };
      }, 500);
    },
  },
};
</script>

<style lang="scss" scoped>
.app-dialog {
  ::v-deep {
    .el-dialog__body {
      flex: 1;
      overflow: auto;
    }
  }

  .close {
    position: absolute;
    right: 6px;
    top: 2px;
    width: 48px;
    height: 40px;
    // border: 1px solid red;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
      width: 16px;
      height: 16px;
      display: block;
    }
  }

  .dialog-content {
    .title {
      font-weight: 500;
      font-size: 14px;
      color: #24262a;
      .icon-jingshi-qiangtishi1 {
        color: #e59900;
        margin-right: 8px;
        position: relative;
        top: 2px;
        font-size: 18px;
      }
    }
    .content {
      font-size: 14px;
      color: #777c94;
      margin: 8px 0 0 30px;
      .num {
        color: #feab05;
        font-weight: 500;
      }
    }
    .tips {
      margin: 8px 0 0 30px;
      background: #f8f8f8;
      border-radius: 12px;
      padding: 16px;
      box-sizing: border-box;
      font-size: 14px;
      color: #777c94;
      line-height: 22px;
    }
    .checked {
      margin: 16px 0 0 30px;
      font-size: 14px;
      color: #46485a;
      span {
        margin-left: 8px;
      }
    }
  }

  ::v-deep .el-dialog__headerbtn {
    display: none;
  }

  ::v-deep .el-dialog__title {
    -moz-user-select: none;
    -o-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #24262a;
    margin-left: 24px;
  }
  ::v-deep .el-dialog__header {
    position: relative;
    margin: 0;
    padding: 10px 0;
    border-bottom: 1px solid #eef0f4;
  }
  ::v-deep .el-dialog__headerbtn {
    width: 60px;
    position: absolute;
    top: 2px;
    right: 0;
    bottom: 0;
  }
  ::v-deep .el-button {
    width: 92px;
    padding: 10px 20px;
    border-radius: 8px;
  }
}
</style>
