<template>
  <div class="fill-field">
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      :header-cell-style="{ background: '#F1F1F1' }"
      class="fill-field-table"
    >
      <el-table-column prop="fieldName" label="名称">
        <template slot-scope="scope">
          <el-input
            v-if="scope.row.isEdit"
            v-model="scope.row.fieldName"
            placeholder="请输入名称"
          ></el-input>
          <span v-else class="item-name">
            {{ scope.row.fieldName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="relationCode" label="关联信息项">
        <template slot="header">
          关联信息项
          <el-tooltip
            content="请先配置签署人的文本控件，若此项为空，在设置签署位置时页面左侧将无自定义控件可供拖拽"
            placement="top"
          >
            <i class="el-icon-info"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-input
            v-if="scope.row.isEdit"
            v-model="scope.row.relationName"
            placeholder="请选择关联信息项"
            @focus="handleShowRelation(scope.$index)"
          ></el-input>
          <span v-else>{{ scope.row.relationName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="100" v-if="isShowAdd">
        <template slot-scope="scope">
          <div v-if="scope.row.isEdit">
            <el-button size="small" @click="cancel(scope.row, scope.$index)"
              >取消</el-button
            >
            <el-button
              type="primary"
              size="small"
              @click="save(scope.row, scope.$index)"
              >确定</el-button
            >
          </div>
          <div v-else>
            <span class="table-name" @click="edit(scope.row, scope.$index)">
              编辑
            </span>
            <span
              class="table-name"
              @click="handleDelete(scope.$index)"
              style="margin-left: 10px;"
            >
              删除
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="add-container" v-show="isShowAdd" @click="addItem">
      <i class="iconfont iconadd"></i>
      新增
    </div>
    <!--选择关联信息项-->
    <el-dialog
      title="选择关联信息项"
      :visible.sync="isShowRelation"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
    >
      <div
        v-for="(item, index) in relationList2"
        :key="index"
        :style="{ marginTop: index != 0 ? '10px' : '' }"
      >
        <div class="group-title">
          <b v-if="item.models.length > 0">{{ item.groupName }}</b>
          <i
            :class="item.isShowAll ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
            @click="handleChangeShow(item, index)"
          ></i>
        </div>

        <div
          style="margin:10px 0px; border-bottom:1px solid #E5E5E5"
          v-if="item.models.length > 0"
        ></div>
        <el-radio-group
          v-model="selectRelation"
          v-show="item.models.length > 0 && item.isShowAll"
        >
          <el-radio
            v-for="(it, id) in item.models"
            :key="id"
            :label="it"
            :value="it"
            >{{ it.fieldName }}</el-radio
          >
        </el-radio-group>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="handleSaveRelation">确定</el-button>
        <el-button @click="isShowRelation = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";

export default {
  props: {
    tableData: {
      type: Array,
      default: []
    },
    operateType: {
      type: String
    },
    isShowAdd: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      fieldName: "",
      isEditFlag: false,
      isAddFlag: false,
      isShowRelation: false,
      selectRelation: "",
      currentIndex: null,
      relationList2: []
    };
  },
  computed: {
    ...mapState("contractManageStore", {
      relationList: "relationList"
    })
  },
  watch: {
    tableData(val) {
      this.$emit("getFillData", val);
    }
  },
  components: {},
  mounted() {},
  methods: {
    //编辑
    edit(row, index) {
      if (this.isEditFlag || this.isAddFlag) {
        this.$message.warning("请先保存当前编辑项");
        return;
      }
      this.isEditFlag = true;
      this.fieldName = row.fieldName;
      if (!row.isEdit) {
        row.isEdit = true;
        this.$set(this.tableData, index, row);
      }
    },
    //保存
    save(row, index) {
      if (row.fieldName === "") {
        this.$message.warning("名称不能为空");
        return;
      }
      if (row.fieldName.length > 10) {
        this.$message.warning("最长可输入10个字符");
        return;
      }
      let checkSuccess = this.$parent.checkName(row.fieldName);
      if (!checkSuccess) {
        this.$message.warning("该名称已存在");
        return;
      }
      row.isEdit = false;
      this.$set(this.tableData, index, row);
      this.isEditFlag = false;
      this.isAddFlag = false;
    },
    //取消
    cancel(row, index) {
      if (this.isEditFlag) {
        row.isEdit = false;
        row.fieldName = this.fieldName;
        this.$set(this.tableData, index, row);
        this.isEditFlag = false;
        this.isAddFlag = false;
      } else {
        this.tableData.splice(index, 1);
        this.isEditFlag = false;
        this.isAddFlag = false;
      }
    },
    //新增
    addItem() {
      if (this.isEditFlag || this.isAddFlag) {
        this.$message.warning("请先保存当前编辑项");
        return;
      }
      this.isAddFlag = true;
      this.tableData.push({
        fieldName: "",
        relationCode: "",
        relationGroup: "",
        relationName: "",
        isEdit: true
      });
    },
    //删除
    handleDelete(index) {
      this.tableData.splice(index, 1);
    },
    //展示关联项弹窗
    handleShowRelation(index) {
      if (this.operateType === "SEAL") {
        this.relationList2 = [
          {
            groupName: "公司信息",
            models: [
              {
                fieldCode: "taxSubName",
                fieldName: "法人实体名称",
                templateGroupCode: ""
              },
              {
                fieldCode: "taxPayerNo",
                fieldName: "纳税人识别号",
                templateGroupCode: ""
              },
              {
                fieldCode: "contactName",
                fieldName: "联系人姓名",
                templateGroupCode: ""
              },
              {
                fieldCode: "contactPhone",
                fieldName: "联系人手机号",
                templateGroupCode: ""
              },
              {
                fieldCode: "legalName",
                fieldName: "法人姓名",
                templateGroupCode: ""
              },
              {
                fieldCode: "idType",
                fieldName: "证件类型",
                templateGroupCode: ""
              },
              {
                fieldCode: "idNo",
                fieldName: "证件号码",
                templateGroupCode: ""
              }
            ]
          }
        ];
      } else {
        this.relationList2 = this.relationList;
      }
      this.relationList2.map(item => {
        item.isShowAll = true;
      });
      this.currentIndex = index;
      this.selectRelation = "";
      this.isShowRelation = true;
    },
    //保存关联项
    handleSaveRelation() {
      let index = this.currentIndex;
      this.tableData[index].relationName = this.selectRelation.fieldName;
      this.tableData[index].relationCode = this.selectRelation.fieldCode;
      this.tableData[
        index
      ].relationGroup = this.selectRelation.templateGroupCode;
      this.isShowRelation = false;
    },
    handleChangeShow(item, index) {
      item.isShowAll = !item.isShowAll;
      this.$set(this.relationList2, index, item);
    }
  }
};
</script>
<style lang="scss" scoped>
.fill-field {
  .el-table {
    .item-name {
      display: inline-block;
      margin: 0 10px;
    }
    /deep/ .el-button--primary {
      font-size: 12px;
    }
  }
  .add-container {
    border: 1px dashed #4F71FF;
    color: #4F71FF;
    font-size: 16px;
    text-align: center;
    padding: 20px 0;
    margin-top: 20px;
    cursor: pointer;
  }
  .table-name {
    color: #4F71FF;
    cursor: pointer;
  }
  /deep/ .el-table th:first-child,
  .el-table td:first-child {
    padding-left: 20px;
    text-align: left !important;
  }
  /deep/ .el-table td:first-child {
    padding-left: 20px;
    text-align: left !important;
  }
  .el-input {
    width: 250px;
  }
}
.group-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  i {
    cursor: pointer;
  }
}
</style>
