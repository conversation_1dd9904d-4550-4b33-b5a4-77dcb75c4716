<template>
  <div>
    <el-drawer
      :title="title"
      :before-close="handleClose"
      :visible.sync="dialog"
      custom-class="demo-drawer"
      ref="drawer"
      :wrapperClosable="false"
      size="500px"
    >
      <div class="demo-drawer__content">
        <header class="header main-title">
          <el-row type="flex">
            <el-col :span="12">
              <span>{{ title }}</span>
            </el-col>
            <el-col :span="12" style="text-align: right">
              <i class="el-icon-close" @click="dialog = false"></i>
            </el-col>
          </el-row>
        </header>
        <el-form :model="form" label-width="120px" :rules="rules" ref="form">
          <el-form-item label="员工" prop="empRecordId">
            <el-select
              v-if="operateType !== 'EDIT' && form.signType === 'NEW_SIGN'"
              v-model="form.empRecordId"
              filterable
              clearable
              remote
              placeholder="请输入员工姓名"
              :remote-method="remoteMethod"
              :loading="loading2"
              @change="changeEmp"
            >
              <el-option
                v-for="item in compEmpOptions"
                :key="item.empRecordId"
                :label="item.empName + '(' + item.mobile + ')'"
                :value="item.empRecordId"
              >
                <div class="emp-item">
                  <p>
                    <span class="empName">{{ item.empName }}</span>
                    <span>({{ item.mobile }})</span>
                  </p>
                  <span class="company">{{ item.taxSubName }}</span>
                </div>
              </el-option>
            </el-select>
            <el-input v-model="form.empName" v-else :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="合同编号" prop="contractNo">
            <el-input
              placeholder="请输入合同编号"
              v-model="form.contractNo"
              clearable
              @change="handleCheck"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="合同主体单位" prop="contractSubId">
            <el-select v-model="form.contractSubId" placeholder="请选择">
              <el-option
                v-for="(item, index) in contractSubList"
                :label="item.contractName"
                :value="item.contractSubId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="合同类型" prop="contractType">
            <el-select
              v-model="form.contractType"
              placeholder="请选择"
              @change="changeContractType"
              clearable
            >
              <el-option
                v-for="(item, index) in contractTypeList"
                :label="item.optionEnumName"
                :value="item.optionEnumCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="合同期限"
            prop="contractTerm"
            v-show="isShowEndDate"
            :rules="{
              required: isShowEndDate,
              message: '请填写合同期限',
              trigger: 'blur',
            }"
          >
            <el-input
              placeholder="请输入合同期限"
              v-model="form.contractTerm"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            label="合同期限单位"
            prop="contractTermUnit"
            v-show="isShowEndDate"
            :rules="{
              required: isShowEndDate,
              message: '请填写合同期限单位',
              trigger: 'blur',
            }"
          >
            <el-select
              v-model="form.contractTermUnit"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(item, index) in contractTermUnitList"
                :label="item.label"
                :value="item.value"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="合同开始日期" prop="contractStartDate">
            <el-date-picker
              v-model="form.contractStartDate"
              type="date"
              placeholder="请选择"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :disabled="form.signType === 'CONTINUE_SIGN'"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            label="合同结束日期"
            prop="contractEndDate"
            v-show="isShowEndDate"
            :rules="[
              {
                required: isShowEndDate,
                message: '请选择合同结束日期',
                trigger: 'change',
              },
              {
                validator: validateContractDate,
                trigger: 'change',
              },
              {
                validator: validateContractSignDate,
                trigger: 'change',
              },
            ]"
          >
            <el-date-picker
              v-model="form.contractEndDate"
              type="date"
              placeholder="请选择"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="合同签订日期" prop="contractSignDate">
            <el-date-picker
              v-model="form.contractSignDate"
              type="date"
              placeholder="请选择"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="合同状态" prop="contractStatus">
            <el-select
              v-model="form.contractStatus"
              placeholder="请选择"
              :disabled="true"
              clearable
            >
              <el-option
                v-for="(item, index) in contractStatusList"
                :label="item.label"
                :value="item.value"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="签订类型" prop="signType">
            <el-select
              v-model="form.signType"
              placeholder="请选择"
              :disabled="true"
              clearable
            >
              <el-option
                v-for="(item, index) in signTypeList"
                :label="item.label"
                :value="item.value"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="续签次数" prop="renewalCount">
            <el-input
              placeholder="请输入"
              v-model="form.renewalCount"
              clearable
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="签约状态" prop="contractSignStatus">
            <el-select
              v-model="form.contractSignStatus"
              placeholder="请选择"
              :disabled="true"
              clearable
            >
              <el-option
                v-for="(item, index) in contractSignStatusList"
                :label="item.label"
                :value="item.value"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="合同备注" prop="signRemark">
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="form.signRemark"
              maxlength="200"
              show-word-limit
              style="width: 220px"
              :rows="4"
            ></el-input>
          </el-form-item>
        </el-form>

        <div class="demo-drawer__footer">
          <el-button @click="cancelForm">取 消</el-button>
          <el-button type="primary" @click="handleSave" v-prevent-re-click>
            保存
          </el-button>
          <el-button type="primary" @click="handleSign" :loading="loading">
            {{ loading ? "签约中 ..." : "发起签约" }}
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapState } from "vuex";
import * as constData from "@/utils/constData";
import {
  apiContractNewSign,
  apiContractContinueSign,
  apiContractUpdateSign,
  apiContractEditSign,
  apiCheckContractNo,
  apiGetALLEmp,
} from "../store/api";
export default {
  props: {
    title: {
      type: String,
    },
    contractTypeList: {
      type: Array,
      default: [],
    },
  },
  computed: {
    ...mapState("contractManageStore", {
      contractSubList: "contractSubList",
    }),
    ...mapState({
      productEdition: (state) => state.productEdition,
    }),
    nowDate() {
      let date = new Date();
      let year = date.getFullYear();
      let month =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      let day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return year + "-" + month + "-" + day;
    },
    isShowEndDate() {
      if (
        this.form.contractType === "NON_FIXED_TERM" ||
        this.form.contractType == "CERTAIN_TASK_TERM"
      ) {
        return false;
      } else {
        return true;
      }
    },
  },
  watch: {
    "form.contractTerm"() {
      this.getContractEndDate();
    },
    "form.contractTermUnit"() {
      this.getContractEndDate();
    },
    "form.contractStartDate"() {
      this.getContractEndDate();
      this.getContractStatus();
      //合同签订日期默认为合同开始日期
      this.form.contractSignDate = this.form.contractStartDate;
    },
    "form.contractEndDate"() {
      this.getContractStatus();
    },
  },
  data() {
    var validateContractDate = (rule, value, callback) => {
      if (this.form.contractEndDate && this.form.contractStartDate) {
        this.$refs.form.clearValidate(["contractStartDate", "contractEndDate"]);
        if (this.form.contractEndDate < this.form.contractStartDate) {
          callback(new Error("合同结束日期须晚于合同开始日期"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    var validateContractSignDate = (rule, value, callback) => {
      if (this.form.contractSignDate && this.form.contractEndDate) {
        this.$refs.form.clearValidate(["contractSignDate", "contractEndDate"]);
        if (this.form.contractEndDate < this.form.contractSignDate) {
          callback(new Error("签订日期不能晚于结束日期"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      loading: false,
      loading2: false,
      form: {
        id: null,
        empName: "", //员工姓名
        compEmpId: "", //员工id
        empRecordId: "", //用工记录
        contractNo: "", //合同编号
        contractSubId: "", //合同主体单位
        contractType: "", //合同类型
        contractTerm: "", //合同期限
        contractTermUnit: "", //合同期限单位
        contractStartDate: "", //合同开始时间
        contractEndDate: "", // 合同结束时间
        contractSignDate: "", //合同签订日期
        contractStatus: "", //合同状态
        signType: "", //签订类型
        renewalCount: "", //续签次数
        contractSignStatus: "", //签约状态
        signRemark: "", //合同备注
      },
      //表单验证规则
      rules: {
        empRecordId: {
          required: true,
          message: "请选择员工",
          trigger: "change",
        },
        contractSubId: {
          required: true,
          message: "请选择合同主体单位",
          trigger: "change",
        },
        contractType: {
          required: true,
          message: "请选择合同类型",
          trigger: "change",
        },
        contractStartDate: [
          {
            required: true,
            message: "请选择合同开始日期",
            trigger: "change",
          },
          {
            validator: validateContractDate,
            trigger: "change",
          },
        ],
        contractSignDate: [
          {
            validator: validateContractSignDate,
            trigger: "change",
          },
        ],
      },
      validateContractDate: validateContractDate,
      validateContractSignDate: validateContractSignDate,
      contractStatusList: constData.contractStatusList,
      contractSignStatusList: constData.contractSignStatusList,
      signTypeList: constData.signTypeList,
      contractTermUnitList: constData.contractTermUnitList,
      compEmpOptions: [], //筛选后的员工列表
      dialog: false,
      operateType: "",
      contractData: null,
    };
  },
  created() {},
  methods: {
    show(status, data) {
      this.operateType = status;
      this.form.contractSignStatus = "WATING_SIGN";
      for (let key in this.form) {
        if (!["signType", "contractSignStatus", "renewalCount"].includes(key)) {
          this.form[key] = data ? data[key] : "";
        }
      }
      //对编辑里员工处理
      if (status === "EDIT") {
        this.form.signType = data.signType;
      } else {
        this.form.signType = status;
      }
      this.form.contractSubId = this.form.contractSubId
        ? Number(this.form.contractSubId)
        : null;
      //如果是新签，续签次数为1
      if (status === "NEW_SIGN") {
        this.form.renewalCount = "0";
      } else if (status === "CONTINUE_SIGN") {
        //如果是续签，合同开始日期默认上一条合同结束日期+1，续签次数+1
        this.form.renewalCount = String(Number(data.renewalCount) + 1);
        let { contractEndDate } = this.form;
        let date = new Date(contractEndDate);
        if (contractEndDate) {
          date.setDate(date.getDate() + 1);
          let y = date.getFullYear();
          let m = date.getMonth() + 1;
          m = m < 10 ? "0" + m : m;
          let d = date.getDate();
          d = d < 10 ? "0" + d : d;
          this.form.contractStartDate = y + "-" + m + "-" + d;
        }
      } else {
        this.form.renewalCount = data.renewalCount;
      }
      this.getContractEndDate();
      this.dialog = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    async remoteMethod(query) {
      if (query !== "") {
        this.loading2 = true;
        let res = await apiGetALLEmp({ key: query });
        if (res.success) {
          this.compEmpOptions = res.data;
        }
        this.loading2 = false;
      } else {
        this.compEmpOptions = [];
      }
    },
    //保存
    async handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.saveContract();
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    //请求保存接口
    async saveContract() {
      let res;
      let operateType = this.operateType;
      if (operateType === "NEW_SIGN") {
        res = await apiContractNewSign(this.form);
      } else if (operateType === "CONTINUE_SIGN") {
        res = await apiContractContinueSign(this.form);
      } else if (operateType === "UPDATE_SIGN") {
        res = await apiContractUpdateSign(this.form);
      } else if (operateType === "EDIT") {
        res = await apiContractEditSign(this.form);
      }
      if (res.success) {
        this.$message.success("操作成功");
        this.dialog = false;
        this.$emit("refresh");
      }
    },
    //发起签约
    async handleSign() {
      if (!this.productEdition.contract) {
        this.$message.warning("请联系客户经理或拨打客服热线开通服务");
        return;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          let operateType = this.operateType;
          if (operateType === "NEW_SIGN") {
            apiContractNewSign(this.form).then((res) => {
              if (res.success && res.data) {
                this.contractData = res.data;
                this.goSignPage();
              }
            });
          } else if (operateType === "CONTINUE_SIGN") {
            apiContractContinueSign(this.form).then((res) => {
              if (res.success && res.data) {
                this.contractData = res.data;
                this.goSignPage();
              }
            });
          } else if (operateType === "UPDATE_SIGN") {
            apiContractUpdateSign(this.form).then((res) => {
              if (res.success && res.data) {
                this.contractData = res.data;
                this.goSignPage();
              }
            });
          } else if (operateType === "EDIT") {
            apiContractEditSign(this.form).then((res) => {
              if (res.success && res.data) {
                this.contractData = res.data;
                this.goSignPage();
              }
            });
          }
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    goSignPage() {
      let data = Object.assign(this.form, this.contractData);
      this.$store.commit("contractManageStore/SET_CHOOSERECORDDATA", [data]);
      this.$store.commit("contractManageStore/SET_CHOOSESEALSTAFFDATA", null);
      this.$store.commit("contractManageStore/SET_CHOOSETEMPLATEDATA", null);
      this.$router.push({
        path: "/contract-manage/initiate-signing",
        query: { isBatchSign: true },
      });
    },
    //校验合同编号是否唯一
    async handleCheck() {
      let id = this.operateType === "EDIT" ? this.form.id : null;
      let { contractNo } = this.form;
      await apiCheckContractNo({ id, contractNo });
    },
    handleClose() {
      this.$refs.form.resetFields();
    },
    cancelForm() {
      this.loading = false;
      this.dialog = false;
    },
    //根据开始日期和合同期限（单位）自动计算合同结束日期
    getContractEndDate() {
      let { contractTerm, contractTermUnit, contractStartDate } = this.form;
      let date = new Date(contractStartDate);
      if (contractStartDate && contractTerm && contractTermUnit) {
        if (contractTermUnit === "YEAR") {
          date.setYear(date.getFullYear() + Number(contractTerm));
        } else if (contractTermUnit === "MONTH") {
          date.setMonth(date.getMonth() + Number(contractTerm));
        } else if (contractTermUnit === "DAY") {
          date.setDate(date.getDate() + Number(contractTerm));
        }
        date.setDate(date.getDate() - 1);
        let y = date.getFullYear();
        let m = date.getMonth() + 1;
        m = m < 10 ? "0" + m : m;
        let d = date.getDate();
        d = d < 10 ? "0" + d : d;
        this.form.contractEndDate = y + "-" + m + "-" + d;
      }
    },
    //切换合同类型
    changeContractType() {
      if (
        this.form.contractType === "NON_FIXED_TERM" ||
        this.form.contractType === "CERTAIN_TASK_TERM"
      ) {
        this.form.contractEndDate = "";
        this.form.contractTerm = "";
        this.form.contractTermUnit = "";
      }
    },
    //获取合同状态
    getContractStatus() {
      if (
        this.form.contractEndDate &&
        this.form.contractEndDate < this.nowDate
      ) {
        this.$set(this.form, "contractStatus", "EXPIRE");
      } else {
        if (
          this.form.contractStartDate &&
          this.form.contractStartDate > this.nowDate
        ) {
          this.$set(this.form, "contractStatus", "NOT_EXECUTE");
        } else {
          this.$set(this.form, "contractStatus", "EXECUTE");
        }
      }
    },
    //选择员工
    changeEmp(val) {
      this.compEmpOptions.forEach((item) => {
        if (item.empRecordId === val) {
          this.form.compEmpId = item.compEmpId;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
/deep/ .demo-drawer {
  overflow: auto;
}
/deep/.el-drawer__header {
  height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
}
.main-title {
  padding-left: 20px;
  font-size: 17px;
  height: 61px;
  line-height: 61px;
  border-bottom: 1px solid #ededed;
  i {
    cursor: pointer;
  }
}
.el-form {
  padding: 10px 50px;
  height: calc(100vh - 143px);
  overflow: auto;
}
.demo-drawer__footer {
  padding: 10px;
  border-top: 1px solid #ededed;
  display: flex;
  justify-content: center;
  position: fixed;
  bottom: 0;
  width: 500px;
}
/deep/ .el-input,
.el-date-editor.el-input {
  width: 220px;
}

.emp-item {
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > p {
    display: flex;
    align-items: center;
  }
  .empName {
    display: inline-block;
    max-width: 70px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .company {
    color: #8492a6;
  }
}
</style>
