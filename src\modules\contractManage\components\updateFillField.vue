<template>
  <div class="fill-field">
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      :header-cell-style="{ background: '#F1F1F1' }"
      class="fill-field-table"
    >
      <el-table-column prop="fieldName" label="名称"> </el-table-column>
      <el-table-column prop="relationCode" label="关联信息项">
        <template slot="header">
          关联信息项
<!--          <el-tooltip-->
<!--            content="请先配置签署人的文本控件，若此项为空，在设置签署位置时页面左侧将无自定义控件可供拖拽"-->
<!--            placement="top"-->
<!--          >-->
<!--            <i class="el-icon-info"></i>-->
<!--          </el-tooltip>-->
        </template>
        <template slot-scope="scope">
          <span>{{ scope.row.relationName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="relationValue"
        label="关联信息项值"
        v-if="!(isBatch && operateType === 'SIGN')"
      >
        <template slot="header">
          关联信息项值
          <el-tooltip content="员工/企业关联信息项数据，可修改" placement="top">
            <i class="el-icon-info"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-input
            clearable
            v-if="scope.row.isEdit"
            v-model="scope.row.relationValue"
            maxlength="100"
            placeholder="请填写关联信息项值"
            @blur="blur(scope.row,scope.$index)"
          ></el-input>
          <span v-else>{{ scope.row.relationValue }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        min-width="100"
        v-if="!(isBatch && operateType === 'SIGN')"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.isEdit">
            <el-button size="small" @click="cancel(scope.row, scope.$index)">
              取消
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="save(scope.row, scope.$index)"
            >
              确定
            </el-button>
          </div>
          <div v-else>
            <span class="table-name" @click="edit(scope.row, scope.$index)">
              编辑
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { mapState } from "vuex";
import fun from "../../../util/fun";

export default {
  props: {
    newTableData: {
      type: Array,
      default: []
    },
    operateType: {
      type: String
    },
    isBatch: {
      type: Boolean
    }
  },
  data() {
    return {
      loading: false,
      isEditFlag: false,
      relationValue: "",
      autoSave: null,
      tableData:[],
      // currentIndex: 0
    };
  },
  computed: {},
  watch: {
    newTableData: {
      handler(val) {
        this.tableData = JSON.parse(JSON.stringify(val))
      },
      deep: true
    }
  },
  components: {},
  mounted() {},
  methods: {
    //编辑
    edit(row, index) {
      if (this.isEditFlag) {
        this.$message.warning("请先保存当前编辑项");
        return;
      }
      this.isEditFlag = true;
      // this.currentIndex = index
      // console.log(this.currentIndex);
      this.relationValue = row.relationValue;

      if (!row.isEdit) {
        row.isEdit = true;
        this.$set(this.tableData, index, row);
      }else{
        this.save(row);
      }

    },
    blur(row, index) {

        // this.$emit("change",JSON.parse(JSON.stringify(this.tableData)))

        // if (!row.isEdit) return;
        // setTimeout(() => {
        //     this.save(row, index)
        // },300)
    },
    //保存
    save(row, index) {
      this.$emit("change",JSON.parse(JSON.stringify(this.tableData)))
      row.isEdit = false;
      this.isEditFlag = false;
    },
    //取消
    cancel(row, index) {
      if (this.isEditFlag) {
        row.isEdit = false;
        row.relationValue = this.relationValue;
        this.$set(this.tableData, index, row);
        this.isEditFlag = false;
      } else {
        this.tableData.splice(index, 1);
        this.isEditFlag = false;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.fill-field {
  .el-table {
    .item-name {
      display: inline-block;
      margin: 0 10px;
    }
    /deep/ .el-button--primary {
      font-size: 12px;
    }
  }
  .table-name {
    color: #4F71FF;
    cursor: pointer;
  }
  /deep/ .el-table th:first-child,
  .el-table td:first-child {
    padding-left: 20px;
    text-align: left !important;
  }
  /deep/ .el-table td:first-child {
    padding-left: 20px;
    text-align: left !important;
  }
  .el-input {
    width: 12vw;
  }
}
</style>
