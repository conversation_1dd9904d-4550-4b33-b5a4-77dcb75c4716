<template>
  <div class="contract-see">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">关闭</span>
          <span class="header-line">|</span>
          <span>发起签约</span>
        </el-col>
      </el-row>
    </header>
    <div class="contract-warp" ref="refContractWarp">
      <el-scrollbar style="height:100%">
        <div class="countPage card-box fixed-top">
          <div class="count"></div>
          <el-button
            size="small"
            type="text"
            @click="pageChange(0)"
            :disabled="currentPdfPage === 1"
          >
            上一页
            <i class="el-icon-arrow-up"></i>
          </el-button>
          <el-button
            size="small"
            type="text"
            @click="pageChange(1)"
            :disabled="currentPdfPage === pdfPictures.length"
          >
            下一页
            <i class="el-icon-arrow-down"></i>
          </el-button>
          <span style="margin: 0 30px;"
            >{{ currentPdfPage }}/{{ pdfPictures.length }} 页</span
          >
          <span style="margin: 0 50px;">
            跳转至
            <el-input
              v-model="tempCurrentPdfPage"
              class="page-jump"
              size="small"
            ></el-input
            >页
            <img
              src="../../assets/images/right.png"
              width="14px"
              @click="handleGotoPage(false)"
              class="right-icon"
            />
          </span>
        </div>
        <div class="movie-info">
          <img
            ref="refPdfImg"
            :src="pdfPictures[currentPdfPage - 1]"
            @click="handlePdfImgClick"
          />
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    pdfPictures: {
      type: Array,
      default: () => {
        return [
          "https://dss1.bdstatic.com/6OF1bjeh1BF3odCf/it/u=2279940428,44292841&fm=190&app=60&f=PNG?w=121&h=75&s=8AB2438534C316F71A30B503030060D1"
        ];
      }
    }
  },
  data() {
    return {
      currentPdfPage: 1,
      tempCurrentPdfPage: "",
      controlListBack: []
    };
  },
  watch: {
    pdfPictures() {
      this.currentPdfPage = 1;
    }
  },
  methods: {
    //pdf翻页
    pageChange(flag) {
      let t = this;
      switch (flag) {
        case 0: {
          t.currentPdfPage = t.currentPdfPage <= 1 ? 1 : --t.currentPdfPage;
          break;
        }
        case 1: {
          t.currentPdfPage =
            t.currentPdfPage >= t.pdfPictures.length
              ? t.pdfPictures.length
              : ++t.currentPdfPage;
          break;
        }
      }
    },
    handleGotoPage(page) {
      const t = this;
      page ? (t.tempCurrentPdfPage = page.toString()) : "";
      const tempNum = parseInt(t.tempCurrentPdfPage);
      if (
        !isNaN(tempNum) &&
        tempNum <= t.pdfPictures.length &&
        tempNum > 0 &&
        t.pdfPictures.length >= 1
      ) {
        t.currentPdfPage = parseInt(t.tempCurrentPdfPage);
      } else {
        this.$message({
          message: "请输入正确的页码",
          type: "warning"
        });
      }
    },
    handlePdfImgClick() {
      const t = this;
      t.controlListBack.forEach(it => (it.isActive = false));
    },
  }
};
</script>
<style lang="scss" scoped>
.header {
  padding: 0 20px;
  font-size: 17px;
  height: 61px;
  border-bottom: 1px solid #ededed;
  line-height: 61px;
  text-align: left;
}
.contract-see {
  text-align: center;
  overflow: hidden;
  .contract-warp {
    // @include widthHeight(800px, 93vh);
    margin: 0px 14px 0 14px;
    overflow: auto;
    .right-icon {
      cursor: pointer;
      display: inline-block;
      margin-left: 20px;
    }
    .countPage {
      text-align: left;
      padding: 8px;
      box-sizing: border-box;
      width: 800px;
      margin-bottom: 4px;
      border: 1px solid #f3f3f3;
      margin-top: 10px;
    }
    .page-jump {
      width: 70px;
      margin: 0 10px 0 10px;
    }

    .movie-info {
      img {
        width: 800px;
        height: auto;
        display: block;
        margin: 10px auto 0 auto;
      }
    }
  }
  .fixed-top {
    // position: fixed;
    width: 800px;
    margin: 0 auto;
    // top: 50px;
    // z-index: 999;
    border: 1px solid #f3f3f3;
    background-color: #fff;
  }
}
</style>
