<template>
  <div class="contract-container def_per_height">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span>合同模板</span>
        </el-col>
      </el-row>
    </header>
    <nav class="nav" style="padding: 0 20px; margin-top: 20px">
      <div>
        <el-button @click="isShowScreening = true">筛选</el-button>
        <el-input
          placeholder="请输入模板名称"
          v-model.trim="searchForm.templateName"
          prefix-icon="iconiconfonticonfontsousuo1 iconfont"
          class="search-input"
          style="margin-left: 10px"
          @keyup.enter.native="handleSearch"
        ></el-input>
      </div>
      <div>
        <el-button
          type="primary"
          @click="handlenewTemplate"
          v-if="
            privilegeVoList.includes('hrContract.conManage.conTemplate.add')
          "
          >新增模板</el-button
        >
      </div>
    </nav>
    <section style="padding: 0 20px; margin-top: 20px" class="table-con">
      <el-table
        :data="tableData"
        v-loading="loading"
        :width="screenWidth"
        @cell-click="cellClick"
        :header-cell-style="{ background: '#F1F1F1' }"
        stripe
        border
      >
        <el-table-column
          prop="templateName"
          label="模板名称"
          min-width="350"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span class="module-name">
              {{ scope.row.templateName }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="templateType" label="模板类型" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.templateType | templateType }}
          </template>
        </el-table-column>

        <el-table-column prop="templateStatus" label="状态" min-width="80">
          <template slot-scope="scope">
            <span
              :class="[
                { green: scope.row.templateStatus == 'ENABLED' },
                { red: scope.row.templateStatus == 'DISABLED' },
              ]"
            >
              {{ scope.row.templateStatus | templateStatus }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="templateUseTimes"
          label="使用次数"
          min-width="80"
        ></el-table-column>

        <el-table-column
          prop="tempRange"
          label="适用范围"
          :show-overflow-tooltip="true"
          min-width="180"
        ></el-table-column>

        <el-table-column
          prop="createdTime"
          label="创建日期"
          min-width="130"
        ></el-table-column>
        <el-table-column
          prop="createrName"
          label="创建人"
          min-width="100"
        ></el-table-column>

        <el-table-column fixed="right" label="操作" width="250">
          <template slot-scope="scope">
            <el-button type="text" @click="show(scope.row)">查看</el-button>
            <el-button
              type="text"
              @click="handleEdit(scope.row)"
              v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.conTemplate.edit'
                )
              "
              >编辑</el-button
            >
            <el-button
              type="text"
              @click="handleStop(scope.row)"
              v-if="scope.row.templateStatus === 'ENABLED'"
              v-show="
                privilegeVoList.includes(
                  'hrContract.conManage.conTemplate.disable'
                )
              "
              >停用</el-button
            >

            <el-button
              v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.conTemplate.enable'
                )
              "
              v-show="scope.row.templateStatus === 'DISABLED'"
              type="text"
              @click="handleStart(scope.row)"
              >启用</el-button
            >
            <el-button
              type="text"
              @click="handleDelete(scope.row)"
              v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.conTemplate.delete'
                )
              "
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </section>

    <footer class="footer" style="padding: 0 20px">
      <div class="block">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-sizes="[20, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </footer>

    <!-- 筛选 -->
    <el-dialog
      title="筛选"
      :visible.sync="isShowScreening"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="700px"
      ref="screenForm"
    >
      <el-form ref="searchForm" :model="searchForm" label-width="100px">
        <el-form-item label="模板类型">
          <el-radio-group v-model="searchForm.templateType">
            <el-radio-button
              v-for="(item, index) in contractTemplateTypeList"
              :label="item.value"
              :key="index"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="模板适用范围">
          <el-select
            v-model="searchForm.tempRange"
            placeholder="请选择公司"
            multiple
            clearable
          >
            <el-option
              v-for="item in employeeSubList"
              :key="item.contractSubId"
              :label="item.contractName"
              :value="item.contractSubId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="使用状态">
          <el-radio-group v-model="searchForm.templateStatus">
            <el-radio-button
              v-for="(item, index) in templateStatusList"
              :label="item.value"
              :key="index"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.createTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="创建人">
          <el-select
            v-model="searchForm.createrUserId"
            filterable
            remote
            clearable
            placeholder="请输入发起人姓名"
            :remote-method="searchSponsor"
            :loading="loading2"
          >
            <el-option
              v-for="item in sponsorList"
              :label="item.name + '(' + item.cellPhone + ')'"
              :value="item.userId"
              :key="item.userId"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="resetSreen">重置</el-button>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";
import * as constData from "@/utils/constData";
import { apiGetExportTemplate } from "../staffManage/store/api";
import { getToken } from "@olading/olading-business-ui";
import {
  apiCanUpdate,
  apiGetContractList,
  apiGetContractSubjectList,
  apiContractSealUserList,
  apiUpdateEmpStatus,
  apiQueryDeptMember,
} from "./store/api";
export default {
  data() {
    return {
      isShowScreening: false,
      screenWidth: document.body.clientWidth, // 屏幕尺寸宽度
      screenHeight: document.body.clientHeight - 270, // 屏幕尺寸高度
      total: 0,
      searchForm: {
        currPage: 1,
        pageSize: 20,
        templateName: "",
        templateType: "",
        templateStatus: "",
        tempRange: [],
        createrUserId: "",
        createTimeStart: "",
        createTimeEnd: "",
      },
      tableData: [],
      contractTemplateTypeList: [{ label: "不限", value: "" }].concat(
        constData.contractTemplateTypeList
      ),
      templateStatusList: [{ label: "不限", value: "" }].concat(
        constData.contractTemplateStatusList
      ),
      employeeSubList: [], //模板适用范围
      sponsorList: [], //发起人列表
      loading: false,
      loading2: false,
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  created() {
    this.getList();
    this.getContractSubjectList();
    this.getRelationList();
  },
  methods: {
    async getList() {
      this.loading = true;
      let res = await apiGetContractList(this.searchForm);
      if (res.success) {
        this.tableData = res.data.records;
        this.total = res.data.total;
      }
      this.loading = false;
    },
    async getContractSubjectList() {
      let res = await apiGetContractSubjectList({ type: "EMPLOYEE" });
      if (res.success) {
        this.employeeSubList = res.data;
        this.$store.commit("contractManageStore/SET_EMPLOYEESUBLIST", res.data);
      }
    },
    async getRelationList() {
      let res = await apiGetExportTemplate({
        exportSource: "CONTRACT_TEMPLATE",
      });
      if (res.success) {
        this.$store.commit("contractManageStore/SET_RELATIONLIST", res.data);
      }
    },
    searchSponsor(query) {
      if (query !== "") {
        this.loading2 = true;
        // apiContractSealUserList({ key: query }).then((res) => {
        apiQueryDeptMember({ name: query }).then((res) => {
          this.loading2 = false;
          if (res.success) {
            this.sponsorList = res.data.deptMemberVoList;
            // this.sponsorList = res.data;
          }
        });
      } else {
        this.sponsorList = [];
      }
    },

    // 单元格点击
    cellClick(row, column) {
      if (column.property === "templateName") {
        this.show(row);
      }
    },

    // 查看
    show(row) {
      window.open(
        `${
          window.env.mapUrl["SALARY_MANAGER"]
        }templateShow?token=${getToken()}&tempId=${
          row.id
        }&templateStatus=${row.templateStatus}`
      );
      // this.$router.push({
      //   path: '/templateShow',
      //   query: {
      //     tempId: row.id
      //   }
      // })
    },

    // 编辑
    async handleEdit(item) {
      const { data } = await apiCanUpdate({
        id: item.id,
      });
      if (data.data) {
        this.$router.push({
          path: "/contract-manage/set-template",
          query: {
            tempId: item.id,
            tempStatus: item.templateStatus
          },
        });
      } else {
        this.$message.warning("使用此模板的合同还未签完，无法更改模板哦");
      }
    },

    //启用
    handleStart(item) {
      this.$confirm(
        `确定启用吗？<p style="color: #999">启用后该模板可用于发起签约</p>`,
        "提示",
        {
          customClass: "special-confirm",
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
          closeOnPressEscape: false,
          dangerouslyUseHTMLString: true,
        }
      ).then(() => {
        apiUpdateEmpStatus({ status: "ENABLED", tempId: item.id }).then(
          (res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.getList();
            }
          }
        );
      });
    },
    //停用
    handleStop(item) {
      this.$confirm(
        `确定停用吗？<p style="color: #999">停用后该模板不可用于发起签约</p>`,
        "提示",
        {
          customClass: "special-confirm",
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
          closeOnPressEscape: false,
          dangerouslyUseHTMLString: true,
        }
      ).then(() => {
        apiUpdateEmpStatus({ status: "DISABLED", tempId: item.id }).then(
          (res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.getList();
            }
          }
        );
      });
    },
    //删除
    handleDelete(item) {
      this.$confirm(
        `确定删除该模板？<p style="color: #999">该操作无法撤回，请您谨慎操作</p>`,
        "提示",
        {
          customClass: "special-confirm",
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
          closeOnPressEscape: false,
          dangerouslyUseHTMLString: true,
        }
      ).then(() => {
        apiUpdateEmpStatus({ status: "DELETED", tempId: item.id }).then(
          (res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.getList();
            }
          }
        );
      });
    },
    //显示页数
    handleSizeChange(val) {
      this.searchForm.pageSize = val;
      this.searchForm.currPage = 1;
      this.getList();
    },
    //翻页
    handleCurrentChange(val) {
      this.searchForm.currPage = val;
      this.getList();
    },
    //查询
    handleSearch() {
      this.searchForm.createTimeStart = this.searchForm.createTime
        ? this.searchForm.createTime[0]
        : "";
      this.searchForm.createTimeEnd = this.searchForm.createTime
        ? this.searchForm.createTime[1]
        : "";
      this.searchForm.currPage = 1;
      this.isShowScreening = false;
      this.getList();
    },
    //筛选重置
    resetSreen() {
      for (let key in this.searchForm) {
        if (!["key", "currPage", "pageSize", "tempRange"].includes(key)) {
          this.searchForm[key] = "";
        }
        this.searchForm.tempRange = [];
      }
      this.handleSearch();
    },
    //新增模板
    handlenewTemplate() {
      this.$store.commit("contractManageStore/SET_CHOOSESEALSTAFFDATA", null);
      this.$store.commit("contractManageStore/SET_CHOOSESIGNSTAFFDATA", []);
      this.$store.commit("contractManageStore/SET_CHOOSECOPYSTAFFDATA", null);
      this.$router.push({
        path: "/contract-manage/set-template",
        query: {
          tempId: null,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../assets/scss/helpers.scss";
.contract-container {
  // overflow: hidden !important;
}
.main-title {
  border-bottom: 1px solid #ededed;
}
.nav {
  display: flex;
  justify-content: space-between;
}
.module-name {
  color: $mainColor;
  cursor: pointer;
}
.el-pagination {
  margin-top: 10px;
}
.footer {
  display: flex;
  justify-content: flex-end;
}
.table-con {
  .el-button {
    padding: 0;
    margin-right: 5px;
  }
  .el-button:last-child {
    margin-right: 0;
  }
}
.red {
  color: #d6342a;
}
.green {
  color: #41bd5a;
}
</style>
