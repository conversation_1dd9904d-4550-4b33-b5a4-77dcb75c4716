<template>
  <div class="create-success">
    <i class="el-icon-success"></i>
    <p>设置成功</p>
    <span>模板签署已设置成功，可使用该模板发起电子签！</span>
  </div>
</template>
<script>
import * as AT from "@/store/actionTypes";
export default {
  components: {},
  created() {
    this.$store.commit(AT.SHOWAPP, false);
  },
  mounted() {
    this.$store.commit(AT.SHOWAPP, false);
  },
  methods: {}
};
</script>
<style lang="scss" scoped>
.create-success {
  width: 100%;
  /*height: calc(100vh - 80px);*/
  text-align: center;
  font-size: 14px;
  color: #6a6f7f;
  i {
    font-size: 72px;
    margin-top: 160px;
  }
  p {
    font-size: 24px;
    color: #070f29;
    margin: 24px 0 16px 0;
  }
}
</style>
