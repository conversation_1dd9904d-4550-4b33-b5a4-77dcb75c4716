<template>
  <div id="laborContract" class="def_per_height">
    <header class="header main-title">
      <section>
        <el-tabs
          class="section-tabs"
          v-model="searchForm.queryItem"
          @tab-click="handleClickTab"
        >
          <template v-for="item in tabs">
            <el-tab-pane
              :key="item.label"
              :label="handleTabName(item.label, item.limitId)"
              :name="item.name"
              :lazy="true"
            >
            </el-tab-pane>
          </template>
        </el-tabs>
      </section>
    </header>

    <nav class="nav" style="padding: 0 20px; margin-top: 20px">
      <div>
        <el-button @click="isShowScreening = true">筛选</el-button>
        <el-input
          v-model.trim="searchForm.key"
          @keyup.enter.native="handleSearch"
          placeholder="请输入文件名称、签署人姓名"
          suffix-icon="iconiconfonticonfontsousuo1 iconfont"
          class="search-input"
          style="margin-left: 10px"
        ></el-input>
      </div>
      <div>
        <el-button
          type="primary"
          @click="handleInitiateSign"
          v-if="
            privilegeVoList.includes('hrContract.conManage.eContract.create')
          "
          >发起签约</el-button
        >
        <el-dropdown style="margin-left: 10px" trigger="click">
          <el-button type="">
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              @click.native="handleBatchRemind"
              v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.eContract.batchRemind'
                ) &&
                (searchForm.queryItem == 'ALL' ||
                  searchForm.queryItem == 'OTHERS_CONTRACT')
              "
              >批量提醒</el-dropdown-item
            >

            <el-dropdown-item
              v-if="
                searchForm.queryItem == 'ALL' ||
                searchForm.queryItem == 'USER_CONTRACT'
              "
              @click.native="handleSignClick"
              >批量签署</el-dropdown-item
            >
            <el-dropdown-item
              v-if="
                searchForm.queryItem == 'ALL' ||
                searchForm.queryItem == 'FINISHED_CONTRACT'
              "
              @click.native="handleDownloadClick"
              >批量下载</el-dropdown-item
            >

            <el-dropdown-item
              @click.native="handleExport"
              v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.eContract.export'
                )
              "
              >导出明细</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </nav>
    <section v-if="searchForm.queryItem == 'ALL'" class="radio">
      <template v-for="(item, index) in contractFiletatusList">
        <div
          :key="item.value"
          class="radio-item f-c"
          :class="{
            current:
              (index == 0 && searchForm.contractSignStatusList.length == 0) ||
              searchForm.contractSignStatusList.includes(item.value),
          }"
          @click="handleChange(item.value)"
        >
          {{ item.label }}
          <i class="iconfont icontag_select"></i>
        </div>
      </template>
    </section>

    <section style="padding: 0 20px; margin-top: 20px" class="table-con">
      <el-table
        ref="table"
        :data="tableData"
        v-loading="loading"
        :width="screenWidth"
        :header-cell-style="{ background: '#F1F1F1' }"
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" fixed></el-table-column>
        <el-table-column
          prop="contractName"
          label="文件名称"
          width="350"
          fixed="left"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span class="table-name" @click="handleSeeSignProcess(scope.row)">
              {{ scope.row.contractName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="contractSignStatus" label="签署状态">
          <template slot-scope="scope">
            {{ scope.row.contractSignStatus | fileStatus }}
          </template>
        </el-table-column>
        <el-table-column prop="contractFileType" label="文件类型">
          <template slot-scope="scope">
            {{ scope.row.contractFileType | templateType }}
          </template>
        </el-table-column>
        <el-table-column
          prop="taxSubName"
          label="合同主体单位"
          width="180px"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="signerList"
          label="签署人"
          :width="signerLength <= 2 ? '240px' : '360px'"
        >
          <template slot-scope="scope">
            <el-tooltip
              placement="top"
              :disabled="scope.row.signerList.length <= 3"
            >
              <div slot="content">
                <span
                  v-for="(item, index) in scope.row.signerList"
                  :key="index"
                >
                  {{ item.sealUser ? "企" : "个" }} {{ item.name }}</span
                >
              </div>

              <div class="sign-person">
                <div
                  class="flex"
                  v-for="(item, index) in scope.row.signerList.slice(0, 3)"
                  :key="index"
                >
                  <span class="flex">
                    <span class="f-c qy">
                      {{ item.sealUser ? "企" : "个" }}
                      <span
                        class="status"
                        :class="[
                          { finshed: item.signerStatus == 'FINISHED' },
                          { wait: item.signerStatus == 'WAIT_SIGN' },
                          { cannot: item.signerStatus == 'CANNOT_SIGN' },
                        ]"
                      ></span
                    ></span>

                    <el-tooltip
                      placement="top"
                      :content="item.name"
                      :disabled="
                        item.name.length <= 4 || scope.row.signerList.length > 3
                      "
                    >
                      <span>
                        {{
                          item.name.length > 4
                            ? item.name.substring(0, 4) + "..."
                            : item.name
                        }}</span
                      >
                    </el-tooltip>
                  </span>
                </div>
                <span v-if="scope.row.signerList.length > 3">
                  剩余{{ scope.row.signerList.length - 3 }}个 ...</span
                >
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="createUser" label="发起人">
          <template slot-scope="scope">
            <span style="white-space: nowrap">{{ scope.row.createUser }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="startDate"
          label="发起日期"
          min-width="120px"
        ></el-table-column>
        <el-table-column
          prop="expirationDate"
          label="完成日期"
          min-width="100px"
        ></el-table-column>
        <el-table-column fixed="right" label="操作" width="170">
          <template slot-scope="scope">
            <el-button
              type="text"
              style="margin-left: 10px"
              @click="handleSign(scope.row)"
              v-show="
                scope.row.currUser &&
                (scope.row.contractSignStatus === 'WAIT_SIGN' ||
                  scope.row.contractSignStatus === 'SIGNING')
              "
              v-if="
                privilegeVoList.includes('hrContract.conManage.eContract.sign')
              "
              >签署
            </el-button>
            <el-button
              type="text"
              @click="handleRemind(scope.row)"
              v-show="
                !scope.row.currUser &&
                !scope.row.sealUser &&
                (scope.row.contractSignStatus === 'WAIT_SIGN' ||
                  scope.row.contractSignStatus === 'SIGNING')
              "
              >提醒
            </el-button>
            <el-button
              type="text"
              @click="handleRecall(scope.row)"
              v-show="
                scope.row.contractSignStatus === 'WAIT_SIGN' ||
                scope.row.contractSignStatus === 'SIGNING'
              "
              v-if="
                privilegeVoList.includes('hrContract.conManage.eContract.back')
              "
              >撤回
            </el-button>
            <el-dropdown trigger="click" style="margin-left: 10px">
              <el-button type="text">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-if="
                    privilegeVoList.includes(
                      'hrContract.conManage.eContract.detail'
                    )
                  "
                  @click.native="handleView(scope.row)"
                  >文件详情
                </el-dropdown-item>
                <el-dropdown-item
                  v-show="
                    scope.row.contractSignStatus === 'COMPLETE' ||
                    scope.row.contractSignStatus === 'EXPIRED' ||
                    scope.row.contractSignStatus === 'CANCEL'
                  "
                  v-if="
                    privilegeVoList.includes(
                      'hrContract.conManage.eContract.download'
                    )
                  "
                  @click.native="handleDownload(scope.row)"
                  >下载
                </el-dropdown-item>
                <el-dropdown-item
                  v-show="
                    scope.row.contractSignStatus === 'SIGNING' ||
                    scope.row.contractSignStatus === 'COMPLETE'
                  "
                  v-if="
                    privilegeVoList.includes(
                      'hrContract.conManage.eContract.abandon'
                    )
                  "
                  @click.native="handleAbandon(scope.row)"
                  >废弃
                </el-dropdown-item>
                <el-dropdown-item
                  v-show="
                    scope.row.contractSignStatus === 'WITHDRAWN' ||
                    scope.row.contractSignStatus === 'CANCEL' ||
                    scope.row.contractSignStatus === 'ASYNC_CREATE_FAIL'
                  "
                  v-if="
                    privilegeVoList.includes(
                      'hrContract.conManage.eContract.delete'
                    )
                  "
                  @click.native="handleDelete(scope.row)"
                  >删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </section>

    <footer class="footer" style="padding: 0 20px">
      <div class="block">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-sizes="[20, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </footer>
    <!-- 筛选 -->
    <el-dialog
      title="筛选"
      :visible.sync="isShowScreening"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="700px"
      ref="screenForm"
    >
      <el-form ref="searchForm" :model="searchForm" label-width="130px">
        <el-form-item label="文件类型">
          <el-radio-group v-model="searchForm.contractFileType">
            <el-radio-button
              v-for="(item, index) in fileTypeList"
              :label="item.value"
              :key="index"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="合同主体单位">
          <el-select
            v-model="searchForm.taxSubId"
            placeholder="请选择合同主体单位"
            clearable
          >
            <el-option
              v-for="item in contractSubList"
              :key="item.contractSubId"
              :label="item.contractName"
              :value="item.contractSubId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="个人签署方">
          <el-select
            v-model="searchForm.signerId"
            filterable
            remote
            clearable
            placeholder="请输入员工姓名"
            :remote-method="remoteMethod"
            :loading="loading2"
          >
            <el-option
              v-for="item in compEmpOptions"
              :key="item.compEmpId"
              :label="item.empName + '(' + item.mobile + ')'"
              :value="item.compEmpId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发起人">
          <el-select
            v-model="searchForm.createUserId"
            filterable
            remote
            clearable
            placeholder="请输入发起人姓名"
            :remote-method="searchSponsor"
            :loading="loading3"
          >
            <el-option
              v-for="item in sponsorList"
              :label="formatValue(item)"
              :value="item.platformUserId"
              :key="item.platformUserId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发起日期">
          <el-date-picker
            v-model="searchForm.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="完成日期">
          <el-date-picker
            v-model="searchForm.expirationDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="resetSreen">重置</el-button>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </div>
    </el-dialog>
    <!-- 查看签署流程 -->
    <el-drawer
      :visible.sync="isShowSignDrawer"
      :with-header="true"
      size="500px"
    >
      <header class="drawer-title">
        <span :title="signProcessInfo.contractName">
          {{ signProcessInfo.contractName }}
        </span>
        <i class="el-icon-close" @click="isShowSignDrawer = false"></i>
      </header>
      <dir class="drawer-content">
        <el-tabs class="drawer-tabs" v-model="currentStep">
          <el-tab-pane label="签署步骤" name="0"></el-tab-pane>
          <el-tab-pane label="填充域" name="1"></el-tab-pane>
        </el-tabs>
        <div v-if="currentStep == 0">
          <div class="sign-process process">
            <div class="contract-title">
              <span class="process-title">签署</span>
              <span>文件状态:</span>
              <span :class="getContractStatus">
                {{ signProcessInfo.contractSignStatus | fileStatus }}
              </span>
            </div>
            <ul>
              <li v-for="(item, index) in steps" :key="index">
                <div class="process-item">
                  <el-avatar
                    size="48"
                    :src="item.oneSizePhoto"
                    v-if="item.oneSizePhoto"
                  ></el-avatar>
                  <div class="avatar" v-else>
                    {{ item.userName ? item.userName.substr(-2) : "" }}
                  </div>
                  <i
                    class="el-icon-success"
                    v-show="item.signStatus === 'SUCCESS'"
                  ></i>
                  <div>
                    <p class="user-name">{{ item.userName }}</p>
                    <p style="font-size: 12px; margin-top: 10px">
                      {{ item.stepName }}
                    </p>
                  </div>
                  <span class="process-time">{{ item.signTime }}</span>
                </div>
                <div
                  class="process-line"
                  v-show="index < steps.length - 1"
                ></div>
              </li>
            </ul>
          </div>
          <div class="copy-process process" v-show="copyList.length > 0">
            <span class="process-title">抄送</span>
            <ul style="display: flex; align-items: center; flex-wrap: wrap">
              <li
                v-for="(item, index) in copyList"
                :key="index"
                style="margin-bottom: 20px"
              >
                <div class="process-item">
                  <el-avatar
                    size="48"
                    :src="item.oneSizePhoto"
                    v-if="item.oneSizePhoto"
                  ></el-avatar>
                  <div class="avatar" v-else>
                    {{ item.userName ? item.userName.substr(-2) : "" }}
                  </div>
                  <p class="user-name">{{ item.userName }}</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="process" v-if="currentStep == 1" style="margin-top: 20px">
          <el-tabs tab-position="left" class="fill-tabs">
            <el-tab-pane
              :label="item.stepName"
              v-for="(item, index) in steps"
              :key="index"
            >
              <el-table
                stripe
                border
                :data="item.contractFieldList"
                :header-cell-style="{ background: '#F1F1F1' }"
              >
                <el-table-column
                  prop="fieldName"
                  label="名称"
                  width="80px"
                  :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                  prop="relationName"
                  label="关联信息项"
                  :show-overflow-tooltip="true"
                >
                  <template slot="header">
                    关联信息项
                    <el-tooltip
                      content="请先配置签署人的文本控件，若此项为空，在设置签署位置时页面左侧将无自定义控件可供拖拽"
                      placement="top"
                    >
                      <i class="el-icon-info"></i>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="fieldValue"
                  label="关联信息项值"
                  :show-overflow-tooltip="true"
                >
                  <template slot="header">
                    关联信息项值
                    <el-tooltip
                      content="员工/企业关联信息项数据，可修改"
                      placement="top"
                    >
                      <i class="el-icon-info"></i>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </dir>
    </el-drawer>
    <electronic-download-dialog ref="download-dialog" />
    <electronic-sign-dialog ref="sign-dialog" />
  </div>
</template>

<script>
import { mapState } from "vuex";
import * as constData from "@/utils/constData";
import { getParameters } from "@/utils/utils";

import electronicDownloadDialog from "./components/electronic/electronic-download-dialog";
import electronicSignDialog from "./components/electronic/electronic-sign-dialog";
import Paid from "../initialize/paid.vue";
import {
  apiGetEnumByCodeList,
  apiDownloadArchive,
  apiGetExportTemplate,
} from "../staffManage/store/api";
import {
  apiGetElcontractList,
  apiGetContractSubjectList,
  apiSignSponsorList,
  apiGetALLEmp,
  apiGetViewUrl,
  apiStartRemind,
  apiStartSign,
  apiStartBack,
  apiStartAbandon,
  apiStartDelete,
  apiExportElecontract,
  apiGetSignProcess,
  apiGetCountContractItem,
} from "./store/api";

export default {
  components: { Paid, electronicDownloadDialog, electronicSignDialog },
  data() {
    return {
      isShowScreening: false,
      loading: false,
      loading2: false,
      loading3: false,
      screenWidth: document.body.clientWidth, // 屏幕尺寸宽度
      screenHeight: document.body.clientHeight - 270, // 屏幕尺寸高度
      total: 0,
      checked: true,
      searchForm: {
        // contractName: "",
        key: "",
        currPage: 1,
        pageSize: 20,
        contractFileType: "", //文件类型
        contractSignStatus: "", //文件状态
        contractSignStatusList: [],
        taxSubId: "", //合同主体单位
        signerId: "", //签署人
        createUserId: "", //发起人
        startDate: "", //发起日期开始
        endDate: "", //发起日期结束
        expirationDateStart: "", //完成日期开始
        expirationDateEnd: "", //完成日期结束
        queryItem: "ALL",
      },
      tableData: [],
      contractSubList: [], //合同主体单位
      sponsorList: [], //发起人列表
      fileTypeList: [{ label: "全部", value: "" }].concat(
        constData.contractTemplateTypeList
      ),
      contractFiletatusList: [{ label: "全部", value: "[]" }].concat(
        constData.contractFiletatusList
      ),
      compEmpOptions: [], //签署人列表
      selectItems: [],
      isShowSignDrawer: false, //查看签署侧弹窗
      signProcessInfo: {},
      steps: [],
      copyList: [],
      currentStep: 0,
      tabs: [
        { label: "全部合同", name: "ALL" },
        {
          label: "待我签署",
          name: "USER_CONTRACT",
          limitId: "userContractCount",
        },
        {
          label: "待他人签署",
          name: "OTHERS_CONTRACT",
          limitId: "otherContractCount",
        },
        {
          label: "签署完成",
          name: "FINISHED_CONTRACT",
        },
      ],
      limit: {
        userContractCount: 0,
        otherContractCount: 0,
      },
      current: "全部",
      signerLength: "",
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
    getContractStatus() {
      let contractSignStatus = this.signProcessInfo.contractSignStatus;
      if (
        contractSignStatus === "DRAFT" ||
        contractSignStatus === "ASYNC_CREATE_FAIL"
      ) {
        return "contract-status-error";
      }
      return contractSignStatus === "COMPLETE"
        ? "contract-status-success"
        : contractSignStatus === "WAIT_SIGN" || contractSignStatus === "SIGNING"
        ? "contract-status-normal"
        : "contract-status-other";
    },
  },
  created() {
    const { type } = getParameters();
    if (type) {
      this.checked = false;
      this.searchForm.contractSignStatusList = ["WAIT_SIGN", "SIGNING"];
      this.searchForm.contractFileType = "LABOUR_CONTRACT";
    }
    this.getContractSubjectList();
    this.getContractRecord();
    this.getRelationList();
    this.getItem();
    this.getList();
  },
  methods: {
    handleClickTab() {
      this.searchForm.contractSignStatusList = [];
      this.resetSreen();
    },
    handleTabName(name, id) {
      const num = typeof id === "string" ? `（${this.limit[id]}）` : "";
      return `${name}${num}`;
    },
    async getList() {
      this.loading = true;
      this.getItem();
      let res = await apiGetElcontractList(this.searchForm);
      this.loading = false;
      let lengthArr = [];
      if (res.success && res.data) {
        this.tableData = res.data.records.map((item) => {
          item.createUser = this.formatName(item.createUser);
          lengthArr.push(item.signerList.length);
          if (item.signerList.length) {
            item.signerList.map((it) => {
              it.name = this.formatName(it.name);
              return it;
            });
          }
          return item;
        });
        this.signerLength = Math.max.apply(null, lengthArr);
        this.total = res.data.total;
      }
    },

    //姓名格式化
    formatName(str) {
      if (!str) return "--";
      // const name = str.length > 4 ? str.substring(0, 4) + "..." : str;
      return str;
    },

    formatValue(item) {
      return `${item.empName} (${item.mobile ? item.mobile : "--"})`;
    },

    //合同类目统计
    async getItem() {
      const { data } = await apiGetCountContractItem();
      this.limit = data;
    },

    async getContractSubjectList() {
      let res = await apiGetContractSubjectList({ type: "CONTRACT" });
      if (res.success) {
        this.contractSubList = res.data;
        this.$store.commit("contractManageStore/SET_CONTRACTSUBLIST", res.data);
      }
    },
    async getContractRecord() {
      let res = await apiGetEnumByCodeList({ fieldCodes: ["contractType"] });
      if (res.success) {
        let data = res.data[0].optionEnums.filter((i) => i.enableYn);
        this.$store.commit("contractManageStore/SET_CONTRACTTYPELIST", data);
      }
    },
    //获取个人签署方关联信息项
    async getRelationList() {
      let res = await apiGetExportTemplate({
        exportSource: "CONTRACT_TEMPLATE",
      });
      if (res.success) {
        this.$store.commit("contractManageStore/SET_RELATIONLIST", res.data);
      }
    },
    //签署人远程搜索
    remoteMethod(query) {
      if (query !== "") {
        this.loading2 = true;
        apiGetALLEmp({ key: query }).then((res) => {
          this.loading2 = false;
          if (res.success) {
            this.compEmpOptions = res.data;
          }
        });
      } else {
        this.compEmpOptions = [];
      }
    },
    searchSponsor(query) {
      if (query !== "") {
        this.loading3 = true;
        apiSignSponsorList({ key: query }).then((res) => {
          this.loading3 = false;
          if (res.success) {
            this.sponsorList = res.data;
          }
        });
      } else {
        this.sponsorList = [];
      }
    },
    //显示签署人
    getSigner(data) {
      let arr = [];
      data.map((item) => {
        if (item.sealUser) {
          if (item.name) {
            arr.push(`企:${item.name ? item.name : ""}`);
          }
        } else {
          arr.push(`个:${item.name ? item.name : ""}`);
        }
      });
      return arr.join("  ");
    },
    //查看签署流程
    async handleSeeSignProcess(row) {
      this.getList();
      let res = await apiGetSignProcess({ contractId: row.id });
      if (res.success) {
        this.signProcessInfo = res.data;
        this.steps = [];
        this.copyList = [];
        this.steps = res.data.steps.filter(
          (item) => item.operate !== "CARBON_COPY"
        );
        this.copyList = res.data.steps.filter(
          (item) => item.operate === "CARBON_COPY"
        );
      }
      this.currentStep = "0";
      this.isShowSignDrawer = true;
    },
    //显示页数
    handleSizeChange(val) {
      this.searchForm.pageSize = val;
      this.searchForm.currPage = 1;
      this.getList();
    },
    //翻页
    handleCurrentChange(val) {
      this.searchForm.currPage = val;
      this.getList();
    },
    //对发起日期、结束日期进行处理
    formatDate() {
      this.searchForm.startDate = this.searchForm.date
        ? this.searchForm.date[0]
        : "";
      this.searchForm.endDate = this.searchForm.date
        ? this.searchForm.date[1]
        : "";
      this.searchForm.expirationDateStart = this.searchForm.expirationDate
        ? this.searchForm.expirationDate[0]
        : "";
      this.searchForm.expirationDateEnd = this.searchForm.expirationDate
        ? this.searchForm.expirationDate[1]
        : "";
    },
    //查询
    handleSearch() {
      this.formatDate();
      this.searchForm.currPage = 1;
      this.isShowScreening = false;
      this.getList();
    },
    //筛选重置
    resetSreen() {
      for (let key in this.searchForm) {
        if (!["key", "currPage", "pageSize", "queryItem"].includes(key)) {
          this.searchForm[key] = "";
        }
      }
      this.searchForm.contractSignStatusList = [];
      this.checked = true;
      this.handleSearch();
    },

    //发起签约
    handleInitiateSign() {
      this.$store.commit("contractManageStore/SET_CHOOSERECORDDATA", []);
      this.$store.commit("contractManageStore/SET_CHOOSETEMPLATEDATA", null);
      this.$store.commit("contractManageStore/SET_CHOOSESEALSTAFFDATA", null);
      this.$store.commit("contractManageStore/SET_CHOOSESIGNSTAFFDATA", []);
      this.$store.commit("contractManageStore/SET_CHOOSECOPYSTAFFDATA", null);
      this.$router.push({
        path: "/contract-manage/initiate-signing",
        query: { isBatchSign: false },
      });
    },
    //查看
    async handleView(data) {
      let res = await apiGetViewUrl({ contractId: data.id });
      if (res.success) {
        window.open(res.data.url);
      }
    },
    //签署
    async handleSign(data) {
      let params = {
        clientSource: "PC",
        contractId: data.id,
        silenceSignYn: data.silenceSignYn,
      };
      let res = await apiStartSign(params);
      if (res.success) {
        window.open(`${res.data.pcUrl}?token=${res.data.token}`);
      }
    },
    //提醒
    async handleRemind(data) {
      if (
        data.contractSignStatus !== "WAIT_SIGN" &&
        data.contractSignStatus !== "SIGNING"
      ) {
        this.$message.warning("请检查是否存在无需提醒签署的合同");
        return;
      } else {
        let contractIdList = [data.id];
        let res = await apiStartRemind({ contractIdList });
        if (res.success) {
          this.$message.success("提醒成功");
        }
      }
    },
    //撤回
    handleRecall(data) {
      this.$confirm("确定撤回该文件？该操作无法撤回，请您谨慎操作", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        apiStartBack({ id: data.id }).then((res) => {
          if (res.success) {
            this.$message.success("操作成功");
            this.getList();
          }
        });
      });
    },
    //废弃
    handleAbandon(data) {
      this.$confirm("确定废弃该文件？该操作无法撤回，请您谨慎操作", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        apiStartAbandon({ id: data.id }).then((res) => {
          if (res.success) {
            this.$message.success("操作成功");
            this.getList();
          }
        });
      });
    },
    //下载
    handleDownload(data) {
      apiDownloadArchive({ archiveId: data.archiveId });
    },
    //删除
    handleDelete(data) {
      this.$confirm("确定删除该文件？该操作无法撤回，请您谨慎操作", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        apiStartDelete({ id: data.id }).then((res) => {
          if (res.success) {
            this.$message.success("操作成功");
            this.getList();
          }
        });
      });
    },
    //表格批量选择
    handleSelectionChange(val) {
      this.selectItems = val;
    },
    //批量发起提醒
    async handleBatchRemind() {
      if (this.selectItems.length === 0) {
        this.$message.warning("请至少先选择一条电子合同");
        return;
      } else {
        let contractIdList = [];
        this.statusFlag = true;
        this.selectItems.map((item) => {
          contractIdList.push(item.id);
          if (
            item.contractSignStatus !== "WAIT_SIGN" &&
            item.contractSignStatus !== "SIGNING"
          ) {
            this.statusFlag = false;
          }
        });
        if (this.statusFlag) {
          let res = await apiStartRemind({ contractIdList });
          if (res.success) {
            this.$message.success("提醒成功");
          }
        } else {
          this.$message.warning("请检查是否存在无需提醒签署的合同");
          return;
        }
      }
    },
    //导出
    async handleExport() {
      let res = await apiExportElecontract(this.searchForm);
    },
    handleChange(item) {
      let arr = this.searchForm.contractSignStatusList;
      item == "[]"
        ? (arr = [])
        : arr.includes(item)
        ? (arr = arr.filter((el) => el != item))
        : arr.push(item);

      if (arr.includes("SIGNING") && !arr.includes("WAIT_SIGN")) {
        arr.push("WAIT_SIGN");
      } else if (!arr.includes("SIGNING") && arr.includes("WAIT_SIGN")) {
        const i = arr.findIndex((it) => it === "WAIT_SIGN");
        arr.splice(i, 1);
      }
      this.searchForm.contractSignStatusList = arr;
      this.searchForm.currPage = 1;
      this.getList();
    },

    //批量签署
    handleSignClick() {
      if (!this.selectItems.length)
        return this.$message.warning("请选择至少一条签署中的合同");

      const singList = this.selectItems.filter(
        (item) => item.contractSignStatus === "SIGNING" && item.currUser
      );
      const cmt = this.$refs["sign-dialog"];
      if (!singList.length)
        return this.$message.warning("请至少选择一条本人可签署合同");

      if (singList.length == this.selectItems.length) {
        //全部可签
        cmt.sign.status = singList.length == 1 ? "ALL-ONE" : "ALL";
      } else {
        //部分可签
        cmt.sign = {
          status: singList.length == 1 ? "PORTION-ONE" : "PORTION",
          n: this.selectItems.length - singList.length,
        };
      }
      cmt.sign.m = singList.length;
      cmt.ids = singList;
      cmt.isShow = true;
    },
    //批量下载
    handleDownloadClick() {
      if (!this.selectItems.length)
        return this.$message.warning("请选择至少一条签署完成的合同");
      const downList = this.selectItems.filter(
        (item) => item.contractSignStatus === "COMPLETE"
      );
      const cmt = this.$refs["download-dialog"];
      if (!downList.length) {
        //不可下载
        cmt.status.downLoad = "NOT";
      } else if (downList.length == this.selectItems.length) {
        //全部可下载
        cmt.status.downLoad = "ALL";
      } else {
        //部分可下载
        cmt.status = {
          downLoad: "PORTION",
          n: this.selectItems.length - downList.length,
        };
      }
      cmt.status.m = downList.length;
      cmt.ids = downList.map((it) => it.id);
      cmt.isShow = true;
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../assets/scss/helpers";
#laborContract {
  // overflow: hidden;
}
.section-tabs {
  /deep/.el-tabs__item {
    font-size: 16px;
    // color: #24262a;
    padding: 0 15px;
    font-weight: 400;
  }
  /deep/.el-tabs__item.is-active {
    color: $mainColor;
    font-weight: 500;
  }
  /deep/.el-tabs__nav-wrap::after {
    display: none;
  }
}
.iconiconfonticonfontsousuo1 {
  font-size: 20px;
  margin-right: 5px;
}
.main-title {
  border-bottom: 1px solid #ededed;
}
.flex {
  display: flex;
  align-items: center;
  .check-box {
    position: relative;
    right: -2px;
    z-index: 2;
  }
}
.nav {
  display: flex;
  justify-content: space-between;
  > div {
    display: flex;
    align-items: center;
  }
}
.radio {
  display: flex;
  margin: 16px 22px;
  .radio-item {
    width: 72px;
    height: 32px;
    border: 1px solid #cbced8;
    border-radius: 4px;
    color: #46485a;
    margin-right: 4px;
    position: relative;
    cursor: pointer;
    .icontag_select {
      position: absolute;
      right: -1px;
      bottom: -1px;
      margin: 0;
      display: none;
    }
  }
  .current {
    color: $mainColor;
    border-color: $mainColor;
    .icontag_select {
      display: block;
    }
  }
}

.el-pagination {
  margin-top: 10px;
}
.footer {
  display: flex;
  justify-content: flex-end;
}
.table-con {
  .el-button {
    padding: 0;
    margin-right: 5px;
  }
  .el-button:last-child {
    margin-right: 0;
  }
  .sign-person {
    display: flex;
    white-space: nowrap;
    .flex {
      display: flex;
      align-items: center;
      margin-right: 15px;
    }
    .qy,
    .gr {
      width: 16px;
      height: 16px;
      border-radius: 4px;
      font-size: 12px;
      margin-right: 5px;
      position: relative;
      background: #f8f8f8;
      color: #777c94;
    }

    // .qy {
    //   background: #f5f7ff;
    //   color: #4f71ff;
    // }
    // .gr {
    //   color: #e59900;
    //   background: #fffaf0;
    // }
    .status {
      width: 10px;
      height: 10px;
      position: absolute;
      right: -3px;
      bottom: -2px;
    }

    .finshed {
      background: url(../../assets/images/select1.png) no-repeat;
      background-size: 100% 100%;
    }
    .wait {
      background: url(../../assets/images/select2.png) no-repeat;
      background-size: 100% 100%;
    }
    .cannot {
      background: url(../../assets/images/select3.png) no-repeat;
      background-size: 100% 100%;
    }
  }
}
.el-tabs {
  margin-right: 20px;
}
/deep/.el-drawer__header {
  height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
}
.drawer-title {
  margin: 0;
  padding: 0 0 0 20px;
  font-size: 17px;
  height: 61px;
  line-height: 61px;
  border-bottom: 1px solid #ededed;
  overflow: hidden;
  i {
    cursor: pointer;
    float: right;
    margin-top: 22px;
  }
  span {
    display: inline-block;
    max-width: 340px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.drawer-content {
  font-size: 14px;
  color: #555;
  padding-left: 20px;
  .contract-title {
    display: flex;
    align-items: center;
    span:nth-child(2) {
      color: #888;
      margin: 0 10px;
    }
  }
  .contract-status-normal {
    color: #ff9b0e;
  }
  .contract-status-success {
    color: #41bd5a;
  }
  .contract-status-error {
    color: #ff6051;
  }
  .contract-status-other {
    color: #888888;
  }
  .process {
    overflow: hidden;
    .process-item {
      display: flex;
      align-items: center;
      position: relative;
    }
    .user-name {
      display: inline-block;
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .process-line {
      width: 2px;
      height: 40px;
      background: #eaeaea;
      margin: 10px 0 10px 24px;
    }
    .process-time {
      position: absolute;
      right: 50px;
    }
    .el-icon-success {
      font-size: 20px;
      position: absolute;
      left: 30px;
      bottom: 0;
    }
  }
  .process-title {
    display: flex;
    align-items: center;
    color: #070f29;
    margin: 20px 0;
  }
  .process-title::before {
    content: "";
    display: inline-block;
    width: 3px;
    height: 14px;
    background-color: #4f71ff;
    margin-right: 8px;
  }
  .avatar {
    width: 48px;
    height: 48px;
    background: rgba(65, 133, 248, 0.2);
    margin-right: 16px;
    border-radius: 50%;
    text-align: center;
    line-height: 48px;
    color: #4f71ff;
    font-size: 18px;
    font-weight: 600;
  }
}
.drawer-tabs {
  /deep/.el-tabs__nav-wrap::after {
    display: none;
  }
}
.fill-tabs {
  /deep/ .el-tabs__item {
    padding-left: 0;
  }
}
</style>
