<template>
  <div class="initiate-sign">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="24">
          <span @click="handleClose" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>发起签约</span>
        </el-col>
      </el-row>
    </header>
    <section class="section">
      <div>
        <p class="title"><span>选择模板</span></p>
        <div v-if="chooseTemplateData" class="select-con">
          <p>
            已选：<span style="color: #4f71ff">
              {{ chooseTemplateData.templateName }}
            </span>
          </p>
          <el-button type="primary" @click="chooseTemplate">更换模板</el-button>
        </div>
        <el-button type="primary" @click="chooseTemplate" v-else>
          选择模板
        </el-button>
        <p class="title"><span>基本信息</span></p>
        <el-form :model="form" ref="form" :rules="rule" label-width="130px">
          <el-form-item label="文件名称" prop="fileName">
            <el-input
              v-model="form.fileName"
              placeholder="请输入文件名称"
              maxlength="50"
            ></el-input>
          </el-form-item>
          <div v-if="chooseTemplateData || chooseRecordData">
            <el-form-item label="合同主体单位" prop="taxSubId">
              <el-select
                v-model="form.taxSubId"
                placeholder="请选择"
                :disabled="isBatchSign"
                clearable
              >
                <el-option
                  v-for="item in contractSubList"
                  :key="item.contractSubId"
                  :label="item.contractName"
                  :value="item.contractSubId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="发起方式" prop="signWay">
              <el-radio-group
                v-model="form.signWay"
                @change="handleChangeSignWay"
              >
                <el-radio label="SINGLE" :disabled="isBatchSign">
                  单份发起
                </el-radio>
                <el-radio label="MULTI" :disabled="isBatchSign || isCanBatch">
                  批量发起
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              prop="contractRecord"
              v-if="form.templateType === 'LABOUR_CONTRACT'"
            >
              <template slot="label">
                <span>选择合同记录</span>
                <el-tooltip
                  content="劳动合同类文件请先选择对应的合同记录"
                  placement="top-start"
                >
                  <i class="el-icon-info"></i>
                </el-tooltip>
              </template>
              <el-input
                class="contract-record"
                :title="form.contractRecord"
                v-model="form.contractRecord"
                placeholder="请输入文件名称"
                @focus="handleSelectRecord"
                :disabled="isBatchSign"
              ></el-input>
            </el-form-item>
            <el-form-item prop="silenceSignYn" v-if="flag">
              <template slot="label">
                <span>企业静默签署</span>
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content" style="line-height: 20px">
                    发起签约后企业方将自动签署
                  </div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
              <el-radio-group v-model="form.silenceSignYn">
                <el-radio label="true">是</el-radio>
                <el-radio label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="命名方式"
              prop="namingRule"
              v-show="isShowNamed"
            >
              文件名称{{ form.namingRule | nominateRule }}
              <el-checkbox-group v-model="form.namingRule">
                <el-checkbox label="NAME">姓名</el-checkbox>
                <el-checkbox label="DEPARTMENT">部门</el-checkbox>
                <el-checkbox label="POSITION">岗位</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
        </el-form>
        <config-sign-process
          ref="configSignProcess"
          :form="form"
          @hiddenStatic="hiddenStatic"
        ></config-sign-process>
      </div>
    </section>
    <footer class="template-footer">
      <el-row type="flex">
        <el-col :span="24">
          <el-button @click="handleClose"> 取消 </el-button>
          <el-button
            :loading="submitLoading"
            type="primary"
            @click="handleSubmit"
            :disabled="!chooseTemplateData"
          >
            提交
          </el-button>
        </el-col>
      </el-row>
    </footer>
    <!-- 选择模板 -->
    <div class="process">
      <right-pop
        :pop-show="popShow"
        :has-footer="false"
        popTitle="选择模板"
        :popWidth="450"
      >
        <div slot="pop-content">
          <chooseTemplate
            ref="chooseTemplate"
            :taxSubId="isBatchSign ? form.taxSubId : ''"
            :isBatchSign="isBatchSign"
          ></chooseTemplate>
        </div>
      </right-pop>
    </div>
    <!-- 选择合同记录 -->
    <div class="process">
      <right-pop
        :pop-show="popShow2"
        :has-footer="false"
        popTitle="选择合同记录"
        :popWidth="550"
      >
        <div slot="pop-content">
          <chooseContractRecord
            ref="chooseContractRecord"
            :isBatch="form.signWay === 'MULTI' ? true : false"
            :taxSubId="form.taxSubId"
          ></chooseContractRecord>
        </div>
      </right-pop>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import rightPop from "@/components/basic/rightPop";
import chooseTemplate from "./components/chooseTemplate";
import chooseContractRecord from "./components/chooseContractRecord";
import configSignProcess from "./components/configSignProcess";
import { debounce } from "../../utils/debounce";
import {
  apiGetTemplateDetail,
  apiStartSignContract,
  apiGetContractSubjectList,
} from "./store/api";
export default {
  name: "initiateSigning",
  components: {
    rightPop,
    chooseTemplate,
    chooseContractRecord,
    configSignProcess,
  },
  data() {
    var validateSpace = (rule, value, callback) => {
      if (value.indexOf(" ") == -1) {
        callback();
      } else {
        callback(new Error("不能包含空格"));
      }
    };
    return {
      flag: false,
      form: {
        fileName: "",
        taxSubId: "",
        contractRecord: "",
        signWay: "SINGLE",
        namingRule: ["NAME"],
        silenceSignYn: "false",
        templateId: "",
      },
      rule: {
        fileName: [
          {
            required: true,
            message: "请输入文件名称",
            trigger: "blur",
          },
          {
            validator: validateSpace,
            message: "不能包含空格",
            trigger: "blur",
          },
        ],
        taxSubId: {
          required: true,
          message: "请选择合同主体单位",
          trigger: "change",
        },
        contractRecord: {
          required: true,
          message: "请选择合同记录",
          trigger: "change",
        },
      },
      popShow: { isshow: false },
      popShow2: { isshow: false },
      steps: [], //判断当前模板步骤
      processStep: {},
      submitLoading: false,
      isBatchSign: this.$route.query.isBatchSign == true ? true : false,
    };
  },
  computed: {
    ...mapState("contractManageStore", {
      contractSubList: "contractSubList",
      chooseTemplateData: "chooseTemplateData",
      chooseRecordData: "chooseRecordData",
      flowStep: "flowStep",
    }),
    ...mapState({
      productEdition: (state) => state.productEdition,
    }),
    isShowNamed() {
      let signArr = this.steps.filter((item) => item.operate === "SIGN");
      if (signArr.length === 1) {
        return true;
      } else {
        return false;
      }
    },
    isCanBatch() {
      //如果只有一个企业签署方或者个人签署方大于等于2或者批量签约，不可批量发起
      let filterSign = this.steps.filter((item) => item.operate === "SIGN");
      if (
        filterSign.length >= 2 ||
        (this.steps.length === 1 && this.steps[0].operate === "SEAL")
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
  watch: {
    "form.taxSubId"() {
      this.$refs.configSignProcess.getStaffField();
    },
    flowStep() {
      this.processStep = this.flowStep;
    },
    chooseTemplateData() {
      if (this.chooseTemplateData) {
        if (!this.isBatchSign) {
          if (
            "LABOUR_CONTRACT" != this.chooseTemplateData.templateType ||
            "LABOUR_CONTRACT" != this.form.templateType
          ) {
            this.$store.commit("contractManageStore/SET_CHOOSERECORDDATA", []);
          } else {
            this.dealContractRecord();
          }
        }
        this.form.templateId = this.chooseTemplateData.id;
        this.getTemplateDetail();
      }
    },
    chooseRecordData() {
      this.dealContractRecord();
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path === "/") {
        if (!vm.productEdition.contract) {
          vm.$router.push({
            path: "/openCharge",
            query: {
              bnsCode: "hrContract",
            },
          });
        }
      }
    });
  },
  created() {
    this.getContractSubjectList();
    this.dealContractRecord();
  },
  mounted() {
    window.addEventListener("beforeunload", (e) => this.beforeunloadHandler(e));
  },
  methods: {
    hiddenStatic(val) {
      this.flag = val;
      if (!val) {
        this.form.silenceSignYn = "false";
      }
    },
    beforeunloadHandler(e) {
      e = e || window.event;
      if (e) {
        this.$store.commit("contractManageStore/SET_CHOOSETEMPLATEDATA", null);
        this.$store.commit("contractManageStore/SET_CHOOSERECORDDATA", []);
      }
    },
    //获取合同主体单位
    async getContractSubjectList() {
      let res = await apiGetContractSubjectList({ type: "CONTRACT" });
      if (res.success) {
        this.$store.commit("contractManageStore/SET_CONTRACTSUBLIST", res.data);
      }
    },
    //获取模板详情
    async getTemplateDetail() {
      let res = await apiGetTemplateDetail({
        tempId: this.chooseTemplateData.id,
      });
      if (res.success) {
        let data = res.data;
        this.form.fileName = data.templateName;
        this.form.templateType = data.templateType;
        //设置流程子组件的回显
        this.steps = data.steps;
        this.$refs.configSignProcess.flowStep.steps = this.dealTemplateData(
          data.templateType,
          data.steps
        );
        this.$refs.configSignProcess.flowStep.carbonCopyList =
          this.dealTemplateData(data.templateType, data.carbonCopyList);
        this.$store.commit(
          "contractManageStore/SET_FLOWSTEP",
          this.$refs.configSignProcess.flowStep
        );
      }
    },
    //对合同模板详情数据进行处理
    dealTemplateData(type, arr) {
      arr.map((item) => {
        item.stepUserList = [];
        if (item.operate === "SIGN" && type === "LABOUR_CONTRACT") {
          if (
            !this.isBatchSign &&
            "LABOUR_CONTRACT" != this.form.templateType
          ) {
            item.compEmpId = "";
            item.compEmpName = "";
          } else {
            this.dealContractRecord();
          }
        } else {
          item.stepUserList.push({
            empRecordId: item.empRecordId ? item.empRecordId : "",
            compEmpName: item.compEmpName ? item.compEmpName : "",
            compEmpId: item.compEmpId ? item.compEmpId : "",
            empContractId: item.empContractId ? item.empContractId : "",
          });
        }
      });
      return arr;
    },
    //对合同记录数据进行回显处理
    dealContractRecord() {
      if (this.chooseRecordData.length > 0) {
        let arr = [];
        this.chooseRecordData.map((item) => {
          let name = `${item.empName}${item.contractNo ? item.contractNo : ""}`;
          arr.push(name);
        });
        this.form.contractRecord = arr.join(",");
        this.form.templateType = "LABOUR_CONTRACT";
        this.form.taxSubId = this.chooseRecordData[0].contractSubId
          ? Number(this.chooseRecordData[0].contractSubId)
          : null;
        if (this.isBatchSign) {
          this.form.signWay =
            this.chooseRecordData.length > 1 ? "MULTI" : "SINGLE";
        }
      } else {
        this.form.contractRecord = "";
        this.$nextTick(() => {
          this.$refs.form.clearValidate("contractRecord");
        });
      }
      this.$nextTick(() => {
        this.$refs.configSignProcess.handleRecordChange();
      });
    },
    //选择模板
    chooseTemplate() {
      this.$refs.configSignProcess.isShowTips = false;
      this.popShow.isshow = true;
    },
    //选择合同记录
    handleSelectRecord() {
      if (!this.form.taxSubId) {
        this.$message.warning("请先选择合同主体单位");
        return;
      }
      this.popShow2.isshow = true;
    },
    //提交
    handleSubmit: debounce(function () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          //判断是否有签署流程
          let flowStep = this.processStep;
          //如果开启了企业静默签，可不选择企业签署方
          let filter;
          if (this.form.silenceSignYn == "true") {
            filter = flowStep.steps
              .filter((item) => item.operate === "SIGN")
              .filter((it) => !it.compEmpId);
          } else {
            filter = flowStep.steps.filter((item) => !item.compEmpId);
          }
          if (filter.length > 0) {
            this.$refs.configSignProcess.isShowTips = true;
            this.$nextTick(() => {
              this.errorScroll(document.querySelectorAll(".choosePerson"));
            });
            // this.$message.warning("请选择企业签署方人员");
            return;
          }
          let filter2 = flowStep.carbonCopyList.filter(
            (item) => !item.compEmpId
          );
          console.log(filter2);
          if (filter2.length > 0) {
            this.$message.warning("请选择企业签署方人员");
            return;
          }
          //判断是否是批量发起
          if (this.form.signWay === "MULTI") {
            let signArr = this.flowStep.steps.filter(
              (item) => item.operate === "SIGN"
            );
            if (signArr[0].stepUserList.length < 2) {
              if (this.form.templateType === "LABOUR_CONTRACT") {
                this.$message.warning("请至少选择2条记录");
              } else {
                this.$message.warning("请至少选择2个个人签署方");
              }
              return;
            }
          }
          //设置保存参数
          let data = { ...this.form };
          flowStep.steps.map((item, index) => {
            item.sortby = index;
          });
          flowStep.carbonCopyList.map((item, index) => {
            item.sortby = index;
          });
          data.signerDtoList = flowStep.steps.concat(flowStep.carbonCopyList);
          this.submitLoading = true;
          apiStartSignContract(data)
            .then((res) => {
              if (res.success) {
                this.$message.success("操作成功");
                this.$router.push("/contract-manage/electronic-contract");
              }
            })
            .finally(() => {
              this.submitLoading = false;
            });
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    }, 200),
    //关闭
    handleClose() {
      this.$confirm(
        `确定退出发起签约吗？<p style="color: #999">退出后已编辑的信息不保存</p>`,
        "提示",
        {
          customClass: "special-confirm",
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
          closeOnPressEscape: false,
          dangerouslyUseHTMLString: true,
        }
      ).then(() => {
        this.$router.go(-1);
      });
    },
    //切换发起方式
    handleChangeSignWay() {
      this.$refs.configSignProcess.flowStep.steps.map((item) => {
        if (item.operate === "SIGN") {
          item.compEmpName = "";
          item.compEmpId = "";
          item.stepUserList = [];
        }
      });
      this.$store.commit("contractManageStore/SET_CHOOSESIGNSTAFFDATA", []);
      this.$store.commit("contractManageStore/SET_CHOOSERECORDDATA", []);
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../assets/scss/helpers";
.template-footer {
  padding: 20px 0;
  background-color: #fff;
  border-top: 1px solid #e5e5e5;
  .el-col {
    display: flex;
    justify-content: center;
  }
}
.header {
  font-size: 17px;
  height: 50px;
  border-bottom: 1px solid #ededed;
  line-height: 50px;
  background-color: #090822;
  color: #fff;
}
.section {
  padding: 0 15%;
  overflow: auto;
  height: calc(100vh - 211px);
}
.title {
  font-size: 18px;
  margin: 20px 0;
  display: flex;
  align-items: center;
}
.title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 14px;
  background-color: $mainColor;
  border-radius: 3px;
  margin-right: 8px;
}
.el-form {
  .el-input {
    width: 300px;
  }
  .el-select {
    width: 300px;
  }
  .contract-record {
    input {
      width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }
  }
}
.process-content {
  .title-content {
    color: #333;
    font-size: 18px;
  }
}
.process-step {
  background: #fff;
  margin: 20px 0;
  padding: 20px 9px;
  border: 1px solid #e6e6e6;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .addBtn {
    font-size: 35px;
    cursor: pointer;
    color: #dfdfdf;
  }
  h3 {
    height: 40px;
    line-height: 40px;
    background: #fff5da;
    position: relative;
  }
  .move-icon {
    background: #fff;
    font-size: 20px;
    overflow: hidden;
    padding: 5px;
    border: 1px solid #f4f4f4;
    color: #e5e5e5;
    .move-left {
      float: left;
      cursor: pointer;
    }
    .move-right {
      float: right;
      cursor: pointer;
    }
  }
  .auditing {
    width: 204px;
    float: left;
    padding: 0 10px;
    text-align: center;
    .choosePerson {
      p {
        height: 50px;
        line-height: 50px;
        background: #fff;
        cursor: pointer;
        border: 1px solid #f4f4f4;
        cursor: pointer;
      }
      p.chooseDirection {
        height: 30px;
        line-height: 30px;
      }
      .chooseAgin {
        position: relative;
        .changeStaff {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 50px;
          background: #989696;
          color: #fff;
          line-height: 50px;
          display: none;
          cursor: pointer;
        }
        .changeNewStaff {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          background: #989696;
          color: #fff;
          display: none;
          cursor: pointer;
          height: 84px;
          line-height: 84px;
        }
        .choose-more-person {
          width: 140px;
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .chooseAgin:hover .changeStaff {
        display: block;
      }
    }
    .chooseNewPerson {
      p {
        height: 84px;
        line-height: 84px;
      }
    }
  }
}
.select-con {
  display: flex;
  align-items: center;
  font-size: 16px;
  .el-button {
    margin-left: 20px;
  }
}
.el-button--text {
  font-size: 14px;
}
</style>
