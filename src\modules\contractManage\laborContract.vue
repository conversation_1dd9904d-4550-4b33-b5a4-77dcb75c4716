<template>
  <div id="laborContract" class="def_per_height">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span>劳动合同</span>
        </el-col>
      </el-row>
    </header>
    <nav class="nav" style="padding: 0 20px; margin-top: 20px">
      <div>
        <el-button @click="isShowScreening = true">筛选</el-button>
        <el-input
          v-model.trim="searchForm.key"
          placeholder="请输入姓名/工号/手机号"
          @keyup.enter.native="handleSearch"
          prefix-icon="iconiconfonticonfontsousuo1 iconfont"
          class="search-input"
          style="margin-left: 10px"
        ></el-input>
      </div>
      <div>
        <el-button
          type="primary"
          @click="handleDrawer('NEW_SIGN', null)"
          v-if="
            privilegeVoList.includes(
              'hrContract.conManage.laborContract.newSign'
            )
          "
          >新签</el-button
        >
        <el-dropdown style="margin-left: 10px" trigger="click">
          <el-button type="">
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              @click.native="handleBatchSign"
              v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.laborContract.batchCreate'
                )
              "
              >批量发起签约</el-dropdown-item
            >
            <el-dropdown-item
              @click.native="handleExport"
              v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.laborContract.export'
                )
              "
              >导出</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </nav>
    <section style="padding: 0 20px; margin-top: 20px" class="table-con">
      <el-table
        :data="tableData"
        v-loading="loading"
        :width="screenWidth"
        :header-cell-style="{ background: '#F1F1F1' }"
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" fixed></el-table-column>
        <el-table-column
          fixed
          prop="empName"
          label="员工"
          width="100"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span
              class="table-name"
              @click="handleName(scope.row)"
              v-if="
                privilegeVoList.includes(
                  'hrEmployee.employee.roster.empDetailentry-export'
                )
              "
            >
              {{ scope.row.empName }}
            </span>
            <span v-else>{{ scope.row.empName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="contractNo"
          label="合同编号"
          min-width="120"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="contractSubName"
          label="合同主体单位"
          min-width="180"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="contractType"
          label="合同类型"
          min-width="180"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ getContractType(scope.row.contractType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="contractTerm"
          label="合同期限"
          min-width="90"
        ></el-table-column>
        <el-table-column
          prop="contractTermUnit"
          label="合同期限单位"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.contractTermUnit | contractTermUnit }}
          </template>
        </el-table-column>
        <el-table-column
          prop="contractStartDate"
          label="合同开始日期"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="contractEndDate"
          label="合同结束日期"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="contractSignDate"
          label="合同签订日期"
          min-width="120"
        ></el-table-column>
        <el-table-column prop="contractStatus" label="合同状态" width="100">
          <template slot-scope="scope">
            {{ scope.row.contractStatus | contractStatus }}
          </template>
        </el-table-column>
        <el-table-column prop="signType" label="签订类型" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.signType | signType }}
          </template>
        </el-table-column>
        <el-table-column
          prop="renewalCount"
          label="续签次数"
          min-width="80"
        ></el-table-column>
        <el-table-column
          prop="signRemark"
          label="合同备注"
          min-width="180"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column prop="contractSignStatus" label="签约状态" width="80">
          <template slot-scope="scope">
            {{ scope.row.contractSignStatus | contractSignStatus }}
          </template>
        </el-table-column>
        <el-table-column
          prop="contractName"
          label="电子合同"
          min-width="200"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span class="table-name" @click="handleView(scope.row)">
              {{ scope.row.contractName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleSign(scope.row)"
              v-show="scope.row.contractSignStatus === 'WATING_SIGN'"
              >发起签约
            </el-button>
            <el-button
              type="text"
              @click="handleDrawer('EDIT', scope.row)"
              v-show="scope.row.contractSignStatus === 'WATING_SIGN'"
              v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.laborContract.edit'
                )
              "
              >编辑
            </el-button>
            <el-dropdown trigger="click">
              <el-button type="text">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-show="
                    scope.row.contractStatus !== 'END' &&
                    scope.row.contractStatus !== 'REMOVE' &&
                    scope.row.contractType !== 'NON_FIXED_TERM' &&
                    scope.row.contractType !== 'CERTAIN_TASK_TERM' &&
                    !scope.row.renewalRecordId
                  "
                  v-if="
                    privilegeVoList.includes(
                      'hrContract.conManage.laborContract.continueSign'
                    )
                  "
                  @click.native="handleDrawer('CONTINUE_SIGN', scope.row)"
                  >续签</el-dropdown-item
                >
                <el-dropdown-item
                  v-show="scope.row.contractStatus === 'EXECUTE'"
                  v-if="
                    privilegeVoList.includes(
                      'hrContract.conManage.laborContract.updateSign'
                    )
                  "
                  @click.native="handleDrawer('UPDATE_SIGN', scope.row)"
                  >变更</el-dropdown-item
                >
                <el-dropdown-item
                  v-show="scope.row.contractSignStatus === 'WATING_SIGN'"
                  v-if="
                    privilegeVoList.includes(
                      'hrContract.conManage.laborContract.delete'
                    )
                  "
                  @click.native="handleDelete(scope.row)"
                  >删除</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </section>

    <footer class="footer" style="padding: 0 20px">
      <div class="block">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[20, 50, 100, 200]"
          :total="total"
        ></el-pagination>
      </div>
    </footer>
    <div>
      <sign-change
        ref="signChange"
        :title="title"
        :contractTypeList="contractTypeList"
        @refresh="getList"
      ></sign-change>
      <!-- 筛选 -->
      <el-dialog
        title="筛选"
        :visible.sync="isShowScreening"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="700px"
        ref="screenForm"
      >
        <el-form ref="searchForm" :model="searchForm" label-width="130px">
          <el-form-item label="合同主体单位">
            <el-select
              v-model="searchForm.contractSubId"
              placeholder="请选择合同主体单位"
              clearable
            >
              <el-option
                v-for="item in contractSubList"
                :key="item.contractSubId"
                :label="item.contractName"
                :value="item.contractSubId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="合同类型">
            <el-select
              v-model="searchForm.contractType"
              placeholder="请选择合同类型"
              clearable
            >
              <el-option
                v-for="(item, index) in contractTypeList"
                :label="item.optionEnumName"
                :value="item.optionEnumCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="合同状态">
            <el-radio-group v-model="searchForm.contractStatus">
              <el-radio-button
                v-for="(item, index) in contractStatusList"
                :label="item.value"
                :key="index"
                >{{ item.label }}</el-radio-button
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item label="签约状态">
            <el-radio-group v-model="searchForm.contractSignStatus">
              <el-radio-button
                v-for="(item, index) in contractSignStatusList"
                :label="item.value"
                :key="index"
                >{{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="签订类型">
            <el-radio-group v-model="searchForm.signType">
              <el-radio-button
                v-for="(item, index) in signTypeList"
                :label="item.value"
                :key="index"
                >{{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="员工状态">
            <el-radio-group v-model="searchForm.empStatus">
              <el-radio-button
                v-for="(item, index) in empStatusList"
                :label="item.value"
                :key="index"
                >{{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="续签次数">
            <el-input
              placeholder="请输入内容"
              v-model="searchForm.renewalCount"
              clearable
              style="width: 220px"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="合同开始日期">
            <el-date-picker
              v-model="searchForm.contractStartDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="合同结束日期">
            <el-date-picker
              v-model="searchForm.contractEndDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="合同签订日期">
            <el-date-picker
              v-model="searchForm.contractSignDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button @click="resetSreen">重置</el-button>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import * as constData from "@/utils/constData";
import signChange from "./components/signChange";
import {
  apiGetContractRecordList,
  apiExportContractRecord,
  apiGetContractSubjectList,
  apiDeleteContractRecord,
  apiGetViewUrl,
} from "./store/api";
import { apiGetEnumByCodeList } from "../staffManage/store/api";
export default {
  components: {
    signChange,
  },
  data() {
    return {
      title: "",
      isShowScreening: false,
      loading: false,
      screenWidth: document.body.clientWidth, // 屏幕尺寸宽度
      screenHeight: document.body.clientHeight - 270, // 屏幕尺寸高度
      total: 0,
      searchForm: {
        key: "",
        currPage: 1,
        pageSize: 20,
        contractSubId: "", //合同主体
        contractType: "", //合同类型
        contractStatus: "", //合同状态
        contractSignStatus: "", //签约状态
        signType: "", //签订类型
        empStatus: "", //员工状态
        renewalCount: "", //续签次数
        contractStartDate: "", //合同开始日期
        contractEndDate: "", //合同结束日期
        contractSignDate: "", //合同签订日期
      },
      contractSubList: [], //合同主体单位
      contractTypeList: [],
      contractStatusList: [{ label: "不限", value: "" }].concat(
        constData.contractStatusList
      ),
      contractSignStatusList: [{ label: "不限", value: "" }].concat(
        constData.contractSignStatusList
      ),
      signTypeList: [{ label: "不限", value: "" }].concat(
        constData.signTypeList
      ),
      empStatusList: [
        { label: "不限", value: "" },
        { label: "在职", value: "ON_THE_JOB" },
        { label: "离职", value: "DIMISSION" },
      ],
      contractState: {
        NEW_SIGN: "新签",
        CONTINUE_SIGN: "续签",
        UPDATE_SIGN: "变更",
        EDIT: "编辑",
      },
      tableData: [],
      selectItems: [],
      selectIds: [],
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
      productEdition: (state) => state.productEdition,
    }),
  },
  created() {
    this.getContractSubjectList();
    this.getList();
  },
  methods: {
    async getList() {
      this.loading = true;
      let res = await apiGetContractRecordList(this.searchForm);
      if (res.success) {
        this.tableData = res.data.records;
        this.total = res.data.total;
      }
      this.loading = false;
    },
    async getContractSubjectList() {
      let res = await apiGetContractSubjectList({ type: "CONTRACT" });
      let res2 = await apiGetEnumByCodeList({ fieldCodes: ["contractType"] });
      if (res.success) {
        this.contractSubList = res.data;
        this.$store.commit("contractManageStore/SET_CONTRACTSUBLIST", res.data);
      }
      if (res2.success) {
        this.contractTypeList = res2.data[0].optionEnums;
      }
    },
    //显示合同类型
    getContractType(val) {
      if (!val) {
        return "";
      }
      let arr = this.contractTypeList.filter(
        (item) => item.optionEnumCode === val
      );
      return arr.length > 0 ? arr[0].optionEnumName : "";
    },
    //显示页数
    handleSizeChange(val) {
      this.searchForm.pageSize = val;
      this.searchForm.currPage = 1;
      this.getList();
    },
    //翻页
    handleCurrentChange(val) {
      this.searchForm.currPage = val;
      this.getList();
    },
    //对合同开始日期、合同结束日期、合同签订日期进行处理
    formatDate() {
      this.searchForm.contractStartDateStart = this.searchForm.contractStartDate
        ? this.searchForm.contractStartDate[0]
        : "";
      this.searchForm.contractStartDateEnd = this.searchForm.contractStartDate
        ? this.searchForm.contractStartDate[1]
        : "";
      this.searchForm.contractEndDateStart = this.searchForm.contractEndDate
        ? this.searchForm.contractEndDate[0]
        : "";
      this.searchForm.contractEndDateEnd = this.searchForm.contractEndDate
        ? this.searchForm.contractEndDate[1]
        : "";
      this.searchForm.contractSignDateStart = this.searchForm.contractSignDate
        ? this.searchForm.contractSignDate[0]
        : "";
      this.searchForm.contractSignDateEnd = this.searchForm.contractSignDate
        ? this.searchForm.contractSignDate[1]
        : "";
    },
    //查询
    handleSearch() {
      this.formatDate();
      this.searchForm.currPage = 1;
      this.isShowScreening = false;
      this.getList();
    },
    //筛选重置
    resetSreen() {
      for (let key in this.searchForm) {
        if (!["key", "currPage", "pageSize"].includes(key)) {
          this.searchForm[key] = "";
        }
      }
      this.handleSearch();
    },
    //导出
    async handleExport() {
      this.formatDate();
      this.searchForm.ids = this.selectIds;
      await apiExportContractRecord(this.searchForm);
    },
    //删除
    async handleDelete(data) {
      let res = await apiDeleteContractRecord(data.id);
      if (res.success) {
        this.$message.success("操作成功");
        this.getList();
      }
    },
    //新签、续签、变更
    handleDrawer(type, data) {
      this.title = this.contractState[type];
      this.$refs.signChange.show(type, data);
    },
    //表格批量选择
    handleSelectionChange(val) {
      this.selectItems = val;
      this.selectIds = [];
      val.map((item) => {
        this.selectIds.push(item.id);
      });
    },
    //查看员工
    handleName(data) {
      this.$router.push({
        path: "/staff-manage/staff-detail",
        query: {
          compEmpId: data.compEmpId,
          empRecordId: data.empRecordId,
          source: "EMPLOYEE",
        },
      });
    },
    //查看电子合同
    async handleView(data) {
      let res = await apiGetViewUrl({ contractId: data.contractId });
      if (res.success) {
        window.open(res.data.url);
      }
    },
    //发起签约
    handleSign(data) {
      if (!this.productEdition.contract) {
        this.$message.warning("请联系客户经理或拨打客服热线开通服务");
        return;
      }
      if (!data.contractSubId) {
        this.$message.warning("请先完善当前记录合同主体单位");
        return;
      }
      this.$store.commit("contractManageStore/SET_CHOOSERECORDDATA", [data]);
      this.$store.commit("contractManageStore/SET_CHOOSESEALSTAFFDATA", null);
      this.$store.commit("contractManageStore/SET_CHOOSETEMPLATEDATA", null);
      this.$router.push({
        path: "/contract-manage/initiate-signing",
        query: { isBatchSign: true },
      });
    },
    //批量发起签约
    handleBatchSign() {
      if (!this.productEdition.contract) {
        this.$message.warning("请联系客户经理或拨打客服热线开通服务");
        return;
      }
      if (this.selectItems.length === 0) {
        this.$message.warning("请先选择数据！");
        return;
      }
      //判断是否有合同主体为空数据
      let hasContractSub = true;
      this.selectItems.forEach((item) => {
        if (!item.contractSubId) {
          hasContractSub = false;
        }
      });
      if (!hasContractSub) {
        this.$message.warning("请先完善当前记录合同主体单位");
        return;
      }
      let flag = true;
      let flag2 = true;
      let taxSubId = this.selectItems[0].taxSubId;
      this.selectItems.map((item) => {
        if (item.taxSubId !== taxSubId) {
          flag = false;
        }
        //判断合同签约状态是否都是未签约
        if (
          item.contractSignStatus === "SIGNING" ||
          item.contractSignStatus === "FINISH"
        ) {
          flag2 = false;
        }
      });
      if (flag && flag2) {
        this.$store.commit(
          "contractManageStore/SET_CHOOSERECORDDATA",
          this.selectItems
        );
        this.$store.commit("contractManageStore/SET_CHOOSESEALSTAFFDATA", null);
        this.$store.commit("contractManageStore/SET_CHOOSETEMPLATEDATA", null);
        this.$router.push({
          path: "/contract-manage/initiate-signing",
          query: { isBatchSign: true },
        });
      } else {
        if (!flag) {
          this.$message.warning("请检查所选记录是否为同一合同主体单位！");
          return;
        }
        if (!flag2) {
          this.$message.warning(
            "请检查所选记录是否存在已签约或者签约中的合同！"
          );
          return;
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.main-title {
  border-bottom: 1px solid #ededed;
}
.nav {
  display: flex;
  justify-content: space-between;
  > div {
    display: flex;
    align-items: center;
  }
}
.footer {
  display: flex;
  justify-content: flex-end;
}
.el-dropdown .el-button {
  padding: 12px 20px;
}
.el-form-item {
  text-align: left !important;
}
.el-pagination {
  margin-top: 10px;
}
.table-con {
  .el-button {
    padding: 0;
  }
  .el-dropdown {
    margin-left: 10px;
  }
}
</style>
