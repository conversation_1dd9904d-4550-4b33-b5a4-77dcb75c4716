import laborContract from '../laborContract';
import electronicContract from '../electronicContract';
import initiateSigning from '../initiateSigning';
import contractTemplate from '../contractTemplate';
import setTemplate from '../setTemplate';
import contractSee from '../contractSee';
import createSucess from '../createSuccess';
import batchSign from '../batchSign';

export default [
  {
    path: '/contract-manage/labor-contract', //劳动合同
    name: 'laborContract',
    component: laborContract,
    meta: {
      businessCode: 'hrContract.conManage.laborContract',
      icon: 'iconhetongguanli',
    },
  },
  {
    path: '/contract-manage/electronic-contract', //电子合同
    name: 'electronicContract',
    component: electronicContract,
    meta: {
      businessCode: 'hrContract.conManage.eContract',
      icon: 'iconhetongguanli',
    },
  },
  {
    path: '/contract-manage/initiate-signing', //发起签约
    name: 'initiateSigning',
    component: initiateSigning,
  },
  {
    path: '/contract-manage/contract-template', //合同模板
    name: 'contractTemplate',
    component: contractTemplate,
    meta: {
      businessCode: 'hrContract.conManage.conTemplate',
      icon: 'iconhetongguanli',
    },
  },
  {
    path: '/contract-manage/set-template', //新增电子合同模板
    name: 'setTemplate',
    component: setTemplate,
  },
  {
    path: '/contract-manage/contract-see', //文件预览
    name: 'contractSee',
    component: contractSee,
  },
  {
    path: '/contract-manage/create-success',
    name: 'createSucess',
    meta: {
      nav: false,
    },
    component: createSucess,
  },

  {
    path: '/contract-manage/batch-sign', //新增电子合同模板
    name: 'batchSign',
    component: batchSign,
  },
];
