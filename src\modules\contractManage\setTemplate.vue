<template>
  <div class="set-template">
    <header class="contract-header main-title">
      <el-row>
        <el-col :span="24">
          <span @click="handleClose" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>{{ titleName }}</span>
        </el-col>
      </el-row>
    </header>
    <section>
      <div class="section">
        <p class="title" v-if="!tempId"><span>上传文件</span><span class="template-tip">（必填）</span></p>
        <el-upload
          v-if="!tempId"
          drag
          :action="uploadUrl"
          :headers="heanderToken"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
          :file-list="fileList"
          class="template-upload"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            <p>拖拽文件到这里上传</p>
          </div>
          <div class="el-upload__text">
            <el-button type="primary" style="margin-top: 10px"
            >{{ isUploadFile ? '重新上传' : '选择文件' }}</el-button>
          </div>
          <div class="el-upload__text">
            <p><i class="el-icon-warning-outline el-icon-special"></i>仅支持pdf且文件总大小不超过5MB</p>
          </div>
        </el-upload>
        <p class="title"><span>基本信息</span></p>
        <el-form class="form-style" :model="form" ref="form" :rules="rule" label-width="130px">
          <el-form-item label="模板名称" prop="templateName">
            <el-input
              v-model.trim="form.templateName"
              placeholder="请输入模板名称"
              @input="debounce('handleCheckName')"
              maxlength="64"
            ></el-input>
          </el-form-item>
          <el-form-item label="模板类型" prop="templateType">
            <el-radio-group v-model="form.templateType">
              <el-radio label="LABOUR_CONTRACT" :disabled="templateTypeFlag"
                >劳动合同</el-radio
              >
              <el-radio label="PROVE">证明</el-radio>
              <el-radio label="RULES">规章制度</el-radio>
              <el-radio label="OTHERS">其他</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="taxSubId">
            <template slot="label">
              <span>模板适用范围</span>
              <el-tooltip
                content="个人签署方只能选择该模板适用公司范围内员工"
                placement="top-start"
              >
                <img src="@/assets/images/help.png" alt="">
              </el-tooltip>
            </template>
            <el-select
              v-model="form.taxSubId"
              placeholder="请选择公司"
              multiple
              clearable
            >
              <el-option
                v-for="item in employeeSubList"
                :key="item.contractSubId"
                :label="item.contractName"
                :value="item.contractSubId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <config-template-process
          ref="configTemplateProcess"
          :baseForm="form"
          :tempId="tempId"
          :isReadonly="form.templateType === 'LABOUR_CONTRACT'"
          :updateFlowStep="updateFlowStep"
        ></config-template-process>
      </div>
<!--      <iframe-->
<!--        id="iframeId"-->
<!--        :src="linkUrl"-->
<!--        frameborder="0"-->
<!--        scrolling="auto"-->
<!--        style="width: 100%; height: calc(100vh - 50px)"-->
<!--      >-->
<!--      </iframe>-->
    </section>
    <footer class="template-footer">
      <el-row type="flex">
        <el-col :span="24">
          <el-button class="closeBtn" @click="handleClose"
          >取消</el-button>
          <el-button
            type="primary"
            class="next-step"
            :loading="nextLoading"
            @click="handleNext"
          >下一步</el-button>
        </el-col>
      </el-row>
    </footer>
  </div>
</template>

<script>
const environmentConfig = window.env.environmentConfig;
import { mapState } from "vuex";
import configTemplateProcess from "./components/configTemplateProcess";
import { baseUrl } from "@/request/fetch";
import { getToken } from "@olading/olading-business-ui";
import {
  apiCreateContractTemplate,
  // apiUploadTemplateFile,
  apiGetTemplateDetail,
  apiCheckTempName,
  // apiSetTemplate,
} from "./store/api";
export default {
  name: 'setTemplate',
  components: {
    configTemplateProcess,
  },
  data() {
    let validateSpace = async (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入模板名称'));
      }
      if (value.indexOf(' ') > -1) {
        this.form.templateName = value.replace(/\s+/g, '');
      }
      callback();
    };
    return {
      templateTypeFlag: false,
      tempName: '',
      updateFlowStep: false,
      uploadUrl: baseUrl + "/api/hrsaas-emp/archive/upload",
      isEdit: this.$route.query.isEdit,
      tempId: this.$route.query.tempId,
      tempStatus: this.$route.query.tempStatus,
      form: {
        templateName: "",
        templateType: "LABOUR_CONTRACT",
        taxSubId: [],
      },
      rule: {
        templateName: [
          {
            required: true,
            validator: validateSpace,
            trigger: "change",
          },
        ],
        taxSubId: {
          required: true,
          message: "请选择公司",
          trigger: "change",
        },
      },
      checkNameSuccess: false,
      fileList: [],
      heanderToken: {
        Authorization: getToken(),
      },
      archiveId: "", //当前文件id
      serverUrl: "",
      nextLoading: false,
      processStep: {},
      titleName: '新增合同模板',
      isUploadFile: false
    };
  },
  computed: {
    ...mapState("contractManageStore", {
      employeeSubList: "employeeSubList",
      flowStep: "flowStep",
    }),
  },
  beforeDestroy() {
    if(this.timer) clearTimeout(this.timer);
  },
  watch: {
    flowStep() {
      this.processStep = this.flowStep;
    },
    'form.templateType': {
      handler(val) {
        if (val === 'LABOUR_CONTRACT') {
          this.updateFlowStep = true;
        } else {
          this.updateFlowStep = false
        }
      }
    }
  },
  created() {
    if (this.tempId) {
      this.storageTitle('编辑合同模板')
      this.getTemplateDetail();
    } else {
      this.storageTitle('新增合同模板')
    }
  },
  mounted() {
    this.serverUrl = environmentConfig.salary ? environmentConfig.salary : `http://${window.location.host}`;
  },
  methods: {

    // 合同管理存储标题
    storageTitle(title) {
      this.titleName = title;
      this.$store.commit('contractManageStore/SET_KEY', {
        key: 'titleName',
        value: title
      })
    },

    async getTemplateDetail() {
      let res = await apiGetTemplateDetail({ tempId: this.tempId });
      if (res.success) {
        let data = res.data;
        //设置流程子组件的回显
        if (!data.steps.filter(v => v.operate === 'SEAL').length
         || !data.steps.filter(v => v.operate === 'SIGN').length) {
          this.templateTypeFlag = true;
        }
        this.$refs.configTemplateProcess.flowStep.steps = data.steps;
        this.$refs.configTemplateProcess.flowStep.carbonCopyList =
          data.carbonCopyList;
        this.$refs.configTemplateProcess.initStepData();
        this.$store.commit(
          "contractManageStore/SET_FLOWSTEP",
          this.$refs.configTemplateProcess.flowStep
        );
        for (let key in this.form) {
          if (key === 'templateName') {
            this.tempName = data[key];
          }
          this.form[key] = data[key];
        }
      }
    },

    //保存上传
    // async handleUpload() {
    //   this.nextLoading = true;
    //   let res = await apiUploadTemplateFile({
    //     archiveId: this.archiveId,
    //     tempId: this.tempId,
    //   });
    //   if (res.success) {
    //     apiSetTemplate(this.tempId)
    //       .then(() => {
    //         // this.linkUrl = `${response.data.url}?token=${response.data.token}&callback=${this.serverUrl}/contract-manage/create-success?token=${response.data.token}`;
    //         this.$router.push('/templateEdit')
    //         this.nextLoading = false;
    //       })
    //       .finally(() => {
    //         this.nextLoading = false;
    //       })
    //   }
    // },
    debounce(fn) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null;
      }
      this.timer = setTimeout(() => {
        this[fn]()
      }, 500)
    },

    //校验模板名称是否已存在
    async handleCheckName() {
      if (this.tempName !== this.form.templateName) {
        this.checkNameSuccess = false;
        let res = await apiCheckTempName({
          templateName: this.form.templateName,
        });
        if (res.success) {
          this.checkNameSuccess = true;
        }
      }
    },

    //创建模板
    async handleCreateTemplate() {
      if (this.fileList.length === 0 && !this.tempId) {
        this.$message.warning("请上传文件");
        return;
      }
      if (!this.checkNameSuccess && this.tempName !== this.form.templateName) {
        this.$message.error("模板名称不可重复");
        return;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          //判断是否有签署流程
          let flowStep = this.processStep;
          if (flowStep.steps.length === 0) {
            this.$message.warning("请配置签署流程");
            return;
          }
          let data = { ...this.form };
          data.tempId = this.tempId;
          data.steps = flowStep.steps;
          data.carbonCopyList = flowStep.carbonCopyList;
          data.steps.map((item, index) => {
            item.sortby = index;
          });
          data.carbonCopyList.map((item, index) => {
            item.sortby = index;
          });
          data.archiveId = this.archiveId;
          this.nextLoading = true;
          apiCreateContractTemplate(data)
            .then((res) => {
              if (res.success) {
                // this.handleUpload();
                this.tempId = res.data.data;
                this.$store.commit("contractManageStore/SET_KEY", {
                  key: 'tempId',
                  value: this.tempId
                })

                this.$store.commit("contractManageStore/SET_KEY", {
                  key: 'tempStatus',
                  value: this.tempStatus
                })
                this.$router.replace('/templateEdit');
              }})
            .finally(() => {
              this.nextLoading = false;
            })
          // let fillField = this.$refs.configTemplateProcess.$refs.fillField;
          // if (fillField.isEditFlag || fillField.isAddFlag) {
          //   this.$message.warning("请保存填充域设置");
          //   return;
          // }
          // this.$confirm(
          //   `确定保存当前模板信息吗？<p style="color: #999">跳转至下一步后已编辑的信息将无法修改</p>`,
          //   "提示",
          //   {
          //     confirmButtonText: "确定",
          //     cancelButtonText: "取消",
          //     type: "warning",
          //     closeOnClickModal: false,
          //     closeOnPressEscape: false,
          //     dangerouslyUseHTMLString: true,
          //   }
          // ).then(() => {
          //
          // });
        } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },

    //返回
    handleClose() {
      const h = this.$createElement;
      this.$msgbox({
        title: '提示',
        message: h('div', null, [
          h('div',{style:'display:flex;align-items:center;height:30px;'},[
            h('i', {class: 'el-icon-warning',style:'font-size:16px;color: #FF9500;margin-right: 5px'}),
            h('span',{style:'padding-right:12px;font-size:14px;'},'确定退出编辑模板吗？')
          ]),
          h('p', { style: 'color: #999;padding-left:21px;' }, '退出后已编辑的信息不保存,直接返回模板管理')
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        this.$router.go(-1);
      });
      // this.$confirm(
      //   `确定退出编辑模板吗？<p style="color: #999">退出后已编辑的信息不保存,直接返回模板管理</p>`,
      //   "提示",
      //   {
      //     confirmButtonText: "确定",
      //     cancelButtonText: "取消",
      //     type: "warning",
      //     closeOnClickModal: false,
      //     closeOnPressEscape: false,
      //     dangerouslyUseHTMLString: true,
      //   }
      // ).then(() => {
      //   this.$router.go(-1);
      // });
    },
    beforeUpload(file) {
      //限制上传文件
      const isPdf = file.type.toUpperCase() === "APPLICATION/PDF";
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message({
          message: "上传文件大小不能超过5MB!",
          type: "warning",
        });
      }
      if (!isPdf) {
        this.$message({
          message: "文件格式不正确，请上传pdf格式文件",
          type: "warning",
        });
      }
      return isLt5M && isPdf;
    },

    // 移除文件
    handleRemove() {
      this.fileList = [];
      this.isUploadFile = false
    },

    handleSuccess(res) {
      this.fileList = [];
      if (res.success) {
        let data = res.data;
        this.archiveId = data.archiveId;
        this.fileList.push(res.data);
        this.isUploadFile = true
        this.form.templateName = data.name.replace(new RegExp('\\.pdf$','gi'), '');
        this.$nextTick(() => {
          this.handleCheckName();
        })
      } else {
        this.$message.warning(res.message);
      }
    },

    // 下一步
    handleNext() {
      this.handleCreateTemplate();
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../assets/scss/helpers.scss";
.set-template {
  position: relative;
}
.template-footer .el-col {
  display: flex;
  justify-content: center;
}
.form-style {
  /deep/ .el-input {
    width: 300px;
  }
}
.template-tip {
  font-size: 16px;
  color: #888888;
  font-weight: normal;
}
.main-title {
  .el-col-12:last-child {
    text-align: left;
    padding-right: 10px;
  }
  .header-title {
    display: flex;
    align-items: center;
    color: #070F29;
    font-size: 16px;
    .title-line {
      display: inline-block;
      width: 1px;
      height: 20px;
      margin: 0 30px;
      background-color: #ffffff;
    }
  }
  .next-step {
    font-size: 14px;
    font-weight: normal;
    width: 84px;
    height: 40px;
    border-radius: 4px;
  }
  .closeBtn {
    font-size: 16px;
  }
}
.section {
  padding: 0 15%;
  overflow: auto;
  height: calc(100vh - 200px);
}
.title {
  font-size: 16px;
  margin: 20px 0;
  display: flex;
  font-weight: bold;
  align-items: center;
}
.title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 14px;
  background-color: $mainColor;
  border-radius: 3px;
  margin-right: 8px;
}
.template-upload {
  width: 100%;
  /deep/ {
    .el-upload {
      width: 100%;
      .el-upload-dragger {
        width: 100%;
        height: auto;
        .el-icon-upload {
          margin: 36px auto 20px;
        }
        .el-upload__text {
          margin-bottom: 20px;
          p {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #888;
          }
          .el-icon-special {
            color: #9EA5BD;
            margin-right: 5px;
            font-size: 17px;
          }
          &:last-of-type {
            margin-bottom: 36px;
          }
        }
      }
    }
  }
}
.el-input {
  width: 200px;
}
.close-btn {
  position: absolute;
  right: 88px;
  top: 11px;
}
.el-button--text {
  font-size: 14px;
}
::v-deep {
  .el-form-item__label {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    img {
      margin-left: 5px;
      width: 16px;
      height: 16px;
    }
  }
  .el-upload-list__item {
    transition: none !important;
  }
  .el-form-item__label {
    color: #888;
  }
  .el-radio__label {
    color: #070F29 !important;
  }
}
</style>
