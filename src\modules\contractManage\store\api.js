import { fetch, fetchFile } from 'request/fetch';

//获取劳动合同记录
export function apiGetContractRecordList(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractRecord/contractRecordList',
    method: 'POST',
    data,
  });
}
//劳动合同导出
export function apiExportContractRecord(data) {
  return fetchFile({
    url: '/api/hrsaas-emp/contractRecord/export',
    method: 'POST',
    data,
  });
}
//合同主体单位列表
export function apiGetContractSubjectList(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/contractSubjectList',
    method: 'GET',
    params: data,
  });
}
//新签
export function apiContractNewSign(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractRecord/newSign',
    method: 'POST',
    data,
  });
}
//续签
export function apiContractContinueSign(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractRecord/continueSign',
    method: 'POST',
    data,
  });
}
//变更
export function apiContractUpdateSign(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractRecord/updateSign',
    method: 'POST',
    data,
  });
}
//编辑
export function apiContractEditSign(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractRecord/editSign',
    method: 'POST',
    data,
  });
}
//劳动合同-删除
export function apiDeleteContractRecord(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractRecord/deleteRecord',
    method: 'POST',
    data,
  });
}
//查看文件
export function apiGetViewUrl(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/view',
    method: 'POST',
    data,
  });
}
//校验合同编号唯一性
export function apiCheckContractNo(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractRecord/checkContractNo',
    method: 'POST',
    data,
  });
}
//发起人
export function apiSignSponsorList(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/signSponsorList',
    method: 'POST',
    data,
  });
}

//企业签署方
export function apiContractSealUserList(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/contractSignUserList',
    method: 'POST',
    data,
  });
}

//模糊查询部门和成员，把企业签署方的接口换成这个
export function apiQueryDeptMember(data) {
  return fetch({
    url: '/api/merchant/dept/dept-Member-query',
    method: 'POST',
    data,
  });
}
//个人签署方
export function apiContractSignUserList(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/contractSignEmployeeList',
    method: 'POST',
    data,
  });
}
//查询所有员工
export function apiGetALLEmp(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/getAllEmp',
    method: 'POST',
    data,
  });
}
//创建模板
export function apiCreateContractTemplate(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/createTemplate',
    method: 'POST',
    data,
  });
}
//创建模板
export function apiCheckTempName(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/checkTempName',
    method: 'POST',
    data,
  });
}
//保存上传的模板文件
export function apiUploadTemplateFile(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/uploadTemplateFile',
    method: 'POST',
    data,
  });
}
//合同模板查询
export function apiGetContractList(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/selectEmplist',
    method: 'POST',
    data,
  });
}
//修改模板状态
export function apiUpdateEmpStatus(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/updateEmpStatus',
    method: 'POST',
    data,
  });
}
//获取模板详情
export function apiGetTemplateDetail(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/getTemplateDetail',
    method: 'GET',
    params: data,
  });
}
//模板设置
export function apiSetTemplate(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/setTemplate',
    method: 'POST',
    data,
  });
}

//电子合同记录查询
export function apiGetElcontractList(data) {
  return fetch({
    url: '/api/hrsaas-emp/elcontract/v1/getElContract',
    method: 'POST',
    data,
  });
}

/**===================  电子合同批量接口  by.瑞萍 ===================*/

//合同类目统计
export function apiGetCountContractItem(data) {
  return fetch({
    url: '/api/hrsaas-emp/elcontract/v1/countContractItem',
    method: 'POST',
    data,
  });
}

//电子合同批量导出校验
export function apiExportContractCheckdata(data) {
  return fetch({
    url: '/api/hrsaas-emp/elcontract/v1/exportContractCheck',
    method: 'POST',
    data,
  });
}

//电子合同批量导出
export function apiExportContract(data) {
  return fetchFile({
    url: '/api/hrsaas-emp/elcontract/v1/exportContract',
    method: 'POST',
    data,
  });
}

//电子合同批量导出取消

export function apiExportContractCancel(data) {
  return fetch({
    url: '/api/hrsaas-emp/elcontract/v1/exportContractCancel',
    method: 'POST',
    data,
  });
}

//批量签署签章查询
export function getPhotosByContract(data) {
  return fetch({
    url: '/api/hrsaas-emp/signphoto/getPhotosByContract',
    method: 'POST',
    data,
  });
}

//批量签署确认
export function getBatchSignConfirm(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/batchSignConfirm',
    method: 'POST',
    data,
  });
}

//批量签署发送短信
export function batchSignPrepare(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/batchSignPrepare',
    method: 'POST',
    data,
  });
}

/**===================  end ===================*/

//合同模板查询
export function apiGetContractTemplate(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/getContractTemplate',
    method: 'POST',
    data,
  });
}
//合同记录查询
export function apiGetSignContractRecord(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/getEmpContract',
    method: 'POST',
    data,
  });
}
//发起签约
export function apiStartSignContract(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/signInit',
    method: 'POST',
    data,
  });
}
//批量发起提醒
export function apiStartRemind(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/remind',
    method: 'POST',
    data,
  });
}
//签署
export function apiStartSign(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/sign',
    method: 'POST',
    data,
  });
}
//撤回
export function apiStartBack(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/back',
    method: 'POST',
    data,
  });
}
//废弃
export function apiStartAbandon(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/abandon',
    method: 'POST',
    data,
  });
}
//删除
export function apiStartDelete(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/delete',
    method: 'POST',
    data,
  });
}
//点子合同-导出
export function apiExportElecontract(data) {
  return fetchFile({
    url: '/api/hrsaas-emp/elcontract/v1/contract_export',
    method: 'POST',
    data,
  });
}

//查看签署流程
export function apiGetSignProcess(data) {
  return fetch({
    url: '/api/hrsaas-emp/elcontract/v1/getContractDetail',
    method: 'POST',
    data,
  });
}

//查看人员合同记录文件
export function apiGetPersonalContractFile(data) {
  return fetch({
    url: '/api/hrsaas-emp/elcontract/v1/getPersonalContractFile',
    method: 'POST',
    params: data,
  });
}

//查询关联信息项值
export function apiGetRelationValue(data) {
  return fetch({
    url: '/api/hrsaas-emp/sign/v1/queryRelationValue',
    method: 'POST',
    data,
  });
}

//获取应用id
export function apiGetAppId(code) {
  return fetch({
    url: '/api/hrsaas-emp/contractRecord/getAppId/' + code,
    method: 'GET',
  });
}

// 查询合同模板是否可编辑
export function apiCanUpdate(data) {
  return fetch({
    url: '/api/hrsaas-emp/contractTemplate/canUpdate',
    method: 'GET',
    params: data,
  });
}
