import * as actions from './actions';
import mutations from './mutations';
import * as getters from './getters';

export const contractManageStore = {
  namespaced: true,
  state: {
    chooseSealStaffData: null, //当前选择的企业签署方
    chooseSignStaffData: [], //当前选择的个人签署方
    chooseCopyStaffData: null, //当前选择的抄送人
    chooseTemplateData: null, //当前选择的模板
    chooseRecordData: [], //当前选择的合同记录
    flowStep: {},
    tempId: null, // 模板ID
    titleName: '', // 新增合同模板标题
    contractSubList: [], //合同主体
    employeeSubList: [], //用工主体
    relationList: [], //填充域关联信息项
    contractTypeList: [], //合同类型
    contractFile: {
      labourArchives: [],
      otherArchives: [],
    },
    batchSignList: [], // 批量签署的文件id
  },
  actions,
  mutations,
  getters,
};
