import * as AT from './actionTypes';

export default {
  [AT.SET_CHOOSESEALSTAFFDATA](state, value) {
    state.chooseSealStaffData = value;
  },
  [AT.SET_CHOOSESIGNSTAFFDATA](state, value) {
    state.chooseSignStaffData = value;
  },
  [AT.SET_CHOOSECOPYSTAFFDATA](state, value) {
    state.chooseCopyStaffData = value;
  },
  [AT.SET_CHOOSETEMPLATEDATA](state, value) {
    state.chooseTemplateData = value;
  },
  [AT.SET_CHOOSERECORDDATA](state, value) {
    state.chooseRecordData = value;
  },
  [AT.SET_FLOWSTEP](state, value) {
    state.flowStep = value;
  },
  [AT.SET_CONTRACTSUBLIST](state, value) {
    state.contractSubList = value;
  },
  [AT.SET_EMPLOYEESUBLIST](state, value) {
    state.employeeSubList = value;
  },
  [AT.SET_RELATIONLIST](state, value) {
    state.relationList = value;
  },
  [AT.SET_CONTRACTTYPELIST](state, value) {
    state.contractTypeList = value;
  },
  [AT.SET_CONTRACTFILE](state, value) {
    state.contractFile = value;
  },
  [AT.SET_KEY](state, obj) {
    state[obj.key] = obj.value;
  },
  //批量签署的文件id集合
  [AT.SET_BATCHSIGN](state, value) {
    state.batchSignList = value;
  },
};
