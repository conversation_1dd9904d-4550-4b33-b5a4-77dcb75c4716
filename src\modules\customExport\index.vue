<template>
  <div class="custom-export">
    <header>
      <span @click="$router.go(-1)" class="back-style">返回</span>
      <span class="header-line">|</span>
      <span class="header-title">自定义导出</span>
    </header>
    <el-main v-loading="loading">
      <el-select
        v-model="reportId"
        placeholder="请选择"
        @change="changeReport"
        filterable
        clearable
      >
        <div v-loading="reportLoading">
          <el-option
            v-for="(item, index) in reportList"
            :key="item.id"
            :label="item.formName"
            :value="item.id"
          >
            <div
              style="display: flex; align-items: center"
              @mouseover="handleMouse(item, index, true)"
              @mouseout="handleMouse(item, index, false)"
            >
              <span
                style="
                  width: 440px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  display: inline-block;
                "
                :title="item.formName"
                >{{ item.formName }}</span
              >
              <i
                class="el-icon-circle-close"
                style="color: #9ea5bd; font-size: 16px; margin-left: 10px"
                v-show="
                  item.formType !== 'PERSONAL_DEFAULT' && item.isShowDelete
                "
                @click.stop="handleDeleteReport(item.id)"
              ></i>
            </div>
          </el-option>
        </div>
      </el-select>
      <div class="select-con">
        <div class="select-con-title">
          <span class="title-line"></span>
          <span class="title-text">选择要导出的字段</span>
          <el-checkbox
            v-model="isCheckAll"
            :indeterminate="isIndeterminate"
            @change="handleClickAll"
          >
            全选
          </el-checkbox>
        </div>
        <div style="overflow: hidden">
          <div
            v-for="(item, index) in list"
            :key="item.groupCode"
            class="select-list"
          >
            <div class="group-name">
              <el-checkbox
                :indeterminate="item.isIndeterminate"
                v-model="item.checkAll"
                @change="(val) => handleCheckAll(val, index)"
                v-if="item.fields.length > 0"
              >
                {{ item.groupName }}
              </el-checkbox>
            </div>
            <div class="group-item">
              <el-checkbox-group v-model="item.checkedList">
                <el-checkbox
                  v-for="(itx, idx) in item.fields"
                  :key="itx.fieldCode"
                  :label="itx"
                  :disabled="itx.fieldCode === 'empName' ? true : false"
                  @change="(val) => handelCheckItem(val, index, idx)"
                >
                  {{ itx.fieldName }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
        <div class="select-con-title">
          <span class="title-line"></span>
          <span class="title-text">选择要导出的多条记录</span>
        </div>
        <div style="color: #6a6f7f; margin-bottom: 16px">
          <i class="el-icon-info" style="color: #6a6f7f" />
          以下信息每个员工可能会有一条或多条记录，如需导出可勾选，导出时每类信息分一个工作表
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <el-image :src="tipImgUrl"></el-image>
            </div>
            <span class="table-name">图例</span>
          </el-tooltip>
        </div>
        <el-table
          ref="mulTable"
          border
          :data="tableData"
          :header-cell-style="{ background: '#F1F1F1' }"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column
            prop="groupName"
            label="工作表分类"
            width="150"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column prop="fields" label="字段"></el-table-column>
        </el-table>
      </div>
    </el-main>
    <footer>
      <p>
        导出
        <span class="num">{{ empNum }}</span>
        人 | 已选
        <span class="num"> {{ selectList.length }} </span>表头和其他档案
        <span class="num">{{ multiDataGroup.length }}</span>
        个
      </p>
      <div>
        <el-button @click="handleShowCommonReport">保存为常用表格</el-button>
        <el-button type="primary" @click="handleShowExport">
          排序并导出
        </el-button>
      </div>
    </footer>
    <!-- 保存为常用表格弹窗 -->
    <el-dialog
      title="保存为常用表格"
      :visible.sync="isShowDialog"
      @closed="handleClosed"
      width="620px"
    >
      <div>
        <p class="info">
          <i
            class="el-icon-info"
          />将所选字段保存为常用表格，保存后将出现在顶部的下拉选择中
        </p>
        <div class="display-flex save-mode">
          保存方式
          <div>
            <el-radio v-model="saveMode" label="1">新增常用表格</el-radio>
            <el-input
              v-show="saveMode === '1'"
              v-model.trim="reportObj.formName"
              placeholder="请输入常用表格名称"
              maxlength="30"
            ></el-input>
            <el-radio v-model="saveMode" label="2">更新常用表格</el-radio>
            <el-select
              v-show="saveMode === '2'"
              v-model="reportObj.id"
              placeholder="请选择"
              @change="changeSaveReport"
              filterable
              clearable
            >
              <el-option
                v-for="item in reportList2"
                :key="item.id"
                :label="item.formName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="isShowDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveReport">确定</el-button>
      </div>
    </el-dialog>
    <!-- 排序并导出 -->
    <el-dialog
      title="排序并导出"
      :visible.sync="isShowExport"
      :destroy-on-close="true"
      width="620px"
    >
      确认表头
      <p style="display: inline-block; color: #555">
        （可<span class="num">拖拽</span>标签调整顺序）
      </p>
      <draggable
        v-model="selectList"
        forceFallback="true"
        animation="150"
        filter=".disabled-item"
      >
        <transition-group>
          <div
            class="item"
            v-for="item in selectList"
            :key="item.fieldCode"
            :class="item.fieldCode === 'empName' ? 'disabled-item' : ''"
          >
            <span>{{ item.fieldName }}</span>
          </div>
        </transition-group>
      </draggable>
      <div slot="footer">
        <el-button @click="isShowExport = false">取消</el-button>
        <el-button type="primary" @click="handleExport">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import draggable from "vuedraggable";
import tipImgUrl from "@/assets/images/export_example.png";
import {
  apiGetTemplate,
  apiGetReports,
  apiSaveHeader,
  apiExportEntry,
} from "../staffManage/store/api";
import { apiDeleteReport } from "./store/api";
export default {
  components: { draggable },
  data() {
    return {
      loading: false,
      reportLoading: false,
      empNum:
        this.$route.query && this.$route.query.num ? this.$route.query.num : 0,
      reportList: [], //常用表格列表
      reportList2: [], //常用表格列表（不好含默认表格）
      reportId: "", //表格id
      reportObj: {
        id: "",
        formName: "",
        formType: "",
      },
      isCheckAll: false,
      isIndeterminate: true,
      list: [],
      tableData: [],
      multiDataGroup: [], //选中的多条分组
      isShowDialog: false, //保存为常用表格弹窗
      saveMode: "1", //保存方式
      selectList: [], //选择的字段
      isShowExport: false, //排序并导出弹窗
      tipImgUrl: tipImgUrl,
    };
  },
  computed: {
    ...mapState("staffManageStore", {
      searchCondition: "searchCondition",
    }),
  },
  created() {
    this.getList();
    this.getReports();
  },
  methods: {
    async getList() {
      this.loading = true;
      let res = await apiGetTemplate();
      if (res.success) {
        //单条分组（用工和任职是多条分组但是只保留有效数据，所以放在单条分组中）
        this.list = res.data.filter(
          (item) =>
            !item.addMultipleYn ||
            item.groupCode === "EMPLOYEE_INFO" ||
            item.groupCode === "POSITION_INFO"
        );
        this.list.forEach((item, index) => {
          item.checkedList = [];
          if (index === 0) {
            item.fields.forEach((i) => {
              if (i.fieldCode === "empName") {
                item.checkedList.push(i);
              }
            });
          }
          item.isIndeterminate = item.groupCode === "BASIC_INFO" ? true : false;
          item.checkAll = false;
        });
        //多条分组
        let multipleList = res.data.filter(
          (item) =>
            item.addMultipleYn &&
            item.groupCode !== "EMPLOYEE_INFO" &&
            item.groupCode !== "POSITION_INFO"
        );
        multipleList.forEach((item) => {
          let arr = [];
          item.fields.forEach((itx) => {
            arr.push(itx.fieldName);
          });
          let fields = arr.join("、");
          this.tableData.push({
            groupName: item.groupName,
            groupCode: item.groupCode,
            fields,
          });
        });
        //默认勾选姓名
        this.selectList.push(this.list[0].fields[0]);
      }
      this.loading = false;
    },
    //获取常用表格
    async getReports() {
      this.reportLoading = true;
      let res = await apiGetReports();
      if (res.success) {
        this.reportList = res.data;
        this.reportList.forEach((item) => {
          item.isShowDelete = false;
        });
        this.reportList2 = res.data.filter(
          (item) => item.formType === "PERSONAL_FORM"
        );
      }
      this.reportLoading = false;
    },
    //切换常用表格
    changeReport(val) {
      let singleDataFields = [];
      let singleDataFieldsParam = [];
      let multiDataGroup = [];
      this.reportList.forEach((item) => {
        if (item.id === val) {
          singleDataFields = item.singleDataFields ? item.singleDataFields : [];
          singleDataFieldsParam = item.singleDataFieldsParam
            ? item.singleDataFieldsParam
            : [];
          multiDataGroup = item.multiDataGroup ? item.multiDataGroup : [];
        }
      });
      //单条回显
      this.list.forEach((item) => {
        item.checkedList = [];
        item.checkAll = true;
        if (item.fields.length > 0) {
          item.fields.forEach((itx) => {
            let findItem = singleDataFields.find((i) => i === itx.fieldCode);
            if (findItem) {
              item.checkedList.push(itx);
            } else {
              item.checkAll = false;
            }
          });
        }
        if (item.checkedList.length > 0) {
          item.isIndeterminate =
            item.checkedList.length === item.fields.length ? false : true;
        } else {
          item.isIndeterminate = false;
        }
      });
      //多条回显
      this.$nextTick(() => {
        this.tableData.forEach((row) => {
          if (multiDataGroup.indexOf(row.groupCode) >= 0) {
            this.$refs.mulTable.toggleRowSelection(row, true);
          }
        });
      });
      this.selectList = JSON.parse(JSON.stringify(singleDataFieldsParam));
      this.multiDataGroup = multiDataGroup;
    },
    //删除常用表格
    handleDeleteReport(id) {
      this.$confirm("删除后，该表格不可使用", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        apiDeleteReport(id).then((res) => {
          if (res.success) {
            this.$message.success("删除成功");
            this.getReports();
          }
        });
      });
    },
    handleMouse(item, index, flag) {
      item.isShowDelete = flag;
      this.$set(this.reportList, index, item);
    },
    //全选
    handleClickAll(val) {
      this.list.forEach((item) => {
        item.checkedList = val ? item.fields : [];
        item.isIndeterminate = val
          ? true
          : item.groupCode === "BASIC_INFO"
          ? true
          : false;
        item.checkAll = val ? true : false;
      });
      if (val) {
        this.list.forEach((item) => {
          if (item.fields.length > 0) {
            item.fields.forEach((itx) => {
              let findItem = this.selectList.find(
                (i) => i.fieldCode === itx.fieldCode
              );
              if (!findItem) {
                this.selectList.push(itx);
              }
            });
          }
        });
      } else {
        this.selectList = [];
        this.selectList.push(this.list[0].fields[0]);
      }
    },
    //分组全选
    handleCheckAll(val, index) {
      if (val) {
        this.list[index]["fields"].forEach((item) => {
          if (item.fieldCode !== "empName") {
            let findItem = this.selectList.find(
              (itx) => itx.fieldCode === item.fieldCode
            );
            if (!findItem) {
              this.selectList.push(item);
            }
          }
        });
      } else {
        this.list[index]["fields"].forEach((item) => {
          let findIndex = this.selectList.findIndex(
            (itx) => itx.fieldCode === item.fieldCode
          );
          if (findIndex !== -1 && item.fieldCode !== "empName") {
            this.selectList.splice(findIndex, 1);
          }
        });
      }
      //checkedList为了控制分组全选图标显示（默认勾选姓名）
      this.list[index]["checkedList"] = val
        ? this.list[index]["fields"]
        : index === 0
        ? [this.list[index]["fields"][0]]
        : [];
      if (val) {
        this.list[index]["checkedList"] = this.list[index]["fields"];
      } else {
        if (index === 0) {
          this.list[index]["fields"].forEach((i) => {
            if (i.fieldCode === "empName") {
              this.list[index]["checkedList"].push(i);
            }
          });
        } else {
          this.list[index]["checkedList"] = [];
        }
      }
      this.list[index]["isIndeterminate"] = false;
    },
    //单个选择
    handelCheckItem(val, index, idx) {
      //数据处理
      let checkItem = this.list[index]["fields"][idx];
      if (val) {
        this.selectList.push(checkItem);
      } else {
        this.selectList.forEach((item, index) => {
          if (item.fieldCode === checkItem.fieldCode) {
            this.selectList.splice(index, 1);
          }
        });
      }
      //控制按钮显示
      let checkedCount = this.list[index]["checkedList"].length;
      this.list[index]["checkAll"] =
        checkedCount === this.list[index]["fields"].length;
      this.list[index]["isIndeterminate"] =
        checkedCount > 0 && checkedCount < this.list[index]["fields"].length;
    },
    //表格多选
    handleSelectionChange(val) {
      this.multiDataGroup = [];
      val.forEach((item) => {
        this.multiDataGroup.push(item.groupCode);
      });
    },
    //切换保存的常用表格
    changeSaveReport(val) {
      this.reportList2.forEach((item) => {
        if (item.id === val) {
          let { formName, formType } = item;
          this.reportObj.formName = formName;
          this.reportObj.formType = formType;
        }
      });
    },
    //保存为常用表格
    handleShowCommonReport() {
      this.isShowDialog = true;
    },
    //保存
    async handleSaveReport() {
      if (this.saveMode === "1" && !this.reportObj.formName) {
        this.$message.warning("请输入常用表格名称");
        return;
      }
      if (this.saveMode === "2" && !this.reportObj.id) {
        this.$message.warning("请选择常用表格");
        return;
      }
      let singleDataFields = [];
      this.selectList.forEach((item) => {
        singleDataFields.push(item.fieldCode);
      });
      //区分是新增还是更新
      this.reportObj.formType =
        this.saveMode === "1" ? "PERSONAL_FORM" : this.reportObj.formType;
      this.reportObj.id = this.saveMode === "1" ? "" : this.reportObj.id;
      let res = await apiSaveHeader({
        ...this.reportObj,
        singleDataFields,
        multiDataGroup: this.multiDataGroup,
      });
      if (res.success) {
        this.isShowDialog = false;
        this.$message.success("保存成功");
        this.getReports();
      }
    },
    //关闭保存常用表格弹窗
    handleClosed() {
      this.saveMode = "1";
      this.reportObj.id = "";
      this.reportObj.formName = "";
      this.reportObj.formType = "";
    },
    //排序并导出
    handleShowExport() {
      this.isShowExport = true;
    },
    //导出
    async handleExport() {
      let exportGroup = [];
      this.multiDataGroup.forEach((item) => {
        exportGroup.push({
          groupCode: item,
        });
      });
      let selectList = [];
      this.selectList.forEach((item, index) => {
        item.orderNum = index;
        selectList.push(item);
      });

      await apiExportEntry({
        source: this.$route.query.source,
        query: this.searchCondition,
        export: selectList,
        exportGroup,
      });
      this.isShowExport = false;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets//scss/helpers";
.custom-export {
  /*height: calc(100vh - 80px);*/
  padding: 0 32px;
  box-sizing: border-box;
  overflow: auto;
  header {
    display: flex;
    align-items: center;
    height: auto !important;
    padding: 28px 0;
    box-sizing: border-box;
    border-bottom: 1px solid #eaeaea;
    .back-style {
      font-size: 16px;
    }
    .header-line {
      font-size: 22px;
      padding: 0px 18px;
      color: rgb(136, 136, 136);
    }
    .header-title {
      font-size: 24px;
    }
  }
  .el-main {
    padding: 16px 0;
    .el-select {
      width: 300px;
    }
    .select-con {
      .select-con-title {
        display: flex;
        align-items: center;
        margin: 16px 0;
        .title-line {
          width: 3px;
          height: 14px;
          background: #4f71ff;
          border-radius: 1px;
        }
        .title-text {
          font-size: 16px;
          margin: 0 16px 0 10px;
        }
      }
    }
    .select-list {
      position: relative;
      margin-bottom: 10px;
      .group-name {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 170px;
        background: #f1f1f1;
        text-align: center;
        position: absolute;
        height: 100%;
        left: 0px;
        top: 0px;
      }
      .group-item {
        min-height: 104px;
        background: #ffffff;
        border: 1px solid #e8eaf3;
        box-sizing: border-box;
        margin-left: 180px;
        padding: 25px 15px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        line-height: 30px;
      }
    }
  }
  footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 20px 0;
    color: #555555;
    .num {
      color: #ff9b0e;
    }
  }
}
.el-dialog {
  .info {
    color: #6a6f7f;
  }
  .el-icon-info {
    color: #6a6f7f;
    margin-right: 8px;
  }
  .save-mode {
    margin-top: 25px;
    > div {
      margin-left: 10px;
    }
  }
  .el-radio {
    display: block;
    margin: 3px 0 0 0 !important;
  }
  .el-radio:not(:first-child) {
    margin-top: 24px !important;
  }
  .el-input,
  .el-select {
    width: 300px;
    margin-left: 35px;
    margin-top: 10px;
  }
  .table-header {
    margin-top: 32px;
  }
  .num {
    color: #ff9b0e;
  }
  .item {
    display: inline-block;
    color: #ffffff;
    padding: 10px 16px;
    background: $mainColor;
    border-radius: 4px;
    margin: 16px 16px 0 0;
  }
  .disabled-item {
    background: #cccccc;
    color: #555555;
  }
}
</style>
