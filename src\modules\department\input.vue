<template>
  <div>
    <div class="input" v-if="value">
      <WechatUserNameOrDepartmentName :departmentId="value" />
      <i class="el-icon-error" style="cursor: pointer" @click="reSelect" />
    </div>
    <el-input
      v-if="!value"
      style="width: 300px"
      v-model="inputDepartment"
      :placeholder="'请输入部门'"
      onkeyup="value=value.replace(/\s*/g,'')"
      @change="changeDepartment"
    ></el-input>
  </div>
</template>
<script>
import WechatUserNameOrDepartmentName from "../staffManage/components/wechatUserNameOrDepartmentName.vue";
export default {
  components: {
    WechatUserNameOrDepartmentName,
  },
  props: {
    departmentValue: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      inputDepartment: "",
      value: this.departmentValue,
    };
  },
  methods: {
    reSelect() {
      this.value = "";
    },
    changeDepartment() {
      this.$emit("input", this.inputDepartment);
    },
  },
};
</script>
<style scoped>
.input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 300px;
  padding-left: 15px;
  box-sizing: border-box;
  border: 1px solid #e6e8ec;
  border-radius: 5px;
}
</style>