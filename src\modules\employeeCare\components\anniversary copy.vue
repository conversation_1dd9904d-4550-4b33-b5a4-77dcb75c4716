<template>
  <div class="anniversary">
    <div class="search-box check-staff-menu">
      <div>
        <el-button class="filter-btn" @click="isShowScreening = true">
          筛选
        </el-button>
        <el-input
          placeholder="请输入姓名\手机号"
          prefix-icon="iconiconfonticonfontsousuo1 iconfont"
          class="search-input"
          v-model="searchForm.key"
          @keyup.enter.native="handleSearch"
        ></el-input>
        <el-select
          v-model="searchForm.term"
          placeholder="请选择"
          filterable
          @change="handleSearch"
        >
          <el-option
            v-for="item in anniversaryDateList"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          ></el-option>
        </el-select>
      </div>
      <el-button
        type="primary"
        @click="goSetting"
        v-if="privilegeVoList.includes('hrEmployee.employee.empcare.config')"
      >
        设置入职周年短信
      </el-button>
    </div>
    <div class="depart-tree-con">
      部门名称:
      <el-popover
        placement="bottom"
        trigger="click"
        popper-class="tree-popper"
        ref="treePopper"
      >
        <div>
          <el-input placeholder="输入部门名称" v-model="filterText"> </el-input>
          <el-tree
            ref="departTree"
            :data="departmentList"
            node-key="id"
            :default-expanded-keys="defaultExpandedKey"
            @node-click="handleNodeClick"
            :filter-node-method="filterNode"
          ></el-tree>
        </div>
        <div class="select-con" slot="reference">
          <span>{{ selectDepartName }}</span>
          <i class="el-icon-caret-bottom"></i>
        </div>
      </el-popover>
    </div>
    <old-table
      :data="tableData"
      :headerData="headerData"
      :isShowOperation="false"
      :isShowTooltip="true"
      :isShowPagination="true"
      :pageOptions="searchForm"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
    >
      <template slot="empNature" slot-scope="scope">
        <div>
          {{ scope.msg.row.empNature | filterEmpType }}
        </div>
      </template>
      <template slot="sendSmsYn" slot-scope="scope">
        <div>
          {{ scope.msg.row.sendSmsYn ? "是" : "否" }}
          <span
            v-if="
              !scope.msg.row.sendSmsYn &&
              isAnniversary(scope.msg.row.entryDate) &&
              privilegeVoList.includes('hrEmployee.employee.empcare.sendsms')
            "
            class="table-name"
            @click="handleSend(scope.msg.row)"
          >
            发送短信
          </span>
        </div>
      </template>
    </old-table>
    <!--筛选-->
    <el-dialog
      title="筛选"
      :visible.sync="isShowScreening"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleCloseScreening"
      width="650px"
      class="screen-dialog"
    >
      <el-form :model="searchForm" ref="searchForm" label-width="130px">
        <el-form-item label="转正状态" prop="turnRegularStatus">
          <el-select
            placeholder="请选择"
            v-model="searchForm.turnRegularStatus"
            clearable
            filterable
          >
            <el-option
              v-for="(item, index) in regularStatusOption"
              :label="item.label"
              :value="item.value"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="入职日期" prop="entryDate">
          <el-date-picker
            v-model="searchForm.entryDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="用工性质" prop="empNature">
          <el-radio-group v-model="searchForm.empNature" size="medium">
            <el-radio-button
              :label="item.value"
              v-for="(item, index) in enumEmpTypeOption"
              :key="index"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="resetSreen">重置</el-button>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { anniversaryDateList } from "../util/constData";
import {
  enumEmpTypeOption,
  regularStatusOption,
} from "../../staffManage/util/constData";
import { apiGetEntryAnniversaryList, apiSendEmpCareSms } from "../store/api";
export default {
  data() {
    return {
      searchForm: {
        currPage: 1,
        pageSize: 20,
        total: 0, //数据总数
        pageSizes: [10, 20, 50, 100], //每页显示个数选择器选项设置
        key: "",
        term: "THIS_MONTH", // 日期
        departmentId: [], // 部门
        turnRegularStatus: "", // 转正状态
        empNature: "", // 用工性质
        entryDate: "", // 入职时间
      },
      tableData: [],
      headerData: [
        { title: "姓名", label: "empName", showTooltip: true, align: "left" },
        {
          title: "公司名称",
          label: "taxSubName",
          showTooltip: true,
          align: "left",
        },
        {
          title: "部门",
          label: "departmentName",
          showTooltip: true,
          align: "left",
        },
        {
          title: "岗位",
          label: "positionName",
          showTooltip: true,
          align: "left",
        },
        {
          title: "入职日期",
          label: "entryDate",
          align: "left",
        },
        { title: "司龄", label: "companyAge", align: "left" },
        {
          title: "工作性质",
          label: "empNature",
          align: "left",
          slot: "empNature",
        },
        { title: "手机号",
          label: "mobile",
          showTooltip: true,
          align: "left" 
        },
        {
          title: "是否已发送短信",
          label: "sendSmsYn",
          align: "left",
          slot: "sendSmsYn",
        },
      ],
      anniversaryDateList: anniversaryDateList,
      enumEmpTypeOption: [{ label: "全部", value: "" }].concat(
        enumEmpTypeOption
      ), //用工性质
      regularStatusOption: regularStatusOption,
      selectDepartName: "", //当前选择的部门名称
      filterText: "",
      defaultExpandedKey: [],
      isShowScreening: false,
      currentDate: "",
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
    ...mapState("homePageStore", {
      departmentList: "departmentList",
    }),
  },
  watch: {
    filterText(val) {
      this.$refs.departTree.filter(val);
    },
    departmentList: {
      handler() {
        if (this.departmentList && this.departmentList.length > 0) {
          this.selectDepartName = this.departmentList[0].label;
          this.departmentList.forEach((item) => {
            this.defaultExpandedKey.push(item.id);
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.getList();
    const date = new Date();
    this.currentDate = `${date.getFullYear()}-${this.timeAdd(date.getMonth() + 1)
    }-${this.timeAdd(date.getDate())}`;
  },
  methods: {
    timeAdd(val){
      console.log(val.length);
      if(val.toString().length<=1){
        val='0'+val
      }
      return val
    },
    async getList() {
      let params = { ...this.searchForm };
      params.entryDateStart = this.searchForm.entryDate
        ? this.searchForm.entryDate[0]
        : "";
      params.entryDateEnd = this.searchForm.entryDate
        ? this.searchForm.entryDate[1]
        : "";
      let res = await apiGetEntryAnniversaryList(params);
      if (res.success) {
        this.tableData = res.data.records;
        this.searchForm.total = res.data.total;
      }
    },
    handleSizeChange(val) {
      this.searchForm.pageSize = val;
      this.searchForm.currPage = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.searchForm.currPage = val;
      this.getList();
    },
    //搜索
    handleSearch() {
      this.isShowScreening = false;
      this.searchForm.currPage = 1;
      this.getList();
    },
    //关闭筛选弹窗
    handleCloseScreening() {
      this.resetSreen();
      this.isShowScreening = false;
    },
    //重置筛选弹窗
    resetSreen() {
      this.$refs["searchForm"].resetFields();
      this.handleSearch();
    },
    //筛选部门
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    //切换部门
    handleNodeClick(data) {
      if (data.rootYn) {
        this.searchForm.departmentId = [];
      } else {
        this.searchForm.departmentId = this.getDepartmentIds(data);
      }
      this.selectDepartName = data.label;
      this.$refs.treePopper.doClose();
      this.handleSearch();
    },
    //递归获取部门id
    getDepartmentIds(obj) {
      if (!obj) return;
      let res = [];
      res.push(obj.id);
      if (obj.children) {
        let arr = obj.children;
        let len = arr.length;
        for (let i = 0; i < len; ++i) {
          let curr = arr[i];
          res.push(curr.id);
          if (curr.children) {
            res = res.concat(this.getDepartmentIds(curr));
          }
        }
      }
      return res;
    },
    //判断是否是周年
    isAnniversary(entryDate) {
      return (
        entryDate !== this.currentDate &&
        entryDate.substring(entryDate.length - 5) ==
          this.currentDate.substring(this.currentDate.length - 5)
      );
    },
    //发送生日短信
    async handleSend(row) {
      let res = await apiSendEmpCareSms({
        empCareType: "ENTRY_ANNIVERSARY_SMS",
        uniqueId: row.empRecordId,
      });
      if (res.success) {
        this.$message.success("发送成功");
        this.getList();
      }
    },
    //设置页面
    goSetting() {
      this.$router.push({
        path: "/employeeCareNote",
        query: {
          type: "ENTRY_ANNIVERSARY",
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.setting {
  .search-box {
    margin-top: 10px;
    .el-input {
      margin-left: 10px;
      margin-right: 10px;
    }
  }
}
</style>