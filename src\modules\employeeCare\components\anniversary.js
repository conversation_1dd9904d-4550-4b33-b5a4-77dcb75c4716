import { listStandardApiParamsAdaptation } from '@/utils/utils';
import { getBaseTopSelectFormJson,getBaseTableHeader } from '@/util/hro';
import { apiGetEntryAnniversaryList } from "../store/api";
import { anniversaryDateList } from "../util/constData";
import { filterEmpType } from "@/filters/index";
import { enumEmpTypeOption, regularStatusOption, } from "../../staffManage/util/constData";


export function getTopSelectFormJson(vm) {
  const key = vm.$route.query.key;
  return [
    {
      type: 'input',
      item: {
        prop: 'key',
        label: '姓名',
        value: key || '',
        placeholder: '请输入姓名/手机号',
      },
    },
    {
      type:"select",
      item:
      {
        prop:"term",
        label:"入职日期",
        placeholder:"请选择入职日期",
        labelMaxLength:"20",
        options:anniversaryDateList,
        defaultValue:"THIS_MONTH"
      },
    },
    ...getBaseTopSelectFormJson(),
    {
      type:"select",
      item:
      {
        prop:"turnRegularStatus",
        label:"转正状态",
        placeholder:"请选择转正状态",
        labelMaxLength:"20",
        options:regularStatusOption,
      },
    },
    {
      type:"datePicker",
      item:
      {
        prop:"entryDate",
        label:"入职日期",
        type:"daterange",
        startField:"entryDateStart",
        endField:"entryDateEnd",
        rangeSeparator:"-",
        startPlaceholder:"开始日期",
        endPlaceholder:"结束日期",
        format:"yyyy-MM-dd",
        valueFormat:"yyyy-MM-dd",
      },
    },
    {
      type:"select",
      item:
      {
        prop:"empNature",
        label:"用工性质",
        placeholder:"请选择用工性质",
        labelMaxLength:"20",
        options:enumEmpTypeOption,
      },
    },
  ];
}

export const getTableHeader = (vm) => {
  return [
    {
      label: '序号',
      type: 'INDEX',
      fixed:"left"
    },
    {
      prop: 'empName',
      label: '姓名',
      minWidth: 100,
      fixed:"left"
    },
    ...getBaseTableHeader(),
    {
      prop: 'taxSubName',
      label: '公司名称',
      minWidth: 140,
    },
    {
      prop: 'departmentName',
      label: '部门',
      minWidth: 150,
    },
    {
      prop: "positionName",
      label: "岗位",
      minWidth:100,
    },
    {
      label: "入职日期",
      prop: "entryDate",
      minWidth:120,
    },
    { 
      label: "司龄", 
      prop: "companyAge",
      minWidth:120,
   },
    {
      label: "工作性质",
      prop: "empNature",
      minWidth:100,
      formatter:row=>filterEmpType(row.empNature)
    },
    { label: "手机号",
      prop: "mobile",
      type:"MOBILE"
    },
    {
      label: "是否已发送短信",
      prop: "sendSmsYn",
      formatter:row=>row.sendSmsYn?'是':"否"
    },
  ];
};

export function getActionButtons(vm) {
  return [
    {
      label:'发送短信',
      ifShow:row=> {
        return !row.sendSmsYn &&
        vm.isAnniversary(row.entryDate) &&
        vm.privilegeVoList.includes('hrEmployee.employee.empcare.sendsms')
      },
      click:row=>vm.handleSend(row)
    },
  ]
}

export function getTableHeaderActionButtons(vm){
  return [
    {
      align: 'right',
      type: 'button',
      label: '设置入职周年短信',
      ifShow:()=>vm.privilegeVoList.includes('hrEmployee.employee.empcare.config'),
      click: vm.goSetting,
    }
  ]
}

export async function getTableListApi(params, vm) {
  const newParams = listStandardApiParamsAdaptation(params);

  newParams.term = newParams.term || "ALL"
  
  const { data } = await apiGetEntryAnniversaryList(newParams);

  vm.ruleForm = newParams;
  data.list = data.records;

  return data;
}


