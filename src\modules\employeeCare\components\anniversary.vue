<template>
  <div>
    <!-- 筛选区域 -->
    <o-top-select
      ref="top-select"
      :formJson="topSelectFormJson"
      :immediate="true"
      labelWidth="86px"
      @search="onSearch"
    />

    <!-- 表格区域 -->
    <o-table
      ref="o-table"
      :sticky="true"
      :pagination="{ fixed:true }"
      :showPagination="true"
      :deleteNullApiParams="true"
      :tableHeader="tableHeader"
      :requestFn="getTableListApi"
      :actionButtons="actionButtons"
      :tableHeaderActionButtons="tableHeaderActionButtons"
      emptyHeight="calc(100vh - 450px)"
    />
  </div>
</template>
<script>
import { mapState } from "vuex";
import { anniversaryDateList } from "../util/constData";
import { getTopSelectFormJson,getTableHeader,getTableListApi, getActionButtons, getTableHeaderActionButtons } from "./anniversary"
import { enumEmpTypeOption, regularStatusOption, } from "../../staffManage/util/constData";
import { apiSendEmpCareSms } from "../store/api";

export default {
  data() {
    return {
      topSelectFormJson:getTopSelectFormJson(this),
      tableHeader:getTableHeader(this),
      getTableListApi:(params)=>getTableListApi(params,this),
      actionButtons:getActionButtons(this),
      tableHeaderActionButtons:getTableHeaderActionButtons(this),

      ruleForm: {
        currPage: 1,
        pageSize: 20,
        key: "",
        term: "THIS_MONTH", // 日期
        departmentId: [], //goSetting 部门
        turnRegularStatus: "", // 转正状态
        empNature: "", // 用工性质
        entryDate: "", // 入职时间
      },

      anniversaryDateList: anniversaryDateList,
      enumEmpTypeOption: [{ label: "全部", value: "" }].concat(
        enumEmpTypeOption
      ), //用工性质
      regularStatusOption: regularStatusOption,
      currentDate: "",
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
    ...mapState("homePageStore", {
      departmentList: "departmentList",
    }),
    oTable(){
      return this.$refs["o-table"]
    }
  },
  created() {
    this.getCurrentDate()
  },
  methods: {
     getCurrentDate(){
      const date = new Date();
      return `${date.getFullYear()}-${this.timeAdd(date.getMonth() + 1)
      }-${this.timeAdd(date.getDate())}`;
    },
    timeAdd(val){
      console.log(val.length);
      if(val.toString().length<=1){
        val='0'+val
      }
      return val
    },
    //筛选查询
    async onSearch(formData = {}) {
      await this.oTable.appendRequestParams(formData)
    },
    getList() {
      this.oTable.reload()
    },
    //判断是否是周年
    isAnniversary(entryDate) {
      return (
        entryDate !== this.currentDate &&
        entryDate.substring(entryDate.length - 5) ==
          this.currentDate.substring(this.currentDate.length - 5)
      );
    },
    //发送生日短信
    async handleSend(row) {
      let res = await apiSendEmpCareSms({
        empCareType: "ENTRY_ANNIVERSARY_SMS",
        uniqueId: row.empRecordId,
      });
      if (res.success) {
        this.$message.success("发送成功");
        this.getList();
      }
    },
    //设置页面
    goSetting() {
      this.$router.push({
        path: "/employeeCareNote",
        query: {
          type: "ENTRY_ANNIVERSARY",
        },
      });
    },
  },
};
</script>