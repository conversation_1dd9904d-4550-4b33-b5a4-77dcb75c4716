<template>
  <div class="birthday">
    <div class="search-box check-staff-menu">
      <div v-if="activeName === 'BIRTHDAY'">
        <el-input
          placeholder="请输入姓名\手机号"
          prefix-icon="iconiconfonticonfontsousuo1 iconfont"
          class="search-input"
          v-model="searchForm.key"
          @keyup.enter.native="handleSearch"
        ></el-input>
        <el-select
          v-model="searchForm.term"
          placeholder="请选择"
          filterable
          @change="handleSearch"
        >
          <el-option
            v-for="item in birthDateList"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          ></el-option>
        </el-select>
      </div>
      <div v-if="activeName === 'ENTRY_ANNIVERSARY'">
        <el-button class="filter-btn" @click="isShowScreening = true">
          筛选
        </el-button>
        <el-input
          placeholder="请输入姓名\手机号"
          prefix-icon="iconiconfonticonfontsousuo1 iconfont"
          class="search-input"
          v-model="searchForm.key"
          @keyup.enter.native="handleSearch"
        ></el-input>
        <el-select
          v-model="searchForm.term"
          placeholder="请选择"
          filterable
          clearable
        >
          <el-option
            v-for="item in anniversaryDateList"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          ></el-option>
        </el-select>
      </div>
      <el-button
        type="primary"
        @click="goSetting"
        v-if="privilegeVoList.includes('hrEmployee.employee.empcare.config')"
      >
        设置生日短信
      </el-button>
    </div>
    <old-table
      :data="tableData"
      :headerData="headerData"
      :isShowOperation="false"
      :isShowTooltip="true"
      :isShowPagination="true"
      :pageOptions="searchForm"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
    >
      <template slot="empSex" slot-scope="scope">
        <div>
          {{
            scope.msg.row.empSex === "MALE"
              ? "男"
              : scope.msg.row.empSex === "FEMALE"
              ? "女"
              : ""
          }}
        </div>
      </template>
      <template slot="sendSmsYn" slot-scope="scope">
        <div>
          {{ scope.msg.row.sendSmsYn ? "是" : "否" }}
          <span
            v-if="
              !scope.msg.row.sendSmsYn &&
              isBirthday(scope.msg.row.birthday) &&
              privilegeVoList.includes('hrEmployee.employee.empcare.sendsms')
            "
            class="table-name"
            @click="handleSend(scope.msg.row)"
          >
            发送短信
          </span>
        </div>
      </template>
    </old-table>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { birthDateList } from "../util/constData";
import { apiGetEmpBirthdayList, apiSendEmpCareSms } from "../store/api";
export default {
  data() {
    return {
      activeName: "BIRTHDAY",
      searchForm: {
        currPage: 1,
        pageSize: 20,
        total: 0, //数据总数
        pageSizes: [10, 20, 50, 100], //每页显示个数选择器选项设置
        key: "",
        term: "THIS_MONTH", // 日期
      },
      tableData: [],
      headerData: [
        { title: "姓名", label: "empName", showTooltip: true, align: "left" },
        { title: "生日", label: "birthdayStr", align: "left" },
        { title: "性别", label: "empSex", align: "left", slot: "empSex" },
        { title: "年龄", label: "empAge", align: "left" },
        { title: "手机号", label: "mobile", align: "left" },
        {
          title: "是否已发送短信",
          label: "sendSmsYn",
          align: "left",
          slot: "sendSmsYn",
        },
      ],
      birthDateList: birthDateList,
      currentDate: "",
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  created() {
    this.getList();
    const date = new Date();
    this.currentDate = `${date.getFullYear()}-${this.timeAdd(date.getMonth() + 1)
    }-${this.timeAdd(date.getDate())}`;
  },
  methods: {
    timeAdd(val){
      console.log(val.length);
      if(val.toString().length<=1){
        val='0'+val
      }
      return val
    },
    async getList() {
      let res = await apiGetEmpBirthdayList(this.searchForm);
      if (res.success) {
        this.tableData = res.data.records;
        this.searchForm.total = res.data.total;
      }
    },
    handleSizeChange(val) {
      this.searchForm.pageSize = val;
      this.searchForm.currPage = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.searchForm.currPage = val;
      this.getList();
    },
    //搜索
    handleSearch() {
      this.searchForm.currPage = 1;
      this.getList();
    },
    //判断是否是周年
    isBirthday(date) {
      return (
        date.substring(date.length - 5) ==
        this.currentDate.substring(this.currentDate.length - 5)
      );
    },
    //发送生日短信
    async handleSend(row) {
      let res = await apiSendEmpCareSms({
        empCareType: "BIRTHDAY_SMS",
        uniqueId: row.compEmpId,
      });
      if (res.success) {
        this.$message.success("发送成功");
        this.getList();
      }
    },
    //设置页面
    goSetting() {
      this.$router.push({
        path: "/employeeCareNote",
        query: {
          type: "BIRTHDAY",
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.birthday {
  .search-box {
    margin-top: 10px;
    .el-input {
      margin-left: 0;
      margin-right: 10px;
    }
  }
}
</style>