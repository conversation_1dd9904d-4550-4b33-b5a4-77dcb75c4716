import { listStandardApiParamsAdaptation } from '@/utils/utils';
import { getBaseTopSelectFormJson,getBaseTableHeader } from '@/util/hro';
import { apiGetEmpBirthdayList } from "../store/api";
import { birthDateList } from "../util/constData";


export function getTopSelectFormJson(vm) {
  const key = vm.$route.query.key;
  return [
    {
      type: 'input',
      item: {
        prop: 'key',
        label: '姓名',
        value: key || '',
        placeholder: '请输入姓名/手机号',
      },
    },
    {
      type:"select",
      item:
      {
        prop:"term",
        label:"出生日期",
        placeholder:"请选择出生日期",
        labelMaxLength:"20",
        options:birthDateList,
        defaultValue:"THIS_MONTH"
      },
    },
    // ...getBaseTopSelectFormJson(),
  ];
}

export const getTableHeader = (vm) => {
  return [
    {
      label: '序号',
      type: 'INDEX',
      fixed:"left"
    },
    {
      prop: 'empName',
      label: '姓名',
      minWidth: 100,
      fixed:"left"
    },
    {
      prop: 'birthdayStr',
      label: '生日',
      minWidth: 82,
    },
    {
      prop: 'empSex',
      label: '性别',
      formatter:row=> {
        const empSexMap = {
          MALE:"男",
          FEMALE:"女",
          default:"-"
        }
        return empSexMap[row.empSex] || empSexMap.default
      }
    },
    {
      prop: 'empAge',
      label: '年龄',
      type:"NUMBER"
    },
    // ...getBaseTableHeader(),
    {
      prop: 'mobile',
      label: '手机号',
      type:"MOBILE"
    },
    {
      prop: 'sendSmsYn',
      label: '是否已发送短信',
      minWidth: 180,
      formatter:row=>row.sendSmsYn?'是':"否"
    },
  ];
};

export function getActionButtons(vm) {
  return [
    {
      label:'发送短信',
      ifShow:row=> {
        return !row.sendSmsYn &&
        vm.isBirthday(row.birthday) &&
        vm.privilegeVoList.includes('hrEmployee.employee.empcare.sendsms')
      },
      click:row=>vm.handleSend(row)
    },
  ]
}

export function getTableHeaderActionButtons(vm){
  return [
    {
      align: 'right',
      type: 'button',
      label: '设置生日短信',
      ifShow:()=>vm.privilegeVoList.includes('hrEmployee.employee.empcare.config'),
      click: vm.goSetting,
    }
  ]
}

export async function getTableListApi(params, vm) {
  const newParams = listStandardApiParamsAdaptation(params);

  newParams.term = newParams.term || "ALL"
  
  const { data } = await apiGetEmpBirthdayList(newParams);

  vm.ruleForm = newParams;
  data.list = data.records;

  return data;
}


