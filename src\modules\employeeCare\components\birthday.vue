<template>
  <div>
    <!-- 筛选区域 -->
    <o-top-select
      ref="top-select"
      :formJson="topSelectFormJson"
      :immediate="true"
      labelWidth="86px"
      @search="onSearch"
    />

    <!-- 表格区域 -->
    <o-table
      ref="o-table"
      :sticky="true"
      :pagination="{ fixed:true }"
      :showPagination="true"
      :deleteNullApiParams="true"
      :tableHeader="tableHeader"
      :requestFn="getTableListApi"
      :actionButtons="actionButtons"
      :tableHeaderActionButtons="tableHeaderActionButtons"
      emptyHeight="calc(100vh - 450px)"
    />
  </div>
</template>
<script>
import { mapState } from "vuex";
import { apiSendEmpCareSms } from "../store/api";
import { getTopSelectFormJson,getTableHeader,getTableListApi, getActionButtons, getTableHeaderActionButtons } from "./birthday"

export default {
  data() {
    return {
      topSelectFormJson:getTopSelectForm<PERSON>son(this),
      tableHeader:getTableHeader(this),
      getTableListApi:(params)=>getTableListApi(params,this),
      actionButtons:getActionButtons(this),
      tableHeaderActionButtons:getTableHeaderActionButtons(this),

      ruleForm: {
        key: "",
        term: "THIS_MONTH",
      },

      currentDate: "",
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
    oTable(){
      return this.$refs["o-table"]
    }
  },
  created() {
    this.getCurrentDate()
  },
  methods: {
    getCurrentDate(){
      const date = new Date();
      return `${date.getFullYear()}-${this.timeAdd(date.getMonth() + 1)
      }-${this.timeAdd(date.getDate())}`;
    },
    timeAdd(val){
      if(val.toString().length<=1){
        val='0'+val
      }
      return val
    },
    //筛选查询
    async onSearch(formData = {}) {
      await this.oTable.appendRequestParams(formData)
    },
    getList() {
      this.oTable.reload()
    },
    //判断是否是周年
    isBirthday(date) {
      return (
        date.substring(date.length - 5) ==
        this.currentDate.substring(this.currentDate.length - 5)
      );
    },
    //发送生日短信
    async handleSend(row) {
      let res = await apiSendEmpCareSms({
        empCareType: "BIRTHDAY_SMS",
        uniqueId: row.compEmpId,
      });
      if (res.success) {
        this.$message.success("发送成功");
        this.getList();
      }
    },
    //设置页面
    goSetting() {
      this.$router.push({
        path: "/employeeCareNote",
        query: {
          type: "BIRTHDAY",
        },
      });
    },
  },
};
</script>