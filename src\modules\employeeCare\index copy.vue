<template>
  <div class="setting">
    <el-header class="header main-title">员工关怀</el-header>
    <el-main>
      <el-tabs v-model="activeName">
        <el-tab-pane label="员工生日" name="BIRTHDAY"></el-tab-pane>
        <el-tab-pane label="入职周年" name="ENTRY_ANNIVERSARY"></el-tab-pane>
      </el-tabs>
      <birthday v-if="activeName === 'BIRTHDAY'"></birthday>
      <anniversary v-if="activeName === 'ENTRY_ANNIVERSARY'"></anniversary>
    </el-main>
  </div>
</template>
<script>
import birthday from "./components/birthday";
import anniversary from "./components/anniversary";
export default {
  components: { birthday, anniversary },
  data() {
    return {
      activeName: this.$route.query.activeName
        ? this.$route.query.activeName
        : "BIRTHDAY",
    };
  },
};
</script>
<style lang="scss" scoped>
/*.setting {*/
/*  height: calc(100vh - 80px);*/
/*  overflow: auto;*/
/*}*/
.el-header {
  padding: 0;
}
</style>
<style lang="scss">
.setting {
  .el-tabs__item {
    font-size: 16px;
  }
}
</style>
