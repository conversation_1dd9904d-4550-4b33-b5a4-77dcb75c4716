<template>
  <o-container title="员工关怀" class="setting">
    <el-tabs v-model="activeName" style="margin-top:6px">
      <el-tab-pane label="员工生日" name="BIRTHDAY"></el-tab-pane>
      <el-tab-pane label="入职周年" name="ENTRY_ANNIVERSARY"></el-tab-pane>
    </el-tabs>
    <birthday v-if="activeName === 'BIRTHDAY'"></birthday>
    <anniversary v-if="activeName === 'ENTRY_ANNIVERSARY'"></anniversary>
  </o-container>
</template>
<script>
import birthday from "./components/birthday.vue";
import anniversary from "./components/anniversary.vue";
export default {
  components: { birthday, anniversary },
  data() {
    return {
      activeName: this.$route.query.activeName
        ? this.$route.query.activeName
        : "BIRTHDAY",
    };
  },
};
</script>
<style lang="scss">
.setting {
  .el-tabs__item {
    font-size: 16px;
  }
}
</style>
