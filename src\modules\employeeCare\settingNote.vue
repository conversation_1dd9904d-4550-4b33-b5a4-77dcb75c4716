<template>
  <div class="setting-note">
    <el-header class="header main-title" style="padding-left: 0">
      <span @click="goBack" class="back-style">返回</span>
      <span class="header-line">|</span>
      <span>设置{{ type === "BIRTHDAY" ? "生日" : "周年" }}短信祝福</span>
    </el-header>
    <el-main>
      <el-form :model="settingForm" v-loading="loading">
        <el-form-item
          :label="type === 'BIRTHDAY' ? '生日提示短信：' : '入职周年提示短信：'"
        >
          <el-switch v-model="settingForm.sendSmsYn"></el-switch>
        </el-form-item>
        <div v-show="settingForm.sendSmsYn">
          <el-form-item label="定时发送时间：">
            {{ type === "BIRTHDAY" ? "生日当天" : "入职周年当天" }}
            <el-select
              v-model="settingForm.sendTime"
              placeholder="请选择"
              filterable
            >
              <el-option
                v-for="item in sendTimeList"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              ></el-option>
            </el-select>
            <span class="tip">若所选时间已过，则会立即发送</span>
          </el-form-item>
          <el-form-item label="员工短信预览：">
            {{ settingForm.empTemplate }}
          </el-form-item>
          <el-form-item>
            <template slot="label">
              <span>是否通知上级：</span>
              <el-tooltip
                content="若员工有设置直接上级，则同时通知直接上级（直接上级可前往花名册修改）"
                placement="top"
              >
                <i class="el-icon-info"></i>
              </el-tooltip>
            </template>
            <el-radio-group v-model="settingForm.notifyLeaderYn">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上级短信预览：">
            {{ settingForm.leaderTemplate }}
          </el-form-item>
        </div>
      </el-form>
    </el-main>
    <el-footer>
      <el-button @click="goBack">取消</el-button>
      <el-button type="primary" @click="handleSave">确定</el-button>
    </el-footer>
  </div>
</template>
<script>
import { sendTimeList } from "./util/constData";
import { apiQueryEmpCareConfig, apiConfigCompanyEmpCare } from "./store/api";
export default {
  data() {
    return {
      type: this.$route.query.type,
      settingForm: {
        empCareType:
          this.type === "BIRTHDAY" ? "BIRTHDAY_SMS" : "ENTRY_ANNIVERSARY_SMS", // 员工关怀配置类型
        sendSmsYn: true, // 是否发送短信
        empTemplate: "", // 短信模板
        sendTime: "08:00", // 定时发送时间
        notifyLeaderYn: true, // 是否通知上级
        leaderTemplate: "", // 上级短信模板
      },
      sendTimeList: sendTimeList,
      loading: false,
    };
  },
  created() {
    this.getConfig();
  },
  methods: {
    //查询员工关怀配置
    async getConfig() {
      this.loading = true;
      let res = await apiQueryEmpCareConfig({
        enumEmpTemplateType:
          this.type === "BIRTHDAY" ? "BIRTHDAY_SMS" : "ENTRY_ANNIVERSARY_SMS",
      });
      if (res.success) {
        this.settingForm = res.data;
      }
      this.loading = false;
    },
    // 确定
    async handleSave() {
      let res = await apiConfigCompanyEmpCare(this.settingForm);
      if (res.success) {
        this.$message.success("保存成功");
        this.goBack();
      }
    },
    //返回
    goBack() {
      this.$router.push({
        path: "/employeeCare",
        query: {
          activeName: this.$route.query.type,
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.setting-note {
  position: relative;
  /*height: calc(100vh - 80px);*/
  .tip {
    color: $textLight;
  }
  .el-main {
    height: calc(100vh - 146px);
    padding-bottom: 30px;
  }
  .el-footer {
    width: 100%;
    border-top: 1px solid #e5e5e5;
    position: absolute;
    bottom: 0;
    left: 0;
    text-align: center;
    padding-top: 10px;
    background: #fff;
    z-index: 9;
  }
  /deep/.el-form-item__content {
    position: static;
  }
}
</style>
