import { fetch } from 'request/fetch';

//查询员工生日列表
export function apiGetEmpBirthdayList(data) {
    return fetch({
        url: '/api/hrsaas-emp/empCare/queryEmpBirthdayList',
        method: 'post',
        data
    });
}

//查询入职周年列表
export function apiGetEntryAnniversaryList(data) {
    return fetch({
        url: '/api/hrsaas-emp/empCare/queryEntryAnniversaryList',
        method: 'post',
        data
    });
}

//发送短信
export function apiSendEmpCareSms(data) {
    return fetch({
        url: '/api/hrsaas-emp/empCare/sendEmpCareSms',
        method: 'post',
        data
    });
}

//查询员工关怀配置
export function apiQueryEmpCareConfig(data) {
    return fetch({
        url: '/api/hrsaas-emp/empCare/queryEmpCareConfig/' + data.enumEmpTemplateType,
        method: 'post'
    });
}

//保存员工关怀配置
export function apiConfigCompanyEmpCare(data) {
    return fetch({
        url: '/api/hrsaas-emp/empCare/configCompanyEmpCare',
        method: 'post',
        data
    });
}