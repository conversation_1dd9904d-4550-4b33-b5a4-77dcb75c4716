<template>
  <div class="homePage">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span>首页</span>
        </el-col>
        <!-- <el-col :span="12">
          <div class="add-table">
            <i class="iconxinzeng iconfont"></i>
            <span>新增工资表</span>
          </div>
        </el-col> -->
      </el-row>
    </header>
    <div class="home-content" style="width: 900px; margin: 0 auto;">
      <!-- <div class="home-head">操作指引 ></div> -->
      <img :src="showImg" alt="" />
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import * as AT from "@/store/actionTypes";
export default {
  components: {},
  data() {
    return {
      showImg: require("../../assets/images/home_content.png"),
    };
  },
  computed: {
    ...mapState({
      token: (state) => state.token,
      userType: (state) => state.userType,
    }),
  },
  created() {
    switch (window.env.theme) {
      case "c81930":
        this.showImg = require("../../assets/images/home_content_c81930.png");
        break;
      case "b6002a":
        this.showImg = require("../../assets/images/home_content_c81930.png");
        break;
      default:
        this.showImg = require("../../assets/images/home_content.png");
    }

    this.$store.commit(AT.SHOWAPP, true);
    this.$store
      .dispatch("homePageStore/actionGetUserInfo", {
        token: this.token,
      })
      .then((res) => {
        if (res.success) {
          let userType = res.data ? res.data.userType : "";
          if (userType === "ENTERPRISE") {
            this.$notify({
              title: "",
              duration: 0,
              message:
                "企业管理员不能进行钉薪税业务操作，请使用已分配钉薪税权限的业务操作员登陆。",
              type: "warning",
            });
          }
        }
      });
    this.clearRouterParams();
  },
  methods: {
    // 清楚路由参数信息
    clearRouterParams() {
      if (this.token && Object.keys(this.$route.query).length) {
        this.$router.replace("/home");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.homePage {
  /*height: calc(100vh - 80px);*/
  .header {
    padding: 0 20px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
    font-size: 17px;
    .add-table {
      cursor: pointer;
      float: right;
      color: $mainColor;
    }
    .iconxinzeng {
      font-size: 18px;
      color: #9c9c9c;
      position: relative;
      top: 1px;
    }
  }
  .home-head {
    display: block;
    height: 110px;
    width: 100%;
    padding: 70px 0px 0px 100px;
    box-sizing: border-box;
    font-size: 20px;
    background: url("../../assets/images/home_head.png") no-repeat;
    background-size: cover;
  }
  .home-content {
    display: flex;
    img {
      flex: 1;
      overflow: hidden;
    }
  }
}
</style>
