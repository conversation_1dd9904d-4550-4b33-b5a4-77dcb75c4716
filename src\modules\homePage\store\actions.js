import {
  apiGetUserInfo,
  apicChangeMerchant,
  apiGetProvince,
  apiGetDepartmentList
} from './api';
import * as AT from './actionTypes';

//累计应税所得额初始化-集合列表
export const actionGetUserInfo = ({ dispatch }, ruleForm) => {
  return apiGetUserInfo(ruleForm);
};

//切换企业
export const actioncChangeMerchant = ({ commit }, data) =>
  apicChangeMerchant(data);

//省份/城市/区县查询
export const actioncGetProvince = ({ commit }) => {
  return apiGetProvince().then(res => {
    commit(AT.SET_PROVINCELIST, res.data);
    return res;
  });
};

//查询部门列表
export const actioncGetDepartmentList = ({ commit }) => {
  return apiGetDepartmentList().then(res => {
    commit(AT.SET_DEPARTMENTLIST, res.data);
    return res;
  });
};
