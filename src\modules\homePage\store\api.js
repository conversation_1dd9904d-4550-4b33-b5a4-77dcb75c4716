import { fetch } from 'request/fetch';

//累计应税所得额初始化-集合列表
export function apiGetUserInfo(ruleForm) {
  return fetch({
    url: '/api/hrsaas-salary/taxSubject/getUserInfo',
    method: 'get',
    params: ruleForm
  });
}

//切换企业
export function apicChangeMerchant(data) {
  return fetch({
    url: '/api/olading-user/user/change_merchant',
    method: 'post',
    data
  });
}

//省份/城市查询
export function apiGetProvince() {
  return fetch({
    url: '/api/hrsaas-emp/custom/v1/query/province',
    method: 'get'
  });
}

//查询部门列表
export function apiGetDepartmentList() {
  return fetch({
    url: '/api/hrsaas-emp/position/getDepartmentList',
    method: 'get'
  });
}

//创建部门
export function apiAddDepartment(data) {
  return fetch({
    url: '/api/hrsaas-emp/position/createDepartment',
    method: 'post',
    data
  });
}
