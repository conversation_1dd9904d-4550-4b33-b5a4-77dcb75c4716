<template>
  <div class="selectSY">
    <el-dialog
      :visible.sync="isShowReturnInfo"
      width="800px"
      title="反馈信息"
      class="diy-el_dialog"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-table :data="reportReturnList" style="width: 100%">
        <el-table-column type="index" label="序号"></el-table-column>
        <el-table-column prop="dealStatus" label="状态" width="180">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center">
              <div
                class="dot"
                :style="{ background: realColor(scope.row.dealStatus) }"
              ></div>
              {{ getOptionsItemLabel(taskStatusOptions, scope.row.dealStatus) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="taxSubName"
          label="法人实体"
          min-width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="failReason"
          label="失败原因"
          min-width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.failReason || "-" }}
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button @click="onIKnow" type="primary" plain>我知道了</el-button>
      </div>
      <div v-show="showReturn" style="color: #ff9500; margin-top: 20px">
        任务仍在处理中，请稍后点击{{ freeBackTip }}查询结果
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { apiTaxSubBatchSubmitBack } from "../store/api";
import { getOptionsItemLabel } from "../getOptionsItemLabel";

const taskStatusOptions = [
  { label: "任务完成", value: "SUCCESS" },
  { label: "任务处理中", value: "PROCESSING" },
  { label: "任务失败", value: "FAIL" },
];

export default {
  props: {
    sign: String, //页面标识
    stopTip: String, //终止文案
    freeBackTip: String, //获取反馈文案
  },
  data() {
    return {
      reportReturnList: [],
      isShowReturnInfo: false,
      isShowReportInfo: false,
      showReturn: false,
      getOptionsItemLabel,
      taskStatusOptions,
    };
  },
  methods: {
    realColor(dealStatus) {
      switch (dealStatus) {
        case "SUCCESS":
          return "#07BB06";
        case "PROCESSING":
          return "#E6A23C";
        case "FAIL":
          return "#F53F3F";
      }
    },
    show(data) {
      this.reportReturnList = [];
      this.isShowReturnInfo = true;
      this.showReturn = false;
      this.reportReturnList = data;
      if (
        data.some(
          (item) =>
            item.dealStatus === "PROCESSING" || item.dealStatus === "INIT"
        )
      ) {
        this.showReturn = true;
      }
    },
    //获取反馈
    async handleReportInfo() {
      const res = await apiTaxSubBatchSubmitBack({
        taxSubIds: [],
      });
      if (res.success) {
        this.isShowReturnInfo = true;
        this.reportReturnList = res.data;
      }
    },
    onIKnow() {
      this.isShowReturnInfo = false;
      this.$emit("refresh");
    },
  },
};
</script>
<style scoped>
.dot {
  width: 6px;
  height: 6px;
  border-radius: 100px;
  opacity: 1;
  margin-right: 8px;
}
::v-deep .el-table th {
  background: #e4e7ed;
}
::v-deep .el-table th > .cell {
  color: #1e2228;
  font-weight: 600;
  font-size: 14px;
}
::v-deep .el-table__body-wrapper {
  height: 288px;
  overflow: auto;
}
</style>
