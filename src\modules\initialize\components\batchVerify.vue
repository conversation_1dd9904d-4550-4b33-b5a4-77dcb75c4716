<template>
  <div class="batchVerify">
    <el-dialog
      :visible.sync="isShowReportInfo"
      title="反馈信息"
      width="800px"
      class="diy-el_dialog"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-table
        v-loading="reportInfoLoading"
        element-loading-text="任务正在处理中"
        element-loading-spinner="el-icon-loading"
        :data="reportInfoList"
        style="width: 100%"
        height="100%"
      >
        <el-table-column type="index" label="序号"></el-table-column>
        <el-table-column prop="dealStatus" label="状态" width="180">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center">
              <div
                class="dot"
                :style="{ background: realColor(scope.row.dealStatus) }"
              ></div>
              {{ getOptionsItemLabel(taskStatusOptions, scope.row.dealStatus) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="taxSubName"
          label="法人实体"
          min-width="180"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="failReason"
          label="失败原因"
          min-width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.failReason || "-" }}
          </template>
        </el-table-column>
      </el-table>
      <div v-show="showReturn" style="color: #ff9500; margin-top: 20px">
        任务仍在处理中，请稍后点击{{ freeBackTip }}查询结果
      </div>
      <div slot="footer">
        <el-button v-show="isShowIKnow" @click="onIKnow" type="primary" plain>
          我知道了
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { apiTaxSubBatchSubmitBack } from "../store/api";
import { getOptionsItemLabel } from "../getOptionsItemLabel";

const taskStatusOptions = [
  { label: "任务完成", value: "SUCCESS" },
  { label: "任务处理中", value: "PROCESSING" },
  { label: "任务处理中", value: "INIT" },
  { label: "任务失败", value: "FAIL" },
];
const delay = (delayTime) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, delayTime);
  });
};

export default {
  props: {
    sign: String, //页面标识
    timeObj: Object, //时间
    freeBackTip: String, //待反馈按钮提示
  },
  data() {
    return {
      reportInfoList: [
        {
          taxSubName: "测试001",
          dealStatus: "SUCCESS",
          failReason: "",
        },
        {
          taxSubName: "测试002",
          dealStatus: "PROCESSING",
          failReason: "",
        },
        {
          taxSubName: "测试003",
          dealStatus: "PROCESSING",
          failReason: "",
        },
        {
          taxSubName: "测试004",
          dealStatus: "PROCESSING",
          failReason: "",
        },
        {
          taxSubName: "测试005",
          dealStatus: "PROCESSING",
          failReason: "",
        },
        {
          taxSubName: "测试006",
          dealStatus: "PROCESSING",
          failReason: "",
        },
        {
          taxSubName: "测试007",
          dealStatus: "PROCESSING",
          failReason: "",
        },
      ],
      isShowReportInfo: false,
      reportInfoLoading: false,
      showReturn: false,
      isShowIKnow: false,
      taxSubIds: [],
      getOptionsItemLabel,
      taskStatusOptions,
      delayTime: 1000 * 5,
    };
  },
  methods: {
    isTaskLoading(list) {
      return list.some(
        (item) => item.dealStatus === "PROCESSING" || item.dealStatus === "INIT"
      );
    },
    async show(data) {
      this.isShowReportInfo = true;
      this.reportInfoList = [];
      this.taxSubIds = [];
      this.showReturn = false;

      this.taxSubIds = data.map((item) => item.taxSubId);

      if (!this.isTaskLoading(data)) {
        return (this.isShowIKnow = true);
      }

      this.startPollingTask();
    },
    realColor(dealStatus) {
      switch (dealStatus) {
        case "SUCCESS":
          return "#07BB06";
        case "PROCESSING":
          return "#E6A23C";
        case "INIT":
          return "#E6A23C";
        case "FAIL":
          return "#F53F3F";
      }
    },
    async loadTaxSubBatchSubmitBack(index) {
      this.reportInfoLoading = true;
      const res = await apiTaxSubBatchSubmitBack({
        taxSubIds: this.taxSubIds,
      });
      if (res.success) {
        this.reportInfoList = res.data;

        if (index === 2 && this.isTaskLoading(res.data)) {
          this.isShowIKnow = true;
          this.reportInfoLoading = false;
          this.showReturn = true;
          return;
        }

        if (this.isTaskLoading(res.data)) {
          return Promise.resolve();
        }

        this.isShowIKnow = true;
        this.reportInfoLoading = false;
        return;
      }

      this.isShowIKnow = true;
      this.reportInfoLoading = false;
    },
    async startPollingTask() {
      const count = 3;
      for (let index = 0; index < count; index++) {
        await this.loadTaxSubBatchSubmitBack(index);
        await delay(this.delayTime);
      }
    },
    onIKnow() {
      this.isShowReportInfo = false;
      this.$emit("refresh");
    },
  },
};
</script>
<style scoped>
.dot {
  width: 6px;
  height: 6px;
  border-radius: 100px;
  opacity: 1;
  margin-right: 8px;
}
::v-deep .el-table th {
  background: #e4e7ed;
}
::v-deep .el-table th > .cell {
  color: #1e2228;
  font-weight: 600;
  font-size: 14px;
}
::v-deep .el-table__body-wrapper {
  height: 288px;
  overflow: auto;
}
</style>
