<template>
  <el-dialog
    :visible.sync="visible"
    title="核定信息下载"
    width="940px"
    style="overflow-x: hidden"
  >
    <el-table :data="tableData" :loading="loading">
      <!-- 第一列，单选按钮 -->
      <el-table-column label="选择" width="60">
        <template slot-scope="scope">
          <input
            type="radio"
            @change="(e) => select(e, scope.row)"
            :checked="isChecked(scope.row)"
          />
        </template>
      </el-table-column>

      <!-- 主管税务局名称 -->
      <el-table-column
        prop="zgswjmc"
        label="主管税务局名称"
        width="200"
      ></el-table-column>

      <!-- 主管税务所 -->
      <el-table-column
        prop="zgswskfj"
        label="主管税务所（科、分局）"
        width="200"
      ></el-table-column>

      <!-- 登记序号 -->
      <el-table-column prop="djxh" label="登记序号" width="260">
        <template slot-scope="scope">
          <span>
            {{ scope.row.djxh }}
            <span v-if="scope.row.isDefault">(上次选择)</span>
          </span>
        </template>
      </el-table-column>
      <!-- 登记日期 -->
      <el-table-column prop="djrq" label="登记日期"></el-table-column>
    </el-table>
    <template slot="footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button
        v-if="tableData && tableData.length"
        :loading="submitting"
        type="primary"
        @click="submit"
      >
        确定并验证
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { apiTaxRegistrationNoList } from "../store/api";
import { apiDealTaxSubject } from "../../tax/store/api";
export default {
  data() {
    return {
      loading: true,
      visible: false,
      tableData: [],
      selectedRow: null,
      taxSub: null,
      submitting: false,
    };
  },
  methods: {
    isChecked(row) {
      return this.selectedRow && this.selectedRow.djxh === row.djxh;
    },
    select(e, row) {
      if (e.target.checked) {
        this.selectedRow = row;
      }
    },
    open(taxSub) {
      this.visible = true;
      this.taxSub = taxSub;
      this.loadData(taxSub.taxSubId);
    },
    close() {
      this.visible = false;
      this.selectedRow = null;
      this.taxSub = null;
      this.tableData = [];
      this.loading = true;
    },

    async loadData(taxSubID) {
      this.loading = true;
      const r = await apiTaxRegistrationNoList({
        id: taxSubID,
      });

      this.loading = false;
      if (!r.success) {
        return;
      }

      this.tableData = r.data;
      for (var c of this.tableData) {
        if (c.isDefault) {
          this.selectedRow = c;
        }
      }
    },
    async submit() {
      if (!this.selectedRow) {
        this.$message.error("请选择登记序号");
        return;
      }
      this.submitting = true;

      const request = {
        taxSubId: this.taxSub.taxSubId,
        taxPayerNo: this.taxSub.taxPayerNo,
        remark: this.taxSub.remark,
        pwd: this.taxSub.reportPwd,
        reportPwd: this.taxSub.reportPwd,
        taxSubName: this.taxSub.taxSubName,
        djxh: this.selectedRow.djxh,
        registrationNo: this.selectedRow.djxh,
        contactName: this.taxSub.contactName,
        contactPhone: this.taxSub.contactPhone,
        legalName: null,
        areaName: null,
        areaId: null,
        contractAuthFailReason: null,
        employeeEnableYn: true,
        contractEnableYn: false,
        taxEnableYn: true,
        updateTime: null,
        userName: null,
        userPhone: null,
        bmbh: null,
        ecif: null,
      };

      const r = await apiDealTaxSubject(request);

      this.submitting = false;
      this.close();
      this.$emit("done");
    },
  },
};
</script>

<style scoped>
</style>