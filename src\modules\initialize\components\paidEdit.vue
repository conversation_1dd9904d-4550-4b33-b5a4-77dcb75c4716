<template>
  <div class="paid-eidt">
    <el-form :rules="rules" label-width="110px" ref="form" :model="form">
      <div class="secTitle">基本信息</div>
      <el-form-item label="企业网银客户号" prop="ecif" v-if="ecif">
        <el-input v-model="ecif" disabled></el-input>
      </el-form-item>
      <el-form-item label="法人实体名称" prop="taxSubName">
        <el-input v-model="form.taxSubName" maxlength="61"></el-input>
      </el-form-item>
      <el-form-item label="纳税人识别号" prop="taxPayerNo">
        <el-input
          v-model.trim="form.taxPayerNo"
          :disabled="
            form.contractAuthStatus === 'SUCCESS' ||
            form.accreditStatus === 'SUCCESS'
          "
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="部门编号" prop="bmbh">
        <el-input v-model="form.bmbh" maxlength="3" onkeyup="this.value = this.value.replace(/[^\d]/g,'')"></el-input>
      </el-form-item> -->
      <el-form-item label="所属区域" prop="areaId">
        <el-select v-model="areaName" filterable @change="changeArea" clearable>
          <el-option
            v-for="(item, index) in areaInfoList"
            :key="index"
            :label="item.areaName"
            :value="item.areaName"
          ></el-option>
        </el-select>
        <span
          class="tip"
          style="color: #4f71ff; cursor: pointer; padding-left: 20px"
          @click="$router.push('/initialize/set-belong-area')"
          >自定义所属区域</span
        >
      </el-form-item>
      <el-form-item label="联系人姓名" prop="contactName">
        <el-input v-model.trim="form.contactName" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="联系人电话" prop="contactPhone">
        <el-input v-model.trim="form.contactPhone"></el-input>
      </el-form-item>
      <div class="secTitle">公司属性</div>
      <el-form-item label="用工主体">
        <el-switch
          v-model="form.employeeEnableYn"
          active-text="开启后，员工入职时用工信息中可选择使用"
        >
        </el-switch>
      </el-form-item>
      <el-form-item label="合同主体">
        <el-switch
          @change="handleChangeContract"
          v-model="form.contractEnableYn"
          active-text="开启后并验证后，员工合同信息和发起电子合同签署时可选择使用"
        >
        </el-switch>
      </el-form-item>
      <el-form-item label="纳税主体">
        <el-switch
          v-model="form.taxEnableYn"
          active-text="开启并验证后，该法人实体下的员工可进行纳税申报等事务"
          :disabled="form.accreditStatus === 'SUCCESS'"
        >
        </el-switch>
      </el-form-item>
    </el-form>
    <span slot="footer" class="con-footer">
      <el-button type="primary" @click="handleSave" v-prevent-re-click>
        保存
      </el-button>
      <el-button
        v-show="form.contractEnableYn"
        @click="handleContract"
        v-prevent-re-click
      >
        保存并认证合同主体
      </el-button>
      <el-button
        v-show="form.taxEnableYn"
        @click="handleVerify"
        v-prevent-re-click
      >
        保存并验证纳税主体
      </el-button>
    </span>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { apiAddTaxSubject } from "../store/api";
import { validateTel } from "assets/js/utils/validate";

export default {
  computed: {
    ...mapState({
      areaList: (state) => state.areaList,
    }),
  },
  data() {
    return {
      form: {
        taxSubId: "",
        taxPayerNo: "",
        employeeEnableYn: true,
        contractEnableYn: false,
        taxEnableYn: true,
        contractAuthStatus: "",
        bmbh: "",
      },
      rules: {
        taxSubName: [
          {
            required: true,
            message: "请输入法人实体名称",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              let ruleName = /^[\u4E00-\u9FA5a-z0-9\s\(\)\（\）]+$/i;
              if (ruleName.test(value)) {
                callback();
              } else {
                callback(
                  new Error(
                    "法人实体名称格式不正确，支持中英文、数字、（）和空格"
                  )
                );
              }
            },
            trigger: "blur",
          },
        ],
        taxPayerNo: [
          {
            required: true,
            message: "请输入纳税人识别号",
            trigger: "blur",
          },
        ],
        contactName: {
          required: true,
          message: "请输入联系人姓名",
          trigger: "blur",
        },
        contactPhone: [
          {
            required: true,
            message: "请输入联系人电话",
            trigger: "blur",
          },
          {
            validator: validateTel,
            message: "请输入正确联系人电话",
            trigger: "blur",
          },
        ],
      },
      list: [],
      checked: true,
      areaInfoList: [],
      areaName: "",
      ecif: "",
    };
  },
  methods: {
    show(data) {
      if (data) {
        this.ecif = data.ecif;
        this.form = JSON.parse(JSON.stringify(data));
        this.areaName = this.form.areaName;
      } else {
        for (let key in this.form) {
          this.form[key] = "";
        }
        this.form.employeeEnableYn = true;
        this.form.contractEnableYn = false;
        this.form.taxEnableYn = true;
      }
      this.areaInfoList = this.areaList.filter((item) => item.id);
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
      console.log(this.form);
    },
    //保存
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.taxSubName = this.form.taxSubName.trim();
          apiAddTaxSubject(this.form).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.$emit("refresh");
            }
          });
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    //保存并验证纳税主体
    async handleVerify() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          apiAddTaxSubject(this.form).then((res) => {
            if (res.success) {
              this.$store
                .dispatch("actionCheckSalaryBusiness", {
                  code: "salary",
                })
                .then((response) => {
                  if (response.success && response.data.isOpen) {
                    this.form.taxSubId = res.data.taxSubId;
                    this.$emit("saveAndVerify", this.form);
                  } else {
                    this.$message.error(
                      "您还没有开通薪税服务，无法验证纳税主体"
                    );
                  }
                });
            }
          });
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    //保存并认证合同主体
    async handleContract() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          apiAddTaxSubject(this.form).then((res) => {
            if (res.success) {
              this.form.taxSubId = res.data.taxSubId;
              this.$emit("saveAndContract", this.form);
            }
          });
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    //下拉框change
    changeArea(val) {
      if (val) {
        this.areaName = val;
        let data = this.areaInfoList.filter((item) => item.areaName === val);
        this.form.areaId = data[0].id;
      } else {
        this.areaName = "";
        this.form.areaName = "";
        this.form.areaId = "";
      }
    },
    //修改合同主体
    handleChangeContract() {
      if (!this.form.contractEnableYn) {
        if (this.form.contractAuthStatus === "COMMIT") {
          this.form.contractEnableYn = true;
          this.$message.warning("请先撤回合同主体身份认证申请");
        }
        if (this.form.contractAuthStatus === "FAIL") {
          this.form.contractEnableYn = true;
          this.$message.warning("请先删除合同主体身份认证申请");
        }
        if (this.form.contractAuthStatus === "SUCCESS") {
          this.form.contractEnableYn = true;
          this.$message.warning("合同主体身份认证申请已通过，无法关闭");
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";
.paid-eidt {
  padding: 0px 30px;
  .el-form {
    padding-right: 10px;
    height: calc(100vh - 110px);
    overflow: auto;
  }
  .secTitle {
    margin: 20px auto;
    font-size: 16px;
  }
  .tip {
    font-size: 12px;
    color: #c0c4cc;
    padding-left: 130px;
    margin-top: -5px;
    margin-bottom: 15px;
  }
  .checkStyle {
    margin: 20px 0px 10px 0px;
  }
  .con-footer {
    position: absolute;
    bottom: 0;
    text-align: center;
    background: #fff;
    width: 100%;
    padding: 10px 0;
  }
}
/deep/ .el-popover {
  padding: 12px 0 !important;
}
.area-item {
  width: 250px;
  line-height: 30px;
  text-align: left;
  padding: 0 20px;
  cursor: pointer;
}
.area-item:hover {
  background-color: #f5f7fa;
}
</style>
