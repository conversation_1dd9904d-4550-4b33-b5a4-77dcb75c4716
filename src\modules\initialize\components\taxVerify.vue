<template>
  <div class="paid-eidt">
    <el-form
      :rules="taxListRules"
      label-width="160px"
      ref="taxListForm"
      class="addForm"
      :model="newBodyFormData"
    >
      <div class="secTitle">公司基本信息</div>
      <el-form-item label="公司全称" prop="taxSubName">
        <el-input
          v-model.trim="newBodyFormData.taxSubName"
          maxlength="61"
        ></el-input>
      </el-form-item>
      <el-form-item label="纳税人识别号" prop="taxPayerNo">
        <el-input v-model.trim="newBodyFormData.taxPayerNo"></el-input>
      </el-form-item>
      <div class="secTitle">纳税主体验证信息</div>
      <el-form-item label="经办人姓名" prop="remark">
        <el-input v-model="newBodyFormData.remark"></el-input>
      </el-form-item>
      <el-form-item label="申报密码" prop="pwd">
        <el-input v-model="newBodyFormData.pwd" type="password"></el-input>
      </el-form-item>
      <div class="tip">自然人电子税务局客户端的申报密码</div>
      <!-- <el-form-item label="部门编号" prop="bmbh">
        <el-input v-model="newBodyFormData.bmbh" maxlength="3" onkeyup="this.value = this.value.replace(/[^\d]/g,'')"></el-input>
      </el-form-item> -->
      <div class="checkStyle">
        <el-checkbox v-model="checked"
          >授权在本系统中实现本公司中人员的报送、算税、报税、缴税等事务</el-checkbox
        >
      </div>
    </el-form>
    <span slot="footer" class="con-footer">
      <el-button type="primary" @click="handleSave">验证</el-button>
      <el-button @click="handleCancel()">取消</el-button>
    </span>
    <!-- 下载-->
    <initSelectS ref="selectSY" :sign="sign" :timeObj="timeObj"></initSelectS>
  </div>
</template>
<script>
import { mapState } from "vuex";
import initSelectS from "./tool/initSelectS";
export default {
  props: {},
  components: { initSelectS },
  data() {
    return {
      newBodyFormData: {
        taxSubId: "",
        taxPayerNo: "",
        remark: "",
        pwd: "",
        // bmbh: "" //部门编号
      },
      taxListRules: {
        remark: [
          {
            required: true,
            message: "请输入经办人姓名",
            trigger: "blur",
          },
        ],
        pwd: [
          {
            required: true,
            message: "请输入申报密码",
            trigger: "blur",
          },
        ],
      },
      list: [],
      checked: true,
      timeObj: {
        first: 3000,
        second: 10000,
        third: 15000,
      },
      sign: "taxVerify",
    };
  },
  watch: {},
  mounted() {},
  methods: {
    show(data) {
      if (data) {
        this.newBodyFormData = {
          ...this.newBodyFormData,
          ...data,
        };

        this.areaName = this.newBodyFormData.areaName;
        this.newBodyFormData.pwd = this.newBodyFormData.reportPwd || "";
      }
      this.$nextTick(() => {
        this.$refs.taxListForm.clearValidate();
      });
    },
    //保存
    handleSave() {
      this.$refs.taxListForm.validate((valid) => {
        if (valid) {
          if (!this.checked) {
            this.$message.warning("请先授权本系统权限");
            return;
          }
          let paramsObj = {
            validParameter: this.newBodyFormData,
            querytAction: "taxPageStore/actionAccreditQuery",
            validAction: "taxPageStore/actionDealTaxSubject",
            stopTip: "授权",
            freeBackTip: "【获取反馈】",
          };
          this.$refs.selectSY.show(true, paramsObj);
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    handleCancel() {
      this.$emit("hanleClose");
    },
    //子组件触发刷新
    freshList(data) {
      if (data === this.sign) {
        this.handleCancel();
        this.$emit("refresh");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";
.paid-eidt {
  padding: 0px 30px;
  .secTitle {
    margin: 20px auto;
    font-size: 16px;
  }
  .tip {
    font-size: 12px;
    color: #c0c4cc;
    padding-left: 130px;
    margin-top: -5px;
    margin-bottom: 15px;
  }
  .checkStyle {
    margin: 20px 0px 10px 0px;
  }
  .con-footer {
    position: absolute;
    bottom: 0;
    text-align: center;
    background: #fff;
    width: 100%;
    padding: 10px 0;
  }
  .addForm {
    padding-bottom: 80px;
  }
}
/deep/ .el-popover {
  padding: 12px 0 !important;
}
.area-item {
  width: 250px;
  line-height: 30px;
  text-align: left;
  padding: 0 20px;
  cursor: pointer;
}
.area-item:hover {
  background-color: #f5f7fa;
}
</style>
