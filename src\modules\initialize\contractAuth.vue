<template>
  <div class="contract-auth">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>合同主体身份认证申请</span>
        </el-col>
      </el-row>
    </header>
    <section class="content-con">
      <div style="position: relative">
        <p class="auth-title">
          <span style="font-size: 16px">身份认证流程</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="提示：合同主体公司身份认证通过后，才能适用该公司发起电子合同签署流程"
            placement="top-start"
          >
            <i class="iconfont iconwenhao"></i>
          </el-tooltip>
          <span
            v-show="form.contractAuthStatus"
            class="auth-status"
            :class="
              form.contractAuthStatus === 'SUCCESS'
                ? 'success-status'
                : form.contractAuthStatus === 'FAIL'
                ? 'error-status'
                : 'other-status'
            "
          >
            {{ form.contractAuthStatus | contractAuthStatus }}
          </span>
          <span v-show="isShowDate"
            >{{
              form.contractAuthStatus === "SUCCESS"
                ? "认证通过时间"
                : form.contractAuthStatus === "FAIL"
                ? "认证未通过时间"
                : "认证提交时间"
            }}：{{ form.updateTime }}</span
          >
          <span
            v-show="form.contractAuthStatus === 'FAIL'"
            class="table-name"
            @click="showFail"
            >查看原因</span
          >
        </p>
        <p class="step">
          1.企业填写认证申请 > 2.服务商审核认证信息(2个工作日内) > 3.完成认证
        </p>
        <div class="btn-con">
          <el-button size="small" v-show="isShowDelete" @click="handleDelete"
            >删除</el-button
          >
          <el-button
            size="small"
            v-show="form.contractAuthStatus === 'COMMIT'"
            @click="handleBack"
            >撤回</el-button
          >
          <el-button
            size="small"
            @click="handleSeeSeal"
            v-show="form.contractAuthStatus === 'SUCCESS'"
            >查看电子章</el-button
          >
        </div>
      </div>
      <el-form :model="form" ref="form" :rules="rule" label-width="130px">
        <p class="title">基本信息</p>
        <el-form-item label="公司全称">
          <el-input
            v-model.trim="form.taxSubName"
            :disabled="true"
            v-if="isEdit"
          ></el-input>
          <span v-else>{{ form.taxSubName }}</span>
        </el-form-item>
        <el-form-item label="统一社会信息代码">
          <el-input
            v-model.trim="form.taxPayerNo"
            :disabled="true"
            v-if="isEdit"
          ></el-input>
          <span v-else>{{ form.taxPayerNo }}</span>
        </el-form-item>
        <p class="title">法人信息</p>
        <el-form-item label="姓名" prop="legalName">
          <el-input v-model.trim="form.legalName" v-if="isEdit"></el-input>
          <span v-else>{{ form.legalName }}</span>
        </el-form-item>
        <el-form-item label="证件类型" prop="idType" @change="changeIdType">
          <el-select
            v-model.trim="form.idType"
            placeholder="请选择证件类型"
            v-if="isEdit"
            clearable
          >
            <el-option
              v-for="item in idTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <span v-else>{{ getIdType(form.idType) }}</span>
        </el-form-item>
        <el-form-item label="证件号码" prop="idNo">
          <el-input v-model.trim="form.idNo" v-if="isEdit"></el-input>
          <span v-else>{{ form.idNo }}</span>
        </el-form-item>
        <div style="margin-bottom: 20px">
          <p class="title">证明材料</p>
          <p class="label-title">
            <span>*</span>
            营业执照照片
            <span
              >温馨提示：本公司承诺营业执照图片及信息的严格保密义务，未经明确授权，不将其信息非法使用。</span
            >
          </p>
          <div class="uploadMain">
            <old-upload
              :isEdit="isEdit"
              :exampleImg="businessLicenseImg"
              :fileList="businessLicenseFileList"
              provideheight="296px"
              providewidth="240px"
              showExample="true"
              showText="true"
              tipText="上传营业执照照片"
              exampleTipText="示例"
              oldUploadBackground="businessLicenseBg"
              @handleSuccess="uploadBusinessLicenseImg"
              @handleRemove="handleRemoveBusinessLicense"
              :imageUrl="imageUrl"
            ></old-upload>
            <img src="" />
          </div>
          <p class="label-title">
            <span>*</span>
            法人证件照照片
            <span
              >温馨提示：本公司承诺营业执照图片及信息的严格保密义务，未经明确授权，不将其信息非法使用。</span
            >
          </p>
          <div class="uploadMain">
            <old-upload
              :isEdit="isEdit"
              :exampleImg="copyEp"
              :fileList="legalIdCardImageList"
              provideheight="296px"
              providewidth="240px"
              showExample="true"
              showText="true"
              tipText="上传法人证件照片"
              exampleTipText="示例"
              oldUploadBackground="idCardCopy"
              @handleSuccess="uploadLegalIdCardFront"
              @handleRemove="handleRemoveLegalIdCardImage"
            ></old-upload>
          </div>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="申请人姓名">
              <el-input
                v-model.trim="form.applyName"
                :disabled="true"
                v-if="isEdit"
              ></el-input>
              <span v-else>{{ form.applyName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码">
              <el-input
                v-model.trim="form.applyMobile"
                :disabled="true"
                v-if="isEdit"
              ></el-input>
              <span v-else>{{ form.applyMobile }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="footer" v-show="isEdit">
        <el-button type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="handleTmpSave">暂存</el-button>
        <el-button @click="$router.push('/initialize/paid')">取消</el-button>
      </div>
      <!-- 查看电子章 -->
      <el-dialog title="查看电子章" :visible.sync="isShowSeal" width="740px">
        <div class="seal-list">
          <div class="seal-item" v-for="item in sealFileList" :key="item.id">
            <img :src="item.url" />
            <p>
              <el-radio
                v-model="sealDefault"
                :label="item.id"
                @change="handleSetDefault"
                >设置默认</el-radio
              >
              <span
                class="seal-delete"
                v-if="item.signImageType !== 'SYSTEM'"
                @click="handleDeleteSeal(item)"
              >
                删除
              </span>
            </p>
          </div>
          <el-upload
            class="seal-upload"
            :headers="heanderToken"
            :action="uploadUrl"
            accept=".jpg,.jpeg,.png,.pdf"
            :before-upload="beforeUpload"
            :on-success="handleSuccess"
          >
            <el-button size="mini" type="primary">上传</el-button>
            <div slot="tip" class="el-upload__tip">
              自定义电子章
              <p>图片格式为 JPG/JPEG/PNG/PDF大小在2MB内</p>
            </div>
          </el-upload>
        </div>
      </el-dialog>
    </section>
  </div>
</template>
<script>
import oldUpload from "@/components/old-upload/index";
import { baseUrl } from "@/request/fetch";
import {
  apiGetTaxAuthInfo,
  apiGetSealList,
  apiUpdateStatus,
  apiDeleteTaxSub,
  apiUploadSealImg,
  apiSetDefaultSeal,
  apiDeleteSeal,
} from "./store/api";
export default {
  components: { oldUpload },
  data() {
    return {
      uploadUrl: baseUrl + "/api/hrsaas-emp/archive/upload",
      taxSubId: this.$route.query.taxSubId,
      form: {},
      rule: {
        legalName: {
          required: true,
          message: "请填写姓名",
          trigger: "blur",
        },
        idType: {
          required: true,
          message: "请选择证件类型",
          trigger: "change",
        },
        idNo: {
          required: true,
          validator: this.validIdNo,
          trigger: "blur",
        },
      },
      idTypeList: [
        { label: "居民身份证", value: "PRC_ID" },
        { label: "护照", value: "PASSPORT" },
        { label: "港澳台同胞回乡证", value: "COMPATRIOTS_CARD" },
        { label: "外国人永久居留证", value: "PERMANENT_RESIDENCE" },
      ],
      businessLicenseImg: require("../../assets/images/businessLicenseEp.png"),
      copyEp: require("../../assets/images/copyEp.png"),
      businessLicenseFileList: [],
      legalIdCardImageList: [],
      isEdit: false, //是否编辑
      isShowSeal: false,
      sealDefault: "",
      sealFileList: [],
      heanderToken: {
        Authorization: this.$getToken(),
      },
    };
  },
  computed: {
    isShowDate: function () {
      return ["COMMIT", "SUCCESS", "FAIL"].includes(
        this.form.contractAuthStatus
      );
    },
    isShowDelete: function () {
      return ["STASH", "FAIL"].includes(this.form.contractAuthStatus);
    },
  },
  created() {
    this.getTaxSubDetail();
  },
  methods: {
    async getTaxSubDetail() {
      let res = await apiGetTaxAuthInfo({ taxSubId: this.taxSubId });
      if (res.success) {
        this.form = res.data;
        this.form.id = this.form.authInfoId;
        this.businessLicenseFileList = this.form.businessLicenseUrl
          ? [{ url: this.form.businessLicenseUrl }]
          : [];
        this.legalIdCardImageList = this.form.legalIdPhotoUrl
          ? [{ url: this.form.legalIdPhotoUrl }]
          : [];
        //判断认证状态进入查看或者编辑页面
        if (
          res.data.contractAuthStatus === "COMMIT" ||
          res.data.contractAuthStatus === "SUCCESS"
        ) {
          this.isEdit = false;
        } else {
          this.isEdit = true;
        }
      }
    },
    //查看失败原因
    showFail() {
      this.$alert(this.form.contractAuthFailReason, "驳回原因");
    },
    //校验身份证号码是否合规
    validIdNo(rule, value, callback) {
      if (value) {
        switch (this.form.idType) {
          case "PRC_ID":
            var regIdNo = new RegExp(
              "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)"
            );
            if (!regIdNo.test(value)) {
              callback(new Error("请输入正确的身份证号"));
            } else {
              callback();
            }
            break;
          default:
            callback();
        }
      } else {
        callback("请输入身份证号");
      }
    },
    //切换证件类型
    changeIdType() {
      this.$refs.form.validateField("idNo");
    },
    uploadBusinessLicenseImg(data) {
      this.form.businessLicense = data.archiveId;
    },
    uploadLegalIdCardFront(data) {
      this.form.legalIdPhoto = data.archiveId;
    },
    handleRemoveBusinessLicense(currentData, file, fileList) {
      this.businessLicenseFileList = fileList;
      this.form.businessLicense = "";
    },
    handleRemoveLegalIdCardImage(currentData, file, fileList) {
      this.legalIdCardImageList = fileList;
      this.form.legalIdPhoto = "";
    },
    //提交
    async handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.businessLicense) {
            this.$message.warning("请上传营业执照照片");
            return;
          }
          if (!this.form.legalIdPhoto) {
            this.$message.warning("请上传法人证件照照片");
            return;
          }
          this.form.authStatus = "COMMIT";
          apiUpdateStatus(this.form).then((res) => {
            if (res.success) {
              this.$message.success("提交成功，服务商将在2个工作日内完成审核");
              this.$router.go(-1);
            }
          });
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    //暂存
    async handleTmpSave() {
      this.form.authStatus = "STASH";
      let res = await apiUpdateStatus(this.form);
      if (res.success) {
        this.$message.success("暂存成功");
        this.$router.go(-1);
      }
    },
    //撤回
    async handleBack() {
      this.form.authStatus = "STASH";
      let res = await apiUpdateStatus(this.form);
      if (res.success) {
        this.$message.success("撤回成功");
        this.isEdit = true;
        this.getTaxSubDetail();
      }
    },
    //删除
    async handleDelete() {
      let res = await apiDeleteTaxSub({ id: this.form.id });
      if (res.success) {
        this.$message.success("删除成功");
        this.$router.go(-1);
      }
    },
    //查看电子章
    async handleSeeSeal() {
      let res = await apiGetSealList({ taxSubId: this.taxSubId });
      if (res.success) {
        this.sealFileList = res.data;
        let filter = res.data.filter((item) => item.defaultImgYn);
        this.sealDefault = filter.length > 0 ? filter[0].id : "";
      }
      this.isShowSeal = true;
    },
    //证件类型回显
    getIdType(val) {
      return val
        ? this.idTypeList.filter((item) => item.value === val)[0].label
        : "";
    },
    beforeUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isImg =
        testmsg === "jpg" ||
        testmsg === "jpeg" ||
        testmsg === "png" ||
        testmsg === "pdf";
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isImg) {
        this.$message({
          message: "上传文件类型只能是JPG/JPEG/PNG/PDF格式!",
          type: "warning",
        });
        this.fileList = [];
      }
      if (!isLt2M) {
        this.$message({
          message: "上传文件大小不能超过 2MB!",
          type: "warning",
        });
        this.fileList = [];
      }
      return isImg && isLt2M;
    },
    //上传电子章
    async handleSuccess(data) {
      let res = await apiUploadSealImg({
        taxSubId: this.taxSubId,
        imageId: data.data.archiveId,
      });
      if (res.success) {
        this.sealFileList.push(res.data);
      }
    },
    //删除电子章
    async handleDeleteSeal(data) {
      let res = await apiDeleteSeal({ id: data.id });
      if (res.success) {
        this.$message.success("操作成功");
        this.handleSeeSeal();
      }
    },
    //设置默认电子章图片
    async handleSetDefault() {
      let res = await apiSetDefaultSeal({ id: this.sealDefault });
      if (res.success) {
        this.$message.success("操作成功");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.contract-auth {
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }
  .content-con {
    height: calc(100vh - 160px);
    overflow-y: auto;
    margin: 0 20px;
    padding-top: 10px;
  }
  .step {
    display: flex;
    align-items: center;
    margin: 10px 0 20px 40px;
  }
  .title {
    font-size: 18px;
    margin: 10px 0 20px 0;
    display: flex;
    align-items: center;
  }
  .title::before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 18px;
    background-color: #4f71ff;
    border-radius: 3px;
    margin-right: 8px;
  }
  .apply-form {
    .el-form-item {
      margin-bottom: 5px;
    }
  }
  .el-input,
  .el-select {
    width: 250px;
  }
  .label-title {
    span:first-child {
      color: red;
    }
    span:last-child {
      display: inline-block;
      margin-left: 10px;
      color: #999;
    }
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #4f71ff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}
.auth-title {
  display: flex;
  align-items: center;
  i {
    margin-left: 5px;
  }
  span {
    margin-left: 10px;
  }
  span:first-child {
    margin-left: 0;
  }
}
.auth-status {
  border-radius: 8px;
  padding: 0 9px;
}
.success-status {
  border: 1px solid#00B4B3;
  color: #00b4b3;
}
.error-status {
  border: 1px solid#b40000;
  color: #b40000;
}
.other-status {
  border: 1px solid#E2A600;
  color: #e2a600;
}
.btn-con {
  position: absolute;
  right: 10px;
  top: 20px;
}
.uploadMain {
  padding-left: 100px;
  overflow: hidden;
  margin-top: 20px;
  .cardFront {
    float: left;
  }
  .cardBack {
    float: left;
    margin-left: 64px;
  }
}
.footer {
  text-align: center;
  margin-top: 10px;
  border-top: 1px solid #ededed;
  padding-top: 10px;
}
.seal-list {
  display: flex;
  flex-wrap: wrap;
  .seal-item {
    margin: 0 20px 20px 0;
    img {
      width: 150px;
      height: 150px;
      border: 1px solid #ededed;
    }
    > p {
      width: 150px;
      border: 1px solid #ededed;
      border-top: none;
      margin-top: -5px;
      height: 30px;
      line-height: 30px;
      display: flex;
      align-items: center;
    }
    .seal-delete {
      display: inline-block;
      width: 80px;
      text-align: center;
      border-left: 1px solid #ededed;
      cursor: pointer;
    }
    .el-radio {
      width: 100%;
      text-align: center;
      line-height: 30px;
    }
  }
  .seal-upload {
    width: 152px;
    height: 180px;
    text-align: center;
    border: 1px solid #ededed;
    padding-top: 50px;
    box-sizing: border-box;
    .el-upload__tip {
      font-size: 14px;
      color: #333;
      p {
        font-size: 12px;
        color: #606266;
        text-align: left;
        margin: 0 10px;
      }
    }
    /deep/.el-upload-list {
      display: none;
    }
  }
}
</style>
