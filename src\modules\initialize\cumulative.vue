<template>
  <div class="el-diy-month cumulative">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>上月收入与减除填写</span>
        </el-col>
      </el-row>
    </header>
    <!-- <p class="tax-collect-tips">自动获取工资表当月的增减员名单，您只需选择人员“报送”即可，报送后系统会在个税系统中完成人员信息采集</p> -->
    <div class="table-content">
      <div v-show="showFilter">
        <div class="check-staff-menu">
          <div>
            <el-select
              v-model="ruleForm.queryYear"
              placeholder="请选择"
              @change="handleChange"
              clearable
            >
              <el-option
                v-for="(item, index) in selectYear"
                :key="index"
                :value="item"
              ></el-option>
            </el-select>
            <el-input
              placeholder="请输入姓名\工号"
              v-model="ruleForm.nameOrEmpNo"
              prefix-icon="iconiconfonticonfontsousuo1 iconfont"
              @keyup.enter.native="handleSearch"
              clearable
              class="search-input"
            >
            </el-input>
          </div>
          <div>
            <!-- <el-button type="primary" @click="handledDownload" class="add-import">局端在线下载</el-button> -->
            <el-button type="primary" @click="handleImport" class="add-import"
              >导入</el-button
            >
          </div>
        </div>
        <div class="selectCon">
          公司名称：
          <el-select
            v-model="ruleForm.taxSubId"
            placeholder="请选择"
            @change="selectSubject"
            clearable
          >
            <el-option
              v-for="item in taxSubjectInfolist"
              :key="item.taxSubId"
              :label="item.taxSubName"
              :value="item.taxSubId"
            ></el-option>
          </el-select>
        </div>
      </div>
      <!-- <div class="page-component-up">
        <i class="el-icon-caret-top"></i>
      </div>-->
      <div class="staff-table" ref="tableCon">
        <div class="floating-menu" v-if="deleteIdsForm.ids.length > 0">
          <span>已选中{{ deleteIdsForm.ids.length }}人</span>
          <el-button size="small" class="button-mini" @click="handleDeleteItem"
            >批量删除</el-button
          >
        </div>
        <el-table
          :data="list"
          class="check-staff_table"
          :style="{ width: screenWidth - 285 + 'px' }"
          @selection-change="handleSelectItem"
          :height="screenHeight"
          ref="table"
          border
        >
          <el-table-column type="selection" width="55" fixed></el-table-column>
          <el-table-column
            prop="empName"
            label="姓名"
            min-width="80"
          ></el-table-column>
          <el-table-column
            label="工号"
            width="82"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.empNo }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="idNo"
            label="证件号码"
            min-width="170"
          ></el-table-column>
          <el-table-column
            prop="taxSubName"
            label="公司名称"
            width="180"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="endMonthStr"
            label="截止月份"
            min-width="120"
          ></el-table-column>
          <!-- <el-table-column prop="endMonthStr" label="数据来源" min-width="120"></el-table-column> -->
          <el-table-column
            prop="currentIncome"
            label="本期收入"
            min-width="100"
          >
            <template slot-scope="scope">
              <div class="number-right">{{ scope.row.currentIncome }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="currentTaxfreeIncome"
            label="本期免税收入"
            min-width="140"
          >
            <template slot-scope="scope">
              <div class="number-right">
                {{ scope.row.currentTaxfreeIncome }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="pensionInsuranceFee"
            label="养老个人"
            min-width="100"
          >
            <template slot-scope="scope">
              <div class="number-right">
                {{ scope.row.pensionInsuranceFee }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="medicalInsuranceFee"
            label="医疗个人"
            min-width="100"
          >
            <template slot-scope="scope">
              <div class="number-right">
                {{ scope.row.medicalInsuranceFee }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="unemploymentInsuranceFee"
            label="失业个人"
            min-width="140"
          >
            <template slot-scope="scope">
              <div class="number-right">
                {{ scope.row.unemploymentInsuranceFee }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="housingFund"
            label="公积金个人"
            min-width="140"
          >
            <template slot-scope="scope">
              <div class="number-right">{{ scope.row.housingFund }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="totalChildrenEdu"
            label="累计子女教育"
            min-width="140"
          >
            <template slot-scope="scope">
              <div class="number-right">{{ scope.row.totalChildrenEdu }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="totalFurtherEdu"
            label="累计继续教育"
            min-width="140"
          >
            <template slot-scope="scope">
              <div class="number-right">{{ scope.row.totalFurtherEdu }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="totalyyezhfzc"
            label="累计婴幼儿照护费"
            width="140"
          >
            <template slot-scope="scope">
              <div class="number-right">
                {{ scope.row.totalyyezhfzc }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="totalHomeLoads"
            label="累计住房贷款利息"
            min-width="140"
          >
            <template slot-scope="scope">
              <div class="number-right">{{ scope.row.totalHomeLoads }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="totalHouseRent"
            label="累计住房租金"
            min-width="140"
          >
            <template slot-scope="scope">
              <div class="number-right">{{ scope.row.totalHouseRent }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="totalSupportParents"
            label="累计赡养老人"
            min-width="140"
          >
            <template slot-scope="scope">
              <div class="number-right">
                {{ scope.row.totalSupportParents }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="yearMount" label="年金" min-width="60">
            <template slot-scope="scope">
              <div class="number-right">{{ scope.row.yearMount }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="bussHealthInsuranceFee"
            label="商业健康保险"
            min-width="140"
          >
            <template slot-scope="scope">
              <div class="number-right">
                {{ scope.row.bussHealthInsuranceFee }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="taxDeferPensionInsuranceFee"
            label="税延养老保险"
            min-width="140"
          >
            <template slot-scope="scope">
              <div class="number-right">
                {{ scope.row.taxDeferPensionInsuranceFee }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="otherMonth" label="其他" min-width="60">
            <template slot-scope="scope">
              <div class="number-right">{{ scope.row.otherMonth }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="totalDonated"
            label="准予扣除的捐赠额"
            min-width="140"
          ></el-table-column>
          <el-table-column
            prop="taxBreakTotal"
            label="减免税额"
            min-width="140"
          ></el-table-column>
          <el-table-column label="操作" fixed="right">
            <template slot-scope="scope">
              <span @click="handleDelete(scope.row.id)" class="funStyle"
                >删除</span
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @current-change="handleSelectionChange"
          :page-size="ruleForm.pageSize"
          layout="prev, pager, next"
          :total="total"
          class="staff-page"
        ></el-pagination>
      </div>
    </div>
    <import-data
      ref="import"
      :radioList="radioList"
      :title="'收入与减除导入'"
      :apiCheck="
        baseUrl + '/api/hrsaas-salary/taxTotalBase/importTaxTotalBaseCheck'
      "
      :apiDownloadLog="'cumulativePageStore/getExportErrorRecord'"
      :apiDownloadTemplate="'cumulativePageStore/actionDownloadTemplate'"
      :parameterData="parameterData"
      sendRadio="1"
      @changeRadioValue="changeRadioValue"
      :impoartAction="'cumulativePageStore/actionImportTaxTotalBaseSuccess'"
      @getLoading="refresh"
      :uploadFileData="uploadFileData"
      :tips="'说明：再次导入后将覆盖同月数据'"
    ></import-data>
  </div>
</template>
<script>
import { baseUrl } from "@/request/fetch";
import RouterLink from "olading-ui/lib/mixins/router-link";
let maxYear = new Date().getFullYear();
let year = [];
for (let i = maxYear; i >= 2019; i--) {
  year.push(i);
}
import importData from "@/components/tool/importData";
import { mapState } from "vuex";
export default {
  components: {
    RouterLink,
    importData,
  },
  data() {
    return {
      deleteIdsForm: {
        ids: [],
      },
      uploadFileData: {
        uuid: "",
      },
      selectYear: year,
      radioList: [
        { lable: "1", title: "通过员工工号匹配人员" },
        { lable: "2", title: "通过身份证匹配人员" },
      ],
      parameterData: {
        type: 1,
        year: "",
      },
      ruleForm: {
        nameOrEmpNo: "",
        currPage: 1,
        pageSize: 20,
        queryYear: year[0],
        taxSubId: "",
      },
      total: 0,
      fileList: [],
      screenWidth: document.body.clientWidth, // 屏幕尺寸
      screenHeight: document.body.clientHeight - 330,
      list: [],
      taxSubjectInfolist: [],
      showFilter: true,
      baseUrl: baseUrl,
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  mounted() {
    this.getTaxSubjectInfoList();
  },
  methods: {
    //表格选中事件
    handleSelectItem(row) {
      this.deleteIdsForm.ids = [];
      row.forEach((element) => {
        this.deleteIdsForm.ids.push(element.id);
      });
    },
    //扣缴义务人集合
    getTaxSubjectInfoList() {
      this.$store.dispatch("taxPageStore/actionTaxSubjectList").then((res) => {
        if (res.success) {
          this.taxSubjectInfolist = res.data;
          this.ruleForm.taxSubId = this.taxSubjectInfolist[0]["taxSubId"];
          this.getList();
        }
      });
    },
    selectSubject(data) {
      this.getList();
    },
    changeRadioValue(val) {
      this.parameterData.type = val;
    },
    handleImport() {
      this.parameterData.year = this.ruleForm.queryYear;
      this.$refs.import.show();
    },
    refresh(data) {
      this.ruleForm.nameOrEmpNo = "";
      this.ruleForm.currPage = 1;
      this.getList();
    },
    //通过姓名或工号搜索
    handleSearch() {
      this.ruleForm.currPage = 1;
      this.getList();
    },
    //年份搜索
    handleChange(val) {
      this.ruleForm.queryYear = val;
      this.ruleForm.currPage = 1;
      this.getList();
    },
    //翻页
    handleSelectionChange(val) {
      this.ruleForm.currPage = val;
      this.getList();
    },
    //搜索接口
    getList() {
      this.loading = true;
      this.$store
        .dispatch("cumulativePageStore/actionTaxTotalBaseList", this.ruleForm)
        .then((res) => {
          if (res.success) {
            this.loading = false;
            this.list = res.data.data;
            this.total = res.data.count;
          }
        });
    },
    //删除接口
    handleDeleteItem() {
      this.$confirm(
        "您确定删除员工本年的累计数据，如果是，请点击“确定”，如果否，请点击“取消”",
        "删除确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: false,
          closeOnClickModal: false,
          closeOnPressEscape: false,
        }
      )
        .then(() => {
          this.$store
            .dispatch(
              "cumulativePageStore/actionDelTaxTotalBase",
              this.deleteIdsForm
            )
            .then((res) => {
              if (res.success) {
                this.getList();
                this.deleteIdsForm.ids = [];
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //删除
    handleDelete(id) {
      this.deleteIdsForm.ids = [id];
      this.handleDeleteItem();
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.cumulative {
  .header {
    padding: 0 20px;
    font-size: 14px;
    height: 61px;
    line-height: 61px;
    border-bottom: 1px solid #ededed;
  }
  .selectCon {
    margin: 10px 0px;
  }
  .table-content {
    padding: 0px 20px;
    .search-input {
      margin-left: 10px;
    }
  }
  .check-search {
    margin-left: 20px;
  }
  .content-header {
    position: relative;
    font-size: 18px;
    margin-bottom: 30px;
    cursor: pointer;
    i {
      font-size: 16px;
      color: #ccc;
    }
    .rotate-el-icon-arrow-left {
      transform: rotate(180deg);
    }
    span {
      position: absolute;
      left: 32px;
      top: 3px;
      z-index: 0;
    }
  }
  .back-style {
    display: inline-block;
    cursor: pointer;
    color: $mainColor;
  }
  .header-line {
    display: inline-block;
    padding: 0 10px;
  }
  .add-import {
    margin-right: 10px;
  }
  .iconiconfonticonfontsousuo1 {
    font-size: 12px;
  }
  .staff-situation {
    .staff-total {
      border-right: 1px solid #e6e6e6;
      padding-right: 15px;
      margin-right: 15px;
    }
    margin-top: 20px;
    color: #999;
    font-size: 12px;
    i {
      color: $mainColor;
      font-style: normal;
      padding: 0 3px;
    }
  }
  .hiden-con {
    width: 150px;
    overflow: hidden;
    word-break: keep-all;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }
  .page-component-up {
    background-color: #fff;
    /*position: fixed;*/
    /*right: 100px;*/
    /*bottom: 150px;*/
    width: 40px;
    height: 40px;
    border-radius: 20px;
    cursor: pointer;
    transition: 0.3s;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.12);
    z-index: 5;
  }
  .page-component-up i {
    color: #4f71ff;
    display: block;
    line-height: 40px;
    text-align: center;
    font-size: 18px;
  }
}
</style>
