<template>
  <div class="paid def_per_height" v-loading="fullScreenLoading">
    <div class="tax el-diy-month">
      <header class="header main-title">
        <el-row type="flex">
          <el-col :span="12">
            <span>法人实体管理</span>
          </el-col>
        </el-row>
      </header>
      <div class="tax-content">
        <div class="screening">
          <div class="check-staff-menu">
            <div>
              <el-input
                placeholder="请输入法人实体名称"
                v-model="ruleForm.name"
                @keyup.enter.native="handleSearch"
                prefix-icon="iconiconfonticonfontsousuo1 iconfont"
                clearable
                class="search-input"
              ></el-input>
            </div>
            <div>
              <el-button
                style="margin-right: 10px"
                type="primary"
                @click="handleShowBox"
                v-if="
                  privilegeVoList.includes('hrEmployee.init.taxSubject.save')
                "
                >新增</el-button
              >
              <el-dropdown trigger="click">
                <el-button>
                  更多操作
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-prevent-re-click
                    @click.native="handleImport"
                    >批量导入
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-prevent-re-click
                    @click.native="batchTaxPayerVerify"
                    >批量纳税主体验证
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-prevent-re-click
                    @click.native="batchGetFeedback"
                    >批量获取反馈
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <!-- <el-button
                class="add-import"
                @click="handleExport"
                v-if="privilegeVoList.includes('hrEmployee.init.taxSubject.export')"
                >导出</el-button
              > -->
            </div>
          </div>
          <div class="area-container">
            <div class="drop-down">
              区域名称:
              <el-select
                v-model="ruleForm.areaId"
                placeholder="请选择"
                filterable
                @change="handleCheckArea"
                class="border-option"
                clearable
              >
                <el-option
                  v-for="item in areaList"
                  :key="item.id"
                  :label="item.areaName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </div>
            <span @click="$router.push('/initialize/set-belong-area')"
              >自定义区域</span
            >
          </div>

          <div class="staff-table">
            <el-table
              ref="elTable"
              v-loading="loading"
              v-el-table-infinite-scroll="load"
              :data="tableData"
              class="check-staff_table"
              height="calc(100vh - 64px - 49px - 40px - 62px - 100px)"
              border
            >
              <el-table-column
                label="序号"
                type="index"
                :width="fitWidth"
              ></el-table-column>
              <el-table-column
                prop="taxSubName"
                label="法人实体名称"
                min-width="180"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="taxPayerNo"
                label="纳税人识别号"
                min-width="180"
                align="left"
                :show-overflow-tooltip="true"
              ></el-table-column>
              <el-table-column
                label="所属区域"
                prop="areaName"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="employeeEnableYn"
                label="用工主体"
                width="80"
              >
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.employeeEnableYn"
                    :disabled="true"
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column
                prop="contractEnableYn"
                label="合同主体"
                width="80"
              >
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.contractEnableYn"
                    :disabled="true"
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="taxEnableYn" label="纳税主体" width="80">
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.taxEnableYn"
                    :disabled="true"
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column
                prop="contractAuthStatus"
                label="身份认证状态"
                width="100"
              >
                <template slot-scope="scope">
                  <span>
                    {{ scope.row.contractAuthStatus | contractAuthStatus }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                prop="accreditStatus"
                label="验证状态"
                width="140"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.accreditStatus | accreditStatus }}</span>
                  <el-popover
                    v-if="scope.row.accreditStatus === 'FAIL'"
                    popper-class="paid-popper-class"
                    trigger="hover"
                    placement="bottom"
                    :open-delay="300"
                    width="200"
                    :content="scope.row.failReason"
                  >
                    <el-button type="text" slot="reference">
                      查看原因
                    </el-button>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100px">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    @click="handleQuery(scope.row)"
                    v-if="scope.row.accreditStatus === 'WAIT_ACCREDIT'"
                  >
                    获取反馈
                  </el-button>
                  <div v-else>
                    <el-button
                      type="text"
                      @click="handleEditor(scope.row)"
                      v-if="
                        privilegeVoList.includes(
                          'hrEmployee.init.taxSubject.save'
                        )
                      "
                    >
                      编辑
                    </el-button>
                    <el-dropdown
                      trigger="click"
                      placement="bottom"
                      style="margin-left: 10px"
                    >
                      <el-button type="text">更多</el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          @click.native="handleTaxVerify(scope.row)"
                          v-if="scope.row.taxEnableYn"
                        >
                          纳税主体验证
                        </el-dropdown-item>
                        <el-dropdown-item
                          @click.native="handleAuthContract(scope.row)"
                          v-if="scope.row.contractEnableYn"
                        >
                          合同主体认证
                        </el-dropdown-item>
                        <el-dropdown-item
                          @click.native="chooseRegistrationNo(scope.row)"
                        >
                          核定信息下载
                        </el-dropdown-item>
                        <!-- <el-dropdown-item
                          @click.native="handleDelete(scope.row.taxSubId)"
                          v-if="
                            privilegeVoList.includes(
                              'hrEmployee.init.taxSubject.delete'
                            )
                          "
                        >
                          删除
                        </el-dropdown-item> -->
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      :visible.sync="tipVisible"
      title="提示"
      width="500px"
      class="diy-el_dialog"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div>由于税务接口限制，最大可支持100条纳税验证信息的提交。</div>
      <div slot="footer">
        <el-button @click="batchTaxPayerVerifyConfirm" type="primary" plain>
          我知道了
        </el-button>
      </div>
    </el-dialog>

    <el-drawer
      :title="isEditPaid ? '编辑法人实体' : '新增法人实体'"
      :visible.sync="popShow.isshow"
      ref="drawer"
      size="650px"
      class="paid-box"
    >
      <paidEdit
        @refresh="refresh"
        @saveAndVerify="saveAndVerify"
        @saveAndContract="saveAndContract"
        ref="paidEdit"
      ></paidEdit>
    </el-drawer>
    <el-drawer
      title="纳税主体验证"
      :visible.sync="isshowTaxVerify"
      ref="drawer"
      size="600px"
      class="paid-box verify-box"
    >
      <taxVerify
        @refresh="refresh"
        @hanleClose="hanleClose"
        ref="taxVerify"
      ></taxVerify>
    </el-drawer>
    <!-- 查询-->
    <initFeedback ref="feedback" :sign="sign"></initFeedback>
    <BatchImport
      ref="batchImport"
      title="批量导入"
      :apiCheck="
        baseUrl + '/api/hrsaas-salary/taxSubject/template/importVerify'
      "
      :apiDownloadTemplate="apiTemplateDownload"
      :apiConfirmImport="apiConfirmImport"
      :apiImportVerifyErrorLog="apiImportVerifyErrorLog"
      @refresh="getList"
    />
    <BatchVerify
      ref="batchVerify"
      stopTip=""
      :timeObj="timeObj"
      :freeBackTip="freeBackTip"
      @refresh="getList"
    ></BatchVerify>
    <BatchFeedback
      ref="batchFeedback"
      :querytAction="queryAction"
      stopTip=""
      :timeObj="timeObj"
      :freeBackTip="freeBackTip"
      @refresh="getList"
    ></BatchFeedback>
    <DialogChooseRegistrationNo ref="chooseRegistrationNo" @done="refresh" />
  </div>
</template>
<script>
import { mapState } from "vuex";
import rightPop from "@/components/basic/rightPop";
import paidEdit from "./components/paidEdit";
import taxVerify from "./components/taxVerify";
import initFeedback from "./components/tool/initFeedback";
import BatchImport from "@/components/tool/batchImport";
import BatchVerify from "./components/batchVerify";
import BatchFeedback from "./components/batchFeedback";
import DialogChooseRegistrationNo from "./components/dialogChooseRegistrationNo";
import * as AT from "@/store/actionTypes";
import { baseUrl } from "@/request/fetch";
import {
  apiTemplateDownload,
  apiConfirmImport,
  apiImportVerifyErrorLog,
  apiTaxSubBatchSubmit,
  apiTaxSubBatchSubmitBack,
} from "./store/api";

const pageSize = 20;
export default {
  components: {
    rightPop,
    paidEdit,
    taxVerify,
    initFeedback,
    BatchImport,
    BatchVerify,
    BatchFeedback,
    DialogChooseRegistrationNo,
  },
  data() {
    return {
      loading: false,
      lastIndex: pageSize,
      ruleForm: {
        name: "",
        areaId: "",
      },
      newBodyFormData: {
        legalName: "",
        remark: "",
        taxPayerNo: "",
        taxSubId: "",
        taxSubName: "",
      },
      taxListRules: {
        taxSubName: [
          {
            required: true,
            message: "请输入扣缴义务人",
            trigger: "blur",
          },
        ],
        taxPayerNo: [
          {
            required: true,
            message: "请输入纳税人识别号",
            trigger: "blur",
          },
        ],
        remark: [
          {
            required: true,
            message: "请输入办税人员姓名",
            trigger: "blur",
          },
        ],
      },
      currentTypeName: "",
      list: [],
      sign: "paid",
      isShowScreen: false,
      screenWidth: document.body.clientWidth, // 屏幕尺寸
      screenHeight: document.body.clientHeight - 270,
      popShow: { isshow: false },
      isshowTaxVerify: false,
      isEditPaid: false,
      baseUrl: baseUrl,
      validAction: "taxPageStore/actionBatchDownload",
      queryAction: "taxPageStore/actionBatchDownloadFeedback",
      freeBackTip: "【批量获取反馈】",
      timeObj: {
        first: 3000,
        second: 10000,
        third: 15000,
      },
      indexWidth: "",
      fullScreenLoading: false,
      tipVisible: false,
      isShowPopover: false,
    };
  },
  computed: {
    tableData() {
      return this.list.slice(0, this.lastIndex);
    },
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
      areaList: (state) => state.areaList,
    }),
    fitWidth() {
      const numLength = this.list.length.toString().length;
      switch (numLength) {
        case 1:
          return "50";
        case 2:
          return "60";
        case 3:
          return "70";
        case 4:
          return "80";
        case 5:
          return "90";
      }
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    apiTemplateDownload,
    apiConfirmImport,
    apiImportVerifyErrorLog,
    load() {
      this.lastIndex += pageSize;
    },
    openPopover() {
      this.isShowPopover = true;
    },
    //新增
    handleShowBox() {
      this.popShow.isshow = true;
      this.isEditPaid = false;
      this.$nextTick(() => {
        this.$refs.paidEdit.show(null);
      });
    },
    batchTaxPayerVerify() {
      this.tipVisible = true;
    },
    async batchTaxPayerVerifyConfirm() {
      this.tipVisible = false;
      this.fullScreenLoading = true;
      const res = await apiTaxSubBatchSubmit();
      this.fullScreenLoading = false;
      if (res.success) {
        this.$refs.batchVerify.show(res.data);
      }
    },
    async batchGetFeedback() {
      this.fullScreenLoading = true;
      const res = await apiTaxSubBatchSubmitBack({
        taxSubIds: [],
      });
      this.fullScreenLoading = false;
      if (res.success) {
        this.$refs.batchFeedback.show(res.data);
      }
    },
    handleImport() {
      this.$refs.batchImport.open();
    },
    //导出
    // handleExport() {
    //     this.$store.dispatch(
    //         "taxPageStore/actionExportTaxSubjectList",
    //         this.ruleForm
    //     );
    // },
    //编辑
    handleEditor(row) {
      this.popShow.isshow = true;
      this.isEditPaid = true;
      this.$nextTick(() => {
        this.$refs.paidEdit.show(row);
      });
    },
    //保存并纳税主体验证
    saveAndVerify(data) {
      this.popShow.isshow = false;
      this.getList();
      this.isshowTaxVerify = true;
      this.$nextTick(() => {
        this.$refs.taxVerify.show(data);
      });
    },
    //保存并合同主体认证
    saveAndContract(data) {
      this.popShow.isshow = false;
      this.getList();
      this.$router.push({
        path: "/initialize/contract-auth",
        query: {
          taxSubId: data.taxSubId,
        },
      });
    },
    //更多-纳税主体验证
    handleTaxVerify(data) {
      this.$store
        .dispatch("actionCheckSalaryBusiness", {
          code: "salary",
        })
        .then((response) => {
          if (response.success && response.data.isOpen) {
            this.isshowTaxVerify = true;
            this.$nextTick(() => {
              this.$refs.taxVerify.show(data);
            });
          } else {
            this.$message.error("您还没有开通薪税服务，无法验证纳税主体");
          }
        });
    },
    //更多-合同主体认证
    handleAuthContract(data) {
      this.$router.push({
        path: "/initialize/contract-auth",
        query: {
          taxSubId: data.taxSubId,
        },
      });
    },
    chooseRegistrationNo(row) {
      this.$refs["chooseRegistrationNo"].open(row);
    },
    //获取反馈
    handleQuery(obj) {
      let paramsObj = {
        validParameter: {
          legalName: obj.legalName,
          remark: obj.remark,
          taxPayerNo: obj.taxPayerNo,
          taxSubId: obj.taxSubId,
          taxSubName: obj.taxSubName,
          pwd: obj.reportPwd,
        },
        querytAction: "taxPageStore/actionAccreditQuery",
        stopTip: "授权",
        freeBackTip: "【获取反馈】",
      };
      this.$refs.feedback.show(true, paramsObj);
    },
    refresh() {
      this.popShow.isshow = false;
      this.isshowTaxVerify = false;
      this.getList();
    },
    //取消
    hanleClose() {
      this.isshowTaxVerify = false;
    },
    handleSearch() {
      this.getList();
    },
    getList() {
      this.loading = true;
      this.$store
        .dispatch("taxPageStore/actionTaxSubjectListByArea", this.ruleForm)
        .then((res) => {
          if (res.success) {
            this.loading = false;
            this.list = res.data;
            // 无限滚动 列表刷新后 滚动条需要滚动到最上面
            this.lastIndex = pageSize;
            this.$refs.elTable.$el.querySelector(
              ".el-table__body-wrapper"
            ).scrollTop = "0";
          }
        });
      //重新获取公司列表
      this.$store.dispatch("taxPageStore/actionTaxSubjectList").then((res) => {
        if (res.success) {
          let taxSubjectInfoList = [
            {
              taxSubId: "",
              taxSubName: "全部",
              taxEnableYn: true,
              employeeEnableYn: true,
              contractEnableYn: true,
            },
          ].concat(res.data);
          this.$store.commit(AT.SET_TAXSUBJECTINFOLIST, taxSubjectInfoList);
        }
      });
    },
    handleDelete(id) {
      this.$confirm(
        "您确定删除该公司，如果是，请点击'确定',如果否,请点击'取消'",
        "删除确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: false,
          closeOnClickModal: false,
          closeOnPressEscape: false,
        }
      )
        .then(() => {
          this.$store
            .dispatch("taxPageStore/actionDelTaxSubject", id)
            .then((res) => {
              if (res.success) {
                this.getList();
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //切换区域
    handleCheckArea() {
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.border-option {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}
.paid {
  .tax-search {
    margin-left: 20px;
  }
  .el-input--prefix .el-input__inner {
    padding: 19px 20px;
  }
  .header {
    border-bottom: 1px solid #ededed;
    .add-table {
      cursor: pointer;
      float: right;
      color: $mainColor;
    }
  }
  .tax-content {
    padding: 0 20px;
    .content-header {
      position: relative;
      font-size: 18px;
      margin-bottom: 30px;
      cursor: pointer;
      i {
        font-size: 16px;
        color: #ccc;
      }
      .rotate-el-icon-arrow-left {
        transform: rotate(180deg);
      }
      span {
        position: absolute;
        left: 32px;
        top: 3px;
        z-index: 0;
      }
    }
  }
  .screening {
    .check-staff-menu {
      .search-input {
        margin-left: 0;
      }
    }
    .current-area_name {
      @include ellipsis;
      max-width: 200px;
      display: inline-block;
      font-style: normal;
    }
    .area-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: -20px;
      span {
        color: #4f71ff;
        cursor: pointer;
      }
    }
    .add-import {
      margin-right: 10px;
    }
    .iconiconfonticonfontsousuo1 {
      font-size: 12px;
    }
    .staff-situation {
      .staff-total {
        border-right: 1px solid #e6e6e6;
        padding-right: 15px;
        margin-right: 15px;
      }
      margin-top: 20px;
      color: #999;
      font-size: 12px;
      i {
        color: $mainColor;
        font-style: normal;
        padding: 0 3px;
      }
      em {
        color: #333;
        font-style: normal;
      }
    }
  }
  .el-icon-close {
    font-size: 18px;
    float: right;
    margin-top: 20px;
    cursor: pointer;
  }
  .paid-box {
    /deep/ .el-drawer__header {
      height: 50px;
      line-height: 50px;
      border-bottom: 1px solid #f1f1f1;
      padding-bottom: 10px;
      text-align: left;
      color: #333;
      font-size: 18px;
      padding: 0;
      margin: 0 20px;
    }
    /deep/ :focus {
      outline: 0;
    }
  }
  .el-dropdown-link {
    color: #4f71ff;
    cursor: pointer;
  }
  .verify-box {
    z-index: 3000 !important;
  }
}
::v-deep .el-table_1_column_2 div {
  white-space: pre !important;
}
</style>
<style>
.paid-popper-class {
  text-align: left !important;
}
</style>
