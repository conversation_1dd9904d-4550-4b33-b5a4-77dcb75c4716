import cumulative from '../cumulative';
import paid from '../paid';
import setBelongArea from '../setBelongArea';
import contractAuth from '../contractAuth';

export default [
  {
    path: '/initialize/cumulative',
    component: cumulative,
    meta: {
      businessCode: 'salary.compute.salaryCheck.taxTotalBase',
    },
  },
  {
    path: '/initialize/paid',
    component: paid,
    meta: {
      businessCode: 'hrEmployee.init.taxSubject',
      icon: 'iconchushihuashezhi',
    },
  },
  {
    path: '/initialize/set-belong-area',
    component: setBelongArea,
  },
  {
    path: '/initialize/contract-auth',
    component: contractAuth,
  },
];
