<template>
  <div class="custom-item">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>自定义所属区域</span>
        </el-col>
      </el-row>
    </header>
    <div class="screening">
      <div class="staff-table">
        <el-table
          :data="tableData"
          ref="table"
          row-key="id"
          v-loading="loading"
          :max-height="screenHeight"
          stripe
          :header-cell-style="{ background: '#F1F1F1' }"
          class="check-staff_table"
        >
          <el-table-column prop="areaName" label="区域名称">
            <template slot-scope="scope">
              <div
                v-if="scope.row.isSet"
                style="display: flex; align-items: center"
              >
                <el-input
                  v-model.trim="scope.row.areaName"
                  style="margin: 0 10px"
                ></el-input>
                <el-button size="small" @click="cancel(scope.row, scope.$index)"
                  >取消</el-button
                >
                <el-button
                  type="primary"
                  size="small"
                  @click="save(scope.row, scope.$index)"
                  >确定</el-button
                >
              </div>
              <div v-else style="padding-left: 20px; display: flex">
                <span
                  class="item-name"
                  style="text-align: left; width: 180px"
                  >{{ scope.row.areaName }}</span
                >
                <span class="table-name" @click="edit(scope.row, scope.$index)"
                  >编辑</span
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="100">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.isenable"
                @change="changeSwitch(scope.row)"
                :disabled="switchDisabled(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
        </el-table>
        <div class="add-container">
          <span @click="addItem">
            <i class="iconfont iconadd"></i>
            新增
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import * as AT from "@/store/actionTypes";

export default {
  data() {
    return {
      tableData: [],
      loading: false,
      areaName: "",
      screenHeight: document.body.clientHeight - 280, // 屏幕尺寸高度
    };
  },
  computed: {},
  components: {},
  created() {
    this.getList();
  },
  mounted() {},
  methods: {
    switchDisabled(row) {
      if(!row.id) {
        return true
      }
      return false
    },
    getList() {
      this.loading = true;
      this.tableData = [];
      this.$store.dispatch("taxPaidStore/actionGetAreaList").then((res) => {
        if (res.success) {
          this.tableData = res.data.map(item=>{
            item.isSet = false; 
            return item
          });
          let data = res.data.filter((item) => item.isenable);
          let areaList = [{ id: "", areaName: "全部区域" }].concat(data);
          this.$store.commit(AT.SET_AREALIST, areaList);
        }
        this.loading = false;
      });
    },
    //编辑
    edit(row, index, flag) {
      this.areaName = row.areaName;
      //点击编辑 判断是否已经保存所有操作
      for (let i of this.tableData) {
        if (i.isSet && i.id != row.id) {
          this.$message.warning("请先保存当前编辑项");
          return false;
        }
      }

      if (!row.isSet) {
        row.isSet = true;
        this.$set(this.tableData, index, row);
      }
    },
    //保存
    save(row, index) {
      if (row.areaName.length > 10) {
        this.$message.warning("最长可输入10个字符");
        return;
      }
      let data = {};
      //如果存在id表示是编辑，否则是新增
      if (row.id) {
        let { id, isenable, areaName } = row;
        data = {
          id,
          isenable,
          areaName,
        };
      } else {
        let { isenable, areaName } = row;
        data = {
          isenable,
          areaName,
        };
      }
      this.$store
        .dispatch("taxPaidStore/actionPostSavaArea", data)
        .then((res) => {
          if (res.code === "0000") {
            this.$message.success("操作成功");
            this.getList();
            row.isSet = false;
          }
        });
    },
    //取消
    cancel(row, index) {
      if (row.id) {
        row.isSet = false;
        row.areaName = this.areaName;
        this.$set(this.tableData, index, row);
      } else {
        this.tableData.splice(index, 1);
      }
    },
    //新增
    addItem() {
      this.tableData.push({
        areaName: "",
        isenable: true,
        isSet: true,
      });
      this.$nextTick(() => {
        this.$refs.table.bodyWrapper.scrollTop =
          this.$refs.table.bodyWrapper.scrollHeight;
      });
    },
    //修改禁用启用
    changeSwitch(data) {
      data.isAloneSwitch = true;
      this.$store
        .dispatch("taxPaidStore/actionPostModifyState", data)
        .then((res) => {
          if (res.code === "0000") {
            this.$message.success("操作成功");
            this.getList();
          }
        });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.custom-item {
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }

  .screening {
    padding: 0 20px;
    .staff-table {
      margin-top: 20px;
      .item-name {
        display: inline-block;
        margin: 0 10px;
      }
      /deep/ .el-button--primary {
        font-size: 12px;
      }
    }
    .add-container {
      border: 1px dashed $mainColor;
      color: $mainColor;
      font-size: 16px;
      text-align: center;
      padding: 20px 0;
      margin-top: 20px;
      span {
        cursor: pointer;
      }
    }
  }
  /deep/ .el-table th:first-child {
    text-align: left !important;
    padding-left: 20px;
  }
}
</style>
