import { fetch, fetchFile } from 'request/fetch';

//累计应税所得额初始化-集合列表
export function apiTaxTotalBaseList(ruleForm) {
  return fetch({
    url: '/api/hrsaas-salary/taxTotalBase/getTaxTotalBaseList',
    method: 'get',
    params: ruleForm,
  });
}

//累计应税所得额初始化--删除
export function apiDelTaxTotalBase(idsForm) {
  return fetch({
    url: '/api/hrsaas-salary/taxTotalBase/delTaxTotalBase',
    method: 'post',
    data: idsForm,
  });
}

//累计应税所得额初始化-导入成功数据
export function apiImportTaxTotalBaseSuccess(ruleForm) {
  return fetch({
    url: '/api/hrsaas-salary/taxTotalBase/importTaxTotalBaseSuccess',
    method: 'post',
    params: ruleForm,
  });
}

//累计应税所得额初始化-下载模板
export function apidownloadTemplate() {
  return fetchFile({
    url: '/api/hrsaas-salary/taxTotalBase/downloadTemplate',
    method: 'get',
  });
}

//导出错误记录
export function apiExportErrorRecord(ruleForm) {
  return fetchFile({
    url: '/api/hrsaas-salary/taxTotalBase/exportErrorRecord',
    method: 'get',
    params: ruleForm,
  });
}

//纳税主体-新增/编辑
export function apiAddTaxSubject(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSubject/addTaxSubject',
    method: 'post',
    data,
  });
}

//查看电子章
export function apiGetSealList(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSub/auth/sealList',
    method: 'post',
    params: data,
  });
}

//根据公司id查询合同主体认证详情
export function apiGetTaxAuthInfo(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSub/auth/getById',
    method: 'post',
    params: data,
  });
}

//合同主体信息编辑
export function apiUpdateStatus(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSub/auth/save',
    method: 'post',
    data,
  });
}

//合同主体信息删除
export function apiDeleteTaxSub(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSub/auth/delete',
    method: 'post',
    params: data,
  });
}

//上传电子签章图片
export function apiUploadSealImg(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSub/auth/sealUpload',
    method: 'post',
    params: data,
  });
}

//设置默认电子签章
export function apiSetDefaultSeal(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSub/auth/sealSetDefault',
    method: 'post',
    params: data,
  });
}

//删除电子签章
export function apiDeleteSeal(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSub/auth/sealDelete',
    method: 'post',
    params: data,
  });
}

//法人实体批量导入-下载模板
export function apiTemplateDownload() {
  return fetchFile({
    url: '/api/hrsaas-salary/taxSubject/template/download',
    method: 'post',
  });
}

//法人实体批量导入-确认导入
export function apiConfirmImport(uuid) {
  return fetch({
    url: '/api/hrsaas-salary/taxSubject/confirmImport/' + uuid,
    method: 'post',
  });
}

//法人实体批量导入-下载错误文件
export function apiImportVerifyErrorLog(uuid) {
  return fetchFile({
    url: '/api/hrsaas-salary/taxSubject/importVerifyErrorLog/' + uuid,
    method: 'get',
  });
}

//法人实体-批量纳税主体验证
export function apiTaxSubBatchSubmit(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSubject/taxSubBatchSubmit',
    method: 'post',
    data,
  });
}

//法人实体-批量获取反馈
export function apiTaxSubBatchSubmitBack(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSubject/taxSubBatchSubmitBack',
    method: 'post',
    data,
  });
}

export function apiTaxRegistrationNoList(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSubject/registrationNoList',
    method: 'post',
    data,
  });
}
