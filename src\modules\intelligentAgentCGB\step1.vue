<template>
  <div class="step1" style="position: relative; height: calc(100vh - 80px)">
    <Title title="开始代发" withBackButton />
    <div style="width: 950px; margin: 0 auto">
      <div style="display: flex; justify-content: center">
        <Steps :current="1" style="margin: 20px 0" />
      </div>
      <div
        style="display: flex; justify-content: center; margin: 20px 0 10px 0"
      >
        <el-form ref="form" :model="sourceOfFunds" label-width="140px">
          <div style="margin: 0 0 10px 5px">开户账号：{{ accountNo }}</div>
          <el-form-item label="资金来源">
            <template #label>
              资金来源 <span style="color: #ccc">(记账日期)</span>
            </template>
            <div>
              <el-date-picker
                v-model="sourceOfFunds.startDate"
                type="date"
                placeholder="开始日期"
                :picker-options="pickerOptionsStartDate"
              ></el-date-picker>
              <span style="margin: 0 8px">至</span>
              <el-date-picker
                v-model="sourceOfFunds.endDate"
                type="date"
                placeholder="结束日期"
                :picker-options="pickerOptionsEndDate"
              ></el-date-picker>
              <el-button
                type="primary"
                style="margin-left: 8px"
                @click="handleSearch"
                >查询</el-button
              >
            </div>
          </el-form-item>
        </el-form>
      </div>

      <SourcesOfFund
        ref="sourcesOfFund"
        :totalAmountPayable="totalAmountPayable"
        :accountSourceRecords="accountSourceRecords"
        :loading="loading"
      />
    </div>
    <div
      class="actions"
      style="
        position: absolute;
        bottom: 0;
        width: 100%;
        border-top: 1px solid #ededed;
        text-align: center;
        padding: 20px 0;
      "
    >
      <el-button @click="$router.back()">取消</el-button>
      <el-button type="primary" @click="next" :loading="loadingNext"
        >下一步</el-button
      >
    </div>
  </div>
</template>

<script>
import {
  apiQueryAccountSource,
  apiPaySalaryCommit,
} from "../intelligentAgent/store/api";
import Title from "../../components/title.vue";
import Steps from "../../components/intelligentAgentCGB/steps.vue";
import SourcesOfFund from "../../components/intelligentAgentCGB/sourcesOfFund.vue";
export default {
  components: {
    Title,
    Steps,
    SourcesOfFund,
  },
  mounted() {
    this.getStartDate();
    this.id = this.$route.query.id;
    if (!this.id) {
      this.$message.error("订单错误，请重新发送");
      this.$router.push({
        path: "/agent-pay",
      });
      return;
    }
    this.handleTableData();
  },
  data() {
    var _this = this;
    return {
      pickerOptionsStartDate: {
        disabledDate(time) {
          if (_this.sourceOfFunds.endDate) {
            const times =
              new Date(
                _this.sourceOfFunds.endDate.toLocaleDateString()
              ).getTime() -
              179 * 8.64e7 -
              1;
              const timesYearAgo =
              new Date(new Date().toLocaleDateString()).getTime() -
              365 * 8.64e7 -
              1;
            return (
              time.getTime() < times ||
              time.getTime() > new Date(_this.sourceOfFunds.endDate).getTime() || 
              time.getTime() < timesYearAgo
            );
          } else {
            const times =
              new Date(new Date().toLocaleDateString()).getTime() -
              365 * 8.64e7 -
              1;
            return (
              time.getTime() < times || time.getTime() > Date.now() - 8.64e6
            );
          }
        },
      },
      pickerOptionsEndDate: {
        disabledDate(time) {
          if (_this.sourceOfFunds.startDate) {
            const times =
              new Date(
                _this.sourceOfFunds.startDate.toLocaleDateString()
              ).getTime() +
              179 * 8.64e7 -
              1;
            return (
              time.getTime() >= times ||
              time.getTime() <
                new Date(_this.sourceOfFunds.startDate).getTime() ||
              time.getTime() > Date.now() - 8.64e6
            );
          } else {
            const times =
              new Date(new Date().toLocaleDateString()).getTime() -
              365 * 8.64e7 -
              1;
            return (
              time.getTime() < times || time.getTime() > Date.now() - 8.64e6
            );
          }
        },
      },
      sourceOfFunds: {
        startDate: "",
        endDate: new Date(),
      },
      totalAmountPayable: 0,
      accountSourceRecords: [],
      id: 0,
      loading: false,
      accountNo: "",
      loadingNext: false,
    };
  },
  methods: {
    next() {
      if (
        this.totalAmountPayable > this.$refs.sourcesOfFund.currentTotalAmount
      ) {
        this.$message.error("可代发额度总必须大于等于代发批次的代发总金额");
        return;
      }
      this.loadingNext = true;
      let data = {
        payBatchId: this.id,
        recordVoList: this.$refs.sourcesOfFund.selectedItems,
      };
      apiPaySalaryCommit(data)
        .then((res) => {
          if (res.success) {
            res.data.isNormal = true;
            res.data.id = this.id;
            this.$router.push({
              path: "/intelligentAgentCGB/step2",
              query: { data: JSON.stringify(res.data), type: "NORMAL" },
            });
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loadingNext = false;
          }, 5000);
        });
    },
    handleTableData() {
      this.loading = true;
      const start = this.formatDate(this.sourceOfFunds.startDate);
      const end = this.formatDate(this.sourceOfFunds.endDate);
      let data = {
        payBatchId: this.id,
        queryStartDate: start,
        queryEndDate: end,
      };
      apiQueryAccountSource(data).then((res) => {
        if (res.success) {
          this.totalAmountPayable = res.data.totalAmount;
          this.accountNo = res.data.payAccountNo;
          this.accountSourceRecords = res.data.accountSourceRecords;
          this.loading = false;
        }
      });
    },
    getStartDate() {
      var dateDay = new Date();
      dateDay.setDate(dateDay.getDate() - 89);
      this.sourceOfFunds.startDate = dateDay;
    },
    handleSearch() {
      if (!this.sourceOfFunds.startDate) {
        this.$message.error("请选择开始日期");
        return;
      }
      if (!this.sourceOfFunds.endDate) {
        this.$message.error("请选择结束日期");
        return;
      }
      this.handleTableData();
    },
    formatDate(date) {
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var day = date.getDate();
      month = month > 9 ? month : "0" + month;
      day = day < 10 ? "0" + day : day;
      return year + "-" + month + "-" + day;
    },
  },
};
</script>

<style scoped>
</style>