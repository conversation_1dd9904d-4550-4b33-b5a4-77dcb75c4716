<template>
  <div class="step3" style="position: relative; height: calc(100vh - 80px)">
    <div
      style="
        padding: 0 20px;
        font-size: 17px;
        height: 60px;
        border-bottom: 1px solid #ededed;
        line-height: 61px;
        display: flex;
      "
    >
      <a style="cursor: pointer" @click="finish">返回</a
      ><span style="margin: 0 10px">|</span>开始代发
    </div>
    <div style="width: 950px; margin: 0 auto">
      <div
        v-if="type === 'NORMAL'"
        style="display: flex; justify-content: center"
      >
        <Steps :current="2" style="margin: 20px 0" />
      </div>
      <ProcessingResult
        style="width: 400px; margin: 110px auto"
        :commitStatus="commitStatus"
        :result="result"
      />
    </div>
    <div
      class="actions"
      style="
        position: absolute;
        bottom: 0;
        width: 100%;
        border-top: 1px solid #ededed;
        text-align: center;
        padding: 20px 0;
      "
    >
      <div v-if="commitStatus">
        <el-button type="primary" @click="finish">完成</el-button>
      </div>
      <div v-else>
        <el-button @click="finish">取消</el-button>
        <el-button type="primary" @click="resend">重新发送</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import Steps from "../../components/intelligentAgentCGB/steps.vue";
import ProcessingResult from "../../components/intelligentAgentCGB/processingResult.vue";
export default {
  components: {
    Steps,
    ProcessingResult,
  },
  data() {
    return {
      commitStatus: "",
      result: {},
      isNormal: false,
      id: "",
      type: "",
    };
  },
  mounted() {
    let data = JSON.parse(this.$route.query.data);
    if (!data) {
      this.$message.error("订单错误，请重新发送");
      this.$router.push({
        path: "/agent-pay",
      });
      return;
    }
    this.isNormal = data.isNormal;
    this.id = data.id;
    this.type = this.$route.query.type;
    if (data.status === "SUCCESS") {
      this.commitStatus = true;
    } else if (data.status === "ERROR") {
      this.result.errorCode = data.errorCode;
      this.result.errorMsg = data.errorMsg;
      this.commitStatus = false;
    }
  },
  methods: {
    finish() {
      this.$router.push({
        path: "/agent-pay",
      });
    },
    resend() {
      if (this.isNormal) {
        this.$router.push({
          path: "/intelligentAgentCGB/step1",
          query: {
            id: this.id,
          },
        });
      } else {
        this.$router.push({
          path: "/agent-pay",
        });
      }
    },
  },
};
</script>

<style>
</style>