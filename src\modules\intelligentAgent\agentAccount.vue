<template>
  <div class="agent-account">
    <header class="header">
      <el-row type="flex" style="justify-content: space-between">
        <span>代发账户</span>
        <div>
          <el-button
            type="primary"
            @click="clickAddAccount"
            v-if="privilegeVoList.includes('salary.newpayroll.account.add')"
            >新增账户</el-button
          >
        </div>
      </el-row>
    </header>
    <div class="screening">
      <!-- <h4 class="title">当前可使用的银行代发通道有</h4>
      <div class="agent-choose">
        <div class="channel-box">
          <el-tooltip
            class="item"
            effect="dark"
            placement="top"
            v-for="item in channelList"
            :key="item.id"
            :disabled="!remarkChange(item.channelDesc)"
          >
            <div
              v-html="remarkChange(item.channelDesc)"
              slot="content"
              class="channel-hover"
            ></div>
            <div class="channel" v-if="item.enbaleYn">
              {{ item.channelName }}
            </div>
          </el-tooltip>
        </div>
      </div> -->
      <div class="agent-table">
        <be-table
          :tableHeader="tableHeader"
          :tableData="resultData"
          @formatter="handleFormatter"
          @btnColumn="handleBtnColumn"
          @search="handleSearch"
          :total="total"
          :isShowIndex="false"
          :isHidePage="false"
          :loading="loading"
        ></be-table>
      </div>
    </div>
    <el-dialog
      :title="this.server_env === 'cgb' ? title : '新增账户'"
      :visible.sync="isShowAccount"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeAddAccount"
      width="630px"
    >
      <div v-if="server_env === 'cgb'">
        <div v-if="showAddAccount">
          <add-account
            :channelList="channelList"
            :companyInfoList="companyInfoList"
            ref="addAccount"
            @cancel="cancel"
            @precheck="precheck"
          ></add-account>
        </div>
        <div v-if="showAddAccountResult">
          <addAccountResult
            :addAccountResult="addAccountResult"
            @resultHandle="closeAddAccount"
          ></addAccountResult>
        </div>
        <div v-if="showAddSelectorList">
          <addSelectorList
            :issuingList="issuingList"
            @cancel="closeAddAccount"
            @issuingNext="issuingNext"
          ></addSelectorList>
        </div>
        <div v-if="showAddAccountKeyboarder">
          <addAccountKeyboarder
            @keyboarderConfirm="keyboarderConfirm"
            @keyboarderLastStep="keyboarderLastStep"
          ></addAccountKeyboarder>
        </div>
      </div>
      <div v-else>
        <add-account :channelList="channelList" ref="addAccount"></add-account>
        <div slot="footer" style="text-align: right">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="accountConfirm">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      :title="'账户详情'"
      :visible.sync="isShowDetail"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="630px"
      @close="dialogClose"
    >
      <account-detail
        ref="accountDetailRef"
        :subjectAccountId="subjectAccountId"
        :channelList="channelList"
      ></account-detail>
      <div slot="footer">
        <el-button @click="isShowDetail = false">我知道了</el-button>
      </div>
    </el-dialog>
    <detail-create-dialog ref="detail-create-dialog" />
  </div>
</template>
<script>
import beTable from "./components/Table";
import addAccount from "./components/addAccount";
import addAccountResult from "./components/addAccountResult";
import addSelectorList from "./components/addSelectorList";
import addAccountKeyboarder from "./components/addAccountKeyboarder";
import accountDetail from "./components/accountDetail";
import detailCreateDialog from "./components/detail-create-dialog";
import { debounce } from "./util/debounce";
import {
  apiGetSubjectAccountList,
  apiGetPayChannelConfList,
  apiSavaSubjectAccount,
  apiSwitchSubjectAccount,
  apiInviteSubjectAccount,
  apiQuerySubject,
  apiEditAccountCheck,
} from "./store/api";
import { mapState } from "vuex";
export default {
  data() {
    return {
      loading: false,
      server_env: window.env.server_env,
      title: "",
      agent: {
        name: "",
        status: "",
        batch: "",
        month: "",
      },
      tableHeader: [
        { prop: "subjectName", label: "公司名称" },
        { prop: "unifiedCode", label: "统一社会信用代码" },
        { prop: "channelName", label: "通道名称" },
        { prop: "accountBank", label: "开户行" },
        { prop: "accountBankNo", label: "开户账号" },
        { prop: "enbaleYn", label: "状态" },
        {
          prop: "createdTime",
          label: "创建时间",
        },
        {
          prop: "def_cz",
          label: "操作",
          width: "200px",
          fixed: "right",
          btn: [
            { prop: "def_ck", label: "查看", type: "def_btn", fun: "look" },
            { prop: "def_bj", label: "编辑", type: "def_btn", fun: "edit" },
            {
              prop: "def_qy",
              label: "启用",
              type: "def_btn",
              fun: "typeChange",
            },
            {
              prop: "def_ty",
              label: "停用",
              type: "def_btn",
              fun: "typeChange",
              // typeName: "danger", // 按钮类型
            },
            {
              prop: "def_iv",
              label: "邀请员工开通电子工资卡",
              type: "def_btn",
              fun: "invite",
              // isDisabled: true,
              // isDisabledSource: "sendMessageYn",
            },
            {
              prop: "def_create",
              label: "生成批量开户二维码",
              type: "def_btn",
              fun: "create",
            },
            {
              prop: "def_query",
              label: "批量开户信息查询",
              type: "def_btn",
              fun: "query",
            },
          ],
        },
      ],
      resultData: [],
      pageSize: 10,
      total: 0,
      currPage: 1,
      isShowAccount: false,
      channelList: [],
      companyInfoList: [],
      issuingList: [],
      addAccountForm: {},
      isShowDetail: false,
      accountList: [],
      subjectAccountId: 0,
      tableHeight: document.body.offsetHeight - 320 + "px",
      showAddAccount: true,
      showAddAccountResult: false,
      showAddSelectorList: false,
      showAddAccountKeyboarder: false,
      addAccountResult: {
        addAccountStatus: "",
        errorMessage: "",
        type: "",
        message: "",
        phoneNumber: "",
        title: "",
      },
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  components: {
    beTable,
    addAccount,
    accountDetail,
    detailCreateDialog,
    addAccountResult,
    addSelectorList,
    addAccountKeyboarder,
  },
  mounted() {
    this.getAccoutList();
    this.getChannel();
    if (window.env.server_env === "cgb") {
      this.getCompanyList();
    }
  },
  methods: {
    // 确定
    accountConfirm: debounce(
      function () {
        this.$refs.addAccount.$refs.agentAccountForm.validate((valid) => {
          if (valid) {
            apiSavaSubjectAccount(this.$refs.addAccount.agentAccountForm).then(
              (res) => {
                if (res.success) {
                  this.$message.success("新增成功");
                  this.$refs.addAccount.$refs.agentAccountForm.resetFields();
                  this.isShowAccount = false;
                  this.getAccoutList();
                }
              }
            );
          } else {
            this.$nextTick(() => {
              this.errorScroll(
                document.querySelectorAll("div.el-form-item__error")
              );
            });
          }
        });
      },
      2000,
      true
    ),
    issuingNext(data) {
      this.addAccountForm.handler = data.userName;
      this.addAccountForm.handlerUserNo = data.userNo;
      this.showAddSelectorList = false;
      this.showAddAccountKeyboarder = true;
    },
    keyboarderConfirm(data) {
      this.addAccountForm.password = data.smsCode;
      this.addAccountForm.otpToken = data.verifyCodeSend;
      this.addAccountForm.handlerMobile = data.phoneNumber;
      apiSavaSubjectAccount(this.addAccountForm).then((res) => {
        if (res.success) {
          this.showAddAccountKeyboarder = false;
          this.showAddAccountResult = true;
          if (this.title === "新增账户") {
            this.addAccountResult.title = "新增成功";
          } else {
            this.addAccountResult.title = "保存成功";
          }
          this.addAccountResult.addAccountStatus = "SUCCESS";
          this.addAccountResult.type = res.data;
          this.addAccountResult.message = this.addAccountForm.handler;
          this.addAccountResult.phoneNumber =
            this.addAccountForm.handlerMobile.replace(
              this.addAccountForm.handlerMobile.substring(3, 7),
              "****"
            );
        }
      });
    },
    keyboarderLastStep() {
      this.showAddAccountKeyboarder = false;
      this.showAddSelectorList = true;
    },
    precheck() {
      this.$refs.addAccount.$refs.agentAccountForm.validate((valid) => {
        if (valid) {
          this.addAccountForm = this.$refs.addAccount.agentAccountForm;
          if (this.addAccountForm.id) {
            this.addAccountForm.accountId = this.addAccountForm.id;
          }
          apiEditAccountCheck(this.addAccountForm).then((res) => {
            if (res.success) {
              this.issuingList = res.data;
              this.showAddAccount = false;
              this.showAddSelectorList = true;
            } else {
              this.showAddAccount = false;
              this.showAddAccountResult = true;
              this.addAccountResult.addAccountStatus = "FAIL";
              if (this.title === "新增账户") {
                this.addAccountResult.title = "新增失败";
              } else {
                this.addAccountResult.title = "保存失败";
              }
              this.addAccountResult.errorMessage = res.message;
            }
          });
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    editAccoutDetail(data) {
      this.title = "编辑账户";
      setTimeout(() => {
        this.$refs.addAccount.editAccount(data);
      }, 0);
      this.showAddAccount = true;
      this.isShowAccount = true;
      this.showAddAccountResult = false;
      this.showAddSelectorList = false;
      this.showAddAccountKeyboarder = false;
      this.addAccountForm = {};
    },
    clickAddAccount() {
      this.title = "新增账户";
      setTimeout(() => {
        this.$refs.addAccount.editAccount(null);
      }, 0);
      this.showAddAccount = true;
      this.isShowAccount = true;
      this.showAddAccountResult = false;
      this.showAddSelectorList = false;
      this.showAddAccountKeyboarder = false;
      this.addAccountForm = {};
    },
    // 代发账户列表
    getAccoutList() {
      this.loading = true;
      let data = {
        currPage: this.currPage,
        pageSize: this.pageSize,
      };
      apiGetSubjectAccountList(data).then((res) => {
        if (res.success) {
          this.resultData = res.data.records;
          this.total = res.data.total;
          this.loading = false;
        }
      });
    },
    // 代发通道列表
    getChannel() {
      apiGetPayChannelConfList().then((res) => {
        if (res.success) {
          this.channelList = res.data;
        }
      });
    },
    getCompanyList() {
      let data = {
        type: "TAX",
        authSuccess: true,
      };
      apiQuerySubject(data).then((res) => {
        if (res.success) {
          this.companyInfoList = res.data;
        }
      });
    },
    // 账户详情弹窗
    accoutDetailDialog(id) {
      this.isShowDetail = true;
      this.subjectAccountId = id;
    },
    handleFormatter({ prop, data, btnItem }, callback) {
      if (prop == "def_cz") {
        switch (btnItem) {
          case "def_qy":
            callback(
              data["enbaleYn"] == false &&
                this.privilegeVoList.includes(
                  "salary.newpayroll.account.enable"
                )
            );
            break;
          case "def_ty":
            callback(
              data["enbaleYn"] == true &&
                this.privilegeVoList.includes(
                  "salary.newpayroll.account.enable"
                )
            );
            break;
          case "def_ck":
            callback(
              this.privilegeVoList.includes("salary.newpayroll.account.detail")
            );
            break;
          case "def_bj":
            callback(
              this.privilegeVoList.includes(
                "salary.newpayroll.account.detail"
              ) &&
                data.enbaleYn === true &&
                window.env.server_env === "cgb"
            );
            break;
          case "def_iv":
            callback(
              data["bankTypeEnum"] == "CGB" &&
                this.privilegeVoList.includes(
                  "salary.newpayroll.account.invitation"
                )
            );
            break;
          case "def_create":
            callback(data["bankTypeEnum"] == "CGB");
            break;
          case "def_query":
            callback(data["bankTypeEnum"] == "CGB");
            break;
        }
      } else {
        switch (prop) {
          case "enbaleYn":
            callback(data[prop] ? "启用" : "停用");
            break;
          case "channelName":
            if (this.channelList) {
              this.channelList.forEach((item) => {
                if (item.channelCode == data.channelCode)
                  data.channelName = item.channelName;
              });
            }
            callback(data[prop]);
            break;
          default:
            callback(data[prop]);
        }
      }
    },
    handleBtnColumn(val, type) {
      if (type == "look") this.accoutDetailDialog(val.id);
      if (type == "edit" && window.env.server_env === "cgb")
        this.editAccoutDetail(val);
      if (type == "typeChange") this.handleUse(val);
      if (type == "invite") this.handleInvite(val.id);
      if (type == "create") this.$refs["detail-create-dialog"].show = true;
      const host = window.location.host;
      var url =
        "https://ebank-yd03.test.cgbchina.com.cn:49081/finhome/qrCodeSignInfo.jsp";
      if (host.includes("cgb.olading.com")) {
        url = "https://ebank.cgbchina.com.cn/finhome/qrCodeSignInfo.jsp";
      }
      if (type == "query") window.open(url);
    },
    handleSearch(val) {
      let { limit, start, page } = val;
      this.currPage = page;
      this.pageSize = limit;
      this.getAccoutList();
    },
    // 启用/停用
    handleUse(row) {
      let data = {
        subjectAccountId: row.id,
        enbaleYn: !row.enbaleYn,
      };
      apiSwitchSubjectAccount(data).then((res) => {
        if (res.success) {
          this.$message.success("修改成功");
          this.getAccoutList();
        }
      });
    },

    //
    handleInvite(subjectAccountId) {
      // this.subjectAccountId = id;
      apiInviteSubjectAccount({ subjectAccountId }).then((res) => {
        if (res.success) {
          this.$message.success("邀请成功");
          this.getAccoutList();
        }
      });
    },
    // hover内容处理
    remarkChange(value) {
      if (!value) return;
      if (value.length < 50) return value;
      return value.replace(new RegExp("(.{30})", "g"), "$1<br/>");
    },
    // 取消
    cancel() {
      this.isShowAccount = false;
      this.$refs.addAccount.$refs.agentAccountForm.resetFields();
    },
    dialogClose() {
      this.$refs.accountDetailRef.reset();
    },
    closeAddAccount() {
      if (window.env.server_env === "cgb") {
        this.isShowAccount = false;
        this.getAccoutList();
        this.getChannel();
        this.getCompanyList();
        this.$refs.addAccount.$refs.agentAccountForm.resetFields();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.agent-account {
  /*height: calc(100vh - 80px);*/
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }
  .screening {
    padding: 0 20px;
    .title {
      margin-top: 20px;
      color: #46485a;
    }
    .agent-choose {
      max-height: 48px;
      display: flex;
      align-content: center;
      margin-top: 20px;
      p {
        line-height: 40px;
        margin-right: 5px;
      }
      .channel-box {
        display: flex;
        .channel-hover {
          height: 50px;
          line-height: 48px;
        }
        .channel {
          display: inline-block;
          white-space: nowrap;
          cursor: pointer;
          background: #fff;
          border: 1px solid #dcdfe6;
          color: #606266;
          text-align: center;
          box-sizing: border-box;
          transition: 0.1s;
          font-weight: 500;
          font-size: 14px;
          border-radius: 4px;
          padding: 10px;
          margin: 0 5px;
          max-width: 160px;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .operate-tips {
        margin-left: 300px;
        display: flex;
        align-items: center;
        cursor: pointer;
        span {
          font-size: 14px;
          color: rgb(106, 127, 255);
        }
      }
    }
    .agent-form,
    .agent-table {
      margin-top: 20px;
    }
    .pagination {
      float: right;
      padding: 22px 0 22px 22px;
    }
  }
  .create-dialog {
    /deep/.el-dialog__body {
      padding: 10px 16px 28px;
    }
  }
}
</style>
