<template>
  <div class="agent-apply" v-loading="loading">
    <header class="header">
      <el-row type="flex" style="justify-content: space-between">
        <span>代发申请</span>
        <div>
          <el-button
            type="primary"
            plain
            @click="$refs.addAgent.show()"
            v-if="privilegeVoList.includes('salary.newpayroll.apply.check')"
            >新增代发</el-button
          >
          <el-button
            type="primary"
            plain
            @click="batchCheck"
            v-if="
              privilegeVoList.includes('salary.newpayroll.apply.check') &&
              this.server_env !== 'cgb'
            "
            >数据校验</el-button
          >
          <el-button
            type="primary"
            plain
            @click="batchSubmit"
            v-if="privilegeVoList.includes('salary.newpayroll.apply.commit')"
            >{{
              this.server_env === "cgb" ? "批量提交发放" : "批量提交"
            }}</el-button
          >
        </div>
      </el-row>
    </header>
    <div class="screening">
      <div class="agent-form">
        <el-form label-position="right" label-width="90px" :inline="true">
          <el-form-item label="">
            <el-select
              v-model="apply.unifiedCode"
              placeholder="请选择发薪公司名称"
              clearable
            >
              <el-option
                v-for="(item, index) in companyData"
                :key="item.unifiedCode + index"
                :label="item.subjectName"
                :value="item.unifiedCode"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <el-date-picker
              v-model="apply.salaryMonth"
              value-format="yyyyMM"
              type="month"
              placeholder="请选择发放月份"
              class="width-198"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="">
            <el-select
              v-if="server_env !== 'cgb'"
              v-model="apply.checkStatus"
              placeholder="请选择校验状态"
              clearable
            >
              <el-option
                v-for="(item, key) in constData.checkData"
                :key="key"
                :label="item.name"
                :value="key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <el-select
              v-model="apply.payStatus"
              placeholder="请选择批次状态"
              clearable
            >
              <el-option
                v-for="(item, key) in constData.paymentData"
                :key="key"
                :label="item.name"
                :value="key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" class="screen" @click="screen"
              >查询</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div class="agent-table">
        <be-table
          :tableHeader="tableHeader"
          :tableData="resultData"
          @formatter="handleFormatter"
          @btnColumn="handleBtnColumn"
          @search="handleSearch"
          :total="total"
          :isShowIndex="false"
          :isHidePage="false"
          @selectionChange="selectionChange"
          :isShowSelection="true"
          @handleDelete="handleDelete"
        >
        </be-table>
      </div>
    </div>
    <add-agent ref="addAgent" @getList="getApplyList"></add-agent>
    <el-dialog
      title="提交发放"
      :visible.sync="showAgentSelectAccount"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="630px"
    >
      <agentSelectAccount
        :commitParameter="commitParameter"
        :key="timer"
        @agentSelectAccountCancel="agentSelectAccountCancel"
        @agentSelectAccountConfirm="agentSelectAccountConfirm"
      ></agentSelectAccount>
    </el-dialog>
    <!-- <mapping-agent
      ref="mappingAgent"
      @showAdd="$refs.addAgent.show()"
    ></mapping-agent> -->
  </div>
</template>
<script>
import beTable from "./components/Table";
import addAgent from "./components/addAgent.vue";
import agentSelectAccount from "./components/agentPay/agentSelectAccount";
// import mappingAgent from "./components/mappingAgent";

import {
  apiPaySalaryApply,
  apiGetSubjectList,
  apiCheckPaySalaryApply,
  apiSubmitApplyBathRecord,
  removePaySalaryApplyBatch,
  apiQuerySubjectAccountList,
  getPaySalaryApplyBatchInfo,
} from "./store/api";
import * as constData from "./util/constData";
import * as AT from "./store/actionTypes";
import { deepClone } from "@/utils/utils";
import { mapState } from "vuex";
export default {
  components: {
    beTable,
    addAgent,
    agentSelectAccount,
    // mappingAgent,
  },
  data() {
    return {
      server_env: window.env.server_env,
      apply: {
        checkStatus: "",
        currPage: 1,
        pageSize: 10,
        payStatus: "",
        queryEndTime: "",
        queryStartTime: "",
        salaryMonth: "",
        unifiedCode: "",
        // subjectName: ""
      },
      tableHeader: [
        { prop: "id", label: "代发批次号", width: "100px" },
        { prop: "subjectName", label: "发薪公司名称", width: "150px" },
        { prop: "salaryMonth", label: "发放月份", width: "80px" },
        { prop: "payStatus", label: "批次状态", width: "80px" },
        { prop: "totalCount", label: "总笔数", width: "80px" },
        { prop: "totalAmount", label: "总金额（元）", width: "120px" },
        { prop: "salaryCheckId", label: "工资表ID", width: "100px" },
        { prop: "salaryRuleName", label: "工资表名称" },
        { prop: "errorInfo", label: "备注" },
        {
          prop: "def_cz",
          label: "操作",
          width: "200px",
          fixed: "right",
          btn: [
            { prop: "def_ck", label: "查看", type: "def_btn", fun: "look" },
            {
              prop: "def_sc",
              label: "删除",
              type: "def_btn",
              fun: "delete",
            },
          ],
        },
      ],
      resultData: [],
      total: 0,
      constData: {},
      selectionList: [],
      companyData: [],
      loading: false,
      tableHeight: document.body.offsetHeight - 320 + "px",
      showAgentSelectAccount: false,
      commitParameter: {
        param: {},
        accountList: [],
      },
      timer: "",
      obj: {},
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },

  mounted() {
    this.getApplyList();
    this.companyList();
    this.constData = constData;
    if (window.env.server_env !== "cgb") {
      this.tableHeader.splice(3, 0, {
        prop: "checkStatus",
        label: "校验状态",
        width: "80px",
      });
    }
    
    if (window.env.server_env === "cgb") {
      let def_cz = this.tableHeader[9];
      def_cz.btn.splice(0, 0, {
        prop: "def_tjff",
        label: "提交发放",
        type: "def_btn",
        fun: "submitForRelease",
      });
      def_cz.btn.push(
        {
          prop: "def_xgsbxx",
          label: "修改失败信息",
          type: "def_btn",
          fun: "failInfo",
        },
        {
          prop: "def_ffgzt",
          label: "发放工资条",
          type: "def_btn",
          fun: "handleGrant",
        }
      );
      this.tableHeader[9] = def_cz;
    } else {
      let def_cz = this.tableHeader[10];
      def_cz.btn.push(
        {
          prop: "def_sjjy",
          label: "数据校验",
          type: "def_btn",
          fun: "salaryApply",
        },
        {
          prop: "def_tjff",
          label: "提交发放",
          type: "def_btn",
          fun: "submit",
        },
        {
          prop: "def_xgsbxx",
          label: "修改失败信息",
          type: "def_btn",
          fun: "failInfo",
        }
      );
      this.tableHeader[10] = def_cz;
    }
    
  },
  methods: {
    // 代发申请列表
    getApplyList() {
      let data = deepClone(this.apply);
      if (!data.checkStatus) delete data.checkStatus;
      if (!data.payStatus) delete data.payStatus;
      this.loading = true;
      apiPaySalaryApply(data)
        .then((res) => {
          this.loading = false;
          if (res.success) {
            this.total = res.data.total;
            this.resultData = res.data.records;
          }
        })
        .catch((res) => {
          this.loading = false;
        });
    },
    // 表格数据处理
    handleFormatter({ prop, data, btnItem }, callback) {
      if (prop == "def_cz") {
        switch (btnItem) {
          case "def_sjjy":
            callback(
              data["checkStatus"] !== "VERIFICATION_PASSED" &&
                this.privilegeVoList.includes("salary.newpayroll.apply.check")
            );
            break;
          case "def_tjff":
            callback(
              (data["payStatus"] == "STASH" ||
                data["payStatus"] == "PAYMENT_FAILED" ||
                data["payStatus"] == "PARTIAL_PAYMENT_FAILED") &&
                this.privilegeVoList.includes("salary.newpayroll.apply.commit")
            );
            break;
          case "def_xgsbxx":
            callback(
              (data["checkStatus"] == "VERIFICATION_FAILED" ||
                data["payStatus"] == "PAYMENT_FAILED" ||
                data["payStatus"] == "PARTIAL_PAYMENT_FAILED") &&
                this.privilegeVoList.includes("salary.newpayroll.apply.edit")
            );
            break;
          case "def_ck":
            callback(
              this.privilegeVoList.includes("salary.newpayroll.apply.detail")
            );
            break;
          case "def_sc":
            callback(
              data["payStatus"] == "STASH" &&
                !data["salaryCheckId"] &&
                this.privilegeVoList.includes("salary.newpayroll.apply.delete")
            );
            break;
          case "def_ffgzt":
            callback(data["payStatus"] == "PAID");
            break;
        }
      } else {
        switch (prop) {
          case "checkStatus":
            callback(
              `<span style="color:${
                this.constData.checkData[data[prop]].color
              }">${this.constData.checkData[data[prop]].name}</span>`
            );
            break;

          case "payStatus":
            callback(
              `<span style="color:${
                this.constData.paymentData[data[prop]].color
              }">${this.constData.paymentData[data[prop]].name}</span>`
            );
            break;
          default:
            callback(data[prop] || "--");
        }
      }
    },
    // 表格按钮处理
    handleBtnColumn(val, type) {
      if (type == "look") this.toDetail(val);
      if (type == "salaryApply") this.salaryApply(val);
      if (type == "failInfo") this.toDetail(val);
      if (type == "submitForRelease") this.submitForRelease(val);
      if (type == "submit") this.submit(val);
      if (type == "delete") this.handleDelete(val);
      if (type == "handleGrant") this.handleGrant(val);
    },
    async handleGrant(val) {
      console.log("val", val);
      await this.getPaySalaryApplyBatchInfo(val.id);
      this.$store.commit("payrollStore/PAYPARAMS", this.obj);
      this.$router.push({
        path: "/payroll/add-payroll",
        query: {
          active: this.queryActive,
        },
      });
    },
    //获取代发批次信息
    async getPaySalaryApplyBatchInfo(id) {
      const { data, success } = await getPaySalaryApplyBatchInfo({
        applyBatchId: id,
      });

      if (success) {
        if (data.uploadFileId) {
          this.queryActive = 1;
          this.obj = {
            name: data.subjectName,
            salaryPaymentMonth: this.format(data.salaryMonth),
            fileId: data.uploadFileId,
            fileName: data.uploadFileName,
            mappings: data.mappings,
            titles: data.titles,
            isAgent: true,
          };
        } else {
          this.queryActive = 2;
          this.obj = {
            salaryPaymentMonth: this.format(data.salaryMonth),
            isAgent: true,
          };
        }
      }
    },
    format(str) {
      return str.substr(0, 4) + "-" + str.substr(4, 2);
    },
    // 分页处理
    handleSearch(val) {
      let { limit, start, page } = val;
      this.apply.currPage = page;
      this.apply.pageSize = limit;
      this.getApplyList();
    },
    // 筛选
    screen() {
      this.apply.currPage = 1;
      this.getApplyList();
    },
    // 跳转详情页
    toDetail(row) {
      this.$router.push({
        path: "/agent-apply-detail",
        query: {
          id: row.id,
        },
      });
    },
    // 公司主体列表
    companyList() {
      apiGetSubjectList().then((res) => {
        if (res.success) {
          this.companyData = res.data;
        }
      });
    },
    // 批量数据校验
    batchCheck() {
      if (this.selectionList.length) {
        console.log("this.selectionList", this.selectionList);
        let [idList, isCheck] = [[], false];
        this.selectionList.forEach((item) => {
          if (item.checkStatus !== "TO_BE_VERIFIED") isCheck = true;
          idList.push(item.id);
        });
        if (isCheck) {
          return this.$message.error(
            "存在不可校验的批次，请选择状态为“待校验”的批次"
          );
        }
        this.salaryApply({ compId: this.selectionList[0].compId, id: idList });
      } else {
        this.$message.error("请勾选数据");
      }
    },
    // 数据校验
    salaryApply(row) {
      this.loading = true;
      let data = {
        compId: row.compId,
        paySalaryApplyBathIds: typeof row.id == "object" ? row.id : [row.id],
      };
      apiCheckPaySalaryApply(data)
        .then((res) => {
          this.loading = false;
          if (res.success) {
            this.$alert("数据校验完成，请到批次详情中查看校验结果", "提示", {
              confirmButtonText: "我知道了",
            });
            this.getApplyList();
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    // 表格勾选数据
    selectionChange(val) {
      this.selectionList = val;
    },

    agentSelectAccountCancel() {
      this.showAgentSelectAccount = false;
    },
    agentSelectAccountConfirm(data) {
      this.showAgentSelectAccount = false;
      this.submitcgb(data);
    },
    submit(row) {
      let data = {
        compId: row.compId,
        paySalaryApplyBathIds: typeof row.id == "object" ? row.id : [row.id],
      };
      this.$confirm("确认要提交发放吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          if (
            row.checkStatus == "TO_BE_VERIFIED" ||
            row.checkStatus == "VERIFICATION_FAILED"
          ) {
            this.$confirm(
              "存在数据校验未校验或未通过的代发订单，确定要提交发放吗?",
              "提示",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                closeOnClickModal: false,
                closeOnPressEscape: false,
              }
            ).then(() => {
              if (row.isCheck) {
                return this.$message.error("存在不可提交的批次");
              }
              this.submitApi(data);
            });
          } else {
            if (row.isCheck) {
              return this.$message.error("存在不可提交的批次");
            }
            this.submitApi(data);
          }
        })
        .catch(() => {
          this.$message({
            type: "success",
            message: "已取消提交发放",
          });
        });
    },
    // 提交发放
    submitcgb(row) {
      let data = {
        compId: row.compId,
        paySalaryApplyBathIds: typeof row.id == "object" ? row.id : [row.id],
        payAccountId: row.payAccountId,
      };
      this.$confirm("确认要提交发放吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          if (row.isCheck) {
            return this.$message.error("存在不可提交的批次");
          }
          this.submitApi(data);
        })
        .catch(() => {
          this.$message({
            type: "success",
            message: "已取消提交发放",
          });
        });
    },
    // 调用提交接口
    submitApi(data) {
      apiSubmitApplyBathRecord(data).then((res) => {
        if (res.success) {
          this.$message.success("操作成功");
          this.getApplyList();
        }
      });
    },
    submitForRelease(row) {
      this.commitParameter.param = row;
      let data = {
        unifiedCode: row.unifiedCode,
      };
      apiQuerySubjectAccountList(data).then((res) => {
        if (res.success) {
          if (res.data.length > 1) {
            this.commitParameter.accountList = res.data;
            this.timer = new Date().getTime();
            this.showAgentSelectAccount = true;
          } else {
            this.submitcgb(row);
          }
        }
      });
    },
    // 批量提交
    batchSubmit() {
      if (this.selectionList.length) {
        let idList = [];
        let checkStatus = "";
        let isCheck = false;
        let subjectName = this.selectionList[0].subjectName;
        for (let i = 0; i < this.selectionList.length; i++) {
          let item = this.selectionList[i];
          if (
            (item.payStatus && item.payStatus == "IN_PAYMENT") ||
            item.payStatus == "PAID"
          ) {
            isCheck = true;
          }
          if (
            item.checkStatus == "VERIFICATION_FAILED" ||
            item.checkStatus == "TO_BE_VERIFIED"
          ) {
            checkStatus = item.checkStatus;
          }
          if (subjectName != item.subjectName) {
            return this.$message.error(
              "只能选择发薪公司名称相同的批次批量提交"
            );
          }
          idList.push(item.id);
        }
        if (window.env.server_env === "cgb") {
          let param = {
            compId: this.selectionList[0].compId,
            id: idList,
            checkStatus: checkStatus,
            isCheck: isCheck,
          };
          this.commitParameter.param = param;
          apiQuerySubjectAccountList({
            unifiedCode: this.selectionList[0].unifiedCode,
          }).then((res) => {
            if (res.success) {
              if (res.data.length > 1) {
                this.commitParameter.accountList = res.data;
                this.timer = new Date().getTime();
                this.showAgentSelectAccount = true;
              } else {
                this.submit(param);
              }
            }
          });
        } else {
          this.submit({
            compId: this.selectionList[0].compId,
            id: idList,
            checkStatus: checkStatus,
            isCheck: isCheck,
          });
        }
      } else {
        this.$message.error("请勾选数据");
      }
    },
    // 删除
    async handleDelete(row) {
      try {
        const res = await removePaySalaryApplyBatch({ applyBatchId: row.id });
        if (res.success) {
          this.getApplyList();
          this.$message({
            type: "success",
            message: "删除成功",
          });
        }
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.agent-apply {
  /*height: calc(100vh - 80px);*/
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }
  .screening {
    padding: 0 20px;
    .agent-form {
      margin-top: 20px;
      .screen {
        position: relative;
        top: 1px;
      }
    }
    .agent-table {
      margin-top: 20px;
    }
    .pagination {
      float: right;
      padding: 22px 0 22px 22px;
    }
    .width-198 {
      width: 198px;
    }
  }
}
</style>
