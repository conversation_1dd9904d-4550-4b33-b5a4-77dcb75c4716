<template>
  <div class="agent-apply-detail">
    <header class="header">
      <el-row type="flex" style="justify-content: space-between">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>代发详情</span>
        </el-col>
        <div>
          <el-button type="primary" @click="modifyInfo">修改失败信息</el-button>
        </div>
      </el-row>
    </header>
    <div class="screening" v-loading="loading">
      <div class="agent-explain">
        <div class="agent-left">
          <p>发薪公司名称：{{ noticeData.subjectName }}</p>
          <p>发放月份：{{ noticeData.salaryMonth }}</p>
        </div>
        <div class="agent-right">
          <p>总笔数：{{ noticeData.totalCount }}笔</p>
          <p>总金额（元）：{{ setDecimalBit(noticeData.totalAmount) }}元</p>
        </div>
      </div>
      <div class="agent-form">
        <section>
          <el-input
            v-model="searchForm.searchValue"
            placeholder="请输入姓名查询"
            style="width: 180px; margin-right: 10px"
            clearable
          ></el-input>
          <el-button @click="search" class="screen" type="primary"
            >查询</el-button
          >
        </section>
      </div>
      <div class="agent-table">
        <el-form
          :rules="submitForm.uploadFormRules"
          ref="uploadForm"
          :model="submitForm"
        >
          <el-table
            :data="submitForm.tableData"
            :header-cell-style="{ background: '#F1F1F1' }"
            stripe
            border
            :height="tableHeight"
          >
            <el-table-column
              v-if="server_env === 'cgb'"
              prop="id"
              label="序号"
              min-width="100"
            ></el-table-column>
            <el-table-column
              v-if="server_env === 'cgb'"
              prop="empName"
              label="姓名"
              min-width="200"
              ><template slot-scope="scope">
                <span v-if="scope.row.isModify">{{ scope.row.empName }}</span>
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.empName'"
                  :rules="submitForm.uploadFormRules.empName"
                  v-else
                >
                  <el-input
                    v-model="scope.row.empName"
                    placeholder="请输入"
                    size="mini"
                    maxlength="32"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              v-else
              prop="empName"
              label="姓名"
              min-width="200"
            ></el-table-column>
            <el-table-column
              prop="idNo"
              label="证件号码"
              min-width="200"
            ></el-table-column>
            <el-table-column
              prop="mobileNo"
              label="手机号"
              min-width="200"
            ></el-table-column>
            <el-table-column label="银行卡号" min-width="200">
              <template slot-scope="scope">
                <span v-if="scope.row.isModify">{{
                  scope.row.accountBankNo
                }}</span>
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.accountBankNo'"
                  :rules="submitForm.uploadFormRules.accountBankNo"
                  v-else
                >
                  <el-input
                    v-model="scope.row.accountBankNo"
                    placeholder="请输入"
                    size="mini"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="开户行" min-width="200">
              <template slot-scope="scope">
                <span v-if="scope.row.isModify">{{
                  scope.row.accountBank
                }}</span>
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.accountBank'"
                  :rules="submitForm.uploadFormRules.accountBank"
                  v-else
                >
                  <el-input
                    v-model="scope.row.accountBank"
                    placeholder="请输入"
                    size="mini"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              v-if="server_env === 'cgb'"
              prop="accUnionBankNo"
              label="联行号"
              min-width="200"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.isModify">{{
                  scope.row.accUnionBankNo
                }}</span>
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.accountBank'"
                  v-else
                >
                  <el-input
                    v-model="scope.row.accUnionBankNo"
                    placeholder="请输入"
                    size="mini"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="realAmount"
              label="实发金额（元）"
              min-width="200"
            ></el-table-column>
            <el-table-column
              v-if="server_env === 'cgb'"
              prop="remark"
              label="备注"
              min-width="200"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.isModify">{{ scope.row.remark }}</span>
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.accountBank'"
                  v-else
                >
                  <el-input
                    v-model="scope.row.remark"
                    placeholder="请输入"
                    size="mini"
                    maxlength="120"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              v-if="server_env === 'cgb'"
              prop="transferTips"
              label="附言"
              min-width="200"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.isModify">{{
                  scope.row.transferTips
                }}</span>
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.accountBank'"
                  v-else
                >
                  <el-input
                    v-model="scope.row.transferTips"
                    placeholder="请输入"
                    size="mini"
                    maxlength="120"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              v-if="server_env !== 'cgb'"
              prop="checkStatusName"
              label="校验状态"
              min-width="200"
            >
              <template slot-scope="scope">
                <span :style="{ color: scope.row.checkStatusName.color }">
                  {{ scope.row.checkStatusName.name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="payStatusName"
              label="订单状态"
              min-width="200"
            >
              <template slot-scope="scope">
                <span :style="{ color: scope.row.payStatusName.color }">
                  {{ scope.row.payStatusName.name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="errorInfo"
              :label="server_env === 'cgb' ? '反馈信息' : '备注'"
              min-width="200"
            ></el-table-column>
            <el-table-column
              v-if="server_env === 'cgb'"
              label="操作"
              min-width="200"
            >
              <template slot-scope="scope">
                <el-link
                  type="primary"
                  :underline="false"
                  @click="revise(scope.$index)"
                  :style="{
                    color: !(
                      scope.row.isModify &&
                      (scope.row.payStatus == 'STASH' ||
                        scope.row.payStatus == 'PAYMENT_FAILED')
                    )
                      ? 'grey'
                      : '',
                  }"
                  :disabled="
                    !(
                      scope.row.isModify &&
                      (scope.row.payStatus == 'STASH' ||
                        scope.row.payStatus == 'PAYMENT_FAILED')
                    )
                  "
                  >修改</el-link
                >
                <el-link
                  type="primary"
                  :underline="false"
                  @click="save(scope.$index)"
                  v-if="!scope.row.isModify"
                  >保存</el-link
                >
              </template>
            </el-table-column>
            <el-table-column v-else label="操作" min-width="200">
              <template slot-scope="scope">
                <el-link
                  type="primary"
                  :underline="false"
                  @click="revise(scope.$index)"
                  :style="{
                    color: !(
                      scope.row.isModify &&
                      (scope.row.checkStatus == 'TO_BE_VERIFIED' ||
                        scope.row.checkStatus == 'VERIFICATION_FAILED' ||
                        scope.row.payStatus == 'PENDING_PAYMENT' ||
                        scope.row.payStatus == 'PAYMENT_FAILED')
                    )
                      ? 'grey'
                      : '',
                  }"
                  :disabled="
                    !(
                      scope.row.isModify &&
                      (scope.row.checkStatus == 'TO_BE_VERIFIED' ||
                        scope.row.checkStatus == 'VERIFICATION_FAILED' ||
                        scope.row.payStatus == 'PENDING_PAYMENT' ||
                        scope.row.payStatus == 'PAYMENT_FAILED')
                    )
                  "
                  >修改</el-link
                >
                <el-link
                  type="primary"
                  :underline="false"
                  @click="save(scope.$index)"
                  v-if="!scope.row.isModify"
                  >保存</el-link
                >
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="prev, pager, next, sizes"
          :total="total"
          background
        >
        </el-pagination>
      </div>
    </div>
    <import-list
      ref="importList"
      :applyBatchId="noticeData.id"
      @refresh="refresh"
    ></import-list>
  </div>
</template>
<script>
import beTable from "./components/Table";
import importList from "./components/importList";
import * as constData from "./util/constData";
import {
  apiGetPaySalaryApplyRecordList,
  apiModifyPaySalaryApplyRecord,
} from "./store/api";
import { setDecimalBit } from "./util/index";
import { validateBankId } from "@/util/validate";
export default {
  data() {
    return {
      server_env: window.env.server_env,
      agent: {
        name: "",
        status: "",
        batch: "",
        month: "",
      },
      pageSize: 10,
      total: 0,
      currPage: 1,
      constData: {},
      noticeData: {},
      submitForm: {
        tableData: [],
        uploadFormRules: {
          empName: [
            {
              required: true,
              message: "请输入",
              trigger: "blur",
            },
          ],
          accUnionBankNo: [
            {
              message: "请输入",
              trigger: "blur",
            },
          ],
          remark: [
            {
              message: "请输入",
              trigger: "blur",
            },
          ],
          accountBankNo: [
            {
              required: true,
              validator: validateBankId,
              trigger: "blur",
            },
          ],
        },
      },
      searchForm: {
        searchValue: "",
      },
      loading: false,
      isShowImport: false,
      tableHeight: document.body.offsetHeight - 400 + "px",
    };
  },
  components: {
    beTable,
    importList,
  },
  mounted() {
    this.constData = constData;
    this.getApplyDetail();
    if (window.env.server_env === "cgb") {
      this.submitForm.uploadFormRules["accountBank"] = {
        message: "请输入",
        trigger: "blur",
      };
    } else {
      this.submitForm.uploadFormRules["accountBank"] = {
        required: true,
        message: "请输入",
        trigger: "blur",
      };
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val;
      this.getApplyDetail();
    },
    handleCurrentChange(val) {
      this.currPage = val;
      this.getApplyDetail();
    },
    // 代发申请详情列表
    getApplyDetail() {
      let data = {
        applyBatchId: this.$route.query.id,
        currPage: this.currPage,
        empName: this.searchForm.searchValue,
        pageSize: this.pageSize,
      };
      this.loading = true;
      apiGetPaySalaryApplyRecordList(data)
        .then((res) => {
          this.loading = false;
          if (res.success) {
            this.noticeData = res.data;
            this.total = res.data.recordVoPageInfo.total;
            this.submitForm.tableData = res.data.recordVoPageInfo.records;
            this.submitForm.tableData.filter((item) => {
              this.$set(item, "isModify", true);
              item.payStatusName = this.constData.paymentData[item.payStatus];
              item.checkStatusName = this.constData.checkData[item.checkStatus];
            });
            console.log(this.submitForm.tableData);
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    // 修改失败信息
    modifyInfo() {
      this.$refs.importList.show();
    },
    // 修改
    revise(index) {
      this.$set(this.submitForm.tableData[index], "isModify", false);
    },
    // 保存
    save(index) {
      let row = this.submitForm.tableData[index];
      this.$refs.uploadForm.validate((valid) => {
        if (valid) {
          let data = {};
          if (window.env.server_env === "cgb") {
            data = {
              accountBank: row.accountBank,
              accountBankNo: row.accountBankNo,
              compId: row.compId,
              id: row.id,
              remark: row.remark,
              transferTips: row.transferTips,
              accUnionBankNo: row.accUnionBankNo,
              empName: row.empName,
            };
          } else {
            data = {
              accountBank: row.accountBank,
              accountBankNo: row.accountBankNo,
              compId: row.compId,
              id: row.id,
            };
          }

          apiModifyPaySalaryApplyRecord(data).then((res) => {
            if (res.success) {
              this.$message.success("保存成功");
              this.getApplyDetail();
              this.$set(this.submitForm.tableData[index], "isModify", true);
            }
          });
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    // 筛选
    search() {
      this.currPage = 1;
      this.getApplyDetail();
    },
    refresh() {
      this.getApplyDetail();
    },
    setDecimalBit(number, n = 2) {
      if (!isNaN(parseFloat(number)) && isFinite(number)) {
        number = (
          Math.round(number * Math.pow(10, n)) * Math.pow(0.1, n)
        ).toFixed(n);
        number = number.split(".");
        number = number[1]
          ? [number[0], number[1].slice(0, n)].join(".")
          : number[0];
      }
      return number;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.agent-apply-detail {
  /*height: calc(100vh - 80px);*/
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }
  .screening {
    padding: 0 20px;
    .agent-explain {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      background-color: #f2f2f2;
      border-radius: 4px;
      padding: 18px 15px;
      line-height: 30px;
      .agent-left,
      .agent-right {
        width: 50%;
      }
    }
    .agent-form {
      margin-top: 20px;

      .screen {
        position: relative;
        top: 1px;
      }
    }
    .agent-table {
      margin-top: 20px;
    }
    .pagination {
      float: right;
      padding: 15px 0 15px;
      /deep/ .el-pagination__sizes {
        position: relative;
        bottom: 2px;
      }
    }
    .width-198 {
      width: 198px;
    }
  }
}
</style>
