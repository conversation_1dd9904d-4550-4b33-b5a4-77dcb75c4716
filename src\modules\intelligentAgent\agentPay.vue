<template>
  <div class="agent-pay">
    <header v-if="!isGuidance" class="pay-header">
      <el-row type="flex" style="justify-content: space-between">
        <span>代发付款</span>
        <div>
          <!-- <el-button type=" " @click=""
            >导出</el-button
          > -->
        </div>
      </el-row>
    </header>
    <section v-if="!isGuidance" class="pay-search">
      <div class="form-select">
        <el-select
          clearable
          v-model="filterForm.gs"
          placeholder="请选择发薪公司名称"
        >
          <el-option
            v-for="(item, index) in optiongs"
            :key="item.unifiedCode + index"
            :label="item.subjectName"
            :value="item.unifiedCode"
          >
          </el-option>
        </el-select>
        <el-select
          clearable
          v-model="filterForm.zt"
          placeholder="请选择批次状态"
        >
          <el-option
            v-for="(item, key) in optionzt"
            :key="key"
            :label="item.name"
            :value="key"
          >
          </el-option>
        </el-select>
        <el-button @click="handleScreen" class="screen" type="primary"
          >查询</el-button
        >
      </div>
      <div class="operate-tips" v-if="server_env !== 'cgb'">
        <span @click="isGuidance = true">图文操作指引</span>
        <span>｜</span>
        <span @click="handleSeeVideo">视频操作指引</span>
      </div>
    </section>
    <section v-if="!isGuidance" class="pay-content">
      <def-table
        :tableHeader="tableHeader"
        :tableData="tableData"
        @formatter="handleFormatter"
        @btnColumn="handleBtnColumn"
        @search="handleSearch"
        :total="total"
        :loading="loading"
        :isShowIndex="true"
        :isHidePage="false"
      />
    </section>
    <!--导出发放文件-->
    <el-dialog
      title="导出发放文件"
      :visible.sync="isShowExportFile"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="750px"
      :show-close="false"
    >
      <def-export-file
        ref="defExportFile"
        :handlerMsg="handlerMsg"
        v-if="isShowExportFile"
      />
      <div slot="footer">
        <el-button @click="isShowExportFile = false">取消</el-button>
        <el-button type="primary" @click="handleExportFile">确定</el-button>
      </div>
    </el-dialog>
    <!--确认发放-->
    <el-dialog
      title="确认发放"
      :visible.sync="isShowConfirmRelease"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      :show-close="false"
    >
      <def-confirm-release
        ref="defConfirmRelease"
        :handlerMsg="handlerMsg"
        v-if="isShowConfirmRelease"
      />
      <div slot="footer">
        <el-button @click="isShowConfirmRelease = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmReleaseConfirm"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!--完成发放-->
    <el-dialog
      title="完成发放"
      :visible.sync="isShowCompleteRelease"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      :show-close="false"
    >
      <def-complete-release
        ref="defCompleteRelease"
        :handlerMsg="handlerMsg"
        v-if="isShowCompleteRelease"
      />
      <div slot="footer">
        <el-button @click="isShowCompleteRelease = false">取消</el-button>
        <el-button type="primary" @click="handleCompleteReleaseConfirm"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!--导入失败文件-->
    <el-dialog
      title="导入失败文件"
      :visible.sync="isShowImportFile"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      :show-close="false"
    >
      <def-import-file
        v-if="isShowImportFile"
        @isDiaLogBtnDis="handleIsDiaLogBtnDis"
        :payBatchId="id"
      />
      <div slot="footer">
        <el-button @click="isShowImportFile = false">取消</el-button>
        <el-button
          type="primary"
          :disabled="isDiaLogBtnDis"
          @click="handleImportFileConfirm"
          >导入通过数据</el-button
        >
      </div>
    </el-dialog>
    <!-- 视频操作指引 -->
    <el-dialog
      title="智能代发操作指引"
      :visible.sync="isShowVideo"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="740px"
      :show-close="true"
      :destroy-on-close="true"
      class="create-dialog"
    >
      <div id="J_prismPlayer"></div>
    </el-dialog>
    <el-dialog
      :title="showTitle"
      width="630px"
      :visible.sync="isShowGetResults"
      @close="closeGetResults"
    >
      <getShowGetResults
        :key="timer"
        :resultDetail="resultDetail"
        @close="closeGetResults"
      ></getShowGetResults>
    </el-dialog>
  </div>
</template>

<script>
import defTable from "./components/Table";
import defExportFile from "./components/agentPay/ExportFile";
import defConfirmRelease from "./components/agentPay/ConfirmRelease";
import defCompleteRelease from "./components/agentPay/CompleteRelease";
import defImportFile from "./components/agentPay/ImportFile";
import operationalGuidance from "./components/agentPay/operationalGuidance";
import getShowGetResults from "./components/agentPay/getShowGetResults";

import {
  apiGetPaySalaryList,
  apiDownloadPaymentDocuments,
  apiConfirmPay,
  apiGetSubjectList,
  apiConfirmUploadPaySalary,
  apiCompleteDistribution,
  apiCancelIssuance,
  apiDownloadPaymentDocumentse,
  apiGetCgbWebPayChannelConfig,
  apiCheckBeforePay,
  apiPaySalaryCommit,
  apiQueryBatch,
} from "./store/api";
import { payEnum } from "./util/constData";
import { mapState } from "vuex";
import { apiOperateArchive } from "../staffManage/store/api";
export default {
  name: "agent-pay",
  components: {
    defTable,
    defExportFile,
    defConfirmRelease,
    defCompleteRelease,
    defImportFile,
    operationalGuidance,
    getShowGetResults,
  },
  data() {
    return {
      server_env: window.env.server_env,
      loading: false,
      tableHeight: document.body.clientHeight - 300 + "px",
      id: "",
      tableHeader: [
        { prop: "fkpch", label: "付款批次号", width: "100px" },
        { prop: "pczt", label: "批次状态", width: "80px" },
        { prop: "fxgsmc", label: "发薪公司名称", width: "180px" },
        { prop: "ffyh", label: "发放银行", width: "180px" },
        // { prop: "ffyf", label: "发放月份" },
        { prop: "zbs", label: "总笔数", width: "80px" },
        { prop: "cgbs", label: "成功笔数", width: "80px" },
        { prop: "sbbs", label: "失败笔数", width: "80px" },
        { prop: "zje", label: "总金额（元）", width: "180px" },
        { prop: "cgje", label: "成功金额（元）", width: "150px" },
        { prop: "sbje", label: "失败金额（元）", width: "150px" },

        { prop: "tjrq", label: "创建日期", width: "160px" },
        { prop: "fkrq", label: "付款日期", width: "160px" },
        { prop: "bz", label: "备注" },
        {
          prop: "def_cz",
          label: "操作",
          width: "200px",
          fixed: "right",
          btn: [
            {
              prop: "def_ck",
              label: "查看",
              type: "def_btn",
              fun: "handleExamine",
            },
            {
              prop: "def_dcffwj",
              label: "导出发放文件",
              type: "def_btn",
              fun: "handleExport",
            },
            {
              prop: "def_ksdf",
              label: "开始代发",
              type: "def_btn",
              fun: "handleCreateAgent",
            },
            {
              prop: "def_qrff",
              label: "确认发放",
              type: "def_btn",
              fun: "handleConfirmRelease",
            },
            {
              prop: "def_qxff",
              label: "取消发放",
              type: "def_btn",
              fun: "handleCancelRelease",
            },
            // {prop:"def_sc",label:"删除",type:"def_btn",fun:"handleDelete"},
          ],
        },
      ],
      tableData: [],
      total: null,
      limit: 10,
      start: 0,
      page: 1,
      optiongs: [],
      optionzt: payEnum,
      filterForm: {
        gs: "",
        yf: "",
        zt: "",
      },
      isShowExportFile: false,
      isShowConfirmRelease: false,
      isShowCompleteRelease: false,
      isShowImportFile: false,
      isDiaLogBtnDis: true,
      handlerMsg: {
        //经办人信息
        handler: "",
        handlerMobile: "",
        handlerMobileTrue: "",
      },
      isGuidance: false,
      isShowVideo: false,
      showTitle: "",
      isShowGetResults: false,
      pictureGuideLinkAddress: null,
      resultDetail: {},
      timer: "",
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  mounted() {
    this.handleInit();
    this.getCgbWebPayChannelConfig();
    if (window.env.server_env === "cgb") {
      this.tableHeader.splice(1, 0, {
        prop: "dfsqpch",
        label: "代发申请批次号",
        width: "112px",
      });
      this.tableHeader[14].btn.push({
        prop: "def_hqffjg",
        label: "获取发放结果",
        type: "def_btn",
        fun: "handleGetResultsRelease",
      });
    } else {
      this.tableHeader[13].btn.push(
        {
          prop: "def_wcff",
          label: "完成发放",
          type: "def_btn",
          fun: "handleCompleteRelease",
        },
        {
          prop: "def_drsbwj",
          label: "导入失败文件",
          type: "def_btn",
          fun: "handleImport",
        }
      );
    }
  },
  methods: {
    handleInit() {
      this.handleTableData();
      this.handleGetSubjectList();
    },
    //脱敏
    handleName(val) {
      let res = val;
      if (val.length == 2) {
        res = val.replace(new RegExp(".*(?=[\u4e00-\u9fa5])"), "*");
      }
      if (val.length > 2) {
        res = val.replace(
          new RegExp("(?<=[\u4e00-\u9fa5]).*(?=[\u4e00-\u9fa5])"),
          Array(val.length - 1).join("*")
        );
      }
      return res;
    },
    handleDownloadPaymentDocumentse() {
      return new Promise((resolve, reject) => {
        this.$set(this.handlerMsg, "handler", "");
        this.$set(this.handlerMsg, "handlerMobile", "");
        this.$set(this.handlerMsg, "handlerMobileTrue", "");
        apiDownloadPaymentDocumentse(this.id).then((res) => {
          if (res.success) {
            const { handler, handlerMobile } = res.data;
            this.$set(this.handlerMsg, "handler", this.handleName(handler));
            this.$set(this.handlerMsg, "handlerMobileTrue", handlerMobile);
            this.$set(
              this.handlerMsg,
              "handlerMobile",
              handlerMobile.replace(new RegExp("^(.{3}).+(.{4})$"), "$1****$2")
            );
            this.isShowExportFile = false;
            resolve("true");
          }
        });
      });
    },
    //获取发薪公司数据
    handleGetSubjectList() {
      apiGetSubjectList().then((res) => {
        if (res.success) {
          this.optiongs = res.data;
        }
      });
    },
    //查询列表数据
    handleTableData() {
      const { gs, yf, zt } = this.filterForm;
      apiGetPaySalaryList({
        unifiedCode: gs,
        payStatus: zt || undefined,
        salaryMonth: yf,
        currPage: this.page,
        pageSize: this.limit,
      }).then((res) => {
        this.tableData = res.data.records;
        this.total = res.data.total;
      });
    },
    //格式化table数据
    handleFormatter({ prop, data, btnItem }, callback) {
      // console.log(data)
      if (prop == "def_cz") {
        let boo = false;
        // callback(true)
        switch (btnItem) {
          case "def_ck": //查看：一直显示
            boo = this.privilegeVoList.includes("salary.newpayroll.pay.detail");
            callback(boo);
            break;
          case "def_ksdf": //开始代发：批次状态为“待提交/待付款”，且发放银行的通道类型为“企业网银”类型
            boo =
              window.env.server_env === "cgb"
                ? data["payStatus"] == "STASH"
                : ["STASH", "IN_PAYMENT"].includes(data["payStatus"]) &&
                  data["payChannelConfVo"]["channelType"] == "WEB" &&
                  this.privilegeVoList.includes(
                    "salary.newpayroll.pay.export"
                  ) &&
                  data["channelCode"] == "CGB-WEB";
            // boo = true
            callback(boo);
            break;
          case "def_dcffwj": //导出发放文件：批次状态为“待提交/待付款”，且发放银行的通道类型为“企业网银”类型
            boo =
              ["STASH", "IN_PAYMENT"].includes(data["payStatus"]) &&
              data["payChannelConfVo"]["channelType"] == "WEB" &&
              this.privilegeVoList.includes("salary.newpayroll.pay.export") &&
              data["channelCode"] == "BOC-WEB";
            callback(boo);
            break;
          case "def_qrff": //确认发放：批次状态为“待提交”，且发放银行的通道类型为“API”类型
            boo =
              data["payStatus"] == "STASH" &&
              data["payChannelConfVo"]["channelType"] == "API" &&
              this.privilegeVoList.includes("salary.newpayroll.pay.comfirmPay");
            callback(boo);
            break;
          case "def_qxff": //取消发放：批次状态为“待提交”
            boo =
              data["payStatus"] == "STASH" &&
              this.privilegeVoList.includes("salary.newpayroll.pay.cancel");
            callback(boo);
            break;
          case "def_drsbwj": //导入失败文件：批次状态为“付款中”，且发放银行的通道类型为“企业网银”类型
            boo =
              data["payStatus"] == "IN_PAYMENT" &&
              data["payChannelConfVo"]["channelType"] == "WEB" &&
              this.privilegeVoList.includes("salary.newpayroll.pay.importFail");
            callback(boo);
            break;
          case "def_wcff": //完成发放：批次状态为“付款中”，且发放银行的通道类型为“企业网银”类型
            boo =
              data["payStatus"] == "IN_PAYMENT" &&
              data["payChannelConfVo"]["channelType"] == "WEB" &&
              this.privilegeVoList.includes("salary.newpayroll.pay.complete");
            callback(boo);
            break;
          case "def_sc": //删除：批次状态为“付款失败”
            boo =
              data["payStatus"] == "PAYMENT_FAILED" &&
              this.privilegeVoList.includes("salary.newpayroll.pay.delete");
            callback(boo);
            break;
          case "def_hqffjg":
            boo = data["payStatus"] == "IN_PAYMENT";
            callback(boo);
            break;
        }
      } else {
        switch (prop) {
          case "fkpch":
            callback(data["id"]);
            break;
          case "dfsqpch":
            callback(data["applyBatchIds"]);
            break;
          case "fxgsmc":
            callback(data["subjectName"]);
            break;
          case "ffyh":
            callback(data["payChannelConfVo"]["channelName"]);
            break;
          case "pczt":
            callback(
              `<span style="color:${payEnum[data["payStatus"]].color}">${
                payEnum[data["payStatus"]].name
              }</span>`
            );
            break;
          // case "ffyf":
          //   callback('发放月份')
          //   break;
          case "zbs":
            callback(data["totalCount"]);
            break;
          case "cgbs":
            callback(data["successCount"] || "--");
            break;
          case "sbbs":
            callback(data["failCount"] || "--");
            break;
          case "cgje":
            callback(data["successAmount"] || "--");
            break;
          case "sbje":
            callback(data["failAmount"] || "--");
            break;
          case "zje":
            callback(data["totalAmount"] || "--");
            break;
          case "tjrq":
            callback(data["createdTime"] || "--");
            break;
          case "fkrq":
            callback(data["payTime"] || "--");
            break;
          case "bz":
            callback(data["errorInfo"] || "--");
            break;
        }
        // callback(data[prop])
      }
    },
    //处理操作按钮
    handleBtnColumn(val, type) {
      console.log("val", val);

      this.id = val.id;
      // let arr = ["handleExport","handleConfirmRelease","handleCompleteRelease"]
      // if(arr.includes(type)){
      //   this.handleDownloadPaymentDocumentse()
      // }

      switch (type) {
        case "handleExport":
          this.handleDownloadPaymentDocumentse().then((res) => {
            console.log(res, "res");
            if ((res = "true")) {
              this.isShowExportFile = true;
            }
          });
          break;

        case "handleCreateAgent":
          if (this.server_env === "cgb") {
            this.loading = true;
            apiCheckBeforePay({ payBatchId: val.id })
              .then((res) => {
                if (res.success) {
                  if (res.data.accountBalance < val.totalAmount) {
                    this.showTitle = "提示";
                    this.resultDetail.status = "FAIL";
                    this.resultDetail.title = "当前账户余额不足，无法发放";
                    this.resultDetail.commissionAccount = res.data.payAccountNo;
                    this.resultDetail.accountBalance = res.data.accountBalance;
                    this.timer = new Date().getTime();
                    this.isShowGetResults = true;
                    return;
                  }
                  if (res.data.accountType === "NORMAL") {
                    this.$router.push({
                      path: "/intelligentAgentCGB/step1",
                      query: {
                        id: this.id,
                      },
                    });
                  } else if (res.data.accountType === "BASIC") {
                    let data = {
                      payBatchId: val.id,
                      recordVoList: [],
                    };
                    apiPaySalaryCommit(data).then((res) => {
                      if (res.success) {
                        this.$router.push({
                          path: "/intelligentAgentCGB/step2",
                          query: {
                            data: JSON.stringify(res.data),
                            type: "BASIC",
                          },
                        });
                      }
                    });
                  }
                }
              })
              .finally(() => {
                setTimeout(() => {
                  this.loading = false;
                }, 5000);
              });
          } else {
            this.$router.push({
              path: "/create-agent-file",
              query: {
                id: this.id,
              },
            });
          }

          break;

        case "handleConfirmRelease":
          this.handleDownloadPaymentDocumentse().then((res) => {
            if ((res = "true")) {
              this.isShowConfirmRelease = true;
            }
          });
          break;
        case "handleCompleteRelease":
          this.handleDownloadPaymentDocumentse().then((res) => {
            if ((res = "true")) {
              this.isShowCompleteRelease = true;
            }
          });
          break;
        case "handleImport":
          this.isShowImportFile = true;
          break;
        case "handleExamine":
          this.$router.push({
            path: "/agent-pay-detail",
            query: { id: this.id },
          });
          break;
        case "handleCancelRelease":
          this.handleCancelRelease();
          break;
        case "handleGetResultsRelease":
          this.handleGetResultsRelease(val);
          break;
      }
    },
    // 查看视频指引
    handleSeeVideo() {
      this.isShowVideo = true;
      this.$nextTick(() => {
        // eslint-disable-next-line no-undef
        this.player = new Aliplayer({
          id: "J_prismPlayer",
          source:
            "https://olading-static-resource.oss-cn-beijing.aliyuncs.com/olading-mini-image/数字人资企业网银代发操作指引.mp4/1639479878666067.mp4",
          width: "708px",
          height: "398px",
          autoplay: true,
          isLive: false,
          rePlay: false,
          playsinline: false,
          preload: true,
        });
      });
    },
    handleSearch({ limit, start, page }) {
      this.page = page;
      this.limit = limit;
      this.start = start;
      console.log(limit, start, page);
      this.handleTableData();
    },
    //取消发放
    handleCancelRelease() {
      apiCancelIssuance(this.id).then((res) => {
        if (res.success) {
          this.$message.success("操作成功");
          this.handleInit();
        }
      });
    },
    //获取发放结果
    async handleGetResultsRelease(val) {
      this.loading = true;
      let res = await apiQueryBatch(val.id);
      if (res.success) {
        const data = res.data;
        if (data.payStatus === "PAID") {
          this.resultDetail.status = "SUCCESS";
          this.resultDetail.title = "付款成功";
        } else if (data.payStatus === "IN_PAYMENT") {
          this.resultDetail.status = "PROCESSING";
          this.resultDetail.title = "付款中";
        } else if (data.payStatus === "PAYMENT_FAILED") {
          this.resultDetail.status = "FAIL_PAY";
          this.resultDetail.title = "付款失败";
        } else if (data.payStatus === "PARTIAL_PAYMENT_FAILED") {
          this.resultDetail.status = "FAIL_PAY";
          this.resultDetail.title = "部分付款失败";
        }
        this.resultDetail.id = val.id;
        this.resultDetail.totalMmount = data.totalAmount;
        this.resultDetail.totalCommission = data.totalFee;
        this.resultDetail.count = data.totalNum;
        this.resultDetail.successCount = data.successNum;
        this.resultDetail.failCount = data.failNum;
        this.resultDetail.processingCount = data.processingNum;
        this.showTitle = "代发结果";
        this.timer = new Date().getTime();
        this.isShowGetResults = true;
      }
      this.loading = false;
      // this.resultDetail.status = "FAIL";
      // this.resultDetail.title = "当前账户余额不足，无法发放";
      // this.resultDetail.commissionAccount = '***************'
      // this.resultDetail.accountBalance = '71387.23'
    },
    closeGetResults() {
      this.isShowGetResults = false;
      this.handleInit();
    },
    //确认发放
    handleConfirmReleaseConfirm() {
      console.log(this.$refs);
      this.$refs["defConfirmRelease"].handleSubmitForm().then((res) => {
        if (res == "true") {
          apiConfirmPay({ payBatchId: this.id }).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.isShowConfirmRelease = false;
              this.handleInit();
            }
          });
        }
      });
    },
    //完成发放
    handleCompleteReleaseConfirm() {
      this.$refs["defCompleteRelease"].handleSubmitForm().then((res) => {
        if (res == "true") {
          apiCompleteDistribution(this.id).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.isShowCompleteRelease = false;
              this.handleInit();
            }
          });
        }
      });
    },
    //导入失败文件
    handleImportFileConfirm() {
      apiConfirmUploadPaySalary(this.id).then((res) => {
        if (res.success) {
          this.$message.success("操作成功");
          this.isShowImportFile = false;
          this.handleInit();
        }
      });
    },
    //导出发放文件
    handleExportFile() {
      this.$refs["defExportFile"].handleSubmitForm().then((res) => {
        if (res == "true") {
          apiDownloadPaymentDocuments(this.id).then((res) => {
            this.$message.success("操作成功");
            this.isShowExportFile = false;
            this.handleInit();
          });
        }
      });
    },
    //导入失败文件-按钮控制
    handleIsDiaLogBtnDis(boo) {
      this.isDiaLogBtnDis = boo;
    },
    // 筛选
    handleScreen() {
      this.page = 1;
      this.handleTableData();
    },
    // 获取详情数据
    getCgbWebPayChannelConfig() {
      apiGetCgbWebPayChannelConfig().then((res) => {
        const {
          data: { pictureGuideLinkAddress },
        } = res;
        this.pictureGuideLinkAddress = pictureGuideLinkAddress;

        console.log(this.pictureGuideLinkAddress);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.agent-pay {
  /*height: calc(100vh - 80px);*/
  .pay-header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
  }
  .pay-search {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    margin: 20px 0;
    .screen {
      position: relative;
      top: 1px;
    }
  }
  .operate-tips {
    margin-left: 300px;
    display: flex;
    align-items: flex-end;
    cursor: pointer;
    span {
      font-size: 14px;
      color: rgb(106, 127, 255);
    }
  }
  .pay-search > div {
    margin: 0 5px 5px 0;
  }
  // .pay-search > button {
  //   margin: 0 5px 5px 0;
  // }
  .pay-content {
    padding: 0 20px;
    margin: 20px 0;
  }
}
</style>
