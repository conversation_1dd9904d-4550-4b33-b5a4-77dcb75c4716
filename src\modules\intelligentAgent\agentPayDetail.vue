<template>
  <div class="agent-pay-detail def_per_height">
    <section class="header">
      <el-row type="flex" style="justify-content: space-between">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>代发付款详情</span>
        </el-col>
        <el-col v-if="server_env !== 'cgb'" :span="10" class="start-payroll">
          <el-button
            class="start-button"
            type="primary"
            @click="handleStartPayroll"
            >开始代发</el-button
          >
        </el-col>
      </el-row>
    </section>
    <section class="middle">
      <template v-for="(item, index) in headerTemp">
        <p :key="item.id">{{ item.name }}：{{ headerData[item.id] }}</p>
      </template>
    </section>
    <section class="table">
      <section class="table-filter">
        <el-input
          v-model="filterForm.xm"
          placeholder="请输入姓名查询"
          style="width: 180px; margin-right: 10px"
        ></el-input>
        <el-button @click="handleTableData" class="screen" type="primary"
          >查询</el-button
        >
      </section>
      <section class="table-content">
        <def-table
          :tableHeader="tableHeader"
          :tableData="tableData"
          @formatter="handleFormatter"
          @btnColumn="handleBtnColumn"
          @search="handleSearch"
          :total="total"
          :isShowIndex="true"
          :isHidePage="false"
        />
      </section>
    </section>
  </div>
</template>

<script>
import defTable from "./components/Table";
import { apiGetPaySalaryRecordList } from "./store/api";
import { payEnum } from "./util/constData";
export default {
  name: "agent-pay-detail",
  components: {
    defTable,
  },
  data() {
    return {
      server_env: window.env.server_env,
      tableHeight: document.body.clientHeight - 430 + "px",
      id: "",
      filterForm: {
        xm: "",
      },
      headerTemp:
        window.env.server_env === "cgb"
          ? [
              { id: "fxgsmc", name: "发薪公司名称" },
              { id: "khzh", name: "开户账号" },
              { id: "zje", name: "总金额" },
              { id: "zjsxf", name: "总计手续费" },
              { id: "zbs", name: "总笔数" },
              { id: "cgbs", name: "成功笔数" },
              { id: "sbbs", name: "失败笔数" },
              { id: "clzbs", name: "处理中笔数" },
            ]
          : [
              { id: "fxgsmc", name: "发薪公司名称" },
              { id: "zbs", name: "总笔数" },
              { id: "ffyh", name: "发放银行" },
              { id: "zje", name: "总金额（元）" },
              { id: "md5bm", name: "MD5编码" },
            ],
      headerData:
        window.env.server_env === "cgb"
          ? {
              fxgsmc: "",
              khzh: "",
              zjsxf: "",
              cgbs: "",
              sbbs: "",
              clzbs: "",
              zbs: "",
              ffyh: "",
              zje: "",
              ffyf: "",
              md5bm: "",
            }
          : {
              fxgsmc: "",
              zbs: "",
              ffyh: "",
              zje: "",
              ffyf: "",
              md5bm: "",
            },
      tableHeader:
        window.env.server_env === "cgb"
          ? [
              { prop: "xm", label: "姓名" },
              { prop: "zjhm", label: "证件号码" },
              { prop: "sjh", label: "手机号" },
              { prop: "yhkh", label: "银行卡号" },
              { prop: "khh", label: "开户行" },
              { prop: "lhh", label: "联行号" },
              { prop: "sfje", label: "实发金额（元）" },
              { prop: "sxf", label: "手续费" },
              { prop: "bz", label: "备注" },
              { prop: "fy", label: "附言" },
              { prop: "ddzt", label: "订单状态" },
              { prop: "fkxx", label: "反馈信息" },
            ]
          : [
              { prop: "xm", label: "姓名" },
              { prop: "zjhm", label: "证件号码" },
              { prop: "sjh", label: "手机号" },
              { prop: "yhkh", label: "银行卡号" },
              { prop: "khh", label: "开户行" },
              { prop: "sfje", label: "实发金额（元）" },
              { prop: "ddzt", label: "订单状态" },
              { prop: "bz", label: "备注" },
              {
                prop: "def_cz",
                label: "操作",
                width: "400px",
                btn: [
                  {
                    prop: "def_xzhd",
                    label: "下载回单",
                    type: "def_btn",
                    fun: "handleDownLoad",
                  },
                ],
              },
            ],
      tableData: [],
      total: null,
      limit: 10,
      start: 0,
      page: 1,
      channelData: {},
    };
  },
  created() {
    this.id = this.$route.query.id;
  },
  mounted() {
    this.handleInit();
  },
  methods: {
    handleInit() {
      this.handleTableData();
    },
    //查询列表数据
    handleTableData() {
      const { xm } = this.filterForm;
      apiGetPaySalaryRecordList({
        currPage: this.page,
        pageSize: this.limit,
        payBatchId: this.id,
        empName: xm,
      }).then((res) => {
        this.tableData = res.data.recordVoPageInfo.records;
        this.channelData = res.data.payChannelConfVo;
        this.total = res.data.recordVoPageInfo.total;
        this.$set(this.headerData, "fxgsmc", res.data.subjectName);
        this.$set(this.headerData, "zbs", res.data.totalCount);
        this.$set(this.headerData, "zje", res.data.totalAmount);
        this.$set(this.headerData, "md5bm", res.data.batchEncrypt);
        this.$set(
          this.headerData,
          "ffyh",
          res.data.payChannelConfVo.channelName
        );
        if (window.env.server_env === "cgb") {
          this.$set(this.headerData, "khzh", res.data.accountBankNo);
          this.$set(this.headerData, "zjsxf", res.data.totalFee);
          this.$set(this.headerData, "cgbs", res.data.successCount);
          this.$set(this.headerData, "sbbs", res.data.failCount);
          this.$set(this.headerData, "clzbs", res.data.processingCount);
          this.$set(this.headerData, "md5bm", res.data.batchEncrypt);
        }
      });
    },
    handleFormatter({ prop, data, btnItem }, callback) {
      if (prop == "def_cz") {
        let boo = false;
        switch (btnItem) {
          case "def_xzhd":
            boo =
              data["payStatus"] == "PAID" &&
              this.channelData.channelType == "API";
            callback(boo);
            break;
        }
      } else {
        switch (prop) {
          case "xm":
            callback(data["empName"]);
            break;
          case "zjhm":
            callback(data["idNo"]);
            break;
          case "sjh":
            callback(data["mobileNo"]);
            break;
          case "yhkh":
            callback(data["accountBankNo"]);
            break;
          case "khh":
            callback(data["accountBank"]);
            break;
          case "sfje":
            callback(data["realAmount"]);
            break;
          case "lhh":
            callback(data["accUnionBankNo"]);
            break;
          case "sxf":
            callback(data["fee"]);
            break;
          case "bz":
            callback(data["remark"]);
            break;
          case "fy":
            callback(data["transferTips"]);
            break;
          case "ddzt":
            callback(
              `<span style="color:${payEnum[data["payStatus"]].color}">${
                payEnum[data["payStatus"]].name
              }</span>`
            );
            break;

          case "fkxx":
            callback(data["errorInfo"]);
            break;
          case "bz":
            callback(data["errorInfo"]);
            break;
        }
      }
    },
    //处理操作按钮
    handleBtnColumn(val, type) {
      switch (type) {
        case "handleDownLoad":
          break;
      }
    },
    handleSearch({ limit, start, page }) {
      this.page = page;
      this.limit = limit;
      this.start = start;
      this.handleTableData();
    },
    // 开始代发
    handleStartPayroll() {
      const id = this.$route.query.id;
      this.$router.push({
        path: "/create-agent-file",
        query: { id },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.agent-pay-detail {
  .header {
    padding: 0 20px;
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
    .start-payroll {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .start-button {
        height: 40px;
      }
    }
  }
  .middle {
    display: flex;
    flex-wrap: wrap;
    line-height: 30px;
    background: rgb(241, 241, 241);
    margin: 20px;
    padding: 10px;
    border-radius: 4px;
    p {
      width: 50%;
    }
  }
  .screen {
    position: relative;
    top: 1px;
  }
  .table {
    padding: 0 20px;
    .table-content {
      margin: 20px 0;
    }
  }
}
</style>
