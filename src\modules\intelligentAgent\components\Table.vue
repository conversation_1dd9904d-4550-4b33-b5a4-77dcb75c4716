<template>
  <div class="temp-table">
    <el-table
      :data="tableData"
      stripe
      border
      :height="def_height"
      :header-cell-style="{ background: '#f1f1f1' }"
      @selection-change="handleSelectionChange"
      v-loading="loading"
      :header-cell-class-name="cellClass"
    >
      <el-table-column v-if="isShowSelection" type="selection" width="70">
      </el-table-column>
      <el-table-column v-if="isShowIndex" label="序号" type="index" width="50">
      </el-table-column>
      <template v-for="(item, index) in tableHeader">
        <template v-if="!item.btn">
          <el-table-column
            min-width="200"
            :width="item.width"
            :prop="item.prop"
            :label="item.label"
            :formatter="handleFormatter"
            :key="item.prop"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div>
                <span
                  v-html="handleFormatter(scope.row, { property: item.prop })"
                ></span>
              </div>
            </template>
          </el-table-column>
        </template>
        <template v-else>
          <el-table-column
            min-width="100"
            :width="item.width"
            :prop="item.prop"
            :label="item.label"
            :formatter="handleFormatter"
            :key="item.prop"
            :fixed="item.fixed"
          >
            <template slot-scope="scope">
              <template
                v-for="(btnItem, btnIndex) in filterData(
                  scope.row,
                  scope.column,
                  item.btn
                ).slice(0, 2)"
              >
                <!-- 自定义按钮 -->
                <template v-if="btnItem.type == 'def_btn'">
                  <el-link
                    v-show="
                      handleFormatter(scope.row, scope.column, btnItem.prop)
                    "
                    :key="btnItem.prop"
                    :type="btnItem.typeName || 'primary'"
                    :underline="false"
                    @click="handleBtnColumn(scope.row, btnItem.fun)"
                    :disabled="
                      btnItem.isDisabled && scope.row[btnItem.isDisabledSource]
                        ? true
                        : false
                    "
                  >
                    <!-- :style="(btnItem.isDisabled&&scope.row[btnItem.isDisabledSource])?'color:#ccc;cursor:not-allowed;':''" -->
                    {{ btnItem.label }}
                  </el-link>
                </template>
                <template v-else>
                  <el-link
                    :key="btnItem.prop"
                    :type="btnItem.typeName || 'primary'"
                    :underline="false"
                    @click="handleBtnColumn(scope.row, btnItem.fun)"
                    :disabled="
                      btnItem.isDisabled && scope.row[btnItem.isDisabledSource]
                        ? true
                        : false
                    "
                  >
                    {{ handleFormatter(scope.row, scope.column) }}
                  </el-link>
                </template>
              </template>
              <!-- 超出两个按钮以更多形式展示-->
              <template
                v-if="filterData(scope.row, scope.column, item.btn).length > 2"
              >
                <el-dropdown
                  trigger="click"
                  class="more-operation"
                  placement="bottom"
                >
                  <el-link type="primary" :underline="false">更多</el-link>
                  <el-dropdown-menu slot="dropdown">
                    <template
                      v-for="(btnItemGd, btnIndexGd) in filterData(
                        scope.row,
                        scope.column,
                        item.btn
                      ).slice(2)"
                    >
                      <el-dropdown-item
                        @click.native="
                          handleBtnColumn(scope.row, btnItemGd.fun)
                        "
                        :key="btnItemGd.prop"
                        :disabled="
                          btnItemGd.isDisabled &&
                          scope.row[btnItemGd.isDisabledSource]
                            ? true
                            : false
                        "
                      >
                        {{ btnItemGd.label }}
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </template>
          </el-table-column>
        </template>
      </template>
    </el-table>
    <section class="temp-table-pagination" v-if="!isHidePage">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 50]"
        :page-size="10"
        layout="total, sizes, prev, pager, next"
        :total="total"
        background
      >
      </el-pagination>
    </section>
  </div>
</template>

<script>
/**
 * search：分页查询
 * formatter：格式化
 * btnColumn：按钮操作
 */
export default {
  name: "temp-table",
  props: {
    tableHeader: {
      type: Array,
      default: [],
    },
    tableData: {
      type: Array,
      default: [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    total: {
      type: Number,
      default: null,
    },
    isShowIndex: {
      type: Boolean,
      default: false,
    },
    def_height: {
      type: String,
      default: "",
    },
    isHidePage: {
      type: Boolean,
      default: false,
    },
    isShowSelection: {
      // 是否展示复选框
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      page: 1,
      limit: 10,
      start: 0,
    };
  },
  methods: {
    cellClass(row) {
      // 判断第几列
      if (row.columnIndex === 0 && this.isShowSelection) {
        return "disableSelection";
      }
    },
    //分页-条数
    handleSizeChange(val) {
      this.limit = val;
      this.start = this.limit * (this.page - 1);
      this.$emit("search", {
        limit: this.limit,
        start: this.start,
        page: this.page,
      });
    },
    //分页-开始位置
    handleCurrentChange(val) {
      this.page = val;
      this.start = this.limit * (val - 1);
      this.$emit("search", {
        limit: this.limit,
        start: this.start,
        page: this.page,
      });
    },
    //格式化数据
    handleFormatter(row, column, btnItem) {
      let value = null;
      this.$emit(
        "formatter",
        { prop: column["property"], data: row, btnItem: btnItem },
        (val) => {
          value = val;
        }
      );
      return value;
    },
    //操作按钮
    handleBtnColumn(val, type) {
      this.$emit("btnColumn", val, type);
    },
    handleSelectionChange(val) {
      this.$emit("selectionChange", val);
    },
    filterData(row, column, val) {
      return val.filter((item) => {
        return this.handleFormatter(row, column, item.prop);
      });
    },
    // 未导入权限点
    handleDelete(val) {
      this.$emit("handleDelete", val);
    },
  },
};
</script>

<style lang="scss" scoped>
.temp-table {
  .el-link.el-link--primary {
    margin: 0 10px;
  }
  .el-link.el-link--danger {
    margin: 0 10px;
  }
  .temp-table-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

::v-deep.el-table .disableSelection .cell::before {
  content: "全选";
  position: absolute;
  right: 13px;
}
</style>
