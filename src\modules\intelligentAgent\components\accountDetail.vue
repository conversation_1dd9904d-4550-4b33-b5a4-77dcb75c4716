<template>
  <ul v-loading="loading" class="account-detail">
    <li v-for="item in finalList" :key="item.id">
      <p class="left">
        <span class="account-required">*</span>
        <span>{{ item.name }}：</span>
      </p>
      <p class="right">
        {{ item.id == "channelCode" ? formtChannel(item.value) : item.value }}
      </p>
    </li>
  </ul>
</template>

<script>
import { apiGetSubjectAccount } from "../store/api";
export default {
  data() {
    return {
      accountData: {},
      finalList: [
        {
          name: "通道名称",
          id: "channelCode",
          value: ""
        },
        {
          name: "公司名称",
          id: "subjectName",
          value: ""
        },
        {
          name: "统一社会信用代码",
          id: "unifiedCode",
          value: ""
        },
        {
          name: "开户行名称",
          id: "accountBank",
          value: ""
        },
        {
          name: "开户账号",
          id: "accountBankNo",
          value: ""
        },
        // {
        //   name: "基本户账户",
        //   id: "baseAccountBankNo",
        //   value: ""
        // },
        // {
        //   name: "基本户开户行",
        //   id: "baseAccountBank",
        //   value: ""
        // },
        {
          name: "经办人",
          id: "handler",
          value: ""
        },
        {
          name: "经办人手机号",
          id: "handlerMobile",
          value: ""
        }
      ],
      loading: false
    };
  },
  props: {
    subjectAccountId: {
      type: Number,
      default: 0
    },
    channelList: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    subjectAccountId(val) {
      this.getList(this.subjectAccountId);
    }
  },
  mounted() {
    this.getList(this.subjectAccountId);
  },
  methods: {
    getList(id) {
      this.loading = true
      apiGetSubjectAccount({
        subjectAccountId: id
      }).then(res => {
        if (res.success) {
          this.accountData = res.data;
          this.finalList.forEach(item => {
            item.value = this.accountData[item.id];
          });
          this.loading = false
        }
      });
    },
    // 格式化通道名称
    formtChannel(code) {
      if(!code) return;
      let list = this.channelList.filter(item => {
        return item.channelCode == code;
      });
      return list[0].channelName;
    },
    reset() {
      this.finalList.forEach(item => {item.value = ''})
    }
  }
};
</script>

<style lang="scss" scoped>
.account-detail {
  li {
    display: flex;
    justify-content: flex-start;
    line-height: 50px;
    .left {
      width: 40%;
      text-align: right;
      .account-required {
        color: #f56c6c;
      }
    }
    .right {
      width: 60%;
    }
  }
}
</style>
