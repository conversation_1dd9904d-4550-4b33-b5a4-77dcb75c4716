<template>
  <div>
    <el-form
      :model="agentAccountForm"
      :rules="agentAccountRules"
      ref="agentAccountForm"
      label-width="140px"
    >
      <el-form-item label="通道名称" prop="channelCode">
        <el-select
          placeholder="请选择"
          :disabled="editStatus"
          clearable
          v-model="agentAccountForm.channelCode"
          style="width: 100%"
        >
          <el-option
            v-for="item in channelList"
            :key="item.id"
            :label="item.channelName"
            :value="item.channelCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="公司名称" prop="subjectName" class="my-select">
        <el-select
          v-if="server_env === 'cgb'"
          placeholder="请选择发薪公司名称"
          clearable
          :disabled="editStatus"
          @change="changeSubjectName"
          v-model="agentAccountForm.subjectName"
          style="width: 100%"
          :title="agentAccountForm.subjectName"
          ref="subjectNameSelect"
          @visible-change="subjectNameVisible"
        >
          <el-option
            v-for="item in companyInfoList"
            :key="item.taxSubName"
            :label="item.taxSubName"
            :value="item.taxSubName"
          ></el-option>
        </el-select>
        <el-input
          v-else
          placeholder="请输入公司名称"
          v-model="agentAccountForm.subjectName"
        ></el-input>
      </el-form-item>
      <el-form-item label="统一社会信用代码" prop="unifiedCode">
        <el-input
          v-if="server_env === 'cgb'"
          v-model="agentAccountForm.unifiedCode"
          :disabled="true"
          placeholder="统一社会信用代码"
        ></el-input>
        <el-input
          v-else
          v-model="agentAccountForm.unifiedCode"
          placeholder="请输入统一社会信用代码（18至22位字符）"
        ></el-input>
      </el-form-item>
      <el-form-item label="开户行名称" prop="accountBank">
        <el-input
          placeholder="请输入开户行名称"
          v-model="agentAccountForm.accountBank"
        ></el-input>
      </el-form-item>
      <el-form-item label="开户账号" prop="accountBankNo">
        <el-input
          placeholder="请输入开户账号"
          v-model="agentAccountForm.accountBankNo"
        ></el-input>
      </el-form-item>
      <el-form-item v-if="server_env !== 'cgb'" label="经办人" prop="handler">
        <el-input
          placeholder="请输入经办人"
          v-model="agentAccountForm.handler"
        ></el-input>
      </el-form-item>
      <el-form-item v-if="server_env !== 'cgb'" label="经办人手机号" prop="handlerMobile">
        <el-input
          v-model="agentAccountForm.handlerMobile"
          placeholder="请输入经办人手机号"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <div
      v-if="server_env === 'cgb'"
      style="display: flex; justify-content: flex-end"
    >
      <el-button @click="cancel">取消</el-button>
      <el-button :loading="loading" type="primary" @click="next"
        >下一步</el-button
      >
    </div>
  </div>
</template>

<script>
import { validateTell } from "@/util/validate";
// import { isBank } from "../util/index";
import { validateBankId } from "@/util/validate";
// const validateLength = (_,value,callback) => {
//   if(value.length === 19) {
//     callback(new Error('统一社会信用代码长度为18或20位字符'))
//   } else {
//     callback()
//   }
// }
export default {
  data() {
    return {
      server_env: window.env.server_env,
      editStatus: false,
      loading: false,
      agentAccountForm: {
        accountBank: "",
        accountBankNo: "",
        baseAccountBank: "",
        baseAccountBankNo: "",
        channelCode: "",
        handler: "",
        handlerMobile: "",
        subjectName: "",
        unifiedCode: "",
      },
      agentAccountRules: {
        accountBank: [
          { required: true, message: "请选择", trigger: "change" },
          {
            min: 1,
            max: 50,
            message: "长度在 1 到 50 个字符",
            trigger: "blur",
          },
        ],
        accountBankNo: [
          { required: true, validator: validateBankId, trigger: "blur" },
        ],
        baseAccountBank: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
        baseAccountBankNo: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
        channelCode: [{ required: true, message: "请选择", trigger: "blur" }],
        handler: [
          { required: true, message: "请输入", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "长度在 1 到 20 个字符",
            trigger: "blur",
          },
        ],
        handlerMobile: [
          { required: true, message: "请输入", trigger: "blur" },
          {
            validator: validateTell,
            message: "请输入正确手机号",
            trigger: "blur",
          },
        ],
        subjectName: [
          {
            required: true,
            message: window.env.server_env === "cgb" ? "请选择" : "请输入",
            trigger: "blur",
          },
          window.env.server_env === "cgb"
            ? {}
            : {
                min: 1,
                max: 50,
                message: "长度在 1 到 50 个字符",
                trigger: "blur",
              },
        ],
        unifiedCode: [
          {
            required: true,
            message: window.env.server_env === "cgb" ? "请先选择公司名称" : "请输入",
            trigger: "blur",
          },
          window.env.server_env === "cgb"
            ? {}
            : {
                min: 18,
                max: 22,
                message: "统一社会信用代码长度为18至22位字符",
                trigger: "blur",
              },
        ],
      },
    };
  },
  props: {
    channelList: {
      type: Array,
      default: () => [],
    },
    companyInfoList: {
      type: Array,
      default: () => [],
    },
  },

  methods: {
    editAccount(data) {
      if (data) {
        this.editStatus = true;
        this.agentAccountForm = JSON.parse(JSON.stringify(data));
      } else {
        this.editStatus = false;
        this.agentAccountForm = {
          accountBank: "",
          accountBankNo: "",
          baseAccountBank: "",
          baseAccountBankNo: "",
          channelCode: "",
          handler: "",
          handlerMobile: "",
          subjectName: "",
          unifiedCode: "",
        };
      }
    },
    changeSubjectName() {
      for (var i = 0; i < this.companyInfoList.length; i++) {
        if (!this.agentAccountForm.subjectName) {
          this.agentAccountForm.unifiedCode = "";
          return;
        }
        if (
          this.agentAccountForm.subjectName ===
          this.companyInfoList[i].taxSubName
        ) {
          this.agentAccountForm.unifiedCode =
            this.companyInfoList[i].taxPayerNo;
        }
      }
    },
    subjectNameVisible(data) {
      data == false ? this.$refs.subjectNameSelect.blur() : "";
    },
    cancel() {
      this.$emit("cancel");
    },
    next() {
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
      }, 1000);
      this.$emit("precheck");
    },
  },
};
</script>

<style>
.my-select .el-input__inner {
  padding: 0 20px 0 10px;
  border: 1px solid #dcdfe6;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: normal;
  white-space: nowrap;
}
</style>
