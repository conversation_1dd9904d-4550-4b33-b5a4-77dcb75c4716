<template>
  <div>
    <div style="display: flex; justify-content: center">
      <el-form
        :model="keyboarderForm"
        :rules="rules"
        ref="keyboarderForm"
        label-width="140px"
      >
        <el-form-item
          label="录入员手机号："
          prop="phoneNumber"
          style="width: 400px"
        >
          <el-input
            placeholder="请输入手机号"
            v-model="keyboarderForm.phoneNumber"
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="verifyCode"
          label="图形验证码："
          class="sendmessage-content"
          style="width: 400px"
        >
          <div style="display: flex">
            <el-input
              v-model="keyboarderForm.verifyCode"
              placeholder="请输入图形验证码"
              maxlength="4"
            >
              <template #suffix>
                <div>
                  <img
                    style="width: 70px; height: 37px; margin: 3px -2px 0px 0px"
                    v-if="keyboarderForm.verifyCodeId"
                    @click="handleResetCodeImage"
                    slot="suffix"
                    :src="`${baseUrls}/api/merchant/platform/captcha?token=${encodeURIComponent(
                      keyboarderForm.verifyCodeId
                    )}`"
                    alt
                  />
                </div>
              </template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="验证码：" prop="smsCode" style="width: 400px">
          <div style="display: flex">
            <el-input
              placeholder="请输入验证码"
              v-model="keyboarderForm.smsCode"
              maxlength="6"
            >
              <el-button
                type="text"
                :disabled="sendCode"
                @click="getVerifyCode"
                slot="suffix"
              >
                <span v-show="show">获取验证码</span>
                <span v-show="!show">{{ count }}s后重发</span>
              </el-button>
            </el-input>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div style="display: flex; justify-content: flex-end; margin-top: 20px">
      <el-button @click="lastStep">上一步</el-button>
      <el-button :loading="confirmLoading" type="primary" @click="confirm"
        >确 定</el-button
      >
    </div>
  </div>
</template>

<script>
const TIME_COUNT = 60;
import { baseUrls } from "../../../request/fetch";
import { validateTel } from "assets/js/utils/validate";
import { apiCodeSms, apiCreateCodeImage } from "../store/api";
export default {
  props: {},
  data() {
    return {
      keyboarderForm: {
        phoneNumber: "",
        smsCode: "",
        verifyCode: "",
        verifyCodeId: "",
        verifyCodeSend: "",
      },
      baseUrls,
      show: true,
      count: "",
      sendCode: false,
      confirmLoading: false,
      rules: {
        smsCode: [
          {
            required: true,
            message: "请输入验证码",
            trigger: "blur",
          },
        ],
        verifyCode: [
          { required: true, message: "请输入图形验证码", trigger: "blur" },
        ],
        phoneNumber: [
          {
            required: true,
            message: "请输入手机号",
            trigger: "blur",
          },
          {
            validator: validateTel,
            message: "请输入正确手机号",
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    "keyboarderForm.verifyCode": {
      handler(val) {
        if (val.length === 4) {
          localStorage.setItem("__captchaAnswer", val);
        }
      },
      deep: true,
    },
  },
  created() {
    localStorage.removeItem("__captchaAnswer");
    localStorage.removeItem("__captchaToken");
    this.handleResetCodeImage();
  },
  methods: {
    handleResetCodeImage() {
      const t = this;
      apiCreateCodeImage({ captchaType: "NUMBER_4_1" }).then((res) => {
        t.keyboarderForm.verifyCodeId = res.data;
        localStorage.setItem("__captchaToken", res.data);
      });
    },
    confirm() {
      this.confirmLoading = true;
      this.$refs.keyboarderForm.validate((valid) => {
        if (valid) {
          if (!this.keyboarderForm.verifyCodeSend) {
            this.$message.error("请获取验证码");
            return;
          }
          setTimeout(() => {
            this.confirmLoading = false;
          }, 1000);
          this.keyboarderForm.password = this.keyboarderForm.smsCode;
          this.$emit("keyboarderConfirm", this.keyboarderForm);
        }
      });
    },
    lastStep() {
      this.$emit("keyboarderLastStep");
    },
    getVerifyCode() {
      if (!this.keyboarderForm.phoneNumber) {
        this.$message.error("请先输入手机号");
        return;
      }
      //获取短信验证码
      const captchaToken = localStorage.getItem("__captchaToken");
      const captchaAnswer = localStorage.getItem("__captchaAnswer");
      if (!captchaToken) {
        this.$message.error("请重置图形验证码");
        return;
      }
      if (!captchaAnswer) {
        this.$message.error("请先填写正确的图形验证码");
        return;
      }
      this.sendCode = true;
      apiCodeSms({
        otpType: "SMS",
        receiver: this.keyboarderForm.phoneNumber,
        captchaToken,
        captchaAnswer,
      })
        .then((res) => {
          if (res.success) {
            this.keyboarderForm.verifyCodeSend = res.data.token;
            this.send();
          } else {
            this.sendCode = true;
          }
        })
        .finally(() => {
          localStorage.removeItem("__captchaToken");
          localStorage.removeItem("__captchaAnswer");
        });
    },
    send() {
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.show = false;
        this.sendCode = true;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.sendCode = false;
            this.show = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
    },
  },
};
</script>

<style>
</style>