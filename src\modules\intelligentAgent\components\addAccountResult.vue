<template>
  <div>
    <div style="display: flex; justify-content: center">
      <div
        style="
          width: 500px;
          height: 121px;
          background-color: #f2f2f2;
          margin: 60px 0;
        "
      >
        <div
          style="display: flex; justify-content: center"
          :style="styleResult"
        >
          <el-image
            style="width: 30px; height: 30px; margin-right: 10px"
            :src="
              this.addAccountResult.addAccountStatus === 'SUCCESS'
                ? require('../../../assets/images/cgbResultSuccess.png')
                : require('../../../assets/images/cgbResultFail.png')
            "
          />
          <h3 style="margin-top: 3px">
            {{ this.addAccountResult.title }}
          </h3>
        </div>
        <div v-if="this.addAccountResult.addAccountStatus === 'FAIL'">
          <p style="text-align: center; margin: 10px 0">
            {{ this.addAccountResult.errorMessage }}
          </p>
        </div>
        <div v-if="this.addAccountResult.addAccountStatus === 'SUCCESS'">
          <div style="width: 300px; margin: 5px 0px 10px 175px">
            <p class="account-result-class">
              账户类型：
              {{ this.addAccountResult.type === "BASIC" ? "基本户" : "一般户" }}
            </p>
            <p class="account-result-class">
              录入员信息：{{ this.addAccountResult.message }}
            </p>
            <p class="account-result-class">
              录入员手机号：{{ this.addAccountResult.phoneNumber }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <div style="display: flex; justify-content: flex-end">
      <el-button
        v-if="this.addAccountResult.addAccountStatus === 'SUCCESS'"
        plain
        @click="resultHandle"
        >关闭
      </el-button>
      <el-button
        v-if="this.addAccountResult.addAccountStatus === 'FAIL'"
        type="primary"
        @click="resultHandle"
      >
        确定
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    addAccountResult: {
      type: Object,
      default: {},
    },
  },
  computed: {
    styleResult() {
      return {
        "margin-top":
          this.addAccountResult.addAccountStatus === "SUCCESS"
            ? "10px"
            : "20px",
      };
    },
  },
  methods: {
    resultHandle() {
      this.$emit("resultHandle");
    },
  },
};
</script>

<style>
.account-result-class {
  width: 300px;
  text-align: left;
  margin-top: 2px;
}
</style>