<template>
  <el-dialog
    class="import-page"
    title="新增代发"
    :visible.sync="isShow"
    width="800px"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <div class="basic-info">
        <el-form
          :model="payrollForm"
          :rules="rules"
          ref="basicInfoForm"
          label-width="110px"
          class="demo-ruleForm"
        >
          <div>
            <el-form-item label="发薪公司名称" prop="subjectName">
              <el-select
                v-if="server_env === 'cgb'"
                v-model="payrollForm.subjectName"
                placeholder="请选择发薪公司"
                clearable
                @change="handleSelectChange"
              >
                <el-option
                  v-for="(item, index) in companyData"
                  :key="item.taxPayerNo"
                  :label="item.taxSubName"
                  :value="item.taxPayerNo"
                ></el-option>
              </el-select>
              <el-select
                v-else
                v-model="payrollForm.subjectName"
                placeholder="请选择发薪公司"
                value-key="unifiedCode"
                clearable
                @change="handleSelectChange"
              >
                <el-option
                  v-for="(item, index) in companyData"
                  :key="item.unifiedCode"
                  :label="item.subjectName"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="工资月份" prop="salaryPaymentMonth">
              <el-date-picker
                v-model="payrollForm.salaryPaymentMonth"
                type="month"
                value-format="yyyyMM"
                placeholder="选择月"
              >
              </el-date-picker>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="upload-con">
        <el-upload
          drag
          class="upload-demo"
          action=""
          :http-request="uploadFile"
          :before-upload="beforeUpload"
          :on-change="handleChange"
          :on-remove="handleMove"
          :file-list="fileList"
          :disabled="uploadStatus"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">拖拽文件到这里上传</div>
          <div class="el-upload__text">
            <el-button type="primary" size="mini" style="margin-top: 10px">{{
              isUploadFile ? "重新上传" : "选择文件"
            }}</el-button>
            <div class="upload-tip">
              仅支持格式为xls、xlsx的文件，且文件大小不超过5Mb
            </div>
            <div class="upload-template" @click="downloadTemplate">
              如有需要也可使用系统模板&nbsp;&nbsp;
              <span style="color: #4f71ff">
                <span class="olading-iconfont oi-download"></span>下载模板
              </span>
            </div>
          </div>
        </el-upload>
      </div>

      <div class="tip-con" v-show="errorFile">
        <i class="el-icon-circle-close"></i>部分数据校验通过，有<span
          class="red"
          >{{ wrongCount }}</span
        >条数据错误，请检查
        <a class="a-red" :href="errorFile"> 下载错误文件</a>
      </div>

      <div class="tip-con">
        <p>上传说明：</p>
        <p>
          工资表数据项必须包含【姓名】【手机号】【证件号码】【银行卡号】【实发金额】；
        </p>
      </div>
      <el-dialog
        append-to-body
        title="系统匹配字段"
        class="import-page"
        :visible.sync="dialogVisible"
        width="800px"
        :before-close="handleChildClose"
      >
        <div class="field-table">
          <el-table
            border
            :data="tableData"
            :header-cell-style="{ background: '#F1F1F1' }"
          >
            <el-table-column
              label="系统匹配字段"
              prop="key"
              min-width="250"
              align="center"
            >
              <template slot-scope="scope">
                <span v-show="scope.row.required" class="is-required">*</span>
                <span>{{ scope.row.key }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="表格字段"
              prop="value"
              min-width="250"
              align="center"
            >
              <template slot-scope="scope">
                <el-select v-model="scope.row.value" clearable>
                  <el-option
                    v-for="(it, index) in systemData"
                    :key="index"
                    :label="it"
                    :value="it"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="is-remember">
          <el-checkbox v-model="autoMatch"
            >记住匹配字段，下次自动匹配</el-checkbox
          >
        </div>
        <span slot="footer" class="foot" style="flort: right">
          <el-button @click="handleChildClose">上一步</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </el-dialog>
      <span slot="footer" class="dialog-footer foot" style="flort: right">
        <el-button @click="cancelBtn">取 消</el-button>
        <el-button :disabled="toNext" type="primary" @click="debounce('next')"
          >确 定</el-button
        >
      </span>
    </div>
  </el-dialog>
</template>

<script>
import {
  apiDownPayrollTemplate,
  // apiUploadTemplate,
  // apiConfirmMappings,
  apiSaveSalaryBatch,
  apiListSalaryBatch,
} from "../../payroll/api";

import {
  getEnableSubjectList,
  apiUploadTemplate,
  saveFieldMapping,
  apiDownloadApplyBatchTemplat,
  savePaySalaryApplyBatch,
  apiQuerySubject,
  apiDownLoadPaySalaryApplyFail,
} from "../store/api";

import { mapState } from "vuex";

const init = () => {
  return {
    subjectName: "",
    subjectNameSelect: "",
    unifiedCode: "",
    salaryPaymentMonth: "",
  };
};

export default {
  data() {
    return {
      server_env: window.env.server_env,
      isShow: false,
      dialogVisible: false,
      loading: false,
      toNext: false,
      isMatching: false,
      fileList: [],
      fileData: [],
      companyData: [],
      taskNo: "",
      errorFile: null,
      wrongCount: 0,
      formData: null, //form对象
      uploadFileName: "", //上传文件名称
      payrollForm: init(),
      autoMatch: true, //记住匹配
      tableData: [
        { key: "姓名", value: "", required: true },
        { key: "工资卡号", value: "", required: true },
        { key: "工资卡银行", value: "", required: false },
        { key: "实发金额", value: "", required: true },
        { key: "手机号", value: "", required: true },
        { key: "证件号码", value: "", required: true },
      ],
      systemData: [], //系统表格字段
      mappings: {},
      fileId: "",
      options: [],
      momentTable: [], // 暂存匹配字段
      rules: {
        subjectName: [
          {
            required: true,
            message: "请选择发薪公司",
            trigger: ["blur", "change"],
          },
        ],
        salaryPaymentMonth: [
          { required: true, message: "请选择月份", trigger: "change" },
        ],
      },
      isUploadFile: false,
      uploadStatus: false,
    };
  },
  // watch: {
  //   "payrollForm.salaryPaymentMonth"(val) {
  //     if (!this.isShowUpload) {
  //       this.payrollForm.name = "";
  //       this.getOptions(val);
  //     }
  //   },
  // },
  computed: {
    ...mapState("payrollStore", {
      payParams: "payParams",
    }),
  },
  created() {
    console.log("alaryPaymentMonth", this.payrollForm.salaryPaymentMonth);
    this.getYearMonth();
  },
  mounted() {
    this.getCompanyList();
    if (window.env.server_env === "cgb") {
      this.tableData.push(
        { key: "联行号", value: "", required: false },
        { key: "备注", value: "", required: false },
        { key: "附言", value: "", required: false }
      );
    }
  },
  methods: {
    show() {
      this.isShow = true;
    },

    // 公司主体列表
    getCompanyList() {
      if (window.env.server_env === "cgb") {
        let data = {
          type: "TAX",
          authSuccess: true,
        };
        apiQuerySubject(data).then((res) => {
          if (res.success) {
            this.companyData = res.data;
          }
        });
      } else {
        getEnableSubjectList().then((res) => {
          if (res.success) {
            this.companyData = res.data;
          }
        });
      }
    },

    getOptions(val) {
      apiListSalaryBatch({ date: val }).then((res) => {
        if (res.success) {
          this.options = res.data;
        }
      });
    },

    handleSelectChange(item) {
      if (window.env.server_env === "cgb") {
        if (item) {
          this.payrollForm.unifiedCode = item;
          for (var i = 0; i < this.companyData.length; i++) {
            if (item === this.companyData[i].taxPayerNo) {
              this.payrollForm.subjectNameSelect =
                this.companyData[i].taxSubName;
            }
          }
        }
      } else {
        if (item) {
          this.payrollForm.subjectName = item.subjectName;
          this.payrollForm.unifiedCode = item.unifiedCode;
        }
      }
    },

    getYearMonth() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth();
      const perch = month + 1 < 10 ? "0" : "";
      this.payrollForm.salaryPaymentMonth = year + perch + (month + 1);
      console.log(
        "this.payrollForm.salaryPaymentMonth",
        this.payrollForm.salaryPaymentMonth
      );
    },
    //上传模板
    async uploadFile(params) {
      this.formData = new FormData();
      this.formData.append("file", params.file);
      this.loading = true;
      apiUploadTemplate(this.formData).then((res) => {
        console.log("done!!!!");
        this.loading = false;
        if (res.success) {
          this.tableData = this.tableData.map((val) => {
            val.value = res.data.mappings[val.key] || "";
            return val;
          });

          this.momentTable = JSON.parse(JSON.stringify(this.tableData));
          this.systemData = res.data.titles.map((i) => i.trim());
          this.fileId = res.data.fileId;
          this.mappings = res.data.mappings;
          this.isMatching = false;
          this.message = "";
          this.$refs["basicInfoForm"].validate((valid) => {
            if (valid) {
              this.dialogVisible = true;
            } else {
              this.$nextTick(() => {
                this.errorScroll(
                  document.querySelectorAll("div.el-form-item__error")
                );
              });
            }
          });
        } else {
          this.message = res.message;
        }
      });
    },
    beforeUpload(file) {
      this.uploadFileName = file.name;
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isxls = testmsg === "xls" || testmsg === "xlsx";
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isxls) {
        this.$message.warning("上传文件类型只能是 xls,xlsx 格式!");
        this.fileList = [];
        return false;
      }
      if (!isLt5M) {
        this.$message.warning("上传文件大小不能超过 5MB!");
        this.fileList = [];
        return false;
      }
    },

    handleMove() {
      this.fileList = [];
      this.isUploadFile = false;
    },
    handleChange(file, fileList) {
      this.fileList = [file];
      this.isUploadFile = true;
    },
    //模板下载
    async downloadTemplate() {
      this.uploadStatus = true;
      const { status } = await apiDownloadApplyBatchTemplat();
      if (status) {
        this.uploadStatus = false;
      }
    },

    // 防止重复提交
    debounce(fn) {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(() => {
        this[fn]();
      }, 200);
    },

    //下一步
    next() {
      this.$refs["basicInfoForm"].validate((valid) => {
        if (valid) {
          if (!this.fileList.length) {
            this.$message.error("请上传文件");
            return;
          }
          if (this.message) return this.$message.error(this.message);

          if (!this.isMatching) {
            this.dialogVisible = true;
            return;
          }

          this.handleSave();
        } else {
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    //确认提交
    handleSave() {
      this.loading = true;
      let param = {};
      if (window.env.server_env === "cgb") {
        param = {
          mappings: this.mappings,
          subjectName: this.payrollForm.subjectNameSelect,
          unifiedCode: this.payrollForm.unifiedCode,
          salaryPaymentMonth: this.payrollForm.salaryPaymentMonth,
        };
      } else {
        param = {
          mappings: this.mappings,
          ...this.payrollForm,
        };
      }

      this.formData.append("param", JSON.stringify(param));
      savePaySalaryApplyBatch(this.formData).then((res) => {
        this.dialogVisible = false;
        this.loading = false;

        console.log("res", res);
        if (res.success) {
          if (res.data.ok) {
            this.toNext = false;
            this.formData = null;
            this.handleClose();
            this.$emit("getList");
          } else {
            this.toNext = true;
            this.errorFile = res.data.errorFile;
            this.wrongCount = res.data.wrongCount;
          }
        } else {
          this.formData = null;
          this.message = res.message;
        }
      });
    },

    confirm() {
      let map = {};
      let duplicateMappingName = [];
      let emptyArray = [];
      let errArray = [];

      const arr = this.tableData;
      for (let i = 0; i < arr.length; i++) {
        console.log("i", arr[i].key);
        if (window.env.server_env === "cgb") {
          if (
            arr[i].value == "" &&
            arr[i].key !== "工资卡银行" &&
            arr[i].key !== "联行号" &&
            arr[i].key !== "备注" &&
            arr[i].key !== "附言"
          ) {
            emptyArray.push(arr[i].key);
          }
        } else {
          if (arr[i].value == "" && arr[i].key !== "工资卡银行") {
            emptyArray.push(arr[i].key);
          }
        }

        if (
          arr[i].value != "" &&
          arr[i].key != arr[i].value &&
          this.systemData.indexOf(arr[i].key) !== -1
        ) {
          errArray.push(arr[i].key);
        }

        map[arr[i].key] = arr[i].value;
        for (let j = i + 1; j < arr.length; j++) {
          if (arr[i].value && arr[i].value === arr[j].value) {
            duplicateMappingName.push(arr[j].key);
          }
        }
      }

      if (emptyArray.length > 0) {
        emptyArray = emptyArray.map((i) => "“" + i + "”");
        return this.$message.error("请选择" + emptyArray.join("、") + "匹配项");
      }

      if (duplicateMappingName.length > 0) {
        duplicateMappingName = duplicateMappingName.map((i) => "“" + i + "”");
        return this.$message.error(
          duplicateMappingName.join("、") +
            "系统字段与表格字段匹配重复，请检查并重新匹配"
        );
      }

      if (errArray.length > 0) {
        errArray = errArray.map((i) => "“" + i + "”");
        return this.$message.error(
          errArray.join("、") + "系统字段必须与表格同名字段匹配"
        );
      }

      this.autoMatch && this.saveMappings(map);
      this.mappings = JSON.parse(JSON.stringify(map));

      this.dialogVisible = false;
      this.isMatching = true;
      this.handleSave();
    },

    saveMappings(map) {
      saveFieldMapping({ mappings: map }).then((res) => {
        if (res.success) {
          this.mappings = JSON.parse(JSON.stringify(map));
        }
      });
    },

    handleChildClose() {
      this.dialogVisible = false;
    },
    handleClose() {
      this.handleMove();
      this.toNext = false;
      this.errorFile = null;
      this.$refs["basicInfoForm"].resetFields();
      this.isShow = false;
    },
    cancelBtn() {
      this.handleClose();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/scss/helpers.scss";
.import-page {
  // height: calc(100vh - 80px);
  .tip-con {
    width: 640px;
    margin: 10px auto 0 auto;
    padding-left: 130px;
    p {
      margin-bottom: 10px;
      font-size: 13px;
    }
    span {
      margin: 0 5px;
    }
    .red {
      color: #c81930;
    }
    .red {
      color: $mainColor;
    }
    .el-icon-circle-close {
      font-size: 16px;
      margin-right: 8px;
    }
    .a-red {
      cursor: pointer;
      font-weight: 500;
    }
    .el-button {
      padding: 0;
    }
  }
  .upload-con {
    width: 100%;
    .upload-demo {
      width: 500px;
      margin: 0 auto;
      position: relative;
      /deep/ .el-upload-dragger {
        width: 500px;
        .el-icon-upload {
          margin-top: 20px;
        }
      }
      .upload-template {
        position: absolute;
        bottom: 10px;
        left: 80px;
        z-index: 999;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 72px;
          height: 16px;
        }
      }
    }
    .upload-tip {
      padding-top: 10px;
    }
  }
  /deep/.el-steps {
    margin: 20px auto;
  }
  .basic-info {
    width: 500px;
    margin: 0 auto;
    .el-date-editor.el-input {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .buttonCon {
    text-align: center;
    position: fixed;
    bottom: 0;
    width: 100%;
    background: #fff;
    padding: 20px 0px;
    border-top: 1px solid #f3f3f3;
    .el-button:first-child {
      margin-left: -285px;
    }
    .el-button--primary {
      font-size: 12px;
    }
  }
  .is-remember {
    margin-top: 20px;
  }
  .is-required {
    color: #f56c6c;
    content: "*";
  }
  /deep/ .el-upload-list__item.is-ready,
  .el-upload-list__item.is-uploading {
    display: block;
  }
}
/deep/.el-upload-dragger {
  height: 210px;
}
.foot {
  display: flex;
  justify-content: flex-end;
}
</style>
