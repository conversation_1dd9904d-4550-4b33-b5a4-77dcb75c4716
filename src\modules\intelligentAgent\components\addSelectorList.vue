<template>
  <div>
    <el-table
      class="tableClass"
      :data="issuingList"
      max-height="300"
      border
      :header-cell-style="{ background: '#f2f2f2' }"
      @row-click="showRow"
      style="width: 100%"
    >
      <el-table-column label="请选择" width="101">
        <template slot-scope="scope">
          <el-radio v-model="selectRadio" :label="scope.$index"
            >&nbsp;</el-radio
          >
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="用户姓名" width="240">
      </el-table-column>
      <el-table-column prop="webUserName" label="网银登录用户名" width="240">
      </el-table-column>
    </el-table>
    <div style="display: flex; justify-content: flex-end; margin-top: 30px">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="next" :disabled="toNext"
        >下一步</el-button
      >
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectRadio: "",
      toNext: true,
    };
  },
  watch: {
    selectRadio(id) {
      if (id >= 0) {
        this.toNext = false;
      }
    },
  },
  props: {
    issuingList: {
      type: Array,
      default: () => {},
    },
  },
  methods: {
    cancel() {
      this.$emit("cancel");
    },
    next() {
      if (!this.selectRadio && this.selectRadio !== 0) {
        this.$message.error("请选择录入员");
        return;
      }
      this.$emit("issuingNext", this.issuingList[this.selectRadio]);
    },
    showRow(row) {
      this.selectRadio = this.issuingList.indexOf(row);
    },
  },
};
</script>

<style>
</style>