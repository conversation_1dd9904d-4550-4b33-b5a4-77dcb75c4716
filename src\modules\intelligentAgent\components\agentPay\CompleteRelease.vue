<template>
  <div class="complete-release">
    <section class="body-content">
      <p>经办人姓名：{{handlerMsg.handler}}</p>
      <p>经办人手机号：{{handlerMsg.handlerMobile}}</p>
      <p>
        <el-input
          size="medium"
          placeholder="请输入验证码"
          v-model="yzm"
          style="width:180px"
          type="number"
          @input="handleInput"
        >
        </el-input>
        <el-button 
          type="primary" size="medium" 
          @click="handleSend" 
          :loading="isLoading" 
          :disabled="isDisabled"
        >{{btnVal}}</el-button>
      </p>
    </section>
  </div>
</template>

<script>
import { apiSmsSend,apiSmsVerify } from '../../store/api'
export default {
  name:"complete-release",
  props:["handlerMsg"],
  data(){
    return {
      yzm:"",
      code:"",
      btnVal:"获取验证码",
      isLoading:false,
      isDisabled:false,
      smsTime:null,
    }
  },
  methods:{
    handleInput(val){
      if(val.length>5){
        this.yzm=val.slice(0,6)
      }
    },
    handleSubmitForm(formName) {
      return new Promise((resolve,reject)=>{
        Promise.all([this.handleSmsVerify()]).then(res=>{
          resolve('true')
        })
      })
    },
    //按钮状态
    handleBtnChange({isLoading,btnVal,isDisabled}){
      this.isLoading = isLoading
      this.btnVal = btnVal
      this.isDisabled = isDisabled
    },
    //验证码计时器
    handleSmsTime(){
      let second = 60;
      let siv = setInterval(() => {
        this.smsTime = second;
        if (second < 0) {
          clearInterval(siv);
          this.handleBtnChange({btnVal:"重新发送",isLoading:false,isDisabled:false})
        }else{
          this.handleBtnChange({btnVal:`${second--}s`,isLoading:false,isDisabled:true})
        }
      }, 1000);
    },
    //下发短信
    handleSend(){
      this.handleBtnChange({btnVal:"发送中",isLoading:true,isDisabled:false})
      apiSmsSend({
        receiver:this.handlerMsg.handlerMobileTrue
      }).then(res=>{
        if(res.success){
          this.code = res.data.code
          this.$message({
            showClose: true,
            message: '发送成功',
            type: 'success'
          });
          this.handleSmsTime()
        }
        console.log(res)
      })
    },
    //短验
    handleSmsVerify(){
      return new Promise((resolve,reject)=>{
        apiSmsVerify({challenge:this.yzm,code:this.code,}).then(res=>{
          if(res.success){
            resolve('true')
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.complete-release{
  display: flex;
  justify-content: center;
  .body-content{
    p{
      margin:20px 0;
    }
  }
}
</style>