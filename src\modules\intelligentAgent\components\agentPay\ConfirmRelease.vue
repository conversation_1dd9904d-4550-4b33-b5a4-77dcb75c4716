<template>
  <div class="confirm-release">
    <section class="body">
      <!-- <el-form label-position="left" :model="ruleForm" :rules="rules" ref="ruleForm" label-width="" class="demo-ruleForm">
        <el-form-item label="是否预约发放" prop="sfyyff">
          <el-radio-group v-model="ruleForm.sfyyff">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="预约发放日期" prop="yyffrq">
          <el-date-picker
            v-model="ruleForm.yyffrq"
            type="date"
            placeholder="请选择预约发放日期"
            style="width:188px"
            >
          </el-date-picker>
        </el-form-item>
      </el-form> -->
      <section class="body-footer">
        <p>经办人姓名：{{handlerMsg.handler}}</p>
        <p>经办人手机号：{{handlerMsg.handlerMobile}}</p>
        <p>
          <el-input
            size="medium"
            placeholder="请输入验证码"
            v-model="yzm"
            style="width:180px"
            type="number"
            @input="handleInput"
          >
          </el-input>
          <el-button 
            type="primary" size="medium" 
            @click="handleSend" 
            :loading="isLoading" 
            :disabled="isDisabled"
          >{{btnVal}}</el-button>
        </p>
    </section>
    </section>
    
  </div>
</template>

<script>
import { apiSmsSend,apiSmsVerify } from '../../store/api'

export default {
  name:"confirm-release",
  props:["handlerMsg"],
  data(){
    return {
      yzm:"",
      code:"",
      ruleForm: {
        sfyyff:false,
        yyffrq:"",
      },
      rules: {
        sfyyff: [
          { required: true, message: '请选择是否预约发放', trigger: 'change' }
        ],
        yyffrq: [
          { required: true, message: '请选择预约发放日期', trigger: 'change' }
        ],
      },
      btnVal:"获取验证码",
      isLoading:false,
      isDisabled:false,
      smsTime:null,
    }
  },
  
  methods:{
    handleInput(val){
      if(val.length>5){
        this.yzm=val.slice(0,6)
      }
    },
    handleSubmitForm(formName) {
      return new Promise((resolve,reject)=>{
        Promise.all([this.handleSmsVerify()]).then(res=>{
          resolve('true')
        })
        // this.$refs["ruleForm"].validate((valid) => {
        //   if (valid) {
        //     resolve('true')
        //   } else {
        //     resolve('false')
        //   }
        // });
      })
    },
    //按钮状态
    handleBtnChange({isLoading,btnVal,isDisabled}){
      this.isLoading = isLoading
      this.btnVal = btnVal
      this.isDisabled = isDisabled
    },
    //验证码计时器
    handleSmsTime(){
      let second = 60;
      let siv = setInterval(() => {
        this.smsTime = second;
        if (second < 0) {
          clearInterval(siv);
          this.handleBtnChange({btnVal:"重新发送",isLoading:false,isDisabled:false})
        }else{
          this.handleBtnChange({btnVal:`${second--}s`,isLoading:false,isDisabled:true})
        }
      }, 1000);
    },
    //下发短信
    handleSend(){
      this.handleBtnChange({btnVal:"发送中",isLoading:true,isDisabled:false})
      apiSmsSend({
        receiver:this.handlerMsg.handlerMobileTrue
      }).then(res=>{
        if(res.success){
          this.code = res.data.code
          this.$message({
            showClose: true,
            message: '发送成功',
            type: 'success'
          });
          this.handleSmsTime()
        }
        console.log(res)
      })
    },
    //短验
    handleSmsVerify(){
      return new Promise((resolve,reject)=>{
        apiSmsVerify({challenge:this.yzm,code:this.code,}).then(res=>{
          if(res.success){
            resolve('true')
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.confirm-release{
  display: flex;
  justify-content: center;
  .body{
    .body-footer{
      p{
        margin:20px 0;
      }
    }
  }
}
</style>