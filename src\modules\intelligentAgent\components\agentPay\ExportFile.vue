<template>
  <div class="export-file">
    <section class="head" v-if="type=='boc'">
      1、每个代发付款批次都有唯一识别码，导出的发放文件根据唯一识别码生成；
      </br>
      2、导出发放文件后，代发付款批次将更新批次状态为“付款中”；
      </br>
      3、请登录银行企业网银系统上传代发文件进行发放。
      </br>
    </section>
    <section class="head" v-else-if="type=='cgb'">
      1、每个代发付款批次都有唯一识别码，导出的发放文件根据唯一识别码生成；
      </br>
      2、导出发放文件后，代发付款批次将更新批次状态为“付款中”；
      </br>
      3、请登录银行企业网银系统上传代发文件进行发放。
    </section>
    <section v-else class="head">
      1、每个代发付款批次都有唯一识别码，导出的发放文件根据唯一识别码生成；
      </br>
      2、导出发放文件后，代发付款批次将更新批次状态为“付款中”；
      </br>
      3、请登录广发银行企业网银系统上传代发文件进行发放。
      </br>
      您可使用IE浏览器，打开链接：https://ebank.cgbchina.com.cn/corporbank/，登录广发银行企业网银系统。
    </section>
    <section class="body">
      <section class="body-content">
        <p>经办人姓名：{{handlerMsg.handler}}</p>
        <p>经办人手机号：{{handlerMsg.handlerMobile}}</p>
        <p>
          <el-input
            size="medium"
            placeholder="请输入验证码"
            v-model="yzm"
            style="width:180px"
            type="number"
            @input="handleInput"
          >
          </el-input>
          <el-button 
            type="primary" size="medium" 
            @click="handleSend" 
            :loading="isLoading" 
            :disabled="isDisabled"
          >{{btnVal}}</el-button>
        </p>
      </section>
    </section>
  </div>
</template>

<script>
import { apiSmsSend, apiSmsVerify } from "../../store/api";
export default {
  name: "export-file",
  props: ["handlerMsg"],
  data() {
    return {
      yzm:"",
      code:"",
      btnVal:"获取验证码",
      isLoading:false,
      isDisabled:false,
      smsTime:null,
      type:""
    }
  },
  mounted(){
    this.type = window.env.server_env
  },
  methods:{
    handleInput(val){
      if(val.length>5){
        this.yzm=val.slice(0,6)
      }
    },
    handleSubmitForm(formName) {
      return new Promise((resolve, reject) => {
        Promise.all([this.handleSmsVerify()]).then((res) => {
          resolve("true");
        });
      });
    },
    //按钮状态
    handleBtnChange({ isLoading, btnVal, isDisabled }) {
      this.isLoading = isLoading;
      this.btnVal = btnVal;
      this.isDisabled = isDisabled;
    },
    //验证码计时器
    handleSmsTime() {
      let second = 60;
      let siv = setInterval(() => {
        this.smsTime = second;
        if (second < 0) {
          clearInterval(siv);
          this.handleBtnChange({
            btnVal: "重新发送",
            isLoading: false,
            isDisabled: false,
          });
        } else {
          this.handleBtnChange({
            btnVal: `${second--}s`,
            isLoading: false,
            isDisabled: true,
          });
        }
      }, 1000);
    },
    //下发短信
    handleSend() {
      this.handleBtnChange({
        btnVal: "发送中",
        isLoading: true,
        isDisabled: false,
      });
      apiSmsSend({
        receiver: this.handlerMsg.handlerMobileTrue,
      }).then((res) => {
        if (res.success) {
          this.code = res.data.code;
          this.$message({
            showClose: true,
            message: "发送成功",
            type: "success",
          });
          this.handleSmsTime();
        }
        console.log(res);
      });
    },
    //短验
    handleSmsVerify() {
      return new Promise((resolve, reject) => {
        apiSmsVerify({ challenge: this.yzm, code: this.code }).then((res) => {
          if (res.success) {
            resolve("true");
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.export-file {
  .head {
    background: rgb(241, 241, 241);
    line-height: 35px;
    padding: 10px;
  }
  .body {
    display: flex;
    justify-content: center;
    .body-content {
      p {
        margin: 10px 0;
      }
    }
  }
}
</style>