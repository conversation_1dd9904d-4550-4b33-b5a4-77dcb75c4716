<template>
  <div class="import-file">
    <section class="head">
      1、请从企业网银中下载失败文件，将失败文件导入。系统根据银行账号自动匹配发放失败人员，并同步到代发申请批次中；
      <br />
      2、支持xlsx和xls文件，文件不超过5M，建议直接使用网银系统模板格式；
      <br />
      3、导入失败信息后，请到代发申请中修改失败信息，或联系负责人进行修改。
    </section>
    <section class="upload">
      <el-upload
        class="upload-demo"
        :action="apiCheck"
        :headers="myHeaders"
        :before-upload="handleBeforeUpload"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        :data="{ payBatchId }"
        multiple
        :limit="1"
        :on-exceed="handleExceed"
        :on-success="handleSuccess"
        :file-list="fileList"
      >
        <el-button size="small" type="primary">点击上传</el-button>
        <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
      </el-upload>
    </section>
    <section class="upload-info">
      <p v-if="upLoadState == 'SUCCESS'" class="success">
        <i class="el-icon-success"></i>数据全部通过校验
      </p>
      <p v-if="upLoadState == 'PARTIAL_SUCCESS'">
        <i class="el-icon-error"></i><span>数据未全部通过校验</span>
      </p>
      <p v-if="upLoadState == 'FAIL'">
        <i class="el-icon-error"></i><span>数据全部未通过校验</span>
      </p>
      <el-button
        type="text"
        @click="handleExportError"
        v-if="upLoadState == 'FAIL' || upLoadState == 'PARTIAL_SUCCESS'"
        >导出错误信息</el-button
      >
    </section>
    <!-- <p class="msg">导入失败信息后，请到代发申请中修改失败信息，或联系负责人进行修改</p> -->
  </div>
</template>

<script>
import { apiDownLoadFailPaySalary } from '../../store/api'
import { baseUrl } from "@/request/fetch";
export default {
  name: "import-file",
  props: ["payBatchId"],
  data() {
    return {
      myHeaders: { Authorization: this.$getToken() },
      fileList:[],
      apiCheck: baseUrl + "/api/payroll/paySalary/uploadFile/importVerify",
      upLoadState:""
    }
  },
  methods: {
    handleRemove(file, fileList) {
      this.upLoadState = "";
      this.$emit(
        "isDiaLogBtnDis",
        !this.upLoadState || this.upLoadState == "FAIL" ? true : false
      );
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`, {
        closeOnClickModal: false,
        closeOnPressEscape: false
      });
    },
    handleBeforeUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isxls = testmsg === "xls" || testmsg === "xlsx";
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isxls) {
        this.$message({
          message: "文件格式有误，请选择xlsx或xls文件",
          type: "warning",
        });
      }
      if (!isLt5M) {
        this.$message({
          message: "文件大小超过5M，请重选文件",
          type: "warning",
        });
      }
      return isxls && isLt5M;
    },
    handleSuccess(res, file, fileList) {
      console.log(res, file, fileList);
      if (res.success) {
        this.upLoadState = res.data;
        this.$emit(
          "isDiaLogBtnDis",
          !this.upLoadState || this.upLoadState == "FAIL" ? true : false
        );
      } else {
        this.$message.error(res.message);
        this.fileList = [];
      }
    },
    handleExportError() {
      apiDownLoadFailPaySalary(this.payBatchId);
    },
  },
};
</script>

<style lang="scss" scoped>
.import-file {
  .head {
    background: rgb(241, 241, 241);
    line-height: 35px;
    padding: 10px;
  }
  .upload {
    margin: 20px;
  }
  .upload-info {
    display: flex;
    align-content: center;
    .success {
      color: #53b536;
    }
    p {
      color: #ed6b59;
      margin-right: 20px;
      padding: 12px 0;
      i {
        margin-right: 8px;
      }
    }
  }
  .msg {
    color: rgb(255, 204, 104);
  }
}
</style>
