<template>
  <div>
    <div
      style="display: flex; justify-content: center; background-color: #f2f2f2"
    >
      <el-form
        :model="accountSelect"
        ref="accountSelect"
        label-width="140px"
        style="margin-top: 40px"
      >
        <div style="margin: 0px 0px 20px 71px">
          <h4>该发薪公司存在多个代发账户，请选择需要使用的代发账户</h4>
        </div>
        <el-form-item label="开户账号" prop="accountNo" style="width: 500px">
          <el-select
            v-model="accountSelect.accountNo"
            placeholder="请选择开户账号"
            style="width: 260px"
            @change="changeAccountNo"
          >
            <el-option
              v-for="item in commitParameter.accountList"
              :key="item.accountBankNo"
              :label="item.accountBankNo"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账户类型">
          {{ accountSelect.accountType }}
        </el-form-item>
      </el-form>
    </div>
    <div style="display: flex; justify-content: flex-end; margin-top: 20px">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    commitParameter: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      accountSelect: {
        accountNo: "",
        accountType: "",
      },
    };
  },
  created() {},
  methods: {
    confirm() {
      if (!this.accountSelect.accountNo) {
        this.$message.error("请选择需要使用的代发账户");
        return;
      }
      this.commitParameter.param.payAccountId = this.accountSelect.accountNo;
      this.$emit("agentSelectAccountConfirm", this.commitParameter.param);
    },
    cancel() {
      this.$emit("agentSelectAccountCancel");
    },
    changeAccountNo(accountNo) {
      for (var i = 0; i < this.commitParameter.accountList.length; i++) {
        if (accountNo === this.commitParameter.accountList[i].id) {
          let accountType = this.commitParameter.accountList[i].accountType;
          if (accountType === "BASIC") {
            this.accountSelect.accountType = "基本户";
          } else if (accountType === "NORMAL") {
            this.accountSelect.accountType = "一般户";
          } else {
            this.accountSelect.accountType = "";
          }
        }
      }
    },
  },
};
</script>

<style>
</style>