<template>
  <div>
    <div style="display: flex; justify-content: center">
      <div
        style="width: 500px; background-color: #f2f2f2; margin: 60px 0"
        :style="{ height: getHeight }"
      >
        <div style="display: flex; justify-content: center; margin-top: 20px">
          <el-image
            style="width: 30px; height: 30px; margin-right: 10px"
            :src="imageUrl"
          />
          <h3 style="margin-top: 3px">
            {{ this.resultDetail.title }}
          </h3>
        </div>
        <div v-if="this.resultDetail.status === 'FAIL'">
          <div style="width: 300px; margin-left: 150px">
            <p style="margin: 5px 0">
              代发账户：{{ this.resultDetail.commissionAccount }}
            </p>
            <p>账户余额：{{ this.resultDetail.accountBalance }}</p>
          </div>
        </div>
        <div
          v-if="
            this.resultDetail.status === 'SUCCESS' ||
            this.resultDetail.status === 'PROCESSING' ||
            this.resultDetail.status === 'FAIL_PAY'
          "
        >
          <div
            style="display: flex; justify-content: space-around; margin: 13px 0"
          >
            <span> 总金额：{{ this.resultDetail.totalMmount }} </span>
            <span> 总计手续费：{{ this.resultDetail.totalCommission }} </span>
            <span> 总笔数：{{ this.resultDetail.count }} </span>
          </div>
          <div style="display: flex; justify-content: space-around">
            <span> 成功笔数：{{ this.resultDetail.successCount }} </span>
            <span> 失败笔数：{{ this.resultDetail.failCount }} </span>
            <span> 处理中笔数：{{ this.resultDetail.processingCount }} </span>
          </div>
        </div>
      </div>
    </div>
    <div style="display: flex; justify-content: flex-end">
      <div>
        <el-button @click="close">关闭</el-button>
        <el-button
          type="primary"
          v-if="this.resultDetail.status === 'SUCCESS' || this.resultDetail.status === 'PROCESSING'"
          plain
          @click="viewDetails"
          >查看详情</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    resultDetail: {
      type: Object,
      default: {},
    },
  },
  computed: {
    getHeight() {
      return this.resultDetail.status === "FAIL" ? "125px" : "145px";
    },
    imageUrl() {
      if (this.resultDetail.status === "SUCCESS") {
        return require("../../../../assets/images/cgbResultSuccess.png");
      } else if (
        this.resultDetail.status === "FAIL" ||
        this.resultDetail.status === "FAIL_PAY"
      ) {
        return require("../../../../assets/images/cgbResultFail.png");
      } else if (this.resultDetail.status === "PROCESSING") {
        return require("../../../../assets/images/cgbResultProcessing.png");
      }
    },
  },
  methods: {
    close() {
      this.$emit("close");
    },
    viewDetails() {
      console.log("this.resultDetail", this.resultDetail);
      this.$router.push({
        path: "/agent-pay-detail",
        query: { id: this.resultDetail.id },
      });
    },
  },
};
</script>

<style>
</style>