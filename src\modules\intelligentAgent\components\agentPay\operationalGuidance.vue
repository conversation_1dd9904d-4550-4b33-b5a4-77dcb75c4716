<template>
  <div class="operational-guidance">
    <header class="header main-title">
      <span @click="back" class="back-style">返回</span>
      <span class="header-line">|</span>
      <span>智能代发操作指引</span>
    </header>
    <el-main class="operational-main">
      <div class="operational-content" v-loading="loading">
        <div v-html="content"></div>
      </div>
    </el-main>
  </div>
</template>
<script>
import { apiGetDocument } from "../../store/api";
export default {
  data() {
    return {
      content: "",
      loading: false,
    };
  },
  props: {
    id: {
      type: Number,
      default: 3,
    },
  },
  mounted() {
    this.getDetail(this.id);
  },
  methods: {
    back() {
      this.$parent.isGuidance = false;
    },
    getDetail(id) {
      this.loading = true;
      apiGetDocument({
        cmsId: id,
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.content = res.data.content;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";

.operational-guidance {
  /*height: calc(100vh - 80px);*/
  .operational-main {
    width: 1000px;
    margin: 20px auto;
    padding: 0px;
    .operational-content {
      margin-top: 30px;
      /deep/p {
        width: 1000px;
        margin: 10px 0;
        img {
          width: 100%;
        }
      }
    }
  }
}
</style>
