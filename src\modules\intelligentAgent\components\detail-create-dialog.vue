<template>
  <el-dialog
    title="生成批量开户二维码"
    :visible.sync="show"
    width="1004px"
    class="dialog"
  >
    <div class="create">
      <div class="qr-code">
        <img src="@/assets/images/agentCreate/qr_code.png" alt="" />
      </div>
      <p class="tips">
        请使用手机扫描上方二维码<br />
        打开批量开户二维码生成页面，填写信息后生成开户二维码
      </p>
      <h1>使用说明：</h1>
      <ul>
        <li v-for="(item, index) in list" :key="index">
          <img
            v-if="index % 4 == 0"
            class="c-line"
            src="@/assets/images/agentCreate/pic_process.png"
            alt=""
          />
          <p>{{ item }}</p>
          <img
            :src="require(`@/assets/images/agentCreate/pic_0${index + 1}.png`)"
            alt=""
          />
        </li>
      </ul>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="show = false">取 消</el-button>
      <el-button type="primary" @click="show = false">我知道了</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      show: false,
      list: [
        "01  扫描二维码打开页面",
        "02  填写信息",
        "03  选择已签约信息",
        "04  设置二维码名称",
        "05  生成二维码",
        "06  保存二维码图片",
        "07  打开微信小程序",
        "08  进入批量开卡上传二维码图片",
      ],
    };
  },
  watch: {
    show(val) {
      if (val == true) {
        this.$nextTick(() => {
          const box = this.$el.querySelector(".create");
          box.scrollTop = 0;
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.create {
  padding: 0 30px;
  height: 600px;
  overflow-y: scroll;
  .qr-code {
    margin: 0 auto;
    width: 156px;
    height: 156px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #f7f8fa;
    box-shadow: 0 16px 30px -4px rgba(204, 204, 204, 0.21);
    border-radius: 16px;
    margin-bottom: 15px;

    img {
      width: 148px;
      height: 148px;
    }
  }
  .tips {
    font-size: 12px;
    color: #888888;
    letter-spacing: 0;
    text-align: center;
    line-height: 20px;
    margin-bottom: 30px;
  }
  h1 {
    width: 100%;
    font-weight: 600;
    font-size: 14px;
    color: #070f29;
    text-align: left;
    margin-bottom: 30px;
  }
  ul {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    li {
      width: 190px;
      margin-bottom: 60px;
      position: relative;
      p {
        font-size: 12px;
        color: #6a6f7f;
        text-align: center;
        margin: 12px 0;
      }
      img {
        height: 410px;
      }
      .c-line {
        width: 722px;
        height: 8px;
        position: absolute;
        top: -10px;
        left: 85px;
      }
    }
  }
}
/deep/.el-dialog__footer {
  border-top: 1px solid #eaeaea;
}
/deep/.el-dialog__body {
  padding-bottom: 0;
}
</style>
