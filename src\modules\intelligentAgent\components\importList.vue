<template>
  <div class="import-data">
    <el-dialog
      :title="'修改失败信息'"
      :visible.sync="isShowImport"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="740px"
    >
      <div class="explain">
        <p>
          1、数据校验的失败信息，请从数据校验中下载失败文件，并修改失败信息，在此处导入文件批量修改。
        </p>
        <p>2、工资发放失败的信息，可下载导入模板，使用模板批量修改信息。</p>
        <p>3、支持xlsx和xls文件，文件不超过5M。</p>
        <div>
          <el-button type="text" @click="download">
            <i class="iconfont iconxiazai-"></i>
            <span>下载模版</span>
          </el-button>
        </div>
      </div>
      <div class="choose">
        <el-upload
          class="avatar-uploader"
          :action="apiCheck"
          :headers="myHeaders"
          :file-list="fileList"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :on-success="handleSuccess"
          :data="{ applyBatchId: applyBatchId }"
        >
          <el-button type="primary">选择文件</el-button>
        </el-upload>
      </div>
      <div class="handle-info">
        <p v-if="uploadType == 'SUCCESS'" class="success">
          <i class="el-icon-success"></i>数据全部通过校验
        </p>
        <p v-if="uploadType == 'PARTIAL_SUCCESS'">
          <i class="el-icon-error"></i><span>数据未全部通过校验</span>
        </p>
        <p v-if="uploadType == 'FAIL'">
          <i class="el-icon-error"></i><span>数据全部未通过校验</span>
        </p>
        <el-button
          type="text"
          @click="exportError"
          v-if="uploadType == 'FAIL' || uploadType == 'PARTIAL_SUCCESS'"
          >导出错误信息</el-button
        >
      </div>
      <div slot="footer">
        <el-button @click="cancel">取消</el-button>
        <el-button
          type="primary"
          @click="confirmUpload"
          :disabled="!uploadType || uploadType == 'FAIL'"
          >导入通过数据</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  apiImportVerify,
  apiDownloadPaySalaryApplyErrorRecord,
  apiConfirmUpload,
  apiDownLoadFail,
} from "../store/api";
import { baseUrl } from "@/request/fetch";
import { getToken } from "@olading/olading-business-ui";
export default {
  props: {
    applyBatchId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      isShowImport: false,
      myHeaders: { Authorization: getToken() },
      fileList: [],
      apiCheck: baseUrl + "/api/payroll/paySalaryApply/uploadFile/importVerify",
      uploadType: ""
    };
  },
  mounted() {},
  methods: {
    show() {
      this.isShowImport = true;
    },
    beforeUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isxls = testmsg === "xls" || testmsg === "xlsx";
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isxls) {
        this.$message({
          message: "文件格式有误，请选择xlsx或xls文件",
          type: "warning",
        });
      }
      if (!isLt5M) {
        this.$message({
          message: "文件大小超过5M，请重选文件",
          type: "warning",
        });
      }
      return isxls && isLt5M;
    },
    handleSuccess(res, file, fileList) {
      if (res.success) {
        let data = res.data;
        this.uploadType = data;
      } else {
        this.$message.error(res.message);
      }
      this.fileList = [fileList[fileList.length - 1]];
    },
    //删除文件
    handleRemove(file, fileList) {
      if (file && file.status === "success") {
        this.uuid = "";
      }
    },
    // 下载
    download() {
      apiDownloadPaySalaryApplyErrorRecord(this.applyBatchId).then((res) => {
        // if(res.success==false) this.$message.error(res.message);
      });
    },
    // 确认上传
    confirmUpload() {
      apiConfirmUpload(this.applyBatchId).then((res) => {
        if (res.success) {
          this.$message.success("操作成功");
          this.cancel();
          this.$emit("refresh");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 导出错误信息
    exportError() {
      apiDownLoadFail(this.applyBatchId);
    },
    cancel() {
      this.isShowImport = false;
      this.fileList = [];
      this.uploadType = "";
    },
  },
};
</script>
<style lang="scss" scoped>
.import-data {
  .explain {
    background-color: #f6f8ff;
    border-radius: 8px;
    padding: 16px;
    line-height: 28px;
  }
  .choose {
    margin-top: 16px;
  }
  .handle-info {
    display: flex;
    align-content: center;
    .success {
      color: #53b536;
    }
    p {
      color: #ed6b59;
      margin-right: 20px;
      padding: 12px 0;
      i {
        margin-right: 8px;
      }
    }
  }
}
</style>
