<template>
  <div class="create-agent-file">
    <header class="header main-title" v-if="!isGuidance">
      <span @click="$router.go(-1)" class="back-style">返回</span>
      <span class="header-line">|</span>
      <span>生成代发文件</span>
    </header>
    <el-main v-if="!isGuidance">
      <div class="step-control">
        <p>
          <i class="iconfont iconbuzhouwancheng1"></i>
          <span>生成代发文件</span>
        </p>
        <p class="step-line"></p>
        <p>
          <i
            class="iconfont"
            :class="active == 1 ? 'icondierbu' : 'icondierbu-wancheng'"
          ></i>
          <span>发放工资</span>
        </p>
      </div>
      <div v-show="active === 1">
        <div class="tip-box verify-box">
          <div>
            经办人姓名：
            <span>{{ handlerMsg.handler }}</span>
          </div>
          <div>
            经办人手机号：
            <span>{{ handlerMsg.handlerMobile }}</span>
          </div>
          <div>
            <el-input
              placeholder="请输入验证码"
              v-model="yzm"
              style="width: 300px"
              type="number"
              @input="handleInput"
            ></el-input>
            <el-button
              class="get-verify"
              @click="handleSend"
              :loading="isLoading"
              :disabled="isDisabled"
              type="text"
              >{{ btnVal }}</el-button
            >
          </div>
        </div>
        <div class="footer">
          <el-button @click="$router.go(-1)">取消</el-button>
          <el-button type="primary" @click="handleGenerateFile"
            >生成文件</el-button
          >
        </div>
      </div>
      <div v-show="active === 2">
        <div class="tip-box guide-box">
          <h5>生成代发文件成功</h5>
          <div>代发文件生成成功，请点击转账代发完成发放</div>
          <div class="btns">
            <el-button type="text" @click="isGuidance = true"
              >查看图文操作指引</el-button
            >
            <el-button type="text" @click="handleSeeVideo"
              >查看视频操作指引</el-button
            >
          </div>
        </div>
        <div class="footer">
          <el-button @click="handlePrev">上一步</el-button>
          <el-button type="primary" plain @click="handleGrant"
            >发放工资条</el-button
          >
          <el-button type="primary" @click="handleAccount">转账代发</el-button>
        </div>
      </div>
    </el-main>
    <operational-guidance
      v-if="isGuidance"
      :id="payChannelConfVo.pictureGuideLinkAddress"
    ></operational-guidance>
    <el-dialog
      title="智能代发操作指引"
      :visible.sync="isShowVideo"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="740px"
      :show-close="true"
      :destroy-on-close="true"
      class="create-dialog"
    >
      <div id="J_prismPlayer"></div>
    </el-dialog>
  </div>
</template>
<script>
import {
  apiDownloadPaymentDocumentse,
  apiSmsSend,
  apiDownloadPaymentDocuments,
  apiSmsVerify,
  apiGetPaySalaryRecordList,
  getPaySalaryApplyBatchInfo,
} from "./store/api";
import operationalGuidance from "./components/agentPay/operationalGuidance";
export default {
  data() {
    return {
      active: 1,
      handlerMsg: {
        //经办人信息
        handler: "",
        handlerMobile: "",
        handlerMobileTrue: "",
      },
      verifyCode: "",
      yzm: "",
      code: "",
      btnVal: "获取验证码",
      isLoading: false,
      isDisabled: false,
      smsTime: null,
      type: "",
      id: 0,
      isGuidance: false,
      isShowVideo: false,
      siv: null,
      payChannelConfVo: {},
      player: null,
      obj: {},
    };
  },
  components: {
    operationalGuidance,
  },
  mounted() {
    this.id = this.$route.query.id || 0;
    this.handleDownloadPaymentDocumentse();
    this.handleTableData();
  },
  methods: {
    // 查看视频指引
    handleSeeVideo() {
      this.isShowVideo = true;
      this.$nextTick(() => {
        this.player = new Aliplayer({
          id: "J_prismPlayer",
          source: this.payChannelConfVo.videoGuideLinkAddress,
          width: "708px",
          height: "398px",
          autoplay: true,
          isLive: false,
          rePlay: false,
          playsinline: false,
          preload: true,
        });
      });
    },
    // 导出文件下载校验获取经办人信息
    handleDownloadPaymentDocumentse() {
      return new Promise((resolve, reject) => {
        this.$set(this.handlerMsg, "handler", "");
        this.$set(this.handlerMsg, "handlerMobile", "");
        this.$set(this.handlerMsg, "handlerMobileTrue", "");
        apiDownloadPaymentDocumentse(this.id).then((res) => {
          if (res.success) {
            const { handler, handlerMobile, applyBatchId } = res.data;
            console.log("applyBatchId", applyBatchId);
            this.applyBatchId = applyBatchId;
            if (applyBatchId) {
              this.getPaySalaryApplyBatchInfo();
            } else {
              this.queryActive = 2;
              this.obj = {
                isAgent: false,
              };
            }
            this.$set(this.handlerMsg, "handler", this.handleName(handler));
            this.$set(this.handlerMsg, "handlerMobileTrue", handlerMobile);
            this.$set(
              this.handlerMsg,
              "handlerMobile",
              handlerMobile.replace(new RegExp("^(.{3}).+(.{4})$"), "$1****$2")
            );
            this.isShowExportFile = false;
            resolve("true");
          }
        });
      });
    },
    //脱敏
    handleName(val) {
      let res = val;
      if (val.length == 2) {
        res = val.replace(new RegExp(".*(?=[\u4e00-\u9fa5])"), "*");
      }
      if (val.length > 2) {
        res = val.replace(
          new RegExp("(?<=[\u4e00-\u9fa5]).*(?=[\u4e00-\u9fa5])"),
          Array(val.length - 1).join("*")
        );
      }
      return res;
    },
    // 输入验证码
    handleInput(val) {
      if (val.length > 5) {
        this.yzm = val.slice(0, 6);
      }
    },
    handleSubmitForm(formName) {
      return new Promise((resolve, reject) => {
        Promise.all([this.handleSmsVerify()]).then((res) => {
          resolve("true");
        });
      });
    },
    //按钮状态
    handleBtnChange({ isLoading, btnVal, isDisabled }) {
      this.isLoading = isLoading;
      this.btnVal = btnVal;
      this.isDisabled = isDisabled;
    },
    //验证码计时器
    handleSmsTime() {
      let second = 60;
      this.siv = setInterval(() => {
        this.smsTime = second;
        if (second < 0) {
          clearInterval(this.siv);
          this.handleBtnChange({
            btnVal: "重新发送",
            isLoading: false,
            isDisabled: false,
          });
        } else {
          this.handleBtnChange({
            btnVal: `${second--}s`,
            isLoading: false,
            isDisabled: true,
          });
        }
      }, 1000);
    },
    //下发短信
    handleSend() {
      this.handleBtnChange({
        btnVal: "发送中",
        isLoading: true,
        isDisabled: false,
      });
      apiSmsSend({
        receiver: this.handlerMsg.handlerMobileTrue,
      }).then((res) => {
        if (res.success) {
          this.code = res.data.code;
          this.$message({
            showClose: true,
            message: "发送成功",
            type: "success",
          });
          this.handleSmsTime();
        }
      });
    },
    //短验
    handleSmsVerify() {
      return new Promise((resolve, reject) => {
        apiSmsVerify({ challenge: this.yzm, code: this.code }).then((res) => {
          if (res.success) {
            resolve("true");
          }
        });
      });
    },
    // 生成文件
    handleGenerateFile() {
      this.handleSubmitForm().then((res) => {
        if (res == "true") {
          apiDownloadPaymentDocuments(this.id).then((response) => {
            this.$message.success("操作成功");
            this.active = 2;
          });
        }
      });
    },
    // 转账代发
    handleAccount() {
      // window.open("https://ebank.cgbchina.com.cn/corporbank/", "_blank");
      window.open(this.payChannelConfVo.linkAddress, "_blank");
    },
    //获取代发批次信息
    async getPaySalaryApplyBatchInfo() {
      const { data, success } = await getPaySalaryApplyBatchInfo({
        applyBatchId: this.applyBatchId,
      });

      if (success) {
        if (data.uploadFileId) {
          this.queryActive = 1;
          this.obj = {
            name: data.subjectName,
            salaryPaymentMonth: this.format(data.salaryMonth),
            fileId: data.uploadFileId,
            fileName: data.uploadFileName,
            mappings: data.mappings,
            titles: data.titles,
            isAgent: true,
          };
        } else {
          this.queryActive = 2;
          this.obj = {
            salaryPaymentMonth: this.format(data.salaryMonth),
            isAgent: true,
          };
        }
      }
    },
    format(str) {
      return str.substr(0, 4) + "-" + str.substr(4, 2);
    },

    //发放工资条
    handleGrant() {
      console.log(this.obj);
      this.$store.commit("payrollStore/PAYPARAMS", this.obj);
      this.$router.push({
        path: "/payroll/add-payroll",
        query: {
          active: this.queryActive,
        },
      });
    },

    // 上一步
    handlePrev() {
      this.active = 1;
      this.btnVal = "获取验证码";
      this.yzm = "";
      this.code = "";
      clearInterval(this.siv);
    },
    // 获取详情数据
    handleTableData() {
      apiGetPaySalaryRecordList({
        currPage: 1,
        pageSize: 10,
        payBatchId: this.id,
        empName: "",
      }).then((res) => {
        this.payChannelConfVo = res.data.payChannelConfVo;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers";
.create-agent-file {
  .el-main {
    overflow-x: hidden;
  }
  .el-steps {
    width: 680px;
    margin: 20px auto 0 auto;
  }
  .step-control {
    width: 680px;
    margin: 20px auto 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .step-line {
      width: 432px;
      height: 1px;
      background: #eaeaea;
    }
    p {
      font-size: 0px;
      i {
        font-size: 24px;
        color: $mainColor;
      }
      span {
        padding-left: 10px;
        font-weight: Medium;
        font-size: 16px;
        color: #070f29;
        text-align: center;
      }
    }
  }
  .tip-box {
    width: 680px;
    min-width: 450px;
    background: #fafafa;
    border-radius: 8px;
    margin: 40px auto 0 auto;
    padding: 40px 0;
    box-sizing: border-box;
    overflow: hidden;
  }
  .verify-box {
    > div {
      width: 300px;
      margin: 0 auto 20px auto;
      display: flex;
      align-items: center;
      position: relative;
      .get-verify {
        position: absolute;
        right: 12px;
        top: 0;
        color: $mainColor;
        cursor: pointer;
        // line-height: 40px;
      }
    }
    > div:last-child {
      margin-bottom: 0;
    }
    /deep/.el-input-group--append .el-input__inner {
      border-right: 0;
    }
    /deep/.el-input-group__append {
      background-color: #fff;
      color: $mainColor;
    }
  }
  .guide-box {
    text-align: center;
    h5 {
      font-size: 16px;
      font-weight: bold;
    }
    > div {
      margin: 20px;
    }
    .btns {
      width: 280px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .footer {
    position: fixed;
    bottom: 0;
    right: 0;
    width: calc(100% - 227px);
    text-align: center;
    border-top: 1px solid #eaeaea;
    padding: 20px 0;
  }
  .create-dialog {
    /deep/.el-dialog__body {
      padding: 10px 16px 28px;
    }
  }
}
</style>
