import agentApply from '../agentApply';
import agentApplyDetail from '../agentApplyDetail';
import agentAccount from '../agentAccount';
import agentPay from '../agentPay';
import agentPayDetail from '../agentPayDetail';
import createAgentFile from '../createAgentFile';
// import operationalGuidance from '../operationalGuidance';

export default [
  {
    path: '/agent-apply',// 代发申请
    name: 'agentApply',
    component: agentApply,
    meta: {
      businessCode: "salary.newpayroll.apply",
      icon: "icongongzi"
    }
  },
  {
    path: '/agent-apply-detail',// 代发申请详情
    name: 'agentApplyDetail',
    component: agentApplyDetail,
  },
  {
    path: '/agent-account',// 代发账户
    name: 'agentAccount',
    component: agentAccount,
    meta: {
      businessCode: "salary.newpayroll.account",
      icon: "icongongzi"
    }
  },
  {
    path: '/agent-pay',// 代发付款
    name: 'agentPay',
    component: agentPay,
    meta: {
      businessCode: "salary.newpayroll.pay",
      icon: "icongongzi"
    }
  },
  {
    path: '/agent-pay-detail',// 代发付款详情
    name: 'agentPayDetail',
    component: agentPayDetail,
  },
  {
    path: '/create-agent-file',// 生成代发文件
    name: 'createAgentFile',
    component: createAgentFile,
  },
  // {
  //   path: '/operational-guidance',// 操作指引
  //   name: 'operationalGuidance',
  //   component: operationalGuidance,
  // },
]