import { fetch, fetchFile } from 'request/fetch';
import oldFetch from 'request/oldFetch';

const env =
  process.env.NODE_ENV == 'development' ? '/agentApi/api/' : '/api/payroll/';

//代发账户列表
export function apiGetSubjectAccountList(form) {
  return fetch({
    url: '/api/payroll/subjectAccount/getSubjectAccountList',
    method: 'post',
    data: form,
  });
}

//代发通道查询列表
export function apiGetPayChannelConfList() {
  return fetch({
    url: '/api/payroll/subjectAccount/getPayChannelConfList',
    method: 'get',
  });
}
//新增代发账户
export function apiSavaSubjectAccount(form) {
  return fetch({
    url: '/api/payroll/subjectAccount/savaSubjectAccount',
    method: 'post',
    data: form,
  });
}

//账户校验
export function apiEditAccountCheck(form) {
  return fetch({
    url: '/api/payroll/subjectAccount/editAccountCheck',
    method: 'post',
    data: form,
  });
}

//查询资金来源
export function apiQueryAccountSource(data) {
  return fetch({
    url: '/api/payroll/paySalary/queryAccountSource',
    method: 'post',
    data,
  });
}

//查询资金来源
export function apiPaySalaryCommit(data) {
  return fetch({
    url: '/api/payroll/paySalary/paySalaryCommit',
    method: 'post',
    data,
  });
}

//获取代发账户详情
export function apiGetSubjectAccount(form) {
  return fetch({
    url: '/api/payroll/subjectAccount/getSubjectAccount',
    method: 'get',
    params: form,
  });
}
//获取代发账户详情
export function apiQueryBatch(data) {
  return fetch({
    url: `/api/payroll/paySalary/queryBatch/${data}`,
    method: 'get',
  });
}

//邀请员工开通电子工资卡
export function apiInviteSubjectAccount(form) {
  return fetch({
    url: '/api/payroll/subjectAccount/invitationElectronicsAccount',
    method: 'post',
    params: form,
  });
}
// 启用/禁用代发账户
export function apiSwitchSubjectAccount(form) {
  return fetch({
    url: '/api/payroll/subjectAccount/switchSubjectAccount',
    method: 'post',
    params: form,
  });
}
// 代发申请列表
export function apiPaySalaryApply(form) {
  return fetch({
    url: '/api/payroll/paySalaryApply/getPaySalaryApplyList',
    method: 'post',
    data: form,
  });
}
// 代发申请详情列表
export function apiGetPaySalaryApplyRecordList(form) {
  return fetch({
    url: '/api/payroll/paySalaryApply/getPaySalaryApplyRecordList',
    method: 'post',
    data: form,
  });
}
// 查询公司主体列表
export function apiGetSubjectList() {
  return fetch({
    url: '/api/payroll/subjectAccount/getSubjectList',
    method: 'get',
  });
}
//数据校验/批量数据校验
export function apiCheckPaySalaryApply(form) {
  return fetch({
    url: '/api/payroll/paySalaryApply/checkPaySalaryApply',
    method: 'post',
    data: form,
  });
}
// 代发申请详情修改银行卡号和开户行
export function apiModifyPaySalaryApplyRecord(form) {
  return fetch({
    url: '/api/payroll/paySalaryApply/modifyPaySalaryApplyRecord',
    method: 'post',
    data: form,
  });
}
// 失败信息下载
export function apiDownloadPaySalaryApplyErrorRecord(data) {
  return oldFetch({
    url: `/payroll/paySalaryApply/downloadPaySalaryApplyErrorRecord/${data}`,
    method: 'get',
    responseType: 'blob',
  });
}
// 代发申请-确认导入
export function apiConfirmUpload(data) {
  return fetch({
    url: `/api/payroll/paySalaryApply/confirmUpload/${data}`,
    method: 'get',
  });
}
// 代发申请-导出失败文件
export function apiDownLoadFail(data) {
  return oldFetch({
    url: `/payroll/paySalaryApply/downLoadFail/${data}`,
    method: 'get',
    responseType: 'blob',
  });
}
// 提交/批量提交代发数据
export function apiSubmitApplyBathRecord(form) {
  return fetch({
    url: '/api/payroll/paySalaryApply/submitApplyBathRecord',
    method: 'post',
    data: form,
  });
}
// 代发付款列表
export function apiGetPaySalaryList(form) {
  return fetch({
    url: '/api/payroll/paySalary/getPaySalaryList',
    method: 'post',
    data: form,
  });
}
// 代发付款详情列表
export function apiGetPaySalaryRecordList(form) {
  return fetch({
    url: '/api/payroll/paySalary/getPaySalaryRecordList',
    method: 'post',
    data: form,
  });
}
// 代发付款-导出发放文件
export function apiDownloadPaymentDocuments(data) {
  return oldFetch({
    url: `/payroll/paySalary/downloadPaymentDocuments/${data}`,
    method: 'get',
    responseType: 'blob',
  });
}
// 代发付款-导出失败文件
export function apiDownLoadFailPaySalary(data) {
  return oldFetch({
    url: `/payroll/paySalary/downLoadFail/${data}`,
    method: 'get',
    responseType: 'blob',
  });
}
// 代发付款-确认导入
export function apiConfirmUploadPaySalary(data) {
  return fetch({
    url: `/api/payroll/paySalary/confirmUpload/${data}`,
    method: 'get',
  });
}
// 代发付款-确认发放
export function apiConfirmPay(form) {
  return fetch({
    url: '/api/payroll/paySalaryApi/confirmPay',
    method: 'post',
    data: form,
  });
}
// 发送短信
export function apiSmsSend(form) {
  return fetch({
    url: '/api/payroll/sms/smsSend',
    method: 'post',
    data: form,
  });
}
// 验证短信
export function apiSmsVerify(form) {
  return fetch({
    url: '/api/payroll/sms/smsVerify',
    method: 'post',
    data: form,
  });
}
// 代发付款-完成发放
export function apiCompleteDistribution(data) {
  return fetch({
    url: `/api/payroll/paySalary/completeDistribution/${data}`,
    method: 'get',
  });
}
// 代发付款-取消发放
export function apiCancelIssuance(data) {
  return fetch({
    url: `/api/payroll/paySalary/cancelIssuance/${data}`,
    method: 'get',
  });
}
// 代发付款-导出文件下载校验获取经办人信息
export function apiDownloadPaymentDocumentse(data) {
  return fetch({
    url: `/api/payroll/paySalary/downloadPaymentDocuments/check/${data}`,
    method: 'get',
  });
}
// 获取代发通道配置的图文链接导航内容
export function apiGetDocument(form) {
  return fetch({
    url: '/api/payroll/subjectAccount/getDocument',
    method: 'get',
    params: form,
  });
}

//代发申请-新增代发上传文件
export function apiUploadTemplate(data) {
  return fetch({
    url: env + '/paySalaryApply/uploadPaySalaryApplyBatch',
    method: 'post',
    data,
  });
}

//代发申请-新增代发上传文件-下一步

export function savePaySalaryApplyBatch(data) {
  return fetch({
    url: env + '/paySalaryApply/savePaySalaryApplyBatch',
    method: 'post',
    data,
  });
}

//代发申请-新增代发上传文件-保存匹配关系

export function saveFieldMapping(data) {
  return fetch({
    url: env + '/paySalaryApply/saveFieldMapping',
    method: 'post',
    data,
  });
}

//代发申请-下载代发上传文件模板文件
export function apiDownloadApplyBatchTemplat(data) {
  return fetchFile({
    url: env + '/paySalaryApply/downloadPaySalaryApplyBatchTemplate',
    method: 'get',
    data,
    response: 'blob',
  });
}

//代发申请-导出失败文件

export function apiDownLoadPaySalaryApplyFail(data) {
  return fetchFile({
    url: `${env}/paySalaryApply/downLoadFail/${data}`,
    method: 'get',
    response: 'blob',
  });
}

//代发申请-获取代发批次信息
export function getPaySalaryApplyBatchInfo(data) {
  return fetch({
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    transformRequest: (params) => JSON.stringify(params),
    url: env + '/paySalaryApply/getPaySalaryApplyBatchInfo',
    method: 'post',
    params: data,
  });
}

// 查询公司主体列表
export function getEnableSubjectList() {
  return fetch({
    url: env + 'subjectAccount/getEnableSubjectList',
    method: 'get',
  });
}

// 代发申请删除待提交
export function removePaySalaryApplyBatch(data) {
  return fetch({
    url: '/api/payroll/paySalaryApply/removePaySalaryApplyBatch',
    method: 'post',
    params: data,
  });
}

export function apiGetCgbWebPayChannelConfig() {
  return fetch({
    url: '/api/payroll/paySalary/getCgbWebPayChannelConfig',
    method: 'post',
  });
}

//获取短信验证码
export function apiCodeSms(data) {
  return fetch({
    url: '/api/merchant/platform/createOtp',
    method: 'post',
    data
  })
}

//创建验证码
export function apiCreateCodeImage(data) {
  return fetch({
    url: '/api/merchant/platform/createCaptcha',
    method: 'post',
    data
  })
}

export function apiQuerySubject(data) {
  return fetch({
    url: '/api/hrsaas-emp/custom/v1/query/subject',
    method: 'post',
    data
  })
}

//当前登录用户的信息
export function apiPlatformProfile(data) {
  return fetch({
    url: '/api/merchant/platform/profile',
    method: 'post',
    data
  })
}

//代发账户列表
export function apiQuerySubjectAccountList(data) {
  return fetch({
    url: '/api/payroll/subjectAccount/querySubjectAccountList',
    method: 'get',
    params: data,
  });
}

//获取账户信息
export function apiCheckBeforePay(data) {
  return fetch({
    url: '/api/payroll/paySalary/checkBeforePay',
    method: 'post',
    data: data,
  });
}