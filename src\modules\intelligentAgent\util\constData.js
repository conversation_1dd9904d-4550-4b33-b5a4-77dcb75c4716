//校验状态

export const checkData = {
  TO_BE_VERIFIED: {
    name: '待校验',
    value: 'TO_BE_VERIFIED',
    color: '#4F71FF',
  },
  VERIFICATION_PASSED: {
    name: '通过',
    value: 'VERIFICATION_PASSED',
    color: '#41bd5a',
  },
  VERIFICATION_FAILED: {
    name: '未通过',
    value: 'VERIFICATION_FAILED',
    color: '#d6342a',
  },
  OTHER_BANK: {
    name: '他行卡未校验',
    value: 'OTHER_BANK',
    color: '#606266',
  },
};

// 批次状态

export const paymentData = {
  STASH: {
    name: '待提交',
    value: 'STASH',
    // color: '#4F71FF',
    color: 'gray'
  },
  // {
  //   name: "待付款",
  //   value: "PENDING_PAYMENT"
  // },
  IN_PAYMENT: {
    name: '付款中',
    value: 'IN_PAYMENT',
    color: '#4F71FF',
  },
  PAID: {
    name: '已付款',
    value: 'PAID',
    color: '#41bd5a',
  },
  PAYMENT_FAILED: {
    name: '付款失败',
    value: 'PAYMENT_FAILED',
    color: '#d6342a',
  },
  PARTIAL_PAYMENT_FAILED: {
    name: '部分付款失败',
    value: 'PARTIAL_PAYMENT_FAILED',
    color: '#d6342a',
  },
};

// 代发付款-批次状态
export const payEnum = {
  STASH: {
    name: '待付款',
    value: 'STASH',
    // color: '#4F71FF',
    color: 'gray'
  },
  IN_PAYMENT: {
    name: '付款中',
    value: 'IN_PAYMENT',
    color: '#4F71FF',
  },
  PAID: {
    name: '已付款',
    value: 'PAID',
    color: '#41bd5a',
  },
  PAYMENT_FAILED: {
    name: '付款失败',
    value: 'PAYMENT_FAILED',
    color: '#d6342a',
  },
  PARTIAL_PAYMENT_FAILED: {
    name: '部分付款失败',
    value: 'PARTIAL_PAYMENT_FAILED',
    color: '#d6342a',
  },
};

// 代发付款-通道类型
export const payChannelType = {
  API: '银企直联',
  WEB: '企业网银',
};
// 代发付款详情-订单状态
export const payOrderState = {
  STASH: '待付款',
  IN_PAYMENT: '付款中',
  PAID: '已付款',
  PAYMENT_FAILED: '付款失败',
  PARTIAL_PAYMENT_FAILED: '部分付款失败',
};
