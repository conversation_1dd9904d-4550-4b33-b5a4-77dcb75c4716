<template>
  <div class="login-page">
    <div>
      <div class="div-slogan">
        <div class="div-m clear">
          <!-- <div class="fl">人力资源科技服务平台</div>
                    <div class="login_rig fr">
                        <span style="cursor: pointer" @click="$router.push('/login')">登录</span>/
                        <span style="cursor: pointer" @click="$router.push('/register')">注册</span>
                    </div> -->
        </div>
      </div>
      <div class="row-bg header">
        <div class="logo div-m">
          <img src="../../assets/images/logo-yongyou.png" alt />
        </div>
      </div>
    </div>
    <div class="login-warp">
      <el-tabs
        v-model="activeTab"
        type="border-card"
        class="tabs"
        @tab-click="handleUserTypeTabClick('loginFormData')"
      >
        <el-tab-pane label="个人" name="person" class="tab-wrap">
          <el-tabs
            v-model="loginFormData.loginMode"
            @tab-click="handleTabClick('loginFormData')"
          >
            <el-tab-pane
              name="CELLPHONE_IMG_CODE"
              label="手机号快捷登录"
            ></el-tab-pane>
            <!-- <el-tab-pane name="CELLPHONE_PWD_IMG_CODE" label="手机号密码登录"></el-tab-pane> -->
          </el-tabs>
        </el-tab-pane>
        <!-- <el-tab-pane label="企业" name="enterprise" class="tab-wrap">
                    <el-tabs v-model="loginFormData.loginMode" @tab-click="handleTabClick('loginFormData')">
                        <el-tab-pane name="ENTERPRISE_EMAIL_PWD_IMG_CODE" label="邮箱登录"></el-tab-pane>
                        <el-tab-pane name="ENTERPRISE_NAME_PWD_IMG_CODE" label="用户登录"></el-tab-pane>
                    </el-tabs>
                </el-tab-pane> -->
        <el-form
          :model="loginFormData"
          :rules="loginFormRules"
          ref="loginFormData"
          label-width="100px"
          class="login-form"
          @keyup.enter.native="handleLogin('loginFormData')"
        >
          <el-form-item
            prop="telePhone"
            label="手机号"
            v-if="activeTab === 'person'"
          >
            <el-input
              v-model="loginFormData.telePhone"
              placeholder="手机号"
              maxlength="11"
            ></el-input>
          </el-form-item>
          <el-form-item
            prop="username"
            label="用户名"
            v-if="loginFormData.loginMode === 'ENTERPRISE_NAME_PWD_IMG_CODE'"
          >
            <el-input
              v-model="loginFormData.username"
              placeholder="用户名"
            ></el-input>
          </el-form-item>
          <el-form-item
            prop="email"
            label="企业邮箱"
            v-if="loginFormData.loginMode === 'ENTERPRISE_EMAIL_PWD_IMG_CODE'"
          >
            <el-input
              v-model="loginFormData.email"
              placeholder="企业邮箱"
            ></el-input>
          </el-form-item>
          <el-form-item prop="password" label="密码" v-if="showPwd">
            <el-input
              type="password"
              placeholder="6-20位字母、数字、字符组合"
              v-model="loginFormData.password"
              maxlength="20"
            ></el-input>
          </el-form-item>
          <el-form-item
            prop="code"
            label="短信验证码"
            class="sendmessage-content"
            v-if="loginFormData.loginMode === 'CELLPHONE_IMG_CODE'"
          >
            <div class="sendMsg-flex">
              <el-input
                class="sendMsg-item"
                v-model="loginFormData.code"
                maxlength="6"
                placeholder="短信验证码"
              ></el-input>
              <get-vaildCode
                class="sendMsg-item"
                :cellPhone="loginFormData.telePhone"
                @getVerifyCode="getVerifyCode"
                getType="cellPhone"
              ></get-vaildCode>
            </div>
          </el-form-item>
          <el-form-item
            prop="verifyCode"
            label="图形验证码"
            class="sendmessage-content"
          >
            <div class="sendMsg-flex">
              <el-input
                v-model="loginFormData.verifyCode"
                class="sendMsg-item"
                placeholder="图形验证码"
                maxlength="4"
              ></el-input>
              <img
                @click="handleResetCodeImage"
                class="sendMsg-item"
                :src="
                  `api/olading-user/captcha/show/?captchaToken=${
                    loginFormData.verifyCodeId
                  }`
                "
                alt
              />
            </div>
          </el-form-item>
          <div class="login-btn">
            <old-button
              type="primary"
              style="width: 100%"
              tag="span"
              @click="handleLogin('loginFormData')"
              >登录</old-button
            >
          </div>
          <!-- <el-form-item prop="agreement" class="agreement">
                        <el-checkbox name="type" v-model="loginFormData.agreement">
                            <span class="change">勾选同意</span>
                        </el-checkbox>
                        <a class="agreement-text" @click="agreement">《阿拉钉平台用户协议》</a>
                    </el-form-item> -->
        </el-form>
        <!-- <p class="findPass">
                    <a @click="$router.push('/findPass')" style="cursor: pointer;">找回密码</a>
                    <a @click="$router.push('/register')" style="cursor: pointer;float: right;">没有账号？立即注册</a>
                </p> -->
      </el-tabs>
    </div>
  </div>
</template>
<script>
import {
  validateTel,
  validateCode,
  validatePass,
  validUsername,
  validateEmail,
  validateCheck
} from "assets/js/utils/validate";
import { mapState } from "vuex";
const TIME_COUNT = 60;
import * as AT from "@/store/actionTypes";
import getVaildCode from "../../components/get-vaildCode/index";

export default {
  components: {
    getVaildCode
  },
  data() {
    return {
      activeTab: "person",
      loginFormData: {
        loginMode: "CELLPHONE_IMG_CODE",
        telePhone: "",
        username: "",
        email: "",
        password: "",
        code: "",
        verifyCode: "", //图形验证码
        agreement: true,
        verifyCodeId: "" //图形验证码ID
      },
      verifyCodeSend: "",
      loginBg: require("../../assets/images/bg.jpg"),
      loginFormRules: {
        telePhone: [
          { required: true, validator: validateTel, trigger: "blur" }
        ],
        username: [
          {
            required: true,
            validator: validUsername,
            trigger: "blur"
          }
        ],
        email: [
          {
            required: true,
            validator: validateEmail,
            trigger: "blur"
          }
        ],
        password: [
          {
            required: true,
            validator: validatePass,
            trigger: "blur"
          }
        ],
        code: [
          {
            required: true,
            validator: validateCode,
            trigger: "blur"
          }
        ],
        verifyCode: [
          {
            required: true,
            validator: validateCode,
            trigger: "blur"
          }
        ],
        agreement: [
          {
            required: true,
            validator: validateCheck,
            trigger: "change"
          }
        ]
      }
    };
  },
  computed: {
    ...mapState({
      urlInfo: "urlInfo",
      token: state => state.token
    }),
    showPwd: function() {
      return (
        this.loginFormData.loginMode === "CELLPHONE_PWD_IMG_CODE" ||
        this.loginFormData.loginMode === "ENTERPRISE_NAME_PWD_IMG_CODE" ||
        this.loginFormData.loginMode === "ENTERPRISE_EMAIL_PWD_IMG_CODE"
      );
    }
  },
  created() {
    this.$store.commit(AT.SHOWAPP, false);
    console.log(this.$store.state);
    const t = this;
    t.handleResetCodeImage();
  },
  methods: {
    //个人、企业切换
    handleUserTypeTabClick(formName, tab, event) {
      this.$refs[formName].resetFields();
      //企业登录
      if (this.activeTab === "enterprise") {
        this.loginFormData.loginMode = "ENTERPRISE_EMAIL_PWD_IMG_CODE";
      }
      //个人登录
      if (this.activeTab === "person") {
        this.loginFormData.loginMode = "CELLPHONE_IMG_CODE";
      }
    },
    //密码、手机号切换
    handleTabClick(formName, tab, event) {
      this.$refs[formName].resetFields();
    },
    //获取短信验证码
    getVerifyCode(val) {
      this.verifyCodeSend = val;
    },
    //获取图形验证码
    handleResetCodeImage() {
      const t = this;
      t.$store
        .dispatch("loginStore/acCreateCodeImage", {
          captchaType: "NUMBER_4_1"
        })
        .then(res => {
          t.loginFormData.verifyCodeId = res.data;
        });
    },
    agreement() {
      window.open("/static/agree.html");
    },
    //登陆
    handleLogin(formName) {
      const t = this;
      t.$refs[formName].validate(valid => {
        if (valid) {
          let username = "";
          if (t.activeTab === "person") {
            username = this.verifyCodeSend;
          }
          if (t.loginFormData.loginMode === "ENTERPRISE_NAME_PWD_IMG_CODE") {
            username = this.loginFormData.username;
          }
          if (t.loginFormData.loginMode === "ENTERPRISE_EMAIL_PWD_IMG_CODE") {
            username = t.loginFormData.email;
          }
          let urlAgent = null;
          if (t.urlInfo) {
            let urlData = {
              inviteCode: t.urlInfo.inviteCode,
              inviteUserId: t.urlInfo.inviteUserId
            };
            urlAgent = {
              serviceName: t.urlInfo.bn,
              sysName: t.urlInfo.sn,
              urlData: JSON.stringify(urlData)
            };
          }
          let parmasPerson = {
            username,
            cellPhone: this.loginFormData.telePhone,
            loginMode: this.loginFormData.loginMode,
            password: this.showPwd
              ? this.loginFormData.password
              : this.loginFormData.code,
            captchaCode: this.loginFormData.verifyCode,
            captchaCodeId: this.loginFormData.verifyCodeId,
            ...urlAgent
          };
          let parmasCompany = {
            username,
            loginMode: this.loginFormData.loginMode,
            password: this.showPwd
              ? this.loginFormData.password
              : this.loginFormData.code,
            captchaCode: this.loginFormData.verifyCode,
            captchaCodeId: this.loginFormData.verifyCodeId,
            ...urlAgent
          };
          if (t.activeTab === "person") {
            t.$store
              .dispatch("loginStore/acAuthenticationPersonLogin", parmasPerson)
              .then(res => {
                t.$store.commit(AT.SET_TOKEN, res.data.token);
                t.$store.dispatch("loginStore/acProfile").then(res => {
                  t.$store
                    .dispatch("selectServerStore/actionBindInfo")
                    .then(res => {
                      this.$store.commit(
                        AT.SETBINDINFO,
                        res.data.relevantMerchantVos
                      );
                      this.$store.commit(AT.SETHOMEINFO, res.data);
                      t.$router.push("/selectServer");
                    });
                });
              });
          } else {
            t.$store
              .dispatch(
                "loginStore/acAuthenticationCompanyLogin",
                parmasCompany
              )
              .then(res => {
                t.$store.dispatch("loginStore/acMerchantProfile").then(res => {
                  // t.$store.commit(AT.EDITRESETNAV, "");
                  t.$router.push("/selectServer");
                });
              });
          }
        } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.login-page {
  height: 100vh;
  color: #555555;
  background: url("../../assets/images/bg.jpg");
  .login-btn {
    text-align: center;
    margin-bottom: 20px;
  }
  .findPass {
    padding: 14px;
  }
  .div-slogan {
    height: 40px;
    line-height: 40px;
    background: #1c2024;
    color: #fff;
    font-size: 14px;
  }
  .fl,
  .fr {
    display: inline-block;
  }
  .fr {
    float: right;
  }
  .div-m {
    width: 1180px;
    margin: 0 auto;
  }
  .header {
    width: 100%;
    height: 70px;
    background: #fff;
    line-height: 75px;
    margin-bottom: 15px;
    img {
      margin: 10px 50px 0 0px;
      height: 50px;
      float: left;
    }
  }
  .logo {
    height: 100%;
    color: #a3a3a3;
    font-size: 16px;
    cursor: pointer;
  }
  .basicHeader {
    margin: 0px 0px 0px 45px;
  }
  .log-out {
    float: right;
    cursor: pointer;
    height: 75px;
    font-size: 25px;
    color: #a3a3a3;
    padding-right: 30px;
  }
  /deep/.sendmessage-content {
    .el-input__inner {
      width: 160px;
    }
    .sendMsg-flex {
      width: 300px;
      display: flex;
      .sendMsg-item {
        flex: 1;
        .old-button {
          width: 130px;
        }
      }
      .sendMsg-item:first-child {
        margin-right: 10px;
      }
    }
  }
  /deep/ .el-input__inner {
    width: 300px;
    background: #f0f2f4;
  }
  /deep/ .el-form-item__label {
    text-align: left;
  }
}
</style>
