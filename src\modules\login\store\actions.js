import {
  apiAuthenticationPersonLogin,
  apiAuthenticationCompanyLogin,
  apiCodeSms,
  apiCreateCodeImage,
  apiProfile,
  apiMerchantProfile,
  apiSendSmsCode,
  apiModifyPhoneSendSmsCode
} from "./api"

import * as AT from "./actionTypes"

//创建验证码
export const acCreateCodeImage = ({ commit }, data) => {
  return apiCreateCodeImage(data).then(res => {
    return res
  })
}

//短信验证码
export const acCodeSms = ({ commit }, data) => {
  return apiCodeSms(data).then(res => {
    return res
  })
}

//不需要手机号获取短信验证码
export const acSendSmsCode = ({ commit }, data) => {
  return apiSendSmsCode(data).then(res => {
    return res
  })
}

//修改手机发送短信验证码
export const acModifyPhoneSendSmsCode = ({ commit }, data) => {
  return apiModifyPhoneSendSmsCode(data).then(res => {
    return res
  })
}

//个人登录
export const acAuthenticationPersonLogin = ({ commit }, data) => {
  return apiAuthenticationPersonLogin(data).then(res => {
    return res
  })
}

//企业登录
export const acAuthenticationCompanyLogin = ({ dispatch, commit }, data) => {
  return apiAuthenticationCompanyLogin(data).then(res => {
    commit(AT.LOGIN_TOKEN, res.data.token)
    return res.data
  })
}

//个人信息
export const acProfile = ({ commit }, data) => {
  return apiProfile(data).then(res => {
    commit(AT.LOGIN_PROFILEINFO, res.data)
    commit(AT.LOGIN_USERTYPE, res.data.userType)
    return res.data
  })
}

//企业信息
export const acMerchantProfile = ({ commit }, data) => {
  return apiMerchantProfile(data).then(res => {
    commit(AT.LOGIN_MERPROFILEINFO, res.data)
    commit(AT.LOGIN_USERTYPE, res.data.type)
    return res.data
  })
}
