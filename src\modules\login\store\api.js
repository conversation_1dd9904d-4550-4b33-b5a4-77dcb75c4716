import { fetch, fetchFile } from 'request/fetch'

//个人登录
export function apiAuthenticationPersonLogin(data) {
  return fetch({
    url: "/api/olading-user/user/login",
    method: "post", //请求方法
    data
  })
}
//企业登录
export function apiAuthenticationCompanyLogin(data) {
  return fetch({
    url: "/api/olading-user/merchant/login",
    method: "post", //请求方法
    data
  })
}
//创建验证码
export function apiCreateCodeImage(data) {
  return fetch({
    url: "/api/olading-user/captcha/create",
    method: "post",
    data
  })
}
//获取短信验证码
export function apiCodeSms(data) {
  return fetch({
    url: "/api/olading-user/sms/send",
    method: "post",
    data
  })
}

//不需要手机号获取短信验证码
export function apiSendSmsCode(data) {
  return fetch({
    url: "/api/olading-user/user/sendSmsCode",
    method: "post",
    data
  })
}

//修改手机发送短信验证码
export function apiModifyPhoneSendSmsCode(data) {
  return fetch({
    url: "/api/olading-user/user/modifyPhoneSendSmsCode",
    method: "post",
    data
  })
}

//个人信息
export function apiProfile(data) {
  return fetch({
    url: "/api/olading-user/user/profile",
    method: "post", //请求方法
    data
  })
}

//企业信息
export function apiMerchantProfile(data) {
  return fetch({
    url: "/api/olading-user/merchant/profile",
    method: "post", //请求方法
    data
  })
}
