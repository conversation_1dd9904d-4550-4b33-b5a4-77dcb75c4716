import * as AT from "./actionTypes"
import {saveToken} from "@olading/olading-business-ui";
export default {
  [AT.LOGIN_TOKEN](state, token) {
    state.token = token
    sessionStorageOther.setItem("token", token)
    saveToken(token);
  },
  [AT.LOGIN_PROFILEINFO](state, profileInfo) {
    state.profileInfo = profileInfo
  },
  [AT.LOGIN_MERPROFILEINFO](state, merprofileInfo) {
    state.merprofileInfo = merprofileInfo
  },
  [AT.LOGIN_USERTYPE](state, userType) {
    state.userType = userType
  }
}
