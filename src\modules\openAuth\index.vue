<template>
  <div class="open-auth">
    <section>
      <h1 class="table-name">{{ title }}</h1>
      <div class="msg">
        <p>{{ msg[0] }}</p>
        <p>{{ msg[1] }}</p>
      </div>

      <div class="link" v-if="server_env === 'boc' || server_env === 'cgb'">
        <p style="margin-top: 20px">如需使用，请联系第三方供应商开通服务</p>
        <p>
          或拨打客服电话<span class="table-name">{{ tel }}</span>
        </p>
      </div>
      <div class="link" v-else>
        <p style="margin-top: 20px">
          如需使用，可自行前往
          <!-- <span style="color: #4f71ff; cursor: pointer" @click="handleOpen">
            线上开通
          </span> -->
        </p>
        <p style="margin: 10px 0">或联系客服开通服务</p>
        <p>
          客服电话：<span class="table-name">{{ tel }}</span>
        </p>
      </div>
    </section>
  </div>
</template>

<script>
const environmentConfig = window.env.environmentConfig;
import { Message } from "element-ui";
import { apiGetAppId } from "../contractManage/store/api";
import { serviceTel } from "@/utils/constData";
export default {
  data() {
    return {
      list: {
        hrContract: {
          title: "合同管理",
          msg: [
            "合同记录管理和电子合同管理，可自定义电子合同模板，发起电子签约。",
            "解决了纸质协议签约繁琐、不便于管理以及远程传递易丢失等痛点，为企业提供人员管理、模板管理、契约签订、契约归档的全套服务，提升效率，降低风险。",
          ],
        },
        salary: {
          title: "薪税管理",
          msg: [
            "为企业提供覆盖薪酬核算全流程的“企业一站式薪税管理”服务。涵盖员工入职定薪、调岗调薪、算薪算税、薪资发放、工资条发放、",
            "个税申报等场景，助力企业高效完成薪酬核算。无缝对接国税系统，薪资计算自动匹配专项附加扣除累计值和算税规则，一键完成个税报税。",
          ],
        },
      },
      title: "",
      msg: "",
      tel:
        window.env.server_env === "boc" || window.env.server_env === "cgb"
          ? serviceTel[window.env.server_env]
          : serviceTel.olading,
      businessCode: "",
      server_env: window.env.server_env ? window.env.server_env : "olading",
    };
  },
  watch: {
    $route: {
      handler(val) {
        const businessCode = val.query.bnsCode;
        this.businessCode = val.query.bnsCode;
        if (Object.keys(this.list).includes(businessCode)) {
          this.title = this.list[businessCode].title;
          this.msg = [...this.list[businessCode].msg];
        } else {
          Message.error("暂无当前应用开通方式，请联系管理员");
        }
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    async handleOpen() {
      let code =
        this.businessCode === "hrContract"
          ? "CONTRACT_MANAGEMENT"
          : "SALARY_MANAGER";
      let res = await apiGetAppId(code);
      if (res.success) {
        let token = this.$getToken();
        let linkUrl = environmentConfig.sso
          ? environmentConfig.sso
          : `http://${window.location.host}`;
        window.open(
          `${linkUrl}/open-server?token=${token}&&openId=${res.data}`,
          "_blank"
        );
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.open-auth {
  /*width: 1200px;*/
  text-align: center;
  padding: 30px;
  .msg {
    margin-top: 20px;
    text-align: left;
  }

  .link {
    // width: 260px;
    text-align: left;
    margin: 0 auto;
  }
  .table-name {
    font-weight: bold;
  }
}
</style>
