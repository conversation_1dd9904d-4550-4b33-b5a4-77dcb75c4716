<template>
  <div class="open-auth">
    <section>
      <h1 class="title">{{ title }}</h1>
      <div class="msg">
        {{ msg }}
      </div>
      <div class="link">
        <p style="margin: 20px 0">如需使用，请联系第三方供应商开通服务</p>
        <p>
          或拨打客服电话：<span class="tel">{{ tel }}</span>
        </p>
      </div>
      <div class="tips">
        <div v-for="(item, index) in tips" :key="index">
          <h5>{{ index + 1 }}.{{ item.title }}</h5>
          <p>{{ item.content }}</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { Message } from "element-ui";
import * as environmentConfig from "@/assets/js/environmentConfig";
import { apiGetAppId } from "../contractManage/store/api";
import { serviceTel } from "@/utils/constData";
export default {
  data() {
    return {
      list: {
        hrContract: {
          title: "合同管理（签约版）",
          msg: "为企业提供线上电子签约服务。支持自定义电子合同模板，一键发起签约，即时生成证明、协议、合同等各类文件；通过实名认证+活体认证，确保签署意愿；解决线下签约管理成本高、异地签署繁琐等痛点，助力企业高效完成合同签署工作。",
          tips: [
            {
              title: "收费标准：",
              content:
                "每月使用发起签约等功能时，按照合同份数计费，收取流量费用：0.8元/人，实际收费情况以供应商每月提供的结算单为准。",
            },
          ],
        },
        salary: {
          title: "薪资核算（计税版）",
          msg: "计税版薪资核算，为企业提供应发工资、个税、实发工资全流程的核算服务。无缝对接国税系统，可直接完成人员报送、专项附加扣除下载、税款计算、个税申报等，无需使用税局客户端系统导出累计算税数据，薪资计算自动匹配专项附加扣除累计值和算税规则，一键完成个税申报和缴款，助力企业高效完成薪酬核算及个税申报工作",
          tips: [
            {
              title: "收费说明：",
              content: `本系统使用第三方供应商的接口服务完成与税局系统的对接，按照供应商服务要求，每月按照实际调用接口人数收取流量费。该流量费统一供应商代收，与本系统使用服务无关。如有疑问，请咨询客户经理或致电客服热线${
                window.env.server_env === "boc" ||
                window.env.server_env === "cgb"
                  ? serviceTel[window.env.server_env]
                  : serviceTel.olading
              }。`,
            },
            {
              title: "收费标准：",
              content:
                "每月使用人员报送、专项附加扣除下载、税款计算、个税申报等功能时，按照身份证号码计算人数，根据人数收取流量费：0.55元/人，实际收费情况以供应商每月提供的结算单为准。",
            },
          ],
        },
      },
      title: "",
      msg: "",
      tel:
        window.env.server_env === "boc" || window.env.server_env === "cgb"
          ? serviceTel[window.env.server_env]
          : serviceTel.olading,
      tips: [],
      businessCode: "",
    };
  },
  computed: {
    ...mapState({
      isManager: (state) => state.isManager,
    }),
  },
  watch: {
    $route: {
      handler(val) {
        const businessCode = val.query.bnsCode;
        this.businessCode = val.query.bnsCode;
        if (Object.keys(this.list).includes(businessCode)) {
          this.title = this.list[businessCode].title;
          this.msg = this.list[businessCode].msg;
          this.tips = this.list[businessCode].tips;
        } else {
          Message.error("暂无当前应用开通方式，请联系管理员");
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    async handleOpen() {
      let code =
        this.businessCode === "hrContract"
          ? "CONTRACT_MANAGEMENT"
          : "SALARY_MANAGER";
      let res = await apiGetAppId(code);
      if (res.success) {
        let token = this.$getToken();
        let linkUrl = environmentConfig.sso[window.__CURRENT_ENV__]
          ? environmentConfig.sso[window.__CURRENT_ENV__]
          : `http://${window.location.host}`;
        window.open(
          `${linkUrl}/open-server?token=${token}&&openId=${res.data}`,
          "_blank"
        );
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../assets//scss/helpers.scss";
.open-auth {
  padding: 30px;
  /*width: 1200px;*/
  text-align: center;
  /*margin: 50px auto;*/
  // padding: 50px 100px 0 150px;
  h1 {
    font-weight: bold;
  }
  .link {
    text-align: left;
    margin: 0 auto;
  }
  .msg {
    margin: 20px auto 0 auto;
    text-align: left;
    line-height: 25px;
  }
  .title,
  .tel {
    font-weight: bold;
    color: $mainColor;
  }
  .tips {
    background: #f8f8f8;
    padding: 12px 8px;
    text-align: left;

    line-height: 25px;
    font-size: 14px;
    color: #555555;
    margin: 32px auto 0 auto;
    > div:not(:last-child) {
      margin-bottom: 25px;
    }
    h5 {
      font-size: 14px;
      color: #070f29;
    }
  }
}
</style>
