<template>
  <div class="import-data">
    <el-dialog
      :title="title"
      :visible.sync="isShowIncrease"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      class="diy-el_dialog"
    >
      <div>
        <p class="headings">1、选择导入匹配方式</p>
        <div class="diy-el_radio">
          <el-radio-group v-model="radio">
            <div v-for="(item, index) in radioList" :key="index">
              <el-radio :label="item.lable" @change="handleRadioValue">{{
                item.title
              }}</el-radio>
            </div>
          </el-radio-group>
        </div>
      </div>
      <div class="select-file">
        <el-upload
          class="avatar-uploader"
          :action="apiCheck"
          :limit="1"
          :headers="myHeaders"
          :file-list="fileList"
          :before-upload="beforeAvatarUpload"
          :on-remove="handleRemove"
          :on-success="handleSuccess"
          :data="parameterData"
        >
          <span class="headings">2、</span>
          <el-button size="small" type="primary">选择文件</el-button>
        </el-upload>
        <div v-show="uuid" style="margin: 15px 28px 0 28px">
          <span v-if="failCount === 0">
            <i class="el-icon-success"></i>数据全部校验通过
          </span>
          <span v-else-if="failCount !== 0 && successCount !== 0">
            <i class="el-icon-warning"></i>数据部分校验通过，有
            <strong style="color: red">{{ failCount }}</strong
            >条数据错误
          </span>
          <span v-else-if="successCount === 0">
            <i class="el-icon-error">数据全部未通过校验</i>
          </span>
          <span>
            <a @click="handleDownload" v-if="failCount !== 0" class="download"
              >下载日志</a
            >
          </span>
        </div>
        <p class="tips">
          支持xlsx和xls文件，文件不超过5M，
          <span>
            <a @click="handleTemplate">{{
              importStatus === "add" ? "下载模板" : "导出数据，修改后导入"
            }}</a>
          </span>
        </p>
        <p class="instructions">{{ tips }}</p>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="uploadFile">导入通过数据</el-button>
        <el-button @click="isShowIncrease = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="isShowIncreaseFinish"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="500px"
      class="importFinishDialog"
    >
      <div class="title"><i class="el-icon-success"></i>导入完成</div>
      <div class="importCount">
        导入成功
        <span style="color: #06b806">{{ importFinishForm.successCount }}</span>
        条数据, 导入失败
        <span style="color: red"> {{ importFinishForm.failCount }} </span>
        条数据。
      </div>
      <div>
        <a
          @click="handleDownload"
          v-if="importFinishForm.failCount !== 0"
          class="download"
        >
          下载日志
        </a>
      </div>
      <div slot="footer">
        <el-button type="primary" plain size="mini" @click="importMemberFinish">
          我知道了
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import utils from "@/utils/utils";
import { getToken } from "@olading/olading-business-ui";
export default {
  props: {
    radioList: Array,
    apiCheck: String, //校验接口
    apiDownloadLog: String, //下载日志接口
    apiDownloadTemplate: String, //下载模板
    parameterData: Object, //校验参数
    impoartAction: String, //导入通过数据接口  需为action
    title: String, //标题,
    sendRadio: String,
    uploadFileData: Object, //导入通过数据参数
    tips: String,
    downloadQueryObj: Object, //下载模板请求参数
    importStatus: String, //当前导入模板状态（新增或编辑）
  },
  data() {
    return {
      myHeaders: { Authorization: getToken() },
      isShowIncrease: false,
      importFinishForm: {
        failCount: "",
        successCount: "",
      },
      radio: "",
      isShowIncreaseFinish: false,
      failCount: 0,
      fileList: [],
      successCount: "",
      uuid: "",
    };
  },
  created() {
    this.radio = this.sendRadio;
  },
  methods: {
    handleTemplate() {
      if (this.importStatus === "add") {
        this.$store.dispatch(this.apiDownloadTemplate);
      } else {
        this.$store
          .dispatch(this.apiDownloadTemplate, this.downloadQueryObj)
          .then((res) => {
            console.log(res);
            utils.exportStream(res, "编辑人员导入.xls");
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    handleDownload() {
      this.$store.dispatch(this.apiDownloadLog, { uuid: this.uuid });
    },
    //改变radio
    show() {
      this.isShowIncrease = true;
      this.fileList = [];
      this.uuid = "";
    },
    handleRadioValue(value) {
      this.$emit("changeRadioValue", value);
    },
    uploadFile() {
      if (this.uuid === "") {
        this.$message.error("请选择文件");
        return;
      }
      this.uploadFileData.uuid = this.uuid;
      this.$store
        .dispatch(this.impoartAction, this.uploadFileData)
        .then((res) => {
          if (res.code === "0000") {
            let importData = res.data;
            this.importFinishForm.failCount = importData.failCount;
            this.importFinishForm.successCount = importData.successCount;
            this.isShowIncrease = false;
            this.isShowIncreaseFinish = true;
          }
        });
    },
    beforeAvatarUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isxls = testmsg === "xls" || testmsg === "xlsx";
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isxls) {
        this.$message({
          message: "上传文件类型只能是 xls,xlsx 格式!",
          type: "warning",
        });
        this.fileList = [];
      }
      if (!isLt5M) {
        this.$message({
          message: "上传文件大小不能超过 5MB!",
          type: "warning",
        });
        this.fileList = [];
      }
      return isxls && isLt5M;
    },
    handleSuccess(res, file) {
      if (res.code === "0000") {
        let data = res.data;
        this.successCount = data.successCount;
        this.failCount = data.failCount;
        this.uuid = data.uuid;
        this.fileList = this.failCount > 0 ? [] : this.fileList;
      } else {
        this.$message.warning(res.message);
        this.fileList = [];
      }
    },
    //删除文件
    handleRemove(file, fileList) {
      if (file && file.status === "success") {
        this.uuid = "";
      }
    },
    importMemberFinish() {
      this.isShowIncrease = false;
      this.isShowIncreaseFinish = false;
      this.$emit("getLoading");
    },
  },
};
</script>
<style lang="scss" scoped>
.import-data {
  .download {
    cursor: pointer;
  }
  .importCount {
    margin: 10px auto;
  }
  /deep/ .el-dialog__header {
    padding-left: 28px;
    background: #fff;
  }
  /deep/ .el-dialog__body {
    padding: 25px 25px 30px;
  }
  /deep/ .el-dialog__footer {
    text-align: center;
  }
  .tips {
    color: #1f2329;
    span {
      margin-left: 0;
    }
  }
  .instructions {
    color: #1f2329;
  }
  /deep/ .importFinishDialog .el-dialog__header {
    display: none;
  }
  /deep/ .importFinishDialog .el-dialog__body {
    padding: 0 50px;
    padding-top: 50px;
  }
  /deep/ .importFinishDialog .el-dialog__footer {
    text-align: right;
  }
}
</style>
