<template>
  <div class="employee-Info">
    <div class="tax el-diy-month">
      <div class="attrition-content">
        <div class="screening">
          <div class="check-staff-menu">
            <div>
              <el-button @click="isShowScreening = true">筛选</el-button>
              <el-input
                placeholder="请输入姓名\工号\身份证号"
                v-model="ruleForm.key"
                prefix-icon="iconiconfonticonfontsousuo1 iconfont"
                @keyup.enter.native="handleSearch"
                clearable
                class="search-input"
              ></el-input>
            </div>
            <div>
              <el-button
                type="primary"
                class="add-import"
                @click="addEmployee"
                v-if="privilegeVoList.includes('salary.compute.emp.addEmp')"
                >新增人员</el-button
              >
              <!-- <el-button class="add-import" @click="batchImport" v-if="privilegeVoList.includes('salary.compute.emp.importEmp')">新增导入</el-button> -->
              <el-dropdown
                trigger="click"
                class="employee-more-operation"
                style="margin: 0 10px"
              >
                <el-button>
                  批量操作
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    @click.native="batchImport('add')"
                    v-if="
                      privilegeVoList.includes('salary.compute.emp.importEmp')
                    "
                    >新增导入</el-dropdown-item
                  >
                  <el-dropdown-item
                    @click.native="batchImport('edit')"
                    v-if="
                      privilegeVoList.includes('salary.compute.emp.importEmp')
                    "
                    >编辑导入</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
              <el-button
                class="more-choose"
                @click="isShowExport = true"
                v-if="privilegeVoList.includes('salary.compute.emp.export')"
                >导出</el-button
              >
            </div>
          </div>
          <div class="drop-down" style="margin-top: 0">
            公司名称:
            <el-select
              v-model="ruleForm.queryFilterParam.taxSubId"
              placeholder="请选择"
              @change="changeSubTaxId"
              clearable
            >
              <el-option
                v-for="item in taxSubjectInfoList"
                :key="item.taxSubId"
                :label="item.taxSubName"
                :value="item.taxSubId"
              ></el-option>
            </el-select>
          </div>
          <div class="staff-table">
            <el-table
              v-loading="loading"
              :data="list"
              class="check-staff_table"
              :style="{ width: screenWidth - 255 + 'px' }"
              border
            >
              <el-table-column
                type="index"
                label="编号"
                width="50"
              ></el-table-column>
              <el-table-column prop="name" label="姓名" width="140">
                <template slot-scope="scope">
                  <span
                    class="table-name"
                    @click="handleEmplayeeName(scope.row)"
                    >{{ scope.row.empName }}</span
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="empNo"
                label="工号"
                width="140"
              ></el-table-column>
              <el-table-column
                prop="idNo"
                label="证件号码"
                width="180"
              ></el-table-column>
              <el-table-column
                prop="taxSubName"
                label="公司名称"
                width="180"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column prop="empType" label="用工性质" min-width="140">
                <template slot-scope="scope">{{
                  scope.row.empType | filterEmpType
                }}</template>
              </el-table-column>
              <el-table-column
                prop="empStatus"
                label="员工状态"
                min-width="140"
              >
                <template slot-scope="scope">{{
                  scope.row.empStatus | filterEmployStatus
                }}</template>
              </el-table-column>
              <el-table-column
                prop="idValidStatus"
                label="入职日期"
                min-width="140"
              >
                <template slot-scope="scope">{{ scope.row.empDay }}</template>
              </el-table-column>
              <el-table-column
                prop="turnRegularDate"
                label="转正日期"
                min-width="140"
              ></el-table-column>
              <el-table-column label="操作" fixed="right" min-width="140px">
                <template slot-scope="scope">
                  <span
                    class="funStyle"
                    @click="onChange(scope.row)"
                    v-if="
                      scope.row.empStatus == 'ON_THE_JOB' &&
                      privilegeVoList.includes(
                        'salary.compute.emp.updateCompany'
                      )
                    "
                    >变更</span
                  >
                  <span
                    class="funStyle"
                    @click="handleDelete(scope.row)"
                    v-if="privilegeVoList.includes('salary.compute.emp.delEmp')"
                    >删除</span
                  >
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              @current-change="handleSelectionChange"
              @size-change="handleSizeChange"
              :current-page="ruleForm.currPage"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[20, 50, 100, 200]"
              :total="total"
              class="staff-page"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- 变更公司-->
    <companyChange
      @getList="getList"
      :companyOption="taxSubjectInfoList"
      ref="companyChange"
    ></companyChange>
    <!--    筛选-->
    <el-dialog
      title="筛选"
      :visible.sync="isShowScreening"
      width="52%"
      ref="screenForm"
      class="screen-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        :model="ruleForm.queryFilterParam"
        ref="screenForm"
        label-width="180px"
        class="demo-ruleForm"
      >
        <div class="shortCon">
          <el-form-item label="用工性质">
            <radios
              ref="enumEmpType"
              radiosRef="enumEmpType"
              :screenOption="enumEmpTypeOption"
              @handleRadio="handleEnumEmpTypes"
            ></radios>
          </el-form-item>
        </div>
        <div class="shortCon">
          <el-form-item label="员工状态">
            <radios
              ref="employStatus"
              radiosRef="employStatus"
              :screenOption="employStatusOption"
              @handleRadio="handleEnumEmpStatuses"
            ></radios>
          </el-form-item>
        </div>
        <div class="shortCon">
          <el-form-item label="部门">
            <el-input v-model="ruleForm.queryFilterParam.departName"></el-input>
          </el-form-item>
        </div>
        <div class="shortCon">
          <el-form-item label="岗位">
            <el-input
              v-model="ruleForm.queryFilterParam.positionName"
            ></el-input>
          </el-form-item>
        </div>
        <div class="shortCon">
          <el-form-item label="工作城市">
            <el-select
              v-model="ruleForm.queryFilterParam.workCity"
              placeholder="请选择"
              filterable
              clearable
            >
              <el-option
                v-for="(item, index) in cityList"
                :label="item.name"
                :value="item.code"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="入职日期">
          <el-date-picker
            v-model="ruleForm.queryFilterParam.enterStartTime"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期"
          ></el-date-picker
          >至
          <el-date-picker
            v-model="ruleForm.queryFilterParam.enterEndTime"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="是否转正">
          <el-radio-group
            v-model="ruleForm.queryFilterParam.regularEmpYn"
            size="small"
          >
            <el-radio-button label>不限</el-radio-button>
            <el-radio-button label="1">是</el-radio-button>
            <el-radio-button label="0">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="转正日期">
          <el-date-picker
            v-model="ruleForm.queryFilterParam.turnRegularStartTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="date"
            placeholder="选择日期"
          ></el-date-picker
          >至
          <el-date-picker
            v-model="ruleForm.queryFilterParam.turnRegularEndTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="date"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="离职日期">
          <el-date-picker
            v-model="ruleForm.queryFilterParam.lastEmployStartTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="date"
            placeholder="选择日期"
          ></el-date-picker
          >至
          <el-date-picker
            v-model="ruleForm.queryFilterParam.lastEmployEndTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="date"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="selectScreen">查询</el-button>
        <el-button @click="resetSreen">重置</el-button>
      </div>
    </el-dialog>
    <!--    导出-->
    <el-dialog
      title="选择导出项"
      :visible.sync="isShowExport"
      width="600px"
      class="exportDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div
        v-for="(item, index) in checkAllList"
        :style="{ marginTop: index != 0 ? '10px' : '' }"
        :key="index"
      >
        <el-checkbox
          :indeterminate="item.isIndeterminate"
          v-model="item.checkAll"
          @change="checkAllInfo(index)"
        >
          <b>{{ item.title }}</b>
        </el-checkbox>
        <div style="margin: 10px 0px; border-bottom: 1px solid #e5e5e5"></div>
        <el-checkbox-group
          v-model="item.checkedInfo"
          @change="checkInfo(index)"
        >
          <el-checkbox
            v-for="it in item.options"
            :label="it"
            :key="it"
            class="checkBoxStyle"
            >{{ it }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="handleExport">确定</el-button>
        <el-button @click="isShowExport = false">取消</el-button>
      </div>
    </el-dialog>
    <!-- 批量导入 -->
    <import-data
      ref="import"
      :radioList="radioList"
      :title="importStatus === 'add' ? '新增人员导入' : '人员信息批量编辑'"
      :apiCheck="
        importStatus === 'add'
          ? baseUrl + '/api/hrsaas-salary/enterprise/employees/importVerify'
          : baseUrl +
            '/api/hrsaas-salary/enterprise/employees-edit/importVerify'
      "
      :apiDownloadLog="
        importStatus === 'add'
          ? 'payMasterStore/actionEmployeesVerifyErrorLog'
          : 'payMasterStore/actionEditEmployeesVerifyErrorLog'
      "
      :apiDownloadTemplate="
        importStatus === 'add'
          ? 'payMasterStore/actionEmployeesTemplate'
          : 'payMasterStore/actionDownloadEditTemplate'
      "
      :downloadQueryObj="ruleForm"
      :importStatus="importStatus"
      :parameterData="parameterData"
      sendRadio="BY_ID_NO"
      @changeRadioValue="changeRadioValue"
      :impoartAction="
        importStatus === 'add'
          ? 'payMasterStore/actionEmployeesImport'
          : 'payMasterStore/actionEmployeesEditImport'
      "
      @getLoading="getList"
      :uploadFileData="uploadFileData"
      :tips="'说明：导入后，仅导入文件中非空单元格对应的字段的值'"
    ></import-data>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { baseUrl } from "@/request/fetch";
import * as constData from "./util/constData";
import companyChange from "./components/companyChange";
import importData from "./components/importData";
import radios from "@/components/tool/radios";
import fun from "@/util/fun";
let date = fun.headDate();
let defaultDate =
  date.year + "年" + (date.month >= 10 ? date.month : "0" + date.month) + "月";
export default {
  data() {
    return {
      activeName: "emplyeeInfo",
      taxSubjectInfoList: [
        {
          //扣缴义务人枚举
          taxSubId: 0,
          taxSubName: "全部",
        },
      ],
      isShowScreening: false,
      ruleForm: {
        key: "",
        currPage: 1,
        pageSize: 20,
        queryFilterParam: {
          enumEmpTypes: [], //用工性质，
          enumEmpStatuses: ["ON_THE_JOB"], //员工状态
          departName: "", //部门
          positionName: "", //岗位
          taxSubId: "",
          workCity: "", //工作城市
          enterStartTime: "", //入职筛选开始时间
          enterEndTime: "",
          regularEmpYn: "", //是否转正
          turnRegularStartTime: "", //转正开始时间
          turnRegularEndTime: "",
          lastEmployStartTime: "",
          lastEmployEndTime: "",
        },
      },
      enumEmpTypeOption: constData.enumEmpTypeOption,
      employStatusOption: constData.employStatusOption,
      companyItem: "",
      screenWidth: document.body.clientWidth, // 屏幕尺寸
      screenHeight: document.body.clientHeight - 290,
      selectMonth: defaultDate,
      list: [],
      total: 0,
      loading: false,
      isShowCompany: false,
      checkAll: false,
      isShowExport: false,
      checkAllList: [
        {
          title: "基本信息",
          checkAll: true,
          checkedInfo: [
            "姓名",
            "工号",
            "证件类型",
            "证件号码",
            "性别",
            "出生日期",
            "国籍",
            "手机号码",
            "最高学历",
            "参加工作日期",
            "户口性质",
            "户口所在城市",
            "婚姻状态",
            "民族",
            "工资卡开户银行",
            "工资银行账号",
          ],
          options: [
            "姓名",
            "工号",
            "证件类型",
            "证件号码",
            "性别",
            "出生日期",
            "国籍",
            "手机号码",
            "最高学历",
            "参加工作日期",
            "户口性质",
            "户口所在城市",
            "婚姻状态",
            "民族",
            "工资卡开户银行",
            "工资银行账号",
          ],
          isIndeterminate: false,
        },
        {
          title: "公司信息",
          checkAll: true,
          checkedInfo: [
            "公司名称",
            "用工性质",
            "部门",
            "岗位",
            "入职日期",
            "工作城市",
            "是否转正",
            "转正日期",
            "员工状态",
            "最后工作日",
          ],
          options: [
            "公司名称",
            "用工性质",
            "部门",
            "岗位",
            "入职日期",
            "工作城市",
            "是否转正",
            "转正日期",
            "员工状态",
            "最后工作日",
          ],
          isIndeterminate: false,
        },
      ],
      radioList: [{ lable: "BY_ID_NO", title: "通过身份证匹配人员" }],
      parameterData: {
        importType: "BY_ID_NO",
      },
      uploadFileData: {
        uuid: "",
        importType: "BY_ID_NO",
      },
      importStatus: "", //当前导入状态（新增或者编辑）
      baseUrl: baseUrl,
    };
  },
  components: {
    companyChange,
    radios,
    importData,
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
      cityList: (state) => state.cityList,
    }),
  },
  created() {
    this.getList();
    //公司列表
    this.getTaxSubjectInfoList();
  },
  mounted() {},
  methods: {
    handleClick() {},
    //调动
    onChange(data) {
      this.companyItem = data;
      this.$refs.companyChange.showCompany(data);
    },
    getList() {
      this.loading = true;
      this.$store
        .dispatch("payMasterStore/actionGetEmployeeList", this.ruleForm)
        .then((res) => {
          if (res.success) {
            this.list = res.data.data;
            this.total = res.data.count;
            this.loading = false;
          }
        });
    },
    //批量导入
    batchImport(val) {
      this.importStatus = val;
      if (val === "add") {
        this.radioList = [{ lable: "BY_ID_NO", title: "通过证件号码匹配人员" }];
      } else {
        this.radioList = [{ lable: "BY_ID_NO", title: "通过证件号码匹配人员" }];
      }
      this.$refs.import.show();
    },
    //导入 按钮切换
    changeRadioValue(val) {
      this.parameterData.importType = val;
      this.uploadFileData.importType = val;
    },
    //删除
    handleDelete(data) {
      this.$confirm("是否删除所选人员", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          this.$store
            .dispatch("payMasterStore/actionDeleteEmployee", data.empId)
            .then((res) => {
              if (res.success) {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                this.getList();
              }
            });
        })
        .catch(() => {});
    },
    //用工性质
    handleEnumEmpTypes(val) {
      this.ruleForm.queryFilterParam.enumEmpTypes = val;
    },
    //员工特征
    handleEnumEmpStatuses(val) {
      this.ruleForm.queryFilterParam.enumEmpStatuses = val;
    },
    //筛选查询
    selectScreen() {
      this.getList();
      this.isShowScreening = false;
    },
    //筛选重置
    resetSreen() {
      for (let key in this.ruleForm.queryFilterParam) {
        if (
          !["enumEmpTypes", "enumEmpStatuses", "regularEmpYn"].includes(key)
        ) {
          this.ruleForm.queryFilterParam[key] = "";
          this.ruleForm.queryFilterParam.regularEmpYn = "";
          this.$refs.enumEmpType.changeNoType();
          this.$refs.employStatus.changeNoType();
        }
      }
    },
    //扣缴义务人列表
    getTaxSubjectInfoList() {
      this.$store.dispatch("taxPageStore/actionTaxSubjectList").then((res) => {
        if (res.success) {
          this.taxSubjectInfoList = [
            { taxSubId: 0, taxSubName: "全部" },
          ].concat(res.data);
          this.ruleForm.queryFilterParam.taxSubId =
            this.taxSubjectInfoList[0].taxSubId;
        }
      });
    },
    //切换扣缴义务人
    changeSubTaxId() {
      this.getList();
    },
    // 关闭更改侧滑框
    hanleClose(isFresh) {
      if (isFresh) {
        this.getList();
      }
    },
    //新增人员
    addEmployee() {
      this.$router.push("/addEmployee");
    },
    //员工详情
    handleEmplayeeName(data) {
      this.$router.push({
        path: "/employeeDetail",
        query: {
          bankId: data.bankId,
          compEmpId: data.compEmpId,
          empId: data.empId,
        },
      });
    },
    //人员基本信息
    checkAllInfo(index) {
      this.checkAllList[index]["checkedInfo"] = this.checkAllList[index][
        "checkAll"
      ]
        ? this.checkAllList[index]["options"]
        : [];
      this.checkAllList[index]["isIndeterminate"] = false;
    },
    checkInfo(index) {
      let checkedCount = this.checkAllList[index]["checkedInfo"].length;
      this.checkAllList[index]["checkAll"] =
        checkedCount === this.checkAllList[index]["options"].length;
      this.checkAllList[index]["isIndeterminate"] =
        checkedCount > 0 &&
        checkedCount < this.checkAllList[index]["options"].length;
    },
    //导出
    handleExport() {
      let exportCompEmpItems = this.checkAllList[0]["checkedInfo"];
      let exportCompInfoItems = this.checkAllList[1]["checkedInfo"];
      this.$store
        .dispatch("payMasterStore/actionEmployeeExport", {
          exportCompEmpItems,
          exportCompInfoItems,
          queryParam: this.ruleForm,
        })
        .then((res) => {
          this.isShowExport = false;
        });
    },
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.ruleForm.currPage = 1;
      this.getList();
    },
    handleSearch() {
      this.ruleForm.currPage = 1;
      this.getList();
    },
    //翻页
    handleSelectionChange(val) {
      this.ruleForm.currPage = val;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.employee-Info {
  .header {
    border-bottom: 1px solid #ededed;
    .add-table {
      cursor: pointer;
      float: right;
      color: $mainColor;
    }
    .iconxinzeng {
      font-size: 18px;
      color: #9c9c9c;
      position: relative;
      top: 1px;
    }
  }
  .attrition-content {
    padding: 0 20px;
    .content-header {
      display: inline-block;
      font-size: 16px;
      margin: 0px 0px 6px 20px;
      i {
        font-size: 16px;
        color: #ccc;
      }
      .rotate-el-icon-arrow-left {
        transform: rotate(180deg);
      }
      span {
        position: absolute;
        left: 32px;
        top: 3px;
        z-index: 0;
      }
    }
    .button-style {
      margin: 0 20px;
    }
  }
  .screening {
    .iconiconfonticonfontsousuo1 {
      font-size: 12px;
    }
    .staff-situation {
      margin: 14px 0px;
      color: #999;
      font-size: 12px;
      .staff-total {
        padding-right: 15px;
        margin-right: 15px;
      }
      i {
        font-style: normal;
        padding: 0 3px;
      }
      em {
        color: #333;
        font-style: normal;
      }
    }
  }
  .wait-report {
    margin-right: 20px;
    cursor: pointer;
    .num {
      font-weight: bold;
      color: $mainColor;
    }
    .active {
      color: #ff9500;
    }
  }
  .iconsanjiao {
    font-size: 22px;
  }
}
.screen-dialog {
  .shortCon {
    width: 450px;
  }
  .el-select {
    width: 100%;
  }
}
.exportDialog {
  .checkBoxStyle {
    display: inline-block;
    padding: 5px 0px;
  }
}
</style>
