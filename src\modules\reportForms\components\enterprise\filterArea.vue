<template>
  <div>
    <div class="operation check-staff-menu">
      <div>
        <div class="content-header" style="margin: 0 10px">
          <el-date-picker
            style="width: 180px"
            v-model="reportForm.queryMonth"
            type="month"
            placeholder="请选择月份"
            @change="updateTaxSubjectInfoList"
            value-format="yyyy-MM"
            :editable="false"
            :clearable="false"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </div>
        <el-select
          style="margin-right: 10px"
          v-model="reportForm.areaId"
          @change="handleChangeArea"
          clearable
        >
          <el-option
            v-for="item in areaList"
            :key="item.id"
            :label="item.areaName"
            :value="item.id"
          ></el-option>
        </el-select>
        <el-select
          style="width: 260px"
          v-model="reportForm.taxSubjectIds"
          placeholder="请选择公司"
          @change="emitSearch"
          filterable
          clearable
          multiple
          collapse-tags
        >
          <el-option
            v-for="(item, index) in taxSubjectInfoList"
            :label="item.taxSubName"
            :value="item.taxSubId"
            :key="index"
          >
          </el-option>
        </el-select>
      </div>
      <slot name="operation" />
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

const currentDate = () => {
  const data = new Date();
  const year = data.getFullYear();
  const month = String(data.getMonth() + 1).padStart(2, "0");
  return `${year}-${month}`;
};

export default {
  data() {
    return {
      reportForm: {
        taxSubjectIds: [],
        areaId: "",
        queryMonth: currentDate(),
      },
      taxSubjectInfoList: [],
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() < new Date(2019, 0).getTime() ||
            time.getTime() > new Date()
          );
        },
      },
    };
  },
  computed: {
    ...mapState({
      areaList: (state) => state.areaList,
    }),
  },
  created() {
    this.updateTaxSubjectInfoList();
  },
  methods: {
    handleChangeArea() {
      this.emitSearch();
    },
    emitSearch() {
      this.$emit("search", {
        ...this.reportForm,
      });
    },
    updateMonth(val) {
      this.reportForm.queryMonth = val;
      this.emitSearch();
    },
    updateTaxSubjectInfoList() {
      this.loadTaxSubjectInfoList();
      this.emitSearch();
    },
    async loadTaxSubjectInfoList() {
      this.$store
        .dispatch("taxPageStore/actionTaxSubjectInfoList", {
          date: this.reportForm.queryMonth,
        })
        .then((res) => {
          if (!res.success) return;
          this.taxSubjectInfoList = res.data;
        });
    },
  },
};
</script>
