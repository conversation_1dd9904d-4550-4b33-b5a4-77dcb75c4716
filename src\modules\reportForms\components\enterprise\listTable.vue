<template>
  <div class="staff-table">
    <el-table :data="tableData" class="check-staff_table" border>
      <el-table-column type="index" label="序号" />
      <el-table-column
        prop="taxSubName"
        label="公司名称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="areaName"
        label="区域名称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="skssq" label="税款所属期" width="100" />
      <el-table-column prop="sbsj" label="申报时间" width="140" />
      <el-table-column prop="dqsre" label="当期收入额" width="120">
        <template slot-scope="scope">
          <div style="text-align: right">
            {{ scope.row.dqsre || "-" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="sbrc" label="申报人次" width="100" />
      <el-table-column prop="ybtse" label="应补(退)税额" width="120">
        <template slot-scope="scope">
          <div style="text-align: right">
            {{ scope.row.ybtse ? scope.row.ybtse : "-" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="sblx" label="申报类型" width="140">
        <template slot-scope="scope">
          <div>
            {{ scope.row.sblx || "-" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="caption"
        label="报表名称"
        width="140"
        show-overflow-tooltip
      />
      <el-table-column prop="sbzt" label="申报状态" width="160">
        <template slot-scope="scope">
          <div>
            {{ scope.row.sbzt || "-" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="下载状态" width="160">
        <template slot-scope="scope">
          <span style="margin-right: 3px">
            {{
              scope.row.downloadStatus
                ? downloadStatusObj[scope.row.downloadStatus]
                : "-"
            }}
          </span>
          <el-popover
            v-if="scope.row.downloadStatus === 'FAIL'"
            placement="top-start"
            width="200"
            trigger="hover"
            :content="scope.row.failReason"
          >
            <a style="cursor: pointer" slot="reference">查看原因</a>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="150">
        <template slot-scope="scope">
          <el-button
            v-if="
              privilegeVoList.includes('salary.report.record.reportFormDownload')
            "
            type="text"
            @click="getDeclarationForm(scope.row)"
            >获取申报表</el-button
          >
          <el-dropdown
            trigger="click"
            placement="bottom"
            style="margin-left: 10px"
          >
            <el-button type="text">更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="
                  privilegeVoList.includes(
                    'salary.report.record.reportFormDownloadQuery'
                  )
                "
                @click.native="getReportFormDownloadQuery(scope.row)"
              >
                获取反馈
              </el-dropdown-item>
              <el-dropdown-item
                v-if="
                  privilegeVoList.includes(
                    'salary.report.record.reportFormExport'
                  )
                "
                @click.native="handleDownload(scope.row)"
              >
                下载申报表
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="padding-bottom: 13px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-size="pageSize"
      :current-page="currPage"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      :page-sizes="[20, 50, 100, 200]"
      class="staff-page"
    />
  </div>
</template>

<script>
import * as SCR from "../../util/constData";
import { mapState } from "vuex";

export default {
  props: {
    pageSize: {
      default: 20,
      type: Number,
    },
    currPage: {
      default: 1,
      type: Number,
    },
    total: {
      default: 20,
      type: Number,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    source: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      downloadStatusObj: SCR.downloadStatus,
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  methods: {
    handleSizeChange(size) {
      this.$emit("size-change", size);
    },
    handleCurrentChange(curr) {
      this.$emit("current-change", curr);
    },
    getDeclarationForm(row) {
      this.$emit("handleDeclarationForm", row);
    },
    getReportFormDownloadQuery(row) {
      this.$emit("getReportFormDownloadQuery", row);
    },
    handleDownload(row) {
      this.$emit("download", row.fileId);
    },
  },
};
</script>
