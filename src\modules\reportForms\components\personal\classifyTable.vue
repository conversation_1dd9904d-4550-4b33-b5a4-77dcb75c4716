<template>
  <div>
    <el-table :data="tableData" class="check-staff_table" border>
      <el-table-column type="index" label="序号" fixed />
      <el-table-column
        prop="empName"
        label="姓名"
        width="180"
        fixed
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="taxSubName"
        label="公司名称"
        width="180"
        fixed
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="areaName"
        label="区域名称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="mobile" label="手机号" width="180" />
      <el-table-column prop="idNo" label="证件号码" width="180" />
      <el-table-column prop="monthDate" label="税款所属期" width="100" />
      <el-table-column prop="sdxm" label="所得项目" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.sdxm || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sr" label="收入" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.sr || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="mssr" label="免税收入" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.mssr || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="sqkcxmccyz"
        label="税前扣除项目财产原值"
        width="180"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.sqkcxmccyz || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="sqkcxmyxkcdsf"
        label="税前扣除项目允许扣除的税费"
        width="190"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.sqkcxmyxkcdsf || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="sqkcxmqt" label="税前扣除项目其他" width="140">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.sqkcxmqt || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="jcfy" label="减除费用" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.jcfy || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="zykcdjze" label="准予扣除的捐赠额" width="140">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.zykcdjze || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="jajsbl" label="减按计税比例" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.jajsbl || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="ynssde" label="应纳税所得额" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ynssde || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="sl" label="税率" width="100">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.sl || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="sskcs" label="速算扣除数" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.sskcs || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="ynse" label="应纳税额" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ynse || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="jmse" label="减免税额" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.jmse || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="yingkjse" label="应扣缴税额" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.yingkjse || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="yikjse" label="已缴税额" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.yikjse || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="ybtse" label="应补(退)税额" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ybtse || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="bz" label="备注" width="120">
        <template slot-scope="scope">
          <div>{{ scope.row.bz || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="下载状态" width="150">
        <template slot-scope="scope">
          <span style="margin-right: 3px">
            {{
              scope.row.downloadStatus
                ? downloadStatusObj[scope.row.downloadStatus]
                : "-"
            }}
          </span>
          <el-popover
            v-if="scope.row.downloadStatus === 'FAIL'"
            placement="top-start"
            width="200"
            trigger="hover"
            :content="scope.row.failReason"
          >
            <a style="cursor: pointer" slot="reference">查看原因</a>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import * as SCR from "../../util/constData";
export default {
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      downloadStatusObj: SCR.downloadStatus,
    };
  },
};
</script>
