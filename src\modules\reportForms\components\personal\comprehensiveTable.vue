<template>
  <div>
    <el-table :data="tableData" class="check-staff_table" border>
      <el-table-column type="index" label="序号" fixed />
      <el-table-column
        prop="empName"
        label="姓名"
        width="180"
        fixed
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="taxSubName"
        label="公司名称"
        width="180"
        fixed
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="areaName"
        label="区域名称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="mobile" label="手机号" width="180" />
      <el-table-column prop="idNo" label="证件号码" width="180" />
      <el-table-column prop="monthDate" label="税款所属期" width="100" />
      <el-table-column prop="sdxm" label="所得项目" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.sdxm || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="bqsr" label="本期收入" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.bqsr || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="bqfy" label="本期费用" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.bqfy || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="bqmssr" label="本期免税收入" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.bqmssr || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="bqjcfy" label="本期减除费用" width="120">
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.bqjcfy || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="jbylaobxf"
        label="本期专项扣除基本养老保险"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">
            {{ scope.row.jbylaobxf || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="jbylbxf"
        label="本期专项扣除基本医疗保险"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">
            {{ scope.row.jbylbxf || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="sybxf"
        label="本期专项扣除失业保险"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">
            {{ scope.row.sybxf || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="zfgjj"
        label="本期专项扣除住房公积金"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.zfgjj || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="nj"
        label="本期其他扣除年金"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.nj || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="syjkbx"
        label="本期其他扣除商业健康保险"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.syjkbx || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="syylbx"
        label="本期其他扣除税延养老保险"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">
            {{ scope.row.syylbx || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="yxkcdsf"
        label="本期允许扣除税费"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.yxkcdsf || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="qt"
        label="本期其他扣除其他"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.qt || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="bqzykcdjze"
        label="本期准予扣除捐赠额"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.bqzykcdjze || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljsr"
        label="累计收入"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljsr || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljjcfy"
        label="累计减除费用"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljjcfy || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljzxkc"
        label="累计专项扣除"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljzxkc || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljznjy"
        label="累计专项附加扣除子女教育"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljznjy || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljsylr"
        label="累计专项附加扣除赡养老人"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljsylr || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljzfdklx"
        label="累计专项附加扣除住房贷款利息"
        width="210"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljzfdklx || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljzfzj"
        label="累计专项附加扣除住房租金"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljzfzj || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljjxjy"
        label="累计专项附加扣除继续教育"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljjxjy || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljyyezhf"
        label="累计专项附加扣除3岁以下婴幼儿照护费用"
        width="270"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">
            {{ scope.row.ljyyezhf || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljzxfjkchj"
        label="累计专项附加扣除合计"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljzxfjkchj || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljgrylj"
        label="累计个人养老金"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">
            {{ scope.row.ljgrylj || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljqtkc"
        label="累计其他扣除"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljqtkc || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljzykcdjze"
        label="累计准予扣除的捐赠额"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljzykcdjze || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljynssde"
        label="累计应纳税所得额"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ljynssde || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="sl"
        label="税率(%)"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.sl || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="sskcs"
        label="速算扣除数"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.sskcs || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljynse"
        label="累计应纳税额"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">
            {{ scope.row.ljynse || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljjmse"
        label="累计减免税额"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">
            {{ scope.row.ljjmse || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ljykjse"
        label="累计应扣缴税额"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">
            {{ scope.row.ljykjse || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ykjse"
        label="累计已预缴税额"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ykjse || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ybtse"
        label="累计应补(退)税额"
        width="180"
        :key="Math.random()"
      >
        <template slot-scope="scope">
          <div class="number-right">{{ scope.row.ybtse || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="bz"
        label="备注"
        :show-overflow-tooltip="true"
        width="140"
      >
      </el-table-column>
      <el-table-column label="下载状态" width="150">
        <template slot-scope="scope">
          <span style="margin-right: 3px">
            {{
              scope.row.downloadStatus
                ? downloadStatusObj[scope.row.downloadStatus]
                : "-"
            }}
          </span>
          <el-popover
            v-if="scope.row.downloadStatus === 'FAIL'"
            placement="top-start"
            width="200"
            trigger="hover"
            :content="scope.row.failReason"
          >
            <a style="cursor: pointer" slot="reference">查看原因</a>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import * as SCR from "../../util/constData";
export default {
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      downloadStatusObj: SCR.downloadStatus,
    };
  },
};
</script>
