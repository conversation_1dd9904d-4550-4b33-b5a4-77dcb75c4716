<template>
  <div>
    <div class="operation check-staff-menu">
      <div>
        <el-select
          style="margin-right: 10px"
          v-model="reportForm.areaId"
          @change="handleChangeArea"
          clearable
        >
          <el-option
            v-for="item in areaList"
            :key="item.id"
            :label="item.areaName"
            :value="item.id"
          ></el-option>
        </el-select>
        <el-select
          style="width: 260px"
          v-model="reportForm.taxSubjectIds"
          placeholder="请选择公司"
          @change="emitSearch"
          filterable
          clearable
          multiple
          collapse-tags
        >
          <el-option
            v-for="(item, index) in taxSubjectInfoList"
            :label="item.taxSubName"
            :value="item.taxSubId"
            :key="index"
          >
          </el-option>
        </el-select>
        <el-date-picker
          style="width: 180px; margin: 0 10px"
          v-model="reportForm.queryMonth"
          type="month"
          placeholder="请选择税款所属期"
          @change="updateTaxSubjectInfoList"
          value-format="yyyy-MM"
          :editable="false"
          :clearable="false"
          :picker-options="pickerOptions"
        ></el-date-picker>
        <el-input
          placeholder="请输入员工姓名\证件号码"
          v-model="reportForm.nameOrMore"
          prefix-icon="iconiconfonticonfontsousuo1 iconfont"
          clearable
          @keyup.enter.native="emitSearch"
          class="search-input"
        ></el-input>
      </div>
      <slot name="operation" />
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

const currentDate = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  return `${year}-${month}`;
};
export default {
  data() {
    return {
      reportForm: {
        areaId: "",
        taxSubjectIds: [],
        queryMonth: currentDate(),
        nameOrMore: "",
      },
      taxSubjectInfoList: [],
      departmentList: [],
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() < new Date(2019, 0).getTime() ||
            time.getTime() > new Date()
          );
        },
      },
    };
  },
  computed: {
    ...mapState({
      areaList: (state) => state.areaList,
    }),
  },
  created() {
    this.updateTaxSubjectInfoList();
  },
  methods: {
    handleChangeArea() {
      this.emitSearch();
    },
    updateMonth(val) {
      this.reportForm.queryMonth = val;
      this.emitSearch();
    },
    emitSearch() {
      this.$emit("search", {
        ...this.reportForm,
      });
    },
    updateTaxSubjectInfoList() {
      this.loadTaxSubjectInfoList();
      this.emitSearch();
    },
    async loadTaxSubjectInfoList() {
      this.$store
        .dispatch("taxPageStore/actionTaxSubjectInfoList", {
          date: this.reportForm.queryMonth,
        })
        .then((res) => {
          if (!res.success) return;
          this.taxSubjectInfoList = res.data;
        });
    },
  },
};
</script>
