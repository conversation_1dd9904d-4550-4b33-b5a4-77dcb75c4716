<template>
  <draggable
    :group="groupConfig"
    :sort="true"
    v-model="listArr"
    @add="addItem"
    @start="startDrag"
    @move="onMove"
    @end="removeItem"
    @change="changeItem"
    class="draggable-cotainer"
  >
    <div v-for="(item, index) in listArr" :key="index">
      <!-- 工资项 -->
      <div class="selectItem ediItem" v-if="item.showType === 'select'">
        <el-popover
          placement="bottom"
          width="300"
          trigger="click"
          :ref="`popover-${index}`"
        >
          <el-radio-group
            v-model="selectTab"
            @change="changTab"
            style="margin-bottom: 30px"
          >
            <el-radio-button label="SALARY_ITEM">工资项目</el-radio-button>
            <el-radio-button label="PERSON_INFO">人员信息</el-radio-button>
            <el-radio-button label="PREINSTALL_ITEM">预设项目</el-radio-button>
            <el-radio-button label="ADJUST_ITEM">定调薪项目</el-radio-button>
          </el-radio-group>
          <el-input
            type="text"
            class="search-item-input"
            prefix-icon="iconiconfonticonfontsousuo1 iconfont"
            v-model="searchText"
            placeholder="请输入要搜索的项目名称"
            @keyup.enter.native="searchSalaryConfig"
            v-if="selectTab !== 'ADJUST_ITEM'"
          ></el-input>
          <div v-if="selectTab === 'ADJUST_ITEM'" class="adjust-tip">
            <i class="iconfont icontishishuoming"></i>
            <span>取算薪周期内员工最晚定调薪金额 </span>
          </div>

          <ul class="custome-salary-item-container">
            <li
              v-for="(i, d) in salaryConfigList"
              :key="d"
              class="custome-salary-item"
              @click="changeSalaryConfigItem(i, index)"
            >
              <!-- <span :data-title="i.tipExplain" v-if="i.tipExplain">
                                {{ i.name }}
                            </span>
                            <span v-else class="item-name">{{ i.name }}</span> -->
              <span :title="i.tipExplain">{{ i.name }}</span>
            </li>
          </ul>
          <p slot="reference" style="margin-right: 0; cursor: pointer">
            {{ item.label }}
            <i class="icon iconfont iconbi"></i>
          </p>
        </el-popover>
      </div>
      <!-- 文本 -->
      <div class="dateItem ediItem" v-if="item.showType === 'text'">
        <el-popover
          placement="bottom"
          width="220"
          trigger="click"
          :ref="`popover-${index}`"
        >
          <el-input
            type="textarea"
            :autosize="{ minRows: 4 }"
            v-model="item.value"
            placeholder="请输入文本（150以内）"
            style="margin-top: 20px"
          ></el-input>
          <div class="custome-btns">
            <el-button @click="closePopover(index)">取消</el-button>
            <el-button type="primary" @click="changeSalaryItem(item, index)"
              >确定</el-button
            >
          </div>

          <p slot="reference" style="margin-right: 0; cursor: pointer">
            {{ item.label }}
            <i class="icon iconfont iconbi"></i>
          </p>
        </el-popover>
      </div>
      <!-- 数值 -->
      <div class="numberItem ediItem" v-if="item.showType === 'number'">
        <el-popover
          placement="bottom"
          width="220"
          trigger="click"
          :ref="`popover-${index}`"
        >
          <el-input
            type="number"
            v-model="item.value"
            placeholder="请输入数字"
            style="margin-top: 20px"
          ></el-input>
          <div class="custome-btns">
            <el-button @click="closePopover(index)">取消</el-button>
            <el-button type="primary" @click="changeSalaryItem(item, index)"
              >确定</el-button
            >
          </div>

          <p slot="reference" style="margin-right: 0; cursor: pointer">
            {{ item.label }}
            <i class="icon iconfont iconbi"></i>
          </p>
        </el-popover>
      </div>
      <!-- 日期 -->
      <div class="dateItem ediItem" v-if="item.showType === 'date'">
        <el-popover
          placement="bottom"
          width="220"
          trigger="click"
          :ref="`popover-${index}`"
        >
          <el-date-picker
            v-model="item.value"
            type="date"
            value-format="yyyy-MM-dd"
            editable
            @change="changeSalaryItem(item, index)"
            placeholder="选择日期"
          ></el-date-picker>

          <p slot="reference" style="margin-right: 0; cursor: pointer">
            {{ item.label }}
            <i class="icon iconfont iconbi"></i>
          </p>
        </el-popover>
      </div>
      <!-- 符号 -->
      <div class="ediItem" v-if="item.showType === 'symbol'">
        {{ item.label }}
      </div>
      <!-- 公式 -->
      <div class="formula-item" v-if="item.showType === 'formula'">
        <div class="dateItem ediItem" :itemType="item.showType">
          {{ item.label }}
        </div>
      </div>
    </div>
  </draggable>
</template>
<script>
import draggable from "vuedraggable";
import { mapState } from "vuex";
import {
  apiSalaryItemInfo,
  apiSalaryItemInfoFormula,
} from "../../../store/api";
export default {
  props: {
    list: Array,
  },
  data() {
    return {
      listArr: [],
      selectTab: "SALARY_ITEM",
      salaryConfigList: [], //工资项列表
      searchText: "",
      searchList: [],
      isDraggingToDelete: false, // 标记是否正在拖拽删除
      dragStartPosition: null, // 记录拖拽开始位置
    };
  },
  components: {
    draggable,
  },
  watch: {
    list: {
      handler(newValue, oldValue) {
        this.listArr = newValue;
      },
      deep: true,
    },
  },
  computed: {
    ...mapState("salaryCalStore", {
      salaryConfigItem: "salaryConfigItem",
    }),
    // 动态配置拖拽组
    groupConfig() {
      return {
        name: "formulaItem",
        pull: true,
        put: (to, from, dragEl, evt) => {
          // 检查是否是从同一个容器拖拽过来的
          if (to === from) {
            return true; // 同一个容器内的排序
          }

          // 检查全局拖拽删除状态 - 更严格的检查
          if (window.formulaDragState && window.formulaDragState.isDragging) {
            const dragState = window.formulaDragState;

            // 如果拖拽的原始容器不是当前容器，禁止接收
            if (dragState.originalContainer !== to) {
              console.log(
                "阻止跨容器拖拽，原始容器:",
                dragState.originalContainer,
                "目标容器:",
                to
              );
              return false;
            }
          }

          return true; // 正常的跨容器拖拽
        },
      };
    },
  },
  created() {
    this.listArr = this.list.map((item) => {
      item.label = item.value;
      return item;
    });
    this.getConfigList();
  },
  methods: {
    //开始拖拽
    startDrag(evt) {
      // 记录拖拽开始位置和容器
      this.dragStartPosition = {
        x: evt.originalEvent.clientX,
        y: evt.originalEvent.clientY,
      };
      this.isDraggingToDelete = false;

      // 设置全局拖拽状态 - 更精确的状态管理
      window.formulaDragState = {
        isDragging: true,
        originalContainer: evt.from,
        startPosition: {
          x: evt.originalEvent.clientX,
          y: evt.originalEvent.clientY,
        },
        draggedElement: evt.item,
        sourceIndex: evt.oldIndex,
      };

      console.log("开始拖拽", window.formulaDragState);
    },
    //拖拽移动过程中
    onMove(evt) {
      const { to, from } = evt;

      // 如果拖拽到不同的容器，检查是否允许
      if (to !== from && window.formulaDragState) {
        const dragState = window.formulaDragState;

        // 如果原始容器不是目标容器，阻止移动
        if (dragState.originalContainer !== to) {
          console.log("onMove: 阻止移动到其他容器");
          return false;
        }
      }

      return true; // 允许移动
    },
    //添加项目
    addItem(evt) {
      // 检查是否应该阻止添加（防止意外的跨容器拖拽）
      if (window.formulaDragState) {
        const dragState = window.formulaDragState;

        // 如果不是从原始容器添加到当前容器，阻止添加
        if (dragState.originalContainer !== evt.to) {
          console.log("阻止意外的跨容器添加");
          // 移除刚添加的元素
          if (evt.newIndex !== undefined) {
            this.listArr.splice(evt.newIndex, 1);
          }
          return;
        }
      }

      let flag = evt.clone && evt.clone.innerHTML.indexOf("itemtype") != -1;
      if (flag) {
        this.listArr.push(
          {
            label: "(",
            value: "(",
            showType: "symbol",
            itemCategory: "LEFT_BRACKET",
          },
          {
            label: ",",
            value: ",",
            showType: "symbol",
            itemCategory: "COMMA",
          },
          {
            label: ")",
            value: ")",
            showType: "symbol",
            itemCategory: "RIGHT_BRACKET",
          }
        );
      }
      this.setVal(); //回传给父组件
    },
    //移出区域
    removeItem(evt) {
      let index = evt.newIndex;

      // 获取拖拽容器的边界信息
      const draggableContainer = evt.to;
      const containerRect = draggableContainer.getBoundingClientRect();

      // 获取鼠标的全局坐标
      const { clientX, clientY } = evt.originalEvent;

      console.log(evt, "end ===>>>>", clientX, clientY);
      console.log(
        `容器边界 - 左：${containerRect.left}, 右：${containerRect.right}，上：${containerRect.top}，下：${containerRect.bottom}`
      );
      console.log(`鼠标位置 - x：${clientX}, y：${clientY}`);

      // 检查鼠标是否在容器外部
      const isOutside =
        clientX < containerRect.left ||
        clientX > containerRect.right ||
        clientY < containerRect.top ||
        clientY > containerRect.bottom;

      // 检查是否是从原始容器拖拽出来的（删除操作）
      const isFromOriginalContainer =
        window.formulaDragState &&
        window.formulaDragState.originalContainer === evt.to;

      // 只有当鼠标在容器外部且是从原始容器拖拽出来时才删除元素
      if (isOutside && isFromOriginalContainer) {
        console.log("元素被拖出原始容器外，删除元素");
        this.listArr.splice(index, 1);
      } else if (!isFromOriginalContainer) {
        console.log("元素被拖拽到其他容器，不删除");
      } else {
        console.log("元素在容器内，保留元素");
      }

      // 重置全局拖拽状态
      window.formulaDragState = null;
      this.isDraggingToDelete = false;
      this.dragStartPosition = null;

      this.setVal(); //回传给父组件
    },
    //改变区域内容
    changeItem(evt) {
      this.$emit("changeItem");
    },
    //设置工资项工资项
    changeSalaryConfigItem(item, index) {
      let { name, configCode } = item;
      let type;
      if (this.selectTab === "SALARY_ITEM") {
        type = item.types.type3;
      } else {
        type = item.itemType;
      }
      let selectTab = this.selectTab;
      let arr = JSON.parse(JSON.stringify(this.listArr));
      //设置当前项目
      arr[index].label = name;
      arr[index].value = name;
      arr[index].itemCode = configCode;
      arr[index].itemType = type;
      arr[index].itemCategory = selectTab;
      //设置结束
      this.listArr = arr;
      //筛选条件初始化
      this.searchText = "";
      this.salaryConfigList = this.searchList;
      this.closePopover(index);
      this.setVal(); //回传给父组件
    },
    //设置出工资项的其他值
    changeSalaryItem(item, index) {
      let arr = JSON.parse(JSON.stringify(this.listArr));
      arr[index].label = item.value;
      arr[index].value = item.value;
      this.listArr = arr;
      this.closePopover(index);
      this.setVal(); //回传给父组件
    },
    //关闭弹出框
    closePopover(index) {
      this.$refs[`popover-${index}`][0].doClose();
    },
    //获取工资项
    getConfigList() {
      apiSalaryItemInfo(this.salaryConfigItem.salaryRuleId).then((res) => {
        //因为工资项是多维数组，需要扁平化
        let arr = res.data.flat();
        let data = arr.filter((item) => item);
        this.salaryConfigList = data.filter((it) => it.enable);
        this.searchList = this.salaryConfigList;
      });
    },
    //获取人员信息、预设项目、定调薪项目
    getSalaryItemInfoFormula() {
      apiSalaryItemInfoFormula(this.selectTab).then((res) => {
        if (this.selectTab === "ADJUST_ITEM") {
          let data = res.data.filter((it) => it.enable);
          let last_arr = JSON.parse(JSON.stringify(data));
          last_arr.map((item) => {
            item.name = "上月" + item.name;
            item.configCode = "LAST-" + item.configCode;
          });
          this.salaryConfigList = data.concat(last_arr);
        } else {
          this.salaryConfigList = res.data;
        }
        this.searchList = this.salaryConfigList;
      });
    },
    //改变工资项tab
    changTab() {
      let tab = this.selectTab;
      if (tab === "SALARY_ITEM") {
        this.getConfigList();
      } else {
        this.getSalaryItemInfoFormula();
      }
    },
    //向父组件回传当前值
    setVal() {
      this.$emit("changeItem");
      this.$emit("getDraggable", this.listArr);
    },
    //工资项搜索
    searchSalaryConfig() {
      let data = JSON.parse(JSON.stringify(this.searchList));
      let salaryConfigList = this.salaryConfigList;
      let searchText = this.searchText;
      if (searchText === "") {
        this.salaryConfigList = data;
        return;
      }
      let len = salaryConfigList.length;
      let arr = [];
      for (let i = 0; i < len; i++) {
        //如果字符串中不包含目标字符会返回-1
        if (salaryConfigList[i].name.indexOf(searchText) >= 0) {
          arr.push(salaryConfigList[i]);
        }
      }
      this.salaryConfigList = arr;
    },
  },
};
</script>
<style lang="scss" scoped>
.draggable-cotainer {
  position: relative;
  padding: 20px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  align-content: flex-start;
  flex-direction: row;
  flex: 1;
  background: rgba(222, 224, 227, 0.3);
  margin-left: 20px;
  min-height: 80px;
  > div {
    position: relative;
  }
  .ediItem {
    margin-right: 8px;
    margin-bottom: 10px;
    border-radius: 4px;
    border: 1px solid rgba(31, 35, 41, 1);
    padding: 8px 6px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    .icon {
      cursor: pointer;
    }
  }
  .selectItem {
    background: #00b4b3;
    border: 1px solid #00b4b3 !important;
    color: #fff !important;
  }
  .numberItem {
    background: #ffc60a;
    border: 1px solid #ffc60a !important;
    color: #fff !important;
  }
  .dateItem {
    background: #48b9f3;
    border: 1px solid #48b9f3 !important;
    color: #fff !important;
  }
  .iconbi {
    margin-left: 6px !important;
  }
}

.custome-salary-item-container {
  height: 240px;
  overflow: auto;
  padding: 10px 20px 0 30px;
  border: 1px solid #e6e8ec;
  margin: 0 5px;
  // position: relative;
  .custome-salary-item {
    line-height: 32px;
    text-align: left;
    cursor: pointer;
    span {
      position: relative;
      width: 100%;
      display: inline-block;
    }
    // span:hover:after {
    //     position: absolute;
    //     top: 10px;
    //     left: 100px;
    //     content: attr(data-title);
    //     //在这里设内置好title出现的位容置就好了
    //     color: #999;
    //     border: 1px solid #ccc;
    //     border-radius: 5px;
    //     background-color: #fff;
    //     line-height: 20px;
    //     font-size: 12px;
    //     padding: 0 5px;
    // }
    // span.item-name:hover:after {
    //     border: none !important;
    // }
  }
}
.el-popper {
  .el-radio-group {
    margin: 10px 0 !important;
    /deep/.el-radio-button__inner {
      padding: 8px 10px;
      font-size: 12px;
    }
  }
}
.search-item-input {
  width: 290px;
  /deep/ input[type="text"] {
    border: none !important;
  }
}
.adjust-tip {
  font-size: 12px;
  text-align: left;
  margin-left: 10px;
  padding: 5px 0;
  display: flex;
  align-items: center;
  color: #909399;
  i {
    margin-right: 5px;
  }
}
.custome-btns {
  // border-top: 1px solid #dee0e3;
  margin-top: 25px;
  /deep/ .el-button {
    line-height: 32px;
    padding: 0 20px;
  }
}
</style>
