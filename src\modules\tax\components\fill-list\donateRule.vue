<template>
  <div class="pension-list">
    <div class="staff-table">
      <el-table
        :data="list"
        class="check-staff_table"
        :style="{ width: screenWidth - 285 + 'px' }"
        :height="screenHeight"
        border
      >
        <el-table-column
          label="序号"
          type="index"
          width="55"
        ></el-table-column>
        <el-table-column
          label="姓名"
          prop="empName"
          min-width="170"
        ></el-table-column>
        <el-table-column
          label="工号"
          prop="empNo"
          min-width="140"
        ></el-table-column>
        <el-table-column
          label="证件号码"
          prop="idNo"
          min-width="170"
        ></el-table-column>
        <el-table-column
          prop="taxSubName"
          label="公司名称"
          width="180"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          label="所得项目"
          prop="taxRule"
          min-width="170"
        >
          <template slot-scope="scope">
            <span>{{ subTaxRuleTypeObj[scope.row.taxRule] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="准予扣除的捐赠"
          prop="amount"
          min-width="170"
        ></el-table-column>
        <el-table-column
          label="填写状态"
          prop="status"
          min-width="100"
        >
          <template slot-scope="scope">
            <span :style="{
                color: scope.row.status == 'NOT_FILLED' ? '#E14C46' : '#606266'
              }">{{ fillInStatusObj[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          min-width="100px"
        >
          <template slot-scope="scope">
            <span
              class="funStyle"
              @click="goFill(scope.row)"
            >填写</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      :visible.sync="isShowFillDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="800px"
      title="明细编辑"
      class="fill-dialog"
    >
      <div class="info-container">
        <div>
          <p style="width: 350px;">
            <span>姓名</span>
            <span>{{ rowInfo.empName }}</span>
          </p>

          <p>
            <span>证件号码</span>
            <span>{{ rowInfo.idNo }}</span>
          </p>
        </div>
        <div>
          <p style="width: 350px;">
            <span>所得项目</span>
            <span>{{ subTaxRuleTypeObj[rowInfo.taxRule] }}</span>
          </p>

          <p>
            <span>扣除合计</span>
            <span>{{ rowInfo.amount }}</span>
          </p>
        </div>
      </div>
      <div class="content-container">
        <el-form
          :model="reportForm"
          ref="reportForm"
          :rules="rules"
          label-width="120px"
        >
          <div
            v-for="(item, index) in reportForm.details"
            :key="index"
            style="margin-bottom: 10px;"
          >
            <div class="btns">
              <span
                @click="addRow()"
                v-if="isEdit"
              >新增</span>
              <span
                @click="deleteRow(index)"
                v-if="isEdit && index > 0"
                class="delete"
              >删除</span>
            </div>

            <div class="form-container">
              <el-row
                style="display: flex"
                v-if="
                  (item.status === 'FAIL' ||
                    item.status === 'UNTREATED' ||
                    item.jzStatus === 'FAIL' ||
                    item.jzStatus === 'UNTREATED') &&
                    item.errorInfo
                "
              >
                <el-col :span="24">
                  <el-form-item
                    label="校验信息"
                    class="err-item"
                  >
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="item.errorInfo"
                      placement="top-start"
                      v-if="item.errorInfo.length > 35"
                    >
                      <span class="form-error-over">{{ item.errorInfo }}</span>
                    </el-tooltip>
                    <span v-else>{{ item.errorInfo }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="display: flex">
                <el-col :span="12">
                  <el-form-item
                    label="捐赠金额"
                    :prop="`details.${index}.jzje`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入捐赠金额',
                        trigger: 'blur'
                      },
                      { validator: validateAmount, trigger: 'blur' }
                    ]"
                  >
                    <el-input
                      v-model="item.jzje"
                      v-if="isEdit"
                    ></el-input>
                    <span
                      class="form-item-value"
                      v-else
                    >{{ item.jzje }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="扣除比例"
                    :prop="`details.${index}.kcbl`"
                    :rules="{
                      required: true,
                      message: '请选择扣除比例',
                      trigger: 'change'
                    }"
                  >
                    <el-select
                      v-model="item.kcbl"
                      placeholder="请选择"
                      v-if="isEdit"
                      clearable
                    >
                      <el-option
                        v-for="it in proportionList"
                        :key="it"
                        :label="it"
                        :value="it"
                      ></el-option>
                    </el-select>
                    <span
                      class="form-item-value"
                      v-else
                    >{{ item.kcbl }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="display: flex">
                <el-col :span="12">
                  <el-form-item
                    label="实际扣除金额"
                    :prop="`details.${index}.sjjze`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入实际扣除金额',
                        trigger: 'blur'
                      },
                      { validator: validateAmount, trigger: 'blur' }
                    ]"
                  >
                    <el-input
                      v-model="item.sjjze"
                      v-if="isEdit"
                    ></el-input>
                    <span
                      class="form-item-value"
                      v-else
                    >{{ item.sjjze }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="受赠单位名称"
                    :prop="`details.${index}.szdwmc`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入受赠单位名称',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input
                      v-model="item.szdwmc"
                      v-if="isEdit"
                    ></el-input>
                    <span
                      class="form-item-value"
                      v-else
                    >{{
                      item.szdwmc
                    }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="display: flex">
                <el-col :span="12">
                  <el-form-item
                    label="纳税人识别号"
                    :prop="`details.${index}.szdwnsrsbh`"
                  >
                    <el-input
                      v-model="item.szdwnsrsbh"
                      v-if="isEdit"
                    ></el-input>
                    <span
                      class="form-item-value"
                      v-else
                    >{{
                      item.szdwnsrsbh
                    }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="捐赠日期"
                    :prop="`details.${index}.jzrq`"
                    :rules="{
                      required: true,
                      message: '请选择捐赠日期',
                      trigger: 'change'
                    }"
                  >
                    <el-date-picker
                      v-model="item.jzrq"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="选择日期"
                      v-if="isEdit"
                    ></el-date-picker>
                    <span
                      class="form-item-value"
                      v-else
                    >{{ item.jzrq }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="display: flex">
                <el-col :span="12">
                  <el-form-item
                    label="捐赠凭证号"
                    :prop="`details.${index}.jzpzh`"
                  >
                    <el-input
                      v-model="item.jzpzh"
                      v-if="isEdit"
                    ></el-input>
                    <span
                      class="form-item-value"
                      v-else
                    >{{ item.jzpzh }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="备注"
                    :prop="`details.${index}.bz`"
                  >
                    <el-input
                      v-model="item.bz"
                      v-if="isEdit"
                    ></el-input>
                    <span
                      class="form-item-value"
                      v-else
                    >{{ item.bz }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          type="primary"
          @click="save"
          v-if="isEdit"
        >保存</el-button>
        <el-button @click="close">{{ isEdit ? "取消" : "返回" }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import * as SCR from "../../util/constData";
import { validateAmountJE } from "@/util/validate";

export default {
  components: {},
  props: {
    isBatch: {
      type: Boolean,
    },
    list: {
      type: Array,
      default: [],
    },
    subTaxReportType: String,
  },
  data() {
    return {
      ruleForm: {
        currPage: 1,
        pageSize: 20,
        queryMonth: "",
        taxSubId: "",
      },
      fillInStatusObj: SCR.fillInStatus,
      subTaxRuleTypeObj: SCR.subTaxRuleType,
      proportionList: ["30%", "100%"],
      isShowFillDialog: false, //是否展示填写弹窗
      //填写弹窗数据
      reportForm: {
        reductionItemType: "DONATE_RULE",
        queryMonth: "",
        details: [
          {
            jzje: "",
            kcbl: "",
            sjjze: "",
            szdwmc: "",
            szdwnsrsbh: "",
            jzrq: "",
            jzpzh: "",
            bz: "",
          },
        ],
      },
      rules: {},
      rowInfo: {}, //当前行信息
      reportStatus: "", //当前申报状态
      validateAmount: validateAmountJE,
      screenWidth: document.body.clientWidth, // 屏幕尺寸
      screenHeight: document.body.clientHeight - 360,
    };
  },
  computed: {
    ...mapState("taxPageStore", {
      currentTaxsubObj: (state) => state.currentTaxsubObj,
    }),
    //是否可以编辑附表
    isEdit: function () {
      return [
        null,
        "",
        "INIT",
        "AWAIT_REPORT",
        "REPORT_ERROR",
        "CANCEL_SUCCESS",
      ].includes(this.reportStatus);
    },
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        window.screenWidth = document.body.clientWidth;
        this.screenWidth = window.screenWidth;
      })();
    };
    this.reportForm.queryMonth = this.currentTaxsubObj.queryMonth;
  },
  methods: {
    //打开填写弹窗
    goFill(data) {
      this.rowInfo = data;
      //获取申报状态
      this.ruleForm.taxSubId = data.taxSubId;
      this.ruleForm.queryMonth = this.reportForm.queryMonth;
      const params = {
        ...this.ruleForm,
        subTaxReportType: String(this.subTaxReportType),
      };
      this.$store
        .dispatch("taxPageStore/actionTaxReportTotalList", params)
        .then((res) => {
          if (res.success) {
            this.reportStatus = res.data.reportStatus;
          }
        });
      this.reportForm.details = [
        {
          jzje: "",
          kcbl: "",
          sjjze: "",
          szdwmc: "",
          szdwnsrsbh: "",
          jzrq: "",
          jzpzh: "",
          bz: "",
        },
      ];
      this.isShowFillDialog = true;
      //如果填写状态是已填写，则请求填写详情接口
      if (
        this.rowInfo.status === "FILLED" ||
        this.rowInfo.status === "CHECK_FAILED"
      ) {
        const { idNo, idType, taxSubId, taxRule, totalId } = this.rowInfo;
        let obj = {
          idNo,
          idType,
          taxSubId,
          taxRule,
          totalId,
          reductionItemType: "DONATE_RULE",
        };
        this.$store
          .dispatch("taxPageStore/actionGetScheduleDetail", obj)
          .then((res) => {
            if (res.success) {
              this.reportForm.details = res.data;
            }
          });
      }
    },
    //填写弹窗新增行
    addRow() {
      this.reportForm.details.push({
        jzje: "",
        kcbl: "",
        sjjze: "",
        szdwmc: "",
        szdwnsrsbh: "",
        jzrq: "",
        jzpzh: "",
        bz: "",
      });
    },
    //填写弹窗删除行
    deleteRow(index) {
      this.reportForm.details.splice(index, 1);
    },
    //保存弹窗内容
    save() {
      this.$refs["reportForm"].validate((valid) => {
        if (valid) {
          const {
            empId,
            empName,
            idNo,
            idType,
            taxSubId,
            taxRule,
            amount,
            totalId,
          } = this.rowInfo;
          let obj = {
            empId,
            empName,
            idNo,
            idType,
            taxSubId,
            taxRule,
            amount,
            totalId,
          };
          let reportForm = this.reportForm;
          Object.assign(reportForm, obj);
          this.$store
            .dispatch("taxPageStore/actionSaveScheduleDetail", reportForm)
            .then((res) => {
              if (res.success) {
                this.$message.success("操作成功");
                this.isShowFillDialog = false;
                this.$refs["reportForm"].resetFields();
                this.$emit("freshList");
              }
            });
        } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    //关闭弹窗
    close() {
      this.isShowFillDialog = false;
      this.$refs["reportForm"].resetFields();
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";
.pension-list {
  /deep/ .fill-dialog {
    .info-container {
      background: rgba(245, 246, 247, 1);
      padding: 26px 30px;
      margin-bottom: 20px;
      > div {
        // display: flex;
        // justify-content: space-between;
        margin-bottom: 10px;
        p {
          display: inline-block;
          span {
            color: #1f2329;
          }
          span:first-child {
            color: #646a73;
            font-weight: normal;
            display: inline-block;
            width: 70px;
            text-align: right;
            margin-right: 20px;
          }
        }
      }
    }
    .content-container {
      .btns {
        background: rgba(245, 246, 247, 1);
        line-height: 32px;
        text-align: right;
        padding-right: 20px;
        color: #4F71FF;
        span {
          cursor: pointer;
        }
        .delete {
          margin-left: 20px;
        }
      }
      .form-container {
        padding: 20px 48px 10px 28px;
        border: 1px solid rgba(245, 246, 247, 1);
      }
      .form-item-value {
        color: #1f2329;
      }
      .form-error-over {
        display: inline-block;
        width: 575px;
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .el-dialog__header {
      border-bottom: 1px solid #ededed;
      text-align: left;
      .el-dialog__title {
        font-size: 16px;
      }
    }
    .el-dialog__body {
      padding: 25px;
      /deep/ .el-input {
        width: 200px;
      }
    }
    .err-item {
      color: #e14c46;
      .el-form-item__label {
        color: #e14c46;
      }
    }
  }
}
</style>
