<template>
  <div>
    <div class="operation check-staff-menu">
      <div>
        <el-select
          style="margin-right: 10px"
          v-model="params.areaId"
          @change="handleChangeArea"
          clearable
        >
          <el-option
            v-for="item in areaList"
            :key="item.id"
            :label="item.areaName"
            :value="item.id"
          ></el-option>
        </el-select>
        <el-select
          style="margin-right: 10px"
          v-model="params.taxSubjectId"
          placeholder="请选择公司"
          @change="emitSearch"
          filterable
          clearable
        >
          <el-option
            v-for="(item, index) in taxSubjectInfoList"
            :label="item.taxSubName"
            :value="item.taxSubId"
            :key="index"
          >
          </el-option>
        </el-select>
        <el-date-picker
          style="margin-right: 10px"
          v-model="params.queryMonth"
          :default-value="params.queryMonth"
          type="month"
          placeholder="请选择所得月份"
          @blur="updateTaxSubjectInfoList"
          value-format="yyyy-MM"
          :editable="false"
          :clearable="false"
        ></el-date-picker>
        <el-input
          placeholder="请输入员工姓名\证件号码"
          v-model="params.nameOrMore"
          prefix-icon="iconiconfonticonfontsousuo1 iconfont"
          clearable
          @keyup.enter.native="emitSearch"
          class="search-input"
        ></el-input>
      </div>
      <slot name="operation" />
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

const getCurrentDate = () => {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = String(currentDate.getMonth() + 1).padStart(2, "0");
  const formattedDate = `${year}-${month}`;
  return formattedDate;
};

export default {
  data() {
    return {
      params: {
        areaId: "",
        queryMonth: getCurrentDate(),
        taxSubjectId: "",
        nameOrMore: "",
      },
      taxSubjectInfoList: [],
    };
  },
  computed: {
    ...mapState({
      areaList: (state) => state.areaList,
    }),
  },
  created() {
    this.updateTaxSubjectInfoList();
  },
  methods: {
    handleChangeArea() {
      this.emitSearch();
    },
    emitSearch() {
      this.$emit("search", {
        ...this.params,
      });
    },
    updateTaxSubjectInfoList() {
      this.loadTaxSubjectInfoList();
      this.emitSearch();
    },
    loadTaxSubjectInfoList() {
      this.$store
        .dispatch("taxPageStore/actionTaxSubjectInfoList", {
          date: this.params.queryMonth,
        })
        .then((res) => {
          if (!res.success) return;
          this.taxSubjectInfoList = [
            { taxSubId: "", taxSubName: "全部公司" },
            ...res.data,
          ];
        });
    },
  },
};
</script>
