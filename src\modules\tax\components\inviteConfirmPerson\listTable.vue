<template>
  <div class="staff-table">
    <el-tabs v-model="activeName" @tab-click="tabClick">
      <el-tab-pane label="未处理" name="0"></el-tab-pane>
      <el-tab-pane label="已处理" name="1"></el-tab-pane>
    </el-tabs>
    <el-table :data="tableData" class="check-staff_table" border>
      <el-table-column type="index" label="序号" width="80" fixed />
      <el-table-column
        prop="empName"
        label="姓名"
        width="180"
        :show-overflow-tooltip="true"
        fixed
      />
      <el-table-column prop="idType" label="证件类型" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.idType | filterIdType }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="idNo" label="证件号码" width="180" />
      <el-table-column
        prop="taxSubName"
        label="对应公司名称"
        width="120"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="areaName"
        label="区域名称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="gj" label="国籍" width="180" />
      <el-table-column prop="sdyf" label="所得月份" width="180" />
      <el-table-column prop="status" label="状态" width="120" />
      <el-table-column
        v-if="activeName === '0'"
        prop="failReason"
        label="反馈信息"
        width="150"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.failReason || "-" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="dealDate" label="处理日期" width="120">
        <template slot-scope="scope">
          <span>
            {{ scope.row.dealDate || "-" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="activeName === '0'" label="操作" width="130">
        <template slot-scope="scope">
          <el-button
            v-if="
              privilegeVoList.includes(
                'salary.report.complaintConfirmInvite.sendInvite'
              ) &&
              (scope.row.feedbackStatus === 'INIT' ||
                scope.row.feedbackStatus === 'FAIL')
            "
            type="text"
            @click="handleClick('send', scope.row)"
            >发送邀请</el-button
          >
          <el-button
            v-else-if="
              privilegeVoList.includes(
                'salary.report.complaintConfirmInvite.sendInviteQuery'
              ) && scope.row.feedbackStatus === 'PROCESSING'
            "
            type="text"
            @click="handleClick('query', scope.row)"
            >获取反馈</el-button
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-size="pageSize"
      :current-page="currPage"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      :page-sizes="[20, 50, 100, 200]"
      class="staff-page"
    />
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  props: {
    currPage: {
      default: 1,
      type: Number,
    },
    pageSize: {
      default: 20,
      type: Number,
    },
    total: {
      default: 20,
      type: Number,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      activeName: "0",
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  methods: {
    handleClick(type, row) {
      this.$emit("handleClick", type, row);
    },
    handleSizeChange(size) {
      this.$emit("size-change", size);
    },
    handleCurrentChange(curr) {
      this.$emit("current-change", curr);
    },
    tabClick(e) {
      this.$emit("tabClick", e.name);
    },
  },
};
</script>
