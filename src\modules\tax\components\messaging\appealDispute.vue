<template>
  <div>
    <el-table :data="tableData" class="check-staff_table" border>
      <el-table-column prop="msgId" label="申诉编号" width="120" fixed />
      <el-table-column
        prop="empName"
        label="申诉人姓名"
        width="180"
        fixed
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="sslxMc"
        label="申诉类型"
        width="120"
        fixed
        :show-overflow-tooltip="true"
      />
      <el-table-column prop="idType" label="证件类型" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.idType | filterIdType }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="idNo" label="证件号码" width="180" />
      <el-table-column
        prop="taxSubName"
        label="公司名称"
        width="140"
        show-overflow-tooltip
      />
      <el-table-column
        prop="areaName"
        label="区域名称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="lzrq" label="离职日期" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.lzrq || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="ssrq" label="申诉日期" width="120" />
      <el-table-column prop="sssxDm" label="申诉事项" width="150">
        <template slot-scope="scope">
          <span>{{ filterSssxDm(scope.row.sssxDm) || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="xqfkrq" label="限期反馈日期" width="180" />
      <el-table-column prop="sffk" label="是否反馈" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.sffk ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="dealStatus" label="处理状态" width="140">
        <template slot-scope="scope">
          <span style="margin-right: 3px">
            {{
              scope.row.dealStatus ? dealStatusObj[scope.row.dealStatus] : "-"
            }}
          </span>
          <el-popover
            v-if="scope.row.dealStatus === 'FAIL'"
            placement="top-start"
            width="200"
            trigger="hover"
          >
            <div
              style="text-align: left"
              v-html="formatter(scope.row.failReason)"
            ></div>
            <a style="cursor: pointer" slot="reference">查看原因</a>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="dealDate" label="处理日期" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.dealDate || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="fkrxm" label="反馈人" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.fkrxm || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="fksm"
        label="反馈说明"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.fksm || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            v-if="
              privilegeVoList.includes(
                'salary.report.comprehensiveMsg.dealAppealDispute'
              ) &&
              (scope.row.dealStatus === 'INIT' ||
                scope.row.dealStatus === 'FAIL')
            "
            type="text"
            size="small"
            @click="handleClick('handle', scope.row)"
            >处理</el-button
          >
          <el-button
            v-if="
              privilegeVoList.includes(
                'salary.report.comprehensiveMsg.dealAppealDisputeQuery'
              ) && scope.row.dealStatus === 'PROCESSING'
            "
            type="text"
            size="small"
            @click="appealFeedback(scope.row)"
            >获取反馈</el-button
          >
          <el-button
            v-if="
              scope.row.dealStatus === 'PROCESSING' ||
              scope.row.dealStatus === 'SUCCESS' ||
              scope.row.dealStatus === 'CANCEL'
            "
            type="text"
            size="small"
            @click="handleClick('detail', scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import * as SCR from "../../util/constData";
import { mapState } from "vuex";

export default {
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dealStatusObj: SCR.dealStatus,
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  methods: {
    formatter(val) {
      return val.replace(/\n/g, "<br>");
    },
    filterSssxDm(val) {
      switch (val) {
        case "10": {
          return "收入信息异议";
        }
        case "30": {
          return "任职受雇信息异议";
        }
      }
    },
    handleClick(type, row) {
      this.$emit("handleAppealClick", type, row);
    },
    appealFeedback(row) {
      this.$emit("appealFeedback", row);
    },
  },
};
</script>
