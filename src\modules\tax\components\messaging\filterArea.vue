<template>
  <div>
    <div class="operation check-staff-menu">
      <div>
        <el-button @click="isShowScreening = true">筛选</el-button>
        <el-select
          style="margin-left: 10px"
          v-model="params.areaId"
          @change="handleChangeArea"
          clearable
        >
          <el-option
            v-for="item in areaList"
            :key="item.id"
            :label="item.areaName"
            :value="item.id"
          ></el-option>
        </el-select>
        <el-select
          style="margin: 0 10px"
          v-model="params.taxSubjectId"
          placeholder="请选择公司"
          @change="emitSearch"
          filterable
          clearable
        >
          <el-option
            v-for="(item, index) in taxSubjectInfoList"
            :label="item.taxSubName"
            :value="item.taxSubId"
            :key="index"
          >
          </el-option>
        </el-select>
        <el-date-picker
          style="width: 190px; margin-right: 10px"
          v-model="params.ssrq"
          type="month"
          placeholder="请选择最早申诉日期"
          @blur="updateTaxSubjectInfoList"
          value-format="yyyy-MM"
          :editable="false"
          clearable
        ></el-date-picker>
        <el-input
          placeholder="请输入员工姓名\证件号码"
          v-model="params.nameOrMore"
          prefix-icon="iconiconfonticonfontsousuo1 iconfont"
          clearable
          @keyup.enter.native="emitSearch"
          class="search-input"
        ></el-input>
      </div>
      <slot name="operation" />
    </div>
    <el-dialog
      title="筛选"
      :visible.sync="isShowScreening"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="650px"
    >
      <el-form :model="params" label-width="180px">
        <div class="shortCon">
          <el-form-item label="离职日期">
            <el-date-picker
              style="width: 320px"
              v-model="leaveDate"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="yyyy-MM"
              :editable="false"
            ></el-date-picker>
          </el-form-item>
        </div>
        <div class="shortCon">
          <el-form-item label="处理状态">
            <el-select
              style="width: 210px"
              v-model="params.dealStatus"
              placeholder="请选择处理状态"
              filterable
              clearable
            >
              <el-option
                v-for="(item, index) in processingStatusList"
                :label="item.label"
                :value="item.value"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="shortCon">
          <el-form-item label="反馈人">
            <el-input
              style="width: 210px"
              v-model="params.fkrxm"
              placeholder="请输入反馈人"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as SCR from "./options";
import { mapState } from "vuex";

export default {
  data() {
    return {
      params: {
        areaId: "",
        ssrq: "",
        taxSubjectId: "",
        nameOrMore: "",
        lzrqq: "",
        lzrqz: "",
        dealStatus: "",
        fkrxm: "",
      },
      leaveDate: [],
      taxSubjectInfoList: [],
      processingStatusList: SCR.processingStatusList,
      isShowScreening: false,
    };
  },
  computed: {
    ...mapState({
      areaList: (state) => state.areaList,
    }),
  },
  created() {
    this.updateTaxSubjectInfoList();
  },
  methods: {
    handleChangeArea() {
      this.emitSearch();
    },
    emitSearch() {
      this.$emit("search", {
        ...this.params,
      });
    },
    handleSearch() {
      this.isShowScreening = false;
      this.params.lzrqq = this.leaveDate ? this.leaveDate[0] : "";
      this.params.lzrqz = this.leaveDate ? this.leaveDate[1] : "";
      this.emitSearch();
    },
    handleReset() {
      this.params = {
        ssrq: "",
        taxSubjectId: "",
        nameOrMore: "",
        dealStatus: "",
        fkrxm: "",
      };
      this.leaveDate = [];
    },
    updateTaxSubjectInfoList() {
      this.loadTaxSubjectInfoList();
      this.emitSearch();
    },
    loadTaxSubjectInfoList() {
      this.$store
        .dispatch("taxPageStore/actionTaxSubjectInfoList", {
          date: this.params.ssrq || "",
        })
        .then((res) => {
          if (!res.success) return;
          this.taxSubjectInfoList = [
            { taxSubId: "", taxSubName: "全部公司" },
            ...res.data,
          ];
        });
    },
  },
};
</script>
