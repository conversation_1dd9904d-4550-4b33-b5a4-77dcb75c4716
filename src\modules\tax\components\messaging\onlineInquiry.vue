<template>
  <div>
    <el-table :data="tableData" class="check-staff_table" border>
      <el-table-column prop="msgId" label="申诉批次" width="120" fixed />
      <el-table-column
        prop="empName"
        label="申诉人姓名"
        width="180"
        fixed
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="sslxMc"
        label="申诉类型"
        width="120"
        fixed
        :show-overflow-tooltip="true"
      />
      <el-table-column prop="idType" label="证件类型" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.idType | filterIdType }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="idNo" label="证件号码" width="180" />
      <el-table-column
        prop="taxSubName"
        label="公司名称"
        width="140"
        show-overflow-tooltip
      />
      <el-table-column
        prop="areaName"
        label="区域名称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="lzrq" label="离职日期" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.lzrq || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="ssrq" label="申诉日期" width="120" />
      <el-table-column prop="zgswry" label="主管税务人员" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.zgswry || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="rwbjqx" label="任务办结期限" width="180" />
      <el-table-column prop="fxzt" label="风险主题" width="190" />
      <el-table-column prop="dealStatus" label="处理状态" width="140">
        <template slot-scope="scope">
          <span style="margin-right: 3px">
            {{
              scope.row.dealStatus ? dealStatusObj[scope.row.dealStatus] : "-"
            }}
          </span>
          <el-popover
            v-if="scope.row.dealStatus === 'FAIL'"
            placement="top-start"
            width="200"
            trigger="hover"
          >
            <div style="text-align: left" v-html="formatter(scope.row.failReason)"></div>
            <a style="cursor: pointer" slot="reference">查看原因</a>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="dealDate" label="最后一次操作日期" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.dealDate || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="fkrxm" label="操作人" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.fkrxm || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="bz" label="反馈说明" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.bz || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            v-if="
              scope.row.dealStatus === 'PROCESSING' ||
              scope.row.dealStatus === 'SUCCESS'
            "
            type="text"
            size="small"
            @click="handleClick('detail', scope.row)"
            >查看</el-button
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import * as SCR from "../../util/constData";
export default {
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dealStatusObj: SCR.dealStatus,
    };
  },
  methods: {
    formatter(val) {
      return val.replace(/\n/g, "<br>");
    },
    handleClick(type, row) {
      this.$emit("handleOnlineClick", type, row);
    },
  },
};
</script>
