<template>
  <div>
    <div class="operation check-staff-menu">
      <div>
        <el-select
          style="margin-right: 10px"
          v-model="params.areaId"
          @change="handleChangeArea"
          clearable
        >
          <el-option
            v-for="item in areaList"
            :key="item.id"
            :label="item.areaName"
            :value="item.id"
          ></el-option>
        </el-select>
        <el-select
          style="width: 200px"
          v-model="params.taxSubIds"
          placeholder="请选择公司名称"
          filterable
          clearable
          multiple
          collapse-tags
          @change="emitSearch"
        >
          <el-option
            v-for="(item, index) in taxSubjectInfoList"
            :label="item.taxSubName"
            :value="item.taxSubId"
            :key="index"
          ></el-option>
        </el-select>
        <el-date-picker
          popper-class="date_class"
          style="margin: 0 10px"
          v-model="params.date"
          :default-value="params.date"
          type="year"
          @blur="updateTaxSubjectInfoList"
          value-format="yyyy"
          :editable="false"
          :clearable="false"
          :picker-options="pickerOptions"
        ></el-date-picker>
        <el-select
          v-model="params.formStatus"
          placeholder="请选择状态"
          @change="updateTaxSubjectInfoList"
          style="margin-right: 10px"
          clearable
          multiple
        >
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <slot name="operation" />
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  data() {
    return {
      params: {
        areaId: "",
        date: (() => {
          const currentDate = new Date();
          const year = currentDate.getFullYear();
          return `${year - 1}`;
        })(),
        taxSubIds: [],
        formStatus: [],
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > new Date() - 365 * 8.64e7 - 1;
        },
      },
      taxSubjectInfoList: [],
      statusList: [
        { label: "未申请", value: "未申请" },
        { label: "审核中", value: "审核中" },
        { label: "审核通过", value: "审核通过" },
        { label: "退付成功", value: "退付成功" },
        { label: "审核未通过", value: "审核未通过" },
      ],
    };
  },
  computed: {
    ...mapState({
      areaList: (state) => state.areaList,
    }),
  },
  created() {
    this.updateTaxSubjectInfoList();
  },
  methods: {
    handleChangeArea() {
      this.emitSearch();
    },
    updateTaxSubjectInfoList() {
      this.emitSearch();
      this.loadTaxSubjectInfoList();
    },
    emitSearch() {
      this.$emit("search", {
        ...this.params,
      });
    },
    loadTaxSubjectInfoList() {
      this.params.taxSubIds = [];
      this.$store
        .dispatch("taxPageStore/actionTaxSubjectInfoList", {
          date: this.params.date,
        })
        .then((res) => {
          if (!res.success) return;
          this.taxSubjectInfoList = res.data;
        });
    },
  },
};
</script>
<style scoped>
::v-deep .el-year-table td.disabled .cell {
  font-weight: normal; 
}
</style>
