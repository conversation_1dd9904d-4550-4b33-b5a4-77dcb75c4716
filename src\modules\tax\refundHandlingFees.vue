<template>
  <div class="tax attach def_per_height">
    <ListTitle title="退付手续费" />
    <div class="tax-content">
      <ListFilterArea ref="listFilterArea" @search="onSearch">
        <div slot="operation" style="display: flex">
          <!-- <el-dropdown trigger="click">
            <el-button style="margin-right: 10px">
              批量核对
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-prevent-re-click
                @click.native="handleCheck"
                >批量核对</el-dropdown-item
              >
              <el-dropdown-item
                v-prevent-re-click
                @click.native="handleQueryResult"
                >获取核对结果</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown> -->
          <Button
            v-if="
              privilegeVoList.includes(
                'salary.taxpay.refundProcessingFee.download'
              ) ||
              privilegeVoList.includes(
                'salary.report.refundProcessingFee.download'
              )
            "
            type="primary"
            @click="openDownloadDialog"
          >
            获取/更新手续费清册
          </Button>
          <Button
            v-if="
              privilegeVoList.includes(
                'salary.taxpay.refundProcessingFee.download'
              ) ||
              privilegeVoList.includes(
                'salary.report.refundProcessingFee.download'
              )
            "
            v-prevent-re-click
            @click="handleBatchFeedback"
            >获取反馈</Button
          >
          <el-dropdown trigger="click">
            <el-button style="margin-left: 10px">
              更多操作
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-prevent-re-click
                @click.native="handleExport"
                v-show="
                  privilegeVoList.includes(
                    'salary.taxpay.refundProcessingFee.exportList'
                  ) ||
                  privilegeVoList.includes(
                    'salary.report.refundProcessingFee.exportList'
                  )
                "
                >导出</el-dropdown-item
              >
              <!-- <el-dropdown-item
                v-prevent-re-click
                @click.native="handleUpdateStatus"
                >批量更新状态</el-dropdown-item
              > -->
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </ListFilterArea>

      <div class="staff-table">
        <Table
          :data="tableData"
          ref="table"
          class="check-staff_table"
          diffKey="taxSubId"
          :style="{ width: screenWidth - 285 + 'px' }"
          v-loading="isTableLoading"
          @selection-change="handleSelectionChange"
          border
          :currentPage="params.currPage"
          :total="total"
          >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="formNo" label="结报单号" width="150" fixed />
          <el-table-column
            prop="taxSubName"
            label="企业名称"
            min-width="360"
            show-overflow-tooltip
            fixed
          />
          <el-table-column
            prop="areaName"
            label="区域名称"
            width="180"
            show-overflow-tooltip
          />
          <el-table-column prop="pzzlmc" label="票证名称" width="140">
          </el-table-column>
          <el-table-column prop="zspmmc" label="品目名称" width="140">
            <template slot-scope="scope">
              <span>{{ scope.row.zspmmc || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="zsxmmc" label="税种" width="140">
          </el-table-column>
          <el-table-column label="申请手续费金额" width="140">
            <template slot-scope="scope">
              <div style="text-align: right">
                {{ scope.row.applyAmount || "-" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="银行名称" width="180" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ formatter(scope.row.bankInfos, 'khyhmc') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="网点名称" width="280" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ formatter(scope.row.bankInfos, 'wdmc') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="银行账号" width="180" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ formatter(scope.row.bankInfos, 'yhzh') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="formStatus" label="退付结果" width="140">
          </el-table-column>
          <el-table-column
            prop="feedback"
            label="反馈说明"
            width="140"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.feedback || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="
                  (privilegeVoList.includes(
                    'salary.taxpay.refundProcessingFee.detailList'
                  ) ||
                    privilegeVoList.includes(
                      'salary.report.refundProcessingFee.detailList'
                    )) &&
                  (scope.row.formStatus === '未申请' ||
                    scope.row.formStatus === '审核未通过')
                "
                type="text"
                size="small"
                @click="handleStatement('check', scope.row)"
                >核对结报单</el-button
              >
              <el-button
                v-if="
                  (privilegeVoList.includes(
                    'salary.taxpay.refundProcessingFee.apply'
                  ) ||
                    privilegeVoList.includes(
                      'salary.report.refundProcessingFee.apply'
                    )) &&
                  (scope.row.formStatus === '未申请' ||
                    scope.row.formStatus === '审核未通过')
                "
                type="text"
                size="small"
                @click="checkCorrect(scope.row)"
                >核对无误</el-button
              >
              <el-button
                v-if="
                  (privilegeVoList.includes(
                    'salary.taxpay.refundProcessingFee.detailList'
                  ) ||
                    privilegeVoList.includes(
                      'salary.report.refundProcessingFee.detailList'
                    )) &&
                  (scope.row.formStatus === '审核通过' ||
                    scope.row.formStatus === '退付成功' ||
                    scope.row.formStatus === '审核中')
                "
                type="text"
                size="small"
                @click="handleStatement('detail', scope.row)"
                >查看结报单</el-button
              >
              <el-button
                v-if="
                  (privilegeVoList.includes(
                    'salary.taxpay.refundProcessingFee.cancel'
                  ) ||
                    privilegeVoList.includes(
                      'salary.report.refundProcessingFee.cancel'
                    )) &&
                  scope.row.formStatus === '审核中'
                "
                type="text"
                size="small"
                @click="withdraw(scope.row)"
                >撤销申请</el-button
              >
              <el-button
                v-if="isShow(scope.row)"
                type="text"
                size="small"
                @click="getFeedback(scope.row)"
                >获取反馈</el-button
              >
            </template>
          </el-table-column>
        </Table>
        <el-pagination
          class="staff-page"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-size="params.pageSize"
          :current-page="params.currPage"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </div>

    <el-dialog
      class="diy-el_dialog"
      width="750px"
      :visible.sync="handlingFeesListDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDownloadDialog"
    >
      <div slot="title" class="dialog-title">
        选择公司
        <el-tooltip
          class="item"
          effect="dark"
          content="最多可选20家"
          placement="right"
        >
          <i class="iconfont iconwarningicon"></i>
        </el-tooltip>
      </div>
      <el-input
        style="width: 270px; margin-bottom: 10px"
        placeholder="请输入公司名称"
        v-model="taxSubName"
        clearable
        @change="handleChange"
      >
      </el-input>
      <div class="staff-table">
        <el-checkbox v-model="selectAll" @change="selectAllChange" :disabled="taxSubName !== ''">全选</el-checkbox>
        <el-table
          class="filter-table"
          height="400px"
          stripe
          ref="taxSubTable"
          :header-cell-style="{ background: '#F1F1F1' }"
          :data="taxSubList"
          @filter-change="handleFilterChange"
          v-loading="isLoading"
        >
          <el-table-column width="55" fixed="left">
            <template slot-scope="scope">
              <el-checkbox
                v-model="scope.row.selected"
                @change="handleCheckboxChange(scope.row)"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column
            prop="taxSubName"
            label="公司名称"
            width="180"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="areaName"
            label="所属区域"
            min-width="180"
            show-overflow-tooltip
            :column-key="'areaIds'"
            :filters="areaList"
          ></el-table-column>
          <el-table-column
            prop="downloadTime"
            label="最近操作时间"
            width="250"
          />
        </el-table>
        <el-pagination
          @current-change="dialogCurrentChange"
          :current-page="dialogCurrPage"
          :page-size="20"
          layout="total, sizes, prev, pager, next, jumper"
          :total="taxSubTotal"
          :page-sizes="[20]"
          class="staff-page"
        />
      </div>
      <!-- <el-tag
        v-for="tag in tags"
        :key="tag.name"
        closable
        :disable-transitions="false"
        @close="handleClose(tag.taxSubId)"
      >
        {{ tag.taxSubName }}
      </el-tag> -->
      <div slot="footer">
        <el-button type="primary" @click="batchDownload">批量下载</el-button>
        <el-button @click="closeDownloadDialog">取消</el-button>
      </div>
    </el-dialog>

    <RefundsConfirm
      ref="refundsConfirm"
      :bankInfo="bankInfo"
      :bankInfoList="bankInfoList"
      :date="params.date"
      @confirmRefunds="confirmRefunds"
    />

    <BatchSelectS ref="selectS" :timeObj="timeObj" :sign="sign" />
    <BatchFeedback ref="batchFeedback" :sign="sign"></BatchFeedback>
  </div>
</template>
<script>
import RefundsConfirm from "./components/refundsConfirm";
import BatchSelectS from "../taxPaid/components/batchSelectS";
import BatchFeedback from "../taxPaid/components/batchFeedback";
import ListTitle from "@/components/listTitle";
import ListFilterArea from "./components/refundHandlingFeesFilterArea.vue";
import Button from "@/components/button";
import Table from "@/components/table.vue";
import {
  apiRefundFeeInventoryList,
  apiGetTaxSubList,
  apiRefundFeeInventoryListExport,
} from "./store/api";
import { mapState } from "vuex";

// let map = {}
export default {
  components: {
    RefundsConfirm,
    BatchSelectS,
    BatchFeedback,
    ListFilterArea,
    ListTitle,
    Button,
    Table
  },
  data() {
    return {
      isTableLoading: false,
      isLoading: false,
      params: {
        currPage: 1,
        pageSize: 20,
        date: "",
        taxSubIds: [],
        formStatus: [],
      },
      total: 0,
      tableData: [],
      selectItems: [],
      handlingFeesListDialog: false,
      taxSubTotal: 0,
      taxSubList: [],
      dialogCurrPage: 1,
      areaIds: [],
      taxSubName: "",
      timeObj: {
        first: 2000,
        second: 3000,
        third: 4000,
      },
      sign: "",
      bankInfo: {},
      bankInfoList: [],
      tags: [],
      map: {},
      selectAll: false,
    };
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
      areaList: (state) => {
        return state.areaList.filter((item) => {
          if (!item.id) return false;
          item.text = item.areaName;
          item.value = item.id;
          return true;
        });
      },
    }),
  },
  methods: {
    handleSelectionChange(row) {
      this.selectItems = row;
    },
    formatter(arr, prop) {
      if(arr && arr.length) {
        return arr[0][prop]
      }else {
        return '-'
      }
    },
    confirmRefunds(id) {
      this.sign = id;
      this.$refs.selectS.show();
    },
    batchFeedback(id) {
      this.sign = id;
      this.$refs.batchFeedback.show();
    },
    isShow(row) {
      if (
        (this.privilegeVoList.includes(
          "salary.taxpay.refundProcessingFee.apply"
        ) ||
          this.privilegeVoList.includes(
            "salary.report.refundProcessingFee.apply"
          )) &&
        (row.formStatus === "未申请" || row.formStatus === "审核未通过")
      ) {
        return true;
      } else if (
        (this.privilegeVoList.includes(
          "salary.taxpay.refundProcessingFee.cancel"
        ) ||
          this.privilegeVoList.includes(
            "salary.report.refundProcessingFee.cancel"
          )) &&
        row.formStatus === "审核中"
      ) {
        return true;
      } else {
        return false;
      }
    },
    onSearch(params) {
      this.params = {
        ...this.params,
        ...params,
        currPage: 1,
        date: params.date + "-01-01",
      };
      this.getList();
    },
    handleSizeChange(val) {
      this.params.pageSize = val;
      this.params.currPage = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.params.currPage = val;
      this.getList();
    },
    async getList() {
      this.isTableLoading = true;
      try {
        const res = await apiRefundFeeInventoryList(this.params);
        if (!res.success) return;
        this.total = res.data.count;
        this.tableData = res.data.data;
      } finally {
        this.isTableLoading = false;
      }
    },
    handleStatement(type, row) {
      this.$router.push({
        path: "/tax/refundHandlingFees/detail",
        query: { type: type, refundId: row.id, date: this.params.date },
      });
    },
    checkCorrect(row) {
      this.bankInfo = row;
      for (var c of row.bankInfos) {
        c.taxSubName = row.taxSubName;
      }
      this.bankInfoList = row.bankInfos;
      this.$refs.refundsConfirm.open();
    },
    async confirmRefunds(selectedRow) {
      this.sign = "refund";
      let paramsObj = {
        validParameter: {
          refundId: this.bankInfo.id,
          bankInfo: selectedRow,
        },
        validAction: "taxPageStore/actionApplyRefundFeeInventory",
        querytAction: "taxPageStore/actionApplyRefundFeeInventoryQuery",
        freeBackTip: "【获取/更新手续费清册】",
      };
      this.$refs.selectS.show(paramsObj);
    },
    withdraw(row) {
      this.$confirm("是否确认撤销对XXX公司的退付?", "手续费退费撤销确认", {
        confirmButtonText: "撤销退付",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          this.sign = "refund";
          let paramsObj = {
            validParameter: {
              refundId: row.id,
            },
            validAction: "taxPageStore/actionCancelRefundFeeInventory",
            querytAction: "taxPageStore/actionCancelRefundFeeInventoryQuery",
            freeBackTip: "【获取/更新手续费清册】",
          };
          this.$refs.selectS.show(paramsObj);
        })
        .catch(() => {});
    },
    getFeedback(row) {
      this.sign = "refund";
      let queryAction =
        row.formStatus === "审核中"
          ? "taxPageStore/actionCancelRefundFeeInventoryQuery"
          : "taxPageStore/actionApplyRefundFeeInventoryQuery";
      let paramsObj = {
        validParameter: {
          refundId: row.id,
        },
        querytAction: queryAction,
        freeBackTip: "【获取反馈】",
      };
      this.$refs.batchFeedback.show(paramsObj);
    },
    openDownloadDialog() {
      this.handlingFeesListDialog = true;
      this.map = {};
      this.tags = []
      this.dialogCurrPage = 1;
      this.getTaxSubList();
    },
    closeDownloadDialog() {
      this.$refs.taxSubTable.clearFilter();
      this.areaIds = [];
      this.taxSubName = "";
      this.handlingFeesListDialog = false;
    },
    handleChange() {
      this.dialogCurrPage = 1;
      this.getTaxSubList();
    },
    async getTaxSubList() {
      this.isLoading = true;
      const res = await apiGetTaxSubList({
        queryMonth: (() => {
          const currentDate = new Date();
          const year = currentDate.getFullYear();
          return `${year - 1}-01-01`;
        })(),
        areaIds: this.areaIds,
        taxSubName: this.taxSubName,
        currPage: this.dialogCurrPage,
        pageSize: 20,
      });
      this.isLoading = false;
      if (res.success) {
        this.taxSubTotal = res.data.count;
        res.data.data.forEach(item => item.selected = false)
        this.taxSubList = res.data.data;
        
        for (var i in this.map) {
          this.map[i].forEach(item1 => {
            this.taxSubList.forEach(item2 => {
              if (item1.taxSubId === item2.taxSubId) {
                item2.selected = true
              }
            })
          })
        }
        this.isShowSelectAll()
      }
    },
    handleClose(id) {
      for(var i in this.map) {
        this.map[i] = this.map[i].filter(it => {
          return it.taxSubId !== id
        })
      }
      this.isShowSelectAll()
      this.tags = Object.values(this.map).flat()
      this.taxSubList.forEach(item => {
        if(item.taxSubId === id) {
          item.selected = false
        }
      })
    },
    selectAllChange(val) {
      if(val) {
        this.map[this.dialogCurrPage] = [...this.taxSubList]
        this.map[this.dialogCurrPage].forEach(item => item.selected = true)
      }else {
        this.taxSubList.forEach(item => item.selected = false)
        this.map[this.dialogCurrPage] = []
      }
      this.tags = Object.values(this.map).flat()

      console.log('map222===', this.map)
    },
    handleCheckboxChange(row) {
      this.map[this.dialogCurrPage] = this.map[this.dialogCurrPage] || [];
      if(row.selected) {
        this.map[this.dialogCurrPage].push(row)
      }else {
        let indexToRemove = this.map[this.dialogCurrPage].findIndex(item => item.taxSubId === row.taxSubId);
        if (indexToRemove !== -1) {
          this.map[this.dialogCurrPage].splice(indexToRemove, 1);
        }
      }
      console.log('map111===', this.map)
      this.tags = Object.values(this.map).flat()
      this.isShowSelectAll()
    },
    isShowSelectAll() {
      if(!this.taxSubName) {
        if(this.map[this.dialogCurrPage] && this.map[this.dialogCurrPage].length === this.taxSubList.length) {
          this.selectAll = true
        }else {
          this.selectAll = false
        }
      }
    },
    handleFilterChange(filters) {
      this.areaIds = filters.areaIds;
      this.dialogCurrPage = 1;
      this.getTaxSubList();
    },
    dialogCurrentChange(val) {
      this.dialogCurrPage = val;
      this.getTaxSubList();
    },
    batchDownload() {
      const selectItems = Object.values(this.map).flat();
      if (!selectItems.length) {
        this.$message.warning("请选择要进行下载的公司！");
        return;
      }
      this.sign = "";
      this.handlingFeesListDialog = false;
      let taxSubIds = selectItems.map((item) => item.taxSubId)

      let paramsObj = {
        validParameter: {
          taxSubIds: taxSubIds,
          date: this.params.date,
        },
        validAction: "taxPageStore/actionGetRefundFeeInventory",
        querytAction: "taxPageStore/actionGetRefundFeeInventoryQuery",
        freeBackTip: "【获取反馈】",
      };
      this.$refs.selectS.show(paramsObj);
    },
    handleBatchFeedback() {
      this.sign = "";
      let paramsObj = {
        validParameter: {
          date: this.params.date,
        },
        querytAction: "taxPageStore/actionGetRefundFeeInventoryQuery",
        freeBackTip: "【获取反馈】",
      };
      this.$refs.batchFeedback.show(paramsObj);
    },
    handleCheck() {

    },
    handleQueryResult() {
      
    },
    async handleExport() {
      await apiRefundFeeInventoryListExport(this.params);
    },
    handleUpdateStatus() {},
    freshList() {
      this.$refs.listFilterArea.loadTaxSubjectInfoList();
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../assets/scss/helpers.scss";
.attach {
  .tax-content {
    padding: 0 20px;
  }
}
.el-tag {
  margin: 0 10px 5px 0
}
</style>
