// 导入fetch函数
import { fetch, fetchFile } from 'request/fetch';

/**
 * 获取汇算清缴任务列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回汇算清缴任务列表
 */
export const fetchTaxReconciliationTasks = async (params) => {
  console.log('查询参数:', params);

  return fetch({
    url: '/api/hrsaas-salary/taxReport/settlementStatus/settlementStatusList',
    method: 'post',
    data: params,
  });
};

/**
 * 触发所选企业的"获取更新数据"流程
 * @param {Object} payload - 请求参数
 * @param {Array} payload.taxSubIds - 企业ID数组
 * @param {number|string} payload.date - 年份
 * @returns {Promise} 返回任务受理结果
 */
export const initiateDataUpdate = async (payload) => {
  console.log('发起更新数据:', payload);

  return fetch({
    url: '/api/hrsaas-salary/taxReport/settlementStatus/getSettlementStatus',
    method: 'post',
    data: payload,
  });
};

/**
 * 获取正在进行的数据更新任务的反馈/状态
 * @returns {Promise} 返回任务反馈结果
 */
export const fetchUpdateFeedback = async (payload) => {
  return fetch({
    url: '/api/hrsaas-salary/taxReport/settlementStatus/getSettlementStatusFeedback',
    method: 'post',
    data: payload,
  });
};

/**
 * 导出汇算任务列表的当前视图
 * @param {Object} params - 导出参数，与查询参数相同
 * @returns {Promise} 返回文件流
 */
export const exportTaxReconciliationTasks = async (params) => {
  // /api/taxReport/settlementStatus/settlementStatusExport
  return fetchFile({
    url: '/api/hrsaas-salary/taxReport/settlementStatus/settlementStatusExport',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
};

/**
 * 获取特定汇算任务的详细信息
 * @param {Object} params - 查询参数
 * @param {number} params.settlementStatusId - 汇算状态ID
 * @param {string} params.reportStatus - 申报状态 (REPORTED: 已申报, NOT_REQUIRED: 未申报(可能无需申报), SHOULD_REPORT: 未申报(应报未报))
 * @param {boolean} params.needRemitPayment - 是否需要汇缴
 * @param {string} params.nameOrMore - 姓名/身份证号
 * @param {number} params.currPage - 当前页码
 * @param {number} params.pageSize - 每页条数
 * @returns {Promise} 返回汇算详情
 */
export const fetchTaxReconciliationDetails = async (params) => {
  return fetch({
    url: '/api/hrsaas-salary/taxReport/settlementStatus/settlementStatusDetailList',
    method: 'post',
    data: params,
  });
};

/**
 * 导出汇算详情列表的当前视图
 * @param {Object} params - 导出参数，与查询参数相同
 * @returns {Promise} 返回文件流
 */
export const exportTaxReconciliationDetails = async (params) => {
  return fetchFile({
    url: '/api/hrsaas-salary/taxReport/settlementStatus/settlementStatusDetailExport',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
};

// /api/taxReport/settlementStatus/deleteSettlementStatus
export const deleteTaxReconciliationTask = async (params) => {
  return fetch({
    url: '/api/hrsaas-salary/taxReport/settlementStatus/deleteSettlementStatus',
    method: 'post',
    data: params,
  });
};
