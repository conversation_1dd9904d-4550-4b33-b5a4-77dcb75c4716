<template>
  <el-select
    v-model="selectedAreas"
    :placeholder="placeholder"
    :clearable="clearable"
    :collapse-tags="collapseTags"
    @change="handleChange"
  >
    <el-option
      v-for="item in areaOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<script>
import { apiGetAreaList } from "../taxPaid/store/api.js";

export default {
  name: "AreaSelector",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: "请选择区域",
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    collapseTags: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      areaOptions: [],
      selectedAreas: this.value || [],
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedAreas = newVal || [];
      },
      immediate: true,
    },
  },
  created() {
    this.fetchAreaList();
  },
  methods: {
    onFieldReset() {
      this.$emit("input", null);
    },
    async fetchAreaList() {
      try {
        const response = await apiGetAreaList();
        if (response.success) {
          // Transform the API response to the format needed for el-select
          this.areaOptions = (response.data || []).map((area) => ({
            label: area.areaName,
            value: area.id,
          }));
        } else {
          this.$message.error(response.message || "获取区域列表失败");
        }
      } catch (error) {
        console.error("获取区域列表出错:", error);
        this.$message.error("获取区域列表出错");
      }
    },
    handleChange(value) {
      this.$emit("input", value);
      this.$emit("change", value);
    },
  },
};
</script>
