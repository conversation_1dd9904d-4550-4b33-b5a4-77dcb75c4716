<template>
  <el-dialog
    title="获取更新数据"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="区域名称">
          <el-select
            v-model="searchForm.areaId"
            placeholder="请选择区域名称"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in areaOptions"
              :key="item.id"
              :label="item.areaName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企业名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入企业名称"
            style="width: 200px"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格区域 -->
    <el-table
      ref="companyTable"
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      height="300px"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="areaId" label="区域名称" width="120">
        <template slot-scope="scope">
          <span>{{ getAreaName(scope.row.areaId) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="taxSubName" label="公司名称"></el-table-column>
    </el-table>

    <div class="dialog-footer" slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        :disabled="selectedCompanies.length === 0"
        @click="handleConfirm"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { apiTaxSubjectList } from "../tax/store/api.js";
import { apiGetAreaList } from "../taxPaid/store/api.js";
import { initiateDataUpdate } from "./apis.js";
export default {
  name: "GetUpdatedDataDialog",
  props: {
    year: {
      type: Number,
      default: new Date().getFullYear() - 1,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      searchForm: {
        areaId: "",
        name: "",
      },
      areaOptions: [],
      tableData: [],
      selectedCompanies: [],
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    },
  },
  methods: {
    getAreaName(areaId) {
      if (!this.areaOptions) {
        return "--";
      }
      const area = this.areaOptions.find((item) => item.id === areaId);
      return area ? area.areaName : "--";
    },
    async init() {
      this.loading = true;
      try {
        await this.fetchAreas();
        await this.fetchCompanies();
      } catch (e) {
        this.$message.error("获取公司/区域列表出错");
      } finally {
        this.loading = false;
      }
    },
    // 获取公司列表
    async fetchCompanies() {
      const params = {
        ...this.searchForm,
        // taxSubEnabled: "true",
      };
      const response = await apiTaxSubjectList(params);
      this.tableData = response.data || [];
      this.tableData = this.tableData.filter(
        (c) => c.accreditStatus === "SUCCESS"
      );
    },
    async fetchAreas() {
      const r = await apiGetAreaList({});
      this.areaOptions = r.data;
      this.areaOptions = this.areaOptions.filter((a) => a.isenable);
    },
    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedCompanies = selection;
    },
    // 处理搜索
    handleSearch() {
      this.fetchCompanies();
    },

    // 处理关闭
    handleClose() {
      this.$emit("update:visible", false);
      this.searchForm = {
        areaName: "",
        companyName: "",
      };
      this.selectedCompanies = [];
    },

    // 处理确认
    async handleConfirm() {
      if (this.selectedCompanies.length === 0) {
        this.$message.warning("请至少选择一个公司");
        return;
      }
      // const r = await initiateDataUpdate({
      //   taxSubIds: this.selectedCompanies.map((company) => company.taxSubId),
      //   date: this.year,
      // });
      this.$emit("confirm", this.selectedCompanies);
      this.handleClose();
    },
  },
};
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}
</style>
