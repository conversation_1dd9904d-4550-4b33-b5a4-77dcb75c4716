<template>
  <o-container :title="'汇算详情'" back="true">
    <!-- 筛选区域 -->
    <o-top-select
      ref="top-select"
      :formJson="topSelectFormJson"
      :immediate="true"
      labelWidth="120px"
      style="margin-top: 20px"
      @search="onSearch"
      @reset="onReset"
    />

    <!-- 已选择条数和全选 -->
    <div class="selection-info" style="display: flex; gap: 20px">
      <el-button @click="handleExport">导出</el-button>
    </div>

    <!-- 表格区域 -->
    <o-table
      ref="o-table"
      :sticky="true"
      :showPagination="true"
      :pagination="{ fixed: true }"
      :tableData="tableData"
      :tableHeader="tableHeader"
      :showTableHeaderAction="false"
      emptyHeight="calc(100vh - 450px)"
      @paginationChange="handlePageChange"
      @sizeChange="handleSizeChange"
    >
      <template #header-right>
        <el-button type="primary" @click="onSearch(searchConditions)"
          >查询</el-button
        >
        <el-button @click="onReset">重置</el-button>
      </template>
    </o-table>
  </o-container>
</template>

<script>
import {
  fetchTaxReconciliationDetails,
  exportTaxReconciliationDetails,
} from "./apis.js";

export default {
  computed: {
    id() {
      return this.$route.params.id;
    },
  },
  data() {
    return {
      id: this.$route.params.id, // 从路由参数获取ID
      searchConditions: {},
      tableData: [],
      errorMessage: "",
      selectedRows: [],
      selectAllPages: false,
      // 分页相关
      total: 0,
      currentPage: 1,
      pageSize: 20, // 默认每页20条

      // 筛选表单配置
      topSelectFormJson: [
        {
          type: "input",
          item: {
            prop: "nameOrMore",
            label: "姓名/身份证号",
            placeholder: "请输入姓名或身份证号",
          },
        },
        {
          type: "select",
          item: {
            prop: "reportStatus",
            label: "申报状态",
            value: "",
            options: [
              { label: "全部", value: "" },
              { label: "已申报", value: "REPORTED" },
              {
                label: "未申报(可能无需申报)",
                value: "NOT_REQUIRED",
              },
              { label: "未申报(应报未报)", value: "SHOULD_REPORT" },
            ],
            placeholder: "请选择",
          },
        },
        {
          type: "select",
          item: {
            prop: "needRemitPayment",
            label: "是否需要汇缴",
            value: "",
            options: [
              { label: "全部", value: "" },
              { label: "是", value: true },
              { label: "否", value: false },
            ],
            placeholder: "请选择",
          },
        },
      ],

      // 表格表头配置
      tableHeader: [
        { prop: "name", label: "姓名", width: "100px" },
        { prop: "idType", label: "证件类型", width: "100px" },
        { prop: "idNo", label: "证照号码", width: "180px" },
        {
          prop: "settlementStatus",
          label: "汇算状态",
          width: "250px",
        },
        {
          prop: "needRemitPayment",
          label: "是否需要汇缴",
          width: "120px",
        },
        {
          prop: "typesOfTaxRefundsAndSupplements",
          label: "退补税类型",
          width: "100px",
        },
        {
          prop: "reportStatus",
          label: "申报状态",
          width: "150px",
        },
        {
          prop: "dealStatus",
          label: "处理状态",
          width: "100px",
        },
        { prop: "failReason", label: "错误信息" },
      ],

      // 表头操作按钮
      tableHeaderActionButtons: [],
    };
  },
  created() {
    // 初始化时获取第一页数据
    this.currentPage = 1;
    this.getDetails();
  },
  methods: {
    // 搜索事件
    onSearch(form) {
      this.searchConditions = form;
      // 重置分页到第一页
      this.currentPage = 1;
      this.getDetails();
      // 重置选择状态
      this.selectedRows = [];
      this.selectAllPages = false;
    },

    // 重置事件
    onReset() {
      // 重置表单字段
      this.$refs["top-select"].resetFields();

      // 手动重置 custom 类型的字段
      this.topSelectFormJson.forEach((item) => {
        if (item.type === "custom" && item.item) {
          if (Array.isArray(item.item.value)) {
            item.item.value = [];
          } else {
            item.item.value = "";
          }
        }
      });

      this.searchConditions = {};
      // 重置分页到第一页
      this.currentPage = 1;
      this.getDetails();
      // 重置选择状态
      this.selectedRows = [];
      this.selectAllPages = false;
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      if (!this.selectAllPages) {
        this.selectedRows = selection;
      }
    },

    // 处理选择全部页面数据
    handleSelectAllPages(val) {
      if (val) {
        // 选中当前页所有数据
        this.$refs["o-table"].$refs["el-table"].toggleAllSelection();
        // 记录选中状态
        this.selectedRows = [...this.tableData];
      } else {
        // 取消选中
        this.selectedRows = [];
        this.$refs["o-table"].$refs["el-table"].clearSelection();
      }
    },

    // 处理分页变化
    handlePageChange(paginationData) {
      if (paginationData && typeof paginationData === "object") {
        this.currentPage = paginationData.start;
        this.pageSize = paginationData.limit;
      }

      this.getDetails();
      this.selectedRows = [];
      this.selectAllPages = false;
    },

    // 处理每页条数变化
    handleSizeChange(size) {
      console.log("每页条数变化:", size);
      this.pageSize = size;
      this.currentPage = 1;
      this.getDetails();
      // 重置选择状态
      this.selectedRows = [];
      this.selectAllPages = false;
    },

    // 获取详情数据
    async getDetails() {
      // 显示加载状态
      this.$nextTick(() => {
        if (this.$refs["o-table"]) {
          this.$refs["o-table"].loading = true;
        }
      });

      try {
        // 移除排序字段，它只用于UI展示
        const { sort, ...searchParams } = this.searchConditions;

        const params = {
          settlementStatusId: parseInt(this.id, 10), // 将路由参数ID转为汇算状态ID
          ...searchParams,
          currPage: this.currentPage, // 确保是数字格式
          pageSize: this.pageSize, // 确保是数字格式
        };

        const response = await fetchTaxReconciliationDetails(params);
        if (response.success) {
          // 使用新的响应格式
          this.tableData = response.data.data || [];
          this.total = response.data.count || 0;

          // 更新o-table的分页信息
          this.$nextTick(() => {
            if (this.$refs["o-table"]) {
              this.$refs["o-table"].total = this.total;
              this.$refs["o-table"].currentPage = this.currentPage;
              this.$refs["o-table"].pageSize = this.pageSize;
            }
          });
        } else {
          this.$message.error(response.message || "获取详情失败");
        }

        // 检查是否有查询失败的记录，如果有则显示错误信息
        const failedRecord = this.tableData.find(
          (item) => item.dealStatus !== "查询成功"
        );
        if (failedRecord) {
          this.errorMessage = failedRecord.failReason || "查询失败";
        } else {
          this.errorMessage = "";
        }
      } catch (error) {
        console.error("获取详情出错:", error);
        this.$message.error("获取详情出错");
      } finally {
        // 隐藏加载状态
        this.$nextTick(() => {
          if (this.$refs["o-table"]) {
            this.$refs["o-table"].loading = false;
          }
        });
      }
    },

    // 处理导出
    async handleExport() {
      try {
        const { sort, ...searchParams } = this.searchConditions;
        const params = {
          settlementStatusId: parseInt(this.id, 10), // 将路由参数ID转为汇算状态ID
          ...searchParams,
          currPage: this.currentPage, // 确保是数字格式
          pageSize: this.pageSize, // 确保是数字格式
        };

        await exportTaxReconciliationDetails(params);
      } catch (error) {
        console.error("导出出错:", error);
        this.$message.error("导出出错");
      }
    },
  },
};
</script>

<style scoped>
.tax-reconciliation {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
}

.error-message {
  margin-bottom: 20px;
}

.selection-info {
  margin: 10px 0;
  display: flex;
  align-items: center;
  font-size: 14px;
}
</style>
