<template>
  <o-container title="汇算状态查询">
    <!-- 描述文本 -->
    <div class="description-banner">
      <p>
        <i class="el-icon-info"></i>
        支持企业从税局获取员工上一年度汇算清缴情况，及归集雇员工个税各类申报外加申报补充处理功能。
      </p>
    </div>

    <!-- 筛选区域 -->
    <o-top-select
      ref="top-select"
      :formJson="topSelectFormJson"
      :immediate="true"
      labelWidth="120px"
      style="margin-top: 20px"
      @search="onSearch"
      @reset="handleReset"
    />

    <!-- 表格区域 -->
    <o-table
      ref="o-table"
      :sticky="true"
      :showPagination="true"
      :pagination="{ fixed: true }"
      :tableData="tableData"
      :tableHeader="tableHeader"
      :actionButtons="actionButtons"
      :tableHeaderActionButtons="dynamicTableHeaderActionButtons"
      :total="total"
      emptyHeight="calc(100vh - 450px)"
      @selection-change="handleSelectionChange"
      @paginationChange="handlePaginationChange"
    />

    <!-- 获取更新数据对话框 -->
    <get-updated-data-dialog
      :visible.sync="dialogVisible"
      :year="searchConditions.year"
      @confirm="handleInitiateDataUpdate"
    />

    <!-- 获取反馈结果对话框 -->
    <el-dialog
      title="获取反馈"
      class="feedDialog"
      :visible.sync="feedbackDialogVisible"
      width="500px"
    >
      <div v-if="feedbackLoading" class="feedback-loading">
        <div style="text-align: center; padding: 40px 0">
          <i
            class="el-icon-loading"
            style="font-size: 24px; color: #409eff"
          ></i>
          <p style="margin-top: 10px; color: #606266">正在获取反馈数据...</p>
        </div>
      </div>
      <div v-else-if="feedbackData.length === 0" class="feedback-empty">
        <p>无待反馈的任务</p>
      </div>
      <div v-else class="feedback-list">
        <div
          v-for="(item, index) in feedbackData"
          :key="index"
          class="feedback-item"
          style="display: flex; gap: 5px"
        >
          <div>【{{ item.taxSubName }}】</div>
          <div>
            任务{{ dealStatus2String(item.dealStatus) }}
            {{ item.failReason }}
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :disabled="feedbackLoading"
          @click="
            () => {
              feedbackDialogVisible = false;
            }
          "
        >
          我知道了
        </el-button>
      </span>
    </el-dialog>

    <!-- 任务处理中提示对话框 -->
    <el-dialog
      title="任务提交成功"
      class="processingDialog"
      :visible.sync="processingDialogVisible"
      width="500px"
    >
      <div class="processing-content">
        <div class="processing-message">
          <i
            class="el-icon-warning"
            style="color: #e6a23c; font-size: 18px; margin-right: 8px"
          ></i>
          <span style="color: #e6a23c; font-weight: bold"
            >任务处理中，请稍后点击【获取反馈】查询结果！</span
          >
        </div>

        <div v-if="processingCompanies.length > 0" class="processing-list">
          <p style="margin: 15px 0 10px 0; font-weight: bold">
            正在处理的企业：
          </p>
          <div
            v-for="(company, index) in processingCompanies"
            :key="index"
            class="processing-item"
          >
            <span>【{{ company.taxSubName }}】</span>
            <span style="color: #e6a23c">{{
              company.dealStatus === "PROCESSING"
                ? "处理中"
                : company.dealStatus
            }}</span>
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="processingDialogVisible = false">
          我知道了
        </el-button>
      </span>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="删除确认"
      :visible.sync="deleteDialogVisible"
      width="400px"
    >
      <p>确定要删除该任务吗？</p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="deleteLoading"
          @click="confirmDelete"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </o-container>
</template>

<script>
import {
  fetchTaxReconciliationTasks,
  initiateDataUpdate,
  fetchUpdateFeedback,
  deleteTaxReconciliationTask,
  exportTaxReconciliationTasks,
} from "./apis.js";
import GetUpdatedDataDialog from "./getUpdatedDataDialog.vue";
import TaxSubjectsSelector from "./taxSubjectsSelector.vue";
import AreaSelector from "./areaSelector.vue";
import YearSelector from "./yearSelector.vue";
export default {
  components: {
    GetUpdatedDataDialog,
  },
  computed: {
    // 动态生成表头操作按钮，支持 loading 状态
    dynamicTableHeaderActionButtons() {
      return [
        {
          align: "left",
          type: "button",
          label: "获取更新数据",
          click: () => this.handleShowDialog(),
        },
        {
          align: "left",
          type: "button",
          label: "获取反馈",
          loading: this.feedbackLoading,
          props: {
            loading: this.feedbackLoading,
          },
          click: () => this.handleGetFeedback(),
        },
        {
          align: "left",
          type: "button",
          label: "导出",
          props: {
            type: "plain",
          },
          click: () => this.handleExport(),
        },
      ];
    },
  },
  data() {
    return {
      searchConditions: {},
      tableData: [],
      selectedRows: [],
      dialogVisible: false,
      feedbackDialogVisible: false,
      deleteDialogVisible: false,
      deleteLoading: false,
      feedbackLoading: false,
      processingDialogVisible: false,
      currentTaskId: null,
      feedbackData: [],
      processingCompanies: [],
      // 分页相关
      total: 0,
      currentPage: 1,
      pageSize: 20,

      // 筛选表单配置
      topSelectFormJson: [
        {
          type: "custom",
          item: {
            prop: "year",
            label: "年度",
            value: new Date().getFullYear() - 1,
            component: YearSelector,
          },
        },
        {
          type: "custom",
          item: {
            prop: "taxSubIds",
            label: "企业名称",
            value: [],
            component: TaxSubjectsSelector,
            componentProps: {
              multiple: true,
              placeholder: "请选择企业",
              collapseTags: true,
              clearable: true,
            },
          },
        },
        {
          type: "select",
          item: {
            prop: "status",
            label: "更新状态",
            value: "",
            options: [
              { label: "全部", value: "" },
              { label: "处理中", value: "PROCESSING" },
              { label: "成功", value: "SUCCESS" },
              { label: "失败", value: "FAIL" },
            ],
            placeholder: "请选择",
          },
        },
        {
          type: "datePicker",
          item: {
            prop: "updateTime",
            label: "更新时间",
            type: "daterange",
            startPlaceholder: "开始日期",
            endPlaceholder: "结束日期",
            valueFormat: "yyyy-MM-dd",
          },
        },
        {
          type: "select",
          item: {
            prop: "allReport",
            label: "全员申报已否",
            value: "",
            options: [
              { label: "请选择", value: "" },
              { label: "是", value: true },
              { label: "否", value: false },
            ],
            placeholder: "请选择",
          },
        },
        {
          type: "custom",
          item: {
            prop: "areaId",
            label: "区域名称",
            value: "",
            component: AreaSelector,
            componentProps: {
              multiple: true,
              placeholder: "请选择区域",
              collapseTags: true,
              clearable: true,
            },
          },
        },
      ],

      // 表格表头配置
      tableHeader: [
        { prop: "taskNo", label: "任务编号", width: "220px" },
        { prop: "companyName", label: "公司名称" },
        { prop: "areaName", label: "区域名称" },
        {
          prop: "updateStatus",
          label: "更新状态",
          width: "100px",
          formatter: (row) => {
            const statusMap = {
              PROCESSING: "处理中",
              SUCCESS: "成功",
              FAIL: "失败",
            };
            return statusMap[row.updateStatus] || row.updateStatus;
          },
        },
        { prop: "updateTime", label: "更新时间", width: "150px" },
        { prop: "employeeCount", label: "人员数", width: "80px" },
        { prop: "declaredCount", label: "已汇算人数", width: "100px" },
        { prop: "undeclaredCount", label: "未汇算人数", width: "100px" },
        { prop: "failReason", label: "失败原因" },
      ],

      // 行操作按钮
      actionButtons: [
        {
          label: "查看详情",
          id: "view",
          ifShow: (row) => row.updateStatus === "SUCCESS",
          click: (row) => this.handleViewDetails(row),
        },
        {
          label: "删除",
          id: "delete",
          ifShow: (row) => ["SUCCESS", "FAIL"].includes(row.updateStatus),
          click: (row) => this.handleDelete(row),
        },
      ],

      // 表头操作按钮
      tableHeaderActionButtons: [],
    };
  },
  created() {
    this.initOptions();
    window.addEventListener("message", this.handleYearChange);
  },
  methods: {
    dealStatus2String(status) {
      switch (status) {
        case "SUCCESS":
          return "成功";
        case "FAIL":
          return "失败";
        case "PROCESSING":
          return "处理中";
        case "INIT":
          return "未处理";
        default:
          return status;
      }
    },
    handleYearChange(event) {
      if (event.data.action === "yearChanged") {
        this.searchConditions.year = event.data.year;
      }
    },
    // 初始化下拉选项
    initOptions() {
      // 这里可以从API获取企业名称和区域名称的选项
      // 示例数据
      const companyOptions = [
        { label: "北京小海豚科技有限公司", value: "北京小海豚科技有限公司" },
        { label: "上海小海豚科技有限公司", value: "上海小海豚科技有限公司" },
      ];

      // 更新表单配置
      this.topSelectFormJson.forEach((item) => {
        if (item.item && item.item.prop === "companyNames") {
          item.item.options = companyOptions;
        }
      });
    },

    // 获取年度选项（当前年份及之前4年）
    getYearOptions() {
      const currentYear = new Date().getFullYear();
      const options = [];

      for (let i = 1; i < 5; i++) {
        const year = currentYear - i;
        options.push({ label: `${year}年`, value: year });
      }

      return options;
    },

    // 搜索事件
    onSearch(form) {
      this.searchConditions = form;
      // 搜索时重置到第一页
      this.currentPage = 1;
      this.getList();
    },

    // 重置按钮事件
    handleReset() {
      this.searchConditions = {};
      // 重置时重置到第一页
      this.currentPage = 1;
    },

    // 查询按钮事件
    handleSearch() {
      const form = this.$refs["top-select"].getFormData();
      this.searchConditions = form;
      // 搜索时重置到第一页
      this.currentPage = 1;
      this.getList();
    },

    // 分页变化事件
    handlePaginationChange(pagination) {
      this.currentPage = pagination.start;
      this.pageSize = pagination.limit;
      this.getList();
    },

    // 获取列表数据
    async getList() {
      try {
        const params = {
          ...this.searchConditions,
          currPage: this.currentPage,
          pageSize: this.pageSize,
        };
        if (!params.year) {
          params.year = new Date().getFullYear() - 1;
        }
        if (!params.status) {
          delete params.status;
        }
        // 处理日期范围
        if (params.updateTime && params.updateTime.length === 2) {
          params.startDate = params.updateTime[0];
          params.endDate = params.updateTime[1];
        }
        delete params.updateTime;

        const response = await fetchTaxReconciliationTasks(params);
        console.log("API响应数据结构:", response.data);
        if (response.success) {
          // 转换响应数据格式以匹配表格需要的数据结构
          const list = response.data.data.map((item) => ({
            id: item.id,
            taskNo: item.taskNo,
            companyName: item.taxSubName,
            areaName: item.areaName,
            updateStatus: item.updateStatus,
            updateTime: item.updateTime,
            employeeCount: item.totalStaff,
            declaredCount: item.settledStaff,
            undeclaredCount: item.unsettledStaff,
            failReason: item.failReason || "",
          }));

          this.tableData = list;
          // 设置总数据量，尝试多个可能的字段名
          this.total =
            response.data.total ||
            response.data.count ||
            response.data.totalCount ||
            0;
        } else {
          this.$message.error(response.message || "获取列表失败");
        }
      } catch (error) {
        console.error("获取列表出错:", error);
        this.$message.error("获取列表出错");
      }
    },

    // 显示获取更新数据对话框
    handleShowDialog() {
      this.dialogVisible = true;
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 处理获取更新数据
    async handleInitiateDataUpdate(companies) {
      try {
        // 构建请求参数
        const taxSubIds = companies.map((company) => company.taxSubId);

        const response = await initiateDataUpdate({
          taxSubIds: taxSubIds,
          date: this.searchConditions.year,
        });
        if (response.success) {
          // 处理响应数据

          const status = response.data.status;
          const taxSubList = response.data.taxSubList || [];

          // 显示处理状态
          if (status === "SUCCESS") {
            this.$message.success("获取更新数据任务已提交");

            // 检查是否有处理中的企业
            const processingCompanies = taxSubList.filter(
              (item) => item.dealStatus === "PROCESSING"
            );

            if (processingCompanies.length > 0) {
              // 保存处理中的企业数据并显示提示对话框
              this.processingCompanies = processingCompanies;
              this.processingDialogVisible = true;
            }

            this.getList(); // 刷新列表
          } else {
            this.$message.warning(
              response.data.failReason || "获取更新数据任务状态异常"
            );
          }
        } else {
          this.$message.error(response.message || "获取更新数据失败");
        }
      } catch (error) {
        console.error("获取更新数据出错:", error);
        this.$message.error("获取更新数据出错");
      }
    },

    // 处理获取反馈
    async handleGetFeedback() {
      this.feedbackLoading = true;
      try {
        const response = await fetchUpdateFeedback({
          date: this.searchConditions.year,
        });
        if (response.success) {
          this.feedbackData = response.data.taxSubList || [];
          this.feedbackDialogVisible = true;

          // 如果有反馈数据，刷新列表
          if (this.feedbackData.length > 0) {
            this.getList();
          }
        }
      } catch (error) {
        console.error("获取反馈出错:", error);
        this.$message.error("获取反馈出错");
      } finally {
        this.feedbackLoading = false;
      }
    },

    // 处理查看详情
    handleViewDetails(row) {
      this.$router.push(`/taxReconciliations/${row.id}`);
    },

    // 处理删除
    handleDelete(row) {
      this.currentTaskId = row.id;
      this.deleteDialogVisible = true;
    },

    // 确认删除
    async confirmDelete() {
      if (!this.currentTaskId) return;

      this.deleteLoading = true;
      try {
        const response = await deleteTaxReconciliationTask({
          settlementStatusId: this.currentTaskId,
        });

        if (response.success) {
          this.$message.success("删除成功");
          this.getList(); // 刷新列表
        } else {
          this.$message.error(response.message || "删除失败");
        }
      } catch (error) {
        console.error("删除出错:", error);
        this.$message.error("删除出错");
      } finally {
        this.deleteLoading = false;
        this.deleteDialogVisible = false;
        this.currentTaskId = null;
      }
    },

    // 处理导出
    async handleExport() {
      try {
        const params = {
          ...this.searchConditions,
          currPage: 1,
          pageSize: this.total || 1000, // 导出时尽量获取所有数据
        };
        if (!params.year) {
          params.year = new Date().getFullYear() - 1;
        }
        if (!params.status) {
          params.status = "SUCCESS";
        }
        // 处理日期范围
        if (params.updateTime && params.updateTime.length === 2) {
          params.startDate = params.updateTime[0];
          params.endDate = params.updateTime[1];
        }
        delete params.updateTime;

        await exportTaxReconciliationTasks(params);
      } catch (error) {
        console.error("导出出错:", error);
        this.$message.error("导出出错");
      }
    },
  },
};
</script>

<style scoped>
.tax-reconciliations {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
}

.description-banner {
  background-color: #f5f7fa;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.description-banner p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.feedback-empty {
  text-align: center;
  padding: 20px 0;
  color: #909399;
}

.feedback-list {
  max-height: 300px;
  overflow-y: auto;
}

.feedback-item {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.feedback-item:last-child {
  border-bottom: none;
}

.feedback-loading {
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feedback-loading .el-icon-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.processing-content {
  padding: 10px 0;
}

.processing-message {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #fdf6ec;
  border: 1px solid #f5dab1;
  border-radius: 4px;
  margin-bottom: 15px;
}

.processing-list {
  max-height: 200px;
  overflow-y: auto;
}

.processing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.processing-item:last-child {
  border-bottom: none;
}
</style>
