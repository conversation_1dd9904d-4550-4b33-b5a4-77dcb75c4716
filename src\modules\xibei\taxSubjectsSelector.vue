<template>
  <div class="tax-subjects-selector">
    <el-select
      v-model="selectedValues"
      multiple
      :placeholder="placeholder"
      :disabled="disabled"
      :clearable="clearable"
      :collapse-tags="collapseTags"
      filterable
      remote
      :remote-method="remoteSearch"
      :loading="loading"
      @change="handleChange"
      @visible-change="handleVisibleChange"
      style="width: 100%"
    >
      <el-option
        v-for="item in options"
        :key="item.taxSubId"
        :label="item.taxSubName"
        :value="item.taxSubId"
      >
        <div class="tax-subject-option">
          <span>{{ item.taxSubName }}</span>
          <small v-if="item.areaName" class="tax-subject-area">{{
            item.areaName
          }}</small>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { apiTaxSubjectList } from "../tax/store/api.js";

export default {
  name: "TaxSubjectsSelector",
  props: {
    value: {
      type: [Array, String, Number],
      default: () => [],
    },
    placeholder: {
      type: String,
      default: "请选择企业",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    collapseTags: {
      type: Boolean,
      default: true,
    },
    areaId: {
      type: [String, Number],
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      options: [],
      selectedValues: this.value,
      searchKeyword: "",
      cacheOptions: {}, // 缓存已加载的选项
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedValues = newVal;
        // 如果有选中值但没有对应的选项数据，则加载选中值的详细信息
        this.loadSelectedOptions();
      },
      immediate: true,
    },
    areaId: {
      handler() {
        this.loadOptions();
      },
    },
  },
  created() {
    // 初始加载选项
    this.loadOptions();
  },
  methods: {
    onFieldReset() {
      this.$emit("input", []);
    },
    // 加载选项
    async loadOptions(keyword = "") {
      this.loading = true;
      try {
        // 构建查询参数
        const params = {
          name: keyword,
          currPage: 1,
          pageSize: 50,
        };

        // 如果指定了区域ID，则添加到查询参数
        if (this.areaId) {
          params.areaId = this.areaId;
        }

        // 调用API获取企业列表
        const response = await apiTaxSubjectList(params);

        if (response.success) {
          this.options = response.data || [];

          // 缓存结果
          this.cacheOptions[keyword || "default"] = this.options;
        } else {
          this.$message.error(response.message || "获取企业列表失败");
        }
      } catch (error) {
        console.error("获取企业列表出错:", error);
        this.$message.error("获取企业列表出错");
      } finally {
        this.loading = false;
      }
    },

    // 远程搜索
    remoteSearch(query) {
      if (query) {
        this.searchKeyword = query;

        // 如果缓存中有该关键词的结果，则直接使用缓存
        if (this.cacheOptions[query]) {
          this.options = this.cacheOptions[query];
          return;
        }

        // 否则重新加载
        this.loadOptions(query);
      } else {
        this.searchKeyword = "";
        this.loadOptions();
      }
    },

    // 处理选择变化
    handleChange(val) {
      this.$emit("input", val);
      this.$emit("change", val);
    },

    // 处理下拉框显示/隐藏
    handleVisibleChange(visible) {
      if (visible) {
        // 如果没有搜索关键词，则重新加载选项
        if (!this.searchKeyword) {
          this.loadOptions();
        }
      }
    },

    // 加载已选中选项的详细信息
    async loadSelectedOptions() {
      // 如果没有选中值，则不需要加载
      if (
        !this.selectedValues ||
        (Array.isArray(this.selectedValues) && this.selectedValues.length === 0)
      ) {
        return;
      }

      // 检查是否需要加载选中值的详细信息
      const selectedIds = Array.isArray(this.selectedValues)
        ? this.selectedValues
        : [this.selectedValues];
      const needToLoad = selectedIds.some(
        (id) => !this.options.find((option) => option.taxSubId === id)
      );

      if (needToLoad) {
        this.loading = true;
        try {
          // 构建查询参数
          const params = {
            taxSubIds: selectedIds.join(","),
            currPage: 1,
            pageSize: selectedIds.length,
          };

          // 调用API获取企业详细信息
          const response = await apiTaxSubjectList(params);

          if (response.success && response.data) {
            // 合并新获取的选项和现有选项
            const newOptions = response.data.filter(
              (item) =>
                !this.options.find(
                  (option) => option.taxSubId === item.taxSubId
                )
            );

            if (newOptions.length > 0) {
              this.options = [...this.options, ...newOptions];
            }
          }
        } catch (error) {
          console.error("获取选中企业详情出错:", error);
        } finally {
          this.loading = false;
        }
      }
    },
  },
};
</script>

<style scoped>
.tax-subjects-selector {
  width: 100%;
}

.tax-subject-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tax-subject-area {
  color: #909399;
  font-size: 12px;
}
</style>
