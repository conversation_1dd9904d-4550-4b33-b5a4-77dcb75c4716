<template>
  <el-select
    v-model="selectedYear"
    :placeholder="placeholder"
    :clearable="clearable"
    @change="handleChange"
  >
    <el-option
      v-for="item in yearOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<script>
export default {
  name: "YearSelector",
  props: {
    value: {
      type: Number,
      default: new Date().getFullYear() - 1,
    },
    placeholder: {
      type: String,
      default: "请选择年度",
    },
    clearable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      selectedYear: this.value,
      yearOptions: this.getYearOptions(),
    };
  },
  methods: {
    getYearOptions() {
      const currentYear = new Date().getFullYear();
      const options = [];

      for (let i = 1; i < 5; i++) {
        const year = currentYear - i;
        options.push({ label: `${year}年`, value: year });
      }
      return options;
    },
    handleChange(value) {
      this.$emit("input", value);
      window.postMessage({
        action: "yearChanged",
        year: value,
      });
    },
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedYear = newVal;
      },
      immediate: true,
    },
  },
  created() {
    this.yearOptions = this.getYearOptions();
  },
};
</script>

<style></style>
