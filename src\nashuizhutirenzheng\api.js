export const verify = async (params) => {
  const abortController = new AbortController();
  const timeoutAdapter = new Promise((resolve, reject) => {
    setTimeout(() => {
      reject('Timeout');
      abortController.abort();
    }, 30000);
  });
  var uri  = '/api/hrsaas-salary/citicTaxSubjectAuth/TaxSubjectAuth'
  if(window.location.href.includes("/gd/hrsaas")){
    uri = '/gd/hrsaas/webapi'+uri
  }else if(window.location.href.includes("/hrsaas")){
    uri = '/hrsaas/webapi'+uri
  }
  var [err, result] = await Promise.race([
    timeoutAdapter,
    fetch(uri, {
      method: 'POST',
      headers: {
        'content-type': 'application/json',
      },
      body: JSON.stringify(params),
      signal: abortController.signal,
    }),
  ])
    .then((res) => res.json())
    .then((data) => [null, data])
    .catch((err) => [err, null]);

  return [err, result];
};
