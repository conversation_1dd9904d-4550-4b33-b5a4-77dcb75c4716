<template>
  <div class="nashuizhuti">
    <div
      id="succeed"
      style="
        margin: 0 auto;
        padding: 180px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        display: none;
      "
    >
      <div
        style="
          width: 60px;
          height: 60px;
          background: #4bca8b;
          border-radius: 50%;
          font-size: 48px;
          font-weight: 300;
          line-height: 60px;
          color: #fff;
          text-align: center;
        "
      >
        ✓
      </div>
      纳税主体验证任务已提交
      <p>请返回开薪易系统后点击[获取反馈] 查询任务处理结果</p>
    </div>
    <div
      id="failed"
      style="
        margin: 0 auto;
        padding: 180px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        display: none;
      "
    >
      <div
        style="
          width: 60px;
          height: 60px;
          background: #e6453c;
          border-radius: 50%;
          font-size: 48px;
          font-weight: 300;
          line-height: 60px;
          color: #fff;
          text-align: center;
        "
      >
        ✘
      </div>
      纳税主体验证任务提交失败
      <p>请返回开薪易系统后，刷新操作页面并重新提交验证任务</p>
    </div>

    <div id="content">
      <div class="title">纳税主体认证</div>
      <div class="container">
        <div style="margin: 0 auto">
          <!-- 公司基本信息 -->
          <div class="section">
            <h3>公司基本信息</h3>
            <div class="input-box">
              <label>公司全称</label>
              <input type="text" v-model="name" value="" disabled />
            </div>
            <div class="input-box">
              <label>纳税人识别号</label>
              <input type="text" v-model="no" disabled />
            </div>
          </div>

          <!-- 纳税主体验证信息 -->
          <div class="section">
            <h3>纳税主体验证信息</h3>
            <div class="input-box">
              <label><span class="info-note">*</span>经办人姓名</label>
              <input type="text" placeholder="请输入经办人姓名" id="contact" />
            </div>
            <div class="input-box">
              <label><span class="info-note">*</span>申报密码</label>
              <input
                type="password"
                id="password"
                placeholder="请输入申报密码"
              />
            </div>
            <div class="input-box">
              <label></label>
              <span style="font-size: 12px; color: #ccc"
                >自然人电子税务局客户端的申报密码</span
              >
            </div>
            <div
              class="checkbox-area"
              style="margin-left: 150px; display: flex; align-items: center"
            >
              <input type="checkbox" id="agree" />
              <label for="agree"> 阅读且同意阿拉钉 </label>
              <a
                id="showProtocol"
                @click="showProtocol()"
                style="cursor: pointer"
              >
                《用户服务协议》
              </a>
            </div>
          </div>

          <!-- 页脚 -->
        </div>
        <div class="footer-text">
          注：当前系统页面来自于北京阿拉钉科技有限公司<br />
          COPYRIGHT © 北京阿拉钉科技有限公司 京ICP备19024749号-1 京公网安备
          11010502046854号
        </div>
      </div>
      <!-- 按钮部分 -->
      <div
        class="buttons"
        style="
          border-top: 1px solid #ccc;
          display: flex;
          align-items: center;
          height: 60px;
          justify-content: center;
        "
      >
        <button type="button" id="submit" @click="submit()">提交</button>
        <!-- <button type="button" class="cancel" onclick="cancel()">取消</button> -->
      </div>
    </div>
  </div>
</template>

<script>
import crypto from "sm-crypto";
import message from "./message";
import dialog from "./dialog";
import { verify } from "./api";

const sm2 = crypto.sm2;
// 1 - C1C3C2，0 - C1C2C3，默认为1
const cipherMode = 1;

const publicKey =
  "045a0d44057038d8d86aa568baade93bf16858c997d596d4bdb57ce2a6782061f36b94f81628a1b1cb4d4f7ce6d51878ceb4c9f916f2d079a73b67552cdb2248d7";

var submitting = false;

const encryptData = (msgString) => {
  return sm2.doEncrypt(msgString, publicKey, cipherMode);
};

const decryptData = (value) => {
  return sm2.doDecrypt(value, privateKey, cipherMode);
};
export default {
  data() {
    return {
      token: "",
      name: "",
      no: "",
    };
  },
  created() {
    const urlParams = new URLSearchParams(window.location.search);
    const name = urlParams.get("name");
    const no = urlParams.get("no");
    const token = urlParams.get("token");
    this.name = name;
    this.no = no;
    this.token = token;
  },
  methods: {
    async submit() {
      if (submitting) {
        return;
      }

      submitting = true;
      setTimeout(() => (submitting = false), 5000);

      const agreeEl = document.getElementById("agree");
      const contactEl = document.getElementById("contact");
      const passwordEl = document.getElementById("password");
      if (!agreeEl.checked) {
        // 使用示例
        message({
          type: "error",
          message: "请阅读且同意阿拉钉用户服务协议",
          duration: 3000, // 消息显示的持续时间，单位是毫秒
        });

        return;
      }
      if (!contactEl.value.trim()) {
        message({
          type: "error",
          message: "请输入经办人姓名",
          duration: 3000,
        });
        return;
      }
      if (contactEl.value.trim().length > 16) {
        message({
          type: "error",
          message: "经办人姓名最长只允许16位",
          duration: 3000,
        });
        return;
      }
      if (!passwordEl.value.trim()) {
        message({
          type: "error",
          message: "请输入申报密码",
          duration: 3000,
        });
        return;
      }
      if (passwordEl.value.trim().length > 32) {
        message({
          type: "error",
          message: "经办人姓名最长只允许32位",
          duration: 3000,
        });
        return;
      }
      if (!this.token.trim()) {
        message({
          type: "error",
          message: "仅支持从开薪易进行申报",
          duration: 3000,
        });
        return;
      }
      const params = {
        remark: contactEl.value.trim(),
        pwd: encryptData(passwordEl.value.trim()),
        // pwd: passwordEl.value.trim(),
        taxSubName: this.name,
        taxPayerNo: this.no,
        agreed: agreeEl.checked,
        token: this.token,
      };

      const res = await verify(params);

      submitting = false;

      const [err, r] = res;
      if (err || (r && !r.success)) {
        document.getElementById("content").style.display = "none";
        document.getElementById("failed").style.display = "flex";

        return;
      }

      document.getElementById("content").style.display = "none";
      document.getElementById("succeed").style.display = "flex";
    },
    showProtocol() {
      const iframe = document.createElement("iframe");
      iframe.style.border = "none";
      iframe.style.width = "100%";
      iframe.style.height = "100%";
      const href = window.location.origin + window.location.pathname;
      const index = href.indexOf("/hrsaas/xst");
      var prefix = "";
      if (index !== -1) {
        prefix = href.substring(0, index) + "/hrsaas/xst";
      } else {
        prefix = window.location.origin;
      }
      const src = prefix + "/static/userServiceProtocol.html";
      iframe.src = src;

      dialog({
        confirmText: "我已阅读",
        contentEl: iframe,
      });
    },
  },
};
</script>

<style></style>
