<template>
  <div class="baseInsuredDetailsNew">
    <el-tabs value="details" @tab-click="handleClick">
      <el-tab-pane label="参保方案" name="base"> </el-tab-pane>
      <el-tab-pane label="参保条目" name="details">
        <div v-if="baseInsuredDetails.length">
          <div
            style="display: flex"
            :key="index"
            v-for="(baseInsuredDetail, index) in baseInsuredDetails"
          >
            <span style="flex: 0 0 40px">{{ index + 1 }}</span>
            <BaseInsuredDetailForm
              :baseInsuredDetail="baseInsuredDetail"
              @onDelete="onDelete"
              @onUpdate="onUpdate"
            />
          </div>
        </div>
        <br />

        <BaseInsuredDetailForm
          @onCreate="onSubmit"
          style="position: relative; left: 40px"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import BaseInsuredDetailForm from '../../components/boss/baseInsuredDetailForm.vue'
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()
export default {
  components: {
    BaseInsuredDetailForm
  },
  computed: {
    projectId() {
      return this.$route.query.projectId
    }
  },
  data() {
    return {
      baseInsuredDetails: []
    }
  },
  created() {
    this.load()
  },
  methods: {
    async load() {
      if (!this.projectId) {
        return
      }

      const [err, r] = await client.detailBaseInsuredProject({
        body: {
          id: this.projectId
        }
      })
      if (err) {
        handleError(err)
        return
      }

      for (var c of r.baseInsuredProject.baseInsuredDetails) {
        c.compScale = c.compScale * 100
        c.personScale = c.personScale * 100
      }

      this.baseInsuredDetails = r.baseInsuredProject.baseInsuredDetails
    },
    handleClick(tab) {
      if (tab.name === 'base') {
        this.onPrev()
      }
    },
    onPrev() {
      this.$router.replace(`/baseInsuredProjects/edit?id=${this.projectId}`)
    },
    async onSubmit(baseInsuredDetail, successCallback) {
      baseInsuredDetail.compScale = baseInsuredDetail.compScale / 100
      baseInsuredDetail.personScale = baseInsuredDetail.personScale / 100
      const [err] = await client.appendBaseInsuredDetail({
        body: {
          projectId: this.projectId,
          baseInsuredDetail
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.$message.success('新增成功')
      successCallback()

      this.load()
    },
    async onDelete(baseInsuredDetail) {
      const projectId = baseInsuredDetail.baseInsuredId
      const [err] = await client.deleteBaseInsuranceDetail({
        body: {
          projectId: projectId,
          id: baseInsuredDetail.id
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.load()
    },
    async onUpdate(baseInsuredDetail) {
      baseInsuredDetail.compScale = baseInsuredDetail.compScale / 100
      baseInsuredDetail.personScale = baseInsuredDetail.personScale / 100

      const projectId = baseInsuredDetail.baseInsuredId
      const [err] = await client.changeBaseInsuredDetail({
        body: {
          projectId,
          baseInsuredDetail
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.$message.success('修改成功')
    }
  }
}
</script>

<style></style>
