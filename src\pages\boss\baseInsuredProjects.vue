<template>
  <div class="baseInsuredProjects">
    <div style="text-align: right; padding-right: 40px">
      <el-button
        type="primary"
        @click="$router.push('/baseInsuredProjects/new')"
      >
        新增
      </el-button>
    </div>
    <br />
    <!-- search form -->
    <el-form :inline="true" class="search">
      <el-form-item label="方案编号">
        <el-input v-model="conditions.projectId"></el-input>
      </el-form-item>
      <el-form-item label="参保方案">
        <el-input v-model="conditions.insuredName"></el-input>
      </el-form-item>
      <el-form-item label="所属城市">
        <el-select
          v-model="conditions.insuredCity"
          filterable
          clearable
          value-key="code"
        >
          <el-option
            v-for="item in cities"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <div
      style="height: calc(100vh - 250px); overflow: hidden; overflow-y: auto"
    >
      <BaseInsuredProject
        :baseInsuredProject="baseInsuredProject"
        :key="index"
        v-for="(baseInsuredProject, index) in baseInsuredProjects"
        style="margin-bottom: 20px; width: 955px"
        @onEdit="onEdit(baseInsuredProject.id)"
        @onDelete="onDelete(baseInsuredProject.id)"
      />
    </div>

    <!-- project list -->
    <div style="text-align: right; margin-top: 20px">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="10"
        layout="total, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentPageChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { Loading } from 'element-ui'
import BaseInsuredProject from '../../components/boss/baseInsuredProject.vue'
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()

export default {
  computed: {
    currentPage() {
      return this.conditions.offset / this.conditions.limit + 1
    }
  },
  components: {
    BaseInsuredProject
  },
  data() {
    return {
      cities: [],
      conditions: {
        projectId: '',
        insuredName: '',
        insuredCity: '',
        offset: 0,
        limit: 10
      },
      baseInsuredProjects: [],
      total: 0
    }
  },
  async created() {
    let loadingInstance = Loading.service()
    await this.loadCities()
    await this.reload()
    loadingInstance.close()
  },
  methods: {
    async loadCities() {
      const [err, r] = await client.listCities()
      if (err) {
        handleError(err)
        return
      }

      this.cities = r.cities || []
    },
    async reload() {
      const params = {
        body: this.conditions
      }
      const [err, r] = await client.listBaseInsurances(params)
      if (err) {
        handleError(err)
        return
      }

      this.baseInsuredProjects = r.baseInsuredProject
      this.total = r.total
    },

    onSearch() {
      this.conditions.offset = 0;
      this.reload()
    },
    onReset() {
      this.conditions = {
        projectId: '',
        insuredName: '',
        insuredCity: ''
      }
      this.reload()
    },

    onEdit(projectId) {
      this.$router.push(`/baseInsuredProjects/edit?id=${projectId}`)
    },
    async onDelete(Id) {
      const [err, r] = await client.deleteBaseInsuredProject({
        body: { id: Id }
      })
      if (err) {
        handleError(err)
        return
      }
      this.reload()
    },
    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0

      this.reload()
    },
    handleCurrentPageChange(currentPage) {
      const offset = (currentPage - 1) * this.conditions.limit
      this.conditions.offset = offset
      this.reload()
    }
  }
}
</script>

<style></style>
