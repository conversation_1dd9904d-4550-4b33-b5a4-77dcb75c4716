<template>
  <div class="baseInsuredProjectsNew">
    <el-tabs value="base" @tab-click="handleClick">
      <el-tab-pane label="参保方案" name="base">
        <el-form
          ref="form"
          :model="baseInsuredProject"
          label-width="140px"
          :rules="rules"
          style="width: 400px"
        >
          <el-form-item prop="insuredName" label="参保方案名称">
            <el-input v-model="baseInsuredProject.insuredName"></el-input>
          </el-form-item>
          <el-form-item prop="city" label="所属城市">
            <el-select
              v-model="baseInsuredProject.city"
              filterable
              clearable
              value-key="code"
            >
              <el-option
                v-for="city in cities"
                :key="city.code"
                :label="city.name"
                :value="city"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="accumulationFundYn" label="是否缴纳公积金">
            <el-select v-model="baseInsuredProject.accumulationFundYn">
              <el-option label="是" value="0"></el-option>
              <el-option label="否" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              type="textarea"
              v-model="baseInsuredProject.remark"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="!id" style="text-align: right">
            <el-button type="primary" @click="onCreate">创建</el-button>
          </el-form-item>
          <el-form-item v-else style="text-align: right">
            <el-button type="primary" @click="onUpdate">更新</el-button>
            <el-button @click="onNext">下一步</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane
        :disabled="id ? false : true"
        label="参保条目"
        name="details"
      >
        <BaseInsuredDetailForm />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import BaseInsuredDetailForm from '../../components/boss/baseInsuredDetailForm.vue'
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()
export default {
  components: {
    BaseInsuredDetailForm
  },
  computed: {
    id() {
      return this.$route.query.id
    }
  },
  data() {
    return {
      cities: [],
      rules: {
        insuredName: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        city: [
          {
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (!value || !value.code) {
                callback(new Error('请选择城市'))
                return
              }
              callback()
            }
          }
        ],
        accumulationFundYn: [
          { required: true, message: '请选择是否缴纳公积金', trigger: 'blur' }
        ]
      },
      baseInsuredProject: {
        id: '',
        insuredName: '',
        city: {
          name: '',
          code: ''
        },
        accumulationFundYn: '0',
        baseInsuredDetails: [],
        remark: ''
      }
    }
  },
  created() {
    this.loadCities()
    this.load()
  },
  methods: {
    handleClick(tab) {
      if (tab.name === 'details') {
        this.onNext()
      }
    },
    async loadCities() {
      const [err, r] = await client.listCities()
      if (err) {
        handleError(err)
        return
      }

      this.cities = r.cities || []
    },

    async remoteSearch(query) {
      if (query !== '') {
        // 这里调用API的方法应该是根据输入的queryString来查询接口
        const [err, r] = await client.listCities({
          params: {
            search: query
          }
        })

        if (err) {
          handleError(err)
          return
        }

        // 假设API正确返回了基于查询的城市数据
        this.cities = r.cities || []
      } else {
        // 如果没有输入值，可能希望加载默认的或全部的城市列表
        this.loadCity()
      }
    },
    async load() {
      if (!this.id) {
        return
      }

      const [err, r] = await client.detailBaseInsuredProject({
        body: {
          id: this.id
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.baseInsuredProject = r.baseInsuredProject
    },
    onCreate() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this._create()
        }
      })
    },
    async _create() {
      const [err, r] = await client.createBaseInsuredProject({
        body: {
          baseInsuredProject: this.baseInsuredProject
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.baseInsuredProject.id = r.id

      this.onNext()
    },
    onUpdate() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this._onUpdate()
        }
      })
    },
    async _onUpdate() {
      const [err] = await client.updateBaseInsuredProject({
        body: {
          id: this.$route.query.id,
          baseInsuredProject: this.baseInsuredProject
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.onNext()
    },
    onNext() {
      this.$router.replace(
        `/baseInsuredDetails/new?projectId=${this.baseInsuredProject.id}`
      )
    }
  }
}
</script>

<style></style>
