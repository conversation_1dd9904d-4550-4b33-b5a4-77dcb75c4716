<template>
  <div class="legal-detail-container">
    <el-row :gutter="20" class="info-section">
      <!-- 法人信息卡片 -->
      <el-col :span="12">
        <el-card shadow="hover" class="info-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-user"></i> 法人实体</span>
          </div>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="法人姓名">{{
              legal.name || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="证件类型">{{
              getIdTypeText(legal.idType) || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="证件号码">{{
              legal.idNo || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="管户客户经理">{{
              accountManagerName || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="管户人机构名称">{{
              accountManagerOrgName || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="纳税主体授权状态">
              <el-tag
                v-if="legal.accreditStatus === 'SUCCESS'"
                type="success"
                size="small"
                >授权通过</el-tag
              >
              <el-tag
                v-else-if="legal.accreditStatus === 'FAIL'"
                type="danger"
                size="small"
                >授权失败</el-tag
              >
              <el-tag
                v-else-if="legal.accreditStatus === 'WAIT_ACCREDIT'"
                type="info"
                size="small"
                >未授权</el-tag
              >
              <span v-else>{{ legal.accreditStatus || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="合同认证状态">
              <el-tag
                v-if="legal.contractAuthStatus === 'SUCCESS'"
                type="success"
                size="small"
                >认证通过</el-tag
              >
              <el-tag
                v-else-if="legal.contractAuthStatus === 'FAIL'"
                type="danger"
                size="small"
                >认证不通过</el-tag
              >
              <el-tag
                v-else-if="legal.contractAuthStatus === 'INIT'"
                type="info"
                size="small"
                >未认证</el-tag
              >
              <el-tag
                v-else-if="legal.contractAuthStatus === 'STASH'"
                type="warning"
                size="small"
                >待提交</el-tag
              >
              <el-tag
                v-else-if="legal.contractAuthStatus === 'COMMIT'"
                type="warning"
                size="small"
                >待审核</el-tag
              >
              <span v-else>{{ legal.contractAuthStatus || '-' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="info-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-office-building"></i> 企业基本信息</span>
          </div>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="企业ID">{{
              legal.merchantId || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="公司名称">{{
              legal.merchantName || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="统一社会信用代码">{{
              legal.taxPayerNo || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="注册地址">{{
              legal.registeredAddress || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="员工人数">{{
              legal.employeeCount || '-'
            }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>

    <!-- 申请人信息卡片 -->
    <el-card shadow="hover" class="info-card">
      <div slot="header" class="card-header">
        <span><i class="el-icon-phone-outline"></i> 申请人信息</span>
      </div>
      <el-row>
        <el-col :span="12">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="申请人姓名">{{
              legal.applyName || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="申请人手机号">{{
              legal.applyMobile || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="申请时间">{{
              legal.createTime || '-'
            }}</el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </el-card>

    <!-- 证明材料卡片 -->
    <el-card shadow="hover" class="info-card">
      <div slot="header" class="card-header">
        <span><i class="el-icon-picture-outline"></i> 证明材料</span>
      </div>
      <div class="image-container">
        <div class="image-item">
          <el-image
            style="height: 300px; width: 220px; object-fit: cover"
            :src="legal.businessLicense"
            alt="营业执照"
            fit="cover"
            :preview-src-list="[legal.businessLicense]"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
          <div class="image-label">营业执照</div>
        </div>

        <div class="image-item">
          <el-image
            style="height: 300px; width: 220px; object-fit: cover"
            :src="legal.legalIdPhoto"
            alt="法人证件"
            fit="cover"
            :preview-src-list="[legal.legalIdPhoto]"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
          <div class="image-label">法人证件</div>
        </div>
      </div>
    </el-card>

    <!-- 审核操作按钮 -->
    <div class="action-buttons" v-if="legal.auditStatus !== 'PASS'">
      <el-button type="success" icon="el-icon-check" @click="approve"
        >审核通过</el-button
      >
      <el-button type="danger" icon="el-icon-close" @click="reject"
        >拒绝</el-button
      >
    </div>
  </div>
</template>

<script>
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      legal: {
        merchantId: '',
        merchantName: '',
        taxPayerNo: '',
        name: '',
        idType: '',
        idNo: '',
        legalIdPhoto: '',
        businessLicense: '',
        applyName: '',
        applyMobile: '',
        auditStatus: '',
        accreditStatus: '',
        contractAuthStatus: '',
        contractAuthFailReason: '',
        employeeCount: '',
        createTime: ''
      },
      requestData: {
        merchantId: this.$route.query.merchantId,
        taxPayerNo: this.$route.query.taxPayerNo
      },
      loading: false
    }
  },
  created() {
    this.reload()
  },
  computed:{
    accountManagerName(){
      return this.$route.query['accountManagerName']
    },
    accountManagerOrgName(){
      return this.$route.query['accountManagerOrgName']
    }
  },
  methods: {
    getIdTypeText(idTypeCode) {
      const idTypeMap = {
        PRC_ID: '中华人民共和国居民身份证',
        PASSPORT: '护照',
        COMPATRIOTS_CARD: '港澳台同胞回乡证',
        PERMANENT_RESIDENCE: '外国人永久居留证'
        // 添加更多的映射关系
      }
      return idTypeMap[idTypeCode] || idTypeCode // 如果传递的值找不到映射，返回原值
    },

    async reload() {
      this.loading = true
      const [err, r] = await client.detailLegal({
        body: this.requestData
      })
      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.legal = r.legal
    },

    goBack() {
      this.$router.push('/legals')
    },

    async approve() {
      if (!this.legal.merchantId || !this.legal.taxPayerNo) {
        this.$message.error('企业ID或统一社会信用代码不可为空')
        return
      }

      try {
        await this.$confirm('确定要审核通过该法人实体吗？', '审核确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })

        this.loading = true
        const [err, r] = await client.approveLegal({
          body: this.requestData
        })
        this.loading = false

        if (err) {
          handleError(err)
          return
        }

        this.$message.success('审核通过成功')
        this.reload()
      } catch (e) {
        // 用户取消操作
        return
      }
    },

    async reject() {
      if (!this.legal.merchantId || !this.legal.taxPayerNo) {
        this.$message.error('企业ID或统一社会信用代码不可为空')
        return
      }

      try {
        const { value } = await this.$prompt('请输入拒绝原因', '拒绝确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入拒绝原因',
          type: 'warning',
          inputValidator: value => {
            if (!value) {
              return '拒绝原因不能为空'
            }
            return true
          }
        })

        if (!value) return

        this.loading = true
        const [err, r] = await client.rejectLegal({
          body: {
            ...this.requestData,
            reason: value
          }
        })
        this.loading = false

        if (err) {
          handleError(err)
          return
        }

        this.$message.success('已拒绝该法人实体申请')
        this.reload()
      } catch (e) {
        // 用户取消操作
        return
      }
    }
  }
}
</script>

<style scoped>
.legal-detail-container {
  height: calc(100vh - 64px);
  overflow: hidden;
  overflow-y: auto;
}

.status-container {
  margin: 20px 0;
  display: flex;
  justify-content: flex-end;
}

.info-section {
  margin-bottom: 20px;
}

.info-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.card-header i {
  margin-right: 8px;
  font-size: 18px;
}

.image-container {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
  margin-top: 20px;
}

.image-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-label {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 30px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
  padding-bottom: 30px;
}

.el-descriptions {
  margin-bottom: 10px;
}

.el-descriptions-item__label {
  width: 140px;
  background-color: #f5f7fa !important;
}
</style>
