<template>
  <div class="legalStatistics">
    <el-form :inline="true" class="search">
      <el-form-item label="企业名称">
        <el-input v-model="conditions.merchantName"></el-input>
      </el-form-item>
      <el-form-item label="纳税人识别号">
        <el-input v-model="conditions.taxPayerNo"></el-input>
      </el-form-item>
      <el-form-item label="范围统计">
        <el-date-picker
          v-model="conditions.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <el-button
      style="margin-bottom: 20px"
      @click="exportToCSV"
      v-if="legalStatistics.length"
    >
      导出
    </el-button>

    <el-table
      :data="legalStatistics"
      border
      style="width: 100%"
      height="calc(100vh - 250px)"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column prop="merchantName" label="企业名称" width="180">
      </el-table-column>
      <el-table-column prop="taxSubName" label="法人实体名称" width="180">
      </el-table-column>
      <el-table-column prop="taxPayerNo" label="纳税人识别号">
      </el-table-column>
      <el-table-column
        prop="accountMangerName"
        label="管户客户经理"
        width="120"
      >
        <template slot-scope="scope">
          <span :title="scope.row.accountManagerCode">{{
            scope.row.accountManagerName
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="accountManagerOrgCode"
        label="管户机构名称"
        width="210"
      >
        <template slot-scope="scope">
          <span :title="scope.row.accountManagerOrgCode">
            {{
              accountManagerOrganizationCode2Name(
                scope.row.accountManagerOrgCode
              )
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="monthDate" label="统计年月"> </el-table-column>
      <el-table-column prop="reportFalg" label="本月是否申报">
        <template slot-scope="scope">
          <span>{{ scope.row.reportFalg === 'Y' ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="personReportCount" label="员工人数">
      </el-table-column>
      <el-table-column prop="feeCount" label="计费人数"> </el-table-column>
    </el-table>
  </div>
</template>

<script>
import formatDateTime from 'kit/formatters/dateTime'
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()
const now = new Date()
const prev = new Date()
prev.setMonth(prev.getMonth() - 1)

export default {
  data() {
    return {
      conditions: {
        merchantName: '',
        taxPayerNo: '',
        startDate: '',
        endDate: '',
        include:"accountManagerOrganization",
        dateRange: [prev, now]
      },
      legalStatistics: [],
      accountManagerOrganizations:[],
      selectedRows: []
    }
  },
  created() {
    this.reload()
  },
  methods: {
    accountManagerOrganizationCode2Name(code){
      const r = this.accountManagerOrganizations.find(c=>c.code === code)
      if(r){
        return r.name;
      }

      return "--"
    },
    async reload() {
      if (this.conditions.dateRange && this.conditions.dateRange.length === 2) {
        this.conditions.startDate = formatDateTime(
          'yyyy-MM-dd',
          this.conditions.dateRange[0]
        )
        this.conditions.endDate = formatDateTime(
          'yyyy-MM-dd',
          this.conditions.dateRange[1]
        )
      } else {
        this.conditions.startDate = ''
        this.conditions.endDate = ''
      }

      const [err, r] = await client.listLegalStatistics({
        body: this.conditions
      })
      if (err) {
        handleError(err)
        return
      }
      this.accountManagerOrganizations = r.accountManagerOrganizations
      this.legalStatistics = r.legalStatistics
    },
    onSearch() {
      this.reload()
    },
    onReset() {
      this.conditions = {
        merchantName: '',
        taxPayerNo: '',
        startDate: '',
        endDate: '',
        dateRange: [prev, now]
      }

      this.reload()
    },

    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    exportToCSV() {
      let data = this.selectedRows.length
        ? this.selectedRows
        : this.legalStatistics.slice(0, 10000)
      if (!data.length) {
        alert('没有数据可导出！')
        return
      }

      const header = [
        'ID',
        '企业名称',
        '法人实体名称',
        '管护客户经理',
        '管护机构名称',
        '纳税人识别号',
        '统计年月',
        '本月是否申报',
        '员工人数',
        '计费人数'
      ]

      const csvContent = data
        .map(row =>
          [
            row.id,
            `"${row.merchantName.replace(/"/g, '""')}"`,
            `"${row.taxSubName.replace(/"/g, '""')}"`,
            `"${row.accountManagerName.replace(/"/g, '""') || '--'}"`,
            `"${this.accountManagerOrganizationCode2Name(row.accountManagerOrgCode)}"`,
            `"${row.taxPayerNo.replace(/"/g, '""')}"`,
            row.monthDate,
            row.reportFalg ? '是' : '否',
            row.personReportCount,
            row.reportCount
          ].join(',')
        )
        .join('\n')

      const csvData = [header.join(',')].concat(csvContent).join('\n')

      const blob = new Blob(['\ufeff' + csvData], {
        type: 'text/csv;charset=utf-8;'
      })

      const url = URL.createObjectURL(blob)

      const downloadLink = document.createElement('a')
      downloadLink.href = url
      downloadLink.setAttribute('download', 'legal_statistics.csv')
      document.body.appendChild(downloadLink)
      downloadLink.click()
      document.body.removeChild(downloadLink)
    }
  }
}
</script>

<style></style>
