<template>
  <div class="legals">
    <el-form :inline="true" class="search">
      <!-- 修改每个输入框，使其v-model指向conditions中的相应属性 -->
      <el-form-item label="企业ID">
        <el-input v-model.number="conditions.compId"></el-input>
        <!-- 使用.number修饰符绑定为数字 -->
      </el-form-item>
      <el-form-item label="企业名称">
        <el-input v-model="conditions.compName"></el-input>
      </el-form-item>
      <el-form-item label="纳税人识别号">
        <el-input v-model="conditions.taxPayerNo"></el-input>
      </el-form-item>
      <el-form-item label="法人姓名">
        <el-input v-model="conditions.legalName"></el-input>
      </el-form-item>
      <el-form-item label="申请人姓名">
        <el-input v-model="conditions.applyName"></el-input>
      </el-form-item>
      <!-- <el-form-item label="合同主体认证状态">
        <el-select v-model="conditions.contractAuthStatus" placeholder="请选择">
          <el-option label="全部" value=""></el-option>
          <el-option label="认证通过" value="SUCCESS"></el-option>
          <el-option label="认证不通过" value="FAIL"></el-option>
          <el-option label="未认证" value="INIT"></el-option>
          <el-option label="待提交" value="STASH"></el-option>
          <el-option label="待审核" value="COMMIT"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="纳税主体验证状态">
        <el-select v-model="conditions.accreditStatus" placeholder="请选择">
          <el-option label="全部" value=""></el-option>
          <el-option label="授权通过" value="SUCCESS"></el-option>
          <el-option label="授权失败" value="FAIL"></el-option>
          <el-option label="未授权" value="WAIT_ACCREDIT"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <!-- 添加点击事件监听 -->
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- <el-button type="primary" style="margin-bottom: 20px">导出</el-button> -->
    <el-table size="small" height="calc(100vh - 250px)" :data="legals" border>
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column
        prop="merchantId"
        label="企业ID"
        width="60"
      ></el-table-column>
      <el-table-column
        prop="merchantName"
        label="企业名称"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="taxSubName"
        label="法人实体"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="taxPayerNo"
        label="纳税人识别号"
        width="160"
      ></el-table-column>
      <el-table-column
        prop="accountMangerName"
        label="管户客户经理"
        width="120"
      >
        <template slot-scope="scope">
          <span :title="scope.row.accountManagerCode">{{
            scope.row.accountManagerName
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="accountManagerOrgCode"
        label="管户机构名称"
        width="210"
      >
        <template slot-scope="scope">
          <span :title="scope.row.accountManagerOrgCode">
            {{
              accountManagerOrganizationCode2Name(
                scope.row.accountManagerOrgCode
              )
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="employeeCount" label="员工人数"></el-table-column>
      <el-table-column prop="name" label="法人姓名"></el-table-column>
      <el-table-column prop="idType" label="证件类型" width="120">
        <template slot-scope="scope">
          {{ idType2string(scope.row.idType) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="idNo"
        label="证件号码"
        width="180"
      ></el-table-column>
      <!-- 注意这里是accreditStatus，需要与后端返回的字段对应 -->
      <el-table-column
        prop="accreditStatus"
        label="纳税主体验证状态"
        width="180"
      >
        <template slot-scope="scope">
          {{ accreditStatus2String(scope.row.accreditStatus) }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="contractAuthStatus"
        label="合同主体认证状态"
        width="120"
      >
        <template slot-scope="scope">
          {{ contractAuthStatus2String(scope.row.contractAuthStatus) }}
        </template>
      </el-table-column> -->
      <el-table-column
        prop="applyName"
        label="申请人姓名"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="applyMobile"
        label="申请人手机号"
        width="120"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleApprove(scope.row.merchantId, scope.row.taxPayerNo)"
            v-if="scope.row.contractAuthStatus === 'COMMIT'"
          >
            审核通过
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleReject(scope.row.merchantId, scope.row.taxPayerNo)"
            v-if="scope.row.contractAuthStatus === 'COMMIT'"
          >
            驳回
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="
              $router.push(
                `/legals/${scope.row.id}?merchantId=${
                  scope.row.merchantId
                }&taxPayerNo=${scope.row.taxPayerNo}&accountManagerName=${
                  scope.row.accountManagerName
                }&accountManagerCode=${
                  scope.row.accountManagerCode
                }&accountManagerOrgName=${accountManagerOrganizationCode2Name(
                  scope.row.accountManagerOrgCode
                )}`
              )
            "
            v-if="
              (scope.row.accreditStatus === 'SUCCESS' && scope.row.applyName) ||
              scope.row.contractAuthStatus === 'COMMIT'
            "
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加分页组件事件监听 -->
    <div style="text-align: right; margin-top: 20px">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="10"
        layout="total, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentPageChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()

export default {
  computed: {
    currentPage() {
      return this.conditions.offset / this.conditions.limit + 1
    }
  },
  data() {
    return {
      conditions: {
        compId: '',
        compName: '',
        taxPayerNo: '',
        legalName: '',
        applyName: '',
        accreditStatus: '',
        contractAuthStatus: '',
        include: 'accountManagerOrganization',
        offset: 0,
        limit: 10
      },
      legals: [],
      accountManagerOrganizations: [],
      total: 0
    }
  },
  created() {
    this.reload()
  },
  methods: {
    accountManagerOrganizationCode2Name(code) {
      const r = this.accountManagerOrganizations.find(c => c.code === code)
      if (r) {
        return r.name
      }

      return '--'
    },
    async handleApprove(merchantId, taxPayerNo) {
      const isOk = await this.$confirm('确定要审核通过吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      if (!isOk) {
        return
      }

      const [err, r] = await client.approvalLegalContractAuthorization({
        body: {
          merchantId,
          taxPayerNo
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.reload()
    },
    async handleReject(merchantId, taxPayerNo) {
      //增加弹框收集拒绝理由
      const { value } = await this.$prompt('请输入驳回原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      if (!value) {
        return
      }

      const [err, r] = await client.rejectLegalContractAuthorization({
        body: {
          merchantId,
          taxPayerNo,
          reason: value
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.reload()
    },
    idType2string(idType) {
      if (idType === 'PRC_ID') {
        return '居民身份证'
      }
      return idType
    },
    contractAuthStatus2String(status) {
      const statusMap = {
        SUCCESS: '认证通过',
        FAIL: '认证不通过',
        INIT: '未认证',
        STASH: '待提交',
        COMMIT: '待审核'
      }
      return statusMap[status] || status // 如果没有找到对应的映射则返回原状态
    },
    accreditStatus2String(status) {
      const statusMap = {
        SUCCESS: '授权通过',
        FAIL: '授权失败',
        WAIT_ACCREDIT: '未授权'
      }
      return statusMap[status] || status // 如果没有找到对应的映射则返回原状态
    },
    async reload() {
      const [err, r] = await client.listLegals({
        body: this.conditions
      })
      if (err) {
        handleError(err)
        return
      }

      this.legals = r.legals
      this.accountManagerOrganizations = r.accountManagerOrganizations
      this.total = r.total
    },
    onSearch() {
      this.conditions.offset = 0
      this.reload()
    },
    onReset() {
      this.conditions = {
        compId: '',
        compName: '',
        taxPayerNo: '',
        legalName: '',
        applyName: '',
        accreditStatus: '',
        include: 'accountManagerOrganization',
        offset: 0,
        limit: 10
      }
      this.reload()
    },
    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0

      this.reload()
    },
    handleCurrentPageChange(currentPage) {
      const offset = (currentPage - 1) * this.conditions.limit
      this.conditions.offset = offset
      this.reload()
    }
  }
}
</script>

<style>
/* 可以添加CSS样式 */
</style>
