<template>
  <div style="height: var(100vh - 64px); overflow: hidden; overflow-y: auto">
    <div class="merchant-info-container">
      <div class="info-section" v-if="false">
        <div class="section-title">应用开通</div>
        <div class="application-section">
          <el-checkbox-group v-model="checkedBusinessIds">
            <el-checkbox
              v-for="business in businesses"
              :key="business.id"
              :label="business.id"
              :disabled="business.remark === '基础功能'"
              :class="{
                'base-function-checkbox': business.remark === '基础功能'
              }"
            >
              {{ business.remark }}
              <el-tooltip
                v-if="business.remark === '基础功能'"
                class="item"
                effect="dark"
                content="组织架构、权限、法人实体等"
                placement="top"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
            </el-checkbox>
          </el-checkbox-group>
          <el-checkbox-group v-model="checkedAdvancedBusinessIds">
            <el-checkbox
              v-for="business in advancedBusinesses"
              :key="business.id"
              :label="business.id"
            >
              {{ business.remark }}
            </el-checkbox>
          </el-checkbox-group>
          <br /><br />
          <el-button type="primary" @click="updateOpenedBusiness">
            开通
          </el-button>
        </div>
      </div>
      <el-form
        ref="merchantForm"
        :model="merchant"
        label-width="140px"
        class="merchant-form"
        :rules="rules"
      >
        <!-- 企业基本信息 -->
        <div class="info-section">
          <div class="section-title">企业基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="公司名称:">
                <span>{{ merchant.name || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司简称:" prop="shortName">
                <el-input
                  v-model="merchant.shortName"
                  placeholder="请输入公司简称"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="统一社会信用代码:">
                <span>{{ merchant.unifiedCode || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属行业:" prop="industry">
                <el-input
                  v-model="merchant.industry"
                  placeholder="请输入所属行业"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="公司注册地址:">
                <span>{{ merchant.registeredAddress || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="管理员:">
                <span>{{ merchant.adminName || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号码:">
                <span>{{ merchant.adminPhone || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="管户客户经理:">
                <span>{{ accountMangerName || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管户人工号:">
                <span>{{ accountMangerCode || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="管户人机构名称:">
                <span>{{ accountMangerOrganizationName || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管户人机构编号:">
                <span>{{ accountMangerOrganizationCode || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="info-section">
          <div class="section-title">证照信息</div>
          <el-form label-width="140px" class="merchant-form">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="营业执照:">
                  <div class="image-container">
                    <img
                      v-if="merchant.businessLicenseImg"
                      :src="merchant.businessLicenseImg"
                      class="license-image"
                      @click="handlePreview(merchant.businessLicenseImg)"
                    />
                    <div v-else class="no-image">暂无图片</div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="法人身份证正面图:">
                  <div class="image-container">
                    <img
                      v-if="merchant.legalIdCardFront"
                      :src="merchant.legalIdCardFront"
                      class="license-image"
                      @click="handlePreview(merchant.legalIdCardFront)"
                    />
                    <div v-else class="no-image">暂无图片</div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="法人身份证反面图:">
                  <div class="image-container">
                    <img
                      v-if="merchant.legalIdCardOpposite"
                      :src="merchant.legalIdCardOpposite"
                      class="license-image"
                      @click="handlePreview(merchant.legalIdCardOpposite)"
                    />
                    <div v-else class="no-image">暂无图片</div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 法人及联系人信息 -->
        <div class="info-section">
          <div class="section-title">法人及联系人信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="法人姓名:">
                <span>{{ merchant.legalName || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证件类型:">
                <span>{{
                  getLegalIdTypeText(merchant.legalIdType) || '-'
                }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="证件号码:">
                <span>{{ merchant.legalIdNo || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系人姓名:" prop="contactName">
                <el-input
                  :maxlength="20"
                  v-model="merchant.contactName"
                  placeholder="请输入联系人姓名"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人手机号:" prop="contactPhone">
                <el-input
                  v-model="merchant.contactPhone"
                  placeholder="请输入联系人手机号"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-actions">
          <el-button type="primary" @click="changeMerchantBaseInfo">
            更新信息
          </el-button>
        </div>
      </el-form>
    </div>
    <el-dialog :visible.sync="previewVisible" append-to-body>
      <img width="100%" :src="previewImageUrl" alt="预览图片" />
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
import { BusinessAdvanced, BusinessNormal } from 'kit/services/boss/constants'
const client = makeClient()

export default {
  computed: {
    accountMangerCode() {
      return this.$route.query['accountMangerCode']
    },
    accountMangerName() {
      return this.$route.query['accountMangerName']
    },
    accountMangerOrganizationCode() {
      return this.$route.query['accountMangerOrganizationCode']
    },
    accountMangerOrganizationName() {
      return this.$route.query['accountMangerOrganizationName']
    }
  },
  data() {
    return {
      activeTab: 'basic',
      checkedBusinessIds: [],
      checkedAdvancedBusinessIds: [],
      reload: 0,
      baseFunctionEnabled: true,
      businesses: [],
      advancedBusinesses: [],
      merchant: {
        id: '',
        binInsi: '',
        blngInsi: '',
        name: '',
        shortName: '',
        unifiedCode: '',
        registeredAddress: '',
        industry: '',
        managerName: '',
        managerJobNumber: '',
        managerOrgName: '',
        managerOrgCode: '',
        legalName: '',
        legalIdType: '',
        legalIdNo: '',
        contactName: '',
        contactPhone: '',
        createTime: '',
        openedBusiness: [],
        openedAdvancedBusiness: [],
        businessLicenseImg: '',
        legalIdCardFront: '',
        legalIdCardOpposite: ''
      },
      // 图片预览相关
      previewVisible: false,
      previewImageUrl: '',
      // 表单验证规则
      rules: {
        shortName: [
          { max: 30, message: '公司简称不能超过30个字符', trigger: 'blur' }
        ],
        industry: [
          { max: 50, message: '所属行业不能超过50个字符', trigger: 'blur' }
        ],
        contactName: [
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/,
            message: '请输入正确的联系人姓名',
            trigger: 'blur'
          }
        ],
        contactPhone: [
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ]
      },
      // 证件类型映射
      idTypeMap: {
        ID_CARD: '身份证',
        PASSPORT: '护照',
        PERMANENT_RESIDENCE: '永久居留证',
        TAIWAN_PASS: '台湾通行证',
        HONGKONG_MACAO_PASS: '港澳通行证',
        OTHER: '其他',
        PRC_ID: '身份证'
      }
    }
  },
  created() {
    this.listBusinesses()
    this.detailMerchant()
  },
  methods: {
    // 获取证件类型文本
    getLegalIdTypeText(idType) {
      return this.idTypeMap[idType] || idType
    },

    // 处理图片预览
    handlePreview(url) {
      this.previewImageUrl = url
      this.previewVisible = true
    },
    async listBusinesses() {
      const [err, r] = await client.listBusinesses()
      if (err) {
        handleError(err)
        return
      }

      if (r && r.businesses) {
        this.businesses = r.businesses.filter(c => c.type === BusinessNormal)

        this.advancedBusinesses = r.businesses.filter(
          c => c.type === BusinessAdvanced
        )

        this.formatAdvancedBusinesses()
      }
    },
    formatAdvancedBusinesses() {
      for (var c of this.advancedBusinesses) {
        if (c.remark === '电子签约') {
          c.remark = '高级电子签约'
        }
        if (c.remark === '在线报税') {
          c.remark = '高级在线报税'
        }
      }
    },
    async detailMerchant() {
      const merchantId = this.$route.params.id
      const body = {
        merchantId: merchantId
      }

      const [err, r] = await client.detailMerchant({
        body
      })

      if (err) {
        handleError(err)
        return
      }

      if (r && r.merchant) {
        this.merchant = r.merchant
        this.checkedBusinessIds = r.merchant.openedBusiness || []
        this.checkedAdvancedBusinessIds =
          r.merchant.openedAdvancedBusiness || []
      }
    },
    async updateOpenedBusiness() {
      const merchantId = this.$route.params.id

      const body = {
        merchantId: merchantId,
        businessIds: this.checkedBusinessIds,
        advancedBusinessIds: this.checkedAdvancedBusinessIds
      }

      const [err] = await client.updateOpenedBusiness({
        body
      })

      if (err) {
        handleError(err)
        return
      }

      this.$message({
        message: '企业应用状态成功更新!',
        type: 'success'
      })
    },

    async changeMerchantBaseInfo() {
      // 表单验证
      this.$refs.merchantForm.validate(async valid => {
        if (!valid) {
          this.$message({
            message: '请正确填写表单信息',
            type: 'warning'
          })
          return
        }

        const merchantId = this.$route.params.id

        // 构建更新请求体，只包含可编辑的字段
        const body = {
          merchantId: merchantId,
          shortName: this.merchant.shortName,
          industry: this.merchant.industry,
          contactName: this.merchant.contactName,
          contactPhone: this.merchant.contactPhone
        }

        // 调用更新企业信息的API
        const [err] = await client.changeMerchantBaseInfo({
          body
        })

        if (err) {
          handleError(err)
          return
        }

        this.$message({
          message: '企业基本信息更新成功!',
          type: 'success'
        })
      })
    }
  }
}
</script>

<style scoped>
.base-function-checkbox {
  color: #c0c4cc; /* This sets the checkbox label to grey */
}
.el-icon-question {
  color: #c0c4cc; /* Optional: Sets the color of the question mark icon */
  cursor: pointer; /* Changes the cursor to a pointer when hovering over the question mark */
}

/* 企业信息样式 */
.merchant-info-container {
  padding: 20px;
}

.info-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.merchant-form .el-form-item {
  margin-bottom: 18px;
}

.form-actions {
  margin-top: 20px;
  text-align: center;
}

.application-section {
  margin-top: 20px;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
}

.placeholder {
  padding: 30px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

/* 证照信息样式 */
.image-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.license-image {
  width: 150px;
  height: 100px;
  object-fit: cover;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.license-image:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.no-image {
  width: 150px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  color: #909399;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
}
</style>
