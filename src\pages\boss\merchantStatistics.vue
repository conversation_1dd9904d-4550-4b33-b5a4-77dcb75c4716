<template>
  <div class="merchantStatistics">
    <el-form :inline="true" class="search">
      <el-form-item label="企业名称">
        <el-input v-model="conditions.merchantName"></el-input>
      </el-form-item>
      <el-form-item label="范围统计">
        <el-date-picker
          v-model="conditions.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <el-button
      style="margin-bottom: 20px"
      @click="exportToCSV"
      v-if="merchantStatistics.length"
    >
      导出
    </el-button>
    <el-table
      :data="merchantStatistics"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column prop="merchantName" label="企业名称" width="180">
      </el-table-column>
      <el-table-column prop="monthDate" label="统计年月"> </el-table-column>
      <el-table-column prop="interfaceService" label="接口模式">
        <template slot-scope="scope">
          {{ interfaceServiceToString(scope.row.interfaceService) }}
        </template>
      </el-table-column>
      <el-table-column prop="personReportCount" label="员工人数">
      </el-table-column>
      <el-table-column prop="reportCount" label="计费人数"> </el-table-column>
    </el-table>
  </div>
</template>

<script>
import formatDateTime from 'kit/formatters/dateTime'
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
import interfaceServiceToString from '../../services/boss/interfaceServiceToString'
const client = makeClient()
const now = new Date()
const prev = new Date()
prev.setMonth(prev.getMonth() - 1)

export default {
  data() {
    return {
      conditions: {
        merchantName: '',
        startDate: '',
        endDate: '',
        dateRange: [prev, now]
      },
      merchantStatistics: [],
      selectedRows: []
    }
  },
  created() {
    this.reload()
  },
  methods: {
    async reload() {
      if (this.conditions.dateRange && this.conditions.dateRange.length === 2) {
        this.conditions.startDate = formatDateTime(
          'yyyy-MM-dd',
          this.conditions.dateRange[0]
        )
        this.conditions.endDate = formatDateTime(
          'yyyy-MM-dd',
          this.conditions.dateRange[1]
        )
      } else {
        this.conditions.startDate = ''
        this.conditions.endDate = ''
      }
      const [err, r] = await client.listMerchantStatistics({
        body: this.conditions
      })
      if (err) {
        handleError(err)
        return
      }

      this.merchantStatistics = r.merchantStatistics
    },
    onSearch() {
      this.reload()
    },
    onReset() {
      this.conditions = {
        merchantName: '',
        startDate: '',
        endDate: '',
        dateRange: [prev, now]
      }
      this.reload()
    },
    interfaceServiceToString,

    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    exportToCSV() {
      let data = this.selectedRows.length
        ? this.selectedRows
        : this.merchantStatistics.slice(0, 10000)
      if (!data.length) {
        alert('没有数据可导出！')
        return
      }

      const header = [
        'ID',
        '企业名称',
        '统计年月',
        '接口模式',
        '员工人数',
        '计费人数'
      ]

      const csvContent = data
        .map(row =>
          [
            row.id,
            `"${row.merchantName.replace(/"/g, '""')}"`,
            row.monthDate,
            interfaceServiceToString(row.interfaceService).replace(/"/g, '""'),
            row.personReportCount,
            row.reportCount
          ].join(',')
        )
        .join('\n')

      const csvData = [header.join(',')].concat(csvContent).join('\n')

      const blob = new Blob(['\ufeff' + csvData], {
        type: 'text/csv;charset=utf-8;'
      })

      const url = URL.createObjectURL(blob)

      const downloadLink = document.createElement('a')
      downloadLink.href = url
      downloadLink.setAttribute('download', 'merchant_statistics.csv')
      document.body.appendChild(downloadLink)
      downloadLink.click()
      document.body.removeChild(downloadLink)
    }
  }
}
</script>

<style></style>
