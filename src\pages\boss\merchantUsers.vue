<template>
  <div class="merchant-users">
    <!-- 搜索表单 -->
    <el-form
      :inline="true"
      :model="searchForm"
      @submit.native.prevent="onSearch"
    >
      <el-form-item label="用户ID">
        <el-input
          v-model="searchForm.userId"
          placeholder="请输入用户ID"
        ></el-input>
      </el-form-item>
      <el-form-item label="真实姓名">
        <el-input
          v-model="searchForm.realName"
          placeholder="请输入真实姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="所属企业">
        <el-input
          v-model="searchForm.merchantName"
          placeholder="请输入所属企业"
        ></el-input>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 企业列表表格 -->
    <el-table
      v-loading="loading"
      :data="userList"
      border
      style="width: 100%"
      size="small"
      height="calc(100vh - 250px)"
    >
      <el-table-column prop="userId" label="用户ID"></el-table-column>
      <el-table-column prop="realName" label="真实姓名"></el-table-column>
      <el-table-column prop="phone" label="手机号"></el-table-column>
      <el-table-column prop="merchantName" label="所属企业"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag :type="getUserStatusType(scope.row.status)">
            {{ getUserStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="300">
        <template slot-scope="scope">
          {{ scope.row?.createTime.replace('T', ' ').split('.')[0] }}
        </template></el-table-column
      >
      <el-table-column fixed="right" label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="markAsVerified(scope.row.userId)"
            v-if="scope.row.status === '0'"
          >
            标记为已认证
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="markAsUnVerified(scope.row.userId)"
            v-else
          >
            标记为未认证
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'

const client = makeClient()

export default {
  name: 'MerchantUsers',
  data() {
    return {
      loading: false,
      userList: [],
      total: 0,
      searchForm: {
        userId: '',
        realName: '',
        phone: '',
        merchantName: ''
      },
      pagination: {
        page: 1,
        pageSize: 10
      },
      // statusMap: {
      //   ACTIVE: '正常',
      //   LOCKED: '锁定',
      //   DISABLED: '禁用',
      //   PENDING: '待激活'
      // },
      statusMap: {
        1: '已认证',
        0: '未认证'
      }
    }
  },
  created() {
    this.fetchUserList()
  },
  methods: {
    // 获取企业状态文本
    getUserStatusText(status) {
      return this.statusMap[status] || status
    },
    // 获取企业状态对应的标签类型
    getUserStatusType(status) {
      if (status === '1') {
        return 'success'
      }
      if (status === '0') {
        return 'danger'
      }
    },
    // 查询企业列表
    async fetchUserList() {
      this.loading = true

      const params = {
        ...this.searchForm,
        page: this.pagination.page,
        pageSize: this.pagination.pageSize
      }

      const [err, res] = await client.listMerchantUsers({
        body: params
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      if (res && res.users) {
        this.userList = res.users
        this.total = res.total || 0
      }
    },
    // 搜索
    onSearch() {
      this.pagination.page = 1
      this.fetchUserList()
    },
    // 重置搜索条件
    onReset() {
      this.searchForm = {
        userId: '',
        realName: '',
        phone: '',
        merchantName: ''
      }
      this.pagination.page = 1
      this.fetchUserList()
    },
    // 改变每页显示数量
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.fetchUserList()
    },
    // 改变页码
    handleCurrentChange(page) {
      this.pagination.page = page
      this.fetchUserList()
    },
    async markAsVerified(userId) {
      try {
        await this.$confirm('确定要将该人标记为已实名认证吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.loading = true
        const [err] = await client.markMerchantUserAsVerified({
          body: { userId }
        })

        if (err) {
          handleError(err)
          this.loading = false
          return
        }

        this.$message({
          type: 'success',
          message: '用户已标记为已认证'
        })

        // 重新加载企业列表
        this.fetchUserList()
      } catch (e) {
        // 企业取消操作，不做处理
      }
    },

    async markAsUnVerified(userId) {
      try {
        await this.$confirm('确定要将该人标记为未实名认证吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.loading = true
        const [err] = await client.markMerchantUserAsUnVerified({
          body: { userId }
        })

        if (err) {
          handleError(err)
          this.loading = false
          return
        }

        this.$message({
          type: 'success',
          message: '用户已标记为未认证'
        })

        // 重新加载企业列表
        this.fetchUserList()
      } catch (e) {
        // 企业取消操作，不做处理
      }
    }
  }
}
</script>

<style scoped>
.merchant-users {
}

.search-form {
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
