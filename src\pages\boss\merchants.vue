<template>
  <div class="merchants">
    <el-form :inline="true" class="search">
      <el-form-item label="企业ID">
        <el-input v-model="conditions.id"></el-input>
      </el-form-item>
      <el-form-item label="企业名称">
        <el-input v-model="conditions.name"></el-input>
      </el-form-item>
      <el-form-item label="认证状态">
        <el-select v-model="conditions.auditStatus">
          <el-option value="" label="全部">全部</el-option>
          <el-option value="NOT_IDENTIFIED" label="未认证">未认证</el-option>
          <el-option value="WAIT" label="待审核">待审核</el-option>
          <el-option value="PASS" label="审核通过">审核通过</el-option>
          <el-option value="NOT_PASS" label="审核驳回">审核驳回</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          v-model="conditions.dateRange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-button style="margin-bottom: 10px" @click="exportMerchants">
      导出
    </el-button>
    <el-table
      size="small"
      height="calc(100vh - 300px)"
      :data="merchants"
      border
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column prop="id" label="ID"> </el-table-column>
      <el-table-column prop="name" label="企业名称" width="180">
      </el-table-column>
      <el-table-column
        prop="accountMangerName"
        label="管户客户经理"
        width="120"
      >
        <template slot-scope="scope">
          <span :title="scope.row.accountMangerCode">{{
            scope.row.accountMangerName
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="accountMangerOrganizationCode"
        label="管户机构名称"
        width="210"
      >
        <template slot-scope="scope">
          <span :title="scope.row.accountMangerOrganizationCode">
            {{
              accountManagerOrganizationCode2Name(
                scope.row.accountMangerOrganizationCode
              )
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="auditStatus" label="认证状态">
        <template slot-scope="scope">
          {{ auditStatus2String(scope.row.auditStatus) }}
          <div style="color: #ccc" v-if="scope.row.auditStatus === 'NOT_PASS'">
            {{ scope.row.auditDesc }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="渠道来源" width="100">
        <template slot-scope="scope"> 鲤想薪管家 </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160">
        <template slot-scope="scope">
          {{ scope.row.createTime.replace('T', ' ') }}
        </template>
      </el-table-column>
      <el-table-column prop="modifyTime" label="修改时间" width="160">
        <template slot-scope="scope">
          {{ scope.row?.modifyTime?.replace('T', ' ') }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="220">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleApprove(scope.row.id)"
            v-if="scope.row.auditStatus === 'WAIT'"
          >
            审核通过
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleReject(scope.row.id)"
            v-if="scope.row.auditStatus === 'WAIT'"
          >
            驳回
          </el-button>

          <el-button type="text" size="small" @click="goDetail(scope.row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="text-align: right; margin-top: 20px">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="10"
        layout="total, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentPageChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
import { exportFileByFront } from './utils/utils.js'
const client = makeClient()
var accountManagerOrganizationsNameMap = {}
const parseAccountManagerOrganizationsNameMap = orgs => {
  if (!orgs || !orgs.length) {
    return {}
  }
  for (var c of orgs) {
    accountManagerOrganizationsNameMap[c.code] = c.name
  }
}
export default {
  computed: {
    currentPage() {
      return this.conditions.offset / this.conditions.limit + 1
    }
  },
  data() {
    return {
      conditions: {
        id: '',
        name: '',
        auditStatus: '',
        include: 'accountManagerOrganization',
        dateRange: '',
        offset: 0,
        limit: 10
      },
      merchants: [],
      total: 0
    }
  },
  created() {
    this.reload()
  },
  methods: {
    accountManagerOrganizationCode2Name(code) {
      return accountManagerOrganizationsNameMap[code] || code
    },
    async exportMerchants() {
      const [err, r] = await client.listMerchants({
        body: {
          limit: this.total,
          include: 'accountManagerOrganization'
        }
      })
      if (err) {
        handleError(err)
        return
      }
      parseAccountManagerOrganizationsNameMap(r.accountManagerOrganizations)
      this.exportToCSV(r.merchants)
    },
    exportToCSV(data) {
      if (!data.length) {
        alert('没有数据可导出！')
        return
      }

      const header = [
        'ID',
        '名称',
        '管户客户经理',
        '管户机构名称',
        '认证状态',
        '渠道来源',
        '创建时间',
        '修改时间'
      ]

      const csvContent = data
        .map(row =>
          [
            row.id,
            `"${row.name.replace(/"/g, '""')}"`,
            `"${row.accountMangerName.replace(/"/g, '""')}"`,
            `"${this.accountManagerOrganizationCode2Name(
              row.accountMangerOrganizationCode
            ).replace(/"/g, '""')}"`,
            this.auditStatus2String(row.auditStatus),
            '鲤想薪管家',
            row.createTime.replace('T', ' '),
            row.modifyTime.replace('T', ' ')
          ].join(',')
        )
        .join('\n')

      const csvData = [header.join(',')].concat(csvContent).join('\n')
      exportFileByFront(data, csvData, '企业列表.csv')
      return
    
    },
    async handleApprove(merchantId) {
      try {
        await this.$confirm('确定要审核通过该企业吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const [err] = await client.approvalMerchant({
          body: {
            merchantId
          }
        })

        if (err) {
          handleError(err)
          return
        }

        this.$message({
          message: '审核通过成功',
          type: 'success'
        })

        this.reload()
      } catch (e) {
        // 用户取消操作，不做处理
      }
    },
    goDetail(row) {
      const params = new URLSearchParams()
      params.set('accountMangerCode', row.accountMangerCode)
      params.set('accountMangerName', row.accountMangerName)
      params.set(
        'accountMangerOrganizationCode',
        row.accountMangerOrganizationCode
      )
      params.set(
        'accountMangerOrganizationName',
        this.accountManagerOrganizationCode2Name(
          row.accountMangerOrganizationCode
        )
      )
      this.$router.push(`/merchants/${row.id}?${params.toString()}`)
    },
    async handleReject(merchantId) {
      try {
        const { value } = await this.$prompt('请输入驳回原因', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const [err] = await client.rejectMerchant({
          body: {
            merchantId,
            reason: value
          }
        })

        if (err) {
          handleError(err)
          return
        }

        this.$message({
          message: '驳回成功',
          type: 'success'
        })

        this.reload()
      } catch (e) {
        // 用户取消操作，不做处理
      }
    },

    async reload() {
      const [startDate, endDate] = this.conditions.dateRange
      const [err, r] = await client.listMerchants({
        body: { ...this.conditions, startDate, endDate }
      })
      if (err) {
        handleError(err)
        return
      }

      this.merchants = r.merchants
      this.total = r.total

      parseAccountManagerOrganizationsNameMap(r.accountManagerOrganizations)
    },
    onSearch() {
      this.conditions.offset = 0
      this.reload()
    },
    onReset() {
      this.conditions = {
        merchantId: '',
        merchantName: '',
        include: 'accountManagerOrganization',
        dateRange: '',
        startTime: '',
        endTime: '',
        offset: 0,
        limit: 10
      }
      this.reload()
    },
    auditStatus2String(status) {
      const statusMap = {
        WAIT: '待审核',
        PASS: '审核通过',
        NOT_PASS: '审核驳回',
        NOT_IDENTIFIED: '未认证'
      }
      return statusMap[status] || status // 如果没有找到对应的映射则返回原状态
    },
    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0

      this.reload()
    },
    handleCurrentPageChange(currentPage) {
      const offset = (currentPage - 1) * this.conditions.limit
      this.conditions.offset = offset
      this.reload()
    }
  }
}
</script>

<style></style>
