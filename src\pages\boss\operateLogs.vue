<template>
  <div class="operateLogs">
    <el-form ref="conditions" :model="conditions" :inline="true" class="search">
      <!-- 修改每个输入框，使其v-model指向conditions中的相应属性 -->
      <el-form-item label="操作时间">
        <el-date-picker
          v-model="conditions.operateTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label-width="100px" label="涉及企业">
        <el-input v-model="conditions.merchantName"></el-input>
      </el-form-item>
      <el-form-item label-width="100px" label="操作人">
        <el-input v-model="conditions.userName"></el-input>
      </el-form-item>
      <el-form-item label-width="100px" label="操作名称">
        <el-input v-model="conditions.name"></el-input>
      </el-form-item>
      <el-form-item label="操作结果">
        <el-select v-model="conditions.result">
          <el-option label="全部" value=""></el-option>
          <el-option label="操作成功" value="succeed"></el-option>
          <el-option label="操作失败" value="failed"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item style="margin-left: 20px">
        <!-- 添加点击事件监听 -->
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- <el-button type="primary" style="margin-bottom: 20px">导出</el-button> -->
    <el-table class="tabelBox" size="small" :data="data" border>
      <!-- <el-table-column type="selection" width="55"></el-table-column> -->
      <el-table-column prop="merchantName" label="涉及企业" width="220"></el-table-column>
      <el-table-column prop="userName" label="操作人" width="100"></el-table-column>
      <el-table-column prop="ip" label="操作人IP" width="100"></el-table-column>
      <el-table-column
        :show-overflow-tooltip="true"
        prop="name"
        label="操作名称"
      ></el-table-column>
      <el-table-column
        :show-overflow-tooltip="true"
        prop="uri"
        label="请求路径"
      ></el-table-column>
      <el-table-column prop="result" label="操作结果" width="80">
        <template slot-scope="scope">
          {{ getResultMap(scope.row.result) }}
        </template>
      </el-table-column>
      <el-table-column prop="createdTime" label="操作时间" width="150">
        <template slot-scope="scope">
          {{ formatterOperateTime(scope.row.createdTime) }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加分页组件事件监听 -->
    <div style="text-align: right; margin-top: 20px">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="conditions.limit"
        layout="total, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentPageChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()

export default {
  computed: {
    currentPage() {
      return this.conditions.offset / this.conditions.limit + 1
    }
  },
  data() {
    return {
      conditions: {
        operateTime: [],
        userAgent: '',
        merchantName: '',
        name: '',
        result: '',
        startDate: '',
        endDate: '',
        limit: 10,
        offset: 0
      },
      data: [],
      total: 0
    }
  },
  created() {
    this.reload()
  },
  methods: {
    // 格式化操作时间
    formatterOperateTime(val) {
      const date = this.format('yyyy-MM-dd hh:mm:ss', new Date(val))
      return date
    },
    // 格式化日期
    format(fmt, date) {
      var o = {
        'M+': date.getMonth() + 1, //月份
        'd+': date.getDate(), //日
        'h+': date.getHours(), //小时
        'm+': date.getMinutes(), //分
        's+': date.getSeconds(), //秒
        'q+': Math.floor((date.getMonth() + 3) / 3), //季度
        S: date.getMilliseconds() //毫秒
      }
      if (/(y+)/.test(fmt))
        fmt = fmt.replace(
          RegExp.$1,
          (date.getFullYear() + '').substr(4 - RegExp.$1.length)
        )
      for (var k in o)
        if (new RegExp('(' + k + ')').test(fmt))
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length == 1
              ? o[k]
              : ('00' + o[k]).substr(('' + o[k]).length)
          )
      return fmt
    },
    // 结果映射
    getResultMap(result) {
      switch (result) {
        case 'succeed':
          return '操作成功'
        case 'failed':
          return '操作失败'
      }
    },
    onSearch() {
      this.conditions.offset = 0;
      this.reload()
    },
    onReset() {
      this.conditions = {
        operateTime: [],
        userAgent: '',
        name: '',
        merchantName: '',
        result: '',
        startDate: '',
        endDate: '',
        limit: 10,
        offset: 0
      }

      this.reload()
    },
    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0

      this.reload()
    },
    handleCurrentPageChange(currentPage) {
      const offset = (currentPage - 1) * this.conditions.limit
      this.conditions.offset = offset
      this.reload()
    },
    async reload() {
      if (
        this.conditions?.operateTime.length > 0 &&
        this.conditions.operateTime[0]
      ) {
        const startDate = this.format(
          'yyyy-MM-dd hh:mm:ss',
          this.conditions?.operateTime[0]
        )
        const endDate = this.format(
          'yyyy-MM-dd hh:mm:ss',
          this.conditions?.operateTime[1]
        )

        Object.assign(this.conditions, { startDate, endDate })
      }
      const [err, r] = await client.listOperateLogs({
        body: this.conditions
      })
      if (err) {
        handleError(err)
        return
      }
      if (!r.operateLogs) {
        return
      }
      this.data = r.operateLogs
      this.total = r.total
    }
  }
}
</script>

<style scoped>
.operateLogs {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.tabelBox {
  overflow: auto;
}
</style>
