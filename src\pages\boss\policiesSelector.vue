<template>
  <div
    class="policiesSelector"
    style="height: calc(100vh - 350px); overflow-y: auto"
  >
    <el-tree
      check-on-click-node
      default-expand-all
      ref="policyTree"
      :data="policyGroups"
      show-checkbox
      node-key="code"
      :props="{
        label: 'name',
        children: 'children'
      }"
      :default-checked-keys="processedSelected"
      @check="handleCheckChange"
    />
  </div>
</template>

<script>
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()

export default {
  props: {
    value: Array
  },
  data() {
    return {
      policyGroups: []
    }
  },
  async created() {
    const [err, r] = await client.listPolicyGroups({
      body: {}
    })
    if (err) {
      handleError(err)
      return
    }
    console.log('policyGroups', r)
    this.policyGroups = r.groups.map(group => ({
      name: group.name,
      code: group.uri,
      children: group.policies.map(policy => ({
        name: policy.name,
        code: policy.uri
      }))
    }))
  },
  computed: {
    processedSelected() {
      const policySet = new Set(this.selectedPolicies)
      const selectedKeys = [...this.value]

      // 遍历所有组，检查是否需要自动选中父节点
      this.policyGroups.forEach(group => {
        const allChildrenSelected = group.children.every(p =>
          policySet.has(p.code)
        )
        if (allChildrenSelected) {
          selectedKeys.push(group.code)
        }
      })

      return selectedKeys
    }
  },
  methods: {
    reset() {
      this.$refs.policyTree.setCheckedKeys([])
    },
    handleCheckChange() {
      const checkedKeys = this.$refs.policyTree.getCheckedKeys()
      console.log('purePolicies', checkedKeys)
      this.$emit('input', checkedKeys)
    }
  }
}
</script>

<style></style>
