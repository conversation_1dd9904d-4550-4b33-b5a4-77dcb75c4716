<template>
  <div class="roles-selector">
    <el-checkbox-group
      v-model="internalSelectedRoleIds"
      @change="handleSelectionChange"
    >
      <el-row :gutter="10">
        <el-col :span="10" v-for="role in roles" :key="role.id">
          <el-checkbox :label="role.id">{{ role.name }}</el-checkbox>
        </el-col>
      </el-row>
    </el-checkbox-group>
    <div v-if="loading" class="roles-selector-loading">加载中...</div>
    <div v-if="!loading && !roles.length" class="roles-selector-empty">
      暂无可用角色
    </div>
  </div>
</template>

<script>
import handleError from 'kit/helpers/handleError'
import makeClient from '../../services/boss/makeClient' // Adjust path if necessary
const client = makeClient()

export default {
  name: 'RolesSelector',
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      roles: [],
      internalSelectedRoleIds: [],
      loading: false
    }
  },
  watch: {
    value: {
      handler(newValue) {
        this.internalSelectedRoleIds = Array.isArray(newValue)
          ? [...newValue]
          : []
      },
      immediate: true
    }
  },
  async created() {
    await this.fetchRoles()
    this.internalSelectedRoleIds = Array.isArray(this.value)
      ? [...this.value]
      : []
  },
  methods: {
    async fetchRoles() {
      this.loading = true
      const [err, r] = await client.listRoles({
        body: {}
      })
      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.roles = r.roles
    },
    handleSelectionChange(newSelectedIds) {
      this.$emit('validator', newSelectedIds)
      this.$emit('input', newSelectedIds)
    },
    reset() {
      this.internalSelectedRoleIds = []
      this.$emit('input', [])
    }
  }
}
</script>

<style scoped>
.roles-selector-loading,
.roles-selector-empty {
  padding: 10px;
  color: #909399;
  text-align: center;
}
.el-checkbox {
  margin-right: 10px;
  margin-bottom: 5px;
  white-space: nowrap;
}
.el-col {
  margin-bottom: 5px;
}
</style>
