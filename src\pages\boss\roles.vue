<template>
  <div class="roles">
    <div style="text-align: right">
      <el-button type="primary" @click="$router.push('/roles/new')">
        新建
      </el-button>
    </div>
    <el-form :inline="true" class="search">
      <el-form-item label="角色名称">
        <el-input v-model="conditions.name"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      size="small"
      height="calc(100vh - 160px)"
      :data="roles"
      border
    >
      <el-table-column prop="name" label="名称"></el-table-column>
      <el-table-column fixed="right" label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="$router.push(`/roles/${scope.row.id}/edit`)"
            v-if="scope.row.name !== '超级管理员'"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="$router.push(`/roles/${scope.row.id}/edit?include=users`)"
          >
            查看用户
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleDelete(scope.row.id)"
            v-if="scope.row.name !== '超级管理员'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      conditions: {},
      roles: [],
      loading: true
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    onSearch() {
      if (!this.conditions.name.trim()) {
        return
      }
      this.roles = this.roles.filter(role =>
        role.name.includes(this.conditions.name)
      )
    },
    onReset() {
      this.conditions = {}
      this.getList()
    },
    async getList() {
      this.loading = true
      const [err, r] = await client.listRoles({
        body: {}
      })
      this.loading = false
      if (err) {
        handleError(err)
        return
      }

      this.roles = r.roles
    },
    async handleDelete(roleId) {
      const isOk = await this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      if (!isOk) {
        return
      }

      const [err, r] = await client.deleteRole({
        body: {
          roleId
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.getList()
    }
  }
}
</script>

<style></style>
