<template>
  <el-form
    :model="roleForm"
    :rules="rules"
    ref="roleForm"
    label-width="100px"
    class="demo-roleForm"
  >
    <el-form-item label="角色名称" prop="name">
      <el-input
        v-model="roleForm.name"
        placeholder="请输入角色名称"
        maxlength="10"
        show-word-limit
        :disabled="showUsers"
      ></el-input>
    </el-form-item>

    <el-form-item label="角色描述" prop="description">
      <el-input
        type="textarea"
        v-model="roleForm.description"
        placeholder="请输入角色描述"
        maxlength="200"
        show-word-limit
        :autosize="{ minRows: 2, maxRows: 4 }"
        :disabled="showUsers"
      ></el-input>
    </el-form-item>
    <el-form-item label="已授权用户" v-if="showUsers">
      <el-row gutter="20">
        <el-col :span="10" v-for="user in users" :key="user.id">
          {{ user.fullName }}({{ user.name }})
        </el-col>
      </el-row>
    </el-form-item>
    <el-form-item label="设置权限" prop="policies" v-if="!showUsers">
      <PoliciesSelector v-model="roleForm.policies" ref="policiesSelector" />
    </el-form-item>
    <el-form-item v-if="!showUsers">
      <el-button type="primary" @click="submitForm('roleForm')">
        保存
      </el-button>
      <el-button @click="resetForm('roleForm')">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import PoliciesSelector from './policiesSelector.vue'
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()
export default {
  computed: {
    id() {
      return this.$route.params.id
    },
    showUsers() {
      return this.$route.query.include === 'users'
    }
  },
  components: { PoliciesSelector },
  data() {
    return {
      users: [],
      roleForm: {
        name: '',
        description: '',
        policies: []
      },
      rules: {
        name: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { min: 1, max: 10, message: '长度在 1 到 10 个字符', trigger: 'blur' }
        ],
        description: [
          { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    if (this.id) {
      this.getDetail()
    }
  },
  methods: {
    async getDetail() {
      const [err, r] = await client.detailRole({
        body: {
          roleId: this.id,
          include: this.$route.query.include === 'users' ? 'users' : ''
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.roleForm = r.role
      this.users = r.users
    },
    async submitForm(formName) {
      const valid = await this.$refs[formName].validate()
      if (!valid) return
      if (this.id) {
        const [err, r] = await client.updateRole({
          body: {
            roleId: this.id,
            role: this.roleForm
          }
        })
        if (err) {
          handleError(err)
          return
        }

        this.$message.success('修改成功')
        this.$router.push('/roles')

        return
      }

      const [err, r] = await client.createRole({
        body: {
          role: this.roleForm
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.$message({
        message: '创建成功',
        type: 'success'
      })

      this.$router.push('/roles')
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$refs.policiesSelector.reset()
    }
  }
}
</script>

<style scoped>
.demo-roleForm {
  width: 500px;
  margin: 20px auto;
}
</style>
