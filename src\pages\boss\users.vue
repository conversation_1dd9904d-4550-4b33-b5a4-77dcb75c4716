<template>
  <div class="users">
    <div style="text-align: right">
      <el-button type="primary" @click="$router.push('/users/new')">
        新建
      </el-button>
    </div>
    <el-form :inline="true" class="search">
      <el-form-item label="工号">
        <el-input v-model="conditions.name"></el-input>
      </el-form-item>
      <el-form-item label="用户名称">
        <el-input v-model="conditions.fullName"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      size="small"
      height="calc(100vh - 160px)"
      :data="users"
      border
    >
      <el-table-column prop="name" label="工号"></el-table-column>
      <el-table-column prop="fullName" label="姓名"></el-table-column>
      <el-table-column prop="roleIds" label="角色">
        <template slot-scope="scope">
          {{ roleIdsToString(scope.row.roleIds) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <span
            :style="{ color: scope.row.status === 'allowed' ? 'green' : 'red' }"
          >
            {{ statusToString(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="allowUser(scope.row.id)"
            v-if="scope.row.status === 'forbidden'"
          >
            启用
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="forbidUser(scope.row.id)"
            v-if="scope.row.status === 'allowed'"
          >
            禁用
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="$router.push(`/users/${scope.row.id}/edit`)"
          >
            编辑
          </el-button>
          <!-- <el-button type="text" size="small" @click="deleteUser(scope.row.id)">
            删除
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      conditions: {
        name: '',
        fullName: ''
      },
      users: [],
      roles: [],
      loading: true
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    async allowUser(userId) {
      const ok = await this.$confirm('确定要启用该用户吗？', '启用确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })
      if(!ok){
        return 
      }
      const [err, r] = await client.allowUser({
        body: {
          userId
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.getList()
    },
    async forbidUser(userId) {
      const ok = await this.$confirm('确定要禁用该用户吗？', '禁用确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })
      if(!ok){
        return 
      }
      
      const [err, r] = await client.forbidUser({
        body: {
          userId
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.getList()
    },
    async deleteUser(userId) {
      const isOk = await this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      if (!isOk) {
        return
      }

      const [err, r] = await client.deleteUser({
        body: {
          userId
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.getList()
    },
    statusToString(status) {
      if (status === 'allowed') {
        return '正常'
      }
      if (status === 'forbidden') {
        return '禁用'
      }
      return '--'
    },
    roleIdsToString(roleIds) {
      var ss = []
      for (var id of roleIds) {
        const role = this.roles.find(c => c.id === id)
        if (role) {
          ss.push(role.name)
        }
      }
      return ss.join(', ')
    },
    onSearch() {
      const name = this.conditions.name.trim()
      const fullName = this.conditions.fullName.trim()
      if (!name && !fullName) {
        return
      }

      this.users = this.users.filter(user => {
        if (name && fullName) {
          return user.fullName.includes(fullName) && user.name === name
        }
        if (this.conditions.fullName) {
          return user.fullName.includes(fullName)
        }
        if (this.conditions.name) {
          return user.name === name
        }

        return false
      })
    },
    onReset() {
      this.conditions = {
        fullName: '',
        name: ''
      }
      this.getList()
    },
    async getList() {
      this.loading = true
      const [err, r] = await client.listUsers({
        body: {
          include: 'roles'
        }
      })
      this.loading = false
      if (err) {
        handleError(err)
        return
      }

      this.users = r.users
      this.roles = r.roles
    }
  }
}
</script>

<style></style>
