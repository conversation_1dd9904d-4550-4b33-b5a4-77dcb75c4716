<template>
  <el-form
    :model="userForm"
    :rules="rules"
    ref="userForm"
    label-width="100px"
    class="form"
  >
    <el-form-item label="工号" prop="name">
      <el-input
        v-model="userForm.name"
        placeholder="请输入工号"
        maxlength="20"
        show-word-limit
      ></el-input>
    </el-form-item>
    <el-form-item label="姓名 " prop="fullName">
      <el-input
        v-model="userForm.fullName"
        placeholder="请输入姓名"
        maxlength="16"
        show-word-limit
      ></el-input>
    </el-form-item>

    <el-form-item label="设置角色" prop="roleIds">
      <RoleIdsSelector v-model="userForm.roleIds" ref="roleIdsSelector" @validator="validator" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm('userForm')">
        保存
      </el-button>
      <el-button @click="resetForm('userForm')">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import RoleIdsSelector from './roleIdsSelector.vue'
import handleError from '../../helpers/handleError'
import makeClient from '../../services/boss/makeClient'
const client = makeClient()
export default {
  components: { RoleIdsSelector },
  computed: {
    id() {
      return this.$route.params.id
    }
  },
  data() {
    return {
      userForm: {
        name: '',
        fullName: '',
        roleIds: []
      },
      rules: {
        name: [
          { required: true, message: '请输入工号', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ],
        fullName: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' }
        ],
        roleIds: [{ required: true, message: '请选择角色', trigger: 'blur' }]
      }
    }
  },
  created() {
    if (this.id) {
      this.getDetail()
    }
  },
  methods: {
    async getDetail() {
      const [err, r] = await client.detailUser({
        body: {
          userId: this.id
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.userForm = r.user
    },
    async submitForm(formName) {
      const valid = await this.$refs[formName].validate()
      if (!valid) return
      if (this.id) {
        const [err, r] = await client.updateUser({
          body: {
            userId: this.id,
            user: this.userForm
          }
        })
        if (err) {
          handleError(err)
          return
        }

        this.$message.success('修改成功')
        this.$router.push('/users')

        return
      }

      const [err, r] = await client.createUser({
        body: {
          user: this.userForm
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.$message({
        message: '创建成功',
        type: 'success'
      })

      this.$router.push('/users')
    },
    validator() {
      this.$refs.userForm.clearValidate('roleIds')
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$refs.RoleIdsSelector.reset()
    }
  }
}
</script>

<style scoped>
.form {
  width: 500px;
  margin: 20px auto;
}
</style>
