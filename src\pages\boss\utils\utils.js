export const exportFileByFront = (data, options, fileName) => {
  if (!data.length) {
    alert('没有数据可以导出！')
    return
  }
  const blob = new Blob(['\ufeff' + options], {
    type: 'text/csv;charset=utf-8;'
  })

  const url = URL.createObjectURL(blob)

  const downloadLink = document.createElement('a')
  downloadLink.href = url
  downloadLink.setAttribute('download', fileName)
  document.body.appendChild(downloadLink)
  downloadLink.click()
  document.body.removeChild(downloadLink)
}
