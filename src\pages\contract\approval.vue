<template>
  <RightLayout class="approval">
    <TopBar>
      <Breadcrumb :title="$route.meta.title">
        <template #rightButton>
          <el-button
            v-if="hadPrivilege('contract2.contractSet.flowManagement.manage')"
            @click="$router.push(`/approvals/${$route.params.id}/edit`)"
            type="text"
          >
            <i class="el-icon-edit-outline" />编辑
          </el-button>
        </template>
      </Breadcrumb>
    </TopBar>
    <MiddleBox>
      <div style="margin: 60px auto; width: 1050px">
        <Title style="margin: 58px 0 24px 0; position: relative">
          <span
            :style="{
              marginLeft: '4px',
              fontSize: '14px',
              color: '#24262A',
              fontWeight: '600'
            }"
          >
            基本信息
          </span>
        </Title>
        <el-row :gutter="20">
          <el-col :span="12">
            <span class="label">合同流程名称</span>
            <span class="value">
              <el-input
                v-model="contractApproval.name"
                maxlength="50"
                :disabled="true"
                placeholder="请输入合同流程名称"
              />
            </span>
          </el-col>
          <el-col :span="12">
            <span class="label">所在分组</span>
            <span class="value">
              <SelectApprovalGroup
                v-model="contractApproval.groupId"
                :disabled="true"
            /></span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <span class="label">适用合同类型 </span>
            <span class="value">
              <template
                v-if="
                  contractApproval.types && contractApproval.types.length > 0
                "
              >
                <CascaderApprovalType
                  v-model="contractApproval.types"
                  :disabled="true"
                />
              </template>
              <span v-else>-</span>
            </span>
          </el-col>
          <el-col :span="12">
            <span class="label">备注</span>
            <span class="value">{{ contractApproval.remark || '-' }}</span>
          </el-col>
        </el-row>

        <ApprovalConfig
          style="margin-top: 50px"
          v-if="isChildRender"
          v-model="contractApproval.approveNodes"
          :disabled="true"
        />
      </div>
    </MiddleBox>
  </RightLayout>
</template>
<script>
import Breadcrumb from '../../components/contract/breadcrumb.vue'
import RightLayout from '../../components/contract/rightLayout.vue'
import TopBar from '../../components/contract/topBar.vue'
import MiddleBox from '../../components/contract/middleBox.vue'
import CascaderApprovalType from './approvalsNew/cascaderApprovalType.vue'
import SelectApprovalGroup from './approvalsNew/selectApprovalGroup.vue'
import ApprovalConfig from './approvalsNew/approvalConfig.vue'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import makeContractClient from '../../services/contract/makeClient'
import { hadPrivilege } from '../../helpers/profile'

const client = makeContractClient()
export default {
  components: {
    Breadcrumb,
    RightLayout,
    TopBar,
    MiddleBox,
    SelectApprovalGroup,
    ApprovalConfig,
    CascaderApprovalType
  },
  async created() {
    if (!this.$route.params.id) {
      return
    }
    this.isChildRender = false
    const [err, r] = await client.approveQueryById({
      body: {
        id: this.$route.params.id
      }
    })

    if (err) {
      handleError(err)
      this.isChildRender = true
      return
    }
    this.contractApproval = r.data
    this.contractApproval.approveNodes.unshift({
      user: {},
      approveType: 'launch',
      dragDisable: true
    })
    for (let approveNode of this.contractApproval.approveNodes) {
      if (!approveNode.key) {
        approveNode.key = Math.random()
      }
    }
    this.isChildRender = true
  },
  data() {
    return {
      contractApproval: {
        approveNodes: [
          {
            user: {},
            approveType: 'launch',
            dragDisable: true,
            key: Math.random()
          },
          {
            user: {
              id: undefined,
              name: '',
              departments: null
            },
            key: Math.random()
          }
        ]
      },

      conflictApprovals: [],
      submitCheckDialog: false,
      isChildRender: true
    }
  },

  methods: {
    cancel() {
      this.$router.go(-1)
    },
    hadPrivilege
  }
}
</script>
<style scoped>
/* ::v-deep .el-form-item{
    margin: 0;
  }
  ::v-deep .el-form-item__label{
    padding: 0;
  } */
::v-deep .el-dialog__body {
  padding-top: 0;
}
.approval ::v-deep .is-disabled .el-input__inner {
  background-color: white;
  outline: none;
  border: 0;
  color: #24262a;
  padding: 0;
}

.approval ::v-deep .el-input__icon::before {
  content: '';
}
.approval ::v-deep .el-tag {
  background-color: #fff;
  padding: 0;
  margin: 0;
  color: #24262a;
  margin-right: 10px;
}
.label {
  color: #777c94;
  margin-right: 24px;
  font-weight: 400;
  font-size: 14px;
  letter-spacing: 0;
  text-align: right;
  min-width: 120px;
  text-align: right;
  display: inline-block;
  display: flex;
  align-items: center;
  justify-content: end;
}
.value {
  font-weight: 400;
  font-size: 14px;
  color: #24262a;
  letter-spacing: 0;
  line-height: 14px;
  flex: 1;
}
::v-deep .el-col {
  margin-bottom: 24px;
  display: flex;
}
</style>