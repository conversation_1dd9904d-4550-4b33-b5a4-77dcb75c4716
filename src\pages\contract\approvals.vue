<template>
  <RightLayout v-if="this.groups && this.groups.length">
    <TopBar>
      <div style="display: flex">
        <h1>合同流程管理</h1>
      </div>
    </TopBar>
    <div
      style="padding: 12px 24px 24px 24px; lex: '1 1 auto'; text-align: right"
    >
      <el-button @click="$router.push('/approvals/new')" type="primary">
        <i class="olading-iconfont oi-icon_add2" />
        新建流程
      </el-button>
      <el-button plain @click="showCreateGroupForm">新建分组</el-button>
      <el-button
        plain
        v-if="needSortGroups.length > 1"
        @click="dialogShown.sortGroups = true"
      >
        分组排序
      </el-button>
    </div>
    <MiddleBox style="padding: 0px 24px 0 24px" top="250px">
      <div :key="group.groupId" v-for="group in groups">
        <Group
          :group="group"
          @enableApproval="enableApproval"
          @disableApproval="disableApproval"
          @deleteApproval="deleteApproval"
          @confirmSort="sortApproval"
          @rename="renameGroupForm"
          @delete="deleteGroupForm"
          @editApproval="editApproval"
          @showApproval="showApproval"
        />
      </div>
    </MiddleBox>
    <DialogGroupForm
      :visible.sync="dialogShown.groupForm"
      :existedGroups="groups"
      :group="currentEditGroup"
      @submit="submitGroupForm"
    />
    <DialogSortGroups
      :visible.sync="dialogShown.sortGroups"
      :groups="needSortGroups"
      @submit="submitSortGroup"
    />
  </RightLayout>
</template>
<script>
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import makeContractClient from '../../services/contract/makeClient'
import Group from '../../components/contract/approval/group.vue'
import DialogGroupForm from '../../components/contract/approval/groupFormDialog.vue'
import DialogSortGroups from '../../components/contract/approval/sortGroupsDialog.vue'
import RightLayout from '../../components/contract/rightLayout.vue'
import TopBar from '../../components/contract/topBar.vue'
import MiddleBox from '../../components/contract/middleBox.vue'
const client = makeContractClient()
export default {
  components: {
    Group,
    DialogGroupForm,
    DialogSortGroups,
    TopBar,
    MiddleBox,
    RightLayout
  },
  async created() {
    const loading = this.$loading({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255,255, 0.7)'
    })
    await this.reload()
    loading.close()
  },
  data() {
    return {
      groups: [],
      dialogShown: {
        groupForm: false,
        sortGroups: false
      },
      currentEditGroup: null,
      groupForm: {
        name: ''
      }
    }
  },
  computed: {
    needSortGroups() {
      var r = []
      for (var c of this.groups) {
        if (c.name !== '其他') {
          r.push(c)
        }
      }

      return r
    }
  },
  methods: {
    async reload() {
      const [err, r] = await client.approveQuery({
        body: {}
      })
      if (err) {
        handleError(err)
        return
      }

      this.groups = r.data
      // 其他返回的sort:null
      for (let group of this.groups) {
        if (group.name === '其他') {
          group.sort = 9999
        }
      }
      this.groups.sort((a, b) => (a.sort < b.sort ? -1 : 1))
    },
    // 弹出重命名分组对话框
    renameGroupForm(group) {
      this.currentEditGroup = group
      this.dialogShown.groupForm = true
    },
    // 弹出新建分组对话框
    showCreateGroupForm() {
      this.currentEditGroup = null
      this.dialogShown.groupForm = true
    },
    // 取消分组对话框
    cancelGroupForm() {
      this.dialogShown.groupForm = false
    },
    // 分组对话框确定触发事件
    submitGroupForm(groupForm) {
      if (this.currentEditGroup) {
        this.renameGroup(groupForm)
        return
      }
      this.createGroup(groupForm)
    },
    // 调用创建分组接口
    async createGroup(groupForm) {
      const groupName = groupForm.name.trim()
      if (!groupName) {
        throw new Error('group name is required')
      }

      const [err, _] = await client.approveGroupAdd({
        body: {
          name: groupName,
          sort: this.groups[0].sort - 1
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.dialogShown.groupForm = false

      this.reload()
    },
    // 调用重命名接口
    async renameGroup(groupForm) {
      if (!this.currentEditGroup) {
        throw new Error('currentEditGroup is required')
      }
      const [err, _] = await client.approveGroupRename({
        body: {
          id: this.currentEditGroup.id,
          name: groupForm.name.trim(),
          sort: this.currentEditGroup.sort
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.dialogShown.groupForm = false
      this.reload()
    },
    // 删除分组
    deleteGroupForm(group) {
      this.$confirm(
        `<b>确认删除分组${group.name}吗？</b>` +
          `<br/>分组内的合同流程不会被删除，将会移入到【其他】分组。`,
        '删除',
        {
          type: 'warning',
          dangerouslyUseHTMLString: true,
          closeOnClickModal: false
        }
      ).then(async () => {
        const [err, _] = await client.approveGroupRemove({
          body: {
            id: group.id
          }
        })
        if (err) {
          handleError(err)
          return
        }
        this.reload()
      })
    },
    // 组内顺序
    async sortApproval(id, approvals) {
      var req = []
      for (var c of approvals) {
        req.push({
          groupId: id,
          id: c.id,
          sort: c.sort
        })
      }

      const [err, _] = await client.approveUpdateApproveSort({
        body: req
      })
      if (err) {
        handleError(err)
        return
      }
      this.reload()
    },
    // 启用
    async enableApproval(approval) {
      const [err, r] = await client.approveEnable({
        body: {
          id: approval.id
        }
      })
      if (err) {
        handleError(err)
        return
      }
      handleSuccess('合同流程启用成功')
      this.reload()
    },
    // 停用
    async disableApproval(approval) {
      const [err, _] = await client.approveDisableCheck({
        body: {
          id: approval.id
        }
      })
      if (err && err.errorCode === 501) {
        this.$msgbox({
          title: '停用提示',
          message:
            '<b>存在模板使用该流程，无法停用</b>' +
            '<br/>已有使用中的模板使用该流程，请先停用模板或修改模板的审核流程。模板调整后，可以停用该流程。',
          dangerouslyUseHTMLString: true,
          type: 'warning',
          confirmButtonText: '我知道了',
          closeOnClickModal: false
        })
        return
      }
      if (err) {
        handleError(err)
        return
      }

      this.$confirm(
        `<b>确认要停用【${approval.name}】吗？</b>` +
          '<br/>停用后，已使用该流程的审核流程不受影响。',
        '停用',
        {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          closeOnClickModal: false
        }
      ).then(async () => {
        const [err, _] = await client.approveDisable({
          body: {
            id: approval.id
          }
        })
        if (err) {
          handleError(err)
          return
        }
        handleSuccess('合同流程停用成功')
        this.reload()
      })
    },
    // 删除
    deleteApproval(approval) {
      this.$confirm(
        `<b>确认要删除【${approval.name}】吗？</b>` +
          '<br/>删除后，已使用该流程的审核流程不受影响。',
        '删除',
        {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          closeOnClickModal: false
        }
      ).then(async () => {
        const [err, _] = await client.approveRemove({
          body: { id: approval.id }
        })
        if (err) {
          handleError(err)
          return
        }
        handleSuccess('合同流程删除成功')
        this.reload()
        close()
      })
    },
    // 组排序
    async submitSortGroup(sortedGroups) {
      // approveGroupUpdateSort
      const l = sortedGroups.length
      for (var i = 0; i < l; i++) {
        sortedGroups[i].sort = i + 10
      }
      const [err, r] = await client.approveGroupUpdateSort({
        body: sortedGroups
      })
      if (err) {
        handleError(err)
        return
      }

      this.dialogShown.sortGroups = false
      this.reload()
    },
    editApproval(row) {
      this.$router.push(`/approvals/${row.id}/edit`)
    },
    showApproval(row) {
      this.$router.push(`/approvals/${row.id}`)
    }
  }
}
</script>
<style scoped></style>