<template>
  <RightLayout>
    <TopBar>
      <Breadcrumb :title="$route.meta.title" />
    </TopBar>
    <MiddleBox style="padding: 20px 0 200px 40px">
      <el-form
        :model="contractApproval"
        ref="contractApprovalForm"
        :rules="rules"
        label-position="top"
      >
        <div :style="{ width: '580px' }">
          <el-form-item label="合同流程名称" prop="name">
            <el-input
              v-model="contractApproval.name"
              maxlength="50"
              :disabled="isShowApproval"
              placeholder="请输入合同流程名称"
            />
          </el-form-item>
          <el-form-item label="所在分组" prop="groupId">
            <SelectApprovalGroup
              v-model="contractApproval.groupId"
              :disabled="isShowApproval"
            />
          </el-form-item>
          <el-form-item label="适用合同类型" prop="types">
            <!-- <SelectApprovalType v-model="contractApproval.types" :disabled="isShowApproval"/> -->
            <CascaderApprovalType
              v-model="contractApproval.types"
              :disabled="isShowApproval"
            />
            <p
              v-if="hadPrivilege('contract2.contractSet.typeManagement.manage')"
              :style="{ margin: '0', color: '#777C94', fontSize: '12px' }"
            >
              管理合同类型，
              <span
                :style="{ color: '#4F71FF', cursor: 'pointer' }"
                @click="jumpToTypesPage"
                >点击这里</span
              >
            </p>
          </el-form-item>
          <el-form-item prop="remark" label="备注">
            <el-input
              v-model="contractApproval.remark"
              maxlength="100"
              show-word-limit
              :disabled="isShowApproval"
              type="textarea"
              rows="6"
              placeholder="请输入使用说明"
            />
          </el-form-item>
        </div>
        <el-form-item prop="approveNodes" style="margin-bottom: 100px">
          <ApprovalConfig
            v-if="isChildRender"
            v-model="contractApproval.approveNodes"
            :disabled="isShowApproval"
            @validFrom="validFrom"
          />
        </el-form-item>
      </el-form>
    </MiddleBox>
    <BottomBar v-if="!isShowApproval">
      <el-button plain @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </BottomBar>
    <el-dialog
      title="提示"
      :visible.sync="submitCheckDialog"
      width="30%"
      :close-on-click-modal="false"
    >
      <h3 style="color: #24262a; font-weight: 600; font-size: 14px">
        <i
          style="
            color: #e59b00;
            font-size: 16px;
            margin-right: 6px;
            vertical-align: middle;
          "
          class="el-icon-warning"
        ></i
        >存在合同类型已关联其他审核流程，确定要进行更改吗？
      </h3>
      <p style="color: #777c94; font-size: 12px">
        以下合同类型已关联其他审核流程：
      </p>
      <ul
        style="
          background-color: #f6fafd;
          padding: 20px 0 20px 40px;
          font-size: 12px;
        "
      >
        <li v-for="approval in conflictApprovals" :key="approval.id">
          【{{ approval.name }}】
        </li>
      </ul>
      <span slot="footer" class="dialog-footer">
        <el-button @click="submitCheckDialog = false">返回修改</el-button>
        <el-button type="primary" @click="continueSumbit">继续提交</el-button>
      </span>
    </el-dialog>
  </RightLayout>
</template>
<script>
import Breadcrumb from '../../components/contract/breadcrumb.vue'
import RightLayout from '../../components/contract/rightLayout.vue'
import TopBar from '../../components/contract/topBar.vue'
import MiddleBox from '../../components/contract/middleBox.vue'
import BottomBar from '../../components/contract/bottomBar.vue'
import SelectApprovalType from './approvalsNew/selectApprovalType.vue'
import CascaderApprovalType from './approvalsNew/cascaderApprovalType.vue'
import SelectApprovalGroup from './approvalsNew/selectApprovalGroup.vue'
import ApprovalConfig from './approvalsNew/approvalConfig.vue'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import makeContractClient from '../../services/contract/makeClient'
import { hadPrivilege } from '../../helpers/profile'
const client = makeContractClient()
export default {
  components: {
    Breadcrumb,
    RightLayout,
    TopBar,
    MiddleBox,
    BottomBar,
    SelectApprovalGroup,
    SelectApprovalType,
    ApprovalConfig,
    CascaderApprovalType
  },
  async created() {
    if (!this.$route.params.id) {
      return
    }
    this.isChildRender = false
    const [err, r] = await client.approveQueryById({
      body: {
        id: this.$route.params.id
      }
    })

    if (err) {
      handleError(err)
      this.isChildRender = true
      return
    }
    this.contractApproval = r.data
    this.contractApproval.approveNodes.unshift({
      user: {},
      approveType: 'launch',
      dragDisable: true
    })
    for (let approveNode of this.contractApproval.approveNodes) {
      if (!approveNode.key) {
        approveNode.key = Math.random()
      }
    }
    this.isChildRender = true
    // mock回显部门数据
    // this.contractApproval =  {
    //     ...r.data,
    //     approveNodes:[{user:{id:344836,departments:[{name:'创新产品'}]}}]
    //   }
  },
  data() {
    return {
      contractApproval: {
        approveNodes: [
          {
            user: {},
            approveType: 'launch',
            dragDisable: true,
            key: Math.random()
          },
          {
            user: {
              id: undefined,
              name: '',
              departments: null
            },
            key: Math.random()
          }
        ]
      },
      rules: {
        name: [{ required: true, message: '请输入合同流程名称' }],
        groupId: [{ required: true, message: '请选择所在分组' }],
        approveNodes: [
          {
            validator(rule, value, bc) {
              for (let node of value) {
                if (!node.user.id && node.approveType !== 'launch') {
                  bc(new Error('请选择审批人'))
                  return
                }
              }
              // 校验审批人是否重复
              for (let index in value) {
                const cur = value[index]
                for (let index2 in value) {
                  if (
                    index !== index2 &&
                    cur.user.id === value[index2].user.id
                  ) {
                    bc(new Error('审批人已存在，不能重复！'))
                  }
                }
              }
              if (value.length === 1) {
                bc(new Error('至少设置一个审批节点！'))
                return
              }
              bc()
            }
          }
        ]
      },
      conflictApprovals: [],
      submitCheckDialog: false,
      isChildRender: true
    }
  },
  computed: {
    isShowApproval() {
      return this.$route.path.indexOf('edit') === -1 && this.$route.params.id
    }
  },
  methods: {
    async submit() {
      this.$refs.contractApprovalForm.validate(valid => {
        if (valid) {
          // 如果没有填合同类型则不校验
          if (
            this.contractApproval.types &&
            this.contractApproval.types.length > 0
          ) {
            this._submit()
          } else {
            this.sendSaveOrUpdate()
          }
        } else {
          this.scrollIntoError(this.$refs.contractApprovalForm)
        }
      })
    },
    async _submit() {
      const _this = this
      // 使用合同类型格式转换
      let { types } = this.contractApproval
      const ids = types.map(type => type.id)
      //  关联合同类型校验
      const [err, r] = await client.approveCheckRelateContractType({
        body: { ids: ids, id: this.$route.params.id }
      })
      // 弹窗
      if (err) {
        handleError(err)
        return
      }
      if (r.data.length === 0) {
        // 校验通过
        this.sendSaveOrUpdate()
      } else {
        this.conflictApprovals = r.data
        this.submitCheckDialog = true
      }
    },
    continueSumbit() {
      this.sendSaveOrUpdate()
      this.submitCheckDialog = false
    },
    async sendSaveOrUpdate() {
      const approveNodes = this.contractApproval.approveNodes.filter(
        apprvoe => apprvoe.approveType !== 'launch'
      )
      const contractApproval = {
        ...this.contractApproval,
        approveNodes
      }
      // 保存编辑
      if (!this.$route.params.id) {
        const [err, r] = await client.approveSave({
          body: { ...contractApproval, enable: true }
        })
        if (err) {
          handleError(err)
          return
        }
        handleSuccess('合同流程保存成功！')
      } else {
        const [err, r] = await client.approveUpdate({
          body: { ...contractApproval, id: this.$route.params.id }
        })
        if (err) {
          handleError(err)
          return
        }
        handleSuccess('合同流程修改成功！')
      }
      this.$router.go(-1)
    },
    cancel() {
      this.$router.go(-1)
    },
    jumpToTypesPage() {
      const routers = this.$router.resolve({ path: '/types' })
      window.open(routers.href, '_blank')
    },
    // 手动校验表单，取消红色提示
    validFrom() {
      this.$refs['contractApprovalForm'].validate()
    },
    hadPrivilege
  }
}
</script>
<style scoped>
/* ::v-deep .el-form-item{
    margin: 0;
  }
  ::v-deep .el-form-item__label{
    padding: 0;
  } */
::v-deep .el-dialog__body {
  padding-top: 0;
}
</style>