<template>
  <div>
    <Title style="margin-bottom: 16px">
      <span
        :style="{
          color: 'red',
          marginRight: '4px'
        }"
      >
      </span
      >默认规则配置
    </Title>
    <DragableList
      :value="value"
      :columns="columns"
      :showIndex="true"
      :allowDrag="!disabled"
      @input="handleDrag"
    >
      <template v-slot="{ item, index }">
        <template v-if="item.approveType === 'launch'">
          <div>发起节点</div>
          <div>默认发起人</div>
          <div>-</div>
          <div>-</div>
        </template>
        <template v-else>
          <div>审批节点</div>
          <!-- <el-form-item :prop="`approveNodes.`+(index-1)" :rules="approveRule"> -->
          <div>
            <PlatformEmployeeSelect
              styleStr="width: 460px"
              :disabled="disabled"
              :value="item.user"
              @input="empObj => selectEmployee(empObj, index)"
            />
          </div>
          <!-- </el-form-item> -->
          <div
            style="
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            "
            :title="showDepartemts(item.user.departments)"
          >
            {{ showDepartemts(item.user.departments) }}
          </div>
          <a
            v-if="!disabled"
            :style="{
              color: '#4F71FF',
              cursor: 'pointer',
              marginRight: '20px'
            }"
            @click="
              () => {
                const n = [...value]
                n.splice(index, 1)
                $emit('input', n)
                $emit('validFrom')
              }
            "
          >
            删除
          </a>
          <template v-else> - </template>
        </template>
      </template>
    </DragableList>
    <el-button
      v-if="!disabled"
      @click="
        () => {
          const n = [...value]
          n.push({ user: {}, key: Math.random() })
          $emit('input', n)
        }
      "
      icon="el-icon-plus"
      plain
      >添加节点</el-button
    >
  </div>
</template>

<script>
import Title from '../../../components/contract/title.vue'
import DragableList from '../../../components/contract/draggableList.vue'
import PlatformEmployeeSelect from './platformEmployeeSelect.vue'
export default {
  name: 'ApprovalConfig',
  components: {
    Title,
    DragableList,
    PlatformEmployeeSelect
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  mounted() {},
  data() {
    return {
      columns: [
        {
          label: '流程节点名称',
          width: '240px'
        },
        {
          label: '审批人',
          width: '484px'
        },
        {
          label: '部门',
          width: '220px'
        },
        {
          label: '操作',
          width: 'auto'
        }
      ]
      // approveRule: [
      //   {
      //     validator: (rule, value, callback) => {
      //       if (!value.user.name) {
      //         return callback(new Error('审批人不能为空'))
      //       }
      //       callback()
      //     }
      //   }
      // ]
    }
  },
  methods: {
    selectEmployee(empObj, index) {
      let formatterDepartments = null
      // 转换数据结构
      if (empObj.deptMember)
        formatterDepartments = empObj.deptMember.map(dep => ({
          ...dep,
          name: dep.deptName,
          id: dep.deptId
        }))
      const n = [...this.value]
      n[index].user = {
        name: empObj.name,
        id: empObj.userId,
        mobile: empObj.cellPhone,
        departments: formatterDepartments
      }
      this.$emit('input', n)
    },
    // 用于展示部门信息
    showDepartemts(departemts) {
      if (departemts?.length > 0) {
        return departemts.reduce((pre, dep) => (pre += dep.name + ' '), '')
      } else {
        return '-'
      }
    },
    handleDrag(n) {
      this.$emit('input', n)
    }
  }
}
</script>