<template>
  <el-cascader
    :disabled="disabled"
    :show-all-levels="false"
    style="width: 100%"
    v-model="types"
    :options="options"
    :props="{ value: 'id', label: 'name', children: 'nodes', multiple: true }"
    @change="handleChange"
  ></el-cascader>
</template>

<script>
import makeContractClient from '../../../services/contract/makeClient'
const client = makeContractClient()
export default {
  name: 'selectApprovalType',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(n) {
        this.types = n.map(item => [item.groupId, item.id])
      }
    }
  },
  data() {
    return {
      options: [],
      types: [],
      cascaderProps: { multiple: true }
    }
  },
  methods: {
    handleChange(n) {
      let types = []
      for (let item of n) {
        types.push({ id: item[1], groupId: item[0] })
      }
      this.$emit('input', types)
    }
  },
  async mounted() {
    const [err, r] = await client.contractTypeGetTypeTree({
      body: {
        withDisable: false
      }
    })
    if (err) {
      handleError(err)
      return
    }
    const data = r.data || {}

    this.options = data
  }
}
</script>