<template>
  <el-select
    :style="styleStr"
    remote
    filterable
    :disabled="disabled"
    placeholder="请选择"
    :loading="loading"
    :remote-method="search"
    value-key="userId"
    v-model="emp"
    @change="handleChange"
  >
    <el-option
      :key="employee.id"
      v-for="employee in employees"
      :label="`${employee.name} (${employee.cellPhone})`"
      :value="employee"
    >
    </el-option>
  </el-select>
</template>
<script>
import handleError from '../../../helpers/handleError'
import makePlatformClient from '../../../services/platform/makeClient'
const pclient = makePlatformClient()
export default {
  props: {
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    value: {
      type: Object | Number
    },
    styleStr: {
      type: String,
      default: 'width: 210px'
    }
  },
  async mounted() {
    const n = this.value
    const filters = { withDeptMember: true }

    if (n && n.id) {
      this.emp = { userId: n.id * 1 }
      filters.userId = [n.id * 1]
    }
    const list = await this.loadEmployees({
      start: 0,
      limit: 100,
      filters
      // n
    })
    if (filters.userId && filters.userId.length > 0 && list.length > 0) {
      // 根据id查询只能查询出一条数据
      this.$emit('input', list[0])
    }
  },
  methods: {
    search(query) {
      let filters = { withDeptMember: true }
      const cellPhone = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/
      if (cellPhone.test(query)) {
        filters = {
          ...filters,
          cellPhone: [query]
        }
      } else {
        filters = {
          ...filters,
          keywords: query
        }
      }
      this.loadEmployees({
        start: 0,
        limit: 100,
        filters
      })
    },
    async loadEmployees(filters = {}) {
      const [err, r] = await pclient.platformListMerchantMember({
        body: filters
      })
      if (err) {
        handleError(err)
        return
      }

      this.loading = false

      this.employees = r.data.list
      return r.data.list
    },
    handleChange(newValue) {
      this.$emit('input', newValue)
    }
  },
  data() {
    return {
      loading: true,
      employees: [],
      emp: {}
    }
  }
}
</script>