<template>
  <el-select
    v-bind="$attrs"
    style="width: 100%"
    @change="handleChange"
    :value="value"
    placeholder="请选择"
  >
    <el-option
      v-for="item in options"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    >
    </el-option>
  </el-select>
</template>

<script>
import makeContractClient from '../../../services/contract/makeClient'
const client = makeContractClient()
export default {
  name: 'selectType',
  props: {
    value: {
      type: Number
    }
  },
  data() {
    return {
      options: []
    }
  },
  methods: {
    handleChange(n) {
      this.$emit('input', n)
    }
  },
  async created() {
    const [err, r] = await client.approveQuery({
      body: {}
    })
    if (err) {
      handleError(err)
      return
    }
    console.log(r.data, 'r.datar.data')
    this.options = r.data
    // 新建时默认选择第一个分组
    if (this.options.length > 0 && !this.$route.params.id) {
      this.$emit('input', this.options[0].id)
    }
  }
}
</script>