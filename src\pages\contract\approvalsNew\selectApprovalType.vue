<template>
  <el-select
    v-bind="$attrs"
    style="width: 100%"
    @change="handleChange"
    :value="value"
    multiple
    placeholder="请选择"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
    </el-option>
  </el-select>
</template>

<script>
import makeContractClient from '../../../services/contract/makeClient'
const client = makeContractClient()
export default {
  name: 'selectApprovalType',
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(n) {
        if (!n.length) {
          this.types = []
        }
        this.types = n.map(item => item.id)
      }
    }
  },
  data() {
    return {
      options: [],
      types: []
    }
  },
  methods: {
    handleChange(n) {
      console.log(n, 'nnnnnnnnn')
      this.$emit('input', n)
    }
  },
  async mounted() {
    const [err, r] = await client.contractTypeGetTypeDict({
      body: {}
    })
    if (err) {
      handleError(err)
      return
    }
    const data = r.data || {}
    const res = []
    for (let n in data) {
      res.push({ value: n, label: data[n] })
    }
    this.options = res
  }
}
</script>