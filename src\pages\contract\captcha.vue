<template>
  <div
    class="captcha"
    style="position: relative; display: flex; align-items: center"
  >
    <el-input
      :maxlength="4"
      v-model="answer"
      placeholder="图形验证码"
      @change="handleAnswer"
      style="z-index: 0"
      ref="input"
    />
    <el-image
      :src="captchaUrl"
      v-if="captchaUrl"
      @click="load()"
      style="
        width: 80px;
        height: 30px;
        position: absolute;
        right: 10px;
        cursor: pointer;
        z-index: 1;
      "
    >
      <div slot="placeholder" class="image-slot">
        加载中<span class="dot">...</span>
      </div>
    </el-image>
  </div>
</template>

<script>
import handleError from '../../helpers/handleError'
import makePlatformClient from '../../services/platform/makeClient'
const pClient = makePlatformClient()
export default {
  created() {
    this.load()
  },
  data() {
    return {
      answer: '',
      code: ''
    }
  },
  computed: {
    captchaUrl() {
      if(!this.code) return false
      const url = `${ window.env.api || window.env.apiPath }/api/merchant/platform/captcha?token=${encodeURIComponent(this.code)}`
      return url.replace('/api/api/', '/api').replace("//","/")
    }
  },
  methods: {
    async load() {
      console.log('load')
      const [err, r] = await pClient.merchantPlatformCreateCaptcha()
      if (err) {
        handleError(err)
        return
      }

      this.code = r.data

      this.answer = ''
      this.$emit('input', {
        token: this.code,
        answer: ''
      })

      this.$refs.input.focus()
    },
    reset() {
      this.load()
    },
    handleAnswer(v) {
      this.answer = v
      this.$emit('input', {
        token: this.code,
        answer: v
      })
    }
  }
}
</script>

<style>
</style>