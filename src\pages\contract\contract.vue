<template>
  <div class="templatesNewStep2" v-if="signingDetail">
    <div>
      <ContractWriteTopBar
        title="查看合同"
        :detailInfo="signingDetail"
        @back="back"
        @withdraw="$refs.withdrawContract.open()"
        @urge="$refs.promptContract.open()"
        @aduit="$refs.aduitContract.open()"
        @reissue="reissue"
        @terminate="terminate"
        @renewal="renewal"
        @modify="modify"
        @download="downloadContract"
      />
      <div
        :style="{
          display: 'flex'
        }"
      >
        <div
          class="webkit-scrollbar"
          :style="{
            flex: '0 0 240px',
            height: 'calc(100vh - 48px)',
            overflowY: 'auto',
            padding: '12px 24px',
            borderRight: '1px solid #EEF0F4',
            fontSize: '12px'
          }"
        >
          <el-collapse :value="['waitingSignatueFiles']">
            <el-collapse-item name="waitingSignatueFiles">
              <template slot="title">
                <Title
                  :title="`待签署文件 (${signingDetail.fileList.length}份)`"
                />
              </template>
              <WaitingSignatueFiles
                :pageFields="[]"
                :files="signingDetail.fileList"
                :fields="[]"
                :currentFileIndex="fileIndex"
                @select="selectFile"
                :showAmount="false"
                :showPagesNumber="true"
              />
            </el-collapse-item>
          </el-collapse>
          <div v-if="attachmentCount > 0">
            <Title :title="`附件 (${attachmentCount}份)`" />
            <AttachmentTips style="margin-top: 14px" />
            <el-collapse :value="['sender', 'signer']">
              <el-collapse-item
                name="sender"
                v-if="signingDetail && signingDetail.attachmentList.length > 0"
              >
                <template slot="title">
                  <Title :title="`发起方附件`" />
                </template>
                <AttachmentFiles
                  :readOnly="true"
                  @preview="previewAttachmentFile"
                  @download="_downloadContract"
                  v-model="signingDetail.attachmentList"
                />
              </el-collapse-item>
              <el-collapse-item
                name="signer"
                v-if="
                  signingDetail && signingDetail.signerAttachmentList.length > 0
                "
              >
                <template slot="title">
                  <Title :title="`签署方附件`" />
                </template>
                <AttachmentFiles
                  :readOnly="true"
                  @preview="previewAttachmentFile"
                  @download="_downloadContract"
                  :creator="signingDetail.creator"
                  v-model="signingDetail.signerAttachmentList"
                />
              </el-collapse-item>
            </el-collapse>
          </div>
          <template v-if="signingDetail.relateContractList">
            <Title
              :title="`相关合同协议 (${signingDetail.relateContractList.length}份)`"
              style="margin-bottom: 10px"
            />
            <RelatedContracts :contracts="signingDetail.relateContractList" />
          </template>
        </div>
        <div
          :style="{
            flex: '1 1 auto',
            width: '0px', //直接自动计算
            height: 'calc(100vh - 48px)',
            background: '#f2f2f2',
            overflow: 'hidden'
          }"
        >
          <FilePages
            ref="filePages"
            :fileId="signingDetail.fileList[fileIndex].fileId"
            :images="signingDetail.fileList[fileIndex].archiveImageList"
          >
            <template v-slot="{ pageNo, fileId }">
              <FilePageFieldWrite
                :key="index"
                v-for="(pageField, index) in pageFields.filter(
                  item => item.pageNo === pageNo && item.fileId === fileId
                )"
                :field="fields.find(item => item.id === pageField.fieldId)"
                :pageField="pageField"
              />
            </template>
          </FilePages>
        </div>
        <div
          id="rightBox"
          class="webkit-scrollbar"
          :style="{
            flex: '0 0 240px',
            height: 'calc(100vh - 48px)',
            overflowY: 'auto',
            padding: '12px 24px',
            borderLeft: '1px solid #EEF0F4',
            fontSize: '12px'
          }"
        >
          <Title title="合同签署信息" style="margin-bottom: 10px" />
          <ContractInfos :infos="signingDetail" />
          <Title title="合同履约状态" style="margin-bottom: 10px" />
          <Performance :contract="signingDetail" />
          <el-collapse
            :value="[
              'approvalProcesses',
              'signingDetail',
              'contractLogList',
              'carbonCopyList'
            ]"
          >
            <el-collapse-item
              name="approvalProcesses"
              v-if="
                signingDetail.approveProcessList &&
                signingDetail.approveProcessList.length
              "
            >
              <template slot="title">
                <Title title="审核流程" style="margin-bottom: 10px" />
              </template>
              <ApprovalProcesses
                :createTime="signingDetail.createTime"
                :creator="signingDetail.creator"
                :processes="signingDetail.approveProcessList"
              />
            </el-collapse-item>

            <el-collapse-item
              name="signingDetail"
              v-if="
                signingDetail.writeProcessList.length &&
                signingDetail.signProcessList.length
              "
            >
              <template slot="title">
                <Title title="签署流程" style="margin-bottom: 10px" />
              </template>
              <SignatureProcesses
                :writeProcesses="signingDetail.writeProcessList"
                :signProcesses="signingDetail.signProcessList"
              />
            </el-collapse-item>

            <el-collapse-item
              name="carbonCopyList"
              v-if="
                signingDetail.carbonCopyList &&
                signingDetail.carbonCopyList.length
              "
            >
              <template slot="title">
                <Title title="抄送方" style="margin-bottom: 10px" />
              </template>
              <CarbonCopies
                :carbonCopies="signingDetail.carbonCopyList"
                style="margin-bottom: 10px"
              />
            </el-collapse-item>

            <el-collapse-item
              name="contractLogList"
              v-if="
                signingDetail.contractLogList &&
                signingDetail.contractLogList.length
              "
            >
              <template slot="title">
                <Title title="文件时间流" style="margin-bottom: 10px" />
              </template>
              <Log :logs="signingDetail.contractLogList" />
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
    <PromptContract
      ref="promptContract"
      :rows="[
        {
          name: signingDetail.name,
          handlingBy: handler,
          id: signingDetail.id,
          status: signingDetail.status
        }
      ]"
    />
    <WithdrawContract
      ref="withdrawContract"
      @reload="load"
      :rows="[
        {
          name: signingDetail.name,
          id: signingDetail.id,
          status: signingDetail.status
        }
      ]"
    />
    <div
      :style="{
        display: 'none'
      }"
    >
      <img
        @load="fileImageLoad($event, file.fileId)"
        :src="file.archiveImageList[0]"
        :key="file.fileId"
        v-for="file of signingDetail.fileList"
      />
    </div>
    <AuditContract
      @reload="load"
      :rows="[
        {
          id: signingDetail.id
        }
      ]"
      ref="aduitContract"
    />
    <TemplateDialog @submit="submitToDraftStep1" ref="templateDialog" />
  </div>
</template>

<script>
import ContractWriteTopBar from '../../components/contract/signing/contractWriteTopBar.vue'
import Title from '../../components/contract/title.vue'
import FilePages from '../../components/contract/file/pages.vue'
import FilePageFieldWrite from '../../components/contract/contract/filePageFieldWrite.vue'
import AttachmentFiles from '../../components/contract/signingDraft/attachmentFiles.vue'
import AttachmentTips from '../../components/contract/signingDraft/attachmentTips.vue'
import ContractInfos from '../../components/contract/contract/infos.vue'
import SignatureProcesses from '../../components/contract/signing/contract/signatureProcesses.vue'
import CarbonCopies from '../../components/contract/signingDraft/carbonCopies.vue'
import WaitingSignatueFiles from '../../components/contract/signing/contract/waitingSignatueFiles.vue'
import makePlatformClient from '../../services/platform/makeClient'
import makeContractClient from '../../services/contract/makeClient'

import handleError from '../../helpers/handleError'
import PromptContract from './signings/promptContractDialog.vue'
import WithdrawContract from './signings/withdrawContractDialog.vue'
import AuditContract from './signings/auditContractDialog.vue'
import RelatedContracts from '../../components/contract/contract/relatedContracts.vue'
import Performance from '../../components/contract/contract/performance.vue'
import Log from '../../components/contract/contract/log.vue'
import ApprovalProcesses from '../../components/contract/contract/approvalProcesses.vue'
import makeDetail2PageFieldsFromFileList from '../../formatters/contract/template/makeDetail2PageFieldsFromFileList'
import makeDetail2FieldsFromFileList from '../../formatters/contract/template/makeDetail2FieldsFromFileList'
import formatPageFieldsPx from '../../formatters/contract/template/formatPageFieldsPx'
import TemplateDialog from './contracts/templateDialog.vue'
import { constractDownloadStatusSuccess } from '../../services/contract/constants'
const client = makeContractClient()
const pclient = makePlatformClient()

export default {
  components: {
    ContractWriteTopBar,
    Title,
    FilePages,
    FilePageFieldWrite,
    WaitingSignatueFiles,
    AttachmentFiles,
    AttachmentTips,
    ContractInfos,
    SignatureProcesses,
    CarbonCopies,
    PromptContract,
    WithdrawContract,
    RelatedContracts,
    Performance,
    Log,
    ApprovalProcesses,
    AuditContract,
    TemplateDialog
  },
  async created() {
    const params = new URLSearchParams(location.search)
    this.backURL = params.get('back')
    this.load()
  },
  mounted() {
    document.body.style.margin = 0
    document.body.style.padding = 0
    document.body.style.overflow = 'hidden'
  },
  methods: {
    back() {
      if (this.backURL && this.backURL.includes('http')) {
        location.href = this.backURL
        return
      }
      if (this.backURL && !this.backURL.includes('http')) {
        this.$router.push(this.backURL)
        return
      }

      this.$router.push('/contracts')
    },
    selectFile(i) {
      this.fileIndex = i
      document.getElementById('pagesBox').scroll({
        top: 0,
        behavior: 'smooth'
      })
    },

    // 预览文件
    async previewAttachmentFile(file, index) {
      const id = file.archiveId
      const [err, r] = await client.fileInfo({
        body: {
          id
        }
      })
      if (err) {
        handleError(err)
        return
      }
      const url = r.data.url
      window.open(url)
    },
    //imageIndex 暂未启用 默认同一文件中图片大小一致
    fileImageLoad(e, fileId) {
      var width = e.target.width
      var height = e.target.height
      this.fileImageSizes[fileId] = [width, height]
      // debugger
      formatPageFieldsPx(this.pageFields, this.fileImageSizes)
    },
    // 重新获取数据
    async load() {
      const id = this.$route.params.id
      if (!id) {
        throw new Error('id is required')
      }

      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255,255, 0.7)'
      })

      // this.signingDetail = fakeData.data
      const [err, r] = await client.signingDetail({
        body: {
          id: id
        }
      })
      if (err) {
        handleError(err)
        loading.close()
        if (err.errorCode === 501) {
          setTimeout(() => {
            this.$router.push('/signings')
          }, 3000)
        }
        if (err.message.includes('没有') && err.message.includes('权限')) {
          setTimeout(() => {
            this.$router.push('/signings')
          }, 3000)
        }
        return
      }

      this.signingDetail = r.data

      this.fields = makeDetail2FieldsFromFileList(this.signingDetail.fileList)
      this.pageFields = makeDetail2PageFieldsFromFileList(
        this.signingDetail.fileList
      )

      loading.close()
    },
    // 重新发起
    reissue() {
      this.$router.push(
        `/signings/drafts/${this.signingDetail.id}/step1/edit?source=CONTRACT_REISSUE`
      )
    },
    // 解约
    terminate() {
      this.$router.push(
        `/contracts/${this.signingDetail.id}/terminate?back=${this.$route.query.back}`
      )
    },
    // 续签
    renewal() {
      this.sourceType = 'CONTRACT_RENEWAL'
      this.$refs.templateDialog.open()
    },
    // 变更
    modify() {
      this.sourceType = 'CONTRACT_MODIFY'
      this.$refs.templateDialog.open()
    },
    // 提交模板选择对话框
    submitToDraftStep1(templateId) {
      this.$router.push(
        `/signings/drafts/${this.signingDetail.id}/step1/edit?source=${this.sourceType}&templateId=${templateId.id}`
      )
    },
    // 下载合同
    async downloadContract(props = {}) {
      let idList = [this.signingDetail.id]

      // 调用下载接口
      props.loading = true
      const [err, r] = await client.contractDownload({
        body: {
          idList
        }
      })
      if (err) {
        handleError(err)
        return
      }
      const downloadStatus = r.data.status
      // 下载任务状态：1-处理中（循环调用checkDownloadTask检测任务）；2-成功（直接使用archiveId下载文件）
      if (downloadStatus === constractDownloadStatusSuccess) {
        props.loading = false
        this._downloadContract({
          name: `合同文件下载_${this.timeStr}_${idList.length}份.zip`,
          archiveId: r.data.archiveId
        })
      } else {
        this.checkDownloadTask(r.data.downloadTaskId, props)
      }
    },
    // 轮询合同是否下载成功
    checkDownloadTask(downloadTaskId, props) {
      const timer = setInterval(async () => {
        const [err, r] = await client.contractCheckDownloadTask({
          body: {
            id: downloadTaskId
          }
        })
        if (err) {
          handleError(err)
          return
        }
        if (r.data.status === constractDownloadStatusSuccess) {
          props.loading = false
          clearInterval(timer)
          this._downloadContract({
            name: `合同文件下载_${this.timeStr}_${downloadTaskId.length}份.zip`,
            archiveId: r.data.archiveId
          })
        }
      }, 1000)
    },
    // 下载文件
    async _downloadContract(file) {
      const id = file.archiveId

      const name = file.name
      const [err, r] = await pclient.platformDownloadFile(
        {
          method: 'GET',
          headers: { 'content-type': 'application/octet-stream' }
        },
        { id, name }
      )
      if (err) {
        console.log(err, 'errrrrrr')
        return
      }

      window.open(r.url)
    }
  },
  computed: {
    handler() {
      return this.signingDetail.handlingBy
    },
    attachmentCount() {
      const { signingDetail } = this
      return (
        (signingDetail &&
          signingDetail.attachmentList &&
          signingDetail.attachmentList.length) +
        (signingDetail &&
          signingDetail.signerAttachmentList &&
          signingDetail.signerAttachmentList.length)
      )
    }
  },
  data() {
    return {
      backURL: '',
      fileIndex: 0,
      signingDetail: null,
      pageFields: [],
      fields: [],
      //用于计算后续的比例
      fileImageSizes: {},
      sourceType: ''
    }
  }
}
</script>

<style scoped></style>