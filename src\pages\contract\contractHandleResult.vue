<template>
  <RightLayout>
    <TopBar>
      <Breadcrumb :title="`批量${type === 'audit' ? '审核' : '撤回'}结果`" />
    </TopBar>
    <MiddleBox style="padding: 0 0 60px 35px">
      <AuditSuccess
        v-if="type === 'audit'"
        :successTotal="successTotal"
        :failTotal="failTotal"
      />
      <WithdrawSuccess
        v-if="type === 'withdraw'"
        :successTotal="successTotal"
        :failTotal="failTotal"
      />
      <o-table
        ref="o-table"
        :actionButtons="actionButtons"
        :pagination="{ fixed: true }"
        :tableHeader="tableHeader"
        :showPagination="true"
        :tableData="tableData"
        :total="listTotal"
        @paginationChange="paginationChange"
        emptyHeight="calc(100vh - 450px)"
      />
    </MiddleBox>
  </RightLayout>
</template>
<script>
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import Breadcrumb from '../../components/contract/breadcrumb.vue'
import RightLayout from '../../components/contract/rightLayout.vue'
import TopBar from '../../components/contract/topBar.vue'
import MiddleBox from '../../components/contract/middleBox.vue'
import AuditSuccess from '../../components/contract/contract/auditSuccess.vue'
import WithdrawSuccess from '../../components/contract/contract/withdrawSuccess.vue'
import formatDateTime from '../../formatters/dateTime'
import SignProcessList from '../../components/contract/signing/signProcessList.vue'
import CarbonCopyEmpList from '../../components/contract/signing/carbonCopyEmpList.vue'
import SignerStatusWithDot from '../../components/contract/signing/signerStatusWithDot.vue'

const client = makeContractClient()
export default {
  components: {
    Breadcrumb,
    RightLayout,
    TopBar,
    MiddleBox,
    AuditSuccess,
    WithdrawSuccess,
    SignProcessList,
    CarbonCopyEmpList,
    SignerStatusWithDot
  },
  async mounted() {
    this.type = this.$route.query.type || 'audit'
    this.failTotal = this.$route.query.failTotal
    this.successTotal = this.$route.query.successTotal
    this.contractIdList = JSON.parse(this.$route.query.contractIdList)
    this.load()
  },

  data() {
    return {
      type: 'audit',
      failTotal: 0,
      successTotal: 0,
      listTotal: 0,
      contractIdList: [12, 13, 16],
      tableHeader: [
        {
          prop: 'name',
          label: '合同名称',
          width: 200,
          fixed: true,
          render: (h, row) => {
            return h('div', [
              h('div', row.name),
              h(
                'div',
                `发起方: ${row.creator.legal.name}(${row.creator.signer.name})`
              )
            ])
          }
        },
        {
          label: '合同类型',
          prop: 'contractType'
        },
        {
          label: '签署方',
          prop: 'signerList',
          width: '150px',
          render: (h, row) => {
            if (this.group === 'DRAFT') {
              return h(DraftEmpList, {
                props: {
                  value: row.signerList
                }
              })
            } else {
              return h(SignProcessList, {
                props: {
                  value: {
                    signerList: row.signerList,
                    writeProcessList: row.writeProcessList,
                    signProcessList: row.signProcessList,
                    handlingBy: row.handlingBy
                  }
                }
              })
            }
          }
        },
        {
          label: '签署状态',
          prop: 'status',
          render: (h, row) => {
            return h(SignerStatusWithDot, {
              props: {
                value: row.status
              }
            })
          }
        },
        {
          label: '签署截止日期',
          prop: 'signEndTime',
          width: '110px',
          formatter: row => `
            <p>
              ${
                row.signEndTime
                  ? formatDateTime('yyyy-MM-dd', row.signEndTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '发起时间',
          prop: 'createTime',
          width: '120px',
          formatter: row => `
            <p>
              ${
                row.createTime
                  ? formatDateTime('yyyy-MM-dd HH:mm', row.createTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '合同开始日期',
          prop: 'startTime',
          width: '110px',
          formatter: row => `
            <p>
              ${
                row.startTime
                  ? formatDateTime('yyyy-MM-dd', row.startTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '合同到期日期',
          prop: 'endTime',
          width: '110px',
          formatter: row => `
            <p>
              ${row.endTime ? formatDateTime('yyyy-MM-dd', row.endTime) : '-'}
            </p>
          `,
          sort: true
        },
        {
          label: '签署完成时间',
          prop: 'signFinishTime',
          width: '120px',
          formatter: row => `
            <p>
              ${
                row.signFinishTime
                  ? formatDateTime('yyyy-MM-dd HH:mm', row.signFinishTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '抄送方',
          prop: 'carbonCopyList',
          render: (h, row) => {
            return h(CarbonCopyEmpList, {
              props: {
                value: row.carbonCopyList
              }
            })
          }
        },
        {
          label: '被证明人',
          prop: 'certifier',
          sort: true,
          formatter: row => `
            <p>
              ${row.certifier && row.certifier.name ? row.certifier.name : '-'}
            </p>
          `
        },
        {
          label: '最近更新时间',
          prop: 'updateTime',
          width: '120px',
          formatter: row => `
            <p>
              ${
                row.updateTime
                  ? formatDateTime('yyyy-MM-dd HH:mm', row.updateTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '合同编号',
          prop: 'no'
        }
      ],
      actionButtons: [
        {
          label: '查看',
          id: '1',
          click: row => this.$router.push(`/contracts/${row.id}`)
        }
      ],
      tableData: [],
      start: 1,
      limit: 20
    }
  },
  methods: {
    async load() {
      const [err, r] = await client.signingQueryContract({
        body: {
          limit: this.limit,
          start: this.start,
          withDeleted: true,
          withDisabled: true,
          withTotal: true,
          filters: {
            contractIdList: this.contractIdList
          }
        }
      })

      if (err) {
        handleError(err)
        return
      }
      this.tableData = r.data.list
      this.listTotal = r.data.total
    },
    paginationChange({ start, limit }) {
      this.start = start
      this.limit = limit
      this.load()
    }
  }
}
</script>
<style scoped></style>