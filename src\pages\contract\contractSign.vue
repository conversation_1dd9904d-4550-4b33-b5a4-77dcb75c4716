<template>
  <div class="templatesNewStep2" v-if="contractSignData">
    <div>
      <ContractWriteTopBar
        title="签署合同"
        submitText="提交签署"
        :detailInfo="contractSignData"
        @back="back"
        @submit="submitVerified"
        @urge="$refs.promptContract.open()"
        @withdraw="$refs.withdrawContract.open()"
      />
      <div
        :style="{
          display: 'flex'
        }"
      >
        <div
          class="webkit-scrollbar"
          :style="{
            flex: '0 0 240px',
            height: 'calc(100vh - 48px)',
            overflowY: 'auto',
            padding: '12px 24px',
            borderRight: '1px solid #EEF0F4',
            fontSize: '12px'
          }"
        >
          <el-collapse :value="['waitingSignatueFiles']">
            <el-collapse-item name="waitingSignatueFiles">
              <template slot="title">
                <Title
                  :title="`待签署文件 (${contractSignData.fileList.length}份)`"
                />
              </template>
              <WaitingSignatueFiles
                :mode="FilePagesModeContractSign"
                :pageFields="pageFields"
                :files="contractSignData.fileList"
                :fields="fields"
                :currentFileIndex="fileIndex"
                @select="selectFile"
                :showAmount="false"
                :showPagesNumber="true"
              />
            </el-collapse-item>
          </el-collapse>
          <Title :title="`附件 (${attachmentCount}份)`" />
          <AttachmentTips style="margin-top: 14px" />
          <el-collapse :value="['sender', 'signer']">
            <el-collapse-item
              name="sender"
              v-if="
                contractSignData && contractSignData.attachmentList.length > 0
              "
            >
              <template slot="title">
                <Title :title="`发起方附件`" />
              </template>
              <AttachmentFiles
                :readOnly="true"
                @preview="previewAttachmentFile"
                @download="downloadAttachmentFile"
                v-model="contractSignData.attachmentList"
              />
            </el-collapse-item>
            <el-collapse-item name="signer">
              <template slot="title">
                <Title :title="`签署方附件`" />
              </template>
              <AttachmentFiles
                @remove="removeAttachmentFile"
                @preview="previewAttachmentFile"
                @download="downloadAttachmentFile"
                @saveAttachment="saveAttachment"
                :creator="contractSignData.creator"
                v-model="contractSignData.signerAttachmentList"
              />
            </el-collapse-item>
          </el-collapse>
        </div>
        <div
          :style="{
            flex: '1 1 auto',
            width: '0px', //直接自动计算
            height: 'calc(100vh - 48px)',
            background: '#f2f2f2',
            overflow: 'hidden'
          }"
        >
          <div
            style="
              position: stricky;
              top: 46px;
              overflow: hidden;
              width: 100%;
              z-index: 1;
              background-color: #fffaf0;
              color: #e59900;
              font-size: 12px;
              padding: 14px 16px;
              text-align: center;
            "
            v-if="!tipClosed"
          >
            <i class="el-icon-info" />
            指定区域已自动加载默认签章。
            <i
              class="el-icon-close"
              style="cursor: pointer;margin-left;10px"
              @click="closeTip"
            />
          </div>
          <FilePages
            ref="filePages"
            :fileId="contractSignData.fileList[fileIndex].fileId"
            :images="contractSignData.fileList[fileIndex].archiveImageList"
          >
            <template v-slot="{ pageNo, fileId }">
              <FilePageFieldSign
                :key="index"
                v-for="(pageField, index) in pageFields.filter(
                  item => item.pageNo === pageNo && item.fileId === fileId
                )"
                :field="fields.find(item => item.id === pageField.fieldId)"
                :pageField="pageField"
                :focusPageFiled="currentPageField"
                :signers="signers"
              />
            </template>
          </FilePages>
        </div>
        <div
          id="rightBox"
          class="webkit-scrollbar"
          :style="{
            flex: '0 0 240px',
            height: 'calc(100vh - 48px)',
            overflowY: 'auto',
            padding: '12px 24px',
            borderLeft: '1px solid #EEF0F4',
            fontSize: '12px'
          }"
        >
          <el-tabs v-model="activeName">
            <el-tab-pane label="签章" name="inputs">
              <FilePageFieldSealSetting
                @select="selectSeal"
                :signatureSeal="contractSignData.signingSealResponse"
                :currentSealId="currentSealId"
              />
            </el-tab-pane>
            <el-tab-pane label="合同签署信息" name="infos" class="rightInfos">
              <ContractInfos :infos="contractSignData" />
              <el-collapse :value="['signatureProcesses', 'carbonCopies']">
                <el-collapse-item name="signatureProcesses">
                  <template slot="title">
                    <Title title="签署流程" />
                  </template>
                  <template>
                    <SignatureProcesses
                      :writeProcesses="contractSignData.writeProcessList"
                      :signProcesses="contractSignData.signProcessList"
                    />
                  </template>
                </el-collapse-item>
                <el-collapse-item
                  name="carbonCopies"
                  v-if="
                    contractSignData.carbonCopyList &&
                    contractSignData.carbonCopyList.length
                  "
                >
                  <template slot="title">
                    <Title title="抄送方" />
                  </template>
                  <template>
                    <CarbonCopies
                      :carbonCopies="contractSignData.carbonCopyList"
                    />
                  </template>
                </el-collapse-item>
              </el-collapse>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <div
      :style="{
        display: 'none'
      }"
    >
      <img
        @load="fileImageLoad($event, file.fileId)"
        :src="file.archiveImageList[0]"
        :key="file.fileId"
        v-for="file of contractSignData.fileList"
      />
    </div>
    <PromptContract
      ref="promptContract"
      :rows="[
        {
          name: contractSignData.name,
          handlingBy: handler,
          id: contractSignData.id,
          status: contractSignData.status
        }
      ]"
    />
    <WithdrawContract
      ref="withdrawContract"
      :rows="[
        {
          name: contractSignData.name,
          id: contractSignData.id,
          status: contractSignData.status
        }
      ]"
      @reload="() => $router.push('/signings')"
    />
    <MoblieSMSValiadation
      ref="moblieSMSValiadation"
      @submit="submitMoblieSMSValiadation"
    />
    <VerifiedDialog ref="verifiedDialog" />
  </div>
</template>

<script>
import ContractWriteTopBar from '../../components/contract/signing/contractWriteTopBar.vue'
import Title from '../../components/contract/title.vue'
import AttachmentFiles from '../../components/contract/signingDraft/attachmentFiles.vue'
import AttachmentTips from '../../components/contract/signingDraft/attachmentTips.vue'
import ContractInfos from '../../components/contract/contract/infos.vue'
import SignatureProcesses from '../../components/contract/signing/contract/signatureProcesses.vue'
import CarbonCopies from '../../components/contract/signingDraft/carbonCopies.vue'
import WaitingSignatueFiles from '../../components/contract/signing/contract/waitingSignatueFiles.vue'
import FilePageFieldSealSetting from '../../components/contract/template/filePageFieldSealSetting.vue'
import formatPageFieldsPx from '../../formatters/contract/template/formatPageFieldsPx'
import makeDetail2PageFieldsFromFileList from '../../formatters/contract/template/makeDetail2PageFieldsFromFileList'
import makeDetail2FieldsFromFileList from '../../formatters/contract/template/makeDetail2FieldsFromFileList'
import makePlatformClient from '../../services/platform/makeClient'
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import { FilePagesModeContractSign } from './constants'
import PromptContract from './signings/promptContractDialog.vue'
import WithdrawContract from './signings/withdrawContractDialog.vue'
import store from '../../helpers/store'
import {
  FieldTypeCompany,
  FieldTypePerson,
  SignStatusSigning
} from '../../services/contract/constants'
import MoblieSMSValiadation from './signings/moblieSMSValiadationDialog.vue'
import FilePages from '../../components/contract/file/pages.vue'
import FilePageFieldSign from '../../components/contract/contract/filePageFieldSign.vue'
import VerifiedDialog from './signings/verifiedDialog.vue'
import { user } from '../../helpers/profile'
2
const client = makeContractClient()
const pclient = makePlatformClient()

export default {
  components: {
    ContractWriteTopBar,
    Title,
    FilePages,
    FilePageFieldSign,
    WaitingSignatueFiles,
    AttachmentFiles,
    AttachmentTips,
    ContractInfos,
    SignatureProcesses,
    CarbonCopies,
    FilePageFieldSealSetting,
    PromptContract,
    WithdrawContract,
    MoblieSMSValiadation,
    VerifiedDialog
  },
  async created() {
    const id = this.$route.params.id
    if (!id) {
      throw new Error('id is required')
    }

    this.tipClosed = store.get('__signingContractSign')

    const loading = this.$loading({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255,255, 0.7)'
    })

    // this.contractSignData = fakeData.data
    const [err, r] = await client.signingSignPreview({
      body: {
        id: id
      }
    })
    if (err) {
      handleError(err)
      loading.close()
      if (err.errorCode === 501) {
        setTimeout(() => {
          this.$router.push('/signings')
        }, 3000)
      }
      if (err.message.includes('没有') && err.message.includes('权限')) {
        setTimeout(() => {
          this.$router.push('/signings')
        }, 3000)
      }
      return
    }

    this.contractSignData = r.data
    console.log('contractSignData', this.contractSignData)
    loading.close()
    if (!this.contractSignData.signingSealResponse) {
      this.$message({
        message: '暂无手写签名，请前往小程序设置',
        type: 'warning',
        offset: 150
      })
    }

    this.fields = makeDetail2FieldsFromFileList(this.contractSignData.fileList)
    this.pageFields = makeDetail2PageFieldsFromFileList(
      this.contractSignData.fileList
    )

    this.currentSealId =
      this.contractSignData.signingSealResponse?.defaultSealId
    if (!this.currentSealId) {
      const list = this.contractSignData.signingSealResponse?.sealList
      if (list && list.length) {
        this.currentSealId = list[0].id
      }
    }

    const defaultSeal =
      this.contractSignData.signingSealResponse?.sealList.find(
        item => item.id === this.currentSealId
      )
    for (var c of this.fields) {
      if (c.signStatus === SignStatusSigning) {
        if (c.type === FieldTypePerson || c.type === FieldTypeCompany) {
          c.value = defaultSeal?.url
        }
      }
    }
  },
  mounted() {
    document.body.style.margin = 0
    document.body.style.padding = 0
    document.body.style.overflow = 'hidden'
    // setTimeout(() => {
    //   if (user.isAuth) {
    //     this.$refs.verifiedDialog.open()
    //   }
    // }, 1000)
  },
  methods: {
    closeTip() {
      this.tipClosed = true
      store.set('__signingContractSign', true)
    },
    back() {
      const params = new URLSearchParams(location.search)
      const back = params.get('back')
      if (back && back.includes('http')) {
        location.href = back
        return
      }
      if (back && !back.includes('http')) {
        this.$router.push(back)
        return
      }
      this.$router.push('/signings')
    },
    submitVerified() {
      if (!user.isAuth) {
        this.$refs.verifiedDialog.open()
        return
      }
      this.$refs.moblieSMSValiadation.open()
    },
    async submit(smsCode, smsToken) {
      var req = {
        idList: [this.contractSignData.id],
        smsCode,
        smsToken,
        sealList: []
      }

      req.sealList.push({
        signerType: this.contractSignData.signingSealResponse?.owner.signerType,
        legalId: this.contractSignData.signingSealResponse?.owner.legal
          ? this.contractSignData.signingSealResponse?.owner.legal.id
          : '',
        userId: this.contractSignData.signingSealResponse?.owner?.signer
          ? this.contractSignData.signingSealResponse?.owner?.signer.id
          : '',
        sealId: this.currentSealId
      })
      this.$refs.moblieSMSValiadation.loading = true
      const [err, _] = await client.signingSign({
        body: req
      })
      if (err) {
        handleError(err)
        this.$refs.moblieSMSValiadation.loading = false
        return
      }
      this.$router.push(`/signings`)
      handleSuccess('签署成功')
      this.$refs.moblieSMSValiadation.loading = false
      this.$refs.moblieSMSValiadation.close()
    },
    pageFieldFocus(pageField) {
      this.currentPageField = pageField
      this.activeName = 'inputs'
      //滚动到指定位置
      const fieldElId = `field${pageField.fieldId}`
      this.$nextTick(() => {
        const fieldEl = document.getElementById(fieldElId)
        const rightBoxEl = document.getElementById('rightBox')
        // const rightBoxElTop = rightBoxEl.scrollTop
        const offsetHeight = fieldEl.offsetTop
        rightBoxEl.scroll({
          top: offsetHeight + 60,
          behavior: 'smooth'
        })
      })
    },
    selectSeal(seal) {
      this.currentSealId = seal.id

      for (var c of this.fields) {
        if (c.signStatus === SignStatusSigning) {
          if (c.type === FieldTypePerson || c.type === FieldTypeCompany) {
            c.value = seal?.url
          }
        }
      }
    },
    selectFile(i) {
      this.fileIndex = i
      document.getElementById('pagesBox').scroll({
        top: 0,
        behavior: 'smooth'
      })
    },
    //imageIndex 暂未启用 默认同一文件中图片大小一致
    fileImageLoad(e, fileId) {
      var width = e.target.width
      var height = e.target.height
      this.fileImageSizes[fileId] = [width, height]
      // debugger
      formatPageFieldsPx(this.pageFields, this.fileImageSizes)
    },
    submitMoblieSMSValiadation(smsCode, smsToken) {
      this.submit(smsCode, smsToken)
    },
    removeAttachmentFile(file) {
      this.contractSignData.signerAttachmentList =
        this.contractSignData.signerAttachmentList.filter(
          attachment => attachment.archiveId !== file.archiveId
        )
      this.saveAttachment()
      console.log(file, 'filefilefile')
    },
    // 下载文件
    async downloadAttachmentFile(file, index) {
      const id = file.archiveId
      const name = file.name
      const [err, r] = await pclient.platformDownloadFile(
        {
          method: 'GET',
          headers: { 'content-type': 'application/octet-stream' }
        },
        { id, name }
      )
      if (err) {
        console.log(err, 'errrrrrr')
        return
      }
      window.open(r.url)
    },
    // 预览文件
    async previewAttachmentFile(file, index) {
      const id = file.archiveId
      const [err, r] = await client.fileInfo({
        body: {
          id
        }
      })
      if (err) {
        handleError(err)
        return
      }
      const url = r.data.url
      window.open(url)
    },
    // 保存签署方附件
    async saveAttachment() {
      const [err, _] = await client.signingUpdateSignAttachment({
        body: {
          contractId: this.contractSignData.id,
          attachmentList: this.contractSignData.signerAttachmentList
        }
      })
      if (err) {
        return handleError(err)
      }
    }
  },
  computed: {
    handler() {
      const owner = this.contractSignData.signingSealResponse?.owner
      if (owner?.legal) {
        return owner.legal
      }
      return owner?.signer
    },
    signers() {
      var r = []
      for (var c of this.contractSignData.signProcessList) {
        r.push(c?.signer)
      }

      return r
    },
    attachmentCount() {
      const { contractSignData } = this
      return (
        (contractSignData &&
          contractSignData.attachmentList &&
          contractSignData.attachmentList.length) +
        (contractSignData &&
          contractSignData.signerAttachmentList &&
          contractSignData.signerAttachmentList.length)
      )
    }
  },
  data() {
    return {
      FilePagesModeContractSign,
      fileIndex: 0,
      currentPageField: null,
      activeName: 'inputs',
      fields: [],
      pageFields: [],
      contractSignData: null,
      tipClosed: false,
      //用于计算后续的比例
      fileImageSizes: {},
      currentSealId: ''
    }
  }
}
</script>

<style scoped></style>