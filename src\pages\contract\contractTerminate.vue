<template>
  <RightLayout>
    <TopBar>
      <Breadcrumb title="解约" />
    </TopBar>
    <MiddleBox style="padding: 0 0 60px 35px">
      <Title title="待解约合同信息" style="margin-top: 20px" />
      <BasicInfo :contractInfo="contractInfo" />
      <el-form
        ref="terminateRef"
        style="width: 580px; padding-bottom: 40px"
        :model="terminate"
        :rules="rules"
      >
        <Title title="解约原因" style="margin: 20px 0 10px 0" />
        <div :style="{ marginLeft: '12px' }">
          <el-form-item prop="reason">
            <template #label>
              <span>解约原因</span>
            </template>
            <el-select style="width: 100%" v-model="terminate.reason">
              <el-option label="条款内容有误" value="条款内容有误"> </el-option>
              <el-option label="协商一致提前终止" value="协商一致提前终止">
              </el-option>
              <el-option label="其他" value="其他"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              maxlength="1000"
              show-word-limit
              v-model.trim="terminate.remark"
              type="textarea"
              rows="6"
            >
            </el-input>
          </el-form-item>
        </div>
        <Title title="解约协议" style="margin: 20px 0 10px 0" />
        <div :style="{ marginLeft: '12px' }">
          <el-form-item prop="changeWay" label="解约协议类型">
            <el-radio-group v-model="terminate.changeWay">
              <el-radio :label="1" style="font-weight: 400"
                >发起电子解约协议</el-radio
              >
              <el-radio :label="2" style="font-weight: 400"
                >签署纸质协议</el-radio
              >
              <el-radio :label="3" style="font-weight: 400"
                >不签署协议</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="templateId" v-if="terminate.changeWay == 1">
            <template #label> 选择合同模板 </template>
            <br />
            <div>
              <el-tag
                v-if="template && template.name"
                closable
                style="margin-right: 10px"
                @close="
                  () => {
                    template = {}
                    terminate.templateId = undefined
                  }
                "
              >
                {{ template.name }}
              </el-tag>
              <el-button plain @click="$refs.templateRef.open()"
                >选择模板</el-button
              >
            </div>
          </el-form-item>
          <el-form-item prop="terminateDate" label="提前终止日期">
            <el-date-picker
              v-model="terminate.terminateDate"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
        </div>
      </el-form>
      <TemplateDialog ref="templateRef" @submit="getTemplateInfo" />
    </MiddleBox>
    <BottomBar>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit">{{ submitText }}</el-button>
    </BottomBar>
  </RightLayout>
</template>
<script>
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import Breadcrumb from '../../components/contract/breadcrumb.vue'
import RightLayout from '../../components/contract/rightLayout.vue'
import TopBar from '../../components/contract/topBar.vue'
import MiddleBox from '../../components/contract/middleBox.vue'
import BottomBar from '../../components/contract/bottomBar.vue'
import Title from '../../components/contract/title.vue'
import TemplateDialog from './contracts/templateDialog.vue'
import BasicInfo from '../../components/contract/contract/basicInfo.vue'
const client = makeContractClient()
export default {
  components: {
    Breadcrumb,
    RightLayout,
    TopBar,
    MiddleBox,
    BottomBar,
    Title,
    TemplateDialog,
    BasicInfo
  },
  async mounted() {
    const id = this.$route.params.id
    this.terminate.contractId = id
    const [err, r] = await client.contractQuery({
      body: {
        limit: 20,
        start: 1,
        withDeleted: true,
        withDisabled: true,
        withTotal: true,
        filters: {
          contractIdList: [id]
        }
      }
    })

    if (err) {
      handleError(err)
      return
    }
    this.contractInfo = r.data.list[0]
  },

  data() {
    return {
      // 用于存储展示合同信息
      contractInfo: {},
      // 弹窗中选择的模板信息
      template: {},
      // 解约参数信息
      terminate: {
        contractId: 0,
        reason: '',
        remark: '',
        changeWay: 1,
        templateId: undefined,
        terminateDate: ''
      },
      rules: {
        reason: [{ required: true, message: '请选择解约原因' }],
        changeWay: [{ required: true, message: '请选择解约协议类型' }],
        terminateDate: [{ required: true, message: '请选择提前终止日期' }],
        templateId: [{ required: true, message: '请选择合同模板' }]
      },
      pickerOptions: {
        disabledDate: time => {
          if (this.contractInfo.endTime) {
            const terminateDate = this.contractInfo.endTime
            return time.getTime() > new Date(terminateDate).getTime()
          } else {
            return false
          }
        }
      }
    }
  },
  methods: {
    // 弹窗点击确定后触发
    getTemplateInfo(template) {
      this.template = template
      this.terminate.templateId = this.template.id
      this.$refs.terminateRef.validateField('templateId')
    },
    async submit() {
      // 校验合同是否为生效中或未生效状态，不满足时，toast错误提示：“已终止合同不能进行解除”

      // 校验合同是否为未解约状态，不满足时，toast错误提示：“合同不能重复发起解约
      this.$refs.terminateRef.validate(async valid => {
        if (valid) {
          const [err, r] = await client.contractTerminate({
            body: this.terminate
          })
          if (err) {
            handleError(err)
            return
          }
          //如果this.terminate.changeWay == 1 则跳转  否则改变状态直接完事
          // 进入使用模板发起签署页面
          if (this.terminate.changeWay === 1) {
            this.$router.push(
              `/signings/drafts/${r.data.id}/step1/edit?source=CONTRACT_TERMINATE&templateId=${this.terminate.templateId}`
            )
          } else {
            handleSuccess('解约成功')
            this.back()
          }
        } else {
          this.scrollIntoError(this.$refs.terminateRef)
        }
      })
    },
    back() {
      const params = new URLSearchParams(location.search)
      const back = params.get('back')

      if (back && !back.includes('http')) {
        this.$router.push(back)
        return
      }

      this.$router.push('/contracts')
    },
    cancel() {
      this.$router.go(-1)
    }
  },
  computed: {
    submitText() {
      return this.terminate.changeWay == 1 ? '下一步' : '保存'
    }
  }
}
</script>
<style scoped>
::v-deep .el-radio-group {
  width: 580px;
}
::v-deep .el-date-editor {
  width: 100%;
}
</style>