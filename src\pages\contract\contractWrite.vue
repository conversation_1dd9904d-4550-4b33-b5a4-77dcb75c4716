<template>
  <div
    class="templatesNewStep2"
    v-if="contractWriteData"
    @mousedown="pageFieldBlur"
  >
    <div>
      <ContractWriteTopBar
        :detailInfo="contractWriteData"
        @back="back"
        @submit="submit"
        @urge="$refs.promptContract.open()"
        @withdraw="$refs.withdrawContract.open()"
      />
      <div
        :style="{
          display: 'flex'
        }"
      >
        <div
          class="webkit-scrollbar"
          :style="{
            flex: '0 0 240px',
            height: 'calc(100vh - 48px)',
            overflowY: 'auto',
            padding: '12px 24px',
            borderRight: '1px solid #EEF0F4',
            fontSize: '12px'
          }"
        >
          <el-collapse :value="['waitingSignatueFiles', 'attachmentList']">
            <el-collapse-item name="waitingSignatueFiles">
              <template slot="title">
                <Title
                  :title="`待签署文件（${contractWriteData.fileList.length}）`"
                />
              </template>
              <WaitingSignatueFiles
                :mode="FilePagesModeContractWrite"
                :pageFields="pageFields"
                :files="contractWriteData.fileList"
                :fields="fields"
                :currentFileIndex="fileIndex"
                @select="selectFile"
              />
            </el-collapse-item>
            <el-collapse-item name="attachmentList">
              <template slot="title">
                <Title
                  :withPrefix="false"
                  :title="`附件 (${contractWriteData.attachmentList.length}份)`"
                />
              </template>
              <Title :title="`发起方附件`" />
              <AttachmentFiles
                :readOnly="true"
                @preview="previewAttachmentFile"
                @download="downloadAttachmentFile"
                v-model="contractWriteData.attachmentList"
              />
            </el-collapse-item>
          </el-collapse>
        </div>
        <div
          :style="{
            flex: '1 1 auto',
            width: '0px', //直接自动计算
            height: 'calc(100vh - 48px)',
            background: '#f2f2f2',
            overflow: 'hidden'
          }"
        >
          <FilePages
            ref="filePages"
            :fileId="contractWriteData.fileList[fileIndex].fileId"
            :images="contractWriteData.fileList[fileIndex].archiveImageList"
          >
            <template v-slot="{ pageNo, fileId }">
              <FilePageFieldWrite
                :key="index"
                v-for="(pageField, index) in pageFields.filter(
                  item => item.pageNo === pageNo && item.fileId === fileId
                )"
                :field="fields.find(item => item.id === pageField.fieldId)"
                :pageField="pageField"
                :focusPageFiled="currentPageField"
                @focus="pageFieldFocus"
              />
            </template>
          </FilePages>
          <div
            style="
              width: 40px;
              height: 40px;
              background: #5072ff;
              border-radius: 8px;
              text-align: center;
              position: sticky;
              bottom: 60px;
              right: 19px;
              float: right;
              cursor: pointer;
            "
            v-if="noValueFieldsCount"
            @click="locateNoValueField"
          >
            <el-tooltip
              placement="top"
              :content="`共有${noValueFieldsCount}个必填项没有填写，点击按钮快速定位`"
            >
              <el-badge :value="noValueFieldsCount">
                <LocationIcon
                  style="
                    color: #fff;
                    width: 30px;
                    height: 30px;
                    position: relative;
                    top: 5px;
                  "
                />
              </el-badge>
            </el-tooltip>
          </div>
        </div>
        <div
          id="rightBox"
          class="webkit-scrollbar"
          :style="{
            flex: '0 0 240px',
            height: 'calc(100vh - 48px)',
            overflowY: 'auto',
            padding: '12px 24px',
            borderLeft: '1px solid #EEF0F4',
            fontSize: '12px'
          }"
        >
          <el-tabs v-model="activeName">
            <el-tab-pane label="填写内容" name="inputs">
              <span style="color: #b2b6c2; padding: 12px 0 24px 0">
                请完成合同信息的填写，提交后不可更改。
              </span>
              <FilePageFieldInputs
                :fields="sortedFields"
                :pageFields="pageFields"
                :currentPageField="currentPageField"
                @fieldFocus="fieldInputFocus"
                :showErrors="showErrors"
                @change="isEdit = true"
              />
            </el-tab-pane>
            <el-tab-pane label="合同签署信息" name="infos" class="rightInfos">
              <ContractInfos :infos="contractWriteData" />
              <el-collapse :value="['signatureProcesses', 'carbonCopies']">
                <el-collapse-item name="signatureProcesses">
                  <template slot="title">
                    <Title title="签署流程" />
                  </template>
                  <template>
                    <SignatureProcesses
                      :writeProcesses="contractWriteData.writeProcessList"
                      :signProcesses="contractWriteData.signProcessList"
                    />
                  </template>
                </el-collapse-item>
                <el-collapse-item
                  name="carbonCopies"
                  v-if="
                    contractWriteData.carbonCopyList &&
                    contractWriteData.carbonCopyList.length
                  "
                >
                  <template slot="title">
                    <Title title="抄送方" />
                  </template>
                  <template>
                    <CarbonCopies
                      :carbonCopies="contractWriteData.carbonCopyList"
                    />
                  </template>
                </el-collapse-item>
              </el-collapse>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <div
      :style="{
        display: 'none'
      }"
    >
      <img
        @load="fileImageLoad($event, file.fileId)"
        :src="file.archiveImageList[0]"
        :key="file.fileId"
        v-for="file of contractWriteData.fileList"
      />
    </div>
    <PromptContract
      ref="promptContract"
      :rows="[
        {
          name: contractWriteData.name,
          handlingBy: handler,
          id: contractWriteData.id,
          status: contractWriteData.status
        }
      ]"
    />
    <WithdrawContract
      ref="withdrawContract"
      :rows="[
        {
          name: contractWriteData.name,
          id: contractWriteData.id,
          status: contractWriteData.status
        }
      ]"
      @reload="() => $router.push('/signings')"
    />
  </div>
</template>

<script>
import ContractWriteTopBar from '../../components/contract/signing/contractWriteTopBar.vue'
import Title from '../../components/contract/title.vue'
import AttachmentFiles from '../../components/contract/signingDraft/attachmentFiles.vue'
import ContractInfos from '../../components/contract/contract/infos.vue'
import SignatureProcesses from '../../components/contract/signing/contract/signatureProcesses.vue'
import CarbonCopies from '../../components/contract/signingDraft/carbonCopies.vue'
import WaitingSignatueFiles from '../../components/contract/signing/contract/waitingSignatueFiles.vue'
import FilePageFieldInputs from '../../components/contract/template/filePageFieldInputs.vue'
import makeDetail2PageFieldsFromFileList from '../../formatters/contract/template/makeDetail2PageFieldsFromFileList'
import makeDetail2FieldsFromFileList from '../../formatters/contract/template/makeDetail2FieldsFromFileList'
import formatPageFieldsPx from '../../formatters/contract/template/formatPageFieldsPx'
import parentHadClass from './templatesNewStep2/parentHadClass'
import makeContractWriteFieldList from '../../formatters/contract/template/makeContractWriteFieldList'
import makePlatformClient from '../../services/platform/makeClient'
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import store from '../../helpers/store'
import { FilePagesModeContractWrite } from './constants'
import PromptContract from './signings/promptContractDialog.vue'
import WithdrawContract from './signings/withdrawContractDialog.vue'
import { ContractWriteProcessStatusWaitingWrite } from '../../services/contract/constants'
import FilePages from '../../components/contract/file/pages.vue'
import FilePageFieldWrite from '../../components/contract/contract/filePageFieldWrite.vue'
import LocationIcon from '../../components/contract/template/locationIcon.vue'
import isSignatureFieldType from '../../components/contract/template/isSignatureFieldType'
import sortFields from '../../components/contract/template/sortFields'

const client = makeContractClient()
const pclient = makePlatformClient()
const validateFields = fields => {
  var errors = []
  for (var c of fields) {
    if (!c.writeable) {
      continue
    }
    if (!c.value.trim() && c.writeRequired) {
      errors.push({
        name: c.name,
        message: `${c.name}是必填的`
      })
    }
  }
  return errors
}
export default {
  components: {
    ContractWriteTopBar,
    Title,
    FilePages,
    FilePageFieldWrite,
    WaitingSignatueFiles,
    AttachmentFiles,
    ContractInfos,
    SignatureProcesses,
    CarbonCopies,
    FilePageFieldInputs,
    PromptContract,
    WithdrawContract,
    LocationIcon
  },
  async created() {
    const id = this.$route.params.id
    if (!id) {
      throw new Error('id is required')
    }

    const loading = this.$loading({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255,255, 0.7)'
    })

    // this.contractWriteData = fakeData.data
    const [err, r] = await client.signingGetContractWriteData({
      body: {
        id: id
      }
    })
    if (err) {
      handleError(err)
      loading.close()
      if (err.errorCode === 501) {
        setTimeout(() => {
          this.$router.push('/signings')
        }, 3000)
      }
      if (err.message.includes('没有') && err.message.includes('权限')) {
        setTimeout(() => {
          this.$router.push('/signings')
        }, 3000)
      }
      return
    }
    this.contractWriteData = r.data

    console.log('contractWriteData', this.contractWriteData)
    this.fields = makeDetail2FieldsFromFileList(this.contractWriteData.fileList)
    this.pageFields = makeDetail2PageFieldsFromFileList(
      this.contractWriteData.fileList
    )

    loading.close()
  },
  mounted() {
    document.body.style.margin = 0
    document.body.style.padding = 0
    document.body.style.overflow = 'hidden'
  },
  methods: {
    locateNoValueField() {
      const firstField = this.fields.find(field => {
        if (
          !field.value &&
          field.writeRequired &&
          !isSignatureFieldType(field.type)
        ) {
          return true
        }

        return false
      })
      const firstPageField = this.pageFields.find(
        item => item.fieldId === firstField.id
      )
      const fileIndex = this.contractWriteData.fileList.findIndex(
        item => item.fileId === firstPageField.fileId
      )
      if (fileIndex !== this.fileIndex) {
        this.selectFile(fileIndex)
      }

      this.$refs.filePages.scrollToFirstPageField(firstPageField)
      this.pageFieldFocus(firstPageField)
    },
    back() {
      if (!this.isEdit) {
        this._back()
        return
      }
      this.$confirm('已填写内容不会被保存，确定要返回退出吗？', '返回提示', {
        cancelButtonText: '取消',
        confirmButtonText: '继续返回',
        type: 'info',
        closeOnClickModal: false
      })
        .then(() => {
          this._back()
        })
        .catch(() => {})
    },
    _back() {
      const params = new URLSearchParams(location.search)
      const back = params.get('back')
      if (back && back.includes('http')) {
        location.href = back
        return
      }
      if (back && !back.includes('http')) {
        this.$router.push(back)
        return
      }
      this.$router.push('/signings')
    },
    async submit() {
      const errors = validateFields(this.fields)
      if (errors.length) {
        this.showErrors = true
        var htmls = []
        for (var c of errors) {
          htmls.push(c.message)
        }
        const mesagesHtml = htmls.join('<br/>')
        const html = `<div class="webkit-scrollbar" style="max-height:calc(100vh - 200px);overflow:auto">${mesagesHtml}</div>`
        this.$confirm(html, '部分字段未填写', {
          type: 'error',
          dangerouslyUseHTMLString: true,
          confirmButtonText: '我知道了',
          showCancelButton: false,
          closeOnClickModal: false
        })
        return
      }
      this.showErrors = false
      var req = {
        contractId: this.contractWriteData.id,
        fieldList: []
      }
      req.fieldList = makeContractWriteFieldList(
        this.fields.filter(item => item.writeable)
      )
      //存即提交
      const [err, _] = await client.signingSaveContractWriteableData({
        body: req
      })
      if (err) {
        handleError(err)
        return
      }
      this.$router.push(`/signings`)
      handleSuccess('提交填写成功')
    },
    pageFieldFocus(pageField) {
      this.currentPageField = pageField
      this.activeName = 'inputs'
      //滚动到指定位置
      const fieldElId = `field${pageField.fieldId}`
      this.$nextTick(() => {
        const fieldEl = document.getElementById(fieldElId)
        const rightBoxEl = document.getElementById('rightBox')
        // const rightBoxElTop = rightBoxEl.scrollTop
        const offsetHeight = fieldEl.offsetTop
        rightBoxEl.scroll({
          top: offsetHeight + 60,
          behavior: 'smooth'
        })
      })
    },
    pageFieldBlur(e) {
      if (
        parentHadClass(e.target, 'rightInfos') ||
        parentHadClass(e.target, '-select-item')
      ) {
        return
      }
      this.currentPageField = null
      this.activeName = 'inputs'
    },
    fieldInputFocus(field) {
      if (!field) {
        return
      }
      const pageField = this.pageFields.find(item => item.fieldId === field.id)
      if (pageField) {
        const index = this.contractWriteData.fileList.findIndex(
          item => item.fileId === pageField.fileId
        )
        if (index !== this.fileIndex) {
          this.selectFile(index)
          this.$nextTick(() => {
            this.$refs.filePages.scrollToFirstPageField(pageField)
          })
        } else {
          this.$refs.filePages.scrollToFirstPageField(pageField)
        }
        this.pageFieldFocus(pageField)
      }
    },
    selectFile(i) {
      this.fileIndex = i
      document.getElementById('pagesBox').scroll({
        top: 0,
        behavior: 'smooth'
      })
    },
    //imageIndex 暂未启用 默认同一文件中图片大小一致
    fileImageLoad(e, fileId) {
      var width = e.target.width
      var height = e.target.height
      this.fileImageSizes[fileId] = [width, height]
      // debugger
      formatPageFieldsPx(this.pageFields, this.fileImageSizes)
    },
    // 下载文件
    async downloadAttachmentFile(file, index) {
      const id = file.archiveId
      const name = file.name
      const [err, r] = await pclient.platformDownloadFile(
        {
          method: 'GET',
          headers: { 'content-type': 'application/octet-stream' }
        },
        { id, name }
      )
      if (err) {
        console.log(err, 'errrrrrr')
        return
      }
      window.open(r.url)
    },
    // 预览文件
    async previewAttachmentFile(file, index) {
      const id = file.archiveId
      const [err, r] = await client.fileInfo({
        body: {
          id
        }
      })
      if (err) {
        handleError(err)
        return
      }
      const url = r.data.url
      window.open(url)
    }
  },
  computed: {
    handler() {
      const writer = this.contractWriteData.writeProcessList.find(
        item => item.status === ContractWriteProcessStatusWaitingWrite
      )

      return writer.signer.signer
    },
    noValueFieldsCount() {
      var count = 0
      for (const pageField of this.pageFields) {
        const field = this.sortedFields.find(
          item => item.id === pageField.fieldId
        )
        if (!field) {
          continue
        }
        if (!field.value && !isSignatureFieldType(field.type)) {
          count++
        }
      }
      return count
    },
    sortedFields() {
      var fields = this.fields.filter(item => item.writeable)
      sortFields(fields, this.pageFields, this.contractWriteData.fileList)
      return fields
    }
  },
  data() {
    return {
      FilePagesModeContractWrite,
      fileIndex: 0,
      currentPageField: null,
      activeName: 'inputs',
      fields: [],
      pageFields: [],
      contractWriteData: null,
      //用于计算后续的比例
      fileImageSizes: {},
      showErrors: false,
      isEdit: false
    }
  }
}
</script>

<style scoped></style>