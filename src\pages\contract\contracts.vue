<template>
  <RightLayout>
    <TopBar>
      <div
        :style="{
          display: 'flex'
        }"
      >
        <el-tabs v-model="activeName" style="width: 100%">
          <el-tab-pane name="">
            <div slot="label" @click="processTypeChange()">全部合同</div>
          </el-tab-pane>
          <el-tab-pane name="CLOSING_SOON">
            <div slot="label" @click="processTypeChange('CLOSING_SOON')">
              即将到期 {{ closingSoonTotal }}
            </div>
          </el-tab-pane>
          <el-tab-pane name="WAITE">
            <div slot="label" @click="processTypeChange('WAITE')">未生效</div>
          </el-tab-pane>
          <el-tab-pane name="IN_PROCESS">
            <div slot="label" @click="processTypeChange('IN_PROCESS')">
              生效中
            </div>
          </el-tab-pane>
          <el-tab-pane name="TERMINATION">
            <div slot="label" @click="processTypeChange('TERMINATION')">
              已终止
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </TopBar>
    <div style="display: flex">
      <LeftTree
        @selectAll="selectAll"
        @selectGroup="selectGroup"
        @selectType="selectType"
        style="
          flex: 0 0 230px;
          padding-top: 16px;
          border-right: 1px solid #eef0f4;
          font-size: 14px;
        "
      />
      <o-container style="padding-top: 16px; width: calc(100% - 200px)">
        <template slot="header"><div></div> </template>

        <!-- 筛选区域 -->
        <o-top-select
          ref="top-select"
          :formJson="formJson"
          :immediate="true"
          class="def-m-b-10 o-app"
          labelWidth="86px"
          @search="onSearch"
          @reset="resetForm"
        />

        <!-- 表格区域 -->
        <o-table
          ref="o-table"
          :sticky="true"
          :actionButtons="actionButtons"
          :pagination="{ fixed: true }"
          :tableHeader="tableHeader"
          :selection="true"
          @selection-change="handleSelectionChange"
          :showPagination="true"
          :requestFn="load"
          emptyHeight="calc(100vh - 450px)"
          :tableHeaderActionButtons="tableHeaderActionButtons"
        />
        <TemplateDialog @submit="turnDraftStep1" ref="templateDialog" />
      </o-container>
    </div>
  </RightLayout>
</template>

<script>
import TopBar from '../../components/contract/topBar.vue'
import RightLayout from '../../components/contract/rightLayout.vue'
import LeftTree from './templates/types.vue'
import TemplateDialog from './contracts/templateDialog.vue'
import SignProcessList from '../../components/contract/signing/signProcessList.vue'
import CarbonCopyEmpList from '../../components/contract/signing/carbonCopyEmpList.vue'
import ContactName from '../../components/contract/contract/contactName.vue'
import formatDateTime from '../../formatters/dateTime'
import ProcessStatusWithDot from '../../components/contract/contract/processStatusWithDot.vue'
import makePlatformClient from '../../services/platform/makeClient'
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'

import {
  verifyTerminate,
  verifyRenewal,
  verifyModify
} from './contracts/verify'
import { hadPrivilege } from '../../helpers/profile'
import { constractDownloadStatusSuccess } from '../../services/contract/constants'
const pclient = makePlatformClient()
const client = makeContractClient()

export default {
  components: {
    RightLayout,
    TopBar,
    LeftTree,
    TemplateDialog,
    Date
  },
  async mounted() {
    this.$refs['top-select'].setOptions({
      processStatus: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未生效',
          value: '2'
        },
        {
          label: '生效中',
          value: '3'
        },
        {
          label: '到期终止',
          value: '4'
        },
        {
          label: '提前终止',
          value: '5'
        }
      ]
    })
    // 发起方企业信息
    const [err2, r2] = await pclient.platformListLegal({
      body: {
        withTotal: true,
        filters: {
          legalUsageType: ['CONTRACT']
        }
      }
    })
    const legals = r2.data.list.map(legal => ({
      label: legal.name,
      value: legal.id
    }))
    this.$refs['top-select'].setOptions({
      createLegalId: legals
    })
    // 计算合同页面即将到期数量
    this.loadContractCount()

    // 根据url的值 切换tab展示数据
    const { processType } = this.$route.query
    if (processType) {
      this.processTypeChange(processType)
      this.activeName = processType
    }
    // 删除businessui的分割线 与ui设计不符合
    document.getElementsByClassName('o-container-header')[0].remove()
    document.getElementsByTagName('hr')[0].remove()
  },
  methods: {
    async onSearch() {
      const processType = this.processType
      const contractTypeIdList = this.contractTypeIdList
      const fData = await this.$refs['top-select'].getFormData()
      await this.$refs['o-table'].appendRequestParams({
        ...fData,
        processType,
        contractTypeIdList
      })
    },
    // tab切换事件
    processTypeChange(type) {
      // 1-尚未签署；2-未生效(未到开始时间)；3-生效中(未到结束时间)；4-已到期；5-已解约
      // 切换tab 隐藏合同状态字段
      if (type === 'IN_PROCESS' || type === 'WAITE') {
        this.$refs['top-select'].setFormJson(
          this.formJson.filter(field => field.formItem.prop !== 'processStatus')
        )
      } else {
        this.$refs['top-select'].setFormJson(this.formJson)
      }
      if (!type || type === 'CLOSING_SOON') {
        this.$refs['top-select'].setOptions({
          processStatus: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '未生效',
              value: '2'
            },
            {
              label: '生效中',
              value: '3'
            },
            {
              label: '到期终止',
              value: '4'
            },
            {
              label: '提前终止',
              value: '5'
            }
          ]
        })
      }
      if (type === 'TERMINATION') {
        this.$refs['top-select'].setOptions({
          processStatus: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '到期终止',
              value: '4'
            },
            {
              label: '提前终止',
              value: '5'
            }
          ]
        })
      }
      // 重置表单
      this.$refs['top-select'].handleResetBtnClick()
      this.processType = type
      this.onSearch()
    },
    async load(params) {
      const [err, r] = await client.contractQuery({
        body: params
      })

      if (err) {
        handleError(err)
        return
      }

      return r.data
    },
    async selectAll() {
      this.contractTypeIdList = []
      this.onSearch()
    },
    selectGroup(groupId, nodes) {
      const node = nodes[0].children.filter(node => node.id === groupId)
      this.contractTypeIdList = node[0].children.map(n => n.id)
      this.onSearch()
    },
    selectType(typeId) {
      this.contractTypeIdList = [typeId]
      this.onSearch()
    },
    // 表格批量勾选
    handleSelectionChange(rows) {
      this.rows = rows
    },
    turnDraftStep1(templateId) {
      this.$router.push(
        `/signings/drafts/${this.currentContractId}/step1/edit?source=${this.sourceType}&templateId=${templateId.id}`
      )
    },

    // 批量导出
    async exportContract() {
      const processType = this.processType
      const contractTypeIdList = this.contractTypeIdList
      const fData = await this.$refs['top-select'].getFormData()

      // 勾选的合同
      let contractIdList = []
      if (this.rows.length > 0) {
        contractIdList = this.rows.map(row => row.id)
      }

      const body = {
        filters: {
          ...fData,
          contractTypeIdList,
          processType,
          contractIdList: contractIdList
        }
      }
      const [err1, r1] = await client.contractCheckExport({ body })
      if (err1) {
        handleError(err1)
        return
      }

      this.$confirm(
        `<b>将导出${r1.data.total}个合同明细信息，确定要导出吗？`,
        '导出明细',
        {
          type: 'warning',
          dangerouslyUseHTMLString: true,
          closeOnClickModal: false
        }
      ).then(async () => {
        const [err, r] = await client.contractExport({ body })
        if (err) {
          handleError(err)
          return
        }

        const tmp = r.headers.get('Content-Disposition').split('filename=')
        const filename = decodeURIComponent(tmp[1])
        const blobData = await r.blob()
        const blob = new Blob([blobData])
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        link.remove()
      })
    },
    checkDownloadContractStatus(props) {
      // const signingContracts = this.rows.filter(
      //   row => row.status === ContractStatusSigning
      // )
      const signingContracts = this.rows
      // 全部不可下载
      if (signingContracts.length === 0) {
        this.$confirm(
          `<b>无可下载合同</b>` +
            `<br/>仅全部签署方都完成签署后，可以进行下载。`,
          '',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true,
            confirmButtonText: '我知道了',
            showCancelButton: false,
            closeOnClickModal: false
          }
        )
        return
      }
      // 全部可下载
      if (signingContracts.length === this.rows.length) {
        this.$confirm(
          `<b>您有${signingContracts.length}份合同可下载，确认要下载吗？` +
            '<br/>仅全部签署方都完成签署后，可以进行下载。',
          '下载',
          {
            dangerouslyUseHTMLString: true,
            type: 'warning',
            confirmButtonText: '下载',
            closeOnClickModal: false
          }
        ).then(async () => {
          this.downloadContract(signingContracts, props)
        })
        return
      }
      // 部分可下载
      this.$confirm(
        `<b>您有${signingContracts.length}份已签署合同可下载，确认要下载吗？` +
          `<br/>您选择的文件有${
            this.rows.length - signingContracts.length
          }份不可下载，请检查是否全部签署方都已完成签署。`,
        '下载',
        {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          confirmButtonText: '下载',
          closeOnClickModal: false
        }
      ).then(async () => {
        this.downloadContract(signingContracts, props)
      })
    },
    // 下载合同
    async downloadContract(rows, props = {}) {
      let idList = []
      // 批量文件下载
      if (rows.length > 0) {
        idList = rows.map(row => row.id)
      } else {
        // 单个文件下载
        idList = [rows.id]
      }
      if (idList.length <= 0) {
        return handleError({ message: '请选择要下载的合同' })
      }
      // 调用下载接口
      props.loading = true
      const [err, r] = await client.contractDownload({
        body: {
          idList
        }
      })
      if (err) {
        handleError(err)
        return
      }
      const downloadStatus = r.data.status
      // 下载任务状态：1-处理中（循环调用checkDownloadTask检测任务）；2-成功（直接使用archiveId下载文件）
      if (downloadStatus === constractDownloadStatusSuccess) {
        props.loading = false
        this._downloadContract({
          // name: `合同文件下载_${this.timeStr}_${idList.length}份.zip`,
          archiveId: r.data.archiveId
        })
      } else {
        this.checkDownloadTask(r.data.downloadTaskId, props)
      }
    },
    // 轮询合同是否下载成功
    checkDownloadTask(downloadTaskId, props) {
      const timer = setInterval(async () => {
        const [err, r] = await client.contractCheckDownloadTask({
          body: {
            id: downloadTaskId
          }
        })
        if (err) {
          handleError(err)
          return
        }
        if (r.data.status === constractDownloadStatusSuccess) {
          props.loading = false
          clearInterval(timer)
          this._downloadContract({
            name: `合同文件下载_${this.timeStr}_${downloadTaskId.length}份.zip`,
            archiveId: r.data.archiveId
          })
        }
      }, 1000)
    },
    // 下载文件
    async _downloadContract(file) {
      const id = file.archiveId

      const name = file.name
      const [err, r] = await pclient.platformDownloadFile(
        {
          method: 'GET',
          headers: { 'content-type': 'application/octet-stream' }
        },
        { id, name }
      )
      if (err) {
        console.log(err, 'errrrrrr')
        return
      }

      window.open(r.url)
    },
    // 表单人员搜索
    async remoteSearchEmployee(query) {
      const [err, r] = await pclient.platformListMerchantMember({
        body: {
          start: 0,
          limit: 100,
          filters: {
            keywords: query
          }
        }
      })
      if (err) {
        handleError(err)
        return
      }
      return r.data.list.map(item => {
        return {
          label: `${item.name} (${item.cellPhone})`,
          value: item.userId,
          key: item.id
        }
      })
    },
    resetForm() {
      this.contractTypeIdList = []
    },
    // 获取待我处理和待他人处理合同数量
    async loadContractCount() {
      const closingSoonList = await this.load({
        limit: 1,
        start: 1,
        withDeleted: true,
        withDisabled: true,
        withTotal: true,
        filters: {
          processType: 'CLOSING_SOON'
        }
      })

      this.closingSoonCount = closingSoonList.total
    }
  },
  data() {
    return {
      currentContractId: 0,
      contractTypeIdList: [],
      closingSoonCount: 0,
      rows: [],
      sourceType: '',
      processType: undefined, //合同TAB分类
      activeName: '',
      formJson: [
        {
          type: 'input',
          formItem: {
            prop: 'name',
            label: '合同名称',
            placeholder: '请输入合同名称'
          }
        },
        {
          type: 'remoteSearchSelect',
          formItem: {
            prop: 'signerUserId',
            label: '签署方',
            placeholder: '请输入姓名/手机号',
            remoteMethod: query => {
              return new Promise(resolve => {
                const data = this.remoteSearchEmployee(query)
                resolve(data)
              })
            }
          }
        },
        {
          type: 'remoteSearchSelect',
          formItem: {
            prop: 'createBy',
            label: '发起人',
            placeholder: '请输入姓名/手机号',
            remoteMethod: query => {
              return new Promise(resolve => {
                const data = this.remoteSearchEmployee(query)
                resolve(data)
              })
            }
          }
        },
        {
          type: 'select',
          formItem: {
            prop: 'createLegalId',
            label: '发起方企业',
            placeholder: '请选择',
            options: []
          }
        },
        {
          type: 'select',
          formItem: {
            prop: 'processStatus',
            label: '合同状态',
            placeholder: '请选择合同状态',
            options: []
          }
        },
        {
          type: 'datePicker',
          formItem: {
            prop: 'startDate',
            label: '合同开始日期',
            placeholder: '开始日期-结束日期',
            type: 'daterange',
            rangeSeparator: '-',
            startField: 'startDateBegin',
            endField: 'startDateEnd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          }
        },
        {
          type: 'datePicker',
          formItem: {
            prop: 'endDate',
            label: '合同结束日期',
            placeholder: '开始日期-结束日期',
            type: 'daterange',
            rangeSeparator: '-',
            startField: 'endDateBegin',
            endField: 'endDateEnd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          }
        },
        {
          type: 'datePicker',
          formItem: {
            prop: 'signCompleteDate',
            label: '签署日期',
            placeholder: '开始日期-结束日期',
            type: 'daterange',
            rangeSeparator: '-',
            startField: 'signCompleteDateBegin',
            endField: 'signCompleteDateEnd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          }
        },
        {
          type: 'select',
          formItem: {
            prop: 'renewalStatus',
            label: '是否续约',
            placeholder: '请选择',
            options: [
              {
                label: '全部',
                value: ''
              },
              {
                label: '未续约',
                value: '1'
              },
              {
                label: '续约中',
                value: '2'
              },
              {
                label: '已续约',
                value: '3'
              }
            ]
          }
        },
        {
          type: 'select',
          formItem: {
            prop: 'changeStatus',
            label: '是否变更',
            placeholder: '请选择',
            options: [
              {
                label: '全部',
                value: ''
              },
              {
                label: '未变更',
                value: '1'
              },
              {
                label: '变更中',
                value: '2'
              },
              {
                label: '已变更',
                value: '3'
              }
            ]
          }
        },
        {
          type: 'input',
          formItem: {
            prop: 'closingSoonDays',
            valueType: 'int',
            maxlength: 10,
            trim: true,
            label: '距离到期 (天)',
            placeholder: '请输入距离到期天数'
          }
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '下载',
          ifShow: () => hadPrivilege('contract2.contractManagement.download'),
          props: {
            type: 'plain',
            loading: false
          },
          style: {
            marginRight: '-6px'
          },
          click: ({ props }) => {
            if (this.rows.length > 0) {
              this.checkDownloadContractStatus(props)
            } else {
              handleError({ message: '请选择至少一条签署完成的合同' })
            }
          }
        },
        {
          align: 'left',
          type: 'button',
          ifShow: () => hadPrivilege('contract2.contractManagement.export'),
          label: '导出明细',
          style: {
            marginRight: '16px'
          },
          props: {
            type: 'plain'
          },
          click: () => this.exportContract()
        },
        {
          align: 'right',
          label: '刷新',
          style: {
            marginRight: '16px'
          },
          props: {
            type: 'plain'
          },
          icon: 'el-icon-refresh',
          click: () => {
            this.onSearch()
            this.loadContractCount()
          }
        }
      ],
      tableHeader: [
        {
          prop: 'name',
          label: '合同名称',
          width: 200,
          render: (h, row) => {
            return h(ContactName, {
              props: { row }
            })
          }
        },
        {
          label: '合同类型',
          prop: 'contractType',
          width: '160px'
        },
        {
          label: '签署方',
          prop: 'signerList',
          width: '250px',
          showOverflowTooltip: false,
          render: (h, row) => {
            return h(SignProcessList, {
              props: {
                value: {
                  signerList: row.signerList
                }
              }
            })
          }
        },
        {
          label: '合同状态',
          width: '140px',
          prop: 'processStatus',
          render: (h, row) => {
            return h(ProcessStatusWithDot, {
              props: {
                value: row.processStatus,
                renewalStatus: row.renewalStatus,
                terminateStatus: row.terminateStatus,
                changeStatus: row.changeStatus
              }
            })
          }
        },
        {
          label: '合同开始日期',
          prop: 'start_time',
          width: '110px',
          formatter: row => `
            <p>
              ${
                row.startTime
                  ? formatDateTime('yyyy-MM-dd', row.startTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '合同到期日期',
          prop: 'end_time',
          width: '110px',
          formatter: row => `
            <p>
              ${row.endTime ? formatDateTime('yyyy-MM-dd', row.endTime) : '-'}
            </p>
          `,
          sort: true
        },
        {
          label: '签署日期',
          prop: 'sign_finish_time',
          width: '110px',
          formatter: row => `
            <p>
              ${
                row.signFinishTime
                  ? formatDateTime('yyyy-MM-dd', row.signFinishTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '终止日期',
          prop: 'terminate_time',
          width: '110px',
          formatter: row => `
            <p>
              ${
                row.terminateTime
                  ? formatDateTime('yyyy-MM-dd', row.terminateTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '距离到期(天)',
          prop: 'closing_soon_days',
          formatter: row => `
            <p>
              ${row.closingSoonDays ? row.closingSoonDays : '-'}
            </p>
          `,
          sort: true
        },
        {
          label: '最后更新时间',
          prop: 'update_time',
          width: '140px',
          formatter: row => `
            <p>
              ${
                row.updateTime
                  ? formatDateTime('yyyy-MM-dd HH:mm', row.updateTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '抄送方',
          prop: 'carbonCopyList',
          render: (h, row) => {
            return h(CarbonCopyEmpList, {
              props: {
                value: row.carbonCopyList
              }
            })
          }
        },
        {
          label: '被证明人',
          prop: 'certifier',
          formatter: row => `
            <p>
              ${row.certifier && row.certifier.name ? row.certifier.name : '-'}
            </p>
          `
        },
        {
          label: '合同编号',
          sort: true,
          prop: 'no',
          width: '180px'
        }
      ],
      actionButtons: [
        {
          label: '续签',
          id: '1',
          ifShow: row => {
            return verifyRenewal(
              row.processStatus,
              row.terminateStatus,
              row.renewalStatus,
              row.certifier
            )
          },
          click: row => {
            this.currentContractId = row.id
            this.sourceType = 'CONTRACT_RENEWAL'
            this.$refs.templateDialog.open()
          }
        },
        {
          label: '解约',
          id: '2',
          ifShow: row => {
            return verifyTerminate(
              row.processStatus,
              row.terminateStatus,
              row.certifier
            )
          },
          click: row => this.$router.push(`/contracts/${row.id}/terminate`)
        },
        {
          label: '变更',
          id: '3',
          ifShow: row => {
            return verifyModify(row.processStatus, row.terminateStatus)
          },
          click: row => {
            this.currentContractId = row.id
            this.sourceType = 'CONTRACT_MODIFY'
            this.$refs.templateDialog.open()
          }
        },
        {
          label: '下载',
          id: '4',
          ifShow: () => hadPrivilege('contract2.contractManagement.download'), //有下载权限显示
          click: row => this.downloadContract(row)
        },
        {
          label: '查看',
          id: '5',
          ifShow: () => hadPrivilege('contract2.contractManagement.query'), //有查看权限显示
          click: row =>
            this.$router.push({
              path: `/contracts/${row.id}`
            })
        }
      ]
    }
  },
  computed: {
    timeStr() {
      const date = new Date()
      const year = date.getFullYear()
      const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate()
      const month =
        date.getMonth() < 10 ? `0${date.getMonth()}` : date.getMonth()
      const hours =
        date.getHours() < 10 ? `0${date.getHours()}` : date.getHours()
      const minutes =
        date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes()
      const seconds =
        date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds()
      return `${year}${month}${day}${hours}${minutes}${seconds}`
    },
    closingSoonTotal() {
      if (this.closingSoonCount) {
        return this.closingSoonCount > 99
          ? `(99+)`
          : `(${this.closingSoonCount})`
      }
      return ''
    }
  }
}
</script>

<style scoped>
::v-deep .table-header-button span {
  flex: 1;
}
::v-deep .o-field {
  margin: 0;
}
::v-deep .o-top-select .form-render .o-field {
  margin-right: 20px;
}
::v-deep .o-top-select .form-render .row .open {
  padding: 0;
}
</style>