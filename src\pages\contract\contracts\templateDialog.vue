<template>
  <el-dialog
    :visible.sync="visible"
    width="840px"
    title="选择模板"
    :close-on-click-modal="false"
  >
    <div
      :style="{
        height: '60px'
      }"
    >
      <el-form
        :inline="true"
        ref="searchForm"
        style="display: flex; justify-content: space-between"
        @submit.native.prevent
      >
        <el-form-item label="模板名称">
          <el-input
            v-model="conditions.name"
            placeholder="请输入模板名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="合同类型">
          <el-select clearable v-model="conditions.contractTypeId">
            <el-option
              v-for="type in types"
              :key="type.id"
              :label="type.label"
              :value="type.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          style="display: flex; justify-content: space-between; margin: 0"
        >
          <!-- <el-button type="primary" plain @click="reload()"> 查询 </el-button> -->
          <el-button @click="load()" type="primary"> 查询 </el-button>
          <el-button
            @click="
              () => {
                conditions.name = ''
                conditions.contractTypeId = null
                load()
              }
            "
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <TenmlateList
      v-if="templateTotal > 0"
      v-model="selectTemplate"
      :templates="templates"
      :templateTotal="templateTotal"
      @pageChange="(start, limit) => pageChange(start, limit)"
    />
    <el-empty v-else :image-size="200"></el-empty>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="close()">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import makeContractClient from '../../../services/contract/makeClient'
import TenmlateList from '../../../components/contract/contract/templateList.vue'
const client = makeContractClient()
export default {
  name: 'templateDialog',
  components: {
    TenmlateList
  },
  data() {
    return {
      visible: false,
      conditions: { contractTypeId: null, name: '' },
      templates: [],
      selectTemplate: null,
      templateTotal: 0,
      start: 1,
      limit: 5,
      types: []
    }
  },
  async mounted() {
    this.load()
    // 请求合同类型列表
    const [err1, r1] = await client.contractTypeGetTypeDict({})
    const types = []
    if (err1) {
      handleError(err1)
      return
    }
    for (let key in r1.data) {
      types.push({ label: r1.data[key], value: key })
    }
    this.types = types
  },
  methods: {
    pageChange(start, limit) {
      this.start = start
      this.limit = limit
      this.load()
    },
    async load() {
      const { start, limit } = this
      const [err, r] = await client.templateQuery({
        body: {
          limit,
          start,
          withDeleted: true,
          withDisabled: true,
          withTotal: true,
          filters: { ...this.conditions }
        }
      })

      this.templates = r.data.list || []
      this.templateTotal = r.data.total
    },
    submit() {
      this.$emit('submit', this.selectTemplate)
      this.close()
    },
    open() {
      this.visible = true
    },
    close() {
      this.visible = false
      this.conditions = {}
      this.load()
    }
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped></style>