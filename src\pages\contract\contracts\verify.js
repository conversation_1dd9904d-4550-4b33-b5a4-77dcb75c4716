import {
  ContractStatusFilling,
  ContractStatusReviewing,
  ContractStatusSigning,
  ContractStatusCompleted,
  ContractStatusOverdue,
  ContractStatusRejected,
  ContractStatusWithdrew,
  ContractProcessStatusEffective,
  ContractProcessStatusIneffective,
  ContractStatusNoTerminate,
  ContractProcessStatusHaveExpired,
  ContractStatusNoRenewal
} from '../../../services/contract/constants'
import { hadPrivilege } from '../../../helpers/profile'
// 校验当前合同能否查看
export const verifyView = (status, canSeeDetail) => {
  return (
    status && (canSeeDetail || hadPrivilege('contract2.signTask.query')) // 查看权限
  )
}

// 校验当前合同能否填写
export const verifyWrite = (status, userId, handlingById) => {
  return status === ContractStatusFilling && handlingById == userId
}

// 校验当前合同能否审核
export const verifyAduit = (status, userId, handlingById) => {
  return (
    status === ContractStatusReviewing &&
    (handlingById == userId || hadPrivilege('contract2.signTask.examine'))
  )
}

// 校验当前合同能否签署
export const verifySign = (status, userId, handlingById) => {
  return status === ContractStatusSigning && handlingById == userId
}

// 校验当前合同能否催办
export const verifyUrge = (status, userId, creatorId) => {
  return (
    (status === ContractStatusReviewing ||
      status === ContractStatusFilling ||
      status === ContractStatusSigning) &&
    (creatorId == userId || hadPrivilege('contract2.signTask.urge'))
  ) // 有催办权限
}

// 校验当前合同能否撤回
export const verifyWithdraw = (status, userId, creatorId) => {
  return (
    (status === ContractStatusReviewing ||
      status === ContractStatusFilling ||
      status === ContractStatusSigning) &&
    (creatorId == userId || hadPrivilege('contract2.signTask.recall'))
  )
}

// 校验当前合同能否编辑
export const verifyEdit = (status, userId, creatorId) => {
  return !status && creatorId == userId
}

// 校验当前合同能否下载
export const verifyDownload = (
  status,
  userId,
  writeProcessList,
  signProcessList
) => {
  return (
    status === ContractStatusCompleted &&
    // 当前用户为合同流程节点的参与人
    ([...writeProcessList, ...signProcessList].filter(
      sign => sign.signer.signer.id == userId
    ).length > 0 ||
      hadPrivilege('contract2.signTask.download'))
  )
}

// 校验当前合同能否删除
export const verifyRemove = (status, userId, creatorId) => {
  return !status && creatorId == userId
}

// 校验当前合同能否重新发起
export const verifyReissue = (status, userId, creatorId) => {
  return (
    (status === ContractStatusOverdue ||
      status === ContractStatusRejected ||
      status === ContractStatusWithdrew) &&
    (creatorId == userId || hadPrivilege('contract2.signTask.initiate'))
  )
}

// 校验当前合同是否能解约
export const verifyTerminate = (
  processStatus,
  terminateStatus,
  isCertifier
) => {
  return (
    (processStatus === ContractProcessStatusEffective ||
      processStatus === ContractProcessStatusIneffective) &&
    terminateStatus === ContractStatusNoTerminate &&
    !isCertifier &&
    hadPrivilege('contract2.contractManagement.terminate')
  )
}

// 校验当前合同是否能续签
export const verifyRenewal = (
  processStatus,
  terminateStatus,
  renewalStatus,
  isCertifier
) => {
  return (
    (processStatus === ContractProcessStatusEffective ||
      processStatus === ContractProcessStatusHaveExpired) &&
    terminateStatus === ContractStatusNoTerminate &&
    renewalStatus === ContractStatusNoRenewal &&
    !isCertifier &&
    hadPrivilege('contract2.contractManagement.renew')
  )
}

// 校验当前合同是否能变更
export const verifyModify = (processStatus, terminateStatus) => {
  return (
    (processStatus === ContractProcessStatusEffective ||
      processStatus === ContractProcessStatusIneffective) &&
    terminateStatus === ContractStatusNoTerminate &&
    hadPrivilege('contract2.contractManagement.change')
  )
}

// 合同详情能否下载
export const verifyContractDonwload = (processStatus, userId, creatorId) => {
  return (
    processStatus === ContractStatusCompleted &&
    (creatorId == userId || hadPrivilege('contract2.signTask.download'))
  )
}