<template>
  <div
    class="contractIndex"
    style="background: #f5f5f5; min-height: calc(100vh - 84px); font-size: 14px"
    v-if="!loading"
  >
    <AuthTip style="flex: 1 1 100%" />
    <div style="display: flex">
      <div style="margin-right: 10px; flex: 1 1 auto; min-width: 770px">
        <BoxWithTitle style="margin-bottom: 10px">
          <Welcome />
        </BoxWithTitle>

        <BoxWithTitle
          title="待我处理"
          style="margin-bottom: 10px"
          @more="more('HANDLE_BY_ME')"
        >
          <WaitingSignatureTasks
            :statistics="statistics.handleByMe"
            @statusChanged="status => (handleByMeStatus = status)"
          />
        </BoxWithTitle>
        <BoxWithTitle
          title="我发起的"
          style="margin-bottom: 10px"
          @more="more('CREATE_BY_ME')"
        >
          <CreatedSignatureTasks
            :statistics="statistics.submitByMe"
            :draftCount="statistics.draftCount"
            @draftStatusChanged="cisDraft => (isDraft = cisDraft)"
            @statusChanged="status => (submitByMeStatus = status)"
            @urge="urge"
          />
        </BoxWithTitle>
      </div>
      <div
        :style="{
          flex: '0 0 354px'
        }"
      >
        <BoxWithTitle title="发起签署" style="margin-bottom: 10px">
          <div style="display: flex">
            <div
              style="
                width: 148px;
                border-radius: 8px;
                text-align: center;
                padding: 12px 0;
                cursor: pointer;
              "
              @click="$router.push('/templates')"
              class="startSigning"
            >
              <i class="el-icon-document-copy" style="font-size: 18px" />
              <br />
              使用模板发起
              <br />
              <span style="color: #a8acba; font-size: 12px">
                通过模板发起签署
              </span>
            </div>
            <div
              class="startSigning"
              style="
                width: 148px;
                border-radius: 8px;
                text-align: center;
                padding: 12px 0;
                cursor: pointer;
              "
              @click="$router.push('/signings?group=DRAFT')"
            >
              <i class="el-icon-edit-outline" style="font-size: 18px" />
              <br />
              草稿发起
              <br />
              <span style="color: #a8acba; font-size: 12px">
                我的草稿 ({{ statistics.draftCount }})
              </span>
            </div>
          </div>
        </BoxWithTitle>

        <BoxWithTitle
          v-if="hadPrivilege('contract2.signTask.query')"
          title="全部签署任务"
          style="margin-bottom: 10px"
        >
          <Statistics :statistics="statistics" />
        </BoxWithTitle>

        <ExpiringContracts />

        <BoxWithTitle>
          <div
            :style="{
              position: 'absolute'
            }"
          ></div>
          <div style="font-size: 12px; display: flex; align-items: center">
            <img
              width="160"
              height="148"
              src="../../assets/images/qrcode.png"
            />
            <div style="margin-left: 20px">
              <span style="font-size: 16px; font-weight: 500">
                扫码进入小程序
              </span>
              <br />
              <span style="color: #46485a">
                移动端签约，实名认证，及时签署，安全方便
              </span>
            </div>
          </div>
        </BoxWithTitle>
      </div>
    </div>
    <PromptContract :rows="urgeRows" ref="promptContractRef" />
  </div>
</template>
<script>
import BoxWithTitle from '../../components/contract/boxWithTitle.vue'
import Welcome from './index/welcome.vue'
import WaitingSignatureTasks from './index/waitingSignatureTasks.vue'
import CreatedSignatureTasks from './index/createdSignatureTasks.vue'
import ExpiringContracts from './index/expiringContracts.vue'
import AuthTip from './index/authTip.vue'
import Statistics from '../../components/contract/index/statistics.vue'
import PromptContract from './signings/promptContractDialog.vue'

import handleError from '../../helpers/handleError'
import makeContractClient from '../../services/contract/makeClient'
import { ContractStatusSigning } from '../../services/contract/constants'
import { hadPrivilege } from '../../helpers/profile'

const client = makeContractClient()

export default {
  components: {
    BoxWithTitle,
    Welcome,
    WaitingSignatureTasks,
    CreatedSignatureTasks,
    ExpiringContracts,
    Statistics,
    AuthTip,
    PromptContract
  },
  async created() {
    const loading = this.$loading({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255,255, 0.7)'
    })

    const [err, r] = await client.workbenchStatis()
    if (err) {
      handleError(err)
      return
    }

    this.statistics = { ...this.statistics, ...r.data }

    loading.close()

    this.loading = false
  },
  methods: {
    urge(contract) {
      this.$refs.promptContractRef.open()
      this.urgeRows = [contract]
    },
    more(type) {
      var urlParams = new URLSearchParams()
      urlParams.set('group', type)
      if (type === 'CREATE_BY_ME') {
        urlParams.set('contractStatus', this.submitByMeStatus)
      }
      if (type === 'HANDLE_BY_ME') {
        urlParams.set('contractStatus', this.handleByMeStatus)
      }
      if (this.isDraft) {
        urlParams.set('group', 'DRAFT')
        urlParams.delete('contractStatus')
      }

      this.$router.push('/signings?' + urlParams.toString())
    },
    hadPrivilege
  },
  data() {
    return {
      urgeRows: [],
      loading: true,
      isDraft: false,
      handleByMeStatus: ContractStatusSigning,
      submitByMeStatus: ContractStatusSigning,
      statistics: {
        handleByMe: {
          waitSignCount: 0,
          waitWriteCount: 0,
          waitApprovalCount: 0,
          closingSoonCount: 0
        },
        submitByMe: {
          waitSignCount: 0,
          waitWriteCount: 0,
          waitApprovalCount: 0,
          closingSoonCount: 0
        },
        approvingCount: 0,
        writingCount: 0,
        signingCount: 0,
        finishCount: 0,
        draftCount: 0
      }
    }
  }
}
</script>
<style scoped>
.startSigning:hover {
  background: #f7fafd;
}
</style>