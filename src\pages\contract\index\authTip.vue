<template>
  <div class="authTip" v-if="isNeedShow">
    <el-alert class="tip-alert" v-if="!isPersonAuth">
      <template #title>
        <div class="tip">
          <i class="el-icon-message-solid tip-icon"></i>您的账号
          <span @click="goAuth">未认证</span>，请进行认证，以方便您完成更多操作
        </div>
      </template>
    </el-alert>

    <el-alert class="tip-alert" v-else-if="auditStatus == 'NOT_IDENTIFIED'">
      <template #title>
        <div class="tip">
          <i class="el-icon-message-solid tip-icon"></i>您尚未完成企业认证
          ，请先进行
          <span @click="goAuth">企业信息认证</span
          >，认证成功后可以开通使用更多功能
        </div>
      </template>
    </el-alert>

    <el-alert class="tip-alert" v-else-if="auditStatus == 'WAIT'">
      <template #title>
        <div class="tip">
          <i class="el-icon-message-solid tip-icon"></i>
          工作人员正在审核您的认证信息，请耐心等待
        </div>
      </template>
    </el-alert>

    <el-alert class="tip-alert" v-else-if="auditStatus == 'NOT_PASS'">
      <template #title>
        <div class="tip">
          <i class="el-icon-message-solid tip-icon"></i>
          您的企业信息认证没有通过审核，请
          <span @click="goAuth">重新认证</span>
        </div>
      </template>
    </el-alert>

    <el-alert class="tip-alert" v-else-if="configStatus === 'CONFIG'">
      <template #title>
        <div class="tip">
          <i class="el-icon-message-solid tip-icon"></i
          >工作人员正在为您进行系统初始化配置，请耐心等待
        </div>
      </template>
    </el-alert>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import { merchantMember, merchant, user } from '../../../helpers/profile'
import makePlatformClient from '../../../services/platform/makeClient'

const pclient = makePlatformClient()
export default {
  computed: {
    isAdmin() {
      return merchantMember.isAdmin === true
    },
    isNeedShow() {
      if (!this.userMerchantProfile || !this.userMerchantProfile.type) {
        return false
      }

      return this.userMerchantProfile.type === 'ENTERPRISE' && this.isAdmin
    },
    auditStatus() {
      return this.userMerchantProfile.auditStatus || ''
    },
    isPersonAuth() {
      return user.isAuth === true
    }
  },
  async created() {
    const [err, r] = await pclient.oladingUserMerchantProfile({
      body: {}
    })
    if (err) {
      handleError(err)
      return
    }

    this.userMerchantProfile = r.data

    const [err2, r2] = await pclient.merchantNetGetMerchantInit({
      body: {
        merchantId: merchant.id
      }
    })
    if (err2) {
      handleError(err2)
      return
    }
    if (r2.data && r2.data.vo && r2.data.configureStatus) {
      this.configStatus = r2.data.vo.configureStatus
    }

    this.loading = false
  },
  data() {
    return {
      loading: true,
      userMerchantProfile: {},
      configStatus: ''
    }
  },
  methods: {
    goAuth() {
      if (!this.isPersonAuth) {
        window.location.href = `${window.env.ssoURL}/personalAuthentication`
        return
      }
      window.location.href = `${window.env.ssoURL}/companyAuthentication`
    }
  }
}
</script>

<style scoped>
.tip-alert {
  height: 35px;
  background-color: #fff;
  margin-bottom: 16px;
}
.tip-alert .tip {
  color: #46485a;
  font-size: 13px;
  font-weight: 400;
}
.tip-alert .tip span {
  color: #feab05;
  cursor: pointer;
}
.tip-alert .tip-icon {
  color: #feab05;
  font-size: 16px;
  margin-right: 10px;
}
</style>