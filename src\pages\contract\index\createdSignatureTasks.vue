<template>
  <Tabs :steps="steps" @select="select">
    <div style="min-height: 153px">
      <template v-if="isDraftTab">
        <div
          :key="signingDraft.id"
          v-for="signingDraft in signingDrafts"
          style="
            display: flex;
            height: 50px;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            border-bottom: 1px solid #eef0f4;
          "
        >
          <span style="flex: 1 1 auto; font-weight: 500">
            {{ signingDraft.name }}
          </span>
          <div style="flex: 0 0 100px">
            <el-button
              @click="
                $router.push(
                  `/signings/drafts/${signingDraft.id}/step1/edit?source=DRAFT`
                )
              "
              plain
            >
              编辑
            </el-button>
          </div>
        </div>
      </template>
      <template v-if="!isDraftTab">
        <SigningLite
          :key="signing.id"
          v-for="signing in signings"
          :signing="signing"
          :showHandler="true"
          :buttonText="buttonText(signing)"
          @buttonClick="buttonClick(signing)"
        />
      </template>
      <template v-if="showEmpty">
        <el-empty title="暂无数据" :image-size="80" />
      </template>
    </div>
  </Tabs>
</template>
<script>
import Tabs from '../../../components/contract/index/tabs.vue'
import SigningLite from '../../../components/contract/index/signingLite.vue'
import handleError from  '../../../helpers/handleError'
import makeContractClient from '../../../services/contract/makeClient'
import {
  ContractStatusReviewing,
  ContractStatusFilling,
  ContractStatusSigning,
  ContractStatusDeadline
} from '../../../services/contract/constants'

const contractStatuses = [
  ContractStatusSigning,
  ContractStatusFilling,
  ContractStatusReviewing,
  ContractStatusDeadline
]
const client = makeContractClient()
export default {
  components: {
    Tabs,
    SigningLite
  },
  created() {
    this.load(ContractStatusSigning)
  },
  computed: {
    showEmpty() {
      if (
        this.isDraftTab &&
        (!this.signingDrafts || !this.signingDrafts.length)
      ) {
        return true
      }
      if (!this.isDraftTab && (!this.signings || !this.signings.length)) {
        return true
      }

      return false
    },
    steps() {
      var r = []
      r.push(`待签署 (${this.cstatistics.waitSignCount})`)
      r.push(`待填写 (${this.cstatistics.waitWriteCount})`)
      r.push(`待审核 (${this.cstatistics.waitApproveCount})`)
      r.push(`即将截止 (${this.cstatistics.closingSoonCount})`)
      r.push(`草稿 (${this.cdraftCount})`)
      return r
    }
  },
  methods: {
    select(index) {
      //最后一个是草稿
      if (this.steps.length - 1 === index) {
        this.isDraftTab = true
        this.$emit('draftStatusChanged', true)
        this.loadDrafts()
        return
      }
      this.$emit('draftStatusChanged', false)
      this.isDraftTab = false

      const status = contractStatuses[index]
      this.load(status)
      this.$emit('statusChanged', status)
      this.contractStatus = status
    },
    async load(status) {
      const [err, r] = await client.workbenchQueryContract({
        body: {
          handleByMe: false,
          submitByMe: true,
          status: status
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.total = r.data.total || 0
      this.signings = r.data.list || []

      const index = contractStatuses.findIndex(c => c === status)
      const keys = Object.keys(this.cstatistics)
      //需要覆盖，之前的可能发生了变化
      this.cstatistics[keys[index]] = this.total
    },
    async loadDrafts() {
      const [err, r] = await client.workbenchMySigningDraft()
      if (err) {
        handleError(err)
        return
      }
      r.data = r.data || {}
      this.cdraftCount = r.data.total || 0
      this.signingDrafts = r.data.list || []
    },
    buttonText(signing) {
      if (
        signing.handler &&
        signing.creator.signer.id !== signing.handler.signer.id
      ) {
        return '催办'
      }

      switch (this.contractStatus) {
        case ContractStatusSigning:
          return '签署'
        case ContractStatusFilling:
          return '填写'
        case ContractStatusReviewing:
          return '审核'
        case ContractStatusDeadline:
          return '查看'

        default:
          break
      }
    },
    buttonClick(signing) {
      const back = '/'
      if (
        signing.handler &&
        signing.creator.signer.id !== signing.handler.signer.id
      ) {
        this.$emit('urge', {
          name: signing.name,
          status: this.contractStatus,
          id: signing.id,
          handlingBy: { name: signing?.handler?.signer?.name }
        })
        return
        // return this.$router.push(
        //   `/signings?group=HANDLE_BY_ME&contractStatus=1&action=prompt&contractId=${signing.id}`
        // )
      }
      switch (this.contractStatus) {
        case ContractStatusSigning:
          return this.$router.push(`/contracts/${signing.id}/sign?back=${back}`)
        case ContractStatusFilling:
          return this.$router.push(
            `/contracts/${signing.id}/write?back=${back}`
          )
        case ContractStatusReviewing:
          return '审核'
        case ContractStatusDeadline:
          return this.$router.push(`/contracts/${signing.id}?back=${back}`)

        default:
          break
      }
    }
  },
  props: {
    draftCount: Number,
    statistics: {
      type: Object,
      default() {
        return {
          waitSignCount: 0,
          waitWriteCount: 0,
          waitApprovalCount: 0,
          closingSoonCount: 0
        }
      }
    }
  },
  data() {
    return {
      total: 0,
      isDraftTab: false,
      contractStatus: ContractStatusSigning,
      signings: [],
      signingDrafts: [],
      cstatistics: this.statistics,
      cdraftCount: this.draftCount
    }
  }
}
</script>