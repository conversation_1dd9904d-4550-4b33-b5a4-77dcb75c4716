<template>
  <BoxWithTitle
    :title="`即将到期 <span style='font-weight:300'>(${total})</span>`"
    style="margin-bottom: 10px"
    :hideMore="total ? false : true"
    @more="expiringMore"
  >
    <div
      v-if="!total"
      style="color: #a8acba; text-align: center; padding: 30px 0"
    >
      <img
        src="../../../assets/images/icon/<EMAIL>"
        height="48px"
        width="48px"
      />
      <br />
      暂无即将到期合同
    </div>
    <div v-else class="expiringContracts">
      <div
        style="
          display: flex;
          font-size: 12px;
          color: #24262a;
          align-items: center;
          cursor: pointer;
        "
        :style="{
          paddingTop: index !== 0 ? '15px' : '',
          paddingBottom: index !== contracts.length - 1 ? '15px' : '',
          borderBottom:
            index !== contracts.length - 1 ? '1px solid #EEF0F4' : 'none'
        }"
        :key="contract.id"
        v-for="(contract, index) in contracts"
        @click="$router.push(`/contracts/${contract.id}`)"
      >
        <div style="flex: 1 1 auto">
          <b style="font-size: 14px">{{ contract.name }}</b>
          <div style="color: #777c94">
            合同结束日期：{{ contract.endTime | formatDateTime('yyyy-MM-dd') }}
            <span style="color: red">
              ({{ contract.closingSoonDays | closingSoonDaysString }})
            </span>
            <br />
            签署方：{{ contract.signerList | signerListString }}
          </div>
        </div>
        <i class="el-icon-arrow-right" />
      </div>
    </div>
  </BoxWithTitle>
</template>
<script>
import BoxWithTitle from '../../../components/contract/boxWithTitle.vue'
import handleError from '../../../helpers/handleError'
import makeContractClient from '../../../services/contract/makeClient'
import {
  SingerTypePerson,
  SingerTypeCompany
} from '../../../services/contract/constants'
const client = makeContractClient()
export default {
  components: {
    BoxWithTitle
  },
  data() {
    return {
      total: 0,
      contracts: []
    }
  },
  methods: {
    expiringMore() {
      this.$router.push('/contracts?processType=CLOSING_SOON')
    }
  },
  async created() {
    const [err, r] = await client.workbenchQueryClosingSoon()
    if (err) {
      handleError(err)
      return
    }
    var i = 0
    for (var item of r.data.list) {
      if (i > 3) {
        break
      }
      i++
      this.contracts.push(item)
    }
    this.total = r.data.total
  },
  filters: {
    closingSoonDaysString(days) {
      if (days === 0) {
        return '今日到期'
      }
      return `${days}天后到期`
    },
    signerListString(signerList) {
      var r = []
      for (var c of signerList) {
        const { signer, legal, signerType } = c
        if (signerType === SingerTypePerson) {
          r.push(`${signer.name}`)
        }
        if (signerType === SingerTypeCompany) {
          r.push(`${legal.name} (${signer.name})`)
        }
      }

      return r.join(' | ')
    }
  }
}
</script>