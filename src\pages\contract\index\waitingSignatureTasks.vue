<template>
  <Tabs :steps="steps" @select="select">
    <template v-if="signings && signings.length">
      <div style="min-height: 153px">
        <SigningLite
          :key="signing.id"
          v-for="signing in signings"
          :signing="signing"
          :buttonText="buttonText"
          @buttonClick="buttonClick(signing)"
        />
      </div>
    </template>
    <template v-else>
      <el-empty title="暂无数据" :image-size="80" />
    </template>
  </Tabs>
</template>
<script>
import Tabs from '../../../components/contract/index/tabs.vue'
import SigningLite from '../../../components/contract/index/signingLite.vue'
import handleError from  '../../../helpers/handleError'
import makeContractClient from '../../../services/contract/makeClient'
import {
  ContractStatusReviewing,
  ContractStatusFilling,
  ContractStatusSigning,
  ContractStatusDeadline
} from '../../../services/contract/constants'

const contractStatuses = [
  ContractStatusSigning,
  ContractStatusFilling,
  ContractStatusReviewing,
  ContractStatusDeadline
]
const client = makeContractClient()
export default {
  components: {
    Tabs,
    SigningLite
  },
  created() {
    this.load(ContractStatusSigning)
  },
  computed: {
    steps() {
      var r = []
      r.push(`待签署 (${this.cstatistics.waitSignCount})`)
      r.push(`待填写 (${this.cstatistics.waitWriteCount})`)
      r.push(`待审核 (${this.cstatistics.waitApproveCount})`)
      r.push(`即将截止 (${this.cstatistics.closingSoonCount})`)
      return r
    },
    buttonText() {
      switch (this.contractStatus) {
        case ContractStatusSigning:
          return '签署'
        case ContractStatusFilling:
          return '填写'
        case ContractStatusReviewing:
          return '审核'
        case ContractStatusDeadline:
          return '查看'

        default:
          break
      }
    }
  },
  methods: {
    select(index) {
      const status = contractStatuses[index]
      this.load(status)
      this.$emit('statusChanged', status)
      this.contractStatus = status
    },
    async load(status) {
      const [err, r] = await client.workbenchQueryContract({
        body: {
          handleByMe: true,
          submitByMe: false,
          status: status
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.total = r.data.total || 0
      this.signings = r.data.list || []

      const index = contractStatuses.findIndex(c => c === status)
      const keys = Object.keys(this.cstatistics)
      this.cstatistics[keys[index]] = this.total
    },
    buttonClick(signing) {
      const currentHref = '/'
      switch (this.contractStatus) {
        case ContractStatusSigning:
          return this.$router.push(
            `/contracts/${signing.id}/sign?back=${currentHref}`
          )
        case ContractStatusFilling:
          return this.$router.push(
            `/contracts/${signing.id}/write?back=${currentHref}`
          )
        case ContractStatusReviewing:
          return this.$router.push(
            `/signings?group=HANDLE_BY_ME&contractStatus=1&action=audit&contractId=${signing.id}&back=${currentHref}`
          )
        case ContractStatusDeadline:
          return this.$router.push(
            `/contracts/${signing.id}?back=${currentHref}`
          )

        default:
          break
      }
    }
  },
  props: {
    statistics: {
      type: Object,
      default() {
        return {
          waitSignCount: 0,
          waitWriteCount: 0,
          waitApprovalCount: 0,
          closingSoonCount: 0
        }
      }
    }
  },
  data() {
    return {
      total: 0,
      signings: [],
      cstatistics: this.statistics,
      contractStatus: ContractStatusSigning
    }
  }
}
</script>