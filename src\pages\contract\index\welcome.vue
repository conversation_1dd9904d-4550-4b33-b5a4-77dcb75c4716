<template>
  <div
    class="welcome"
    :style="{
      fontSize: '14px'
    }"
  >
    <div
      :style="{
        display: 'flex'
      }"
    >
      <img
        :src="userImg"
        height="48"
        width="48"
        :style="{
          marginRight: '12px',
          position: 'relative',
          top: '-5px'
        }"
      />
      <div
        :style="{
          flex: '1 1 auto',
          lineHeight: '1.5'
        }"
      >
        <span class="title">{{ user.name }}，下午好！</span>
        <el-tag
          class="info-tag"
          size="mini"
          effect="dark"
          v-if="merchantMember.isAdmin"
          style="
            padding: 0 8px;
            background: #f5f7ff;
            border-color: #f5f7ff;
            color: #4f71ff;
          "
        >
          超级管理员
        </el-tag>
        <br />
        {{ department.name }}
      </div>
      <div
        :style="{
          color: '#46485A',
          flex: '0 0 200px',
          fontSize: '14px',
          textAlign: 'right'
        }"
        v-if="formatDayDuration()"
      >
        您已加入公司 <span v-html="formatDayDuration()"></span>
      </div>
    </div>
    <hr color="#EEF0F4" style="border-top: none; margin: 20px 0" />
    <div style="display: flex">
      <b>消息通知</b>
      <span v-if="notificationsTotal">
        (<span style="color: #ffac04">{{ notificationsTotal }}条</span>)
      </span>
      <span
        style="flex: 1 1 auto; color: #a8acba; text-align: center"
        v-if="!notification"
      >
        暂无未读消息通知
      </span>
      <span style="flex: 1 1 auto" v-if="notification">
        <span style="color: #46485a"
          >【{{ notification.msgSourceType | msgSourceName }}】{{
            notification.title
          }}</span
        >
      </span>
      <span style="flex: 0 0 80px; color: #777c94" v-if="notification">
        {{ formatDateTime({ format: 'yyyy-MM-dd' }, notification.createTime) }}
      </span>
      <a
        v-if="notification"
        style="flex: 0 0 28px; cursor: pointer; color: #4f71ff"
        @click="goMessages"
      >
        更多
      </a>
    </div>
  </div>
</template>
<script>
import handleError from '../../../helpers/handleError'
import makePlatformClient from '../../../services/platform/makeClient'
import {
  user,
  merchantMember,
  merchant,
  deptVos,
  loginDefaultMerchantId,
  isManager,
  joinedMerchant
} from '../../../helpers/profile'
import formatDateTime from '../../../formatters/dateTime'
import userDefault from '../../../assets/images/user_default.png'
import userC81930 from '../../../assets/images/user_c81930.png'
const pclient = makePlatformClient()

export default {
  filters: {
    msgSourceName(v) {
      if (v === 'NEW_CONTRACT') {
        return '合同'
      }

      return '消息'
    }
  },
  async created() {
    this.user.name = user.realName
    this.merchant.name = merchant.name
    this.department.name = deptVos[0].namePath.slice(1).join('/')
    const jmerchant = joinedMerchant.find(
      c => c.merchantId === loginDefaultMerchantId
    )
    if (jmerchant) {
      this.merchant.entryTime = jmerchant.entryTime
    }

    const [err2, r2] = await pclient.platformListMsg({
      body: {
        start: 0,
        limit: 1,
        withTotal: true,
        sorts: [
          {
            field: 'createTime',
            direction: 'DESCENDING'
          }
        ],
        filters: {
          type: 'NEW_CONTRACT',
          isRead: false,
          channel: ['APP']
        }
      }
    })
    if (err2) {
      handleError(err2)
      return
    }
    if (r2.data && r2.data.list) {
      this.notification = r2.data.list[0]
      this.notificationsTotal = r2.data.total
    }
  },
  computed: {
    userImg() {
      if (window.env.theme === 'red') {
        return userC81930
      }

      return userDefault
    }
  },
  data() {
    return {
      merchantMember,
      user: {
        name: '',
        isAdmin: false
      },
      merchant: {
        name: '',
        entryTime: 0
      },
      department: {
        name: ''
      },
      notification: null,
      notificationsTotal: 0
    }
  },
  methods: {
    formatDateTime,
    formatDayDuration() {
      if (!this.merchant.entryTime) {
        return ''
      }
      const entryTime = new Date(this.merchant.entryTime).getTime()
      const nowTime = new Date().getTime()

      const v = parseInt((nowTime - entryTime) / (86400 * 1000), 10)
      if (!v) {
        return '-天'
      }
      if (v < 90) {
        return `<span style="font-weight: 500;font-size: 24px;color: #24262a;">${v}</span>天`
      }
      if (v < 365) {
        const month = parseInt(v / 30, 10)
        const remain = v - month * 30
        return `<span style="font-weight: 500;font-size: 24px;color: #24262a;">${month}</span>月<span style="font-weight: 500;font-size: 24px;color: #24262a;">${remain}</span>天`
      }
      if (v > 365) {
        const year = parseInt(v / 365, 10)
        const remain = v - year * 365
        return `<span style="font-weight: 500;font-size: 24px;color: #24262a;">${year}</span>年<span style="font-weight: 500;font-size: 24px;color: #24262a;">${remain}</span>天`
      }
      return value
    },
    goMessages() {
      location.href = window.env.messageURL
    }
  }
}
</script>
<style scoped>
.title {
  width: 144px;
  height: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 18px;
  color: #24262b;
  letter-spacing: 0;
  line-height: 18px;
}
.tag {
  width: 60px;
  height: 12px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #4f71ff;
  letter-spacing: 0;
  line-height: 12px;
}
</style>