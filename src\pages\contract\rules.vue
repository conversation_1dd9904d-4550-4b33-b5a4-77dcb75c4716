<template>
  <div
    :style="{
      width: 'calc(100% - 16px)'
    }"
  >
    <o-pc-list
      ref="list"
      :title="$route.meta.title"
      :formJson="[]"
      :requestFn="getListApi"
      :ifShowTopSelect="false"
      :showOverflowTooltip="false"
      :actionButtons="actionButtons"
      :tableHeaderActionButtons="tableHeaderActionButtons"
      :tableHeader="tableHeader"
    />
  </div>
</template>

<script>
import makeContractClient from '../../services/contract/makeClient'
import { makeRulesPreviews } from './rules/makeRulesPreviews'
import { makeNumberRuleRule } from './rules/makeNumberRuleRule'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import TextWidthDot from '../../components/contract/textWithDot.vue'
import formatDateTime from '../../formatters/dateTime'

const client = makeContractClient()

const getListApi = async params => {
  const [err, { data }] = await client.contractNoRuleQuery({
    body: params
  })

  if (err) {
    handleError(err)
    return
  }

  return data
}

export default {
  components: {
    TextWidthDot
  },
  data() {
    return {
      getListApi,
      conditions: {},
      actionButtons: [
        {
          label: '编辑',
          id: '1',
          click: row => this.$router.push(`/rules/${row.id}/edit`)
        },
        {
          label: '删除',
          id: '2',
          ifShow: row => !row.enable,
          click: async row => {
            const [err, _] = await client.contractNoRuleRemoveCheck({
              body: {
                id: row.id
              }
            })
            // 无法删除弹窗
            if (err && err.errorCode === 501) {
              this.$msgbox({
                title: '删除提示',
                message:
                  '<b>该编号已有合同使用，无法删除</b>' +
                  '<br/>如后续不想使用，您可以停用此编号规则',
                dangerouslyUseHTMLString: true,
                type: 'warning',
                confirmButtonText: '我知道了',
                closeOnClickModal: false
              })
              return
            }
            if (err) {
              handleError(err)
              return
            }

            this.$confirm(
              `<b>确认要删除【${row.name}】编号吗？</b>` +
                '<br/>删除后，将无法再使用该编号规则发起合同。',
              '删除',
              {
                dangerouslyUseHTMLString: true,
                type: 'warning',
                closeOnClickModal: false
              }
            ).then(async () => {
              // 确认删除弹窗
              const [err1, _] = await client.contractNoRuleRemove({
                body: {
                  id: row.id
                }
              })
              if (err1) {
                handleError(err1)
                return
              }
              handleSuccess('合同编号规则删除成功')
              this.reload()
            })
          }
        },
        {
          label: '启用',
          id: '3',
          ifShow: row => !row.enable,
          click: row => this.onEnable(row)
        },
        {
          label: '停用',
          id: '4',
          ifShow: row => row.enable,
          click: row => this.onDisable(row)
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'right',
          type: 'button',
          label: '新建编号规则',
          icon: 'olading-iconfont oi-icon_add2',
          click: () => this.$router.push('/rules/new')
        }
      ],
      tableHeader: [
        {
          label: '序号',
          type: 'INDEX',
          fixed: true
        },
        {
          prop: 'name',
          label: '编号规则名称',
          formatter: row => `
          <div>
            <p title=${row.name} style="font-size: 14px;line-height: 14px;font-weight: 500;" class="text-ellipsis-2line ">
              ${row.name}
            </p>
            <p title=${row.remark} style="color: #777C94;font-size: 10px;" class="text-ellipsis-2line ">
             ${row.remark}
            </p>
          </div>
            
          `
        },
        {
          prop: 'rules',
          label: '编号规则配置',
          width: 320,
          render(h, row) {
            return h('div', [
              ...row.rules.map(rule =>
                h(
                  'el-tag',
                  {
                    style: {
                      margin: '0 10px 10px 0',
                      border: 'none',
                      color: '#24262A',
                      background: '#f8f8f8'
                    }
                  },
                  makeNumberRuleRule(rule)
                )
              ),
              h(
                'div',
                {
                  title: makeRulesPreviews(row.rules),
                  style: 'color: #777C94;font-size: 10px;line-height: 14px;'
                },
                ['预览示例: ' + makeRulesPreviews(row.rules)]
              )
            ])
          }
        },
        {
          prop: 'enable',
          label: '状态',
          width: 140,
          render: (h, row) => {
            // const statusText = row.enable ? '已启用' : '已停用'
            return h(TextWidthDot, {
              props: {
                color: row.enable ? 'blue' : 'gray',
                text: row.enable ? '已启用' : '已停用'
              }
            })
          }
        },
        {
          prop: 'updater',
          label: '更新人',
          width: '150px',
          formatter: row => row.updater.name || '-'
        },
        {
          prop: 'updateTime',
          label: '更新时间',
          width: '150px',
          formatter: row =>
            `${
              row.updateTime
                ? formatDateTime('yyyy-MM-dd HH:mm', row.updateTime)
                : '-'
            }`
        }
      ]
    }
  },
  methods: {
    reload() {
      this.$refs['list'].appendRequestParams(this.conditions)
    },
    async onEnable(row) {
      const [err] = await client.contractNoRuleEnable({
        body: {
          id: row.id
        }
      })
      if (err) {
        handleError(err)
        return
      }
      handleSuccess('合同编号规则启用成功')
      this.reload()
      row.enable = true
    },
    async onDisable(row) {
      const [err, r] = await client.contractNoRuleDisableCheck({
        body: {
          id: row.id
        }
      })
      // 无法停用弹窗
      if (err && err.errorCode === 501) {
        this.$msgbox({
          title: '停用提示',
          message:
            '<b>该编号规则已被合同类型或模板使用，无法停用</b>' +
            '<br/>已有启用中的合同类型或模板，正在使用该合同编号规则，无法停用。',
          dangerouslyUseHTMLString: true,
          type: 'warning',
          confirmButtonText: '我知道了',
          showCancelButton: false,
          closeOnClickModal: false
        })
        return
      }
      if (err) {
        handleError(err)
        return
      }

      // 确认停用提示
      this.$confirm(
        `<b>确认要停用【${row.name}】编号吗？</b>` +
          '<br/>停用后，将无法再使用该编号规则发起合同，已发起合同不受影响。',
        '停用',
        {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          closeOnClickModal: false
        }
      ).then(async () => {
        const [err] = await client.contractNoRuleDisable({
          body: {
            id: row.id
          }
        })
        if (err) {
          handleError(err)
          return
        }
        handleSuccess('合同编号规则停用成功')
        this.reload()
        row.enable = false
      })
    }
  }
}
</script>