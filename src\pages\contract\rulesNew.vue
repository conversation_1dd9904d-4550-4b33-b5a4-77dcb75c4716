<template>
  <RightLayout>
    <TopBar>
      <Breadcrumb :title="title" />
    </TopBar>
    <MiddleBox>
      <el-alert
        v-if="false"
        style="width: 580px; margin: 20px auto 10px auto"
        title="已关联发起合同的规则配置，不可修改"
        type="warning"
        show-icon
      >
      </el-alert>
      <el-form
        ref="numberRuleForm"
        :model="numberRule"
        :rules="formRules"
        label-position="top"
        :style="{
          margin: '0 auto',
          width: '580px',
          padding: '15px 0 200px 0'
        }"
      >
        <el-form-item label="规则名称" prop="name" :error="errorMessage.name">
          <el-input
            v-model="numberRule.name"
            maxlength="50"
            placeholder="请输入规则名称"
          />
        </el-form-item>
        <el-form-item prop="description" label="使用说明">
          <el-input
            v-model="numberRule.remark"
            type="textarea"
            rows="6"
            maxlength="100"
            show-word-limit
            placeholder="请输入使用说明"
          />
        </el-form-item>
        <el-form-item prop="rules" :error="errorMessage.rules">
          <RuleConfigs @delete="againVaildRules" v-model="numberRule.rules" />
        </el-form-item>
        <div
          :style="{
            backgroundColor: '#F7FAFD',
            height: '44px',
            opacity: '0.8',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            paddingLeft: '20px',
            display: 'flex'
          }"
          v-if="numberRule.rules && numberRule.rules.length"
        >
          <span
            style="
              color: #24262a;
              font-size: 12px;
              font-weight: 600;
              flex: 0 0 60px;
            "
          >
            预览示例：
          </span>
          <span style="color: #46485a; font-size: 14px">
            {{ makeRulesPreviews(numberRule.rules) }}
          </span>
        </div>
      </el-form>
    </MiddleBox>
    <BottomBar>
      <el-button @click="cancel">取消</el-button>
      <el-button plain @click="submit">保存</el-button>
      <el-button
        v-if="!this.$route.params.id"
        type="primary"
        @click="submit('save')"
        >保存并启用</el-button
      >
    </BottomBar>
  </RightLayout>
</template>
<script>
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import Breadcrumb from '../../components/contract/breadcrumb.vue'
import RightLayout from '../../components/contract/rightLayout.vue'
import TopBar from '../../components/contract/topBar.vue'
import MiddleBox from '../../components/contract/middleBox.vue'
import BottomBar from '../../components/contract/bottomBar.vue'
import RuleConfigs from '../../components/contract/rule/configs.vue'
import { makeRulesPreviews } from './rules/makeRulesPreviews'
import {
  rulesTypeText,
  rulesTypeAutoIncrementNumber,
  rulesTypeRandomNumber
} from '../../services/contract/constants'
const client = makeContractClient()
export default {
  components: {
    Breadcrumb,
    RightLayout,
    TopBar,
    MiddleBox,
    BottomBar,
    RuleConfigs
  },
  async created() {
    if (!this.$route.params.id) {
      return
    }

    const [err, r] = await client.contractNoRuleQueryById({
      body: {
        id: this.$route.params.id
      }
    })
    if (err) {
      handleError(err)
      return
    }

    this.numberRule = r.data
  },
  computed: {
    title() {
      return this.$route.params.id ? '编辑编号规则' : '新建编号规则'
    }
  },
  data() {
    return {
      numberRule: {
        rules: [
          { type: '1', value: '', start: '', key: 1 },
          { type: '2', value: 'yyyyMMdd', start: '', key: 2 },
          { type: '3', value: '', start: '', key: 3 }
        ]
      },
      errorMessage: { name: '', rules: '' },
      formRules: {
        name: [
          {
            required: true,
            message: '请输入规则名称',
            trigger: 'blur'
          },
          {
            max: 50,
            message: '编号规则名称不可超过50字',
            trigger: 'blur'
          }
        ],
        description: [
          {
            max: 100,
            message: '编号规则使用说明不可超过100字',
            trigger: 'blur'
          }
        ],
        rules: [
          {
            validator(rule, value, callback) {
              for (let rule of value) {
                if (rule.type === rulesTypeText) {
                  if (rule.value.length <= 0)
                    callback(new Error('自定义文本不可为空'))
                  if (rule.value.length > 20)
                    callback(new Error('自定义文本内容不可超过20'))
                  const reg = /^[A-Za-z0-9-/\[\]【】{}\u4e00-\u9fa5]{1,20}$/
                  if (!reg.test(rule.value)) {
                    callback(new Error('自定义文本内容格式不正确'))
                  }
                } else if (rule.type === rulesTypeAutoIncrementNumber) {
                  if (rule.value.length <= 0)
                    callback(new Error('请选择自增流水号位数'))
                  // if (rule.value != rule.start.length)
                  //   callback(new Error('自增流水号位数不匹配'))
                } else if (rule.type === rulesTypeRandomNumber) {
                  if (rule.value.length <= 0)
                    callback(new Error('请选择随机流水号位数'))
                }
              }
              if (makeRulesPreviews(value).length > 32) {
                callback(new Error('编号位数超出32位限制'))
              }
              if (value && value.length === 0) {
                callback(new Error('请选择编号规则配置'))
              }

              callback()
            },
            trigger: 'change'
          }
        ]
      }
    }
  },
  methods: {
    makeRulesPreviews,
    async _submit() {
      const numberRule = this.numberRule
      this.errorMessage = { name: '', rules: '' }
      const [err, _] = await client.contractNoRuleCheckRule({
        body: numberRule
      })
      if (err) {
        if (err.message.indexOf('规则名称') !== -1) {
          this.errorMessage.name = err.message
        } else {
          this.errorMessage.rules = err.message
        }
        return
      }
      // 校验通过弹窗
      this.$confirm(
        '请仔细检查编号规则，开始使用后将无法修改。',
        numberRule.enable ? '编号规则保存提示' : '编号规则保存提示',
        {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          closeOnClickModal: false
        }
      ).then(async () => {
        // 修改
        if (numberRule.id) {
          const [err, _] = await client.contractNoRuleUpdate({
            body: numberRule
          })
          if (err) {
            handleError(err)
            return
          }
        } else {
          const [err, _] = await client.contractNoRuleSave({
            body: numberRule
          })
          if (err) {
            handleError(err)
            return
          }
        }

        handleSuccess(numberRule.enable ? '保存并启用成功' : '保存成功')
        this.$router.go(-1)
      })
    },
    submit(save) {
      save === 'save'
        ? (this.numberRule.enable = true)
        : (this.numberRule.enable = false)
      const _this = this

      this.$refs.numberRuleForm.validate(valid => {
        if (valid) {
          _this._submit()
        } else {
          this.scrollIntoError(this.$refs.numberRuleForm)
        }
      })
    },
    againVaildRules() {
      this.$refs.numberRuleForm.validateField('rules')
    },
    // 取消按钮
    cancel() {
      this.$router.go(-1)
    }
  }
}
</script>