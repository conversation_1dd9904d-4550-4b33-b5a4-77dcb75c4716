import {
  rulesTypeText,
  rulesTypeDate,
  rulesTypeAutoIncrementNumber,
  rulesTypeRandomNumber
} from '../../../services/contract/constants'
export const makeNumberRuleRule = rule => {
  if (rule.type === rulesTypeText) {
    return rule.value
  }
  if (rule.type === rulesTypeDate) {
    const len = rule.value.length
    return len === 4
      ? 'yyyy'
      : len === 6
      ? 'yyyyMM'
      : len === 8
      ? 'yyyyMMdd'
      : len === 12
      ? 'yyyyMMddHHmm'
      : len === 14
      ? 'yyyyMMddHHmmss'
      : 'yyyyMMdd'
  }
  if (rule.type === rulesTypeAutoIncrementNumber) {
    return `${rule.value}位自增流水号`
  }
  if (rule.type === rulesTypeRandomNumber) {
    return `${rule.value}位随机流水号`
  }
  throw new Error('后端没有提供此编号规则的校验!!!')
}