import {
  rulesTypeText,
  rulesTypeDate,
  rulesTypeAutoIncrementNumber,
  rulesTypeRandomNumber
} from '../../../services/contract/constants'
export const makeRulesPreviews = (rules = []) => {
  var r = []
  var cRules = [...rules]
  for (var c of cRules) {
    if (c.type === rulesTypeText) {
      r.push(c.value)
    }
    if (c.type === rulesTypeDate) {
      r.push(formatRuleDate(Date.now(), c.value))
    }
    if (c.type === rulesTypeAutoIncrementNumber) {
      if (c.value == 0) break
      r.push(formatIntFillZero(c.start, parseInt(c.value, 10)))
    }
    if (c.type === rulesTypeRandomNumber) {
      if (c.value == 0) break
      let random =
        Math.round(Math.random() * Math.pow(10, parseInt(c.value, 10))) + ''
      if (random.length > c.value) random = random.slice(0, c.value) + ''
      r.push(random.padStart(c.value, '0'))
    }
  }
  return r.join('')
}
export const formatIntFillZero = (v, size) => {
  const s = v + ''
  const l = s.length
  var needFileSize = 0
  if (l < size) {
    needFileSize = size - l
  }
  const zeros = Array(needFileSize).fill('0')
  return zeros.join('') + s
}

//format 仅支持rules
//yyyy yyyyMM yyyyMMdd yyyyMMddHH yyyyMMddHHmm yyyyMMddHHmmss
const formatRuleDate = (datetime, format) => {
  if (!datetime) {
    datetime = new Date().getTime()
  }
  const current = new Date().toLocaleTimeString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })

  const ds = current.replace(' ', '').replaceAll('/', '').replaceAll(':', '')
  const l = format.length

  return ds.slice(0, l)
}