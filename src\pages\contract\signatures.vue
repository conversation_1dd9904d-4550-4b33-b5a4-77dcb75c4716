<template>
  <div style="padding: 0 20px">
    <div style="overflow: hidden">
      <Button
        style="float: right; margin: 20px 0"
        @click="showHandwriteSignature = true"
        >添加签名</Button
      >
    </div>
    <template v-for="signature in signatures">
      <Signature
        :signature="signature"
        :key="signature.imageId"
        @setDefault="setDefault"
        @remove="deleteSignature"
      />
    </template>
    <HandwriteSignature
      style="
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 100vw;
        background: #fff;
      "
      v-if="showHandwriteSignature"
      @onComplete="handWriteComplete"
      @clickReturn="showHandwriteSignature = false"
    />
  </div>
</template>

<script>
import { Toast, Button } from 'vant'
import handleErrorH5 from 'kit/helpers/handleErrorH5'
import HandwriteSignature from '../../components/mpH5/signatures/handwrite.vue'
import Signature from '../../components/mpH5/signatures/signature.vue'
import store from '../../helpers/store'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  components: {
    Signature,
    HandwriteSignature,
    Button
  },
  data() {
    return {
      signatures: [],
      showHandwriteSignature: false
    }
  },
  async created() {
    this.loadSignatures()
  },
  methods: {
    async deleteSignature(imageId) {
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        loadingType: 'spinner'
      })

      const [err, r] = await platformClient.deleteSignature(imageId, {})

      if (err) {
        handleErrorH5(err)
        return
      }

      Toast.clear()
      this.loadSignatures()
    },
    async setDefault(imageId) {
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        loadingType: 'spinner'
      })

      const [err, r] = await platformClient.setDefaultSignature({
        body: { imageId },
        method: 'PUT'
      })

      if (err) {
        handleErrorH5(err)
        return
      }

      Toast.clear()
      this.loadSignatures()
    },
    async loadSignatures() {
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        loadingType: 'spinner'
      })

      const [err, r] = await platformClient.profileSignatureQuery('ALL', {
        method: 'GET'
      })

      if (err) {
        handleErrorH5(err)
        return
      }

      this.signatures = r.data
      Toast.clear()
    },
    base64ToFile(dataUrl, filename = 'file') {
      let arr = dataUrl.split(',')
      let mine = arr[0].match(/:(.*?);/)[1]
      let suffix = mine.split('/')[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], `${filename}.${suffix}`, {
        type: mine
      })
    },
    async handWriteComplete(base64Url) {
      this.showHandwriteSignature = false
      const file = this.base64ToFile(base64Url)

      const formData = new FormData()
      formData.append('file', file)

      const response1 = await fetch(
        `${window.env.host}/webapi/api/contract/open/archive/upload`,
        {
          method: 'post',
          headers: {
            Authorization: store.get('token')
          },
          body: formData
        }
      )
      const r = await response1.json()

      // const [err,r] = await platformClient.contractOpenArchiveUpload({
      //   file :formData
      // })

      if (!r.success) {
        return handleErrorH5('添加签名失败')
      }

      const [err2, r2] = await platformClient.createSignature({
        body: {
          defaultImage: false,
          imageId: r.data,
          signatureType: 'HAND_WRITER'
        }
      })

      if (err2) {
        handleErrorH5(err2)
        return
      }

      this.loadSignatures()
    }
  }
}
</script>

<style>
</style>