<template>
  <o-container>
    <template slot="header">
      <TopBar style="padding: 0">
        <div
          :style="{
            display: 'flex'
          }"
        >
          <SignTypeTabs
            @groupChange="groupChange"
            :handleByOtherCount="handleByOtherCount"
            :handleByMeCount="handleByMeCount"
            v-model="signingGroup"
          />
        </div>
      </TopBar>
    </template>

    <ContactStatusCheckbox
      :group="group"
      v-model="contractStatusList"
      style="margin-top: 16px"
    />
    <!-- 筛选区域 -->
    <o-top-select
      ref="top-select"
      :formJson="formJson"
      :immediate="true"
      labelWidth="96px"
      @search="onSearch"
      @reset="resetForm"
    />

    <!-- 表格区域 -->
    <o-table
      style="margin-top: 24px"
      ref="o-table"
      :sticky="true"
      :actionButtons="actionButtons"
      :selection="true"
      @selection-change="handleSelectionChange"
      :tableHeader="tableHeader"
      :showPagination="true"
      :requestFn="load"
      emptyHeight="calc(100vh - 450px)"
      :tableHeaderActionButtons="tableHeaderActionButtons"
      :pagination="{ fixed: true }"
    />
    <WithdrawContract
      @reload="onSearch"
      :rows="rows"
      ref="withdrawContractRef"
    />
    <AuditContract @reload="onSearch" :rows="rows" ref="auditContractRef" />
    <PromptContract @reload="onSearch" :rows="rows" ref="promptContractRef" />
    <BatchSign
      :currentId="user.id"
      :signAccordList="accordSignings"
      :rows="rows"
      ref="batchSignRef"
    />
    <VerifiedDialog ref="verifiedDialog" />
  </o-container>
</template>

<script>
import TopBar from '../../components/contract/topBar.vue'
import SignerStatusWithDot from '../../components/contract/signing/signerStatusWithDot.vue'
import WithdrawContract from './signings/withdrawContractDialog.vue'
import AuditContract from './signings/auditContractDialog.vue'
import PromptContract from './signings/promptContractDialog.vue'
import DraftEmpList from '../../components/contract/signing/draftEmpList.vue'
import SignProcessList from '../../components/contract/signing/signProcessList.vue'
import CarbonCopyEmpList from '../../components/contract/signing/carbonCopyEmpList.vue'
import ContactName from '../../components/contract/signing/contactName.vue'
import formatDateTime from '../../formatters/dateTime'
import ContactStatusCheckbox from '../../components/contract/signing/contactStatusCheckbox.vue'
import SignTypeTabs from '../../components/contract/signing/signTypeTabs.vue'
import TextWithDot from '../../components/contract/textWithDot.vue'
import VerifiedDialog from './signings/verifiedDialog.vue'
import BatchSign from '../../components/contract/signing/batchSignDialog.vue'
import makePlatformClient from '../../services/platform/makeClient'
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import { hadPrivilege, user } from '../../helpers/profile'
import {
  ContractStatusCompleted,
  ContractStatusFilling,
  ContractStatusOverdue,
  ContractStatusRejected,
  ContractStatusReviewing,
  ContractStatusSigning,
  ContractStatusWithdrew,
  constractDownloadStatusSuccess
} from '../../services/contract/constants'
import {
  verifyWrite,
  verifyAduit,
  verifySign,
  verifyUrge,
  verifyWithdraw,
  verifyEdit,
  verifyDownload,
  verifyReissue,
  verifyRemove,
  verifyView
} from './contracts/verify'

const pclient = makePlatformClient()
const client = makeContractClient()

export default {
  components: {
    TopBar,
    SignerStatusWithDot,
    WithdrawContract,
    AuditContract,
    PromptContract,
    DraftEmpList,
    SignProcessList,
    CarbonCopyEmpList,
    ContactStatusCheckbox,
    SignTypeTabs,
    ContactName,
    BatchSign,
    VerifiedDialog
  },
  async created() {
    this.tableHeader = this.originTableHeader
    this.loadSelectData()
    // 获取当前用户id

    this.user = user
    this.loadSignsingCount()
  },
  async mounted() {
    // businessui自带的分割线 不符合ui设计
    document.querySelector('hr').remove()

    const { group, contractStatus, action, contractId } = this.$route.query
    // 通过路由的参数 选中不同的筛选项
    if (group || contractStatus || action || contractId) {
      this.signingGroup = group
      this.group = this.signingGroup
      this.contractStatusList = contractStatus ? [contractStatus] : []
      this.onSearch()

      // 根据url的值 执行对应的操作
      if (contractId) {
        const r = await this.load({
          filters: {
            contractIdList: [contractId]
          }
        })
        this.rows = r.list
        switch (action) {
          // 催办
          case 'prompt':
            this.$refs.promptContractRef.open()
            break
          // 审核
          case 'audit':
            this.$refs.auditContractRef.open()
            break
        }
      }
    }
  },
  methods: {
    // 改变复选框
    handleSelectionChange(rows) {
      this.rows = rows
      console.log(rows, '123123123123')
    },
    async groupChange(group) {
      // 清空筛选框内容及表单内容
      this.contractStatusList = []
      this.$refs['top-select'].handleResetBtnClick()
      this.group = group
      // 每次切换都会导致下拉框消失，需要重新加载
      this.loadSelectData()
      this.onSearch()
    },
    async onSearch() {
      // 获取筛选条件
      const fData = await this.$refs['top-select'].getFormData()
      let group = this.group
      let contractStatusList = this.contractStatusList
      // o-top-sreach默认是是'' 后端要数组 直接给会报错。如果没选择需要处理
      if (!fData.contractTypeIdList) {
        fData.contractTypeIdList = []
      } else {
        fData.contractTypeIdList = [fData.contractTypeIdList]
      }
      // 如果分组为草稿 分组和筛选条件清空
      if (this.group === 'DRAFT') {
        group = undefined
        contractStatusList = undefined
      }

      // 发起请求
      await this.$refs['o-table'].appendRequestParams({
        ...fData,
        group,
        contractStatusList
      })
    },
    async load(params) {
      let body = {
        limit: 20,
        start: 1,
        withDeleted: true,
        withDisabled: true,
        withTotal: true,
        ...params
      }
      if (this.group == 'DRAFT') {
        const [err, r] = await client.signingQueryDraft({ body })

        if (err) {
          handleError(err)
          return
        }
        return r.data
      }

      const [err, r] = await client.signingQueryContract({ body })

      if (err) {
        handleError(err)
        return
      }

      return r.data
    },
    // 撤回合同
    handleWithdraw(row) {
      if (!Array.isArray(row)) {
        this.rows = [row]
      } else {
        this.rows = row
      }
      // 有撤回权限
      const signingList = this.rows.filter(signing =>
        verifyWithdraw(
          signing.status,
          this.user.id,
          signing?.creator?.signer?.id
        )
      )
      if (signingList.length <= 0) {
        handleError({ message: '请选择至少一条本人可撤回的合同' })
        return
      }

      this.$refs.withdrawContractRef.open()
    },
    // 审核合同
    handleAudit(row) {
      if (!Array.isArray(row)) {
        this.rows = [row]
      } else {
        this.rows = row
      }
      const signingList = this.rows.filter(signing =>
        verifyAduit(signing.status, this.user.id, signing.handlingBy.id)
      )
      if (signingList.length <= 0) {
        handleError({ message: '请选择至少一条本人可审核的合同' })
        return
      }
      this.$refs.auditContractRef.open()
    },
    // 催办
    handlePrompt(row) {
      if (!Array.isArray(row)) {
        this.rows = [row]
      } else {
        this.rows = row
      }
      const signList = this.rows.filter(signsing =>
        verifyUrge(signsing.status, this.user.id, signsing.creator.signer.id)
      )
      if (signList.length <= 0) {
        handleError({ message: '请选择至少一条本人可催办的合同' })
        return
      }
      this.$refs.promptContractRef.open()
    },
    // 批量签署
    handleSign() {
      // 校验个人是否已经进行实名认证，未进行时显示实名认证弹窗
      if (!this.user.isAuth) {
        this.$refs.verifiedDialog.open()
        return
      }
      const currentId = this.user.id
      // 通过审核的条数
      this.accordSignings = this.rows.filter(sign => {
        const signerList = [...sign.signProcessList, ...sign.writeProcessList]

        // 状态均为签署中
        return (
          sign.status == ContractStatusSigning &&
          // 当前用户为当前处理人或有权限签署的用户
          sign.handlingBy.id == currentId &&
          // 当前流程的处理人签署方式不是限制必须使用手写签名
          signerList.filter(
            signer =>
              signer.signer.signer.id == sign.handlingBy.id &&
              // 签署方式：1-不限制；2-系统标准签章；3-手写；
              signer.signWay &&
              // 个人
              signer.signer.signerType == '1' &&
              // 手写签名
              signer.signWay !== '3'
          ).length > 0
        )
      })
      if (this.accordSignings.length <= 0) {
        return handleError({ message: '请至少选择一条本人可签署合同' })
      }
      this.$refs.batchSignRef.open()
    },

    // 表单人员搜索
    async remoteSearchEmployee(query) {
      const [err, r] = await pclient.platformListMerchantMember({
        body: {
          start: 0,
          limit: 100,
          filters: {
            keywords: query
          }
        }
      })
      if (err) {
        handleError(err)
        return
      }
      return r.data.list.map(item => {
        return {
          label: `${item.name} (${item.cellPhone})`,
          value: item.userId,
          key: item.id
        }
      })
    },

    // 删除草稿
    removeDraft(row) {
      this.$confirm(`<b>确认要删除【${row.name}】吗？</b>`, '删除', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        closeOnClickModal: false
      }).then(async () => {
        const [err, r] = await client.signingRemoveDraft({
          body: {
            id: row.id
          }
        })
        if (err) {
          handleError(err)
          return
        }
        this.onSearch()
        handleSuccess('草稿删除成功')
      })
    },
    // 批量导出
    async exportContract() {
      // 获取筛选条件
      const fData = await this.$refs['top-select'].getFormData()
      let group = this.group
      let contractStatusList = this.contractStatusList

      // o-top-sreach默认是是'' 后端要数组 直接给会报错。如果没选择需要处理
      if (!fData.contractTypeIdList) {
        fData.contractTypeIdList = []
      } else {
        fData.contractTypeIdList = [fData.contractTypeIdList]
      }

      // 如果分组为草稿 分组和筛选条件清空
      if (this.group === 'DRAFT') {
        group = undefined
        contractStatusList = undefined
      }

      // 勾选的合同
      let contractIdList = []
      if (this.rows.length > 0) {
        contractIdList = this.rows.map(row => row.id)
      }

      const body = {
        filters: {
          ...fData,
          contractStatusList,
          group,
          contractIdList: contractIdList
        }
      }

      const [err1, r1] = await client.signingCheckExport({ body })
      if (err1) {
        handleError(err1)
        return
      }
      this.$confirm(
        `<b>将导出${r1.data.total}个合同明细信息，确定要导出吗？`,
        '导出明细',
        {
          type: 'warning',
          dangerouslyUseHTMLString: true,
          closeOnClickModal: false
        }
      ).then(async () => {
        const [err, r] = await client.signingExportContract({ body })

        if (err) {
          handleError(err)
          return
        }

        const tmp = r.headers.get('Content-Disposition').split('filename=')
        const filename = decodeURIComponent(tmp[1])
        const blobData = await r.blob()
        const blob = new Blob([blobData])
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        link.remove()
      })
    },

    checkDownloadContractStatus(props) {
      const signingContracts = this.rows.filter(
        row => row.status === ContractStatusCompleted
      )
      // 全部不可下载
      if (signingContracts.length === 0) {
        this.$confirm(
          `<b>无可下载合同</b>` +
            `<br/>仅全部签署方都完成签署后，可以进行下载。`,
          '',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true,
            confirmButtonText: '我知道了',
            showCancelButton: false,
            closeOnClickModal: false
          }
        )
        return
      }
      // 全部可下载
      if (signingContracts.length === this.rows.length) {
        this.$confirm(
          `<b>您有${signingContracts.length}份合同可下载，确认要下载吗？` +
            '<br/>仅全部签署方都完成签署后，可以进行下载。',
          '下载',
          {
            dangerouslyUseHTMLString: true,
            type: 'warning',
            confirmButtonText: '下载',
            closeOnClickModal: false
          }
        ).then(async () => {
          this.downloadContract(signingContracts, props)
        })
        return
      }
      // 部分可下载
      this.$confirm(
        `<b>您有${signingContracts.length}份已签署合同可下载，确认要下载吗？` +
          `<br/>您选择的文件有${
            this.rows.length - signingContracts.length
          }份不可下载，请检查是否全部签署方都已完成签署。`,
        '下载',
        {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          confirmButtonText: '下载',
          closeOnClickModal: false
        }
      ).then(async () => {
        this.downloadContract(signingContracts, props)
      })
    },
    // 下载合同
    async downloadContract(rows, props = {}) {
      let idList = []
      // 批量文件下载
      if (rows.length > 0) {
        idList = rows.map(row => row.id)
      } else {
        // 单个文件下载
        idList = [rows.id]
      }
      if (idList.length <= 0) {
        return handleError({ message: '请选择要下载的合同' })
      }
      // 调用下载接口
      props.loading = true
      const [err, r] = await client.contractDownload({
        body: {
          idList
        }
      })
      if (err) {
        handleError(err)
        return
      }
      const downloadStatus = r.data.status
      // 下载任务状态：1-处理中（循环调用checkDownloadTask检测任务）；2-成功（直接使用archiveId下载文件）
      if (downloadStatus === constractDownloadStatusSuccess) {
        props.loading = false
        this._downloadContract({
          // name: `合同文件下载_${this.timeStr}_${idList.length}份.zip`,
          archiveId: r.data.archiveId
        })
      } else {
        this.checkDownloadTask(r.data.downloadTaskId, props)
      }
    },
    // 轮询合同是否下载成功
    checkDownloadTask(downloadTaskId, props) {
      const timer = setInterval(async () => {
        const [err, r] = await client.contractCheckDownloadTask({
          body: {
            id: downloadTaskId
          }
        })
        if (err) {
          handleError(err)
          return
        }
        if (r.data.status === constractDownloadStatusSuccess) {
          props.loading = false
          clearInterval(timer)
          this._downloadContract({
            name: `合同文件下载_${this.timeStr}_${downloadTaskId.length}份.zip`,
            archiveId: r.data.archiveId
          })
        }
      }, 1000)
    },
    // 下载文件
    async _downloadContract(file) {
      const id = file.archiveId
      const name = file.name
      const [err, r] = await pclient.platformDownloadFile(
        {
          method: 'GET',
          headers: { 'content-type': 'application/octet-stream' }
        },
        { id, name }
      )
      if (err) {
        console.log(err, 'errrrrrr')
        return
      }
      window.open(r.url)
    },
    // 查看合同
    preViewContract(row) {
      if (String(user.id) !== String(row?.handlingBy?.id)) {
        // 查看合同页面
        this.$router.push({
          path: `/contracts/${row.id}?back=/signings`
        })
        return
      }
      switch (row.status) {
        // 填写中
        case ContractStatusFilling:
          // 当前处理人 填写合同页面
          this.$router.push({
            path: `/contracts/${row.id}/write?back=/signings`
          })

          break
        // 签署中
        case ContractStatusSigning:
          // 当前处理人签署页面 并且有权限
          this.$router.push({
            path: `/contracts/${row.id}/sign?back=/signings`
          })

          break
        // 审核中
        case ContractStatusReviewing:
          // 有权限且当前处理人 审核页面
          this.$router.push({
            path: `/contracts/${row.id}?back=/signings`
          })
          break
        // 已撤回，已拒绝，已逾期   查看合同页面
        case ContractStatusWithdrew:
        case ContractStatusRejected:
        case ContractStatusOverdue:
          this.$router.push({
            path: `/contracts/${row.id}?back=/signings`
          })
          break
        // 已完成
        case ContractStatusCompleted:
          // 判断签署文件是否生成，未生成时toast提示：签署文件正在生成中，请稍后查看。文件存在时，进入查看合同页面
          this.$router.push({
            path: `/contracts/${row.id}?back=/signings`
          })
          break
        // 其他情况就是草稿
        default:
          this.$router.push({
            path: `/signings/drafts/${row.id}/step1/edit`,
            query: { source: 'DRAFT' }
          })
      }
    },
    // 重置表单的回调
    resetForm() {
      this.contractStatusList = []
    },
    // 获取待我处理和待他人处理合同数量
    async loadSignsingCount() {
      const handleByMeList = await this.load({
        filters: {
          group: 'HANDLE_BY_ME'
        }
      })
      const handleByOtherList = await this.load({
        filters: {
          group: 'HANDLE_BY_OTHER'
        }
      })
      this.handleByMeCount = handleByMeList?.total || 0
      this.handleByOtherCount = handleByOtherList?.total || 0
    },
    // 加载下拉框内容
    async loadSelectData() {
      // 请求合同类型列表
      const [err1, r1] = await client.contractTypeGetTypeDict({})
      const types = []
      if (err1) {
        handleError(err1)
        return
      }
      for (let key in r1.data) {
        types.push({ label: r1.data[key], value: key })
      }

      this.$refs['top-select'].setOptions({
        contractTypeIdList: types
      })
      // 发起方企业信息
      const [err2, r2] = await pclient.platformListLegal({
        body: {
          withTotal: true,
          filters: {
            legalUsageType: ['CONTRACT']
          }
        }
      })
      const legals = r2.data.list.map(legal => ({
        label: legal.name,
        value: legal.id
      }))
      this.$refs['top-select'].setOptions({
        createLegalId: legals
      })
    }
  },
  data() {
    return {
      rows: [],
      user: {},
      signingGroup: 'ALL',
      contractStatusList: [],
      group: undefined,
      handleByMeCount: 0,
      handleByOtherCount: 0,
      accordSignings: [],
      // 表单结构
      formJson: [
        {
          type: 'input',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME',
            'DRAFT'
          ],
          formItem: {
            prop: 'name',
            label: '合同名称',
            placeholder: '请输入合同名称'
          }
        },
        {
          type: 'remoteSearchSelect',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME',
            'DRAFT'
          ],
          formItem: {
            prop: 'signerUserId',
            label: '签署方',
            placeholder: '请输入姓名/手机号',
            remoteMethod: query => {
              return new Promise(resolve => {
                const data = this.remoteSearchEmployee(query)
                resolve(data)
              })
            }
          }
        },
        {
          type: 'remoteSearchSelect',
          belong: [
            'ALL',
            'HANDLE_BY_OTHER',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          formItem: {
            prop: 'handlerId',
            label: '当前处理人',
            placeholder: '请输入姓名/手机号',
            remoteMethod: query => {
              return new Promise(resolve => {
                const data = this.remoteSearchEmployee(query)
                resolve(data)
              })
            }
          }
        },
        {
          type: 'select',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          formItem: {
            prop: 'contractTypeIdList',
            label: '合同类型',
            placeholder: '请选择',
            options: []
          }
        },
        {
          type: 'datePicker',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          formItem: {
            prop: 'createDate',
            label: '发起日期',
            placeholder: '开始日期-结束日期',
            type: 'daterange',
            rangeSeparator: '-',
            startField: 'createDateBegin',
            endField: 'createDateEnd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          }
        },
        {
          type: 'datePicker',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          formItem: {
            prop: 'signEndDate',
            label: '签署截止日期',
            placeholder: '开始日期-结束日期',
            type: 'daterange',
            rangeSeparator: '-',
            startField: 'signEndDateBegin',
            endField: 'signEndDateEnd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          }
        },
        {
          type: 'datePicker',
          belong: [
            'ALL',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          formItem: {
            prop: 'signComplete',
            label: '签署完成日期',
            placeholder: '开始日期-结束日期',
            type: 'daterange',
            rangeSeparator: '-',
            startField: 'signCompleteDateBegin',
            endField: 'signCompleteDateEnd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          }
        },
        {
          type: 'datePicker',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          formItem: {
            prop: 'startDate',
            label: '合同开始日期',
            placeholder: '开始日期-结束日期',
            type: 'daterange',
            rangeSeparator: '-',
            startField: 'startDateBegin',
            endField: 'startDateEnd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          }
        },
        {
          type: 'datePicker',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          formItem: {
            prop: 'endDate',
            label: '合同到期日期',
            placeholder: '开始日期-结束日期',
            type: 'daterange',
            rangeSeparator: '-',
            startField: 'endDateBegin',
            endField: 'endDateEnd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          }
        },
        {
          type: 'select',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          formItem: {
            prop: 'createLegalId',
            label: '发起方企业',
            placeholder: '请选择企业',
            options: []
          }
        },
        {
          type: 'remoteSearchSelect',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          formItem: {
            prop: 'createBy',
            label: '发起人',
            placeholder: '请输入姓名/手机号',
            remoteMethod: query => {
              return new Promise(resolve => {
                const data = this.remoteSearchEmployee(query)
                resolve(data)
              })
            }
          }
        },
        {
          type: 'remoteSearchSelect',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          formItem: {
            prop: 'carbonCopyId',
            label: '抄送方',
            placeholder: '请输入姓名/手机号',
            remoteMethod: query => {
              return new Promise(resolve => {
                const data = this.remoteSearchEmployee(query)
                resolve(data)
              })
            }
          }
        }
      ],
      // 表格上方按钮
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '签署',
          props: {
            type: 'plain'
          },
          style: {
            marginRight: '-6px'
          },
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          click: () => {
            this.handleSign(this.rows)
          }
        },
        {
          align: 'left',
          type: 'button',
          ifShow: () => hadPrivilege('contract2.signTask.examine'),
          props: {
            type: 'plain'
          },
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          label: '审核',
          style: {
            marginRight: '-6px'
          },

          click: () =>
            this.handleAudit(this.$refs['o-table'].$children[1].selection)
        },
        {
          align: 'left',
          type: 'button',
          ifShow: () => hadPrivilege('contract2.signTask.urge'),
          label: '催办',
          props: {
            type: 'plain'
          },
          style: {
            marginRight: '-6px'
          },
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          click: () =>
            this.handlePrompt(this.$refs['o-table'].$children[1].selection)
        },
        {
          align: 'left',
          type: 'button',
          ifShow: () => hadPrivilege('contract2.signTask.recall'),
          label: '撤回',
          props: {
            type: 'plain'
          },
          style: {
            marginRight: '-6px'
          },
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          click: () => {
            this.handleWithdraw(this.$refs['o-table'].$children[1].selection)
          }
        },
        {
          align: 'left',
          ifShow: () => hadPrivilege('contract2.signTask.download'),
          type: 'button',
          label: '下载',
          props: {
            type: 'plain',
            loading: false
          },
          style: {
            marginRight: '-6px'
          },
          belong: [
            'ALL',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          click: ({ props }) => {
            if (this.rows.length > 0) {
              this.checkDownloadContractStatus(props)
              return
            }
            handleError({ message: '请选择至少一条签署完成的合同' })
          }
        },
        {
          align: 'left',
          type: 'button',
          ifShow: () => hadPrivilege('contract2.signTask.export'),
          props: {
            type: 'plain'
          },
          label: '导出明细',
          style: {
            marginRight: '16px'
          },
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ],
          click: () => this.exportContract()
        },

        {
          align: 'right',
          type: 'button',
          label: '使用模板发起',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME',
            'DRAFT'
          ],
          style: {
            marginRight: '0px'
          },

          click: () => this.$router.push('/templates')
        },
        {
          align: 'right',
          label: '刷新',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'COMPLETE',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME',
            'DRAFT'
          ],
          style: {
            marginRight: '16px'
          },
          props: {
            type: 'plain'
          },
          icon: 'el-icon-refresh',
          click: () => {
            this.onSearch()
            this.loadSignsingCount()
          }
        }
      ],
      // 表头
      originTableHeader: [
        {
          prop: 'name',
          label: '合同名称',
          width: 200,
          fixed: true,
          render: (h, row) => {
            return h(ContactName, {
              props: {
                row,
                user: this.user,
                back: '/signings'
              }
            })
          }
        },
        {
          label: '合同类型',
          prop: 'contractType',
          width: '160px'
        },
        {
          label: '签署方',
          prop: 'signerList',
          width: '250px',
          showOverflowTooltip: false,
          render: (h, row) => {
            if (this.group === 'DRAFT') {
              return h(DraftEmpList, {
                props: {
                  value: row.signerList
                }
              })
            } else {
              return h(SignProcessList, {
                props: {
                  value: {
                    signerList: row.signerList,
                    writeProcessList: row.writeProcessList,
                    signProcessList: row.signProcessList,
                    handlingBy: row.handlingBy
                  }
                }
              })
            }
          }
        },
        {
          label: '签署状态',
          prop: 'status',
          width: '110px',
          render: (h, row) => {
            if (this.group === 'DRAFT' && !row.status) {
              return h(TextWithDot, {
                props: {
                  color: 'blue',
                  text: '创建中'
                }
              })
            }
            return h(SignerStatusWithDot, {
              props: {
                value: row.status
              }
            })
          }
        },
        {
          label: '签署截止日期',
          prop: 'sign_end_time',
          width: '110px',
          formatter: row => `
            <p>
              ${
                row.signEndTime
                  ? formatDateTime('yyyy-MM-dd', row.signEndTime)
                  : '不限制'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '发起时间',
          prop: 'create_time',
          type: 'DATE_MINUTE',
          draftHidden: true,
          formatter: row => `
            <p>
              ${
                row.createTime
                  ? formatDateTime('yyyy-MM-dd HH:mm', row.createTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '合同开始日期',
          prop: 'start_time',
          width: '110px',
          formatter: row => `
            <p>
              ${
                row.startTime
                  ? formatDateTime('yyyy-MM-dd', row.startTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '合同到期日期',
          prop: 'end_time',
          width: '110px',
          formatter: row => `
            <p>
              ${row.endTime ? formatDateTime('yyyy-MM-dd', row.endTime) : '-'}
            </p>
          `,
          sort: true
        },
        {
          label: '签署完成时间',
          prop: 'sign_finish_time',
          width: '130px',
          draftHidden: true,
          formatter: row => `
            <p>
              ${
                row.signFinishTime
                  ? formatDateTime('yyyy-MM-dd HH:mm', row.signFinishTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '抄送方',
          prop: 'carbonCopyList',
          render: (h, row) => {
            return h(CarbonCopyEmpList, {
              props: {
                value: row.carbonCopyList
              }
            })
          }
        },
        {
          label: '被证明人',
          prop: 'certifier',
          formatter: row => `
            <p>
              ${row.certifier && row.certifier.name ? row.certifier.name : '-'}
            </p>
          `
        },
        {
          label: '最近更新时间',
          prop: 'update_time',
          width: '130px',
          formatter: row => `
            <p>
              ${
                row.updateTime
                  ? formatDateTime('yyyy-MM-dd HH:mm', row.updateTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '合同编号',
          prop: 'no',
          draftHidden: true,
          width: '180px',
          sort: true
        }
      ],
      tableHeader: [],
      // 表格操作按钮
      actionButtons: [
        {
          label: '查看',
          id: '1',
          ifShow: row => {
            return verifyView(row.status, row.canSeeDetail)
          },
          click: row => this.preViewContract(row)
        },
        {
          label: '填写',
          ifShow: row => {
            return verifyWrite(row.status, this.user.id, row.handlingBy?.id)
          },
          id: '2',
          click: row =>
            this.$router.push(`contracts/${row.id}/write?back=/signings`)
        },
        {
          label: '审核',
          id: '3',
          ifShow: row => {
            return verifyAduit(row.status, this.user.id, row.handlingBy?.id)
          },
          click: row => {
            this.$router.push(`/contracts/${row.id}?back=/signings`)
          }
        },
        {
          label: '签署',
          id: '4',
          ifShow: row => {
            return verifySign(row.status, this.user.id, row.handlingBy?.id)
          },
          click: async row => {
            if (!this.user.isAuth) {
              this.$refs.verifiedDialog.open()
              return
            }
            const [err, r] = await client.signingCheckSignFile({
              body: {
                id: row.id
              }
            })
            if (err) {
              return handleError(err)
            }
            this.$router.push(`contracts/${row.id}/sign?back=/signings`)
          }
        },
        {
          label: '催办',
          id: '5',
          ifShow: row => {
            return verifyUrge(row.status, this.user.id, row.creator?.signer?.id)
          },
          click: row => this.handlePrompt(row)
        },
        {
          label: '撤回',
          id: '6',
          ifShow: row => {
            return verifyWithdraw(
              row.status,
              this.user.id,
              row.creator?.signer?.id
            )
          },
          click: row => this.handleWithdraw(row)
        },
        {
          label: '编辑',
          id: '7',
          ifShow: row => {
            return verifyEdit(row.status, this.user.id, row.creator?.signer?.id)
          },
          click: row =>
            this.$router.push(
              `/signings/drafts/${row.id}/step1/edit?source=DRAFT`
            )
        },
        {
          label: '下载',
          id: '8',
          ifShow: row => {
            return verifyDownload(
              row.status,
              this.user.id,
              row.writeProcessList,
              row.signProcessList
            )
          },
          click: row => {
            this.downloadContract(row)
          }
        },
        {
          label: '删除',
          id: '9',
          ifShow: row => {
            return verifyRemove(
              row.status,
              this.user.id,
              row.creator?.signer?.id
            )
          },
          click: row => this.removeDraft(row)
        },
        {
          label: '重新发起',
          id: '10',
          ifShow: row => {
            return verifyReissue(
              row.status,
              this.user.id,
              row.creator.signer.id
            )
          },
          click: row =>
            this.$router.push(
              `/signings/drafts/${row.id}/step1/edit?source=CONTRACT_REISSUE`
            )
        }
      ]
    }
  },
  watch: {
    group: {
      handler(newValue) {
        if (!newValue) newValue = 'ALL'
        // 如果是草稿则隐藏一些列信息
        if (newValue === 'DRAFT') {
          this.tableHeader = this.originTableHeader.filter(
            field => !field.draftHidden
          )
        } else {
          this.tableHeader = this.originTableHeader
        }
        // 更新表单参数
        this.$refs['top-select'].setFormJson(
          this.formJson.filter(field => field?.belong?.includes(newValue))
        )

        // 更新按钮
        const buttons = this.tableHeaderActionButtons.filter(button => {
          return button?.belong?.includes(newValue)
        })
        this.$refs['o-table'].context.setTableHeaderActionButtons(buttons)
      }
    }
  },

  computed: {
    timeStr() {
      const date = new Date()
      const year = date.getFullYear()
      const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate()
      const month =
        date.getMonth() < 10 ? `0${date.getMonth()}` : date.getMonth()
      const hours =
        date.getHours() < 10 ? `0${date.getHours()}` : date.getHours()
      const minutes =
        date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes()
      const seconds =
        date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds()
      return `${year}${month}${day}${hours}${minutes}${seconds}`
    }
  }
}
</script>
<style scoped>
::v-deep .table-header-button span {
  flex: 1;
}
::v-deep .o-field {
  margin: 0;
}
</style>