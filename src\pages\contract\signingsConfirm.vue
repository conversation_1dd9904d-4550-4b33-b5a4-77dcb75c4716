<template>
  <div
    :style="{
      height: 'calc(100vh - 16px)',
      overflowY: 'auto'
    }"
  >
    <div
      :style="{
        position: 'sticky',
        top: 0,
        background: '#fff',
        zIndex: 999
      }"
    >
      <TopBar
        title="批量签署"
        :steps="['确认文件信息', '选择签署印章']"
        :step="0"
        @back="back"
        @cancel="cancel"
        @next="next"
      />
    </div>
    <div style="padding: 0 24px">
      <p style="margin: 24px 0 12px 0">
        共 {{ tableData.length }} 个合同可以批量签署
      </p>
      <o-table
        ref="o-table"
        :actionButtons="actionButtons"
        :pagination="{ fixed: true }"
        :tableHeader="tableHeader"
        :showPagination="true"
        :tableData="tableData"
        :total="listTotal"
        @paginationChange="paginationChange"
        @sort-change="sortChange"
        emptyHeight="calc(100vh - 450px)"
      />
    </div>
  </div>
</template>
<script>
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import TopBar from '../../components/contract/signing/topBar.vue'
import formatDateTime from '../../formatters/dateTime'
import SignProcessList from '../../components/contract/signing/signProcessList.vue'
import CarbonCopyEmpList from '../../components/contract/signing/carbonCopyEmpList.vue'
import SignerStatusWithDot from '../../components/contract/signing/signerStatusWithDot.vue'
import ContactName from '../../components/contract/signing/contactName.vue'

const client = makeContractClient()
export default {
  name: 'SigningsConfirm',
  components: {
    TopBar,
    SignProcessList,
    CarbonCopyEmpList,
    SignerStatusWithDot
  },
  mounted() {
    this.accordSignIdList = JSON.parse(this.$route.query.accordSignIdList)
    this.limit =
      JSON.parse(
        window.localStorage.getItem('oTablePageCache_/signings/confirm')
      )?.data || 20
    this.load()
  },
  data() {
    return {
      accordSignIdList: [],
      sorts: [],
      tableHeader: [
        {
          prop: 'name',
          label: '合同名称',
          width: 200,
          fixed: true,
          render: (h, row) => {
            return h(ContactName, {
              props: {
                row,
                user: {},
                back: `/${window.location.pathname.replace(
                  window.env.staticPath,
                  ''
                )}${window.location.search}`
              }
            })
          }
        },
        {
          label: '合同类型',
          prop: 'contractType',
          width: '160px'
        },
        {
          label: '签署方',
          prop: 'signerList',
          width: '250px',
          showOverflowTooltip: false,
          render: (h, row) => {
            if (this.group === 'DRAFT') {
              return h(DraftEmpList, {
                props: {
                  value: row.signerList
                }
              })
            } else {
              return h(SignProcessList, {
                props: {
                  value: {
                    signerList: row.signerList,
                    writeProcessList: row.writeProcessList,
                    signProcessList: row.signProcessList,
                    handlingBy: row.handlingBy
                  }
                }
              })
            }
          }
        },
        {
          label: '签署状态',
          prop: 'status',
          width: '110px',
          render: (h, row) => {
            if (this.group === 'DRAFT' && !row.status) {
              return h(TextWithDot, {
                props: {
                  color: 'blue',
                  text: '创建中'
                }
              })
            }
            return h(SignerStatusWithDot, {
              props: {
                value: row.status
              }
            })
          }
        },
        {
          label: '签署截止日期',
          prop: 'sign_end_time',
          width: '110px',
          formatter: row => `
            <p>
              ${
                row.signEndTime
                  ? formatDateTime('yyyy-MM-dd', row.signEndTime)
                  : '不限制'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '发起时间',
          prop: 'create_time',
          type: 'DATE_MINUTE',
          draftHidden: true,
          formatter: row => `
            <p>
              ${
                row.createTime
                  ? formatDateTime('yyyy-MM-dd HH:mm', row.createTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '合同开始日期',
          prop: 'start_time',
          width: '110px',
          formatter: row => `
            <p>
              ${
                row.startTime
                  ? formatDateTime('yyyy-MM-dd', row.startTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '合同到期日期',
          prop: 'end_time',
          width: '110px',
          formatter: row => `
            <p>
              ${row.endTime ? formatDateTime('yyyy-MM-dd', row.endTime) : '-'}
            </p>
          `,
          sort: true
        },
        {
          label: '签署完成时间',
          prop: 'sign_finish_time',
          width: '130px',
          draftHidden: true,
          formatter: row => `
            <p>
              ${
                row.signFinishTime
                  ? formatDateTime('yyyy-MM-dd HH:mm', row.signFinishTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '抄送方',
          prop: 'carbonCopyList',
          render: (h, row) => {
            return h(CarbonCopyEmpList, {
              props: {
                value: row.carbonCopyList
              }
            })
          }
        },
        {
          label: '被证明人',
          prop: 'certifier',
          formatter: row => `
            <p>
              ${row.certifier && row.certifier.name ? row.certifier.name : '-'}
            </p>
          `
        },
        {
          label: '最近更新时间',
          prop: 'update_time',
          width: '130px',
          formatter: row => `
            <p>
              ${
                row.updateTime
                  ? formatDateTime('yyyy-MM-dd HH:mm', row.updateTime)
                  : '-'
              }
            </p>
          `,
          sort: true
        },
        {
          label: '合同编号',
          prop: 'no',
          draftHidden: true,
          width: '180px',
          sort: true
        }
      ],
      actionButtons: [
        {
          label: '查看',
          id: '1',
          click: row => {
            this.$router.push({
              path: `/contracts/${row.id}`,
              query: {
                back: `/${window.location.pathname.replace(
                  window.env.staticPath,
                  ''
                )}${window.location.search}`
              }
            })
          }
        }
      ],
      tableData: [
        {
          id: 11,
          name: '',
          creator: {
            signerType: '2',
            legal: {
              id: 2030,
              name: ''
            },
            signer: {
              id: 103207,
              name: '',
              mobile: ''
            }
          },
          contractType: '',
          signerList: [],
          signEndTime: '',
          carbonCopyList: [],
          certifierList: [],
          startTime: null,
          endTime: null,
          updater: {
            id: 103207,
            name: ''
          },
          updateTime: ''
        }
      ],
      listTotal: 0,
      start: 1,
      limit: 20
    }
  },
  methods: {
    sortChange(sorts) {
      this.sorts = sorts
      this.load()
    },
    async load() {
      const [err, r] = await client.signingQueryContract({
        body: {
          limit: this.limit,
          start: this.start,
          sorts: this.sorts,
          withDeleted: true,
          withDisabled: true,
          withTotal: true,
          filters: {
            contractIdList: this.accordSignIdList
          }
        }
      })

      if (err) {
        handleError(err)
        return
      }
      this.tableData = r.data.list
      this.listTotal = r.data.total
    },
    next() {
      this.$router.push({
        path: '/signings/sign',
        query: { accordSignIdList: JSON.stringify(this.accordSignIdList) }
      })
    },
    back() {
      this.$router.push('/signings')
    },
    cancel() {
      this.$router.push('/signings')
    },
    paginationChange({ start, limit }) {
      this.start = start
      this.limit = limit
      this.load()
    }
  }
}
</script>
<style scoped>
</style>