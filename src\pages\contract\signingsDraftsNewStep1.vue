<template>
  <div
    :style="{
      height: 'calc(100vh - 16px)',
      overflowY: 'auto'
    }"
  >
    <div
      :style="{
        position: 'sticky',
        top: 0,
        background: '#fff',
        zIndex: 999
      }"
    >
      <TopBar
        title="使用模板发起"
        :step="0"
        :steps="['设置合同签署信息', '预览填写合同']"
        @back="back"
        @save="save"
        @next="next"
      />
    </div>
    <div
      :style="{
        padding: '30px 40px',
        width: '1360px',
        margin: '0 auto',

        boxSizing: 'border-box'
      }"
    >
      <el-form
        ref="templatesForm"
        label-position="top"
        :model="signing"
        :style="{
          display: 'flex',
          flexWrap: 'wrap'
        }"
        :rules="rules"
      >
        <el-form-item
          prop="fileList"
          :style="{
            width: '100%'
          }"
        >
          <Title :title="`合同基本信息`" />
          <div style="position: relative; top: 15px">
            <span style="color: #24262a; height: 14px; line-height: 14px">
              待签署文件 ({{ signing.fileList.length }}份)
            </span>
            <el-tooltip
              placement="top"
              content="使用模板创建合同，模板中的签署文档不可更改"
            >
              <i
                class="olading-iconfont oi-wenhao"
                style="
                  color: #7f7f7f;
                  cursor: pointer;
                  vertical-align: middle;
                  padding-left: 5px;
                "
              ></i>
            </el-tooltip>
          </div>

          <FileList
            v-if="signing.fileList.length > 0"
            v-model="signing.fileList"
          />
        </el-form-item>
        <el-form-item
          label="合同名称"
          prop="name"
          :style="{
            flex: '0 0 580px',
            marginRight: '100px'
          }"
        >
          <el-input
            maxlength="50"
            @input="isEdit = true"
            v-model="signing.name"
          />
        </el-form-item>
        <el-form-item
          class="legalId-select"
          label="发起方公司主体"
          prop="legalId"
          :style="{
            flex: '0 0 580px'
          }"
        >
          <PlatformMerchantSelect
            v-model="signing.legalId"
            @input="handleLegalInput"
            @change="isEdit = true"
          />
        </el-form-item>

        <el-form-item
          :style="{
            flex: '0 0 580px',
            marginRight: '100px'
          }"
          prop="signDeadDay"
        >
          <span>签署截止日期</span>
          <span
            :style="{
              color: 'red'
            }"
          >
            *
          </span>

          <i
            @click="$refs.signDeadDayDialog.open()"
            class="olading-iconfont oi-wenhao"
            style="color: #7f7f7f; cursor: pointer; margin: 0 12px 0 6px"
          />
          <el-switch v-model="signing.enableDeadline" @input="isEdit = true">
          </el-switch>
          <br />
          <div style="display: flex">
            <el-date-picker
              :disabled="!signing.enableDeadline"
              v-model="signing.signDeadDay"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="选择日期"
              @input="isEdit = true"
            >
            </el-date-picker>
          </div>
        </el-form-item>
        <el-form-item
          prop="approveId"
          :style="{
            flex: '0 0 580px'
          }"
        >
          <div>
            关联审核流程
            <span
              :style="{
                color: 'red',
                marginRight: '12px'
              }"
              >*</span
            >
            <el-switch v-model="signing.enableApprove" @input="isEdit = true">
            </el-switch>
          </div>
          <ApprovalSelect
            ref="approveSelectRef"
            :disabled="!signing.enableApprove"
            :value="{
              id: signing.approveId
            }"
            v-if="isRenderChildCompontent"
            @input="selectApproval"
            @change="isEdit = true"
          />
          <br />
          <template
            v-if="
              signing.enableApprove &&
              hadPrivilege('contract2.contractSet.flowManagement.manage')
            "
          >
            管理合同流程，
            <a
              @click="jumpToApprovalPage"
              :style="{
                color: '#4f71ff',
                cursor: 'pointer'
              }"
            >
              点击这里
            </a>
          </template>
        </el-form-item>

        <el-form-item
          prop="signStepList"
          :style="{
            flex: '0 0 100%'
          }"
        >
          <Title title="设置签署方" style="margin: 50px 0 20px 0" />
          <Signatures
            :isDraftEdit="isDraftEdit"
            v-model="signing.signStepList"
            @clearValid="clearValidate('signStepList')"
            @change="isEdit = true"
          />
        </el-form-item>

        <el-form-item
          prop="carbonCopyList"
          :style="{
            flex: '0 0 620px'
          }"
        >
          合同抄送方
          <el-tooltip content="发起签署后发送至抄送方，最多可添加30人">
            <i class="olading-iconfont oi-wenhao" style="color: #7f7f7f" />
          </el-tooltip>

          <CarbonCopies
            v-model="signing.carbonCopyList"
            @remove="validate('carbonCopyList')"
            @change="isEdit = true"
          />
        </el-form-item>
        <Title title="其他设置" style="margin-top: 50px" />
        <el-form-item
          prop="certifiedUserIds"
          v-if="signing.certifier"
          :style="{
            flex: '0 0 100%'
          }"
        >
          <div>
            被证明人:
            <span
              :style="{
                color: 'red',
                marginRight: '15px'
              }"
            >
              *
            </span>

            <PlatformEmployeeMultipleSelect
              v-model="signing.certifiedUserIds"
              @change="isEdit = true"
              @input="
                newValue => {
                  $forceUpdate()
                }
              "
            />
          </div>
        </el-form-item>
        <el-form-item>
          <div>
            合同附件
            <el-tooltip
              class="attach-tooltip"
              effect="dark"
              placement="top-start"
            >
              <div slot="content">
                附件仅供查阅不签署，不计入合同签署数量；最多20份文件，<br />支持doc、docx、wps、jpg、jpeg、png、pdf、xls、xlsx、<br />zip、rar、mp4、amr、mp3、wav格式，单份文件不超过50M
              </div>
              <i class="olading-iconfont oi-wenhao" style="color: #7f7f7f" />
            </el-tooltip>
            <div
              style="
                font-size: 12px;
                color: #a8acba;
                height: 20px;
                position: relative;
                top: -20px;
              "
            >
              附件仅供查阅不签署，不计入合同签署数量
            </div>
          </div>

          <Uploader
            style="display: inline"
            ref="uploader"
            @handleProgress="handleAccessoryProgress"
            @handleSuccess="handleAccessorySuccess"
            @handleUploadError="handleAccessoryError"
          />
          ({{ signing.attachmentList ? signing.attachmentList.length : 0 }}/20)
          <ContractFileList
            :uploadRef="$refs.uploader"
            v-if="signing.attachmentList && signing.attachmentList.length > 0"
            v-model="signing.attachmentList"
            @input="isEdit = true"
            :showOperateBtn="false"
          />
        </el-form-item>
      </el-form>
      <SignDeadDayDialog ref="signDeadDayDialog" />
    </div>
  </div>
</template>
<script>
import TopBar from '../../components/contract/template/topBar.vue'
import Title from '../../components/contract/title.vue'
import ContractFileList from './templatesNewStep1/contractFileList.vue'
import ApprovalSelect from './typesNew/approveSelect.vue'
import Signatures from './signingsDraftsNewStep1/signatures.vue'
import SignDeadDayDialog from '../../components/contract/signingDraft/signDeadDayDialog.vue'
import CarbonCopies from './templatesNewStep1/carbonCopies.vue'
import Uploader from './templatesNewStep1/uploader.vue'
import PlatformMerchantSelect from './signingsDraftsNewStep1/platformMerchantSelect.vue'
import PlatformEmployeeMultipleSelect from './signingsDraftsNewStep1/platformEmployeeMultipleSelect.vue'
import FileList from './signingsDraftsNewStep1/fileList.vue'
import makeContractClient from '../../services/contract/makeClient'
import {
  carbonCopyListValid,
  signStepListValid
} from './signingsDraftsNewStep1/validator'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import { hadPrivilege } from '../../helpers/profile'

const client = makeContractClient()

export default {
  components: {
    TopBar,
    Title,
    ApprovalSelect,
    Signatures,
    CarbonCopies,
    ContractFileList,
    Uploader,
    SignDeadDayDialog,
    PlatformMerchantSelect,
    PlatformEmployeeMultipleSelect,
    FileList
  },
  computed: {
    isDraftEdit() {
      // DRAFT-草稿编辑，TEMPLATE-模板列表，点击使用发起；CONTRACT_REISSUE-合同重新发起；CONTRACT_TERMINATE-合同解约发起；CONTRACT_RENEWAL-合同续约发起；CONTRACT_MODIFY-合同变更发起
      const source = this.$route.query.source
      if (source === 'DRAFT' || !source) {
        return true
      }
      return false
    }
  },
  async created() {
    this.id = this.$route.params.id
    this.isRenderChildCompontent = false
    if (this.id) {
      // 编辑
      // DRAFT-草稿编辑，TEMPLATE-模板列表，点击使用发起；CONTRACT_REISSUE-合同重新发起；CONTRACT_TERMINATE-合同解约发起；CONTRACT_RENEWAL-合同续约发起；CONTRACT_MODIFY-合同变更发起
      this.source = this.$route.query.source
      this.templateId = this.$route.query.templateId
      this.sourceData = {
        source: this.source,
        sourceId: this.id,
        templateId: this.templateId
      }
    } else {
      // 新建
      this.templateId = this.$route.query.templateId
      this.sourceData = {
        templateId: this.templateId,
        source: 'TEMPLATE',
        sourceId: this.templateId
      }
    }
    const [err, r] = await client.signingGetDraft({
      body: {
        ...this.sourceData
      }
    })
    if (err) {
      handleError(err)
      return
    }

    this.signing = r.data
    this.isRenderChildCompontent = true
    if (this.signing.certifiedUserList?.length > 0) {
      this.signing.certifiedUserIds = this.signing.certifiedUserList.map(user =>
        Number(user.id)
      )
    }
    // legalId回显的值在createor对象中
    if (this.id) {
      this.signing.legalId =
        this.signing.creator && this.signing.creator.legal.id * 1
    }
    // 给签署方列表赋值key 防止频繁的重新渲染
    this.signing.signStepList = this.signing.signStepList.map(sign => ({
      ...sign,
      key: Math.random()
    }))
    // carbonCopyList 抄送人回显参数与请求参数不一致需要转换
    this.signing.carbonCopyList = this.signing.carbonCopyList.map(
      carbonCopy => ({
        type: carbonCopy.signerType,
        userId: Number(carbonCopy.signer.id)
      })
    )
    // 为符合组件内数据格式，需要把回显内容格式转换下
    if (this.signing.attachmentList && this.signing.attachmentList.length > 0) {
      this.signing.attachmentList = this.signing.attachmentList.map(attach => ({
        archiveFile: attach
      }))
    } else {
      this.signing.attachmentList = []
    }
  },

  methods: {
    handleLegalInput(selectLegalId) {
      setTimeout(() => {
        this.signing.legalId = selectLegalId
        this.clearValidate('legalId', selectLegalId)
        if (this.firstChange) {
          this.firstChange = false
          const signStepList = this.signing.signStepList.map(sign => {
            return sign.signerType == '2'
              ? { ...sign, legalId: selectLegalId }
              : sign
          })
          this.signing.signStepList = signStepList
          this.clearValidate('signStepList')
        }
        this.$forceUpdate()
      })
    },

    // 附件上传中
    handleAccessoryProgress(res, file, fileList) {
      console.log(res, file, fileList)
      const currentFile = this.signing.attachmentList.find(
        curFile => curFile.archiveFile && curFile.archiveFile.uid === file.uid
      )
      if (currentFile) {
        currentFile.percent = res.percent
      } else {
        this.signing.attachmentList.push({
          archiveFile: { name: file.name, uid: file.uid },
          key: Math.random(),
          percent: res.percent
        })
      }
    },
    // 附件上传成功
    handleAccessorySuccess(res, file, fileList) {
      if (!res.success) {
        this.handleAccessoryError(res, file, fileList)
        return
      }
      this.isEdit = true
      // 上传成功后替换成真实的数据
      this.signing.attachmentList = this.signing.attachmentList.map(
        fileList => {
          if (fileList.archiveFile.uid === file.uid) {
            return {
              ...fileList,
              archiveFile: {
                archiveId: res?.data?.archiveId,
                name: res?.data?.name,
                size: res?.data?.size
              }
            }
          } else {
            return fileList
          }
        }
      )
      this.isEdit = true
      handleSuccess('文件上传成功')
    },
    handleAccessoryError(res, file, fileList) {
      setTimeout(() => {
        this.signing.attachmentList = this.signing.attachmentList.filter(
          fileList => fileList.archiveFile.uid != file.uid
        )
        this.$refs.uploader.$refs.uploader.uploadFiles.splice(0, 1)
        handleError({ message: res.message })
      }, 500)
    },
    async save() {
      this._save(() => this.$router.push('/signings'))
    },

    next() {
      const _this = this
      this._save(() => {
        _this.$router.push(`/signings/drafts/${_this.id}/step2/edit`)
      }, true)
    },
    async _save(cb, isNext) {
      for (let file of this.signing.attachmentList) {
        if (
          !(file.archiveFile && file.archiveFile.archiveId) &&
          !file.archiveId
        ) {
          handleError('文件上传中，请上传成功后保存！')
          return
        }
      }

      this.$refs.templatesForm.validate(async valid => {
        if (!valid) {
          this.scrollIntoError(this.$refs.templatesForm)
          this.$message({
            type: 'error',
            message: '请检查下方输入项'
          })
          return
        }
        this.isEdit = false

        // 格式化合同附件数据格式
        let attachmentList = []
        if (this.signing.attachmentList.length > 0) {
          attachmentList = this.signing.attachmentList.map(file => ({
            ...file.archiveFile
          }))
        }
        if (this.source === 'DRAFT') {
          // 修改第一步草稿
          const [err, r] = await client.signingUpdateDraft({
            body: {
              ...this.signing,
              attachmentList
            }
          })
          if (err) {
            handleError(err)
            return
          }
          if (cb) {
            cb()
          }
          if (!isNext) {
            handleSuccess('修改草稿成功')
          }
          return
        }
        // 保存第一步草稿

        const [err2, r2] = await client.signingSaveDraft({
          body: {
            ...this.signing,
            attachmentList,
            ...this.sourceData
          }
        })
        if (err2) {
          handleError(err2)
          return
        }

        this.id = r2.data.id
        if (!isNext) {
          handleSuccess('保存草稿成功')
        }

        if (cb) {
          cb()
        }
      })
    },
    selectApproval(selectedApproval) {
      this.signing.approveId = selectedApproval.id
    },
    // 跳转到流程页面
    jumpToApprovalPage() {
      const routers = this.$router.resolve({ path: '/approvals' })
      window.open(routers.href, '_blank')
    },
    // 返回
    back() {
      if (!this.signing.name || !this.isEdit) {
        this.goBack()
        return
      }
      this.$confirm('是否保存对合同的更改？', `返回提示？`, {
        type: 'warning',
        confirmButtonText: '保存草稿',
        cancelButtonText: '不保存',
        distinguishCancelAndClose: true,
        closeOnClickModal: false
      })
        .then(async () => {
          this._save(() => {
            this.goBack()
          })
        })
        .catch(action => {
          if (action === 'cancel') {
            this.goBack()
          }
        })
    },
    goBack() {
      const isDraftEdit = this.$route.query.source === 'DRAFT'
      if (isDraftEdit) {
        this.$router.push('/signings?group=DRAFT')
        return
      }
      const params = new URLSearchParams(location.search)
      const back = params.get('back')
      if (back && back.includes('http')) {
        location.href = back
        return
      }
      if (back && !back.includes('http')) {
        this.$router.push(back)
        return
      }

      this.$router.go(-1)
    },
    // 清除校验
    clearValidate(prop) {
      this.$nextTick(() => {
        this.$refs.templatesForm.clearValidate(prop)
      })
    },
    // 清除校验
    validate(prop) {
      this.$nextTick(() => {
        this.$refs.templatesForm.validateField(prop)
      })
    },
    hadPrivilege
  },

  data() {
    return {
      firstChange: true, //第一次修改发起方公司 签署方的法人实体同步改变
      id: '',
      sourceData: {},
      isEdit: false,
      signing: {
        legalId: '',
        templateId: '',
        draftId: '',
        relateContractId: '',
        fileList: [],
        attachmentList: [],
        name: '',
        signDeadDay: '',
        contractTypeId: null,
        enableApprove: false,
        approveId: null,
        certifiedUserList: [],
        certifiedUserIds: [],
        signStepList: [
          {
            key: '1',
            signerType: '1',
            needWrite: true,
            needSign: true,
            name: '签署方1',
            signerUserId: 0,
            signWay: '1',
            participateType: 0,
            userIdList: []
          },
          {
            key: '2',
            signerType: '2',
            needWrite: true,
            needSign: true,
            name: '签署方2',
            signerUserId: 0,
            signWay: '1',
            participateType: 0,
            userIdList: []
          }
        ],
        carbonCopyList: [],
        numberRuleMessage: '',
        approveErrorMessage: ''
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ],
        legalId: [
          {
            required: true,
            message: '请选择发起方公司主体',
            trigger: 'blur'
          }
        ],
        // 关联审核流程
        approveId: [
          {
            validator: (rule, value, callback) => {
              if (this.signing.enableApprove && this.approveErrorMessage) {
                return callback(this.approveErrorMessage)
              }
              if (this.signing.enableApprove) {
                if (!value) return callback('请选择关联审核流程')
              }
              callback()
            }
          }
        ],
        signStepList: [{ validator: signStepListValid }],
        carbonCopyList: [{ validator: carbonCopyListValid }],
        certifiedUserIds: [
          {
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                return callback(new Error('请选择被证明人'))
              }
              callback()
            }
          }
        ],
        signDeadDay: [
          {
            validator: (rule, value, callback) => {
              if (this.signing.enableDeadline) {
                if (!value) return callback('请选择签署截止日期')
              }
              callback()
            }
          }
        ]
      },
      signDeadDayDialog: false,
      templateId: '',
      isRenderChildCompontent: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      }
    }
  }
}
</script>
<style scoped>
::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
}
::v-deep .legalId-select .el-select {
  width: 100% !important;
}
::v-deep .el-input__inner {
  border-color: #dcdfe6 !important;
}
</style>