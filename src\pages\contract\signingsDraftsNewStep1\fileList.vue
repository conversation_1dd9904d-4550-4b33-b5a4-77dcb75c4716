<template>
  <div
    style="
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 12px;
    "
  >
    <div
      style="
        width: 580px;
        opacity: 0.8;
        background: #f7fafd;
        border-radius: 8px;
        margin-bottom: 10px;
        padding: 0 16px;
        box-sizing: border-box;
        color: #24262a;
        height: 44px;
      "
      :key="index"
      v-for="(item, index) in value"
    >
      <div style="position: relative">
        <div
          style="
            display: flex;
            justify-content: space-between;
            overflow: hidden;
          "
        >
          <span
            style="
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              width: 238px;
              display: inline-block;
            "
            :title="item.name"
            >{{ item.name }}</span
          >
          <div>
            <el-button
              class="list-icon"
              title="预览"
              @click="previewFile(item, index)"
              type="text"
              ><i class="el-icon-view"
            /></el-button>
            <el-button
              class="list-icon"
              title="下载"
              @click="downloadFile(item, index)"
              type="text"
              ><i class="el-icon-download"
            /></el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import makePlatformClient from '../../../services/platform/makeClient'
import makeContractClient from '../../../services/contract/makeClient'
const pclient = makePlatformClient()
const client = makeContractClient()
export default {
  name: 'ContractFileList',

  props: {
    value: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {}
  },
  methods: {
    // 下载文件
    async downloadFile(file, index) {
      const id = file.archiveId
      const name = file.name
      const [err, r] = await pclient.platformDownloadFile(
        {
          method: 'GET',
          headers: { 'content-type': 'application/octet-stream' }
        },
        { id, name }
      )
      if (err) {
        console.log(err, 'errrrrrr')
        return
      }
      console.log(r, 'errrrrrrrrrr')
      window.open(r.url)
    },
    // 预览文件
    async previewFile(file, index) {
      const id = file.archiveId
      const [err, r] = await client.fileInfo({
        body: {
          id
        }
      })
      if (err) {
        handleError(err)
        return
      }
      const url = r.data.url
      window.open(url)
    }
  }
}
</script>

<style scoped>
.list-icon {
  color: #777c94;
  vertical-align: middle;
  font-size: 20px;
}
</style>