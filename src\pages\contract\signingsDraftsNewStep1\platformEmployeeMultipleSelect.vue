<template>
  <el-select
    :style="styleStr"
    remote
    filterable
    :disabled="disabled"
    placeholder="请选择"
    :loading="loading"
    :remote-method="search"
    :value="value"
    multiple
    @change="handleChange"
    collapse-tags
    v-bind="$attrs"
  >
    <el-option
      :key="employee.id"
      v-for="employee in employees"
      :label="`${employee.name} (${employee.cellPhone})`"
      :value="employee.userId"
    >
    </el-option>
  </el-select>
</template>
<script>
import handleError from '../../../helpers/handleError'
import makePlatformClient from '../../../services/platform/makeClient'
const pclient = makePlatformClient()
export default {
  props: {
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default() {
        return false
      }
    },
    value: {
      type: Array
    },
    styleStr: {
      type: String,
      default: 'width: 230px'
    }
  },
  mounted() {
    let filters = {}
    if (this.value && this.value.length > 0) {
      filters = { userId: this.value }
    } else {
      filters = {}
    }
    this.loadEmployees({
      start: 0,
      limit: 100,
      filters
    })
  },
  methods: {
    search(query) {
      this.loadEmployees({
        start: 0,
        limit: 100,
        filters: {
          keywords: query,
          withDeptMember: true
        }
      })
    },
    async loadEmployees(filters = {}) {
      const [err, r] = await pclient.platformListMerchantMember({
        body: filters
      })
      if (err) {
        handleError(err)
        return
      }

      this.loading = false

      this.employees = r.data.list
      return r.data.list
    },
    handleChange(newValue) {
      this.$emit('input', newValue)
      this.$emit('change')
    }
  },
  data() {
    return {
      loading: true,
      employees: []
    }
  }
}
</script>
<style>
/* .el-select__tags {
  flex-wrap: nowrap;
  overflow: hidden;
  max-width: 100% !important;
}
.el-select__tags-text {
  display: inline-block;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
} */
</style>