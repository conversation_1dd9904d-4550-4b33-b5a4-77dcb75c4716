<template>
  <el-select
    :style="styleStr"
    remote
    filterable
    :disabled="disabled"
    placeholder="请选择"
    :loading="loading"
    :remote-method="search"
    :value="value"
    @change="handleChange"
  >
    <el-option
      :key="employee.id"
      v-for="employee in employees"
      :label="`${employee.name} (${employee.cellPhone})`"
      :value="employee.userId"
    >
    </el-option>
  </el-select>
</template>
<script>
import handleError from '../../../helpers/handleError'
import makePlatformClient from '../../../services/platform/makeClient'
const pclient = makePlatformClient()
export default {
  props: {
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    value: {
      type: Number
    },
    styleStr: {
      type: String,
      default: 'width: 210px'
    }
  },
  mounted() {
    if (!this.value) {
      this.loadEmployees({
        start: 0,
        limit: 100,
        filters: {}
      })
    } else {
      this.loadEmployees({
        start: 0,
        limit: 100,
        filters: { userId: [this.value] }
      })
    }
  },
  methods: {
    search(query) {
      this.loadEmployees({
        start: 0,
        limit: 100,
        filters: {
          keywords: query,
          withDeptMember: true
        }
      })
    },
    async loadEmployees(filters = {}) {
      const [err, r] = await pclient.platformListMerchantMember({
        body: filters
      })
      if (err) {
        handleError(err)
        return
      }

      this.loading = false

      this.employees = r.data.list
      return r.data.list
    },
    handleChange(newValue) {
      this.$emit('input', newValue)
      this.$emit('change')
    }
  },
  data() {
    return {
      loading: true,
      employees: []
    }
  }
}
</script>