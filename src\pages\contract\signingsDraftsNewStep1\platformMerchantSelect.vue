<template>
  <el-select :value="value" v-bind="$attrs" @change="handleChange">
    <el-option
      :key="legal.id"
      v-for="legal in legals"
      :value="legal.id"
      :label="legal.name"
    ></el-option>
  </el-select>
</template>

<script>
import makePlatformClient from '../../../services/platform/makeClient'
import handleError from '../../../helpers/handleError'
const pclient = makePlatformClient()
export default {
  name: 'platformMerchantSelect',
  props: {
    value: {
      type: Number | String
    }
  },
  data() {
    return {
      legals: []
    }
  },
  methods: {
    handleChange(id) {
      this.$emit('input', id)
      this.$emit('change')
    }
  },
  async created() {
    const [err, r] = await pclient.platformListLegal({
      body: {
        withTotal: true,
        filters: {
          legalUsageType: ['CONTRACT']
        }
      }
    })
    if (err) {
      handleError(err)
      return
    }
    this.legals = r.data.list
    //只有一个法人实体时，默认值为该法人实体。
    if (this.legals.length === 1) {
      this.$emit('input', this.legals[0].id)
      this.$emit('clearValid')
    }
  }
}
</script>

<style>
</style>