<template>
  <div class="signatures">
    <DragableList
      :columns="columns"
      :value="value"
      @input="n => $emit('input', n)"
    >
      <template v-slot="{ item, index }">
        <div>
          <el-select
            :class="
              item.signerType === SingerTypePerson
                ? 'person-select'
                : 'enterprise-select'
            "
            style="width: 100px"
            :value="item.signerType"
            disabled
            @change="
              type => {
                const n = [...value]
                n[index].signerType = type
                n[index].userIdList = null
                $emit('input', n)
              }
            "
          >
            <el-option value="1" label="个人"></el-option>
            <el-option value="2" label="企业"></el-option>
          </el-select>
        </div>
        <div>
          <el-input
            maxlength="50"
            style="width: 150px"
            :value="item.name"
            disabled
            @input="
              name => {
                const n = [...value]
                n[index].name = name
                $emit('input', n)
              }
            "
          ></el-input>
        </div>
        <div style="position: relative">
          <el-checkbox
            :value="item.needWrite"
            disabled
            @input="
              needWrite => {
                const n = [...value]
                n[index].needWrite = needWrite
                $emit('input', n)
              }
            "
            >填写</el-checkbox
          >
          <el-checkbox
            style="position: relative"
            disabled
            :value="item.needSign"
            @input="
              needSign => {
                const n = [...value]
                n[index].needSign = needSign
                $emit('input', n)
              }
            "
            >签署
          </el-checkbox>
          <el-tooltip
            content="当前签署顺序，列表拖拽可调整排序"
            placement="top"
          >
            <div
              v-if="value.length > 1"
              style="
                position: absolute;
                width: 15px;
                height: 15px;
                background: #4f71ff;
                color: #fff;
                border-radius: 50%;
                font-size: 12px;
                top: 5px;
                right: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
              "
            >
              {{ index + 1 }}
            </div>
          </el-tooltip>
        </div>
        <div>
          <div style="display: flex">
            <span
              v-if="item.signerType === SingerTypeCompany"
              style="margin-right: 8px"
            >
              <PlatformMerchantSelect
                :value="item.legalId"
                :disabled="!isDraftEdit"
                @input="
                  legalId => {
                    const n = [...value]
                    n[index].legalId = legalId
                    $emit('input', n)
                  }
                "
                @change="$emit('change')"
                @clearValid="$emit('clearValid')"
                v-model="item.legalId"
              />
            </span>
            <!-- -参与方及人员-企业 公司主体禁用，人员可更改 -->
            <PlatformEmployeeMultipleSelect
              :disabled="!isDraftEdit && item.signerType === SingerTypePerson"
              :value="item.userIdList"
              :multipleLimit="item.signerType === SingerTypePerson ? 0 : 1"
              @change="$emit('change')"
              @input="
                userIdList => {
                  const n = [...value]
                  n[index].userIdList = userIdList
                  $emit('input', n)
                }
              "
            />
          </div>
        </div>
        <div>
          <el-select
            :value="item.signWay"
            @change="
              signWay => {
                const n = [...value]
                n[index].signWay = signWay
                $emit('input', n)
                $emit('change')
              }
            "
            style="width: 150px"
            v-if="item.signerType === SingerTypePerson"
          >
            <el-option value="1" label="不限制签名方式"></el-option>
            <el-option value="2" label="手绘签名"></el-option>
            <!-- <el-option value="2" label="系统标准签名"></el-option> -->
          </el-select>
          <span v-else> - </span>
        </div>
      </template>
    </DragableList>
  </div>
</template>
<script>
import DragableList from '../../../components/contract/draggableList.vue'
import PlatformEmployeeMultipleSelect from './platformEmployeeMultipleSelect.vue'
import PlatformMerchantSelect from '../signingsDraftsNewStep1/platformMerchantSelect.vue'
import {
  SingerTypePerson,
  SingerTypeCompany
} from '../../../services/contract/constants'
export default {
  components: {
    DragableList,
    PlatformMerchantSelect,
    PlatformEmployeeMultipleSelect
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    isDraftEdit: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      SingerTypePerson,
      SingerTypeCompany,
      columns: [
        {
          label: '参与主体',
          width: '120px'
        },
        {
          label: '参与方名称',
          width: '180px'
        },
        {
          label: '参与方式及顺序',
          width: '180px'
        },
        {
          label: '参与方及人员',
          width: 'auto'
        },
        {
          label: '签署要求',
          width: '180px'
        }
      ]
    }
  }
}
</script>
<style scoped>
.person-select,
.enterprise-select {
  margin-right: 24px;
}
::v-deep .person-select.el-button {
  background: #fff;
  border: 1px solid #ffac04;
  border-radius: 8px;
  color: #ffac04;
}
::v-deep .enterprise-select.el-button {
  background: #fff;
  border: 1px solid #4f71ff;
  border-radius: 8px;
  color: #4f71ff;
}
::v-deep .person-select .el-input--suffix .el-input__inner {
  width: 100px;
  background: rgba(255, 172, 4, 0.06) !important;
  border-radius: 8px;
  border: 1px solid #ffac04;
}
::v-deep .enterprise-select .el-input--suffix .el-input__inner {
  width: 100px;
  background: #f7fafd;
  border: 1px solid #4f71ff;
  border-radius: 8px;
}
::v-deep .draggableItem {
  border-bottom: 1px solid #eef0f4;
}
</style>