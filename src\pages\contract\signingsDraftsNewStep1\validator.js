import {
  SingerTypePerson,
  SingerTypeCompany
} from '../../../services/contract/constants'
export const signStepListValid = (rule, value, callback) => {
  for (let step of value) {
    if (!step.name) return callback(new Error('参与方名称不能为空'))
    if (!step.needSign && !step.needWrite)
      return callback(new Error('请至少选择一种参与方式及顺序'))
    if (!step.userIdList || (step.userIdList && step.userIdList.length) <= 0)
      return callback(new Error('请选择参与方及人员'))
    if (step.signerType === SingerTypeCompany && !step.legalId)
      return callback(new Error('请选择企业公司主体'))
    if (!step.signWay) return callback(new Error('请选择签署要求'))
  }
  // 签署方只能有一个参与主体选择多个人员
  let userCount = 0
  for (let signing of value) {
    if (signing.userIdList && signing.userIdList.length > 1) {
      userCount++
    }
    if (userCount > 1)
      return callback(new Error('签署方只能有一个参与主体选择多个人员'))
  }
  // 同一参与方相同企业，不能多次签署
  const enterpriseSignList = value.filter(
    signing => signing.signerType === SingerTypeCompany && signing.needSign
  )
  for (let signing of enterpriseSignList) {
    for (let signing2 of enterpriseSignList) {
      if (
        signing.legalId === signing2.legalId &&
        signing.userIdList[0] === signing2.userIdList[0] &&
        signing.key !== signing2.key
      ) {
        return callback(new Error('同一参与方相同企业，不能多次签署'))
      }
    }
  }

  // 同一参与方相同企业，不能多次填写
  const enterpriseWriteList = value.filter(
    signing => signing.signerType === SingerTypeCompany && signing.needWrite
  )
  for (let signing of enterpriseWriteList) {
    for (let signing2 of enterpriseWriteList) {
      if (
        signing.legalId === signing2.legalId &&
        signing.userIdList[0] === signing2.userIdList[0] &&
        signing.key !== signing2.key
      ) {
        return callback(new Error('同一参与方相同企业，不能多次填写'))
      }
    }
  }
  // 同一参与方相同人员，不能多次签署
  const personalSignList = value.filter(
    signing => signing.signerType === SingerTypePerson && signing.needSign
  )
  const userIdSignList = []
  for (let signing of personalSignList) {
    if (signing.userIdList) userIdSignList.push(...signing.userIdList)
  }
  const userIdSignList2 = Array.from(new Set(userIdSignList))
  if (userIdSignList.length !== userIdSignList2.length) {
    return callback(new Error('同一参与方相同人员，不能多次签署'))
  }

  // 同一参与方相同人员，不能多次填写
  const personalWriteList = value.filter(
    signing => signing.signerType === SingerTypePerson && signing.needWrite
  )
  const userIdWriterList = []
  for (let signing of personalWriteList) {
    if (signing.userIdList) userIdWriterList.push(...signing.userIdList)
  }
  const userIdWriterList2 = Array.from(new Set(userIdWriterList))
  if (userIdWriterList.length !== userIdWriterList2.length) {
    return callback(new Error('同一参与方相同人员，不能多次填写'))
  }

  callback()
}
export const carbonCopyListValid = (rule, value, callback) => {
  var userIds = {}
  console.log(value)
  for (let carbon of value) {
    const key = carbon.type + carbon.userId
    if (userIds[key]) {
      return callback(new Error('抄送人不能重复'))
    }
    userIds[key] = true
    if (!carbon.userId) return callback(new Error('合同抄送方不能为空'))
  }
  callback()
}