<template>
  <div
    class="templatesNewStep2"
    v-if="draftModifiableData"
    @mousedown="pageFieldBlur"
  >
    <div>
      <TopBar
        title="使用模板发起"
        :step="1"
        :steps="['设置合同签署信息', '预览填写合同']"
        @back="back"
        @prev="prev"
        @save="save"
        :submitBtnText="'发起签署'"
        @submit="submit"
      />
      <div
        :style="{
          display: 'flex'
        }"
      >
        <div
          class="webkit-scrollbar"
          :style="{
            flex: '0 0 240px',
            height: 'calc(100vh - 48px)',
            overflowY: 'auto',
            padding: '12px 24px',
            borderRight: '1px solid #EEF0F4',
            fontSize: '12px'
          }"
        >
          <el-collapse :value="['waitingSignatueFiles', 'attachmentList']">
            <el-collapse-item name="waitingSignatueFiles">
              <template slot="title">
                <div style="display: flex">
                  <Title
                    :title="`待签署文件 (${draftModifiableData.fileList.length}份)`"
                  />
                  <i
                    class="olading-iconfont oi-wenhao"
                    style="color: #7f7f7f; margin-left: 5px; cursor: default"
                    v-if="waitingSignatueFilesTipClosed"
                    title="多个文件时，可点击切换文件，针对每个文件设置填充域和签章区"
                  />
                </div>
              </template>
              <div
                v-if="
                  !waitingSignatueFilesTipClosed &&
                  draftModifiableData.fileList.length > 1
                "
                :style="{
                  borderRadius: '8px',
                  background: '#F8F8F8',
                  padding: '10px',
                  display: 'flex',
                  marginBottom: '10px'
                }"
              >
                <span
                  :style="{
                    color: '#777C94',
                    fontSize: '12px'
                  }"
                >
                  多个文件时，可点击切换文件，针对每个文件设置填充域和签章区
                </span>
                <i
                  class="el-icon-close"
                  style="cursor: pointer; margin-left: 10px"
                  @click="closeWaitingSignatueFilesTip"
                />
              </div>
              <WaitingSignatueFiles
                :files="draftModifiableData.fileList"
                :pageFields="pageFields"
                :fields="fields"
                :currentFileIndex="fileIndex"
                @select="selectFile"
              />
            </el-collapse-item>
            <el-collapse-item name="attachmentList">
              <template slot="title">
                <Title
                  :withPrefix="false"
                  :title="`附件 (${draftModifiableData.attachmentList.length}份)`"
                />
              </template>
              <div
                style="
                  background-color: #f8f8f8;
                  color: #777c94;
                  font-size: 12px;
                  padding: 14px 16px;
                  display: flex;
                  margin-bottom: 10px;
                  border-radius: 8px;
                "
                v-if="!attachmentFilesTipClosed"
              >
                <div>附件仅供查阅不签署，不计入合同签署数量。</div>
                <i
                  class="el-icon-close"
                  style="cursor: pointer; margin-left: 10px"
                  @click="closeAttachmentFilesTip"
                />
              </div>
              <Title :title="`发起方附件`" />
              <AttachmentFiles
                @remove="removeAttachmentFile"
                @download="downloadAttachmentFile"
                v-model="draftModifiableData.attachmentList"
              />
            </el-collapse-item>
          </el-collapse>
        </div>
        <div
          :style="{
            flex: '1 1 auto',
            width: '0px', //直接自动计算
            height: 'calc(100vh - 48px)',
            background: '#f2f2f2',
            overflow: 'hidden'
          }"
        >
          <div
            style="
              position: stricky;
              top: 46px;
              overflow: hidden;
              width: 100%;
              z-index: 1;
              background-color: #fffaf0;
              color: #e59900;
              font-size: 12px;
              padding: 14px 16px;
              text-align: center;
            "
            v-if="!tipClosed"
          >
            <i class="el-icon-info" />
            预览合同时，您可以对相关区域内容进行填写。
            <i
              class="el-icon-close"
              style="cursor: pointer; margin-left: 10px"
              @click="closeTip"
            />
          </div>
          <FilePages
            ref="filePages"
            :fileId="draftModifiableData.fileList[fileIndex].fileId"
            :images="draftModifiableData.fileList[fileIndex].archiveImageList"
            @scaleChange="scale => (currentScale = scale)"
          >
            <template v-slot="{ pageNo, fileId }">
              <FilePageFieldDraft
                :key="index"
                v-for="(pageField, index) in pageFields.filter(
                  item => item.pageNo === pageNo && item.fileId === fileId
                )"
                :field="fields.find(item => item.id === pageField.fieldId)"
                :pageField="pageField"
                :focusPageFiled="currentPageField"
                @focus="pageFieldFocus"
                :stepSigners="draftModifiableData.stepSignerList"
              />
            </template>
          </FilePages>
        </div>
        <div
          id="rightBox"
          class="webkit-scrollbar"
          :style="{
            flex: '0 0 240px',
            height: 'calc(100vh - 48px)',
            overflowY: 'auto',
            padding: '12px 24px',
            borderLeft: '1px solid #EEF0F4',
            fontSize: '12px'
          }"
        >
          <el-tabs v-model="activeName">
            <el-tab-pane label="合同签署信息" name="infos" class="infos">
              <ContractInfos :infos="draftModifiableData" />
              <el-collapse
                :value="[
                  'approvalProcesses',
                  'signatureProcesses',
                  'carbonCopies'
                ]"
              >
                <el-collapse-item name="approvalProcesses">
                  <template slot="title">
                    <Title :title="`审核流程`" />
                  </template>
                  <template>
                    <ApprovalProcesses
                      :creator="draftModifiableData.creator"
                      :users="draftModifiableData.approveUserList"
                    />
                  </template>
                </el-collapse-item>
                <el-collapse-item name="signatureProcesses">
                  <template slot="title">
                    <Title title="签署流程" />
                  </template>
                  <template>
                    <SignatureProcesses
                      :writeableUsers="draftModifiableData.needWriteUserList"
                      :signableUsers="draftModifiableData.needSignUserList"
                    />
                  </template>
                </el-collapse-item>
                <el-collapse-item
                  name="carbonCopies"
                  v-if="
                    draftModifiableData.carbonCopyList &&
                    draftModifiableData.carbonCopyList.length
                  "
                >
                  <template slot="title">
                    <Title title="抄送方" />
                  </template>
                  <template>
                    <CarbonCopies
                      :carbonCopies="draftModifiableData.carbonCopyList"
                    />
                  </template>
                </el-collapse-item>
              </el-collapse>
            </el-tab-pane>
            <el-tab-pane class="inputs" label="待填写内容" name="inputs">
              <Title title="填写" :withPrefix="false" style="display: inline" />
              <i
                class="olading-iconfont oi-wenhao"
                style="color: #7f7f7f; margin-left: 5px"
                @click="showFillTip"
                v-if="fillTipClosed"
              />

              <div
                class="tip"
                :style="{
                  display: 'flex',
                  background: '#F8F8F8',
                  padding: '10px',
                  borderRadius: '8px',
                  margin: '10px 0',
                  color: '#777C94',
                  fontSize: '12px'
                }"
                v-if="!fillTipClosed"
              >
                <div>
                  填写后不可更改，请仔细检查。若发现错误，在签署完成前，发起人可以撤回合同，重新发起签署。
                </div>
                <i
                  class="el-icon-close"
                  @click="close"
                  :style="{
                    cursor: 'pointer',
                    marginLeft: '10px'
                  }"
                />
              </div>

              <FilePageFieldInputs
                :fields="sortedFields"
                :pageFields="pageFields"
                :currentPageField="currentPageField"
                @fieldFocus="fieldInputFocus"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <div
      :style="{
        display: 'none'
      }"
    >
      <img
        @load="fileImageLoad($event, file.fileId)"
        :src="file.archiveImageList[0]"
        :key="file.fileId"
        v-for="file of draftModifiableData.fileList"
      />
    </div>
  </div>
</template>

<script>
import TopBar from '../../components/contract/template/topBar.vue'
import Title from '../../components/contract/title.vue'
import AttachmentFiles from '../../components/contract/signingDraft/attachmentFiles.vue'
import ContractInfos from '../../components/contract/contract/infos.vue'
import ApprovalProcesses from '../../components/contract/signingDraft/approvalProcesses.vue'
import SignatureProcesses from '../../components/contract/signingDraft/signatureProcesses.vue'
import CarbonCopies from '../../components/contract/signingDraft/carbonCopies.vue'
import WaitingSignatueFiles from '../../components/contract/signingDraft/waitingSignatueFiles.vue'
import FilePageFieldInputs from '../../components/contract/template/filePageFieldInputs.vue'
import FilePages from '../../components/contract/file/pages.vue'
import FilePageFieldDraft from '../../components/contract/signing/filePageFieldDraft.vue'
import makeDetail2PageFieldsFromFileList from '../../formatters/contract/template/makeDetail2PageFieldsFromFileList'
import makeDetail2FieldsFromFileList from '../../formatters/contract/template/makeDetail2FieldsFromFileList'
import formatPageFieldsPx from '../../formatters/contract/template/formatPageFieldsPx'
import parentHadClass from './templatesNewStep2/parentHadClass'
import makeDraftFieldList from '../../formatters/contract/template/makeDraftFieldList'
import { FilePagesModePreviewWrite } from './constants'
import makePlatformClient from '../../services/platform/makeClient'
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import store from '../../helpers/store'
import { hadPrivilege } from '../../helpers/profile'
import sortFields from '../../components/contract/template/sortFields'

const client = makeContractClient()
const pclient = makePlatformClient()

export default {
  components: {
    TopBar,
    Title,
    FilePages,
    FilePageFieldDraft,
    WaitingSignatueFiles,
    AttachmentFiles,
    ContractInfos,
    ApprovalProcesses,
    SignatureProcesses,
    CarbonCopies,
    FilePageFieldInputs
  },
  async created() {
    const id = this.$route.params.id
    if (!id) {
      throw new Error('id is required')
    }

    this.tipClosed = store.get('__signingDraftsNewStep2TipClosed')
    this.waitingSignatueFilesTipClosed = store.get('__waitingSignatueFilesTip')

    const loading = this.$loading({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255,255, 0.7)'
    })

    // this.draftModifiableData = fakeData.data
    const [err, r] = await client.signingGetDraftModifiableData({
      body: {
        id: id
      }
    })
    if (err) {
      handleError(err)
      return
    }
    this.draftModifiableData = r.data

    console.log('draftModifiableData', this.draftModifiableData)
    this.fields = makeDetail2FieldsFromFileList(
      this.draftModifiableData.fileList
    )
    this.pageFields = makeDetail2PageFieldsFromFileList(
      this.draftModifiableData.fileList
    )

    this.attachmentFilesTipClosed = store.get('__attachmentFilesTipClosed')

    this.fillTipClosed = store.get('__fillTipClosed')

    loading.close()
  },
  computed: {
    sortedFields() {
      var fields = this.fields.filter(item => item.modifiable)
      sortFields(fields, this.pageFields, this.draftModifiableData.fileList)
      return fields
    }
  },
  mounted() {
    document.body.style.margin = 0
    document.body.style.padding = 0
    document.body.style.overflow = 'hidden'
  },
  methods: {
    close() {
      store.set('__fillTipClosed', true)
      this.fillTipClosed = true
    },
    showFillTip() {
      this.$msgbox({
        title: '填写提示',
        message:
          '填写后不可更改，请仔细检查。若发现错误，在签署完成前，发起人可以撤回合同，重新发起签署。',
        type: 'warning',
        confirmButtonText: '我知道了',
        closeOnClickModal: false
      })
    },
    closeAttachmentFilesTip() {
      store.set('__attachmentFilesTipClosed', true)
      this.attachmentFilesTipClosed = true
    },
    closeWaitingSignatueFilesTip() {
      store.set('__waitingSignatueFilesTip', true)
      this.closed = true
    },
    removeAttachmentFile(file) {
      this.draftModifiableData.attachmentList =
        this.draftModifiableData.attachmentList.filter(
          attachment => attachment.archiveId !== file.archiveId
        )
      console.log(file, 'filefilefile')
    },
    // 下载文件
    async downloadAttachmentFile(file, index) {
      const id = file.archiveId
      const name = file.name
      const [err, r] = await pclient.platformDownloadFile(
        {
          method: 'GET',
          headers: { 'content-type': 'application/octet-stream' }
        },
        { id, name }
      )
      if (err) {
        console.log(err, 'errrrrrr')
        return
      }
      window.open(r.url)
    },

    closeTip() {
      this.tipClosed = true
      store.set('__signingDraftsNewStep2TipClosed', true)
    },
    back() {
      const _this = this
      this.$confirm('是否保存对合同的修改？', '返回提示', {
        cancelButtonText: '不保存',
        confirmButtonText: '保存草稿',
        type: 'info',
        distinguishCancelAndClose: true,
        closeOnClickModal: false
      })
        .then(() => {
          this.save(() => {
            _this.$router.push('/templates')
          })
        })
        .catch(action => {
          if (action === 'cancel') {
            this.$router.push('/templates')
          }
        })
    },
    prev() {
      const _this = this
      const id = this.$route.params.id
      this.$confirm('是否保存对合同的修改？', '保存草稿提示', {
        cancelButtonText: '不保存',
        confirmButtonText: '保存草稿',
        type: 'info',
        distinguishCancelAndClose: true,
        closeOnClickModal: false
      })
        .then(() => {
          this.save(() => {
            _this.$router.push(`/signings/drafts/${id}/step1/edit?source=DRAFT`)
          })
        })
        .catch(action => {
          if (action === 'cancel') {
            this.$router.push(`/signings/drafts/${id}/step1/edit?source=DRAFT`)
          }
        })
    },
    async _save() {
      var req = {
        draftId: this.draftModifiableData.draftId,
        draftFieldList: [],
        attachmentList: this.draftModifiableData.attachmentList
      }
      req.draftFieldList = makeDraftFieldList(
        this.fields.filter(item => item.modifiable)
      )
      const [err, _] = await client.signingSaveDraftModifiableData({
        body: req
      })

      return err
    },
    async save(cb) {
      const err = await this._save()
      if (err) {
        handleError(err)
        return
      }

      handleSuccess('保存成功')
      if (cb) {
        cb()
        return
      }
      this.$router.push(`/signings`)
    },
    async submit() {
      if (!hadPrivilege('contract2.signTask.initiate')) {
        this.$msgbox({
          title: '提交签署失败',
          message: '您没有权限发起合同签署任务，请联系管理人员',
          type: 'warning',
          confirmButtonText: '我知道了',
          closeOnClickModal: false
        })
        return
      }
      const serr = await this._save()
      if (serr) {
        handleError(serr)
        return
      }

      const [err, _] = await client.signingSubmitDraft({
        body: {
          id: this.draftModifiableData.draftId
        }
      })
      if (err && err.errorCode === 501) {
        this.$msgbox({
          title: '提交签署失败',
          message: err.message,
          // dangerouslyUseHTMLString: true,
          type: 'warning',
          confirmButtonText: '我知道了',
          closeOnClickModal: false
        })
        return
      }
      if (err) {
        handleError(err)
        return
      }

      this.$router.push(`/signings`)
      handleSuccess('提交签署成功')
    },
    // 保存附件
    // async saveAttachment() {
    //   const [err, _] = await client.signingUpdateDraftAttachment({
    //     body: {
    //       draftId: this.draftModifiableData.draftId,
    //       attachmentList: this.draftModifiableData.attachmentList
    //     }
    //   })
    //   if (err) {
    //     return handleError(err)
    //   }
    // },
    pageFieldFocus(pageField) {
      this.currentPageField = pageField
      this.activeName = 'inputs'
      //滚动到指定位置
      const fieldElId = `field${pageField.fieldId}`
      this.$nextTick(() => {
        const fieldEl = document.getElementById(fieldElId)
        const rightBoxEl = document.getElementById('rightBox')
        // const rightBoxElTop = rightBoxEl.scrollTop
        const offsetHeight = fieldEl.offsetTop

        rightBoxEl.scroll({
          top: offsetHeight + 60,
          behavior: 'smooth'
        })
      })
    },
    pageFieldBlur(e) {
      if (
        parentHadClass(e.target, 'pageField') ||
        parentHadClass(e.target, 'pageFieldSettings') ||
        parentHadClass(e.target, 'infos') ||
        parentHadClass(e.target, 'inputs') ||
        parentHadClass(e.target, '-select-item')
      ) {
        return
      }
      console.log('blur')
      this.currentPageField = null
      // this.activeName = 'infos'
      const rightBoxEl = document.getElementById('rightBox')
      rightBoxEl.scroll({
        top: 0,
        behavior: 'smooth'
      })
    },
    fieldInputFocus(field) {
      if (!field) {
        return
      }
      const pageField = this.pageFields.find(item => item.fieldId === field.id)
      if (pageField) {
        const index = this.draftModifiableData.fileList.findIndex(
          item => item.fileId === pageField.fileId
        )
        if (index !== this.fileIndex) {
          this.selectFile(index)
          this.$nextTick(() => {
            this.$refs.filePages.scrollToFirstPageField(pageField)
          })
        } else {
          this.$refs.filePages.scrollToFirstPageField(pageField)
        }
        this.pageFieldFocus(pageField)
      }
    },
    selectFile(i) {
      this.fileIndex = i
      document.getElementById('pagesBox').scroll({
        top: 0,
        behavior: 'smooth'
      })
    },
    //imageIndex 暂未启用 默认同一文件中图片大小一致
    fileImageLoad(e, fileId) {
      var width = e.target.width
      var height = e.target.height
      this.fileImageSizes[fileId] = [width, height]
      // debugger
      formatPageFieldsPx(this.pageFields, this.fileImageSizes)
    },
    // 下载文件
    async downloadAttachmentFile(file, index) {
      const id = file.archiveId
      const name = file.name
      const [err, r] = await pclient.platformDownloadFile(
        {
          method: 'GET',
          headers: { 'content-type': 'application/octet-stream' }
        },
        { id, name }
      )
      if (err) {
        console.log(err, 'errrrrrr')
        return
      }
      window.open(r.url)
    },
    // 预览文件
    async previewAttachmentFile(file, index) {
      const id = file.archiveId
      const [err, r] = await client.fileInfo({
        body: {
          id
        }
      })
      if (err) {
        handleError(err)
        return
      }
      const url = r.data.url
      window.open(url)
    }
  },
  data() {
    return {
      FilePagesModePreviewWrite,
      fileIndex: 0,
      currentPageField: null,
      activeName: 'infos',
      fields: [],
      pageFields: [],
      draftModifiableData: null,
      tipClosed: false,
      waitingSignatueFilesTipClosed: false,
      attachmentFilesTipClosed: false,
      //用于计算后续的比例
      fileImageSizes: {},
      fillTipClosed: false
    }
  }
}
</script>

<style scoped></style>