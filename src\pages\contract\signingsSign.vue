<template>
  <div
    :style="{
      height: 'calc(100vh - 16px)',
      overflowY: 'auto'
    }"
  >
    <div
      :style="{
        position: 'sticky',
        top: 0,
        background: '#fff',
        zIndex: 999
      }"
    >
      <TopBar
        title="批量签署"
        :step="1"
        :steps="['确认文件信息', '选择签署印章']"
        submitBtnText="提交签署"
        @back="back"
        @prev="prev"
        @submit="submit"
      />
      <div style="padding: 0 24px">
        <template v-for="(seal, index) in sealList">
          <SealRadioList
            v-if="seal.sealList && seal.sealList.length > 0"
            :key="index"
            :value="seal"
            @handleSealChange="sealModal => handleSealChange(sealModal, index)"
          />
        </template>
      </div>
    </div>
    <CellPhoneCode @submit="sign" ref="CellPhoneRef" />
  </div>
</template>
<script>
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import TopBar from '../../components/contract/signing/topBar.vue'
import SealRadioList from '../../components/contract/signing/sealRadioList.vue'
import CellPhoneCode from './signings/moblieSMSValiadationDialog.vue'
const client = makeContractClient()

export default {
  components: {
    TopBar,
    SealRadioList,
    CellPhoneCode
  },
  data() {
    return {
      sealList: [],
      queryModal: [],
      idList: []
    }
  },

  async mounted() {
    this.idList = JSON.parse(this.$route.query.accordSignIdList)
    const [err, r] = await client.signingListSignSeal({
      body: {
        idList: this.idList
      }
    })
    if (err) {
      handleError(err)
      return
    }
    console.log(r, '234324324234324')
    this.sealList = r.data
  },
  methods: {
    submit() {
      this.$refs.CellPhoneRef.open()
    },

    handleSealChange(sealModal, index) {
      this.queryModal[index] = sealModal
    },
    async sign(smsCode, smsToken) {
      this.$refs.CellPhoneRef.loading = true
      const [err, r] = await client.signingSign({
        body: {
          idList: this.idList,
          smsCode,
          smsToken,
          sealList: this.queryModal
        }
      })
      if (err) {
        this.$refs.CellPhoneRef.loading = false
        handleError(err)
        return
      }
      handleSuccess('签署成功')
      this.$refs.CellPhoneRef.loading = false
      this.$refs.CellPhoneRef.close()
      this.$router.push({ path: '/signings' })
    },
    prev() {
      this.$router.go(-1)
    },
    back() {
      this.$router.go(-1)
    }
  }
}
</script>
<style scoped>
</style>