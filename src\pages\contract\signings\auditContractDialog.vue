<template>
  <el-dialog
    title="审核"
    :visible.sync="dialogVisible"
    width="560px"
    @close="close"
    :close-on-click-modal="false"
  >
    <div style="padding: 14px 40px">
      <div
        style="
          width: 480px;
          height: 52px;
          opacity: 0.8;
          background: #f8f8f8;
          border-radius: 8px;
          font-size: 12px;
          color: #777c94;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 16px auto;
          box-sizing: border-box;
        "
        v-if="rows.length > 1"
      >
        请确保您已阅读并知晓所选{{
          rows.length
        }}个合同的全部内容，点击提交后，审核结果不可更改。
      </div>
      <el-form
        :rules="rules"
        hide-required-asterisk
        :model="formData"
        ref="auditRef"
      >
        <el-form-item prop="pass">
          <template #label>
            <div>审核结果：</div>
          </template>
          <br />
          <el-radio-group v-model="formData.pass" @change="handlePassChange">
            <el-radio :label="true">通过</el-radio>
            <el-radio :label="false">驳回</el-radio>
          </el-radio-group>
        </el-form-item>
        <div style="display: flex">
          <i
            v-if="formData.pass === false"
            style="color: red; margin-right: 5px"
            >*</i
          >
          <el-form-item prop="remark" style="flex: 1">
            <el-input
              placeholder="请输入审核意见"
              v-model.trim="formData.remark"
              type="textarea"
              maxlength="1000"
              show-word-limit
              :rows="6"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <span slot="footer" style="margin-top: 46px">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :loading="loading" type="primary" @click="handleSubmit"
        >审 核</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeContractClient from '../../../services/contract/makeClient'
const client = makeContractClient()
export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      formData: { remark: '', pass: null },
      rules: {
        pass: [{ required: true, message: '请选择审核结果', trigger: 'blur' }],
        remark: [
          {
            validator: (rule, value, callback) => {
              // 审核拒绝时 意见必填
              if (!this.formData.pass) {
                if (!value) return callback('请输入审核意见')
              }
              callback()
            }
          }
        ]
      }
    }
  },
  props: {
    rows: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
    },
    close() {
      this.$refs.auditRef.resetFields()
      this.dialogVisible = false
    },
    async handleSubmit() {
      const idList = this.rows.map(row => row.id)

      this.$refs.auditRef.validate(async valid => {
        if (valid) {
          this.loading = true
          const [err, r] = await client.signingApprove({
            body: {
              idList,
              remark: this.formData.remark,
              pass: this.formData.pass
            }
          })
          if (err && err.errorCode === 501 && idList.length === 1) {
            this.close()
            this.loading = false
            this.$confirm(
              `<b>当前合同状态发生变化，无法审核，请查看合同最新信息。</b>`,
              {
                type: 'warning',
                dangerouslyUseHTMLString: true,
                confirmButtonText: '查看合同',
                showCancelButton: false,
                closeOnClickModal: false
              }
            ).then(async () => {
              this.$router.push(`contracts/${idList[0]}`)
            })
            return
          }
          if (err) {
            handleError(err)
            return
          }
          this.loading = false
          this.close()
          if (r.data.failList && r.data.failList.length > 0) {
            const failTotal = r.data.failList.length
            const successTotal = this.rows.length - failTotal
            this.$router.push({
              path: '/contracts/handleResult',
              query: {
                type: 'audit',
                failTotal,
                successTotal,
                contractIdList: JSON.stringify(r.data.failList)
              }
            })
          } else {
            handleSuccess('审核成功')
            this.$emit('reload')
          }
        }
      })
    },
    handlePassChange() {
      this.$refs.auditRef.clearValidate()
    }
  }
}
</script>

<style scoped>
::v-deep .el-dialog__body {
  padding: 0;
}
</style>