<template>
  <el-dialog
    title="验证身份"
    @close="close"
    :visible.sync="dialogVisible"
    :width="width"
    :close-on-click-modal="false"
  >
    <div style="padding: 45px">
      <div style="margin-bottom: 17px">
        <span
          style="margin-right: 6px"
          class="olading-iconfont oi-phone"
        ></span>
        <span>手机号：{{ cellPhone | hideCellPhone }}</span>
      </div>

      <Captcha v-model="captcha" style="margin: 14px 0" ref="captcha" />

      <el-input v-model.trim="smsCode" maxlength="6" placeholder="请输入验证码">
        <div
          slot="suffix"
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
          "
        >
          <el-button v-if="showSendBtn" @click="send" type="text">
            获取验证码</el-button
          >
          <span style="color: #cbced8" v-else>{{ count }}s后重新发送</span>
        </div>
      </el-input>
    </div>

    <span slot="footer">
      <div>
        <el-button @click="close">取 消</el-button>
        <el-button :loading="loading" type="primary" @click="handleSubmit"
          >确 定</el-button
        >
      </div>
    </span>
  </el-dialog>
</template>

<script>
import handleError from '../../../helpers/handleError'

import makePlatformClient from '../../../services/platform/makeClient'
import { user } from '../../../helpers/profile'
import Captcha from '../captcha.vue'

const TIME_COUNT = 60

const pclient = makePlatformClient()

export default {
  name: 'BatchSign',
  components: {
    Captcha
  },
  data() {
    return {
      checked: false,
      dialogVisible: false,
      showSendBtn: true,
      count: 0,
      cellPhone: '',
      smsCode: '',
      smsToken: '',
      loading: false,
      captcha: {
        token: '',
        answer: ''
      }
    }
  },
  props: {
    width: {
      type: String,
      default: '420px'
    }
  },
  async mounted() {
    // 获取当前用户的手机号
    this.cellPhone = user.cellPhone
  },
  methods: {
    open() {
      this.dialogVisible = true

      this.$nextTick(() => {
        if (!this.captcha.token) {
          this.$refs.captcha.reset()
        }
      })
    },
    close() {
      this.smsCode = ''
      this.dialogVisible = false
    },
    async handleSubmit() {
      if (this.smsCode === '') {
        handleError('请输入验证码')
        return
      }
      if (!this.smsToken) {
        handleError('请先获取验证码')
        return
      }
      this.$emit('submit', this.smsCode, this.smsToken)
    },
    //验证码倒计时
    send() {
      if (!this.captcha.token) {
        handleError('请重置图形验证码')
        return
      }
      if (!this.captcha.answer) {
        handleError('请输入正确的图形验证码')
        return
      }

      if (!this.timer) {
        this.count = TIME_COUNT
        this.showSendBtn = false
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
          } else {
            this.showSendBtn = true
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
        this.createCode()
      }
    },
    // 发送验证码
    async createCode() {
      const [err, r] = await pclient.platformCreateOtp({
        body: {
          otpType: 'SMS',
          receiver: user.cellPhone,
          captchaToken: this.captcha.token,
          captchaAnswer: this.captcha.answer
        }
      })
      if (err) {
        handleError(err)
        this.showSendBtn = true
        clearInterval(this.timer)
        this.timer = null

        this.captcha.token = ''
        this.captcha.answer = ''
        return
      }
      this.smsToken = r.data.token

      this.captcha.token = ''
      this.captcha.answer = ''
    }
  },
  filters: {
    hideCellPhone: val => {
      //隐藏手机号
      let statusName = ''
      if (val) {
        statusName =
          val.substring(0, 3) + '****' + val.substring(val.length - 4)
      }
      return statusName
    }
  }
}
</script>

<style scoped>
.small-font {
  color: #777c94;
  font-size: 12px;
  line-height: 16px;
  margin: 0;
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep .el-dialog__body {
  padding: 0;
}
::v-deep .el-dialog__footer {
  padding: 0 24px 16px 0;
}
::v-deep .el-dialog__close {
  position: relative;
  top: -10px;
}
</style>