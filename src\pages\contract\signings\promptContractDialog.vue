<template>
  <el-dialog
    title="催办"
    :visible.sync="dialogVisible"
    width="420px"
    :close-on-click-modal="false"
  >
    <el-form
      label-width="100px"
      ref="promptRef"
      :rules="rules"
      :model="formData"
    >
      <div v-if="rows.length <= 1" style="padding: 24px 68px">
        <el-form-item label="文件名称">{{
          currentPromptInfo && currentPromptInfo.name
        }}</el-form-item>
        <el-form-item label="当前进度"
          ><div style="margin-left: 5px">
            <SignerStatusWithDot
              :value="currentPromptInfo && currentPromptInfo.status"
            /></div
        ></el-form-item>
        <el-form-item label="当前处理人">{{
          currentPromptInfo &&
          currentPromptInfo.handlingBy &&
          currentPromptInfo.handlingBy.name
        }}</el-form-item>
        <!-- <el-form-item label="上次催办时间">2022-12-12 12:12</el-form-item> -->
        <!-- <el-form-item prop="noticeWayList" label="通知方式">
          <el-checkbox-group v-model="formData.noticeWayList">
            <el-checkbox label="WEXIN_MP">小程序通知</el-checkbox>
            <el-checkbox label="SMS">短信</el-checkbox>
          </el-checkbox-group>
        </el-form-item> -->
      </div>

      <div v-else style="padding: 0 48px">
        <h4
          style="
            color: rgba(36, 38, 42, 1);
            position: relative;
            font-size: 14px;
          "
        >
          <i
            style="
              color: #e59b00;
              font-size: 16px;
              margin-right: 6px;
              vertical-align: middle;
              position: absolute;
              top: 2px;
              left: -22px;
            "
            class="el-icon-warning"
          ></i>
          您有{{ rows.length }}个合同任务可批量催办，确定要批量催办吗？
        </h4>
        <p style="font-size: 14px; color: #777c94">
          仅审核、填写、签署中的合同可以催办
        </p>
        <!-- <div style="margin-top: 32px">通知方式</div>
        <el-form-item prop="noticeWayList" label-width="0">
          <el-checkbox-group v-model="formData.noticeWayList">
            <el-checkbox label="WEXIN_MP">小程序通知</el-checkbox>
            <el-checkbox label="SMS">短信</el-checkbox>
          </el-checkbox-group>
        </el-form-item> -->
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit"
        >催 办</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
var requesting = false
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeContractClient from '../../../services/contract/makeClient'
import SignerStatusWithDot from '../../../components/contract/signing/signerStatusWithDot.vue'
import {
  ContractStatusReviewing,
  ContractStatusFilling,
  ContractStatusSigning
} from '../../../services/contract/constants'
const client = makeContractClient()
export default {
  components: {
    SignerStatusWithDot
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      formData: { noticeWayList: ['WEXIN_MP'] },
      rules: {
        noticeWayList: {
          required: true,
          message: '请选择通知方式',
          trigger: 'change'
        }
      }
    }
  },
  props: {
    rows: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
      this.$refs.promptRef.resetFields()
    },
    async handleSubmit() {
      const idList = this.rows.map(row => row.id)

      // 状态均为签署中/填写中/催办中
      const accordArray = this.rows.filter(
        row =>
          row.status === ContractStatusReviewing ||
          row.status === ContractStatusFilling ||
          row.status === ContractStatusSigning
      )
      // 当全部已选择合同都不满足时，toast提示
      if (accordArray.length === 0) {
        handleError({ message: '请至少选择一条本人可审核合同' })
        return
      }

      this.$refs.promptRef.validate(async valid => {
        if (valid) {
          this.loading = true
          const [err, r] = await client.signingUrge({
            body: {
              idList,
              noticeWayList: this.formData.noticeWayList
            }
          })
          if (err) {
            this.loading = false
            handleError(err)
            return
          }
          this.loading = false
          this.close()
          handleSuccess('催办成功')
          this.$emit('reload')
        }
      })
    }
  },
  computed: {
    currentPromptInfo() {
      return this.rows[0]
    }
  }
}
</script>

<style scoped>
::v-deep .el-dialog__body {
  padding: 0;
}
::v-deep .el-form-item {
  margin: 0;
}
::v-deep .el-form-item__label {
  color: #777c94;
}
</style>