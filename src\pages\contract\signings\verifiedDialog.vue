<template>
  <el-dialog
    title="实名认证"
    :visible.sync="dialogVisible"
    width="560px"
    @close="close"
    :close-on-click-modal="false"
  >
    <div style="padding: 14px 40px">
      <div style="margin-bottom: 5px">请完成本人实名认证，再进行合同签署</div>
      <div style="display: flex; flex-direction: column; align-items: center">
        <img
          style="width: 200px; height: 200px; margin-bottom: 15px"
          src="../../../assets/images/verifiedCode.png"
        />
        <div style="margin-bottom: 5px">微信扫码认证</div>
        <div>认证成功后点击继续签署</div>
      </div>
    </div>

    <span slot="footer" style="margin-top: 46px">
      <el-button
        type="primary"
        :loading="loading"
        style="width: 200px"
        @click="handleSubmit"
        >继续签署</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makePlatformClient from '../../../services/platform/makeClient'

const pclient = makePlatformClient()
export default {
  name: 'VerifiedDialog',
  data() {
    return {
      dialogVisible: false,
      loading: false
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
    },
    async handleSubmit() {
      this.loading = true
      const [err, r] = await pclient.platformProfile({
        body: { withPrivileges: true }
      })
      if (err) {
        handleError(err)
        this.loading = false
        throw new Error(err)
      }
      const profile = r.data
      this.loading = false
      if (!profile.user.isAuth) {
        return handleError({ message: '您还未完成实名认证，无法继续签署' })
      }
      this.close()
      window.location.reload(true)
    }
  }
}
</script>

<style scoped>
::v-deep .el-dialog__footer {
  text-align: center;
}
</style>