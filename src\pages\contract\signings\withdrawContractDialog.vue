<template>
  <el-dialog
    title="撤回提示"
    :visible.sync="dialogVisible"
    width="420px"
    :close-on-click-modal="false"
  >
    <div
      style="
        width: 372px;
        height: 52px;
        opacity: 0.8;
        background: #f8f8f8;
        border-radius: 8px;
        font-size: 12px;
        color: #777c94;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 14px;
        margin: 16px auto;
        box-sizing: border-box;
      "
    >
      <span v-if="rows.length <= 1">
        文件撤回后，将中止签署流程。未处理人员不会看见合同文件，已处理人员不受影响，仍可见合同。</span
      >
      <span v-else>
        撤回提示 您将撤回{{
          rows.length
        }}个合同，文件撤回后，会终止签署流程。未处理人员不会看见合同文件，已处理人员不受影响，仍可见合同。
      </span>
    </div>
    <div style="display: flex">
      <el-input
        placeholder="请输入撤回原因(必填)"
        v-model.trim="reason"
        type="textarea"
        maxlength="1000"
        show-word-limit
        :rows="6"
      ></el-input>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit"
        >撤 回</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeContractClient from '../../../services/contract/makeClient'
import { verifyWithdraw } from '../contracts/verify'
import { user } from '../../../helpers/profile'

const client = makeContractClient()
export default {
  data() {
    return {
      dialogVisible: false,
      reason: '',
      loading: false
    }
  },
  props: {
    rows: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
      this.reason = ''
    },
    async handleSubmit() {
      if (!this.reason) {
        return handleError({ message: '请输入撤回原因' })
      }
      const idList = this.rows.map(row => row.id)
      this.loading = true
      const [err, r] = await client.signingWithdraw({
        body: {
          idList,
          reason: this.reason
        }
      })
      if (err && err.errorCode === 501 && idList.length === 1) {
        this.loading = false
        this.close()
        this.$confirm(
          `<b>当前合同状态发生变化，无法撤回，请查看合同最新信息。</b>`,
          {
            type: 'warning',
            dangerouslyUseHTMLString: true,
            confirmButtonText: '查看合同',
            showCancelButton: false,
            closeOnClickModal: false
          }
        ).then(async () => {
          this.$router.push(`contracts/${idList[0]}`)
        })
        return
      }
      if (err) {
        handleError(err)
        return
      }
      this.loading = false
      this.close()
      if (r.data.failList && r.data.failList.length > 0) {
        const failTotal = r.data.failList.length
        const successTotal = this.rows.length - failTotal
        this.$router.push({
          path: '/contracts/handleResult',
          query: {
            type: 'withdraw',
            failTotal,
            successTotal,
            contractIdList: JSON.stringify(r.data.failList)
          }
        })
      } else {
        handleSuccess('撤回成功')
        this.$emit('reload')
      }
    }
  }
}
</script>

<style></style>