<template>
  <o-container style="padding: 0 16px">
    <template slot="header">
      <TopBar :noBorder="true">
        <div
          :style="{
            display: 'flex'
          }"
        >
          <h1>模板管理</h1>
        </div>
      </TopBar>
    </template>

    <div
      :style="{
        display: 'flex'
      }"
    >
      <div
        :style="{
          flex: '0 0 226px',
          height: 'calc(100vh - 184px)',
          overflow: 'hidden',
          position: 'sticky',
          borderRight: ' 1px solid #eef0f4',
          top: 0
        }"
      >
        <Types
          @selectAll="selectAll"
          @selectGroup="selectGroup"
          @selectType="selectType"
          style="padding-top: 16px; font-size: 14px"
        />
      </div>
      <div
        style="padding: 16px 20px; width: calc(100% - 254px); overflow: hidden"
      >
        <div
          style="
            height: 84px;
            background: #f7fafd;
            border-radius: 8px;
            padding: 24px;
            box-sizing: border-box;
            min-width: 800px;
          "
        >
          <el-form :inline="true" ref="searchForm">
            <el-form-item label="模板名称">
              <el-input
                v-model.trim="conditions.name"
                maxlength="50"
                placeholder="请输入模板名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="模板状态">
              <el-select v-model="conditions.status">
                <el-option label="全部" value=""></el-option>
                <el-option label="草稿" value="1"></el-option>
                <el-option label="已启用" value="2"></el-option>
                <el-option label="已停用" value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="reload()"> 查询 </el-button>
              <el-button
                @click="
                  () => {
                    conditions.status = ''
                    conditions.name = ''
                    reload()
                  }
                "
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div
          :style="{
            marginTop: '24px',
            textAlign: 'right'
          }"
        >
          <el-button
            v-if="hadPrivilege('contract2.templateManagement.add')"
            type="primary"
            @click="
              $router.push({
                path: '/templates/new/step1',
                query: {
                  contractTypeId: conditions.contractTypeId,
                  back: '/templates'
                }
              })
            "
          >
            <i class="olading-iconfont oi-icon_add2" />
            新建模板
          </el-button>
        </div>
        <!-- 表格区域 -->
        <o-table
          style="margin-top: 12px"
          :sticky="true"
          ref="o-table"
          :actionButtons="actionButtons"
          :tableHeader="tableHeader"
          :showPagination="true"
          :pagination="{ fixed: true }"
          :tableData="tableData"
          :total="listTotal"
          @paginationChange="paginationChange"
          emptyHeight="calc(100vh - 450px)"
        />
      </div>
      <CheckDialog :visible.sync="checkDialog" :checkEnable="checkEnable" />
    </div>
  </o-container>
</template>

<script>
import TopBar from '../../components/contract/topBar.vue'
import CheckDialog from '../../components/contract/template/checkDialog.vue'
import Status from '../../components/contract/template/status.vue'
import Types from './templates/types.vue'

import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import formatDateTime from '../../formatters/dateTime'
import {
  TemplateStatusOpened,
  TemplateStatusStopped
} from '../../services/contract/constants'
import { hadPrivilege } from '../../helpers/profile'
const client = makeContractClient()
export default {
  components: {
    TopBar,
    Types,
    Status,
    CheckDialog
  },
  // watch: {
  //   conditions: {
  //     handler(n) {
  //       console.log('new conditions', n)
  //     },
  //     deep: true
  //   }
  // },
  methods: {
    async reload() {
      const [err, r] = await client.templateQuery({
        body: {
          limit: this.limit,
          start: this.start,
          withTotal: true,
          filters: {
            ...this.conditions
          }
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.tableData = r.data.list
      this.listTotal = r.data.total
    },

    selectAll() {
      var n = { ...this.conditions }
      n.contractTypeGroupId = ''
      n.contractTypeId = ''
      this.start = 1
      this.conditions = n
      this.reload()
    },
    selectGroup(groupId) {
      var n = { ...this.conditions }
      n.contractTypeGroupId = groupId
      n.contractTypeId = ''
      this.start = 1
      this.conditions = n
      this.reload()
    },
    selectType(typeId) {
      var n = { ...this.conditions }
      n.contractTypeGroupId = ''
      n.contractTypeId = typeId
      this.start = 1
      this.conditions = n
      this.reload()
    },
    paginationChange({ start, limit }) {
      this.start = start
      this.limit = limit
      this.reload()
    },
    hadPrivilege
  },
  mounted() {
    this.limit = this.$refs['o-table'].context.pagination.limit
    this.reload()
  },
  data() {
    return {
      tableData: [],
      checkDialog: false,
      checkEnable: {},
      listTotal: 0,
      start: 1,
      limit: 20,
      conditions: {
        status: '',
        name: '',
        contractTypeGroupId: '',
        contractTypeId: ''
      },
      tableHeader: [
        {
          label: '序号',
          type: 'INDEX',
          fixed: true
        },
        {
          prop: 'name',
          label: '模板名称',
          fixed: true,
          width: 250,
          showOverflowTooltip: false,
          formatter: row => {
            if (row.remark) {
              return `<div title="${row.name}" class="text-ellipsis" style="font-weight: 500;font-size: 12px;color: #24262A;line-height: 20px;">${row.name}</div>
            <div title="${row.remark}" class="text-ellipsis-2line" style="white-space: normal;padding-top:4px;font-weight: 400;color: #777C94;line-height: 20px;">${row.remark}</div>`
            }
            return `<div title="${row.name}" class="text-ellipsis" style="font-weight: 500;font-size: 12px;color: #24262A;line-height: 20px;">${row.name}</div>`
          }
        },
        {
          label: '合同类型',
          prop: 'contractType'
        },
        {
          label: '状态',
          prop: 'status',
          width: '80px',
          render: (h, row) => {
            // const statusText = row.enable ? '已启用' : '已停用'
            return h(Status, {
              props: {
                status: row.status
              }
            })
          }
        },
        {
          label: '更新人',
          prop: 'updater',
          formatter: row => `
            <p>
              ${row.updater.name}
            </p>
          `
        },
        {
          label: '更新时间',
          prop: 'updateTime',
          width: '150px',
          formatter: row => `
            <p>
              ${formatDateTime('yyyy-MM-dd HH:mm', row.updateTime)}
            </p>
          `
        }
      ],
      actionButtons: [
        {
          label: '编辑',
          id: '1',
          ifShow: row =>
            row.status != TemplateStatusOpened &&
            hadPrivilege('contract2.templateManagement.edit'),
          click: row =>
            this.$router.push(`/templates/${row.id}/step1/edit?back=/templates`)
        },
        {
          label: '删除',
          id: '2',
          ifShow: row =>
            row.status != TemplateStatusOpened &&
            hadPrivilege('contract2.templateManagement.delete'),
          click: async row => {
            this.$confirm(
              `<b>确认要删除吗？</b>` +
                '<br/>删除后，将无法使用该模板创建合同，已发起合同不受影响。',
              '删除',
              {
                dangerouslyUseHTMLString: true,
                type: 'warning',
                closeOnClickModal: false
              }
            ).then(async () => {
              const [err, _] = await client.templateRemove({
                body: {
                  id: row.id
                }
              })
              if (err) {
                handleError(err)
                return
              }
              handleSuccess('模板删除成功')
              this.reload()
            })
          }
        },
        {
          label: '启用',
          id: '3',
          ifShow: row =>
            row.status == TemplateStatusStopped &&
            hadPrivilege('contract2.templateManagement.enable'),
          click: async row => {
            // 确认删除弹窗
            const [err, _] = await client.templateEnable({
              body: {
                id: row.id
              }
            })
            if (err && err.errorCode === 501) {
              const message = err.message.split('\n')
              this.checkEnable.message = message
              this.checkEnable.name = type.name
              this.checkEnable.id = type.id
              this.checkDialog = true
              return
            }
            if (err) {
              handleError(err)
              return
            }
            handleSuccess('模板启用成功')
            this.reload()
          }
        },
        {
          label: '发起签署',
          id: '5',
          ifShow: row =>
            row.status == TemplateStatusOpened &&
            hadPrivilege('contract2.signTask.initiate'),
          click: row =>
            this.$router.push(
              `/signings/drafts/step1/new?templateId=${row.id}&back=/templates`
            )
        },
        {
          label: '停用',
          id: '4',
          ifShow: row =>
            row.status == TemplateStatusOpened &&
            hadPrivilege('contract2.templateManagement.enable'),
          click: async row => {
            const [err, r] = await client.templateDisableCheck({
              body: {
                id: row.id
              }
            })
            // 无法停用弹窗
            if (err && err.errorCode === 501) {
              this.$msgbox({
                title: '停用提示',
                message:
                  '<b>存在未签署的合同正在使用该模板，无法停用</b>' +
                  '<br/>请在使用该模板的合同签署完成后，再进行停用。',
                dangerouslyUseHTMLString: true,
                type: 'warning',
                confirmButtonText: '我知道了',
                closeOnClickModal: false
              })
              return
            }
            if (err) {
              handleError(err)
              return
            }
            // 确认停用提示
            this.$confirm(
              `<b>确认要停用吗？</b>` +
                '<br/>停用后，将无法使用该模板创建合同，已发起的合同不受影响。',
              '停用',
              {
                dangerouslyUseHTMLString: true,
                type: 'warning',
                closeOnClickModal: false
              }
            ).then(async () => {
              const [err] = await client.templateDisable({
                body: {
                  id: row.id
                }
              })
              if (err) {
                handleError(err)
                return
              }
              handleSuccess('模板停用成功')
              this.reload()
            })
          }
        }
      ]
    }
  }
}
</script>
<style scoped>
::v-deep .o-container {
  padding: 0;
}
</style>