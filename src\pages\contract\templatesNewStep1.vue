<template>
  <div
    class="templatesNewStep1"
    :style="{
      height: 'calc(100vh - 16px)',
      overflowY: 'auto'
    }"
  >
    <div
      :style="{
        position: 'sticky',
        top: 0,
        background: '#fff',
        zIndex: 999
      }"
    >
      <TopBar
        :title="this.id ? '编辑模板' : '新建模板'"
        :step="0"
        @back="back"
        @save="save"
        @next="next"
      />
    </div>
    <div
      :style="{
        padding: '30px 40px',
        width: '1360px',
        boxSizing: 'border-box',
        margin: '0 auto'
      }"
    >
      <el-form
        ref="templatesForm"
        label-position="top"
        :model="template"
        :style="{
          display: 'flex',
          flexWrap: 'wrap',
          margin: '0 auto'
        }"
        :rules="rules"
      >
        <el-form-item
          prop="fileList"
          :style="{
            width: '100%'
          }"
        >
          <Title :title="`模版基本信息`" />
          <br />
          <DragUpload
            ref="dragUpload"
            :fileList="template.fileList"
            @handleSuccess="handleSuccess"
            @handleProgress="handleProgress"
            @handleUploadError="handleUploadError"
          />
          <ContractFileList
            :uploadRef="$refs.dragUpload"
            v-if="template.fileList.length > 0"
            v-model="template.fileList"
          />
        </el-form-item>
        <el-form-item
          label="模板名称"
          prop="name"
          :style="{
            flex: '0 0 580px',
            marginRight: '100px'
          }"
        >
          <el-input
            maxlength="50"
            v-model="template.name"
            @input="() => (isEdit = true)"
          />
        </el-form-item>
        <el-form-item
          label="模板归属"
          prop="12"
          :style="{
            flex: '0 0 580px'
          }"
        >
          <el-input disabled :value="compName" />
        </el-form-item>
        <el-form-item
          label="合同类型"
          prop="contractTypeId"
          :style="{
            flex: '0 0 580px',
            marginRight: '100px',
            width: '100%'
          }"
        >
          <ContractTypeSelect
            @input="contractTypeChange"
            v-model="template.contractTypeId"
          />
          <br />
          <template
            v-if="hadPrivilege('contract2.contractSet.typeManagement.manage')"
          >
            管理合同类型，
            <a
              @click="jumpToTypesPage"
              :style="{
                color: '#4f71ff',
                cursor: 'pointer'
              }"
            >
              点击这里
            </a>
          </template>
        </el-form-item>
        <el-form-item
          prop="noRuleId"
          :style="{
            flex: '0 0 580px'
          }"
        >
          <div style="margin-bottom: 10px">
            合同编号规则
            <span
              v-if="template.enableNoRule"
              :style="{
                color: 'red'
              }"
              >*</span
            >
            <el-switch v-model="template.enableNoRule"> </el-switch>
          </div>
          <RuleSelect
            ref="ruleSelectRef"
            :disabled="!template.enableNoRule"
            v-if="renderChildComponent"
            :value="{
              name: template.noRuleName,
              id: template.noRuleId
            }"
            @input="selectNumberRule"
            @numberRuleMessage="message => (numberRuleMessage = message)"
          />
          <div
            v-if="
              currentSelectNumberRule &&
              currentSelectNumberRule.rules &&
              currentSelectNumberRule.rules.length &&
              template.enableNoRule &&
              hadPrivilege('contract2.contractSet.numberManagement.manage')
            "
          >
            预览示例：{{ makeRulesPreviews(currentSelectNumberRule.rules) }}，
            <a
              :style="{
                color: '#4f71ff',
                cursor: 'pointer'
              }"
              @click="jumpToNumberPage"
              >设置编号规则</a
            >
          </div>
        </el-form-item>

        <el-form-item
          :style="{
            flex: '0 0 580px',
            marginRight: '100px'
          }"
        >
          <span>签署截止日期</span>
          <span
            :style="{
              color: 'red'
            }"
          >
            *
          </span>
          <i
            @click="$refs.expirationDate.open()"
            class="olading-iconfont oi-wenhao"
            style="color: #7f7f7f; cursor: pointer"
          />
          <div style="display: flex">
            <el-select
              style="width: 100%"
              @change="isEdit = true"
              v-model="template.signDeadlineWay"
            >
              <el-option value="1" label="发起合同时设置"></el-option>
              <el-option value="2" label="发起合同后固定天数"></el-option>
              <el-option value="3" label="不限制"></el-option>
            </el-select>
            <div
              :style="{ display: 'flex' }"
              v-if="
                template.signDeadlineWay ===
                contractSignDeadLineWayInitiateFixedDays
              "
            >
              <el-input-number
                style="margin-left: 8px; margin-right: 8px"
                v-model="template.signDeadlineValue"
                :min="1"
                :max="1000"
                :precision="0"
                controls-position="right"
              />
              <span>天</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item
          prop="approveId"
          :style="{
            flex: '0 0 580px'
          }"
        >
          <div>
            关联审核流程
            <span
              v-if="template.enableApprove"
              :style="{
                color: 'red'
              }"
              >*</span
            >
            <el-switch v-model="template.enableApprove"> </el-switch>
          </div>
          <ApprovalSelect
            ref="approveSelectRef"
            :disabled="!template.enableApprove"
            @approveErrorMessage="message => (approveErrorMessage = message)"
            :value="{
              id: template.approveId
            }"
            v-if="renderChildComponent"
            @input="selectApproval"
          />
          <br />
          <template
            v-if="
              hadPrivilege('contract2.contractSet.flowManagement.manage') &&
              template.enableApprove
            "
          >
            管理合同流程，
            <a
              @click="jumpToApprovalPage"
              :style="{
                color: '#4f71ff',
                cursor: 'pointer'
              }"
            >
              点击这里
            </a>
          </template>
        </el-form-item>
        <el-form-item
          label="使用说明"
          prop="remark"
          :style="{
            flex: '0 0 580px'
          }"
        >
          <el-input
            maxlength="100"
            show-word-limit
            type="textarea"
            rows="6"
            v-model="template.remark"
            @input="isEdit = true"
          />
        </el-form-item>

        <el-form-item
          prop="signStepList"
          :style="{
            flex: '0 0 100%'
          }"
        >
          <Title title="设置签署方" style="margin-bottom: 20px" />
          <Signatures
            v-if="renderChildComponent"
            v-model="template.signStepList"
            @input="isEdit = true"
          />
        </el-form-item>

        <el-form-item
          prop="carbonCopyList"
          :style="{
            flex: '0 0 620px'
          }"
        >
          <template #label>
            合同抄送方
            <el-tooltip content="发起签署后发送至抄送方，最多可添加30人">
              <i class="olading-iconfont oi-wenhao" style="color: #7f7f7f" />
            </el-tooltip>
          </template>

          <CarbonCopies
            @remove="validateField('carbonCopyList')"
            v-model="template.carbonCopyList"
            @input="isEdit = true"
          />
        </el-form-item>
        <Title title="其他设置" />
        <el-form-item
          v-if="template.signStepList.length === 1"
          prop="certifier"
          :style="{
            flex: '0 0 100%'
          }"
        >
          <div>
            是否为证明模板:
            <span
              :style="{
                color: 'red',
                marginRight: '15px'
              }"
            >
              *
            </span>
            <el-select v-model="template.certifier" @input="isEdit = true">
              <el-option :value="true" label="是"></el-option>
              <el-option :value="false" label="否"></el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item>
          <div>
            合同附件
            <el-tooltip
              class="attach-tooltip"
              effect="dark"
              placement="top-start"
            >
              <div slot="content">
                附件仅供查阅不签署，不计入合同签署数量；最多20份文件，<br />支持doc、docx、wps、jpg、jpeg、png、pdf、xls、xlsx、<br />zip、rar、mp4、amr、mp3、wav格式，单份文件不超过50M
              </div>
              <i class="olading-iconfont oi-wenhao" style="color: #7f7f7f" />
            </el-tooltip>
            <div
              style="
                font-size: 12px;
                color: #a8acba;
                position: relative;
                top: -20px;
              "
            >
              附件仅供查阅不签署，不计入合同签署数量
            </div>
          </div>
          <div style="position: relative; top: -20px">
            <Uploader
              style="display: inline"
              ref="uploader"
              @handleProgress="handleAccessoryProgress"
              @handleSuccess="handleAccessorySuccess"
              @handleUploadError="handleAccessoryError"
            />
            ({{
              template.attachmentList ? template.attachmentList.length : 0
            }}/20)
            <ContractFileList
              :uploadRef="$refs.uploader"
              v-if="
                template.attachmentList && template.attachmentList.length > 0
              "
              v-model="template.attachmentList"
              :showOperateBtn="false"
            />
          </div>
        </el-form-item>
      </el-form>
      <ExpirationDate ref="expirationDate" />
    </div>
  </div>
</template>
<script>
import TopBar from '../../components/contract/template/topBar.vue'
import Title from '../../components/contract/title.vue'
import RuleSelect from './typesNew/ruleSelect.vue'
import ApprovalSelect from './typesNew/approveSelect.vue'
import ContractTypeSelect from './templatesNewStep1/contractTypeSelect.vue'
import { makeRulesPreviews } from './rules/makeRulesPreviews'
import Signatures from './templatesNewStep1/signatures.vue'
import ContractFileList from './templatesNewStep1/contractFileList.vue'
import CarbonCopies from './templatesNewStep1/carbonCopies.vue'
import DragUpload from './templatesNewStep1/dragUpload.vue'
import Uploader from './templatesNewStep1/uploader.vue'
import ExpirationDate from '../../components/contract/template/expirationDateDialog.vue'
import { hadPrivilege } from '../../helpers/profile'
import makePlatformClient from '../../services/platform/makeClient'
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import { carbonCopyListValid } from './signingsDraftsNewStep1/validator'
import { contractSignDeadLineWayInitiateFixedDays } from '../../services/contract/constants'

const pclient = makePlatformClient()
const client = makeContractClient()
export default {
  components: {
    TopBar,
    Title,
    RuleSelect,
    ApprovalSelect,
    ContractTypeSelect,
    Signatures,
    CarbonCopies,
    ContractFileList,
    DragUpload,
    Uploader,
    ExpirationDate
  },
  async created() {
    const [profileErr, profileR] = await pclient.platformProfile({
      body: {
        withPrivileges: true,
        withRoles: true
      }
    })
    if (profileErr) {
      handleError(profileErr)
      return
    }
    // 获取当前用户的企业名称
    this.compName = profileR?.data?.merchant.name
    // 开通的业务
    this.openedBusiness = profileR?.data?.merchant.openedBusiness
    if (!this.$route.params.id) {
      // 新建模板逻辑
      // 根据类型id 给合同类型默认值
      const contractTypeId = this.$route.query.contractTypeId
      if (contractTypeId) {
        this.template.contractTypeId = contractTypeId * 1
        this.contractTypeChange(this.template.contractTypeId)
      }

      // 判断当前用户是否开通了人事  填写+签署（未开通人事时）/签署（已开通人事时）
      if (this.openedBusiness.includes('HR')) {
        this.template.signStepList[0].needWrite = false
      }

      return
    }
    // 修改模板逻辑
    this.id = this.$route.params.id
    const loading = this.$loading({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255,255, 0.7)'
    })
    this.renderChildComponent = false
    const [err, r1] = await client.templateGet({
      body: {
        id: this.$route.params.id
      }
    })
    if (err) {
      handleError(err)
      return
    }

    this.template = r1.data
    this.renderChildComponent = true

    loading.close()

    // 为符合组件内数据格式，需要把回显内容格式转换下
    if (r1.data.attachmentList && r1.data.attachmentList.length > 0) {
      this.template.attachmentList = r1.data.attachmentList.map(attach => ({
        archiveFile: attach
      }))
    } else {
      this.template.attachmentList = []
    }
    // 请求完毕之后，改成未编辑状态
    this.$nextTick(() => {
      this.isEdit = false
      this.$refs.templatesForm.validate()
    })
  },
  mounted() {
    const draggerEl = document.querySelector('.uploader .el-upload-dragger')
    draggerEl.style.width = '580px'
    draggerEl.style.height = '130px'
    draggerEl.style.border = 'none'
  },
  methods: {
    handleUploadError(res, file, fileList) {
      setTimeout(() => {
        this.template.fileList = this.template.fileList.filter(
          fileList => fileList.archiveFile.uid != file.uid
        )
        this.$refs.dragUpload.$refs.uploader.uploadFiles.splice(0, 1)
        handleError({ message: res.message })
      }, 500)
    },
    // 文件上传中
    handleProgress(res, file, fileList) {
      const currentFile = this.template.fileList.find(
        curFile => curFile.archiveFile && curFile.archiveFile.uid == file.uid
      )
      if (currentFile) {
        currentFile.percent = res.percent
      } else {
        this.template.fileList.push({
          archiveFile: { name: file.name, uid: file.uid },
          key: Math.random(),
          percent: res.percent
        })
      }
    },
    // 文件上传成功
    async handleSuccess(res, file, fileList) {
      if (!res.success) {
        this.handleUploadError(res, file, fileList)

        return
      }
      // 第一次上传成功后 把名字赋值过来
      if (this.isFirstUpdate && !this.template.name) {
        this.isFirstUpdate = false
        this.template.name = res.data.name.split('.')[0]
      }
      // 上传成功后替换成真实的数据
      this.template.fileList = this.template.fileList.map(fileList => {
        if (fileList.archiveFile.uid == file.uid) {
          return { ...fileList, archiveFile: res.data }
        } else {
          return fileList
        }
      })
      handleSuccess('文件上传成功')
      this.isEdit = true
      // 上传成功后再次校验，取消红色提示
      this.$nextTick(() => {
        this.$refs.templatesForm.clearValidate()
      })
    },

    // 附件上传中
    handleAccessoryProgress(res, file, fileList) {
      console.log(res, file, fileList)
      const currentFile = this.template.attachmentList.find(
        curFile => curFile.archiveFile && curFile.archiveFile.uid == file.uid
      )
      if (currentFile) {
        currentFile.percent = res.percent
      } else {
        this.template.attachmentList.push({
          archiveFile: { name: file.name, uid: file.uid },
          key: Math.random(),
          percent: res.percent
        })
      }
    },
    handleAccessoryError(res, file, fileList) {
      setTimeout(() => {
        this.template.attachmentList = this.template.attachmentList.filter(
          fileList => fileList.archiveFile.uid != file.uid
        )
        this.$refs.uploader.$refs.uploader.uploadFiles.splice(0, 1)
        handleError({ message: res.message })
      }, 500)
    },
    // 附件上传成功
    handleAccessorySuccess(res, file, fileList) {
      if (!res.success) {
        this.handleAccessoryError(res, file, fileList)

        return
      }
      // 上传成功后替换成真实的数据
      this.template.attachmentList = this.template.attachmentList.map(
        fileList => {
          if (fileList.archiveFile.uid == file.uid) {
            return {
              ...fileList,
              archiveFile: {
                archiveId: res?.data?.archiveId,
                name: res?.data?.name,
                size: res?.data?.size
              }
            }
          } else {
            return fileList
          }
        }
      )
      handleSuccess('文件上传成功')
      this.isEdit = true
    },
    async save() {
      this._save()
    },

    next() {
      const _this = this
      this._save(() => {
        _this.$router.push(`/templates/${_this.id}/step2/edit`)
      }, true)
    },
    async _save(cb, isNext) {
      const fileList = [
        ...this.template.fileList,
        ...this.template.attachmentList
      ]
      for (let file of fileList) {
        if (
          !(file.archiveFile && file.archiveFile.archiveId) &&
          !file.archiveId
        ) {
          handleError('文件上传中，请上传成功后保存！')
          return
        }
      }
      this.$refs.templatesForm.validate(async valid => {
        if (!valid) {
          this.scrollIntoError(this.$refs.templatesForm)
          this.$message({
            type: 'error',
            message: '请检查下方输入项'
          })
          return
        }
        this.isEdit = false

        const loading = this.$loading({
          lock: true,
          text: '保存中...',
          spinner: 'el-icon-loading',
          background: 'rgba(255, 255,255, 0.7)'
        })
        // 格式化合同附件数据格式
        let attachmentList = []
        if (this.template.attachmentList.length > 0) {
          attachmentList = this.template.attachmentList.map(file => ({
            ...file.archiveFile
          }))
        }

        if (this.$route.params.id) {
          const [err1, _] = await client.templateUpdate({
            body: {
              ...this.template,
              attachmentList
            }
          })
          if (err1) {
            handleError(err1)
            loading.close()
            return
          }
          loading.close()
          if (!isNext) {
            handleSuccess('模板修改成功')
          }

          if (cb) {
            cb()
          }
          return
        }

        const [err2, r2] = await client.templateSave({
          body: {
            ...this.template,
            attachmentList
          }
        })
        if (err2) {
          loading.close()
          handleError(err2)
          return
        }

        this.id = r2.data.id
        loading.close()
        handleSuccess('保存草稿成功')

        if (cb) {
          cb()
        }
      })
    },
    selectNumberRule(selectedNumberRule) {
      this.currentSelectNumberRule = selectedNumberRule
      this.template.noRuleId = selectedNumberRule.id
      if (selectedNumberRule.id) {
        this.isEdit = true
      }
    },
    selectApproval(selectedApproval) {
      this.template.approveId = selectedApproval.id
      if (selectedApproval.id) {
        this.isEdit = true
      }
    },
    // 跳转到合同类型
    jumpToTypesPage() {
      const routers = this.$router.resolve({ path: '/types' })
      window.open(routers.href, '_blank')
    },
    // 跳转到流程页面
    jumpToApprovalPage() {
      const routers = this.$router.resolve({ path: '/approvals' })
      window.open(routers.href, '_blank')
    },
    // 跳转到编号页面
    jumpToNumberPage() {
      const routers = this.$router.resolve({ path: '/rules' })
      window.open(routers.href, '_blank')
    },
    // 返回
    back() {
      if (!this.template.name || !this.isEdit) {
        this.$router.push('/templates')
        return
      }
      this.$confirm('是否保存对模板的更改？', `确认返回提示`, {
        type: 'warning',
        confirmButtonText: '保存草稿',
        cancelButtonText: '不保存',
        distinguishCancelAndClose: true,
        closeOnClickModal: false
      })
        .then(async () => {
          this._save(() => this.$router.push('/templates'))
        })
        .catch(action => {
          if (action === 'cancel') {
            this.$router.push('/templates')
          }
        })
    },
    async contractTypeChange(v) {
      const [err, r] = await client.contractTypeQueryById({
        body: {
          id: v
        }
      })
      if (err) {
        handleError(err)
        return
      }

      const data = r.data
      this.template.approveId = data.approveId
      this.template.enableNoRule = data.enableNoRule
      this.template.enableApprove = data.enableApprove
      this.template.noRuleId = data.noRuleId
      this.template.signDeadlineWay = data.signDeadlineWay
      this.template.signDeadlineValue = data.signDeadlineValue
      this.isEdit = true
      await this.$refs.approveSelectRef.search()
      await this.$refs.ruleSelectRef.search()
    },
    // 清除校验
    validateField(prop) {
      this.$nextTick(() => {
        this.$refs.templatesForm.validateField(prop)
      })
    },
    makeRulesPreviews,
    hadPrivilege
  },

  data() {
    return {
      id: '',
      compName: '',
      // 开通的业务
      openedBusiness: [],
      template: {
        fileList: [],
        attachmentList: [],
        name: '',
        contractTypeId: null,
        enableNoRule: true,
        noRuleId: null,
        signDeadlineWay: '1',
        signDeadlineValue: 15,
        enableApprove: false,
        approveId: null,
        signStepList: [
          {
            key: '1',
            signerType: '1',
            needWrite: true,
            needSign: true,
            name: '签署方1',
            signerUserId: 0,
            signWay: '1',
            participateType: 0
          },
          {
            key: '2',
            signerType: '2',
            needWrite: true,
            needSign: true,
            name: '签署方2',
            signerUserId: 0,
            signWay: '1',
            participateType: 0
          }
        ],
        carbonCopyList: [],
        numberRuleMessage: '',
        approveErrorMessage: ''
      },
      rules: {
        fileList: [
          {
            validator: (rule, value, callback) => {
              if (value && value.length === 0)
                return callback(new Error('文件列表不能为空'))
              callback()
            }
          }
        ],
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ],
        contractTypeId: [
          {
            required: true,
            message: '请输入合同类型',
            trigger: 'blur'
          }
        ],
        // 合同编号规则
        noRuleId: [
          {
            validator: (rule, value, callback) => {
              if (this.template.enableNoRule && this.numberRuleMessage) {
                return callback(new Error(this.numberRuleMessage))
              }
              if (this.template.enableNoRule) {
                if (!value) return callback('请选择合同编号规则')
              }
              callback()
            }
          }
        ],
        // 关联审核流程
        approveId: [
          {
            validator: (rule, value, callback) => {
              if (this.template.enableApprove && this.approveErrorMessage) {
                return callback(this.approveErrorMessage)
              }
              if (this.template.enableApprove) {
                if (!value) return callback('请选择关联审核流程')
              }
              callback()
            }
          }
        ],
        signStepList: [
          {
            validator: (rule, value, callback) => {
              for (let step of value) {
                if (!step.name) return callback(new Error('参与方名称不能为空'))
                if (!step.needSign && !step.needWrite)
                  return callback(new Error('请至少选择一种参与方式及顺序'))
                if (step.participateType === 1 && step.signerUserId === 0)
                  return callback(new Error('请选择参与方及人员'))
                if (!step.signWay) return callback(new Error('请选择签署要求'))
              }
              for (let i in value) {
                for (let j in value) {
                  if (value[i].name === value[j].name && i !== j)
                    return callback(new Error('参与方名称不能重复'))
                  if (
                    value[i].signerUserId === value[j].signerUserId &&
                    value[j].signerUserId !== 0 &&
                    value[i].signerUserId !== 0 &&
                    i !== j
                  )
                    return callback(new Error('参与方及人员不能重复'))
                }
              }
              callback()
            }
          }
        ],
        carbonCopyList: [{ validator: carbonCopyListValid }],
        certifier: [
          {
            required: true,
            message: '请选择是否为证明模板',
            trigger: 'change'
          }
        ]
      },
      currentSelectNumberRule: null,
      isFirstUpdate: true,
      isEdit: false,
      renderChildComponent: true,
      contractSignDeadLineWayInitiateFixedDays
    }
  }
}
</script>
<style scoped>
::v-deep .el-input__inner {
  border-color: #dcdfe6 !important;
}
</style>
<style >
.el-textarea__inner {
  font-family: Avenir, Helvetica, Arial, sans-serif;
}
</style>