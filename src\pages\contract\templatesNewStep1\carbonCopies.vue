<template>
  <div>
    <div
      :style="{
        display: 'flex',
        margin: '8px 24px 0 0 ',
        alignItems: 'center'
      }"
      v-for="(carbon, index) in carbons"
      :key="index"
    >
      <el-select
        :class="
          carbon.type === SingerTypePerson
            ? 'person-select'
            : 'enterprise-select'
        "
        v-model="carbon.type"
        @change="$emit('change')"
      >
        <el-option value="1" label="个人"></el-option>
        <el-option value="2" label="企业"></el-option>
      </el-select>
      <PlatformEmployeeSelect
        @change="$emit('change')"
        v-model="carbon.userId"
      />
      <i
        @click="
          carbons.splice(index, 1)
          $emit('remove')
          $emit('change')
        "
        title="删除"
        style="font-size: 20px; margin-left: 12px; cursor: pointer"
        class="el-icon-delete"
      />
    </div>
    <div style="color: #f56c6c" v-if="carbons && carbons.length >= 30">
      最多能添加30个抄送方
    </div>
    <div v-else style="margin-top: 8px">
      <el-button
        style="margin-right: 8px"
        class="enterprise-select"
        @click="
          carbons.push({ type: SingerTypeCompany })
          $emit('change')
        "
        >+ 添加企业抄送方</el-button
      >
      <el-button
        style="margin: 0"
        class="person-select"
        @click="
          carbons.push({ type: SingerTypePerson })
          $emit('change')
        "
        >+ 添加个人抄送方</el-button
      >
    </div>
  </div>
</template>
<script>
import PlatformEmployeeSelect from '../signingsDraftsNewStep1/platformEmployeeSelect.vue'
import {
  SingerTypePerson,
  SingerTypeCompany
} from '../../../services/contract/constants'
export default {
  name: 'carbonCopies',
  components: {
    PlatformEmployeeSelect
  },
  props: {
    value: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      carbons: [],
      SingerTypePerson,
      SingerTypeCompany
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(n) {
        this.carbons = n || []
      }
    },
    carbons: {
      handler(n) {
        this.$emit('input', n)
      }
    }
  }
}
</script>
<style scoped>
.person-select,
.enterprise-select {
  margin-right: 24px;
}
::v-deep .person-select.el-button {
  background: #fff;
  border: 1px solid #ffac04;
  border-radius: 8px;
  color: #ffac04;
}
::v-deep .enterprise-select.el-button {
  background: #fff;
  border: 1px solid #4f71ff;
  border-radius: 8px;
  color: #4f71ff;
}
::v-deep .person-select .el-input--suffix .el-input__inner {
  width: 100px;
  background: rgba(255, 172, 4, 0.06) !important;
  border-radius: 8px;
  border: 1px solid #ffac04;
}
::v-deep .enterprise-select .el-input--suffix .el-input__inner {
  width: 100px;
  background: #f7fafd;
  border: 1px solid #4f71ff;
  border-radius: 8px;
}
</style>