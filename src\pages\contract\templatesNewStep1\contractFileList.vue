<template>
  <div>
    <DragableList
      style="width: 580px; margin-top: 12px"
      v-model="currentFileList"
    >
      <template v-slot="{ item, index }">
        <div style="position: relative">
          <div
            @mouseover="currentShowIndex = index"
            @mouseleave="currentShowIndex = -1"
            style="
              display: flex;
              justify-content: space-between;
              overflow: hidden;
              align-items: center;
            "
          >
            <span
              style="
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 238px;
                display: inline-block;
              "
              >{{ item.archiveFile && item.archiveFile.name }}</span
            >
            <div style="height: 40px" v-show="currentShowIndex === index">
              <el-button
                class="list-icon"
                title="预览"
                @click="previewFile(item, index)"
                type="text"
                ><i class="el-icon-view"
              /></el-button>
              <el-button
                class="list-icon"
                title="替换"
                v-if="showOperateBtn"
                @click="replaceFile(item, index)"
                type="text"
                ><img
                  style="width: 16px; height: 16px"
                  src="../../../assets/images/icon/<EMAIL>"
              /></el-button>
              <el-button
                class="list-icon"
                v-if="showOperateBtn"
                title="下载"
                @click="downloadFile(item, index)"
                type="text"
                ><i class="el-icon-download"
              /></el-button>
              <el-button
                class="list-icon"
                title="删除"
                @click="deleteFile(item, index)"
                type="text"
                ><i class="el-icon-delete"
              /></el-button>
            </div>
          </div>
          <el-progress
            style="position: absolute; width: 560px; bottom: -5px"
            :percentage="
              item.percent ||
              (item.archiveFile && item.archiveFile.archiveId ? 100 : 0)
            "
            :status="
              item.archiveFile && item.archiveFile.archiveId ? 'success' : null
            "
            :stroke-width="2"
          ></el-progress>
        </div>
      </template>
    </DragableList>
    <el-dialog
      title="请选择替换文件"
      :destroy-on-close="true"
      :visible.sync="selectDialog"
      width="30%"
      :close-on-click-modal="false"
    >
      <DragUpload
        ref="replaceUpload"
        :showFileList="true"
        :limit="1"
        :multiple="false"
        @handleRemove="handleRemove"
        @handleSuccess="replaceUploadSuccess"
        :styleObj="{ width: '380px', margin: '0 auto' }"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="selectDialog = false">取 消</el-button>
        <el-button
          :disabled="!replaceBtn"
          type="primary"
          @click="selectFileConfirm"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="replaceDialog"
      :destroy-on-close="true"
      width="420px"
      :close-on-click-modal="false"
    >
      <div style="padding: 24px;box-sizing">
        <div style="font-weight: 600; color: #24262a; line-height: 14px">
          <i
            style="
              font-size: 16px;
              color: #e59900;
              margin-right: 8px;
              vertical-align: middle;
            "
            class="el-icon-warning"
          />

          确定要替换文档内容吗？
        </div>
        <p style="color: #777c94; line-height: 22px; padding-left: 26px">
          替换后，系统默认不影响原控件的位置和设置信息，但不在新文档页面范围内的控件将被删除。
        </p>
        <div
          style="
            background: #f7fafd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            padding-left: 20px;
          "
        >
          <img
            style="width: 26px; height: 40px; margin-right: 14px"
            src="../../../assets/images/icon/<EMAIL>"
            alt=""
          />
          <div
            style="
              color: #46485a;
              font-size: 12px;
              letter-spacing: 0;
              line-height: 12px;
              display: flex;
              flex-direction: column;
              height: 80px;
              justify-content: center;
            "
          >
            <span style="margin-bottom: 16px">{{
              currentFileList[replaceIndex] &&
              currentFileList[replaceIndex].archiveFile &&
              currentFileList[replaceIndex].archiveFile.name
            }}</span>
            <span>
              {{
                replaceFileList[0] &&
                replaceFileList[0].archiveFile &&
                replaceFileList[0].archiveFile.name
              }}
            </span>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="replaceDialog = false">取 消</el-button>
        <el-button type="primary" @click="replaceFileConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import DragableList from '../../../components/contract/draggableList.vue'
import DragUpload from './dragUpload.vue'

import makeContractClient from '../../../services/contract/makeClient'
import makePlatformClient from '../../../services/platform/makeClient'
const pclient = makePlatformClient()
const client = makeContractClient()
export default {
  name: 'ContractFileList',
  components: {
    DragableList,
    DragUpload
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    showOperateBtn: {
      type: Boolean,
      default: () => true
    },
    uploadRef: Object
  },
  watch: {
    value: {
      immediate: true,
      handler(newFileList) {
        this.currentFileList = newFileList
        for (let file of this.currentFileList) {
          if (!file.key) {
            file.key = Math.random()
          }
        }
      }
    },
    currentFileList: {
      immediate: true,
      handler(newFileList) {
        this.$emit('input', newFileList)
      }
    }
  },
  data() {
    return {
      currentFileList: [],
      selectDialog: false,
      replaceDialog: false,
      replaceFileList: [],
      replaceIndex: 0,
      currentShowIndex: -1,
      replaceBtn: false
    }
  },
  methods: {
    // 确认删除
    deleteFile(file, index) {
      if (this.currentFileList[index].id) {
        this.$confirm(
          `<b>确认要删除【${this.currentFileList[index].archiveFile.name}】吗？</b>` +
            '<br/>删除后，文件已设置控件信息将会丢失。',
          '删除',
          {
            dangerouslyUseHTMLString: true,
            type: 'warning',
            closeOnClickModal: false
          }
        ).then(async () => {
          this.currentFileList.splice(index, 1)
          this.uploadRef.$refs.uploader.uploadFiles.splice(0, 1)
          this.$emit('input', this.currentFileList)
        })
      } else {
        this.currentFileList.splice(index, 1)
        // 删除组件内部维护的数组元素 否则会造成limit计算错误
        this.uploadRef.$refs.uploader.uploadFiles.splice(0, 1)
        this.$emit('input', this.currentFileList)
      }
    },
    // 下载文件
    async downloadFile(file, index) {
      const id = file.archiveFile.archiveId
      const name = file.archiveFile.name
      const [err, r] = await pclient.platformDownloadFile(
        {
          method: 'GET',
          headers: { 'content-type': 'application/octet-stream' }
        },
        { id, name }
      )
      if (err) {
        console.log(err, 'errrrrrr')
        return
      }
      window.open(r.url)
    },
    // 预览文件
    async previewFile(file, index) {
      const id = file.archiveFile.archiveId
      const [err, r] = await client.fileInfo({
        body: {
          id
        }
      })
      if (err) {
        handleError(err)
        return
      }
      const url = r.data.url
      window.open(url)
    },
    replaceFile(item, index) {
      this.replaceFileList = []

      this.selectDialog = true
      this.replaceIndex = index
      // this.$refs.replaceUpload.clearFiles()
    },
    // 选择好替换的文件
    selectFileConfirm() {
      if (this.currentFileList[this.replaceIndex].id) {
        this.replaceDialog = true
        this.selectDialog = false
      } else {
        this.replaceFileConfirm()
        this.selectDialog = false
      }
    },
    // 确认替换文件
    replaceFileConfirm() {
      const replaceFile = this.replaceFileList[0]
      this.currentFileList[this.replaceIndex].archiveFile =
        replaceFile.archiveFile
      this.replaceFileList = []
      this.replaceDialog = false
    },
    replaceUploadSuccess(res, file, fileList) {
      this.replaceFileList = []
      this.replaceFileList.push({ archiveFile: res.data })
      this.replaceBtn = true
    },
    handleRemove(res, file, fileList) {
      this.replaceFileList = []
      this.replaceBtn = false
    }
  }
}
</script>

<style scoped>
::v-deep .draggableItem {
  box-sizing: border-box;
  height: 40px;
  opacity: 0.8;
  background: #f7fafd;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 8px;
}
::v-deep .el-dialog__body {
  padding: 0;
}
::v-deep .el-dialog__body .uploader {
  border: 0 !important;
}
::v-deep .el-progress-bar {
  width: 98%;
}
.list-icon {
  color: #777c94;
  vertical-align: middle;
  font-size: 14px;
}
</style>