<template>
  <el-cascader
    v-model="contractTypeValue"
    style="width: 100%"
    :options="groups"
    :show-all-levels="false"
    :props="{ value: 'id', label: 'name', children: 'nodes' }"
    @change="handleChange"
    @focus="initType()"
  ></el-cascader>
</template>
<script>
import makeContractClient from '../../../services/contract/makeClient'
const client = makeContractClient()
export default {
  props: {
    value: {
      type: Number,
      default: 0
    }
  },
  async created() {
    this.initType()
  },
  watch: {
    value: {
      handler(n) {
        // 后端回显的仅仅是typeid 没有groupId 所以需要在列表中找到groupId正常回显
        for (let group of this.groups) {
          for (let type of group.nodes) {
            if (type.id === n) {
              this.contractTypeValue = [group.id, n]
            }
          }
        }
      }
    }
  },
  methods: {
    handleChange(v) {
      this.$emit('input', v[1])
    },
    async initType() {
      const [err, r] = await client.contractTypeGetTypeTree({
        body: {
          withDisable: false
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.groups = r.data
      for (let group of this.groups) {
        for (let type of group.nodes) {
          if (type.id === this.value) {
            this.contractTypeValue = [group.id, this.value]
          }
        }
      }
    }
  },
  data() {
    return {
      groups: [],
      contractTypeValue: []
    }
  }
}
</script>