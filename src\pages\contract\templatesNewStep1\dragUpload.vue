<template>
  <el-upload
    class="uploader"
    drag
    ref="uploader"
    :action="action"
    :headers="{
      Authorization: `Bear<PERSON> ${token}`
    }"
    :multiple="multiple"
    :on-success="handleSuccess"
    :on-progress="handleProgress"
    :before-upload="handleBeforeUpload"
    :on-remove="handleRemove"
    :on-error="handleUploadError"
    :limit="limit"
    :on-exceed="handleExceed"
    :show-file-list="showFileList"
    :style="{
      width: '580px',
      border: '1px solid #EEF0F4',
      borderRadius: '8px',
      textAlign: 'center',
      ...styleObj
    }"
  >
    <i
      class="el-icon-upload"
      :style="{
        fontSize: '36px',
        color: '#4F71FF',
        margin: '15px 0 0 0 '
      }"
    ></i>
    <div class="el-upload__text">
      请将文件拖至此处或<a style="color: #4f71ff">点击上传</a
      ><span v-if="limit > 0 && fileList.length > 0"
        >（{{ fileList.length }}/{{ limit }}）</span
      >
    </div>
    <div
      class="el-upload__tip"
      :style="{
        marginTop: '-10px',
        fontSize: '12px',
        color: '#a8acba'
      }"
    >
      仅支持pdf格式，单个文件大小不超过50M
    </div>
  </el-upload>
</template>

<script>
import handleError from  '../../../helpers/handleError'
import store from '../../../helpers/store'
var hadTip = false
export default {
  name: 'DragUpload',
  computed: {
    action() {
      if (window.env.current === 'dev') {
        return 'http://************:8102/api/template/uploadSignFile'
      }

      return `${window.env.api}/contract2/template/uploadSignFile`
    },
    token() {
      return store.get('token')
    }
  },
  props: {
    fileList: {
      type: Array,
      default: () => []
    },
    showFileList: {
      type: Boolean,
      default: () => false
    },
    limit: {
      type: Number,
      default: 50
    },
    styleObj: {
      type: Object,
      default: () => {}
    },
    multiple: {
      type: Boolean,
      default: () => true
    }
  },
  methods: {
    handleSuccess(res, file, fileList) {
      this.$emit('handleSuccess', res, file, fileList)
    },
    handleProgress(res, file, fileList) {
      this.$emit('handleProgress', res, file, fileList)
    },
    handleRemove(res, file, fileList) {
      this.$emit('handleRemove', res, file, fileList)
    },
    handleExceed() {
      handleError({ message: `文件最多上传${this.limit}个` })
    },
    handleUploadError(res, file, fileList) {
      this.$emit('handleUploadError', res, file, fileList)
    },
    handleBeforeUpload(file) {
      let fileName = file.name
      let is50M = file.size / 1024 / 1024 < 50
      if (!is50M) {
        this.$message.error('文件必须小于50M')
        return false
      }
      let pos = fileName.lastIndexOf('.')
      let lastName = fileName.substring(pos, fileName.length)
      if (lastName.toLowerCase() !== '.pdf') {
        //避免提示多次
        if (!hadTip) {
          this.$message.error('文件必须为pdf类型')
          hadTip = true
          setTimeout(() => {
            hadTip = false
          }, 1000)

          return false
        }

        return false
      }
    }
  }
}
</script>

<style></style>