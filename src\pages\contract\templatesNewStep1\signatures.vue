<template>
  <div class="signatures">
    <DragableList :columns="columns" v-model="signs">
      <template v-slot="{ item, index }">
        <div>
          <el-select
            :class="
              item.signerType === SingerTypePerson
                ? 'person-select'
                : 'enterprise-select'
            "
            style="width: 100px"
            :value="item.signerType"
            @change="type => signerTypeChange(type, item, index)"
          >
            <el-option value="1" label="个人"></el-option>
            <el-option value="2" label="企业"></el-option>
          </el-select>
        </div>
        <div :ref="'name-' + index">
          <el-input
            maxlength="50"
            style="width: 150px"
            v-model="item.name"
          ></el-input>
        </div>
        <div style="position: relative" :ref="'needWrite-' + index">
          <el-checkbox v-model="item.needWrite">填写</el-checkbox>
          <el-checkbox style="position: relative" v-model="item.needSign"
            >签署
          </el-checkbox>
          <el-tooltip
            content="当前签署顺序，列表拖拽可调整排序"
            placement="top"
          >
            <div
              v-if="signs.length > 1"
              style="
                position: absolute;
                width: 15px;
                height: 15px;
                background: #4f71ff;
                color: #fff;
                border-radius: 50%;
                font-size: 12px;
                top: 5px;
                right: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
              "
            >
              {{ index + 1 }}
            </div>
          </el-tooltip>
        </div>

        <div>
          <el-select
            v-model="item.participateType"
            @change="type => participateTypeChange(type, index)"
          >
            <el-option :value="0" label="发起时选择人员"></el-option>
            <el-option
              v-if="item.signerType === SingerTypeCompany"
              :value="1"
              label="固定人员"
            ></el-option>
          </el-select>
        </div>
        <div :ref="'signerUserId-' + index">
          <span v-if="item.participateType === 0"
            >发起方使用模板时，再添加具体参与人员</span
          >
          <PlatformEmployeeSelect
            :value="item.signerUserId"
            @input="emp => selectEmployee(emp, index)"
            v-else
          />
        </div>
        <div>
          <el-select
            v-model="item.signWay"
            style="width: 150px"
            v-if="item.signerType === SingerTypePerson"
          >
            <!-- <el-select v-model="item.signWay"> -->
            <el-option value="1" label="不限制签名方式"></el-option>
            <el-option value="2" label="手绘签名"></el-option>
            <!-- <el-option value="2" label="系统标准签名"></el-option> -->
          </el-select>
          <span v-else> - </span>
        </div>
        <div>
          <i
            style="font-size: 20px; cursor: pointer"
            title="删除"
            class="el-icon-delete"
            @click="() => deleteSign(item, index)"
          />
        </div>
      </template>
    </DragableList>
    <div style="color: #f56c6c" v-if="signs.length >= 30">
      最多能添加30个签署方
    </div>
    <div v-else>
      <el-button
        style="margin-right: 8px"
        class="enterprise-select"
        @click="addCompany"
        >+ 添加企业</el-button
      >
      <el-button style="margin: 0" class="person-select" @click="addPerson"
        >+ 添加个人</el-button
      >
    </div>
  </div>
</template>
<script>
var key = 99
import DragableList from '../../../components/contract/draggableList.vue'
import PlatformEmployeeSelect from '../signingsDraftsNewStep1/platformEmployeeSelect.vue'
import {
  SingerTypePerson,
  SingerTypeCompany
} from '../../../services/contract/constants'
export default {
  components: {
    DragableList,
    PlatformEmployeeSelect
  },
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  created() {
    this.signs = this.value
    key = this.signs.length
  },
  mounted() {
    this.signs = this.value.map(sign => {
      if (sign.signerUserId == 0) {
        return {
          ...sign,
          participateType: 0,
          key: Math.random()
        }
      } else {
        return {
          ...sign,
          participateType: 1,
          key: Math.random()
        }
      }
    })
  },
  watch: {
    signs: {
      handler(n) {
        this.$emit('input', n)
      },
      deep: true
    }
  },
  data() {
    return {
      SingerTypePerson,
      SingerTypeCompany,
      columns: [
        {
          label: '参与主体',
          width: '120px'
        },
        {
          label: '参与方名称',
          width: '180px'
        },
        {
          label: '参与方式及顺序',
          width: '180px'
        },
        {
          label: '参与人员设定',
          width: '260px'
        },
        {
          label: '参与方及人员',
          width: '270px'
        },
        {
          label: '签署要求',
          width: '180px'
        },
        {
          label: '操作',
          width: 'auto'
        }
      ],
      signs: []
    }
  },
  methods: {
    getKey() {
      key++
      return key
    },
    addCompany() {
      const ckey = this.getKey()
      this.signs.push({
        key: ckey,
        signerType: '2',
        name: `签署方${ckey}`,
        needWrite: true,
        needSign: true,
        signerUserId: 0,
        signWay: '1',
        participateType: 0
      })
    },
    addPerson() {
      const ckey = this.getKey()
      this.signs.push({
        key: ckey,
        signerType: '1',
        name: `签署方${ckey}`,
        needWrite: true,
        needSign: true,
        signerUserId: 0,
        signWay: '1',
        participateType: 0
      })
    },
    // 选择签署方及人员
    selectEmployee(userId, index) {
      // console.log(emp,'empempemp')
      this.signs[index].signerUserId = userId
    },
    // 切换参与人员设定
    participateTypeChange(type, index) {
      // 切换至发起时选择人员值为0
      if (type === 0) {
        this.signs[index].signerUserId = 0
      } else {
        this.signs[index].signerUserId = undefined
      }
    },
    signerTypeChange(type, sign, index) {
      if (sign.inFile) {
        this.$confirm(
          `<b>修改签署方类型提示</b>` +
            '<br/>修改后，文档中已经设定的该签署方控件，将全部被删除',
          '修改',
          {
            dangerouslyUseHTMLString: true,
            type: 'warning',
            closeOnClickModal: false
          }
        ).then(() => {
          this.signs[index].signerType = type
          if (type === SingerTypePerson) {
            this.signs[index].participateType = 0
          }
        })
      } else {
        this.signs[index].signerType = type
        if (type === SingerTypePerson) {
          this.signs[index].participateType = 0
        }
      }
    },
    deleteSign(sign, index) {
      if (sign.inFile) {
        this.$confirm(
          `确认要删除【${sign.name}】吗？` +
            '文档中已经设定的该签署方控件，将同时被删除',
          '删除',
          {
            dangerouslyUseHTMLString: true,
            type: 'warning',
            closeOnClickModal: false
          }
        ).then(async () => {
          this.signs.splice(index, 1)
        })
      } else {
        this.signs.splice(index, 1)
      }
    }
  }
}
</script>
<style scoped>
.person-select,
.enterprise-select {
  margin-right: 24px;
}
::v-deep .person-select.el-button {
  background: #fff;
  border: 1px solid #ffac04;
  border-radius: 8px;
  color: #ffac04;
}
::v-deep .enterprise-select.el-button {
  background: #fff;
  border: 1px solid #4f71ff;
  border-radius: 8px;
  color: #4f71ff;
}
::v-deep .person-select .el-input--suffix .el-input__inner {
  width: 100px;
  background: rgba(255, 172, 4, 0.06) !important;
  border-radius: 8px;
  border: 1px solid #ffac04;
}
::v-deep .enterprise-select .el-input--suffix .el-input__inner {
  width: 100px;
  background: #f7fafd;
  border: 1px solid #4f71ff;
  border-radius: 8px;
}

::v-deep .draggableItem {
  border-bottom: 1px solid #eef0f4;
}

::v-deep .el-input__inner {
  border-color: #dcdfe6 !important;
}
</style>