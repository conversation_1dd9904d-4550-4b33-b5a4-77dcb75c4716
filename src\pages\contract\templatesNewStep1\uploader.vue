<template>
  <el-upload
    class="uploader"
    ref="uploader"
    :action="action"
    :multiple="isMultiple"
    :on-success="handleSuccess"
    :on-progress="handleProgress"
    :on-error="handleUploadError"
    :before-upload="handleBeforeUpload"
    :limit="limit"
    :headers="{
      Authorization: `Bear<PERSON> ${token}`
    }"
    :on-exceed="handleExceed"
    :show-file-list="showFileList"
  >
    <slot>
      <el-button type="text" style="font-weight: 400">
        <i class="el-icon-paperclip" /> 添加附件
      </el-button>
    </slot>
  </el-upload>
</template>

<script>
import handleError from  '../../../helpers/handleError'
import store from '../../../helpers/store'
export default {
  name: 'DragUpload',
  data() {
    return {
      store
    }
  },
  computed: {
    action() {
      if (window.env.current === 'dev') {
        return 'http://************:8102/file/upload'
      }

      return `${window.env.api}/contract2/file/upload`
    },
    token() {
      return store.get('token')
    }
  },
  props: {
    showFileList: {
      type: Boolean,
      default: () => false
    },
    limit: {
      type: Number,
      default: 20
    },
    isMultiple: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleSuccess(res, file, fileList) {
      this.$emit('handleSuccess', res, file, fileList)
    },
    handleProgress(res, file, fileList) {
      this.$emit('handleProgress', res, file, fileList)
    },
    handleUploadError(err, file, fileList) {
      this.$emit('handleUploadError', err, file, fileList)
    },
    handleExceed() {
      handleError({ message: `文件最多上传${this.limit}个` })
    },
    handleBeforeUpload(file) {
      let fileName = file.name
      let is50M = file.size / 1024 / 1024 < 50
      if (!is50M) {
        this.$message.error('文件必须小于50M')
        return false
      }
      let pos = fileName.lastIndexOf('.')
      let lastName = fileName.substring(pos, fileName.length)
      if (
        lastName.toLowerCase() !== '.doc' &&
        lastName.toLowerCase() !== '.docx' &&
        lastName.toLowerCase() !== '.wps' &&
        lastName.toLowerCase() !== '.jpg' &&
        lastName.toLowerCase() !== '.jpeg' &&
        lastName.toLowerCase() !== '.png' &&
        lastName.toLowerCase() !== '.pdf' &&
        lastName.toLowerCase() !== '.xls' &&
        lastName.toLowerCase() !== '.xlsx' &&
        lastName.toLowerCase() !== '.zip' &&
        lastName.toLowerCase() !== '.rar' &&
        lastName.toLowerCase() !== '.mp4' &&
        lastName.toLowerCase() !== '.amr' &&
        lastName.toLowerCase() !== '.mp3' &&
        lastName.toLowerCase() !== '.wav'
      ) {
        this.$message.error(
          '文件必须为doc、docx、wps、jpg、jpeg、png、pdf、xls、xlsx、zip、rar、mp4、amr、mp3、wav类型'
        )
        return false
      }
    }
  }
}
</script>

<style></style>