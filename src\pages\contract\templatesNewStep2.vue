<template>
  <div class="templatesNewStep2" @mousedown="pageFieldBlur">
    <div v-if="templateMakeDetail">
      <TopBar title="新建模板" :step="1" @back="back" @save="save" @prev="prev">
        <template v-slot:submit>
          <el-button title="提交启用模板" type="primary" @click="submit">
            提交
          </el-button>
        </template>
      </TopBar>
      <div style="display: flex">
        <div
          class="webkit-scrollbar"
          :style="{
            flex: '0 0 240px',
            height: 'calc(100vh - 48px)',
            overflowY: 'auto',
            padding: '12px 24px',
            borderRight: '1px solid #EEF0F4',
            fontSize: '12px'
          }"
        >
          <h3>选择控件字段</h3>
          <el-input
            placeholder="请输入字段名称搜素"
            :value="fieldsFilterKeyword"
            @input="filterFields"
            clearable
          />
          <el-collapse :value="activedFieldGroups">
            <el-collapse-item name="signature">
              <template slot="title">
                <Title :title="'签章控件'" />
              </template>
              <SignatureFieldGroup
                :currentScale="currentScale"
                :filter="fieldsFilterKeyword"
                :signatures="templateMakeDetail.signStepList"
              />
            </el-collapse-item>
            <el-collapse-item name="custom">
              <template slot="title">
                <Title :title="'自定义填写字段'" />
              </template>
              <CustomFieldGroup
                :currentScale="currentScale"
                @add="addCustomField"
                @del="deleteCustomField"
                :fields="templateMakeDetail.customFieldList"
                :otherFieldGroups="templateMakeDetail.fieldGroupList"
                :filter="fieldsFilterKeyword"
              />
            </el-collapse-item>

            <el-collapse-item
              v-for="fieldGroup in normalFieldGroups"
              :name="fieldGroup.name"
              :key="fieldGroup.name"
            >
              <template slot="title">
                <Title :title="fieldGroup.name" />
              </template>
              <NormalFieldGroup
                :fieldGroupName="fieldGroup.name"
                :currentScale="currentScale"
                :fields="fieldGroup.fieldList"
                :filter="fieldsFilterKeyword"
              />
            </el-collapse-item>

            <el-collapse-item :name="associatedContractGroup.name">
              <template slot="title">
                <Title :title="associatedContractGroup.name" />
              </template>
              <AssociatedContractFieldGroup
                :remark="associatedContractGroup.remark"
                :currentScale="currentScale"
                :fields="associatedContractGroup.fieldList"
                :filter="fieldsFilterKeyword"
              />
            </el-collapse-item>
          </el-collapse>
        </div>
        <div
          :style="{
            flex: '1 1 auto',
            width: '0px', //直接自动计算
            height: 'calc(100vh - 48px)',
            background: '#f2f2f2',
            overflow: 'hidden'
          }"
        >
          <FilePages
            ref="filePages"
            :fileId="templateMakeDetail.fileList[fileIndex].fileId"
            :images="templateMakeDetail.fileList[fileIndex].archiveImageList"
            @scaleChange="scale => (currentScale = scale)"
            @itemDrop="itemDrop"
          >
            <template v-slot="{ pageNo, fileId }">
              <FilePageField
                :key="index"
                v-for="(pageField, index) in pageFields.filter(
                  item => item.pageNo === pageNo && item.fileId === fileId
                )"
                :field="fields.find(item => item.id === pageField.fieldId)"
                :pageField="pageField"
                :focusPageFiled="currentPageField"
                :currentScale="currentScale"
                :isSubmitting="isSubmitting"
                @focus="pageFieldFocus"
                @change="pageFieldChange"
                @delete="pageFieldDelete"
              />
            </template>
          </FilePages>
          <div
            style="
              width: 40px;
              height: 40px;
              background: #5072ff;
              border-radius: 8px;
              text-align: center;
              position: sticky;
              bottom: 60px;
              right: 19px;
              float: right;
              cursor: pointer;
            "
            v-if="noSignStepFieldsCount"
            @click="locateNoSignStepField"
          >
            <el-tooltip
              placement="top"
              :content="`共有${noSignStepFieldsCount}个字段未设置或信息设置不完整，点击按钮快速定位。`"
            >
              <el-badge :value="noSignStepFieldsCount">
                <LocationIcon
                  style="
                    color: #fff;
                    width: 30px;
                    height: 30px;
                    position: relative;
                    top: 5px;
                  "
                />
              </el-badge>
            </el-tooltip>
          </div>
        </div>
        <div
          class="webkit-scrollbar"
          :style="{
            flex: '0 0 240px',
            height: 'calc(100vh - 48px)',
            overflowY: 'auto',
            padding: '12px 24px',
            borderLeft: '1px solid #EEF0F4',
            fontSize: '12px'
          }"
        >
          <el-tabs v-model="activeName">
            <el-tab-pane label="控件设置" name="pageFileSettings">
              <FilePageFieldSettings
                :field="
                  fields.find(item => {
                    if (!currentPageField) {
                      return false
                    }
                    return item.id === currentPageField.fieldId
                  }) || {}
                "
                :pageField="currentPageField"
                :signatures="templateMakeDetail.signStepList"
                @saveCustomField="saveCustomField"
                @removeCustomField="removeCustomField"
                @selectSignStep="selectSignStep"
                @changeFieldName="changeFieldName"
                @changeCustomFieldName="changeCustomFieldName"
              />
              <div v-if="!currentPageField" style="text-align: center">
                <img
                  src="../../assets/images/icon/<EMAIL>"
                  height="48px"
                  width="48px"
                  style="margin: 20px 0"
                />
                <br />
                请选择填充域或签章进行设置
              </div>
            </el-tab-pane>
            <el-tab-pane
              :label="`待签署文件 (${templateMakeDetail.fileList.length}份)`"
              name="files"
            >
              <el-collapse
                :value="[
                  'waitingSignatueFiles',
                  'attachmentFiles',
                  'signProcesses'
                ]"
              >
                <el-collapse-item name="waitingSignatueFiles">
                  <template slot="title">
                    <Title :title="`待签署文件`" />
                  </template>
                  <WaitingSignatueFiles
                    :currentFileIndex="fileIndex"
                    :files="templateMakeDetail.fileList"
                    @select="selectFile"
                  />
                </el-collapse-item>
                <el-collapse-item
                  name="attachmentFiles"
                  v-if="templateMakeDetail.attachmentList.length"
                >
                  <template slot="title">
                    <Title
                      :title="`附件 (${templateMakeDetail.attachmentList.length}份)`"
                    />
                  </template>
                  <AttachmentFiles :files="templateMakeDetail.attachmentList" />
                </el-collapse-item>
                <el-collapse-item name="signProcesses">
                  <template slot="title"> 签署流程 </template>
                  <SignProcesses
                    :fields="fields"
                    :pageFields="pageFields"
                    :signSteps="templateMakeDetail.signStepList"
                  />
                </el-collapse-item>
              </el-collapse>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <SubmitErrorsDialog :visible.sync="dialog.errors" :errors="errors" />
      <SubmitWarningsDialog
        :visible.sync="dialog.warnings"
        :warnings="warnings"
        @close="dialog.warnings = false"
        @submit="submitWarning"
      />
      <div
        :style="{
          display: 'none'
        }"
      >
        <img
          @load="fileImageLoad($event, file.fileId)"
          :src="file.archiveImageList[0]"
          :key="file.fileId"
          v-for="file of templateMakeDetail.fileList"
        />
      </div>
    </div>
  </div>
</template>

<script>
import TopBar from '../../components/contract/template/topBar.vue'
import Title from '../../components/contract/title.vue'
import SignatureFieldGroup from '../../components/contract/template/fieldGroup/signature.vue'
import CustomFieldGroup from '../../components/contract/template/fieldGroup/custom.vue'
import NormalFieldGroup from '../../components/contract/template/fieldGroup/normal.vue'
import AssociatedContractFieldGroup from '../../components/contract/template/fieldGroup/associatedContract.vue'
import FilePageFieldSettings from '../../components/contract/template/filePageFieldSettings.vue'
import WaitingSignatueFiles from '../../components/contract/template/waitingSignatueFiles.vue'
import SubmitErrorsDialog from '../../components/contract/template/submitErrorsDialog.vue'
import SubmitWarningsDialog from '../../components/contract/template/submitWarningsDialog.vue'
import AttachmentFiles from '../../components/contract/template/attachmentFiles.vue'
import FilePages from '../../components/contract/file/pages.vue'
import FilePageField from '../../components/contract/template/filePageField.vue'
import SignProcesses from '../../components/contract/template/signProcesses.vue'
import LocationIcon from '../../components/contract/template/locationIcon.vue'
import formatPageFieldsPx from '../../formatters/contract/template/formatPageFieldsPx'
import makeControlGroupList from '../../formatters/contract/template/makeControlGroupList'
import makeField from '../../formatters/contract/template/makeField'
import makeFieldId from '../../formatters/contract/template/makeFieldId'
import makePageField from '../../formatters/contract/template/makePageField'
import makeDetail2Fields from '../../formatters/contract/template/makeDetail2Fields'
import makeCustomFieldFromField from '../../formatters/contract/template/makeCustomFieldFromField'
import makeDetail2PageFields from '../../formatters/contract/template/makeDetail2PageFields'
import autoChooseSignStep from './templatesNewStep2/autoChooseSignStep'
import validFieldsAndPageFields from './templatesNewStep2/validFieldsAndPageFields'
import parentHadClass from './templatesNewStep2/parentHadClass'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import deepClone from '../../helpers/deepClone'
import { PageFieldDefaultWidth } from './constants'
import isSignatureFieldType from '../../components/contract/template/isSignatureFieldType'
import { WriteTypeSingleLineText } from '../../services/contract/constants'
import makeContractClient from '../../services/contract/makeClient'

const client = makeContractClient()

export default {
  components: {
    TopBar,
    Title,
    SignatureFieldGroup,
    CustomFieldGroup,
    NormalFieldGroup,
    AssociatedContractFieldGroup,
    FilePages,
    FilePageField,
    FilePageFieldSettings,
    WaitingSignatueFiles,
    SubmitErrorsDialog,
    SubmitWarningsDialog,
    AttachmentFiles,
    SignProcesses,
    LocationIcon
  },
  async created() {
    const id = this.$route.params.id
    if (!id) {
      throw new Error('id is required')
    }
    const loading = this.$loading({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255,255, 0.7)'
    })

    const [err, r] = await client.templateMakeDetail({
      body: {
        id: id
      }
    })
    if (err) {
      handleError(err)
      return
    }
    this.templateMakeDetail = r.data
    this.templateMakeDetail.attachmentList = r.data.attachmentList || []
    for (var item of this.templateMakeDetail.customFieldList) {
      //当进入时候，将默认返回的自定义字段 设置为常用字段 为后续交互做准备
      item.common = true
      //因为自定义字段在签署阶段可以被发起方修改 也可以被合适的填写方修改
      //其属性为true才可以过后端检测
      //因为后端认为前端可以直接加，不改后端，故而在此增补
      item.modifiable = true
      item.writeable = true
    }

    this.fields = makeDetail2Fields(this.templateMakeDetail)
    this.pageFields = makeDetail2PageFields(this.templateMakeDetail)

    const customFields = makeCustomFieldFromField(this.fields)
    this.templateMakeDetail.customFieldList = customFields.concat(
      this.templateMakeDetail.customFieldList
    )

    var fgs = ['signature', 'custom']
    for (var item of this.templateMakeDetail.fieldGroupList) {
      fgs.push(item.name)
    }
    this.activedFieldGroups = fgs

    loading.close()
  },
  mounted() {
    document.body.style.margin = 0
    document.body.style.padding = 0
    document.body.style.overflow = 'hidden'

    document
      .querySelectorAll('.el-collapse-item__wrap')
      .forEach(item => (item.style.border = 'none'))

    const _this = this
    window.addEventListener('keydown', e => {
      if (!_this.currentPageField) {
        return
      }
      if (e.target.tagName !== 'BODY') {
        return
      }
      if (e.key === 'ArrowLeft') {
        _this.currentPageField.coordX -= 5
      }
      if (e.key === 'ArrowRight') {
        _this.currentPageField.coordX += 5
      }
      if (e.key === 'ArrowUp') {
        _this.currentPageField.coordY -= 5
      }
      if (e.key === 'ArrowDown') {
        _this.currentPageField.coordY += 5
      }
      e.preventDefault()
    })
  },
  methods: {
    back() {
      const _this = this
      this.$confirm('是否保存对模板的修改？', '返回提示', {
        cancelButtonText: '不保存',
        confirmButtonText: '保存草稿',
        type: 'info',
        distinguishCancelAndClose: true,
        closeOnClickModal: false
      })
        .then(() => {
          this.save(() => {
            _this.$router.push('/templates')
          })
        })
        .catch(action => {
          if (action === 'cancel') {
            this.$router.push('/templates')
          }
        })
    },
    prev() {
      const _this = this
      const id = this.$route.params.id
      this.$confirm('是否保存对模板的修改？', '返回上一步提示', {
        cancelButtonText: '不保存',
        confirmButtonText: '保存草稿',
        type: 'info',
        distinguishCancelAndClose: true,
        closeOnClickModal: false
      })
        .then(() => {
          this.save(() => {
            _this.$router.push(`/templates/${id}/step1/edit`)
          })
        })
        .catch(action => {
          if (action === 'cancel') {
            this.$router.push(`/templates/${id}/step1/edit`)
          }
        })
    },
    async save(cb) {
      var req = {
        templateId: this.templateMakeDetail.templateId,
        controlGroupList: []
      }
      req.controlGroupList = makeControlGroupList(
        this.fields,
        this.pageFields,
        this.fileImageSizes
      )

      const [err, _] = await client.templateMakeSave({
        body: req
      })
      if (err) {
        handleError(err)
        return err
      }
      if (cb) {
        cb()
      }
      handleSuccess('保存成功')
      return null
    },
    async _submit() {
      var req = {
        templateId: this.templateMakeDetail.templateId,
        controlGroupList: []
      }
      req.controlGroupList = makeControlGroupList(
        this.fields,
        this.pageFields,
        this.fileImageSizes
      )

      const [err, _] = await client.templateMakeSubmit({
        body: req
      })
      if (err) {
        handleError(err)
        return err
      }

      handleSuccess('提交成功')
      this.dialog.warnings = false
      return null
    },
    async submitWarning() {
      const err = await this._submit()
      if (!err) {
        this.$router.push('/templates')
      }
    },
    async submit() {
      const r = validFieldsAndPageFields(
        this.fields,
        this.pageFields,
        this.templateMakeDetail
      )
      const errors = r.errors
      const warnings = r.warnings

      if (Object.keys(errors).length) {
        this.dialog.errors = true
        this.errors = errors
        this.isSubmitting = true
        return
      }
      if (Object.keys(warnings).length) {
        this.dialog.warnings = true
        this.warnings = warnings
        return
      }
      const err = await this._submit()
      if (!err) {
        this.isSubmitting = false
        this.$router.push('/templates')
      }
    },
    addCustomField(name) {
      this.templateMakeDetail.customFieldList.unshift({
        name: name,
        modifiable: true,
        writeable: true
      })
    },
    updateCustomFieldName() {
      //@todo
      // client.updateCustomFieldName()
    },
    deleteCustomField(field, isNormal, isCommon) {
      const exitedField = this.fields.find(item => item.name === field.name)
      if (exitedField) {
        this.pageFields = this.pageFields.filter(
          item => item.fieldId !== exitedField.id
        )
      }
      //通用字段 且 勾选了删除通用字段
      if (isCommon && field.common) {
        this.templateMakeDetail.customFieldList =
          this.templateMakeDetail.customFieldList.filter(
            item => item.name !== field.name
          )
        this.removeCustomField(field)
      }
      if (isNormal && !field.common) {
        this.templateMakeDetail.customFieldList =
          this.templateMakeDetail.customFieldList.filter(
            item => item.name !== field.name
          )
      }
    },
    pageFieldChange(pageFieldId, changedParams) {
      var pageField = this.pageFields.find(item => item.id === pageFieldId)
      if (!pageField) {
        throw new Error('logic err, pageField not existed')
      }
      // console.log('changePageField before', pageField, changedParams)

      const index = this.pageFields.findIndex(item => item.id === pageFieldId)
      for (var key in changedParams) {
        this.pageFields[index][key] = changedParams[key]
      }
      // console.log('changePageField after', this.pageFields[index])
    },
    itemDrop(e, fileId, pageNo) {
      //左侧移入
      const s = e.dataTransfer.getData('field')
      if (s) {
        const field = JSON.parse(s)
        if (e.target.tagName !== 'IMG') {
          this.$message({
            message: '不可叠放',
            type: 'error'
          })
          return
        } else {
          const coordX = e.offsetX - field.mouseX
          const coordY = e.offsetY - field.mouseY
          const params = {
            field,
            coordX,
            coordY,
            fileId,
            pageNo
          }

          this.pageFieldAdd(params)
          return
        }

        return
      }
      //页面内移动
      const ps = e.dataTransfer.getData('pageField')

      if (ps) {
        const pageField = JSON.parse(ps)
        //不可跨页
        if (pageField.pageNo !== pageNo) {
          return
        }
        //不可叠放
        if (e.target.tagName !== 'IMG') {
          return
        }
        const coordX = e.offsetX - pageField.mouseX
        const coordY = e.offsetY - pageField.mouseY
        this.pageFieldChange(pageField.id, {
          coordX,
          coordY
        })
      }
    },
    pageFieldAdd(params) {
      if (!params.field.fieldGroupName) {
        params.field.fieldGroupName = ''
      }
      if (this.pageFields.length >= 100) {
        handleError('系统字段和自定义新增字段总和不能超过100个')
        return
      }

      //提前计算出signStep以保持fieldId正确
      const fieldSignInfos = autoChooseSignStep(
        params.field.type,
        params.field.fieldGroupName,
        this.templateMakeDetail.signStepList
      )

      params.field.signStepId = fieldSignInfos.signStepId || ''
      const pageField = makePageField(params)
      const normalPageFields = this.pageFields.filter(item => {
        const field = this.fields.find(c => c.id === item.fieldId)
        return !isSignatureFieldType(field.type)
      })
      var lastPageField = null
      if (normalPageFields && normalPageFields.length > 0) {
        lastPageField = normalPageFields[normalPageFields.length - 1]
      }
      var lastField = null
      if (lastPageField) {
        lastField = this.fields.find(item => item.id === lastPageField.fieldId)
      }
      //维持最后一个控件的字号
      if (
        lastPageField &&
        lastPageField.fontSize &&
        //签章类型不走此机制
        !isSignatureFieldType(params.field.type) &&
        //必须是单行 多行的不再维持
        lastField.writeType === WriteTypeSingleLineText
      ) {
        pageField.font = lastPageField.font
        pageField.fontSize = lastPageField.fontSize
        pageField.height = lastPageField.height
        const p = lastPageField.fontSize * params.field.name.length
        var w = PageFieldDefaultWidth
        var existedPageFields = this.pageFields.filter(
          item => item.fieldId === pageField.fieldId
        )
        if (existedPageFields && existedPageFields.length) {
          w = existedPageFields[existedPageFields.length - 1].width
        }
        if (w < p) {
          w = p
        }
        if (w < pageField.width) {
          w = pageField.width
        }
        pageField.width = w
      }

      this.pageFields.push(pageField)

      const fieldExisted = this.fields.filter(
        item => item.id === pageField.fieldId
      ).length

      if (!fieldExisted) {
        var field = makeField(params.field)
        field.signStepId = fieldSignInfos.signStepId
        field.signStepName = fieldSignInfos.signStepName
        field.signerType = fieldSignInfos.signerType
        this.fields.push(field)
      }

      this.currentPageField = pageField
      this.activeName = 'pageFileSettings'
      console.log('pageFieldAdded', this.fields, this.pageFields)
    },
    pageFieldDelete(pageField) {
      var pageField = this.pageFields.find(item => item.id === pageField.id)
      if (!pageField) {
        throw new Error('logic err, pageField not existed')
      }
      const index = this.pageFields.findIndex(item => item.id === pageField.id)
      this.pageFields.splice(index, 1)

      const count = this.pageFields.filter(
        item => item.fieldId === pageField.fieldId
      ).length

      if (count === 0) {
        const findex = this.fields.findIndex(
          item => item.id === pageField.fieldId
        )
        this.fields.splice(findex, 1)
      }

      this.currentPageField = null
      this.activeName = 'files'
    },
    pageFieldFocus(pageField) {
      this.currentPageField = null
      this.$nextTick(() => {
        this.currentPageField = pageField
        this.activeName = 'pageFileSettings'
      })
    },
    pageFieldBlur(e) {
      if (
        parentHadClass(e.target, 'pageField') ||
        parentHadClass(e.target, 'pageFieldSettings') ||
        parentHadClass(e.target, '-select-item')
      ) {
        return
      }
      console.log('blur')
      this.currentPageField = null
      this.activeName = 'files'
    },
    async saveCustomField(field) {
      const [err, _] = await client.templateSaveCustomField({
        body: {
          templateId: this.templateMakeDetail.templateId,
          name: field.name,
          value: field.value
        }
      })
      if (err) {
        handleError(err)
        field.common = false
        return
      }
      for (var c of this.templateMakeDetail.customFieldList) {
        if (c.name === field.name) {
          c.common = true
        }
      }
    },
    async changeCustomFieldName(newName, oldCustomField) {
      for (var c of this.templateMakeDetail.customFieldList) {
        if (c.name === oldCustomField.name) {
          c.name = newName
        }
      }

      //非通用字段 不需要更新名称
      if (!oldCustomField.common) {
        return
      }

      const [err, _] = await client.templateUpdateCustomFieldName({
        body: {
          oldName: oldCustomField.name,
          newName: newName
        }
      })
      if (err) {
        handleError(err)
        return
      }
    },
    async removeCustomField(field) {
      const [err, _] = await client.templateRemoveCustomField({
        body: {
          name: field.name
        }
      })
      if (err) {
        handleError(err)
        field.common = true
        return
      }
    },
    //将pageField移入targetField下
    selectSignStep(field, pageField, signStep) {
      const targetFieldId = makeFieldId(field.name, signStep.id)

      //将pageField移入
      pageField.fieldId = targetFieldId

      const existedTargetField = this.fields.find(
        item => item.id === targetFieldId
      )

      if (!existedTargetField) {
        const targetField = deepClone(field)
        targetField.signStepName = signStep.name
        targetField.signerType = signStep.signerType
        targetField.signStepId = signStep.id
        targetField.id = targetFieldId
        this.fields.push(targetField)
      }

      //移入后，原来的field下pageField数量为0，则需要删除原有的field
      const pageFieldsRemainCount = this.pageFields.filter(
        item => item.fieldId === field.id
      ).length
      if (pageFieldsRemainCount === 0) {
        const index = this.fields.findIndex(item => item.id === field.id)
        this.fields.splice(index, 1)
      }

      console.log('merged fields', this.fields)
      console.log('merged pageFields', this.pageFields)
    },
    //imageIndex 暂未启用 默认同一文件中图片大小一致
    fileImageLoad(e, fileId) {
      var width = e.target.width
      var height = e.target.height
      this.fileImageSizes[fileId] = [width, height]
      // debugger
      formatPageFieldsPx(this.pageFields, this.fileImageSizes)
    },
    selectFile(i) {
      this.fileIndex = i
      document.getElementById('pagesBox').scroll({
        top: 0,
        behavior: 'smooth'
      })
    },
    filterFields(v) {
      this.fieldsFilterKeyword = v
      var r = ['signature', 'custom']
      for (var item of this.templateMakeDetail.fieldGroupList) {
        r.push(item.name)
      }
      this.activedFieldGroups = r
    },
    changeFieldName(v) {
      console.log('file', v)
    },
    locateNoSignStepField() {
      const firstField = this.fields.find(item => !item.signStepId)
      const firstPageField = this.pageFields.find(
        item => item.fieldId === firstField.id
      )
      const fileIndex = this.templateMakeDetail.fileList.findIndex(
        item => item.fileId === firstPageField.fileId
      )
      if (fileIndex !== this.fileIndex) {
        this.selectFile(fileIndex)
      }

      this.$refs.filePages.scrollToFirstPageField(firstPageField)
    }
  },
  computed: {
    noSignStepFieldsCount() {
      var count = 0
      for (const pageField of this.pageFields) {
        const field = this.fields.find(item => item.id === pageField.fieldId)
        if (!field.signStepId) {
          count++
        }
      }
      return count
    },
    normalFieldGroups() {
      var r = []
      for (var fg of this.templateMakeDetail.fieldGroupList) {
        if (!fg.name.includes('关联原合同字段')) {
          r.push(fg)
        }
      }

      return r
    },
    associatedContractGroup() {
      for (var fg of this.templateMakeDetail.fieldGroupList) {
        if (fg.name.includes('关联原合同字段')) {
          return fg
        }
      }

      return null
    }
  },
  data() {
    return {
      fileIndex: 0,
      currentPageField: null,
      activeName: 'files',
      //维持左侧拖拽到页面时候的框的大小
      currentScale: 1,
      fields: [],
      pageFields: [],
      fieldsFilterKeyword: '',
      dialog: {
        errors: false,
        warnings: false
      },
      errors: {},
      warnings: {},
      template: null,
      templateMakeDetail: null,
      //用于计算后续的比例
      fileImageSizes: {},
      //左侧需要展开的折叠面板
      activedFieldGroups: [],
      //处于提交中，会导致每个pageField检查自己是否正确
      isSubmitting: false,
      submitBtnLoading: false
    }
  }
}
</script>