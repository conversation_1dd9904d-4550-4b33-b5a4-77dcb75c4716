import {
  FieldTypeCompany,
  FieldTypeCustom,
  FieldTypeDate,
  FieldTypePerson
} from '../../../services/contract/constants'
export const autoChooseSignStep = (fieldType, fieldGroupName, signStepList) => {
  var r = {}

  //仅有一个 则选择它
  if (signStepList && signStepList.length === 1) {
    r.signStepId = signStepList[0].id
    r.signStepName = signStepList[0].name
    r.signerType = signStepList[0].signerType
    return r
  }

  if (fieldType === FieldTypeCustom) {
    return r
  }

  //日期签章控件 总是选择第一个
  if (fieldType === FieldTypeDate && signStepList.length > 1) {
    r.signStepId = signStepList[0].id
    r.signStepName = signStepList[0].name
    r.signerType = signStepList[0].signerType
    return r
  }

  if (fieldType === FieldTypePerson) {
    const personSignStepList = signStepList.filter(
      item => item.signerType === FieldTypePerson
    )
    //仅有一个则选择它
    if (personSignStepList && personSignStepList.length === 1) {
      r.signStepId = personSignStepList[0].id
      r.signStepName = personSignStepList[0].name
      r.signerType = personSignStepList[0].signerType
    }
  }
  if (
    fieldType === FieldTypeCompany ||
    (fieldGroupName && fieldGroupName.includes('合同公司信息'))
  ) {
    const companySignStepList = signStepList.filter(
      item => item.signerType === FieldTypeCompany
    )

    if (companySignStepList && companySignStepList.length === 1) {
      r.signStepId = companySignStepList[0].id
      r.signStepName = companySignStepList[0].name
      r.signerType = companySignStepList[0].signerType
    }
  }

  //以上都未命中 则给默认值为个人
  if (!r.signStepId) {
    const personSignStepList = signStepList.filter(
      item => item.signerType === FieldTypePerson
    )

    if (personSignStepList && personSignStepList.length === 1) {
      r.signStepId = personSignStepList[0].id
      r.signStepName = personSignStepList[0].name
      r.signerType = personSignStepList[0].signerType
    }
  }

  return r
}

export default autoChooseSignStep