const walkParentClass = (el, fn) => {
  fn(el.className)
  if (el.parentElement.tagName === 'BODY') {
    return
  }
  walkParentClass(el.parentElement, fn)
}
const parentHadClass = (el, className) => {
  var had = false
  walkParentClass(el, cClassName => {
    if (cClassName && cClassName.includes && cClassName.includes(className)) {
      had = true
    }
  })

  return had
}
export default parentHadClass