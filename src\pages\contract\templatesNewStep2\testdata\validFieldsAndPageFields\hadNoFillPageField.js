export default [
    [
        {
            "id": "1462940397",
            "name": "企业印章",
            "type": "2",
            "isCustomControl": false,
            "common": false,
            "signStepId": 226,
            "signStepName": "q1",
            "signerType": "2",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "497663861",
            "name": "个人签名",
            "type": "1",
            "isCustomControl": false,
            "common": false,
            "signStepId": 224,
            "signStepName": "g1",
            "signerType": "1",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "1462940399",
            "name": "企业印章",
            "type": "2",
            "isCustomControl": false,
            "common": false,
            "signStepId": 228,
            "signStepName": "q3",
            "signerType": "2",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "1462940398",
            "name": "企业印章",
            "type": "2",
            "isCustomControl": false,
            "common": false,
            "signStepId": 227,
            "signStepName": "q2",
            "signerType": "2",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "-326452570",
            "name": "签署日期",
            "type": "3",
            "isCustomControl": false,
            "common": false,
            "signStepId": 224,
            "signStepName": "g1",
            "signerType": "1",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "-326452569",
            "name": "签署日期",
            "type": "3",
            "isCustomControl": false,
            "common": false,
            "signStepId": 225,
            "signStepName": "g2",
            "signerType": "1",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "-326452568",
            "name": "签署日期",
            "type": "3",
            "isCustomControl": false,
            "common": false,
            "signStepId": 226,
            "signStepName": "q1",
            "signerType": "2",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "-326452567",
            "name": "签署日期",
            "type": "3",
            "isCustomControl": false,
            "common": false,
            "signStepId": 227,
            "signStepName": "q2",
            "signerType": "2",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "-326452566",
            "name": "签署日期",
            "type": "3",
            "isCustomControl": false,
            "common": false,
            "signStepId": 228,
            "signStepName": "q3",
            "signerType": "2",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "-1425418729",
            "name": "aaa",
            "type": "4",
            "isCustomControl": true,
            "common": true,
            "signStepId": 228,
            "signStepName": "q3",
            "signerType": "2",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "-1425418733",
            "name": "aaa",
            "type": "4",
            "isCustomControl": true,
            "common": true,
            "signStepId": 224,
            "signStepName": "g1",
            "signerType": "1",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "-1425418732",
            "name": "aaa",
            "type": "4",
            "isCustomControl": true,
            "common": true,
            "signStepId": 225,
            "signStepName": "g2",
            "signerType": "1",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "-1425418731",
            "name": "aaa",
            "type": "4",
            "isCustomControl": true,
            "common": true,
            "signStepId": 226,
            "signStepName": "q1",
            "signerType": "2",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "-1425418730",
            "name": "aaa",
            "type": "4",
            "isCustomControl": true,
            "common": true,
            "signStepId": 227,
            "signStepName": "q2",
            "signerType": "2",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        },
        {
            "id": "-1494572497",
            "name": "aaa",
            "type": "4",
            "isCustomControl": true,
            "common": true,
            "signStepId": "",
            "signStepName": "",
            "signerType": "",
            "modifiable": false,
            "writeable": false,
            "writeType": "1",
            "writeSort": 0,
            "writeRequired": true,
            "value": ""
        }
    ],
    [
        {
            "id": "844790289-45376",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 131,
            "coordY": 135.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "",
            "fieldId": "497663861"
        },
        {
            "id": "306216199-42890",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 488,
            "coordY": 141.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "",
            "fieldId": "1462940397"
        },
        {
            "id": "844790289-63847",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 125,
            "coordY": 350.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "",
            "fieldId": "497663861"
        },
        {
            "id": "306216199-13682",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 678,
            "coordY": 121.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "",
            "fieldId": "1462940398"
        },
        {
            "id": "306216199-86018",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 887,
            "coordY": 129.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "",
            "fieldId": "1462940399"
        },
        {
            "id": "895782018-75392",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 480,
            "coordY": 352.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "yyyy年MM月dd日",
            "fieldId": "-326452570"
        },
        {
            "id": "895782018-17663",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 718,
            "coordY": 335.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "yyyy年MM月dd日",
            "fieldId": "-326452569"
        },
        {
            "id": "895782018-25950",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 575,
            "coordY": 429.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "yyyy年MM月dd日",
            "fieldId": "-326452568"
        },
        {
            "id": "895782018-66919",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 817.40625,
            "coordY": 454.5625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "yyyy年MM月dd日",
            "fieldId": "-326452567"
        },
        {
            "id": "895782018-60536",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 1016.40625,
            "coordY": 415.5625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "yyyy年MM月dd日",
            "fieldId": "-326452566"
        },
        {
            "id": "-1494572497-51137",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 157,
            "coordY": 713.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "",
            "fieldId": "-1425418733"
        },
        {
            "id": "-1494572497-29923",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 340,
            "coordY": 696.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "",
            "fieldId": "-1425418732"
        },
        {
            "id": "-1494572497-97617",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 584,
            "coordY": 704.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "",
            "fieldId": "-1425418731"
        },
        {
            "id": "-1494572497-72072",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 920,
            "coordY": 665.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "",
            "fieldId": "-1425418729"
        },
        {
            "id": "-1494572497-55865",
            "fileId": 170,
            "pageNo": 1,
            "coordX": 627,
            "coordY": 591.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "",
            "fieldId": "-1425418730"
        },
        {
            "id": "-1494572497-12268",
            "fileId": 170,
            "pageNo": 2,
            "coordX": 189,
            "coordY": 50.0625,
            "width": 170,
            "height": 24,
            "font": "",
            "fontSize": 16,
            "textAlign": "1",
            "dateFormat": "",
            "fieldId": "-1494572497"
        }
    ],
    {
        "fileList": [
            {
                "id": 170,
                "archiveFile": {
                    "archiveId": "67883a6565494cc68eaadca0a54c7712",
                    "name": "劳动合同.pdf",
                    "size": 141182,
                    "pageCount": null
                }
            }
        ],
        "name": "劳动合同",
        "contractTypeId": 62,
        "enableNoRule": false,
        "noRuleId": 0,
        "enableApprove": false,
        "signDeadlineWay": "2",
        "signDeadlineValue": 15,
        "approveId": 0,
        "remark": "",
        "signStepList": [
            {
                "id": 224,
                "name": "g1",
                "signerType": "1",
                "needWrite": true,
                "needSign": true,
                "signerUserId": 0,
                "signatureType": "1",
                "inFile": false
            },
            {
                "id": 225,
                "name": "g2",
                "signerType": "1",
                "needWrite": true,
                "needSign": true,
                "signerUserId": 0,
                "signatureType": "1",
                "inFile": false
            },
            {
                "id": 226,
                "name": "q1",
                "signerType": "2",
                "needWrite": true,
                "needSign": true,
                "signerUserId": 0,
                "signatureType": "1",
                "inFile": false
            },
            {
                "id": 227,
                "name": "q2",
                "signerType": "2",
                "needWrite": true,
                "needSign": true,
                "signerUserId": 0,
                "signatureType": "1",
                "inFile": false
            },
            {
                "id": 228,
                "name": "q3",
                "signerType": "2",
                "needWrite": true,
                "needSign": true,
                "signerUserId": 0,
                "signatureType": "1",
                "inFile": false
            }
        ],
        "certifier": false,
        "carbonCopyList": [],
        "attachmentList": [],
        "templateId": 107,
        "compName": "清爽草莓味技术公司"
    },
    {
        "templateId": 107,
        "fieldGroupList": [
            {
                "name": "合同基础信息",
                "remark": "",
                "fieldList": [
                    {
                        "name": "合同编号",
                        "relationCode": "CONTRACT_NO",
                        "relationSource": "1"
                    },
                    {
                        "name": "合同开始日期",
                        "relationCode": "CONTRACT_START_DATE",
                        "relationSource": "1"
                    },
                    {
                        "name": "合同结束日期",
                        "relationCode": "CONTRACT_END_DATE",
                        "relationSource": "1"
                    }
                ]
            },
            {
                "name": "合同公司信息",
                "remark": "",
                "fieldList": [
                    {
                        "name": "合同主体名称",
                        "relationCode": "COMPANY_NAME",
                        "relationSource": "1"
                    },
                    {
                        "name": "公司地址",
                        "relationCode": "COMPANY_ADDRESS",
                        "relationSource": "1"
                    },
                    {
                        "name": "法人姓名",
                        "relationCode": "COMPANY_LEGAL_PERSON",
                        "relationSource": "1"
                    }
                ]
            },
            {
                "name": "个人信息",
                "remark": "",
                "fieldList": [
                    {
                        "name": "姓名",
                        "relationCode": "PERSON_NAME",
                        "relationSource": "1"
                    },
                    {
                        "name": "手机号码",
                        "relationCode": "PERSON_MOBILE",
                        "relationSource": "1"
                    },
                    {
                        "name": "证件类型",
                        "relationCode": "PERSON_IDENTIFY_TYPE",
                        "relationSource": "1"
                    },
                    {
                        "name": "证件号码",
                        "relationCode": "PERSON_IDENTIFY_NO",
                        "relationSource": "1"
                    },
                    {
                        "name": "部门",
                        "relationCode": "PERSON_DEPARTMENT",
                        "relationSource": "1"
                    },
                    {
                        "name": "岗位",
                        "relationCode": "PERSON_POST",
                        "relationSource": "1"
                    }
                ]
            },
            {
                "name": "关联原合同字段",
                "remark": "适用于签订解约、续约、变更协议",
                "fieldList": [
                    {
                        "name": "原合同名称(含唯一文件标识)",
                        "relationCode": "OLD_CONTRACT_NAME",
                        "relationSource": "1"
                    },
                    {
                        "name": "原合同签署日期",
                        "relationCode": "OLD_CONTRACT_SIGN_DATE",
                        "relationSource": "1"
                    }
                ]
            }
        ],
        "customFieldList": [
            {
                "name": "aaa",
                "relationCode": "",
                "relationSource": "1",
                "common": true
            }
        ],
        "fileList": [
            {
                "fileId": 170,
                "name": "劳动合同.pdf",
                "archiveId": "67883a6565494cc68eaadca0a54c7712",
                "size": 141182,
                "archiveImageList": [
                    "https://archive-qa.lanmaoly.com/api/v3/download/c271c6434d05430b91e8051d5879a79e/0.jpg",
                    "https://archive-qa.lanmaoly.com/api/v3/download/bd41e3df75f446c28f8891d6859c7e48/1.jpg",
                    "https://archive-qa.lanmaoly.com/api/v3/download/46d620c674344caaa14880a7ce417fe1/2.jpg",
                    "https://archive-qa.lanmaoly.com/api/v3/download/7731209f697c42b9b5789255ddf877b5/3.jpg"
                ],
                "controlGroupList": null
            }
        ]
    }
]