import isSignatureFieldType from '../../../components/contract/template/isSignatureFieldType'
import {
  FieldTypePerson,
  FieldTypeCompany,
  FieldTypeDate
} from '../../../services/contract/constants'
const validFieldsAndPageFields = (fields, pageFields, templateMakeDetail) => {
  // console.log([fields, pageFields, template, templateMakeDetail])
  var r = {
    warnings: {},
    errors: {}
  }
  for (var signStep of templateMakeDetail.signStepList) {
    //不需要填写
    if (!signStep.needWrite) {
      continue
    }
    if (
      !fields.filter(item => {
        //仅type=4算填充域 1,2，3算签章
        return (
          item.signStepId === signStep.id && !isSignatureFieldType(item.type)
        )
      }).length
    ) {
      if (!r.errors[`以下填写方, 未设定填充域`]) {
        r.errors[`以下填写方, 未设定填充域`] = []
      }
      r.errors[`以下填写方, 未设定填充域`].push(signStep.name)
    }
  }

  for (var signStep of templateMakeDetail.signStepList) {
    //不需要签署
    if (!signStep.needSign) {
      continue
    }
    //多个签署方均需要签署
    if (
      !fields.filter(item => {
        return (
          //找到自己的类型 且找到已设置过签署
          item.signerType === signStep.signerType &&
          item.signStepId === signStep.id
        )
      }).length
    ) {
      if (!r.errors[`以下签署方, 未设定签署方签章`]) {
        r.errors[`以下签署方, 未设定签署方签章`] = []
      }
      r.errors[`以下签署方, 未设定签署方签章`].push(signStep.name)
    }
  }

  for (var file of templateMakeDetail.fileList) {
    const pfields = pageFields.filter(item => item.fileId === file.fileId)
    var hadNoSignStep = true
    for (var pf of pfields) {
      const field = fields.find(item => item.id === pf.fieldId)
      if (field.type === FieldTypePerson || field.type === FieldTypeCompany) {
        hadNoSignStep = false
        break
      }
    }
    if (hadNoSignStep) {
      if (!r.errors[`以下待签署文件, 未设定签署方签章`]) {
        r.errors[`以下待签署文件, 未设定签署方签章`] = []
      }
      r.errors[`以下待签署文件, 未设定签署方签章`].push(file.name)
    }
  }

  var fm = {}
  for (var pageField of pageFields) {
    const field = fields.find(item => item.id === pageField.fieldId)
    if (!field.signStepId || field.name.trim() === '') {
      if (!r.errors[`存在N个填充域, 未设置填写字段或签署方`]) {
        r.errors[`存在N个填充域, 未设置填写字段或签署方`] = []
      }
      const file = templateMakeDetail.fileList.find(
        item => item.fileId === pageField.fileId
      )
      const tip = `${field.name} (${file.name}第${pageField.pageNo}页)`
      r.errors[`存在N个填充域, 未设置填写字段或签署方`].push(
        field.name ? tip : '名称为空'
      )
    }
  }

  for (var signStep of templateMakeDetail.signStepList) {
    //不需要签署的方，不需要检查签署日期
    if (!signStep.needSign) {
      continue
    }
    if (
      !fields.filter(
        item => item.type === FieldTypeDate && item.signStepId === signStep.id
      ).length
    ) {
      if (!r.warnings[`以下签署方, 未设定签署方日期`]) {
        r.warnings[`以下签署方, 未设定签署方日期`] = []
      }
      r.warnings[`以下签署方, 未设定签署方日期`].push(signStep.name)
    }
  }

  if (
    r.errors[`存在N个填充域, 未设置填写字段或签署方`] &&
    r.errors[`存在N个填充域, 未设置填写字段或签署方`].length
  ) {
    const length = r.errors[`存在N个填充域, 未设置填写字段或签署方`].length
    r.errors[`存在${length}个填充域, 未设置填写字段或签署方`] =
      r.errors[`存在N个填充域, 未设置填写字段或签署方`]
    delete r.errors[`存在N个填充域, 未设置填写字段或签署方`]
  }

  return r
}

export default validFieldsAndPageFields