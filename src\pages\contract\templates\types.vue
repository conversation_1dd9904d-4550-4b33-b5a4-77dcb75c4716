<template>
  <div
    class="webkit-scrollbar"
    :style="{
      height: `calc(100vh - ${minusHeight})`,
      overflow: 'hidden',
      overflowY: 'auto'
    }"
  >
    <el-tree
      :expand-on-click-node="false"
      :data="nodes"
      node-key="id"
      current-node-key="all"
      default-expand-all
      highlight-current
      @node-click="nodeClick"
      style="width: 190px; margin: 0 auto"
    >
      <div
        class="custom-tree-node text-ellipsis"
        :class="{ disabled: !data.enable }"
        :title="node.label"
        slot-scope="{ node, data }"
      >
        {{ data.label }}
      </div>
    </el-tree>
  </div>
</template>
<script>
import makeContractClient from '../../../services/contract/makeClient'
import handleError from '../../../helpers/handleError'
import { makeType2TreeNodes } from '../../../formatters/contract/type/makeType2TreeNodes'

const client = makeContractClient()
export default {
  props: {
    minusHeight: {
      type: String,
      default() {
        return '86px'
      }
    }
  },
  async created() {
    const [err, r] = await client.contractTypeGetTypeTree({
      body: {
        withDisable: true
      }
    })
    if (err) {
      handleError(err)
      return
    }

    // r.data = [
    //   {
    //     id: 3,
    //     name: '临时用',
    //     nodeType: null,
    //     nodes: [
    //       {
    //         id: 2,
    //         name: '第二个类型',
    //         nodeType: null,
    //         enable: false,
    //         nodes: null
    //       }
    //     ]
    //   },
    //   {
    //     id: 1,
    //     name: '自定义组1',
    //     nodeType: null,
    //     nodes: []
    //   },
    //   {
    //     id: 0,
    //     name: '其他',
    //     nodeType: null,
    //     nodes: [
    //       {
    //         id: 0,
    //         name: '分类1',
    //         nodeType: 'string',
    //         nodes: []
    //       }
    //     ]
    //   }
    // ]

    this.nodes = makeType2TreeNodes(r.data)
    console.log('nodes', this.nodes)
  },
  data() {
    return {
      nodes: []
    }
  },

  methods: {
    nodeClick(node) {
      console.log('node', node)
      if (node.id === 'all') {
        this.$emit('selectAll')
      }
      if (node.type === 'group') {
        this.$emit('selectGroup', node.id, this.nodes)
      }
      if (node.type === 'type') {
        this.$emit('selectType', node.id)
      }
    }
  }
}
</script>
<style scoped>
.custom-tree-node {
  widows: 190px;
  display: inline-block;
  padding: 0;
  height: 36px;
  line-height: 36px;
}
.disabled {
  font-weight: 400;
  font-size: 14px;
  color: #cccccc;
}
::v-deep .el-tree-node__content {
  overflow: none !important;
  height: 36px;
}
</style>