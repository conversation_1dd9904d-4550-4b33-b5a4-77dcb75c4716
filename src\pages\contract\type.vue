<template>
  <RightLayout>
    <TopBar>
      <Breadcrumb title="查看合同类型">
        <template #rightButton>
          <el-button
            v-if="hadPrivilege('contract2.contractSet.typeManagement.manage')"
            @click="$router.push(`/types/${$route.params.id}/edit`)"
            type="text"
          >
            <i class="el-icon-edit-outline" />编辑
          </el-button>
        </template>
      </Breadcrumb>
    </TopBar>
    <MiddleBox>
      <div style="margin: 40px auto; width: 850px">
        <Title style="margin: 58px 0 24px 0; position: relative">
          <span
            :style="{
              marginLeft: '4px',
              fontSize: '14px',
              color: '#24262A',
              fontWeight: '600'
            }"
          >
            基本信息
          </span>
        </Title>
        <el-row :gutter="20">
          <el-col :span="24">
            <span class="label">合同类型名称</span>
            <span class="value">{{ type.name }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <span class="label">更新人</span>
            <span class="value">{{ type.updater && type.updater.name }}</span>
          </el-col>
          <el-col :span="12">
            <span class="label">所在分组</span>
            <span class="value">{{ group[type.groupId] }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <span class="label">更新时间 </span>
            <span class="value">
              {{ type.updateTime | formatDateTime('yyyy-MM-dd HH:mm') }}
            </span>
          </el-col>
          <el-col :span="12">
            <span class="label">使用说明</span>
            <span class="value">{{ type.remark }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <span class="label">即将到期设置 </span>
            <span class="value">
              到期前{{ type.closingSoonDays }}天，进行即将到期提示
            </span>
          </el-col>
        </el-row>
        <Title style="margin: 58px 0 24px 0; position: relative">
          <span
            :style="{
              marginLeft: '4px',
              fontSize: '14px',
              color: '#24262A',
              fontWeight: '600'
            }"
          >
            默认规则配置
          </span>
        </Title>
        <el-row :gutter="20">
          <el-col :span="24">
            <span class="label">合同编号规则</span>
            <span class="value">{{
              (type.enableNoRule && noRule.name) || '未设置'
            }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <span class="label">关联审核流程</span>
            <span class="value">{{
              (type.enableApprove && approval.name) || '未设置'
            }}</span>
          </el-col>
          <el-col :span="12">
            <span class="label">签署截止日期</span>
            <span class="value">{{
              type.signDeadlineWay === contractSignDeadLineWayInitiateFixedDays
                ? `发起合同签署${type.signDeadlineValue}天后`
                : signDeadlineWay[type.signDeadlineWay]
            }}</span>
          </el-col>
        </el-row>
      </div>
    </MiddleBox>
  </RightLayout>
</template>
<script>
import Breadcrumb from '../../components/contract/breadcrumb.vue'
import Title from '../../components/contract/title.vue'
import RightLayout from '../../components/contract/rightLayout.vue'
import TopBar from '../../components/contract/topBar.vue'
import MiddleBox from '../../components/contract/middleBox.vue'
import { contractSignDeadLineWayInitiateFixedDays } from '../../services/contract/constants'
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import { hadPrivilege } from '../../helpers/profile'
const client = makeContractClient()
export default {
  components: {
    Title,
    Breadcrumb,
    RightLayout,
    TopBar,
    MiddleBox
  },
  async created() {
    if (this.$route.params && this.$route.params.id) {
      const id = this.$route.params.id
      const [err, r] = await client.contractTypeQueryById({
        body: {
          id: id
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.type = r.data
    }
    // 分组接口
    const [groupErr, group] = await client.contractTypeGroupGetTypeGroupDict({
      body: {}
    })
    if (groupErr) {
      handleError(groupErr)
      return
    }
    this.group = group.data
    // 编号接口
    const [noRuleErr, noRule] = await client.contractNoRuleQuery({
      body: {
        filters: {},
        start: 0,
        limit: 1000000,
        withTotal: true,
        withDisabled: false,
        withDeleted: false
      }
    })
    if (noRuleErr) {
      handleError(noRuleErr)
      return
    }
    this.noRule = noRule.data.list.filter(
      rule => rule.id == this.type.noRuleId
    )[0]
    // // 流程接口
    const [approveErr, approveList] = await client.approveQuery({
      body: {}
    })
    if (approveErr) {
      handleError(approveErr)
      return
    }
    let approvals = []
    for (let i of approveList.data) {
      approvals = [...approvals, ...i.approves]
    }
    this.approval = approvals.filter(
      approval => approval.id == this.type.approveId
    )[0]
  },
  data() {
    return {
      approval: {},
      group: {},
      noRule: {},
      type: {
        id: 0,
        name: '',
        remark: '',
        groupId: 0,
        enableNoRule: true,
        noRuleId: '',
        noRuleName: '',
        enableApprove: false,
        approveId: '',
        signDeadlineWay: '1',
        signDeadlineValue: 15,
        enable: false
        //是否可以不传
        // sort: 0
      },
      currentSelectNumberRule: {},
      signDeadlineWay: {
        1: '发起合同时设置',
        2: '发起合同后固定天数',
        3: '不限制'
      },
      contractSignDeadLineWayInitiateFixedDays
    }
  },
  methods: {
    hadPrivilege
  }
}
</script>
<style scoped>
.label {
  color: #777c94;
  margin-right: 24px;
  font-weight: 400;
  font-size: 14px;
  letter-spacing: 0;
  text-align: right;
  line-height: 14px;
  min-width: 120px;
  text-align: right;
  display: inline-block;
}
.value {
  font-weight: 400;
  font-size: 14px;
  color: #24262a;
  letter-spacing: 0;
  line-height: 14px;
}
::v-deep .el-col {
  margin-bottom: 24px;
  display: flex;
}
</style>