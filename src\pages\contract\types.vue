<template>
  <RightLayout>
    <TopBar>
      <div
        :style="{
          display: 'flex'
        }"
      >
        <h1>合同类型管理</h1>
      </div>
    </TopBar>
    <div
      style="padding: 12px 24px 24px 24px; lex: '1 1 auto'; text-align: right"
    >
      <el-button type="primary" @click="$router.push('/types/new')">
        <i class="olading-iconfont oi-icon_add2" />
        新建合同类型
      </el-button>
      <el-button plain @click="showCreateGroupForm">新建分组</el-button>
      <el-button plain @click="dialogShown.sortGroups = true">
        分组排序
      </el-button>
    </div>
    <MiddleBox style="padding: 0px 24px 0 24px" top="250px">
      <div :key="group.groupId" v-for="group in groups">
        <Group
          :group="group"
          @rename="
            group => {
              currentEditGroup = group
              dialogShown.groupForm = true
            }
          "
          @delete="deleteGroup"
          @enableType="enableType"
          @disableType="disableType"
          @editType="editType"
          @deleteType="deleteType"
          @sortTypes="sortTypes"
        />
      </div>
    </MiddleBox>
    <DialogGroupForm
      :visible.sync="dialogShown.groupForm"
      :group="currentEditGroup"
      :existedGroups="groups"
      @submit="submitGroupForm"
    />
    <DialogSortGroups
      :visible.sync="dialogShown.sortGroups"
      :groups="needSortGroups"
      @submit="submitSortGroup"
    />
    <EnableCheck :hintObj="hintObj" :visible.sync="dialogShown.enableCheck" />
  </RightLayout>
</template>
<script>
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import Group from '../../components/contract/type/group.vue'
import DialogGroupForm from '../../components/contract/type/groupFormDialog.vue'
import DialogSortGroups from '../../components/contract/type/sortGroupsDialog.vue'
import EnableCheck from '../../components/contract/type/enableCheckDialog.vue'
import RightLayout from '../../components/contract/rightLayout.vue'
import TopBar from '../../components/contract/topBar.vue'
import MiddleBox from '../../components/contract/middleBox.vue'
import makeContractClient from '../../services/contract/makeClient'
const client = makeContractClient()
export default {
  components: {
    Group,
    DialogGroupForm,
    DialogSortGroups,
    TopBar,
    MiddleBox,
    RightLayout,
    EnableCheck
  },
  async created() {
    const loading = this.$loading({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255,255, 0.7)'
    })
    await this.reload()
    loading.close()
  },
  data() {
    return {
      groups: [],
      dialogShown: {
        groupForm: false,
        sortGroups: false,
        enableCheck: false
      },
      currentEditGroup: null,
      groupForm: {
        name: ''
      },
      hintObj: {}
    }
  },
  computed: {
    needSortGroups() {
      var r = []
      for (var c of this.groups) {
        if (c.groupName !== '其他') {
          r.push(c)
        }
      }

      return r
    }
  },
  methods: {
    showCreateGroupForm() {
      this.currentEditGroup = null
      this.dialogShown.groupForm = true
    },
    async reload() {
      const [err, r] = await client.contractTypeQuery({
        body: {}
      })
      if (err) {
        handleError(err)
        return
      }

      for (var c of r.data) {
        if (c.groupName === '其他') {
          c.sort = 9999
        }
      }
      r.data.sort((a, b) => (a.sort < b.sort ? -1 : 1))

      this.groups = r.data
    },
    async enableType(type) {
      const [err, _] = await client.contractTypeEnable({
        body: {
          id: type.id
        }
      })
      if (err && err.errorCode === 501) {
        const message = err.message.split('\n')
        this.hintObj.message = message
        this.hintObj.name = type.name
        this.hintObj.id = type.id
        this.dialogShown.enableCheck = true
        return
      }
      if (err) {
        handleError(err)
        return
      }
      handleSuccess('合同类型启用成功')
      this.reload()
    },
    async disableType(type) {
      const [err, _] = await client.contractTypeDisableCheck({
        body: {
          id: type.id
        }
      })
      if (err && err.errorCode === 501) {
        this.$msgbox({
          title: '停用提示',
          message:
            '<b>存在模板使用该合同类型，无法停用</b>' +
            '<br/>已有使用中的模板使用该合同类型，请先停用模板或修改模板的合同类型。模板调整后，可以停用该合同类型。',
          confirmButtonText: '我知道了',
          dangerouslyUseHTMLString: true,
          type: 'warning',
          closeOnClickModal: false
        })
        return
      }
      if (err) {
        handleError(err)
        return
      }

      const _this = this
      this.$confirm(
        `<b>确认要停用【${type.name}】吗？</b><br/>` +
          '停用后，将无法再发起该合同类型的合同，已发起合同不受影响。',
        '停用',
        {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          closeOnClickModal: false
        }
      ).then(async () => {
        const [err, _] = await client.contractTypeDisable({
          body: {
            id: type.id
          }
        })
        if (err) {
          handleError(err)
          return
        }
        handleSuccess('合同类型停用成功')
        setTimeout(() => _this.reload(), 0)
      })
    },
    editType(row) {
      this.$router.push(`/types/${row.id}/edit`)
    },
    async deleteType(type) {
      const [err, _] = await client.contractTypeRemoveCheck({
        body: { id: type.id }
      })
      //检查不通过
      if (err && err.errorCode === 501) {
        this.$msgbox({
          title: '删除提示',
          message:
            '<b>该合同类型已有模板或合同使用，无法删除</b>' +
            '<br/>如后续不想使用，您可以停用此合同类型。',
          confirmButtonText: '我知道了',
          dangerouslyUseHTMLString: true,
          type: 'warning',
          closeOnClickModal: false
        })
        return
      }

      const _this = this
      this.$confirm(
        `<b>确认要删除【${type.name}】吗？</b>` +
          '<br/>删除后，将无法再发起该合同类型的合同，已发起合同不受影响。',
        '删除',
        {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          closeOnClickModal: false
        }
      ).then(async () => {
        const [err, _] = await client.contractTypeRemove({
          body: { id: type.id }
        })
        if (err) {
          handleError(err)
          return
        }
        handleSuccess('合同类型删除成功')
        setTimeout(() => _this.reload(), 0)
      })
    },
    submitGroupForm(groupForm) {
      if (this.currentEditGroup) {
        this.renameGroup(groupForm)
        return
      }
      this.createGroup(groupForm)
    },
    async createGroup(groupForm) {
      const groupName = groupForm.name.trim()
      if (!groupName) {
        throw new Error('group name is required')
      }

      const [err, _] = await client.contractTypeGroupAdd({
        body: {
          name: groupName,
          sort: this.groups[0].sort - 1
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.dialogShown.groupForm = false

      this.reload()
    },
    async renameGroup(groupForm) {
      if (!this.currentEditGroup) {
        throw new Error('currentEditGroup is required')
      }
      const [err, _] = await client.contractTypeGroupRename({
        body: {
          id: this.currentEditGroup.groupId,
          name: groupForm.name.trim(),
          sort: this.currentEditGroup.sort
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.dialogShown.groupForm = false
      this.reload()
    },
    async deleteGroup(group) {
      const _this = this
      this.$confirm(
        `<b>确认删除分组【${group.groupName}】吗？</b>` +
          '<br/>分组内的合同流程不会被删除，将会移入到【其他】分组。',
        '删除',
        {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          closeOnClickModal: false
        }
      ).then(async () => {
        const [err, _] = await client.contractTypeGroupRemove({
          body: {
            id: group.groupId
          }
        })
        if (err) {
          handleError(err)
          return
        }
        _this.reload()
      })
    },
    async sortTypes(groupId, types) {
      var req = []
      for (var c of types) {
        req.push({
          groupId: groupId,
          id: c.id,
          sort: c.sort
        })
      }

      const [err, _] = await client.contractTypeUpdateTypeSort({
        body: req
      })
      if (err) {
        handleError(err)
        return
      }

      this.reload()
    },
    async submitSortGroup(sortedGroups) {
      const l = sortedGroups.length
      for (var i = 0; i < l; i++) {
        sortedGroups[i].sort = i + 10
      }
      const [err, r] = await client.contractTypeGroupUpdateSort({
        body: sortedGroups
      })
      if (err) {
        handleError(err)
        return
      }

      this.dialogShown.sortGroups = false
      this.reload()
    }
  }
}
</script>
<style scoped></style>