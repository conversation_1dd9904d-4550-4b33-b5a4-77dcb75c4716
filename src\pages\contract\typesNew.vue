<template>
  <RightLayout>
    <TopBar>
      <Breadcrumb :title="$route.meta.title" />
    </TopBar>
    <MiddleBox>
      <el-form
        :model="type"
        ref="typesForm"
        label-position="top"
        :rules="rules"
        :style="{
          margin: '0 auto',
          width: '580px',
          padding: '20px 0 100px 0'
        }"
      >
        <el-form-item label="合同类型名称" prop="name">
          <el-input
            v-model="type.name"
            placeholder="请输入合同类型名称"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="所在分组" prop="groupId">
          <GroupSelect v-model="type.groupId" />
        </el-form-item>
        <el-form-item label="使用说明" prop="remark">
          <el-input
            type="textarea"
            v-model="type.remark"
            maxlength="100"
            rows="6"
            show-word-limit
            placeholder="请输入使用说明"
          />
        </el-form-item>
        <Title title="默认规则配置" />
        <div style="margin: 25px 8px 8px 0">
          合同编号规则
          <span
            v-if="type.enableNoRule"
            :style="{
              color: 'red',
              marginRight: '8px'
            }"
            >*</span
          >
          <el-switch
            @change="
              newValue => {
                store.set('__numberRuleEnabled', newValue)
                validForm()
              }
            "
            v-model="type.enableNoRule"
          >
          </el-switch>
        </div>
        <el-form-item prop="noRuleId">
          <RuleSelect
            ref="ruleSelectRef"
            v-if="isRenderChild"
            :disabled="!type.enableNoRule"
            :value="{
              name: type.noRuleName,
              id: type.noRuleId
            }"
            @input="selectNumberRule"
            @numberRuleMessage="message => (numberRuleMessage = message)"
          />
        </el-form-item>
        <span
          :style="{
            margin: '0',
            color: '#777C94',
            fontSize: '12px'
          }"
          v-if="
            currentSelectNumberRule &&
            currentSelectNumberRule.rules &&
            currentSelectNumberRule.rules.length &&
            type.enableNoRule &&
            hadPrivilege('contract2.contractSet.numberManagement.manage')
          "
        >
          预览示例：{{ makeRulesPreviews(currentSelectNumberRule.rules) }}，
          <span
            :style="{ color: '#4F71FF', cursor: 'pointer', fontSize: '12px' }"
            @click="jumpToNumberPage"
          >
            设置编号规则
          </span>
        </span>

        <div style="margin: 16px 8px 8px 0">
          关联审核流程
          <span
            v-if="type.enableApprove"
            :style="{
              color: 'red',
              marginRight: '8px'
            }"
            >*</span
          >
          <el-switch
            @change="
              newValue => {
                store.set('__approveEnabled', newValue)
                validForm()
              }
            "
            v-model="type.enableApprove"
          >
          </el-switch>
        </div>
        <el-form-item prop="approveId" :error="approveErrorMessage">
          <ApprovalSelect
            ref="approveSelectRef"
            v-if="isRenderChild"
            :disabled="!type.enableApprove"
            :value="{
              id: type.approveId
            }"
            @input="selectApproval"
            @approveErrorMessage="message => (approveErrorMessage = message)"
          />
        </el-form-item>
        <p
          :style="{
            margin: '0',
            color: '#777C94',
            fontSize: '12px',
            marginTop: '8px'
          }"
          v-if="
            type.enableApprove &&
            hadPrivilege('contract2.contractSet.flowManagement.manage')
          "
        >
          管理合同流程，
          <span
            :style="{ color: '#4F71FF', cursor: 'pointer' }"
            @click="jumpToApprovalPage"
            >点击这里</span
          >
        </p>

        <Title :withPrefix="false">
          <span>签署截止日期</span>
          <span
            :style="{
              color: 'red'
            }"
          >
            *
          </span>
          <i
            @click="dialogVisible = true"
            class="olading-iconfont oi-wenhao"
            style="color: #7f7f7f; margin-left: 5px; cursor: pointer"
          />
        </Title>
        <el-form-item prop="signDeadlineWay">
          <div style="display: flex; align-items: center; margin-top: 8px">
            <el-select v-model="type.signDeadlineWay">
              <el-option value="1" label="发起合同签署时设置"></el-option>
              <el-option value="2" label="发起合同签署后固定天数"></el-option>
              <el-option value="3" label="不限制"></el-option>
            </el-select>
            <div
              style="display: flex; align-items: center"
              v-if="
                type.signDeadlineWay ===
                contractSignDeadLineWayInitiateFixedDays
              "
            >
              <el-input-number
                style="margin: 0 8px"
                v-model="type.signDeadlineValue"
                :min="1"
                :max="1000"
                controls-position="right"
              />
              <span>天</span>
            </div>
          </div>
        </el-form-item>
        <Title style="margin-top: 20px" :withPrefix="false">
          <span>即将到期设置</span>
        </Title>
        <div style="display: flex; align-items: center; margin-top: 8px">
          到期前
          <el-input-number
            style="margin: 0 5px"
            v-model="type.closingSoonDays"
            :min="0"
            :max="1000"
            controls-position="right"
          />
          天，进行即将到期提示
        </div>
      </el-form>
      <el-dialog
        title="签署截止日期"
        :visible.sync="dialogVisible"
        width="550px"
        :close-on-click-modal="false"
      >
        <div style="width: 480px; margin: 0 auto; line-height: 1.5">
          <p
            style="background: #f7fafd; border-radius: 8px; padding: 16px 12px"
          >
            合同超过截止日期未签署完成，将无法继续签订，需要重新发起。系统会在到期前1天（系统默认）提醒参与人。
          </p>
          <div style="margin-top: 16px">
            <b>无限期：</b>合同无签署时限要求，发起后一直可签署
          </div>
          <div>
            <b>发起签约时设置：</b> 使用模板发起签约时，由发起方设置截止日期
          </div>
          <div>
            <b>发起签约后固定天数：</b> 发起后固定天数内还未签订完成，则自动过期
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="dialogVisible = false"
            >我知道了</el-button
          >
        </span>
      </el-dialog>
    </MiddleBox>
    <BottomBar>
      <el-button @click="$router.back()">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </BottomBar>
  </RightLayout>
</template>
<script>
import Breadcrumb from '../../components/contract/breadcrumb.vue'
import Title from '../../components/contract/title.vue'
import RightLayout from '../../components/contract/rightLayout.vue'
import TopBar from '../../components/contract/topBar.vue'
import MiddleBox from '../../components/contract/middleBox.vue'
import BottomBar from '../../components/contract/bottomBar.vue'
import GroupSelect from './typesNew/groupSelect.vue'
import RuleSelect from './typesNew/ruleSelect.vue'
import ApprovalSelect from './typesNew/approveSelect.vue'
import store from '../../helpers/store'
import { makeRulesPreviews } from './rules/makeRulesPreviews'
import makeContractClient from '../../services/contract/makeClient'
import handleError from '../../helpers/handleError'
import handleSuccess from '../../helpers/handleSuccess'
import { hadPrivilege } from '../../helpers/profile'
import { contractSignDeadLineWayInitiateFixedDays } from '../../services/contract/constants'
import textVue from '../../components/contract/template/filePageField/text.vue'
const client = makeContractClient()
export default {
  components: {
    Title,
    Breadcrumb,
    RightLayout,
    TopBar,
    MiddleBox,
    BottomBar,
    GroupSelect,
    RuleSelect,
    ApprovalSelect
  },
  methods: {
    async _saveType() {
      if (this.type.id > 0) {
        throw new Error('logic error, can not save a existed contract type')
      }

      const [err, _] = await client.contractTypeSave({
        body: { ...this.type, enable: true }
      })

      if (err) {
        handleError(err)
        return
      }

      handleSuccess('合同类型保存成功')
      this.$router.back()
    },
    async _updateType() {
      if (this.type.id <= 0) {
        throw new Error('contract type id is required')
      }

      const [err, _] = await client.contractTypeUpdate({
        body: this.type
      })

      if (err) {
        handleError(err)
        return
      }

      handleSuccess('合同类型更新成功')
      this.$router.back()
    },
    async save() {
      this.$refs.typesForm.validate(async valid => {
        //
        await this.$refs.approveSelectRef.search()
        await this.$refs.ruleSelectRef.search()

        if (
          (this.numberRuleMessage && this.type.enableNoRule) ||
          (this.approveErrorMessage && this.type.enableApprove)
        ) {
          return
        }

        if (valid) {
          if (this.type.id) {
            this._updateType()
            return
          }

          store.set('__numberRuleEnabled', this.type.enableNoRule)
          store.set('__approveEnabled', this.type.enableApprove)

          this._saveType()
        } else {
          this.scrollIntoError(this.$refs.typesForm)
        }
      })
    },
    selectNumberRule(selectedNumberRule) {
      this.type.noRuleName = selectedNumberRule.name
      this.type.noRuleId = selectedNumberRule.id
      this.currentSelectNumberRule = selectedNumberRule
    },
    selectApproval(selectedApproval) {
      console.log(
        selectedApproval,
        'selectedApprovalselectedApprovalselectedApproval'
      )
      this.type.approveId = selectedApproval.id
      this.$forceUpdate()
    },
    makeRulesPreviews,
    numberRuleEnabled() {
      if (this.type && this.type.id) {
        return this.type.enableNoRule
      }
      const status = store.get('__numberRuleEnabled')

      return status === true
    },
    approveEnabled() {
      if (this.type && this.type.id) {
        return this.type.enableApprove
      }

      const status = store.get('__approveEnabled')

      return status === true
    },
    jumpToApprovalPage() {
      const routers = this.$router.resolve({ path: '/approval' })
      window.open(routers.href, '_blank')
    },
    jumpToNumberPage() {
      const routers = this.$router.resolve({ path: '/rules' })
      window.open(routers.href, '_blank')
    },
    // 手动校验
    validForm() {
      this.$refs.typesForm.validate()
    },
    hadPrivilege
  },
  async created() {
    if (this.$route.params && this.$route.params.id) {
      const id = this.$route.params.id
      this.isRenderChild = false
      const [err, r] = await client.contractTypeQueryById({
        body: {
          id: id
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.type = r.data
      this.isRenderChild = true
    }
  },
  mounted() {
    if (!this.type.id) {
      this.type.enableNoRule = this.numberRuleEnabled()
      this.type.enableApprove = this.approveEnabled()
    }
  },
  computed: {},
  data() {
    return {
      store,
      type: {
        id: 0,
        name: '',
        remark: '',
        groupId: 0,
        enableNoRule: true,
        noRuleId: '',
        noRuleName: '',
        enableApprove: false,
        approveId: '',
        signDeadlineWay: '2',
        signDeadlineValue: 15,
        closingSoonDays: '',
        enable: false

        //是否可以不传
        // sort: 0
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入合同类型名称'
          }
        ],
        groupId: [
          {
            required: true,
            message: '分组不能为空'
          }
        ],
        // 合同编号规则
        noRuleId: [
          {
            validator: (rule, value, callback) => {
              if (this.type.enableNoRule && this.numberRuleMessage) {
                return callback(new Error(this.numberRuleMessage))
              }
              if (this.type.enableNoRule) {
                if (!value) return callback('请选择合同编号规则')
              }
              callback()
            }
          }
        ],
        // 关联审核流程
        approveId: [
          {
            validator: (rule, value, callback) => {
              if (this.type.enableApprove && this.approveErrorMessage) {
                return callback(this.approveErrorMessage)
              }
              if (this.type.enableApprove) {
                if (!value) return callback('请选择关联审核流程')
              }
              callback()
            }
          }
        ], //
        // 发起合同后固定天数
        signDeadlineWay: [
          {
            validator: (rule, value, callback) => {
              if (
                value === contractSignDeadLineWayInitiateFixedDays &&
                !this.type.signDeadlineValue
              ) {
                return callback('请输入发起合同后固定天数')
              }
              callback()
            }
          }
        ] //signDeadlineWay
      },
      currentSelectNumberRule: {},
      dialogVisible: false,
      approveErrorMessage: '',
      numberRuleMessage: '',
      // 获取完数据再渲染子组件
      isRenderChild: true,
      contractSignDeadLineWayInitiateFixedDays
    }
  }
}
</script>
<style scoped>
::v-deep .el-select {
  width: 100%;
}
</style>