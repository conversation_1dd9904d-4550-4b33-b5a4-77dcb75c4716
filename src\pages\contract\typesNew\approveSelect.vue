<template>
  <el-select
    filterable
    :disabled="disabled"
    placeholder="请选择"
    :loading="loading"
    :value="value.id"
    @input="select"
    style="width: 100%"
    @focus="search('')"
    @change="$emit('change')"
  >
    <el-option
      :key="approval.id"
      v-for="approval in approvals"
      :label="approval.name"
      :value="approval.id"
    ></el-option>
  </el-select>
</template>
<script>
import handleError from  '../../../helpers/handleError'
import makeContractClient from '../../../services/contract/makeClient'
const client = makeContractClient()
export default {
  props: {
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default() {
        return false
      }
    },
    value: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  async created() {
    var filters = {}
    if (this.value.name) {
      filters.name = this.value.name
    }
    await this.loadApprovals(filters)
    //如果是编辑页面启用校验，新增页面不启用校验
    if (this.$route.params.id || this.$route.query.templateId) {
      this.validApprovalSelect()
    }
  },
  data() {
    return {
      allApprovals: []
    }
  },
  methods: {
    async search(query) {
      this.loading = true
      this.$emit('approveErrorMessage', ``)
      await this.loadApprovals({ name: query })

      this.validApprovalSelect()
    },
    async loadApprovals(filters = {}) {
      const [err, r] = await client.approveQuery({
        body: {
          filters: filters,
          start: 1,
          limit: 1000000,
          withTotal: true,
          withDisabled: false,
          withDeleted: false
        }
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      // 获取到流程组后 获取流程组内启用的流程
      const approvals = []
      const allApprovals = []
      for (let group of r.data) {
        if (group.approves.length !== 0) {
          for (let approval of group.approves) {
            if (approval.enable) approvals.push(approval)
            allApprovals.push(approval)
          }
        }
      }
      // 存储所有的流程 包括未启用的
      this.allApprovals = allApprovals
      this.approvals = approvals
    },
    select(v) {
      for (var c of this.approvals) {
        if (c.id === v) {
          this.$emit('input', c)
          // 清除错误提示
          this.$emit('approveErrorMessage', ``)
          return
        }
      }
    },
    validApprovalSelect() {
      this.$nextTick(() => {
        if (this.value.id) {
          const currentApproval = this.allApprovals.filter(
            approval => approval.id == this.value.id
          )
          if (currentApproval.length > 0) {
            if (currentApproval[0].enable) {
              this.select(this.value.id)
              this.$emit('approveErrorMessage', ``)
            } else {
              // 已停用
              this.$emit(
                'approveErrorMessage',
                `${currentApproval[0].name}已停用`
              )
              this.$emit('input', {})
            }
          } else {
            // 不存在
            this.$emit(
              'approveErrorMessage',
              `原关联审核流程【id-${this.value.id}】不存在`
            )
            this.$emit('input', {})
          }
        } else {
          this.$emit('input', {})
        }
      })
    }
  },
  data() {
    return {
      loading: true,
      approvals: []
    }
  }
}
</script>