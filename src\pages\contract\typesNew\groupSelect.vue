<template>
  <el-select v-on="$listeners" v-bind="$attrs" filterable placeholder="请选择">
    <el-option
      :key="group.groupId"
      v-for="group in groups"
      :label="group.groupName"
      :value="parseInt(group.groupId, 10)"
    ></el-option>
  </el-select>
</template>
<script>
import handleError from  '../../../helpers/handleError'
import makeContractClient from '../../../services/contract/makeClient'
const client = makeContractClient()
export default {
  async created() {
    const [err, r] = await client.contractTypeQuery({
      body: {}
    })
    if (err) {
      handleError(err)
      return
    }
    this.groups = r.data
    // 新建逻辑
    if (!this.$route.params.id && this.groups.length > 0) {
      this.$emit('input', this.groups[0].groupId)
    }
  },
  data() {
    return {
      groups: []
    }
  }
}
</script>