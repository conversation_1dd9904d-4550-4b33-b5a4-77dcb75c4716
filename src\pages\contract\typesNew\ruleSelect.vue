<template>
  <el-select
    filterable
    remote
    :remote-method="search"
    :disabled="disabled"
    placeholder="请选择"
    :loading="loading"
    :value="value.id"
    @input="select"
    style="width: 100%"
    @focus="search('')"
  >
    <el-option
      :key="numberRule.id"
      v-for="numberRule in numberRules"
      :label="numberRule.name"
      :value="numberRule.id"
    ></el-option>
  </el-select>
</template>
<script>
import handleError from 'kit/helpers/handleError'
import makeContractClient from 'kit/services/contract/makeClient'
const client = makeContractClient()
export default {
  props: {
    disabled: {
      type: <PERSON><PERSON>an,
      default() {
        return false
      }
    },
    value: {
      type: Object,
      default() {
        return { id: 0 }
      }
      // validator(v) {
      //   if (!v.name && !v.id) {
      //     return true
      //   }
      //   if (!v.name || !v.id) {
      //     return false
      //   }

      //   return true
      // }
    }
  },
  async created() {
    var filters = {}
    if (this.value.name) {
      filters.name = this.value.name
    }
    await this.loadNumberRules()
    //对外界发出选择事件，这样外界使用到的该事件流程不会错误
    if (this.$route.params.id) {
      this.validNumberRuleSelect()
    }
  },
  methods: {
    async search(query) {
      this.$emit('numberRuleMessage', ``)
      await this.loadNumberRules({ name: query })
      await this.validNumberRuleSelect()
    },
    async loadNumberRules(filters = {}) {
      this.loading = true
      const [err, r] = await client.contractNoRuleQuery({
        body: {
          filters: filters,
          start: 0,
          limit: 1000000,
          withTotal: true,
          withDisabled: false,
          withDeleted: false
        }
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }
      // 只显示已启用的编号规则
      this.numberRules = r.data.list.filter(rule => rule.enable)
      this.allNumberRules = r.data.list
    },
    select(v) {
      for (var c of this.numberRules) {
        if (c.id === v) {
          this.$emit('numberRuleMessage', ``)
          this.$emit('input', c)
          return
        }
      }
    },
    async validNumberRuleSelect() {
      if (this.value.id) {
        const currentRule = this.allNumberRules.filter(
          rule => rule.id == this.value.id
        )
        if (currentRule && currentRule.length > 0) {
          if (currentRule[0].enable) {
            this.select(this.value.id)
            this.$emit('numberRuleMessage', ``)
          } else {
            // 已停用
            this.$emit('numberRuleMessage', `${currentRule[0].name}已停用`)
            this.$emit('input', { id: '' })
          }
        } else {
          this.$emit(
            'numberRuleMessage',
            `原合同编号规则【id-${this.value.id}】不存在`
          )
          this.$emit('input', { id: '' })
        }
      } else {
        this.$emit('input', {})
      }
    }
  },
  data() {
    return {
      loading: true,
      numberRules: [],
      allNumberRules: []
    }
  }
}
</script>
