<template>
  <Container
    ref="Container"
    :back="true"
    confirmButtonText="开始抽奖"
    @confirm="confirm"
    :title="$route.meta.title"
  >
    <Form
      ref="form"
      :model="form"
      style="padding: 10px 20px 0px"
      label-position="top"
    >
      <FormGroupTitle class="title" style="margin-bottom: 20px"
        >抽奖次数</FormGroupTitle
      >
      <el-form-item lang="抽奖次数">
        <Input
          v-model="form.lotteryNum"
          maxlength="3"
          valueType="int"
          placeholder="请输入抽奖次数"
        />
      </el-form-item>

      <WinningRateRulesTableCopy
        style="margin-bottom: 20px"
        ref="winningRateRulesTable"
        :ruleForm="form"
        :noneAwardEnabled="form.noneAwardEnabled"
        @itemChange="winningRateRulesTableOnChange"
        :show="ifShowWinningRateRulesTable1"
        v-model="form.winningRateRules"
        v-show="ifShowWinningRateRulesTable1"
      />

      <el-button type="primary" @click="handleAddClick">添加奖品组</el-button>
    </Form>

    <pre
      style="padding: 20px; font-size: 14px; line-height: 16px"
      v-html="result.data.result"
    ></pre>

    <Table :data="tableList" style="padding: 0 10px">
      <el-table-column label="序号" prop="index" width="100" />
      <el-table-column label="奖品名称" prop="name" />
    </Table>
  </Container>
</template>
<script>
import WinningRateRulesTableCopy from 'kit/pages/marketing/admin/activity/wechatActivitiesNew/activityRules/winningRateRulesTableCopy.vue'
import { activityProbabilityOptions } from 'kit/pages/marketing/admin/activity/wechatActivityOptions.js'
import Container from 'kit/components/marketing/admin/container.vue'
import FormGroupTitle from 'kit/components/marketing/admin/formGroupTitle.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import Table from 'kit/components/marketing/admin/table.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

import {
  ACTIVITY_SHARE_TYPE,
  ACTIVITY_AVERAGE_TYPE,
  ACTIVITY_PROBABILITY_TYPE
} from '../constants'
import handleError from 'kit/helpers/handleError'

const defaultGroupItem = {
  name: '奖品组1',
  logoUrl: '',
  themeColor: '#FFC45C',
  id: 0,
  itemList: [
    {
      couponsType: '1',
      couponsId: '',
      count: '',
      sendRule: {
        hours: '',
        minutes: '',
        seconds: '',
        interval: '',
        count: ''
      }
    }
  ]
}

export default {
  components: {
    FormGroupTitle,
    WinningRateRulesTableCopy,
    Container,
    Table,
    Input,
    Form,
  },
  data() {
    return {
      activityProbabilityOptions,
      componentKey: 0,
      result: {
        data: {
          result: '',
          awards: []
        }
      },
      form: {
        lotteryNum: 10,
        winningRateType: ACTIVITY_SHARE_TYPE,
        noneAwardEnabled: false,
        winningRateRules2: [],
        winningRateRules: [],
        groupList: [JSON.parse(JSON.stringify(defaultGroupItem))]
      }
    }
  },
  computed: {
    ifShowWinningRateRulesTable1() {
      return this.form.winningRateType === ACTIVITY_SHARE_TYPE
    },
    isEqualEAwardInAverage() {
      return this.form.winningRateType === ACTIVITY_AVERAGE_TYPE
    },
    ifShowWinningRateRulesTable2() {
      return this.form.winningRateType === ACTIVITY_PROBABILITY_TYPE
    },
    tableList() {
      let awards = this.result.data.awards
      let groupSize = 10
      let groups = []

      for (let i = 0; i < awards.length; i += groupSize) {
        let group = awards.slice(i, i + groupSize).map((name, index) => {
          return `${index + 1}-${name}`
        })
        groups.push({
          name: group.join('、')
        })
      }

      groups.forEach((item, index) => {
        item.index = index + 1
      })

      return groups
    }
  },
  methods: {
    formatterParams() {
      const params = JSON.parse(JSON.stringify(this.form))
      const { winningRateRules2, winningRateRules } = params
      delete params.winningRateRules
      delete params.winningRateRules2
      delete params.groupList
      delete params.winningRateType
      delete params.noneAwardEnabled
      if (this.ifShowWinningRateRulesTable1) {
        params.winningRateRules = winningRateRules
      }
      if (this.ifShowWinningRateRulesTable2) {
        params.winningRateRules = winningRateRules2
      }
      params.winningRateRules.forEach(item => {
        delete item.groupItem
      })
      params.rules = params.winningRateRules

      delete params.winningRateRules

      return JSON.stringify(params, null, 4)
    },
    winningRateRulesTableOnChange() {},
    async confirm() {
      const [err, result] = await marketingClient.activityTest({
        body: JSON.parse(this.formatterParams(this.form))
      })
      if (err) {
        handleError(err)
        return
      }
      this.result = result
    },
    handleAddClick() {
      const groupItem = JSON.parse(JSON.stringify(defaultGroupItem))
      this.form.groupList.push(groupItem)
      groupItem.id = this.form.groupList.length - 1
      groupItem.name = `奖品组${this.form.groupList.length}`
      this.componentKey++
    },
    winningRateRulesTableOnChange(data) {
      console.log(data)
      const id = data.id
      const groupItem = this.form.groupList.find(item => item.id === id)
      const { num, rate, position, name } = data
      name && this.$set(groupItem, 'name', name)
      num && this.$set(groupItem, 'num', num)
      rate && this.$set(groupItem, 'rate', rate)
      position && this.$set(groupItem, 'position', position)
    }
  }
}
</script>