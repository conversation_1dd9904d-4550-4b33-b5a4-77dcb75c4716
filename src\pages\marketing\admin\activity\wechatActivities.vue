<template>
  <o-pc-list
    ref="pc-list"
    :title="$route.meta.title"
    :formJson="searchFormJson"
    :requestFn="getListApi"
    labelWidth="60px"
    :deleteNullApiParams="true"
    :actionButtons="actionButtons"
    :tableHeaderActionButtons="tableHeaderActionButtons"
    :tableHeader="tableHeader"
    :beforeSearch="beforeSearch"
  />
</template>
<script>
import {
  activityStatusOptions,
  activityTypeOptions
} from './wechatActivityOptions'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { showMessage } from '../../../../helpers/showMessage'
import formatDateTime from 'kit/formatters/dateTime'
import handleError from 'kit/helpers/handleError'
import { STATUS_ACTIVE, STATUS_PENDING } from '../constants'
import { oConfirm } from 'kit/components/marketing/admin/messageBox'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.activityQueryActivity({
    body: params
  })
  if (err) return handleError(err)
  return result.data
}

export default {
  data() {
    return {
      getListApi: loadList,
      searchFormJson: [
        {
          type: 'input',
          item: {
            prop: 'name',
            label: '活动名称',
            placeholder: '请输入活动名称'
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            prop: '123',
            rangeSeparator: '~',
            label: '创建时间',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd HH:mm:ss'
          }
        },
        {
          type: 'select',
          formItem: {
            prop: 'status',
            label: '活动状态',
            placeholder: '请选择活动状态',
            options: activityStatusOptions
          }
        },
        {
          type: 'select',
          formItem: {
            prop: 'getWay',
            label: '活动方式',
            placeholder: '请选择活动方式',
            options: activityTypeOptions
          }
        }
      ],
      isFirstLoad: true,
      tableHeader: [
        {
          prop: 'name',
          label: '活动名称',
          width: 150,
          fixed: true,
          click: row =>
            this.$router.push(`/activity/wechatActivity/${row.activityId}`)
        },
        {
          prop: 'createTime',
          label: '创建时间',
          type: 'DATE'
        },
        {
          prop: 'shortName',
          label: '活动时间',
          width: 180,
          formatter: row => {
            const start = formatDateTime('yyyy-MM-dd', row.availableBeginTime)
            const end = formatDateTime('yyyy-MM-dd', row.availableEndTime)
            return `${start} ~ ${end}`
          }
        },
        {
          prop: 'getWay',
          label: '活动方式',
          width: 100,
          formatter: row => {
            return getOptionsItemLabel(activityTypeOptions, row.getWay) || '-'
          }
        },
        {
          prop: 'rewardInfo',
          label: '活动商品',
          minWidth: 220
        },
        {
          prop: 'status',
          label: '状态',
          width: 80,
          formatter: row => {
            return getOptionsItemLabel(activityStatusOptions, row.status) || '-'
          }
        }
      ],
      actionButtons: [
        {
          label: '发放明细',
          click: row => {
            this.$router.push(
              `/activity/wechatActivitySentDetail/${row.activityId}`
            )
          }
        },
        {
          label: '停用',
          ifShow: row => !row.disabled && row.status === STATUS_ACTIVE,
          click: row => {
            oConfirm(
              '停用后，活动将会停止执行，无法展示在推广员的界面，请谨慎操作',
              '停用此营销活动？',
              {
                confirm: async () => {
                  const [err] = await marketingClient.activityDisableActivity({
                    body: {
                      id: row.activityId
                    }
                  })
                  if (err) return handleError(err)

                  this.tableReload()
                  showMessage('操作成功')
                }
              }
            )
          }
        },
        {
          label: '启用',
          ifShow: row => row.disabled && row.status === STATUS_ACTIVE,
          click: row => {
            oConfirm(
              '启用后，活动将会继续执行，将会展示在推广员的界面，请谨慎操作',
              '启用此营销活动？',
              {
                confirm: async () => {
                  const [err] = await marketingClient.activityEnableActivity({
                    body: {
                      id: row.activityId
                    }
                  })
                  if (err) return handleError(err)

                  this.tableReload()
                  showMessage('操作成功')
                }
              }
            )
          }
        },
        {
          label: '删除',
          ifShow: row => row.status === STATUS_PENDING,
          click: row => {
            oConfirm('删除后将无法找回此数据，请谨慎操作', '删除此营销活动？', {
              confirm: async () => {
                const [err] = await marketingClient.activityDeleteActivity({
                  body: {
                    id: row.activityId
                  }
                })
                if (err) return handleError(err)
                this.tableReload()
                showMessage('操作成功')
              }
            })
          }
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '新建活动',
          click: () => {
            this.$router.push('/activity/wechatActivitiesNew')
          }
        }
        // {
        //   align: 'left',
        //   type: 'button',
        //   label: '抽奖测试页面',
        //   click: () => {
        //     this.$router.push('/activity/probabilityTest')
        //   }
        // },
        // {
        //   align: 'left',
        //   type: 'button',
        //   label: 'testRandom测试页面',
        //   click: () => {
        //     this.$router.push('/testRandom')
        //   }
        // }
      ]
    }
  },
  computed: {
    oTable() {
      return this.$refs['pc-list']
    }
  },
  activated() {
    if (!this.isFirstLoad) this.reload()
  },
  methods: {
    // 刷新页面
    async tableReload() {
      this.oTable.reload()
    },

    // 搜索之前对参数处理
    async beforeSearch(fData) {
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')
      return fData
    }
  }
}
</script>
