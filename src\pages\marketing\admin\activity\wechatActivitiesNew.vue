<template>
  <Container
    ref="Container"
    :back="true"
    :title="$route.meta.title"
    :confirmButtonText="confirmButtonText"
    cancelButtonText="上一步"
    :hideFootButton="hideFootButton"
    :showCancelButton="showCancelButton"
    @confirm="confirm"
    @cancel="cancel"
  >
    <div class="wrap" v-loading="isLoading">
      <!-- <el-button @click="handleTestClick">一键填充（zxm测试专用）</el-button> -->
      <CheckMessageDialog
        ref="CheckMessageDialog"
        @confirm="checkConfirm"
        @edit="checkEdit"
      />
      <TopProgressBar
        style="margin: 0 auto 24px"
        id="step"
        :steps="steps"
        v-model="activeStep"
      />
      <ActivityInfo
        :disabled="disabled"
        ref="activityInfo"
        v-show="activeStep === 'activityInfo'"
      />
      <ActivityRules
        :disabled="disabled"
        ref="activityRules"
        v-show="activeStep === 'activityRules'"
        :getWay="formData.getWay"
      />
      <Completed
        ref="completed"
        v-show="activeStep === 'completed'"
        @toDetail="toDetail"
      />
    </div>
  </Container>
</template>

<script>
import {
  formatterActivityRulesFormData,
  formatterCreateActivityRequestParams,
  formatterActivityDetailResponseParams,
  formatterCheckCouponsRequestParams
} from 'kit/formatters/marketing/formatWechatActivitiesParams.js'

import CheckMessageDialog from './wechatActivitiesNew/checkMessageDialog.vue'
import TopProgressBar from 'kit/components/marketing/admin/topProgressBar.vue'
import { oConfirm } from 'kit/components/marketing/admin/messageBox.js'
import Container from 'kit/components/marketing/admin/container.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import ActivityRules from './wechatActivitiesNew/activityRules.vue'
import ActivityInfo from './wechatActivitiesNew/activityInfo.vue'
import Completed from './wechatActivitiesNew/completed.vue'
import { showMessage } from 'kit/helpers/showMessage'
import handleError from 'kit/helpers/handleError'
import { delay } from 'kit/helpers/delay'
// import { activityInfoMock } from '../mock/wechatActivitiesNewInfo.mock'

const marketingClient = makeMarketingClient()

const updateActivity = async params => {
  const [err] = await marketingClient.activityUpdateActivity({
    body: params
  })
  if (err) {
    handleError(err)
    return Promise.reject(err)
  }
}

const steps = [
  {
    label: '填写活动信息',
    value: 'activityInfo'
  },
  {
    label: '配置活动规则',
    value: 'activityRules'
  },
  {
    label: '完成',
    value: 'completed'
  }
]

export default {
  components: {
    CheckMessageDialog,
    TopProgressBar,
    ActivityRules,
    ActivityInfo,
    Completed,
    Container
  },
  data() {
    return {
      steps,
      activeStep: 'activityInfo',
      isLoading: false,
      isPublished: false,
      formData: {
        getWay: ''
      },
      createActivityId: ''
    }
  },
  computed: {
    confirmButtonText() {
      if (this.isEdit) return '修改活动'
      return this.activeStep === 'completed' ? '确认' : '下一步'
    },
    disabled() {
      return Boolean(this.activityId)
    },
    isEdit() {
      return this.activityId && this.activeStep === 'activityRules'
    },
    hideFootButton() {
      return this.activeStep === 'completed'
    },
    showCancelButton() {
      return this.activeStep === 'activityRules'
    },
    activityId() {
      return this.$route.params.id
    }
  },
  mounted() {
    window.that = this
    if (this.activityId) this.loadDetail()
  },
  methods: {
    // handleTestClick() {
    //   this.formData = formatterActivityDetailResponseParams(activityInfoMock)

    //   this.setFormData(this.formData)
    // },
    async confirm() {
      const formData = await this.$refs[this.activeStep].getFormData()
      if (!formData) return
      const index = steps.findIndex(item => item.value === this.activeStep)
      const lastStepItem = this.steps[index + 1]
      if (!lastStepItem) return
      Object.assign(this.formData, formData)
      if (this.activeStep !== 'activityRules') {
        this.activeStep = lastStepItem.value
        document.getElementById('step').scrollIntoView({
          behavior: 'smooth'
        })
        return
      }
      const params = formatterCreateActivityRequestParams(this.formData)
      if (this.isEdit) {
        params.activityId = this.activityId
        oConfirm(
          `修改后，将会影响后续活动发放，请谨慎操作`,
          '确认要修改此营销活动吗？',
          {
            type: 'warning',
            confirm: async () => {
              await updateActivity(params)
              showMessage('修改成功')
              this.$router.back()
            }
          }
        )
        return
      }

      if (this.isPublished) {
        await this.checkConfirm()
        return
      }

      const [checkErr, checkResult] =
        await marketingClient.activityCheckCoupons({
          body: formatterCheckCouponsRequestParams(params)
        })
      if (checkErr) return handleError(checkErr)
      if (!checkResult.data.result) {
        this.$refs.CheckMessageDialog.setMessageList(
          checkResult.data.messageList
        )
        this.$refs.CheckMessageDialog.open(true)
        return
      }
      await this.checkConfirm()
    },
    async checkConfirm() {
      this.isPublished = true
      const params = formatterCreateActivityRequestParams(this.formData)
      this.$refs.Container.toggleSaveButtonLoading()

      const [err, result] = await marketingClient.activitySaveActivity({
        body: params
      })

      this.$refs.Container.toggleSaveButtonLoading(false)

      if (err) return handleError(err)
      this.createActivityId = result.data.id
      this.activeStep = 'completed'
    },
    async checkEdit() {
      this.cancel()
      await this.$nextTick()
      const fieldEl = document.body.querySelector('.collectUserRadio')
      fieldEl.scrollIntoView({
        behavior: 'smooth'
      })
    },
    cancel() {
      this.activeStep = 'activityInfo'
    },
    setFormData(formData) {
      this.$refs.activityInfo.setFormData(formData)
      this.$refs.activityRules.setFormData(
        formatterActivityRulesFormData(formData)
      )
    },
    async loadDetail() {
      this.isLoading = true
      const [err, { data }] = await marketingClient.activityDetail({
        body: {
          id: this.activityId
        }
      })
      await delay(200)
      this.isLoading = false
      if (err) return handleError(err)
      this.formData = formatterActivityDetailResponseParams(data)
      await this.$nextTick()
      this.setFormData(this.formData)
    },
    toDetail() {
      this.$router.replace(`/activity/wechatActivity/${this.createActivityId}`)
    }
  }
}
</script>

<style scoped>
.wrap {
  padding: 24px 0;
}
</style>
