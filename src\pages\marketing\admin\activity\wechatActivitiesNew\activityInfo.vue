<template>
  <div style="padding: 0 24px">
    <FormGroupTitle style="margin-bottom: 16px">活动信息</FormGroupTitle>
    <Form
      ref="form"
      :model="form"
      :rules="formRules"
      label-position="top"
      :disabled="disabled"
    >
      <el-row type="flex" align="middle" style="gap: 80px">
        <el-form-item label="活动名称" prop="name" style="flex: 1">
          <Input
            v-model="form.name"
            :trim="true"
            placeholder="请输入活动名称，不超过10字"
            maxlength="10"
          />
        </el-form-item>
        <el-form-item
          label="活动开始时间"
          prop="availableBeginTime"
          style="flex: 1"
        >
          <DatePicker
            v-model="form.availableBeginTime"
            style="width: 100%"
            :picker-options="pickerOptions"
            valueFormat="yyyy-MM-dd 00:00:00"
            placeholder="请选择活动开始时间"
          />
        </el-form-item>
        <el-form-item
          label="活动结束时间"
          prop="availableEndTime"
          style="flex: 1"
        >
          <DatePicker
            v-model="form.availableEndTime"
            valueFormat="yyyy-MM-dd 23:59:59"
            :picker-options="pickerOptions"
            style="width: 100%"
            placeholder="请选择活动结束时间"
          />
        </el-form-item>
      </el-row>
      <el-form-item label="达标资格" prop="qualification">
        <Input
          maxlength="100"
          :trim="true"
          v-model="form.qualification"
          placeholder="请输入达标资格，将展示在推广员的移动端中，不超过100字"
        />
      </el-form-item>
      <el-form-item label="活动奖励" prop="rewardInfo">
        <Input
          maxlength="100"
          :trim="true"
          v-model="form.rewardInfo"
          placeholder="请输入活动奖励，将展示在推广员的移动端中，不超过100字"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <Textarea
          maxlength="1024"
          :trim="true"
          v-model="form.remark"
          placeholder="请输入备注信息，供管理区分使用，不超过1024字"
        />
      </el-form-item>

      <el-form-item label="配置推广方式" prop="promoteTypes">
        <PromotionMethodField
          v-model="form.promoteTypes"
          @input="promotionMethodFieldOnInput"
        >
          <PromoterTable
            v-if="isPromotionTable"
            v-model="form.quotaList"
            ref="PromoterTable"
            style="margin-bottom: 20px; margin-left: 24px"
          />
          <div v-else style="margin-bottom: 6px" />
        </PromotionMethodField>
      </el-form-item>

      <el-form-item label="选择活动方式" prop="getWay">
        <SelectActivityRadio v-model="form.getWay" />
      </el-form-item>

      <el-form-item
        label="用户领取信息收集内容"
        prop="collectUser"
        class="collectUserRadio"
      >
        <UserInformationCollectionRadio
          v-model="form.collectUser"
          @change="onCollectUserChange"
        />
      </el-form-item>

      <!-- 收集用户信息 复选框 -->
      <el-form-item
        style="margin-left: 146px; margin-bottom: 48px"
        prop="collectUserItemList"
        class="collectUserItemList"
      >
        <CheckboxGroup
          v-model="form.collectUserItemList"
          :options="collectUserOptions"
          v-show="form.collectUser"
        />
      </el-form-item>

      <div class="checkQualification" v-if="form.collectUser">
        <el-checkbox
          v-model="form.checkQualification"
          label="是否进行参与人员资格接口校验"
        />
        <p>
          用户进入活动时，我们将通过接口方式询问该用户是否有参与资格及次数，但这可能需要接口对接，请联系我们。
        </p>
      </div>
    </Form>
  </div>
</template>
<script>
import {
  ACTIVITY_H5_PROMOTION,
  ACTIVITY_PROMOTION_OFFLINE,
  ACTIVITY_TRIPARTITE_DOCKING
} from '../../constants'
import UserInformationCollectionRadio from './activityInfo/userInformationCollectionRadio.vue'
import FormGroupTitle from 'kit/components/marketing/admin/formGroupTitle.vue'
import CheckboxGroup from 'kit/components/marketing/admin/checkboxGroup.vue'
import PromotionMethodField from './activityInfo/promotionMethodField.vue'
import SelectActivityRadio from './activityInfo/selectActivityRadio.vue'
import DatePicker from 'kit/components/marketing/admin/datePicker.vue'
import Textarea from 'kit/components/marketing/admin/textarea.vue'
import Select from 'kit/components/marketing/admin/select.vue'
import { collectUserOptions } from '../wechatActivityOptions'
import Input from 'kit/components/marketing/admin/input.vue'
import PromoterTable from './activityInfo/promoterTable.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import getFormRules from './activityInfoFormRules'
import deepClone from 'kit/helpers/deepClone'

export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    UserInformationCollectionRadio,
    PromotionMethodField,
    SelectActivityRadio,
    FormGroupTitle,
    CheckboxGroup,
    PromoterTable,
    DatePicker,
    Textarea,
    Select,
    Form,
    Input
  },
  computed: {
    isPromotionTable() {
      return this.form.promoteTypes?.includes(ACTIVITY_PROMOTION_OFFLINE)
    }
  },
  watch: {
    isPromotionTable() {
      this.form.quotaList = [
        {
          promoterId: '',
          promoterName: '',
          amount: ''
        }
      ]
    }
  },
  data() {
    return {
      form: {
        name: '',
        availableBeginTime: '',
        availableEndTime: '',
        qualification: '',
        rewardInfo: '',
        remark: '',
        getWay: '',
        checkQualification: true,
        collectUser: true,
        promoteTypes: [
          ACTIVITY_H5_PROMOTION,
          ACTIVITY_PROMOTION_OFFLINE
          // ACTIVITY_TRIPARTITE_DOCKING
        ],
        collectUserItemList: ['NAME', 'SMS_CAPTCHA', 'ID_NO'],
        quotaList: [
          {
            promoterId: '',
            promoterName: '',
            amount: ''
          }
        ]
      },
      formRules: getFormRules(this),
      collectUserOptions,
      pickerOptions: {
        disabledDate(time) {
          // 获取今天的时间戳，通过将小时、分钟、秒钟和毫秒设置为零
          const today = new Date()
          today.setHours(0, 0, 0, 0)
          // 传入的时间戳小于今天的时间戳，禁用
          return time.getTime() < today.getTime()
        }
      }
    }
  },
  methods: {
    async promotionMethodFieldOnInput() {
      const isPromotionTable = this.isPromotionTable
      await this.$nextTick()
      if (isPromotionTable) {
        const promoterTableRef = this.$refs['PromoterTable']
        return promoterTableRef.validate()
      }
      // promoterTableRef.clearValidate()
    },
    onCollectUserChange() {
      this.$refs.form.validateField('collectUserItemList')
    },
    async getFormData() {
      let isFormError = null

      if (
        this.isPromotionTable &&
        (await this.$refs['PromoterTable'].validate())
      ) {
        isFormError = true
      }

      if (await this.validate()) {
        isFormError = true
      }

      if (isFormError) return null

      return this.form
    },
    validate() {
      return this.$refs.form.validate()
    },
    setFormValue(data) {
      return this.$refs['json-form'].setFormValue(data)
    },
    resetForm() {
      this.$refs.form.resetFields()
    },
    async setFormData(data) {
      this.form = deepClone(data)
      if (this.isPromotionTable) {
        await this.$nextTick()
        this.$refs.PromoterTable.setPromoters(data.quotaList)
      }
    }
  }
}
</script>
<style scoped>
.collectUserRadio {
  margin-bottom: 0;
}
.collectUserRadio ::v-deep .el-form-item__content {
  display: flex;
}
.collectUserRadio ::v-deep .el-radio {
  display: flex;
}
.collectUserRadio ::v-deep .el-radio-group {
  display: flex;
}
.checkQualification {
  margin-left: 146px;
  margin-top: -40px;
}
.checkQualification p {
  color: #666;
  font-size: 14px;
  padding: 10px 22px;
}
.collectUserItemList ::v-deep .el-form-item__error {
  top: 80%;
}
</style>
