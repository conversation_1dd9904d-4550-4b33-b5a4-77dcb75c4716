function checkEndTime(_rule, value, callback) {
  const beginTime = this.form.availableBeginTime
  if (!beginTime) return callback()
  if (!value) return callback()

  const startTime = new Date(beginTime)
  const endTime = new Date(value)

  if (startTime > endTime) {
    return callback(new Error('活动结束时间不可早于开始时间'))
  }
  callback()
}

function checkCollectUserItemList(_rule, value, callback) {
  if (!this.form.collectUser) return callback()
  if (!value.length) return callback(new Error('至少收集一项信息'))
  callback()
}

export default function (vm) {
  return {
    name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
    promoteTypes: [
      {
        required: true,
        message: '请配置推广方式',
        trigger: 'change',
        type: 'array'
      }
    ],
    availableBeginTime: [
      { required: true, message: '请选择活动开始时间', trigger: 'blur' }
    ],
    availableEndTime: [
      { required: true, message: '请选择活动结束时间', trigger: 'blur' },
      { validator: checkEndTime.bind(vm), trigger: 'blur' }
    ],
    qualification: [
      { required: true, message: '请输入达标资格', trigger: 'blur' }
    ],
    rewardInfo: [
      { required: true, message: '请输入活动奖励', trigger: 'blur' }
    ],
    getWay: [{ required: true, message: '请选择活动方式', trigger: 'change' }],
    collectUser: [
      { required: true, message: '请选择活动方式', trigger: 'change' }
    ],
    collectUserItemList: [
      { validator: checkCollectUserItemList.bind(vm), trigger: 'change' }
    ]
  }
}
