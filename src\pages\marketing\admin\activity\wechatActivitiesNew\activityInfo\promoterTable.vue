<template>
  <Form
    class="form"
    ref="form"
    :model="form"
    :rules="formRules"
    label-position="top"
  >
    <!-- <el-button @click="validate">校验</el-button> -->
    <Table :data="promoters" style="width: 100%; margin-bottom: 8px">
      <el-table-column label="推广人员">
        <template slot-scope="scope">
          <el-form-item
            :prop="'promoterId' + scope.$index"
            :rules="getRules('promoterId', scope.$index)"
          >
            <SelectEmployeeInput
              :name.sync="scope.row.promoterName"
              :disabled="isEmployeeDisabled(scope.row)"
              :employeeDisableIds="getEmployeeDisableIds(scope.row)"
              v-model="scope.row.promoterId"
              placeholder="请选择人员"
            />
          </el-form-item>
        </template>
      </el-table-column>

      <el-table-column prop="amount" label="额度">
        <template slot-scope="scope">
          <el-form-item
            :prop="'amount' + scope.$index"
            :rules="getRules('amount', scope.$index)"
          >
            <Input
              v-model="scope.row.amount"
              @input="onAmountInput(scope.row)"
              maxlength="12"
              :disabled="isAmountDisabled(scope.row)"
              valueType="int"
              placeholder="请输入额度"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="useCount" label="已执行" v-if="isEdit" />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <span
            class="delete"
            v-if="showDeleteButton(scope.row, scope)"
            @click="handleDeletePromoter(scope.row)"
            >删除</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
    </Table>
    <AddCouponButton style="width: 100%" @click="handleAddClick"
      >添加推广员</AddCouponButton
    >
  </Form>
</template>

<script>
import AddCouponButton from 'kit/components/marketing/admin/addCouponButton.vue'
import SelectEmployeeInput from 'kit/pages/marketing/admin/selectEmployeeInput.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import Table from 'kit/components/marketing/admin/table.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import { showMessage } from 'kit/helpers/showMessage'
import deepClone from 'kit/helpers/deepClone'

const getPromoterItem = () => {
  return {
    promoterId: null,
    promoterName: '',
    amount: '',
    id: Math.random()
  }
}

export default {
  components: {
    SelectEmployeeInput,
    AddCouponButton,
    Table,
    Input,
    Form
  },
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {},
      formRules: {},
      promoters: []
    }
  },
  created() {
    this.initPromoters()
    this.initFormRules()
    this.initFormData()
    this.$watch(() => this.promoters, this.onInput, { deep: true })
  },
  computed: {
    isEdit() {
      return this.$route.params.id
    },
    employeeDisableIds() {
      return this.promoters.map(item => item.promoterId).filter(item => item)
    }
  },
  methods: {
    clearValidate() {
      this.$refs.form.clearValidate()
    },
    getEmployeeDisableIds({ promoterId }) {
      return this.employeeDisableIds.filter(id => id !== promoterId)
    },
    showDeleteButton(row) {
      // 如果只有一条不可删除
      if (this.promoters.length === 1) return
      // 如果已经执行了 ， 不可删除
      if (row.useCount) return false
      return true
    },
    onAmountInput(row) {
      this.$set(row, 'amountChange', true)
    },
    isEmployeeDisabled(row) {
      // 如果已经执行了 ， 不可修改
      if (row.useCount) return true
      return false
    },
    isAmountDisabled(row) {
      // 不是编辑页面 放开
      if (!row.amount) return false
      if (!this.isEdit) return false
      if (row.amountChange) return false
      if (row.amount > row.useCount) return false
      if (row.amount === row.useCount) return false
      return true
    },
    initPromoters() {
      const deepValue = deepClone(this.value)
      this.promoters = deepValue
    },
    setPromoters(list) {
      const deepValue = deepClone(list)
      this.promoters = deepValue
    },
    validate() {
      return this.$refs.form.validate()
    },
    onInput(value) {
      this.$emit('input', value)
      this.initFormData()
    },
    initFormData() {
      this.form = this.promoters.reduce((acc, cur, index) => {
        acc[`amount${index}`] = cur.amount
        acc[`promoterId${index}`] = cur.promoterId || null
        return acc
      }, {})
    },
    handleDeletePromoter(promoter) {
      if (this.promoters.length === 1) {
        return showMessage('至少保留一项', 'error')
      }
      const id = promoter.id || promoter.promoterId
      this.promoters = this.promoters.filter(
        item => (item.id || item.promoterId) !== id
      )
    },
    initFormRules() {
      this.formRules = this.promoters.reduce((acc, cur, index) => {
        acc[`amount${index}`] = [
          { required: true, message: '请输入额度', trigger: 'blur' }
        ]
        acc[`promoterId${index}`] = [
          { required: true, message: '请选择人员', trigger: 'change' }
        ]
        if (this.isEdit && cur.useCount) {
          acc[`amount${index}`].push({
            validator: (rule, value, callback) => {
              if (Number(value) < cur.useCount) {
                callback(new Error('员工可发放额度不可小于已执行'))
              }
              callback()
            },
            trigger: 'blur'
          })
        }
        return acc
      }, {})
    },
    getRules(prop, index) {
      return this.formRules[`${prop}${index}`]
    },
    handleAddClick() {
      this.promoters.push(getPromoterItem())
      this.initFormRules()
    }
  }
}
</script>

<style scoped>
::v-deep.form .el-form-item {
  margin-bottom: 0;
}
::v-deep.form .el-table__row .el-table__cell {
  padding: 7px 0;
}
::v-deep.form .el-form-item__error {
  position: inherit;
}
.delete {
  color: #f77234ff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  cursor: pointer;
}
</style>
