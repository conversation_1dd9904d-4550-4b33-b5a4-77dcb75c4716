<template>
  <div class="promotionMethodField">
    <el-checkbox
      label="H5推广"
      :value="value.includes(ACTIVITY_H5_PROMOTION)"
      @input="onInput($event, ACTIVITY_H5_PROMOTION)"
    />
    <div class="description">
      您可以将活动H5链接复制到您的公众号菜单、自定义回复或者其他可点击链接的位置
    </div>
    <el-checkbox
      label="推广员线下推广"
      :value="value.includes(ACTIVITY_PROMOTION_OFFLINE)"
      @input="onInput($event, ACTIVITY_PROMOTION_OFFLINE)"
    />
    <slot />
    <el-checkbox
      label="嵌入自有app、网页或小程序"
      :value="value.includes(ACTIVITY_TRIPARTITE_DOCKING)"
      @input="onInput($event, ACTIVITY_TRIPARTITE_DOCKING)"
    />
    <div class="description">
      您可以将活动发布至您的自由渠道，但这可能需要接口对接，请联系我们
    </div>
  </div>
</template>

<script>
import {
  ACTIVITY_H5_PROMOTION,
  ACTIVITY_PROMOTION_OFFLINE,
  ACTIVITY_TRIPARTITE_DOCKING
} from '../../../constants'
export default {
  props: {
    value: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      ACTIVITY_H5_PROMOTION,
      ACTIVITY_PROMOTION_OFFLINE,
      ACTIVITY_TRIPARTITE_DOCKING
    }
  },
  methods: {
    onInput(check, value) {
      let result = !check
        ? [...this.value].filter(type => type !== value)
        : [...this.value, value]
      this.$emit('input', result)
    }
  }
}
</script>

<style scoped>
.promotionMethodField .description {
  color: #828b9b;
  font-family: 'PingFang SC';
  font-size: 14px;
  padding-left: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
</style>