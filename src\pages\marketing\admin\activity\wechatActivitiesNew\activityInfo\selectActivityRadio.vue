<template>
  <el-radio-group v-model="selectedValue" class="radio">
    <el-radio
      :label="option.value"
      v-for="option in options"
      :key="option.value"
    >
      <el-row type="flex" align="middle">
        <span class="label">{{ option.label }}</span>
        <el-popover
          placement="right"
          width="239"
          popper-class="selectActivityRadioPopper"
          trigger="hover"
          class=""
        >
          <div>
            <h2>
              {{ option.label }}
            </h2>
            <span class="text">{{ option.description }}</span>
            <div style="padding: 16px 12px; height: 400px; overflow: hidden">
              <img class="img" :src="option.image" alt="" />
            </div>
          </div>
          <span
            slot="reference"
            class="icon iconfont icon-remind-question-circle"
          ></span>
        </el-popover>
      </el-row>
    </el-radio>
  </el-radio-group>
</template>

<script>
import { activityTypeOptions } from '../../wechatActivityOptions'
export default {
  props: {
    value: {
      type: String,
      required: true
    },
    options: {
      type: Array,
      default: () => activityTypeOptions
    }
  },
  computed: {
    selectedValue: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  }
}
</script>
<style scoped>
.radio.el-radio-group {
  display: flex;
}
::v-deep .el-radio {
  display: flex;
}
.icon {
  color: #828b9b;
  margin-left: 5px;
}
.label {
  color: #1e2228ff;
}
.icon-remind-question-circle:hover {
  color: var(--o-primary-color);
}
h2 {
  color: #1e2228ff;
  font-size: 14px;
  font-weight: 600;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
  margin-bottom: 8px;
}
.text {
  color: #1e2228ff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
}
.img {
  width: 100%;
  box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}
</style>
