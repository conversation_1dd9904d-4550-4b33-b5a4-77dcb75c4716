<template>
  <el-radio-group :value="value" @input="onInput" @change="onCollectUserChange">
    <el-radio
      v-for="option in collectUserRadioOptions"
      :label="option.value"
      :key="option.value"
    >
      <div class="flex">
        <span> {{ option.label }}</span>
        <el-tooltip
          class="item"
          effect="dark"
          content="如不收集信息，用户扫码后会直接进入活动页面，但无法更好的收集领取人信息"
          placement="top-start"
        >
          <span
            v-if="!option.value"
            style="margin-left: 5px; color: #828b9b"
            class="icon iconfont icon-remind-question-circle"
          ></span>
        </el-tooltip>
      </div>
    </el-radio>
  </el-radio-group>
</template>
<script>
import { collectUserRadioOptions } from '../../wechatActivityOptions'

export default {
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      collectUserRadioOptions
    }
  },
  methods: {
    onCollectUserChange() {
      this.$emit('change')
    },
    onInput(value) {
      this.$emit('input', value)
    }
  }
}
</script>