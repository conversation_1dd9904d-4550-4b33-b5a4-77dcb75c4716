<template>
  <el-row type="flex">
    <Form
      :key="componentKey"
      ref="form"
      :model="form"
      :rules="formRules"
      label-position="top"
      class="activityRulesForm"
    >
      <FormGroupTitle class="title">活动banner</FormGroupTitle>
      <el-form-item
        prop="bannerImageUrl"
        :style="{ marginBottom: disabled ? '20px' : '48px' }"
      >
        <BannerUpload v-model="form.bannerImageUrl" />
      </el-form-item>

      <FormGroupTitle class="title">
        <span style="flex: 1">{{ prizeConfigTitle }}</span>
        <el-checkbox
          v-model="form.noneAwardEnabled"
          :disabled="disabled"
          v-if="ifShowHasPrize"
          >开启无奖品组</el-checkbox
        >
      </FormGroupTitle>

      <el-form-item prop="groupList">
        <CouponConfig
          ref="CouponConfig"
          :disabled="disabled"
          :getWay="getWay"
          v-model="form.groupList"
        />
      </el-form-item>

      <template v-if="ifShowProbabilityWinning">
        <FormGroupTitle class="title">配置中奖概率</FormGroupTitle>
        <el-form-item prop="winningRateType">
          <Radio
            v-model="form.winningRateType"
            :disabled="disabled"
            :options="activityProbabilityOptions"
          />
        </el-form-item>
        <el-checkbox
          style="margin-bottom: 20px"
          :disabled="disabled"
          v-if="isEqualEAwardInAverage"
          v-model="form.awardAdjustEnabled"
          label="匹配奖品为空时自动切换为其他商品"
        />
      </template>

      <template v-if="ifShowProbabilityWinning">
        <WinningRateRulesTable
          style="margin-bottom: 20px"
          ref="winningRateRulesTable"
          :ruleForm="form"
          :disabled="disabled"
          :key="winningRateRulesTableKey"
          :noneAwardEnabled="form.noneAwardEnabled"
          @itemChange="winningRateRulesTableOnChange"
          :show="ifShowWinningRateRulesTable1"
          v-model="form.winningRateRules"
          v-show="ifShowWinningRateRulesTable1"
        />

        <WinningRateRulesTable2
          style="margin-bottom: 20px"
          ref="WinningRateRulesTable2"
          :ruleForm="form"
          :key="winningRateRulesTableKey + 100"
          :disabled="disabled"
          @itemChange="winningRateRulesTableOnChange"
          :show="ifShowWinningRateRulesTable2"
          v-model="form.winningRateRules2"
          v-show="ifShowWinningRateRulesTable2"
        />
      </template>

      <FormGroupTitle class="title" v-if="ifShowConfigPrizeDetail">
        配置奖品展示详情
      </FormGroupTitle>

      <ConfigPrizeDetailTable
        style="margin-bottom: 20px"
        ref="ConfigPrizeDetailTable"
        :getWay="getWay"
        :key="getWay"
        :disabled="disabled"
        v-model="form.awardShowList"
        v-if="ifShowConfigPrizeDetail"
      />

      <FormGroupTitle class="title" v-if="showNumberBombConfig"
        >配置兑奖规则
      </FormGroupTitle>

      <el-form-item prop="scoreRule" v-if="showNumberBombConfig">
        <ConfigurePrizeRedemptionRules
          :disabled="disabled"
          v-model="form.scoreRule"
          :noneAwardEnabled="form.noneAwardEnabled"
          :prizeGroupOptions="prizeGroupOptions"
          ref="configurePrizeRedemptionRules"
        />
      </el-form-item>

      <FormGroupTitle class="title">配置领取规则</FormGroupTitle>
      <el-form-item
        prop="getLimit"
        style="margin: 0"
        :class="'getLimit-' + this.getWay"
        class="getLimit"
      >
        <LimitInput
          :disabled="disabled"
          :getWay="getWay"
          v-model.number="form.getLimit"
        />
      </el-form-item>

      <el-form-item prop="repeatGet" v-if="showRepeatGetFormItem">
        <RepeatGetCheckbox :disabled="disabled" v-model="form.repeatGet" />
      </el-form-item>

      <FormGroupTitle class="title">配置活动规则</FormGroupTitle>
      <el-form-item prop="ruleInfo">
        <Textarea
          maxlength="1024"
          :trim="true"
          :disabled="textareaDisabled"
          :rows="6"
          v-model="form.ruleInfo"
          placeholder="请输入活动规则文案，将会展示在领取页面，最多1024字"
        />
      </el-form-item>
    </Form>
    <MobilePreview :getWay="getWay" style="margin-right: 24px" />
  </el-row>
</template>

<script>
import ConfigurePrizeRedemptionRules from './activityRules/configurePrizeRedemptionRules.vue'
import FormGroupTitle from 'kit/components/marketing/admin/formGroupTitle.vue'
import {
  ACTIVITY_AVERAGE_TYPE,
  ACTIVITY_SHARE_TYPE,
  ACTIVITY_PROBABILITY_TYPE,
  BLIND_BOX,
  COUPON,
  NUMBER_BOMB,
  SCRATCH_CARD,
  BIG_WHEEL_GAME
} from '../../constants'
import WinningRateRulesTable2 from './activityRules/winningRateRulesTable2.vue'
import ConfigPrizeDetailTable from './activityRules/configPrizeDetailTable.vue'
import WinningRateRulesTable from './activityRules/winningRateRulesTable.vue'
import { activityProbabilityOptions } from '../wechatActivityOptions'
import RepeatGetCheckbox from './activityRules/repeatGetCheckbox.vue'
import Textarea from 'kit/components/marketing/admin/textarea.vue'
import MobilePreview from './activityRules/mobilePreview.vue'
import Radio from 'kit/components/marketing/admin/radio.vue'
import BannerUpload from './activityRules/bannerUpload.vue'
import CouponConfig from './activityRules/couponConfig.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import LimitInput from './activityRules/limitInput.vue'
import getFromRules from './activityRulesFormRules'
import deepClone from 'kit/helpers/deepClone'

const formData = {
  ruleInfo: '',
  bannerImageUrl: '',
  repeatGet: false,
  getLimit: '',
  winningRateType: ACTIVITY_AVERAGE_TYPE,
  noneAwardEnabled: true,
  shareAllocation: [],
  awardShowList: [],
  awardAdjustEnabled: true,
  groupList: [
    {
      name: '',
      logoUrl: '',
      themeColor: '#FFC45C',
      id: 0,
      itemList: [
        {
          couponsType: '1',
          couponsId: '',
          count: '',
          sendRule: {
            hours: '',
            minutes: '',
            seconds: '',
            interval: '',
            count: ''
          }
        }
      ]
    }
  ],
  scoreRule: [
    {
      min: '0',
      max: '',
      groupItemIndex: ''
    },
    {
      min: '',
      max: null,
      groupItemIndex: ''
    }
  ],
  winningRateRules2: [],
  winningRateRules: []
}

export default {
  props: {
    getWay: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    ConfigurePrizeRedemptionRules,
    ConfigPrizeDetailTable,
    WinningRateRulesTable,
    WinningRateRulesTable2,
    RepeatGetCheckbox,
    FormGroupTitle,
    MobilePreview,
    BannerUpload,
    CouponConfig,
    LimitInput,
    Textarea,
    Radio,
    Form,
  },
  data() {
    return {
      formRules: getFromRules(this),
      activityProbabilityOptions,
      form: deepClone(formData),
      componentKey: 0,
      winningRateRulesTableKey: 0
    }
  },
  provide() {
    return {
      disabled: this.disabled
    }
  },
  watch: {
    // 活动方式发生改变 重置中奖概率
    getWay() {
      if (this.disabled) return
      this.form.winningRateType = ACTIVITY_AVERAGE_TYPE
      this.winningRateRulesTableKey++
      this.form.groupList.forEach(item => {
        item.num = ''
        item.rate = ''
        item.position = ''
      })
    }
  },
  computed: {
    isEqualEAwardInAverage() {
      const { winningRateType } = this.form
      return (
        winningRateType === ACTIVITY_AVERAGE_TYPE ||
        winningRateType === ACTIVITY_PROBABILITY_TYPE
      )
    },
    ifShowWinningRateRulesTable1() {
      return this.form.winningRateType === ACTIVITY_SHARE_TYPE
    },
    ifShowWinningRateRulesTable2() {
      return this.form.winningRateType === ACTIVITY_PROBABILITY_TYPE
    },
    ifShowHasPrize() {
      return this.getWay !== COUPON
    },
    ifShowProbabilityWinning() {
      const getWay = this.getWay
      return (
        getWay === BIG_WHEEL_GAME ||
        getWay === SCRATCH_CARD ||
        getWay === BLIND_BOX
      )
    },
    ifShowConfigPrizeDetail() {
      return (
        this.getWay === BIG_WHEEL_GAME ||
        this.getWay === BLIND_BOX ||
        this.getWay === SCRATCH_CARD
      )
    },
    textareaDisabled() {
      return this.$route.name === 'wechatActivity'
    },
    prizeGroupOptions() {
      const options = this.form.groupList.map((item, index) => {
        return {
          label: `奖品组${index + 1}`,
          value: item.groupId || index
        }
      })

      if (this.form.noneAwardEnabled) {
        options.push({
          label: '无奖品组',
          value: -1
        })
      }
      return options
    },
    showNumberBombConfig() {
      return NUMBER_BOMB === this.getWay
    },
    prizeConfigTitle() {
      const map = {
        [COUPON]: '配置卡券',
        [BLIND_BOX]: '配置盲盒奖品',
        [SCRATCH_CARD]: '配置刮刮乐奖品',
        [NUMBER_BOMB]: '配置数字风暴奖品',
        [BIG_WHEEL_GAME]: '配置幸运大转盘奖品'
      }
      return map[this.getWay]
    },
    showRepeatGetFormItem() {
      // const isDetailPage = this.$route.name === 'wechatActivity'
      // if (isDetailPage && !this.form.repeatGet) return false
      return this.getWay === COUPON
    }
  },
  methods: {
    winningRateRulesTableOnChange(data) {
      this.$refs.CouponConfig.updateDataById(data.id, data)
    },
    async getFormData() {
      let isFormError = null

      if (
        this.ifShowConfigPrizeDetail &&
        (await this.$refs['ConfigPrizeDetailTable'].validate())
      ) {
        isFormError = true
      }

      if (
        this.ifShowWinningRateRulesTable1 &&
        (await this.$refs['winningRateRulesTable'].validate())
      ) {
        isFormError = true
      }

      if (
        this.ifShowWinningRateRulesTable2 &&
        (await this.$refs['WinningRateRulesTable2'].validate())
      ) {
        isFormError = true
      }

      if (await this.$refs['CouponConfig'].validate()) {
        isFormError = true
      }

      if (
        this.getWay === NUMBER_BOMB &&
        (await this.$refs['configurePrizeRedemptionRules'].validate())
      ) {
        isFormError = true
      }

      if (await this.validate()) {
        isFormError = true
      }

      if (isFormError) {
        return null
      }

      return this.form
    },
    async validate() {
      return await this.$refs.form.validate()
    },
    setFormData(formData) {
      this.form = deepClone(formData)
      this.componentKey++
    }
  }
}
</script>

<style scoped>
.title {
  margin-bottom: 16px;
}

.getLimit ::v-deep .el-form-item__error {
  top: 32px;
  left: 92px;
}
.getLimit-4 ::v-deep .el-form-item__error {
  left: 108px;
}
.activityRulesForm {
  flex: 1;
  padding: 24px;
}
</style>
