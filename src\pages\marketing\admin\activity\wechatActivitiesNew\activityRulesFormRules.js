export default function getFromRules(vm) {
  return {
    bannerImageUrl: [
      { required: true, message: '请上传活动banner', trigger: 'change' }
    ],
    winningRateType: [
      { required: true, message: '请配置中奖概率', trigger: 'change' }
    ],
    getLimit: [
      { required: true, message: '请输入奖品数量', trigger: 'blur' },
      {
        pattern: /^(1[0-9]|20|[1-9])$/,
        message: '最多输入20个奖品',
        trigger: 'blur'
      }
      // {
      //   validator: (_rule, value, callback) => {
      //     const { ifShowProbabilityWinning, ifShowWinningRateRulesTable1 } = vm
      //     const { winningRateRules, getLimit } = vm.form
      //     if (!ifShowProbabilityWinning) return callback()
      //     if (!ifShowWinningRateRulesTable1) return callback()

      //     let sum = winningRateRules.reduce((a, b) => {
      //       return a + parseInt(b.num)
      //     }, 0)

      //     console.log({ sum })

      //     if (sum !== getLimit) {
      //       return callback('参与人领取份数之和应该等于用户设置的奖品份数')
      //     }

      //     callback()
      //   },
      //   trigger: 'blur'
      // }
    ],
    ruleInfo: [
      { required: true, message: '请输入活动规则文案', trigger: 'blur' }
    ]
  }
}
