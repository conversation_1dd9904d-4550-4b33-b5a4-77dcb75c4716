<template>
  <div>
    <ImageUploader
      class="banner-upload"
      :disabled="disabled"
      :maxSize="10"
      @input="onInput"
      :value="value"
    />
    <p v-if="!disabled">
      请上传宽度750像素，高度424像素的jpg、jpeg或png格式的banner图片，大小不超过10MB
    </p>
    <p v-else style="margin-bottom: 20px"></p>
  </div>
</template>
<script>
import ImageUploader from './imageUploader.vue'
export default {
  components: {
    ImageUploader
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  inject: ['disabled'],
  methods: {
    onInput(value) {
      this.$emit('input', value)
      this.validate()
    },
    validate() {
      const prop = this?.$parent?.prop
      this.$parent?.elForm?.validateField(prop)
    }
  }
}
</script>
<style scoped>
.banner-upload ::v-deep .el-upload,
.banner-upload ::v-deep .preview-container {
  width: 240px;
  height: 122px;
}
p {
  opacity: 1;
  color: #828b9bff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
  padding-top: 8px;
}
</style>
