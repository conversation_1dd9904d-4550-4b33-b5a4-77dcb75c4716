<template>
  <Form
    class="form"
    ref="form"
    :model="form"
    :disabled="isDisabled"
    :rules="formRules"
    label-position="top"
  >
    <Table :data="tableData" style="width: 100%; margin-bottom: 8px">
      <el-table-column label="奖品组" prop="index" width="200px" />

      <el-table-column prop="name" label="名称">
        <template slot-scope="scope">
          <el-form-item
            :prop="'name' + scope.$index"
            :rules="getRules('name', scope.$index)"
          >
            <Input
              v-model="scope.row.name"
              maxlength="6"
              placeholder="请输入名称"
            />
          </el-form-item>
        </template>
      </el-table-column>

      <el-table-column prop="imageUrl" label="展示图片">
        <template slot-scope="scope">
          <el-form-item
            :prop="'imageUrl' + scope.$index"
            :rules="getRules('imageUrl', scope.$index)"
          >
            <PrizeShowImgUpload
              v-model="scope.row.imageUrl"
              :disabled="isDisabled"
            />
          </el-form-item>
        </template>
      </el-table-column>
    </Table>
  </Form>
</template>

<script>
import Input from 'kit/components/marketing/admin/input.vue'
import Table from 'kit/components/marketing/admin/table.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import PrizeShowImgUpload from './prizeShowImgUpload.vue'
import { BIG_WHEEL_GAME, BLIND_BOX, SCRATCH_CARD } from '../../../constants'
import deepClone from 'kit/helpers/deepClone'

const couponsURL =
  '/api/v3/download/793890a97044468398c5d4b4f3bcbf4f/coupons.png'
const laughterURL =
  '/api/v3/download/74815f9d5ba44905b43c1b0b272f6040/<EMAIL>'

const prizeNumberMap = {
  [BIG_WHEEL_GAME]: [
    {
      name: '一等奖',
      img: couponsURL
    },
    {
      name: '二等奖',
      img: couponsURL
    },
    {
      name: '三等奖',
      img: couponsURL
    },
    {
      name: '四等奖',
      img: couponsURL
    },
    {
      name: '五等奖',
      img: couponsURL
    },
    {
      name: '祝您好运',
      img: laughterURL
    }
  ],
  [BLIND_BOX]: [
    {
      name: '消费立减金',
      img: '/api/v3/download/261d840282ca4e4286072e2b79abf45e/<EMAIL>'
    },
    {
      name: '话费充值',
      img: '/api/v3/download/c41ef49bd151452680495ced5864680c/<EMAIL>'
    },
    {
      name: '视频权益',
      img: '/api/v3/download/62a324d872ec415e8ee6c187128857ec/<EMAIL>'
    }
  ],
  [SCRATCH_CARD]: [
    {
      name: '消费立减金',
      img: '/api/v3/download/261d840282ca4e4286072e2b79abf45e/<EMAIL>'
    },
    {
      name: '话费充值',
      img: '/api/v3/download/c41ef49bd151452680495ced5864680c/<EMAIL>'
    },
    {
      name: '视频权益',
      img: '/api/v3/download/62a324d872ec415e8ee6c187128857ec/<EMAIL>'
    }
  ]
}

export default {
  components: {
    PrizeShowImgUpload,
    Table,
    Input,
    Form,
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: () => []
    },
    getWay: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {},
      formRules: {},
      tableData: []
    }
  },
  computed: {
    isDisabled() {
      return this.disabled && this.$route.name === 'wechatActivity'
    }
  },
  created() {
    if (this.disabled) {
      this.tableData = deepClone(this.value)
      this.tableData.forEach((item, index) => {
        item.index = `位置${index + 1}`
      })
      this.$watch(() => this.tableData, this.onInput, { deep: true })
      return
    }
    this.initTableData()
    this.initFormRules()
    this.initFormData()
    this.$watch(() => this.tableData, this.onInput, { deep: true })
  },
  methods: {
    clearValidate() {
      this.$refs.form.clearValidate()
    },
    showDeleteButton(row) {
      // 如果只有一条不可删除
      if (this.tableData.length === 1) return
      // 如果已经执行了 ， 不可删除
      if (row.useCount) return false
      return true
    },
    initTableData() {
      const tableData = []
      for (let i = 0; i < prizeNumberMap[this.getWay].length; i++) {
        let { name, img } = prizeNumberMap[this.getWay][i]
        tableData.push({
          index: `位置${i + 1}`,
          name,
          imageUrl: img
        })
      }
      this.tableData = tableData
      this.onInput(tableData)
    },
    validate() {
      return this.$refs.form.validate()
    },
    onInput(value) {
      this.$emit('input', value)
      this.initFormData()
    },
    initFormData() {
      this.form = this.tableData.reduce((acc, cur, index) => {
        acc[`name${index}`] = cur.name
        acc[`imageUrl${index}`] = cur.imageUrl || null
        return acc
      }, {})
    },
    initFormRules() {
      this.formRules = this.tableData.reduce((acc, cur, index) => {
        acc[`name${index}`] = [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
        acc[`imageUrl${index}`] = [
          { required: true, message: '请上传展示图片', trigger: 'change' }
        ]
        if (this.isEdit && cur.useCount) {
          acc[`name${index}`].push({
            validator: (rule, value, callback) => {
              if (Number(value) < cur.useCount) {
                callback(new Error('员工可发放额度不可小于已执行'))
              }
              callback()
            },
            trigger: 'blur'
          })
        }
        return acc
      }, {})
    },
    getRules(prop, index) {
      return this.formRules[`${prop}${index}`]
    }
  }
}
</script>

<style scoped>
::v-deep.form .el-form-item {
  margin-bottom: 0;
}
::v-deep.form .el-table__row .el-table__cell {
  padding: 7px 0;
}
::v-deep.form .el-form-item__error {
  position: inherit;
}
</style>
