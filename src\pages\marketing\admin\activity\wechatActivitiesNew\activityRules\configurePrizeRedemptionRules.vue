<template>
  <div>
    <Form
      ref="form"
      :model="groupForm"
      :rules="groupFormRules"
      size="small"
      :disabled="disabled"
    >
      <el-row
        v-for="(item, index) in formList"
        :key="item.id"
        type="flex"
        align="middle"
      >
        <Input
          :clearable="false"
          style="width: 100px"
          placeholder="请输入"
          valueType="int"
          v-model="item.min"
          disabled
        />
        <span style="margin: 0 8px">至</span>
        <el-form-item :prop="'max-' + index">
          <Input
            :clearable="false"
            style="width: 100px"
            maxlength="8"
            placeholder="请输入"
            valueType="int"
            v-model="item.max"
            @blur="onMaxBlur(item, index)"
          />
        </el-form-item>
        <span style="margin: 0 8px">的奖品为</span>
        <el-form-item :prop="'groupItemIndex-' + index">
          <Select
            style="width: 216px"
            :options="prizeGroupOptions"
            v-model="item.groupItemIndex"
          />
        </el-form-item>
        <button class="delete" v-if="!disabled">
          <i
            class="icon iconfont icon-base-delete"
            @click="handleDeleteClick(item)"
          />
        </button>
      </el-row>
      <AddCouponButton
        v-if="!disabled"
        style="width: 518px; margin-bottom: 24px"
        @click="handleAddClick"
        >添加区间</AddCouponButton
      >
      <el-row type="flex" align="middle">
        <span style="margin-right: 8px">大于等于</span>
        <Input
          :clearable="false"
          style="width: 100px"
          v-model="defaultLastItem.min"
          disabled
        />
        <span style="margin: 0 8px">的奖品为</span>
        <el-form-item prop="defaultLastItemGroupItemIndex">
          <Select
            style="width: 216px"
            :options="prizeGroupOptions"
            v-model="defaultLastItem.groupItemIndex"
          />
        </el-form-item>
      </el-row>
    </Form>
  </div>
</template>
<script>
import AddCouponButton from 'kit/components/marketing/admin/addCouponButton.vue'
import Select from 'kit/components/marketing/admin/select.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import { showMessage } from 'kit/helpers/showMessage'
import deepClone from 'kit/helpers/deepClone'

const getFormListItem = () => {
  return {
    min: 0,
    max: '',
    groupItemIndex: '',
    id: Math.random()
  }
}

export default {
  props: {
    value: {
      type: Array,
      default: () => {
        return {
          min: '',
          max: '',
          groupItemIndex: ''
        }
      }
    },
    noneAwardEnabled: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    prizeGroupOptions: {
      type: Array,
      default: () => []
    }
  },
  components: {
    AddCouponButton,
    Select,
    Form,
    Input
  },
  data() {
    return {
      formList: [],
      defaultLastItem: {
        min: '',
        max: '',
        groupItemIndex: ''
      },
      groupForm: {},
      groupFormRules: {}
    }
  },
  created() {
    this.initData()
    this.formGroupForm()
    this.initGroupFormRules()
  },
  watch: {
    noneAwardEnabled(value) {
      if (value) return
      // 关闭无奖品组，清空下拉选项的value
      const clearValue = item => {
        if (Number(item.groupItemIndex) === -1) {
          item.groupItemIndex = ''
        }
      }
      this.formList.forEach(clearValue)
      clearValue(this.defaultLastItem)
    }
  },
  methods: {
    initData() {
      const list = deepClone(this.value)
      this.defaultLastItem = list.splice(-1)[0]
      this.formList = list

      this.$watch('formList', this.onInput, {
        deep: true
      })
      this.$watch('defaultLastItem', this.onInput, {
        deep: true
      })
    },
    initGroupFormRules() {
      this.groupFormRules = this.formList.reduce((acc, _cur, index) => {
        acc[`max-${index}`] = [
          { required: true, message: '请输入范围', trigger: 'blur' }
        ]
        acc[`groupItemIndex-${index}`] = [
          { required: true, message: '请选择奖品组', trigger: 'change' }
        ]
        return acc
      }, {})

      this.groupFormRules['defaultLastItemGroupItemIndex'] = [
        { required: true, message: '请选择奖品组', trigger: 'change' }
      ]
    },
    validate() {
      return this.$refs.form.validate()
    },
    formGroupForm() {
      this.groupForm = this.formList.reduce((acc, cur, index) => {
        acc[`max-${index}`] = cur.max
        acc[`groupItemIndex-${index}`] = cur.groupItemIndex
        return acc
      }, {})

      this.groupForm.defaultLastItemGroupItemIndex =
        this.defaultLastItem.groupItemIndex
    },
    handleAddClick() {
      const formList = this.formList
      const lastItem = formList[formList.length - 1]
      const formItem = getFormListItem()

      if (lastItem && !lastItem.max) {
        return showMessage('请填写最大值', 'error')
      }

      if (lastItem && lastItem.max === '99999999') {
        return showMessage('已达最大值，无法添加', 'error')
      }

      if (lastItem && lastItem.max < lastItem.min) {
        return
      }

      if (lastItem && lastItem.max) {
        formItem.min = Number(lastItem.max) + 1
      }

      this.defaultLastItem.min = ''

      this.formList.push(formItem)
    },
    handleDeleteClick({ id }) {
      if (this.formList.length === 1) {
        return showMessage('至少保留一项', 'error')
      }
      const deleteItem = this.formList.find(item => item.id === id)
      const deleteItemIndex = this.formList.findIndex(item => item.id === id)
      if (deleteItemIndex !== this.formList.length - 1) {
        this.formList[deleteItemIndex + 1].min = deleteItem.min
      }

      this.formList = this.formList.filter(item => item.id !== id)
      this.$nextTick(() => {
        const { max } = this.formList[this.formList.length - 1]
        this.defaultLastItem.min = Number(max) + 1
      })
    },
    onMaxBlur(item, index) {
      if (item.max && Number(item.max) < item.min) {
        return showMessage('输入最大值需要大于等于最小值', 'error')
      }
      const lastItem = this.formList[index + 1]

      if (index === this.formList.length - 1) {
        const defaultLastItem = this.defaultLastItem
        defaultLastItem.min = Number(item.max) + 1
      }

      if (!lastItem) return
      lastItem.min = Number(item.max) + 1
    },
    onInput() {
      this.$emit('input', [...this.formList, this.defaultLastItem])

      this.formGroupForm()
      this.initGroupFormRules()
    }
  }
}
</script>
<style scoped>
.el-row {
  margin-bottom: 24px;
}
.delete {
  width: 32px;
  height: 32px;
  border-radius: 100px;
  opacity: 1;
  border: 1px solid #cad0dbff;
  background: #ffffffff;
  cursor: pointer;
  display: block;
  color: #828b9bff;
  margin-left: 16px;
}

::v-deep.el-input.is-disabled .el-input__inner {
  background: #f3f5f7 !important;
}
::v-deep.el-form-item {
  margin: 0 !important;
}
</style>
