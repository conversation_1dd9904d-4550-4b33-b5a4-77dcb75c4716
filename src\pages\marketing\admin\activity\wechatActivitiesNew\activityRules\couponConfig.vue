<template>
  <div>
    <el-row
      class="group"
      v-for="(groupItem, groupItemIndex) in groupList"
      :key="groupItem.id"
      :class="{ disabled }"
    >
      <CouponAddDeleteButton
        @remove="handleRemoveGroupItemClick(groupItem)"
        :showCopyButton="false"
        v-if="showDeleteButton(groupList)"
        class="coupon-add-delete-button"
      />

      <h4>{{ groupTitle }}组{{ groupItemIndex | formattedIndex }}</h4>

      <Form
        :ref="'group-item-form-' + groupItem.id"
        :disabled="disabled"
        label-position="top"
        :model="groupItem"
        :rules="groupFormRules"
        size="small"
      >
        <el-form-item style="margin-bottom: 14px" prop="name" label="展示名称">
          <Input
            v-model="groupItem.name"
            placeholder="请输入展示名称"
            maxLength="10"
          />
        </el-form-item>
        <el-form-item
          style="margin-bottom: 14px"
          prop="themeColor"
          label="卡券颜色"
          v-if="getWay === COUPON"
        >
          <CouponColorPicker
            v-model="groupItem.themeColor"
            :colors="['#fff', '#D9C0FF', '#C0D7FE', '#FFDCDC', '#FFC45C']"
          />
        </el-form-item>
        <el-form-item class="logoUrl" prop="logoUrl" label="展示logo">
          <LogoUpload v-model="groupItem.logoUrl" />
        </el-form-item>
      </Form>

      <div
        class="box"
        v-for="(item, itemIndex) in groupItem.itemList"
        :key="item.id"
      >
        <CouponAddDeleteButton
          v-if="showDeleteButton(groupItem.itemList)"
          @remove="handleRemoveItemClick(groupItem, item)"
          :showCopyButton="false"
          class="coupon-add-delete-button"
        />
        <Form
          :ref="'item-form-' + item.id"
          :model="item"
          :rules="groupFormRules"
          :disabled="disabled"
          size="small"
        >
          <h4>{{ groupTitle }}{{ itemIndex | formattedIndex }}</h4>
          <el-row type="flex" align="middle" class="gap-10">
            <el-form-item
              prop="couponsType"
              label="权益分类"
              style="margin-right: 6px"
            >
              <el-input
                type="text"
                v-model="item.couponsTypeName"
                v-if="disabled"
              />
              <Select
                v-else
                style="max-width: 216px"
                :options="couponsTypeOptions"
                v-model="item.couponsType"
                @change="onSelectChange(item)"
              />
            </el-form-item>
            <el-form-item
              prop="couponsId"
              :label="groupTitle + '名称'"
              style="margin-right: 6px"
            >
              <el-input
                type="text"
                v-model="item.couponsName"
                v-if="disabled"
              />
              <Select
                v-else
                placeholder="请选择卡劵名称"
                style="max-width: 216px"
                :options="getCouponsNameOptions(item)"
                v-model="item.couponsId"
              />
            </el-form-item>
            <el-form-item prop="count" :label="groupTitle + '数量'">
              <Input
                valueType="int"
                v-model="item.count"
                placeholder="请输入卡券数量"
                maxLength="4"
              >
                <template slot="append">张</template>
              </Input>
            </el-form-item>
          </el-row>

          <template v-if="ifShowSendRuleLabel(item.couponsType)">
            <div style="margin-bottom: 2px">
              <span>多张卡券发放规则</span>
              <el-tooltip
                class="item"
                effect="dark"
                content="用户领取时为第一次发放时间，之后按下面的设置分期发放给用户。"
                placement="top-start"
              >
                <i class="icon iconfont icon-remind-question-circle tip" />
              </el-tooltip>
            </div>

            <el-row type="flex" align="middle" class="gap-10 interval">
              <span>每隔</span>
              <el-form-item prop="sendRule.hours">
                <Input
                  valueType="int"
                  :clearable="false"
                  :allowZero="true"
                  maxLength="2"
                  v-model="item.sendRule.hours"
                />
              </el-form-item>
              <span>时</span>
              <el-form-item prop="sendRule.minutes">
                <Input
                  valueType="int"
                  :clearable="false"
                  :allowZero="true"
                  maxLength="2"
                  v-model="item.sendRule.minutes"
                />
              </el-form-item>
              <span>分</span>
              <el-form-item prop="sendRule.seconds">
                <Input
                  valueType="int"
                  :clearable="false"
                  :allowZero="true"
                  maxLength="2"
                  v-model="item.sendRule.seconds"
                />
              </el-form-item>
              <span>秒</span>
              <span>发放</span>
              <el-form-item prop="sendRule.count">
                <Input
                  valueType="int"
                  :clearable="false"
                  v-model="item.sendRule.count"
                />
              </el-form-item>
              <span>张，直至发放完成</span>
            </el-row>
          </template>
        </Form>
      </div>
      <AddCouponButton
        style="width: 100%"
        v-if="!disabled"
        @click="handlePushItemClick(groupItem)"
        >新增{{ groupTitle }}</AddCouponButton
      >
    </el-row>

    <AddCouponButton
      v-if="!disabled"
      style="width: 100%"
      @click="handlePushGroupItemClick"
    >
      新增{{ groupTitle }}组
    </AddCouponButton>
  </div>
</template>
<script>
import CouponAddDeleteButton from 'kit/components/marketing/admin/couponAddDeleteButton.vue'
import CouponColorPicker from 'kit/components/marketing/admin/couponColorPicker.vue'
import AddCouponButton from 'kit/components/marketing/admin/addCouponButton.vue'
import Select from 'kit/components/marketing/admin/select.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import { showMessage } from 'kit/helpers/showMessage'
import getGroupFormRules from './couponConfigFormRules'
import deepClone from 'kit/helpers/deepClone'
import LogoUpload from './logoUpload.vue'

import makeMarketingClient from 'kit/services/marketing/makeClient'
import { ACTIVITY_COUPONS_TYPE, BLIND_BOX, COUPON } from '../../../constants'
const marketingClient = makeMarketingClient()

const defaultItem = {
  couponsType: '',
  couponsId: '',
  count: '',
  sendRule: {
    hours: '',
    minutes: '',
    seconds: '',
    interval: '',
    count: ''
  }
}

const defaultGroupItem = {
  name: '',
  logoUrl: '',
  themeColor: '#FFC45C',
  id: 0,
  itemList: []
}

function getDefaultItem() {
  const item = deepClone(defaultItem)
  item.id = Date.now()
  return item
}

const getPushGroupItem = () => {
  const item = deepClone(defaultGroupItem)
  item.id = Date.now()
  item.itemList.push(getDefaultItem())
  return item
}

export default {
  props: {
    value: {
      type: Array,
      default: () => []
    },
    getWay: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    CouponAddDeleteButton,
    CouponColorPicker,
    AddCouponButton,
    LogoUpload,
    Select,
    Form,
    Input
  },
  filters: {
    formattedIndex(index) {
      return index + 1
    }
  },
  data() {
    return {
      COUPON,
      couponsTypeOptions: [],
      groupList: [],
      groupFormRules: getGroupFormRules(this)
    }
  },
  computed: {
    groupTitle() {
      const map = {
        [COUPON]: '卡券',
        [BLIND_BOX]: '盲盒',
        default: '奖品'
      }
      return map[this.getWay] || map.default
    }
  },
  methods: {
    ifShowSendRuleLabel(couponsType) {
      return couponsType !== ACTIVITY_COUPONS_TYPE
    },
    onSelectChange(item) {
      item.couponsId = ''
    },
    updateDataById(id, item) {
      const groupItem = this.groupList.find(item => item.id === id)
      Object.keys(item).forEach(key => {
        this.$set(groupItem, key, item[key])
      })
    },
    showDeleteButton(list) {
      if (this.disabled) return false
      if (list.length === 1) return false
      return true
    },
    getCouponsNameOptions(it) {
      const item = this.couponsTypeOptions?.find(
        item => item.value === it.couponsType
      )
      return item?.children || []
    },
    async validate() {
      let error = null

      const validateItem = async refName => {
        const [components] = this.$refs[refName]
        const errMsg = await components.validate()
        if (errMsg) error = errMsg
      }

      for (let groupItem of this.groupList) {
        await validateItem(`group-item-form-${groupItem.id}`)
        for (let item of groupItem.itemList) {
          await validateItem(`item-form-${item.id}`)
        }
      }

      if (!error) {
        this.$emit('input', this.groupList)
      }

      return error
    },
    handlePushGroupItemClick() {
      if (this.groupList.length >= 50) {
        showMessage('最多添加50个卡券组', 'error')
        return
      }
      this.groupList.push(getPushGroupItem())
    },
    handlePushItemClick(groupItem) {
      if (groupItem.itemList.length >= 20) {
        showMessage('每组最多添加20个卡券', 'error')
        return
      }
      groupItem.itemList.push(getDefaultItem())
    },
    handleRemoveGroupItemClick({ id }) {
      if (this.groupList.length === 1) {
        return showMessage('至少保留一个卡券组', 'error')
      }
      this.groupList = this.groupList.filter(item => item.id !== id)
    },
    handleRemoveItemClick(groupItem, item) {
      if (groupItem.itemList.length === 1) {
        return showMessage('卡券组中至少保留一个卡券', 'error')
      }
      groupItem.itemList = groupItem.itemList.filter(it => it.id !== item.id)
    },
    onInput() {
      this.$emit('input', deepClone(this.groupList))
    },
    initGroupList() {
      this.groupList = deepClone(this.value)
      this.$watch('groupList', () => this.onInput(), {
        deep: true
      })
    },
    async loadOptions() {
      const [err, result] = await marketingClient.activityCouponsSelect({
        method: 'GET'
      })
      if (err) return handleError(err)
      this.couponsTypeOptions = result.data || []
      if (this.disabled) return
      defaultItem.couponsType = this.couponsTypeOptions[0].value
      this.groupList[0].itemList[0].couponsType = defaultItem.couponsType
    }
  },
  created() {
    this.initGroupList()
    this.loadOptions()
  }
}
</script>
<style scoped>
h2,
h4 {
  margin: 0;
  font-size: 14px;
}
h4 {
  padding: 24px 0 16px 0;
}
h2 {
  padding: 16px 0 24px 0;
}

.group {
  border-radius: 8px;
  opacity: 1;
  background: #f7f9fcff;
  padding: 0 24px 24px;
  margin-bottom: 24px;
  position: relative;
}
.group.disabled {
  padding-bottom: 0;
}
.box {
  padding: 0 20px 30px;
  border-radius: 8px;
  opacity: 1;
  background: #ffffffff;
  position: relative;
  margin-bottom: 24px;
}
.gap-10 {
  gap: 10px;
}
main {
  padding: 20px;
}
.interval ::v-deep.el-form-item {
  margin: 0 !important;
  flex: 1;
}
/* .interval ::v-deep.el-form-item__content {
  flex:1;
} */
.tip {
  margin-left: 5px;
  cursor: pointer;
  font-size: 14px;
  color: #828b9bff;
}
.coupon-add-delete-button {
  position: absolute;
  right: 24px;
  top: -12px;
  z-index: 1;
}
.logoUrl {
  margin-bottom: 24px !important;
  border-bottom: 1px solid #e4e7edff;
  padding-bottom: 24px;
}
::v-deep .el-input--suffix .el-input__inner {
  padding-right: 15px;
}
</style>
