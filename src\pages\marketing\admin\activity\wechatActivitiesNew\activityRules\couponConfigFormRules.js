function validateRange(value, min, max, callback) {
  const number = parseInt(value, 10)
  const result = number >= min && number <= max
  if (!result) callback(new Error(`输入${min}-${max}`))
  return callback()
}

export default function (vm) {
  return {
    name: [
      { required: true, message: '请输入展示名称，最多10个字', trigger: 'blur' }
    ],
    logoUrl: [{ required: true, message: '请上传展示logo', trigger: 'change' }],
    themeColor: [
      { required: true, message: '请选择卡券颜色', trigger: 'change' }
    ],
    couponsType: [{ required: true, message: '请选择', trigger: 'change' }],
    couponsId: [
      { required: true, message: '请选择卡券名称', trigger: 'change' }
    ],
    count: [
      { required: true, message: '请输入卡券数量', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          validateRange(value, 1, 1000, callback)
        },
        trigger: 'blur'
      }
      // {
      //   validator: (_rule, value, callback) => {
      //     const count = vm.
      //     callback("卡券发放张数需要小于等于卡券数量")
      //   },
      //   trigger: 'blur'
      // }
    ],
    'sendRule.hours': [
      { required: true, message: '请输入0-72', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          validateRange(value, 0, 72, callback)
        },
        trigger: 'blur'
      }
    ],
    'sendRule.minutes': [
      { required: true, message: '请输入0-59', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          validateRange(value, 0, 59, callback)
        },
        trigger: 'blur'
      }
    ],
    'sendRule.seconds': [
      { required: true, message: '请输入0-59', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          validateRange(value, 0, 59, callback)
        },
        trigger: 'blur'
      }
    ],
    'sendRule.count': [
      { required: true, message: '请输入张数', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          validateRange(value, 1, 999999, callback)
        },
        trigger: 'blur'
      }
    ]
  }
}
