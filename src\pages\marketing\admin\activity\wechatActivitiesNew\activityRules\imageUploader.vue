<template>
  <UploadImage
    :uploadUrl="uploadUrl"
    v-bind="$attrs"
    :uploadHeaders="uploadHeaders"
    :disabled="disabled"
    :fileList="[value]"
    @upload-success="handleUploadSuccess"
  />
</template>

<script>
import UploadImage from 'kit/components/marketing/admin/uploadImage.vue'
import { getToken } from 'kit/helpers/token'

export default {
  components: {
    UploadImage
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uploadHeaders: {
        Authorization: `Bearer ${getToken()}`
      }
    }
  },
  computed: {
    uploadUrl() {
      return `${window.env.api}/marketing/file/upload`
    }
  },
  methods: {
    handleUploadSuccess(value) {
      this.$emit('input', value)
    }
  }
}
</script>
