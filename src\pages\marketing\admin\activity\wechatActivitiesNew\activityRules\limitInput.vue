<template>
  <div v-if="text">
    <div class="flex-center" style="margin-bottom: 10px">
      <span>{{ text[0] }}</span>
      <Input
        @input="onInput"
        :value="value"
        maxlength="2"
        :disabled="disabled"
        placeholder="请输入"
        :clearable="false"
        valueType="int"
        style="width: 80px; margin: 0 10px"
      ></Input>
      <span>{{ text[1] }}</span>
    </div>
  </div>
</template>
<script>
import {
  BIG_WHEEL_GAME,
  BLIND_BOX,
  COUPON,
  NUMBER_BOMB,
  SCRATCH_CARD
} from '../../../constants'
import Input from 'kit/components/marketing/admin/input.vue'

export default {
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    getWay: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Input
  },
  computed: {
    text() {
      const map = {
        [COUPON]: ['最多每次可领', '组卡券'],
        [BLIND_BOX]: ['每次最多可抽', '组盲盒'],
        [SCRATCH_CARD]: ['每次最多可刮', '次刮刮乐'],
        [NUMBER_BOMB]: ['每次最多可输入', '次数字风暴'],
        [BIG_WHEEL_GAME]: ['最多每次可领', '组卡券'],
      }
      return map[this.getWay] || null
    }
  },
  methods: {
    onInput(value) {
      this.$emit('input', value)
    }
  }
}
</script>
<style scoped>
.flex-center {
  display: flex;
  align-items: center;
}
</style>
