<template>
  <div>
    <ImageUploader
      class="logo-upload"
      :disabled="disabled"
      :maxSize="3"
      @input="onInput"
      :value="value"
    />
    <p v-if="!disabled">
      请上传64*64像素以内，jpg、jpeg或png格式的图片，大小不超过3MB
    </p>
  </div>
</template>
<script>
import ImageUploader from './imageUploader.vue'
export default {
  components: {
    ImageUploader
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  inject: ['disabled'],
  methods: {
    onInput(value) {
      this.$emit('input', value)
      this.validate()
    },
    validate() {
      const prop = this?.$parent?.prop
      this.$parent?.elForm?.validateField(prop)
    }
  }
}
</script>
<style scoped>
.logo-upload ::v-deep .el-upload,
.logo-upload ::v-deep .preview-container {
  width: 64px;
  height: 64px;
}
p {
  opacity: 1;
  color: #828b9bff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
  padding-top: 8px;
}
</style>
