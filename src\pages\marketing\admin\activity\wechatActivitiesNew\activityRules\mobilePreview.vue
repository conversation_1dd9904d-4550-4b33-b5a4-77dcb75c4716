<template>
  <div class="mobilePreview">
    <h1>活动样式预览</h1>
    <div class="mobile-wrap">
      <div class="mobilePreviewContainer">
        <header>{{ title }}</header>
        <main class="customer-webkit-scrollbar">
          <el-image :src="previewURL" />
        </main>
      </div>
    </div>
  </div>
</template>
<script>
import mobileActivityPreview1 from 'kit/assets/images/marketing/admin/mobileActivityPreview/1.png'
import mobileActivityPreview2 from 'kit/assets/images/marketing/admin/mobileActivityPreview/2.png'
import mobileActivityPreview3 from 'kit/assets/images/marketing/admin/mobileActivityPreview/3.png'
import mobileActivityPreview4 from 'kit/assets/images/marketing/admin/mobileActivityPreview/4.png'
import mobileActivityPreview5 from 'kit/assets/images/marketing/admin/mobileActivityPreview/5.jpg'

import {
  COUPON,
  BLIND_BOX,
  SCRATCH_CARD,
  NUMBER_BOMB,
  BIG_WHEEL_GAME
} from '../../../constants'

const previewMap = {
  [COUPON]: mobileActivityPreview1,
  [BLIND_BOX]: mobileActivityPreview2,
  [SCRATCH_CARD]: mobileActivityPreview3,
  [NUMBER_BOMB]: mobileActivityPreview4,
  [BIG_WHEEL_GAME]: mobileActivityPreview5
}

export default {
  props: {
    getWay: {
      type: String,
      default: ''
    }
  },
  computed: {
    previewURL() {
      return previewMap[this.getWay]
    },
    title() {
      const map = {
        [COUPON]: '奖品领取',
        [BLIND_BOX]: '抽盲盒',
        [SCRATCH_CARD]: '刮刮乐',
        [NUMBER_BOMB]: '数字风暴'
      }
      return map[this.getWay]
    }
  }
}
</script>
<style scoped>
h1 {
  color: #1e2228ff;
  font-size: 16px;
  font-weight: 600;
  font-family: 'PingFang SC';
  line-height: 24px;
  width: 100%;
  text-align: center;
  margin: 24px 0;
}
.mobilePreview {
  width: 360px;
  height: 606px;
  border-radius: 8px;
  position: sticky;
  top: 24px;
  opacity: 1;
  background: #f7f9fcff;
  padding: 0 56px;
  box-sizing: border-box;
}
.mobile-wrap {
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 10px 30px 4px rgba(182, 185, 196, 0.2);
  padding: 14px;
}
.mobilePreviewContainer {
  border: 0.59px solid #e4e7edff;
  border-radius: 10px;
  overflow: hidden;
  height: 449px;
}
.mobilePreviewContainer main {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: scroll;
  max-height: 400px;
}
header {
  background: url('kit/assets/images/mobile-preview-header.webp') no-repeat
    center;
  background-size: 100%;
  width: 100%;
  height: 52px;
  text-align: center;
  line-height: 78px;
  font-weight: 40;
}
.customer-webkit-scrollbar::-webkit-scrollbar {
  width: 0 !important;
  height: 1px;
}
</style>
