<template>
  <div>
    <ImageUploader
      class="logo-upload"
      :disabled="disabled"
      :maxSize="3"
      @input="onInput"
      :value="value"
    />
  </div>
</template>
<script>
import ImageUploader from './imageUploader.vue'
export default {
  components: {
    ImageUploader
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    onInput(value) {
      this.$emit('input', value)
      this.$nextTick(this.validate)
    },
    validate() {
      const prop = this?.$parent?.prop
      this.$parent?.elForm?.validateField(prop)
    }
  }
}
</script>
<style scoped>
.logo-upload ::v-deep .el-upload,
.logo-upload ::v-deep .preview-container {
  width: 64px;
  height: 64px;
}
p {
  opacity: 1;
  color: #828b9bff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
  padding-top: 8px;
}
</style>
