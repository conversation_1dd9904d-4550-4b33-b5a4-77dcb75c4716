<template>
  <div class="flex-center">
    <el-checkbox
      @input="onInput"
      v-bind="$attrs"
      :value="value"
      style="margin-right: 6px"
    ></el-checkbox>
    <span>允许多次领取同一个卡券包</span>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    onInput(value) {
      this.$emit('input', value)
    }
  }
}
</script>
