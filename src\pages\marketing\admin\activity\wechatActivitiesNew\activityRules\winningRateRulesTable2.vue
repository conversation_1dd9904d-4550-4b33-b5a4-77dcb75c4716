<template>
  <Form
    class="form"
    ref="form"
    :model="form"
    :disabled="disabled"
    :rules="formRules"
    label-position="top"
  >
    <Table :data="tableData" style="width: 100%; margin-bottom: 8px">
      <el-table-column label="奖品组" prop="name" />

      <el-table-column prop="rate" label="概率">
        <template slot-scope="scope">
          <el-form-item
            v-if="show"
            :prop="'rate' + scope.$index"
            :rules="getRules('rate', scope.$index)"
          >
            <Input
              v-model="scope.row.rate"
              maxlength="4"
              valueType="decimals_2"
              @blur="emitChange(scope.row.groupItem, scope.row)"
              :allowZero="true"
              placeholder="请输入概率"
            >
              <template slot="append">%</template>
            </Input>
          </el-form-item>
        </template>
      </el-table-column>
    </Table>
    <p style="color: #999; padding-top: 10px; font-size: 14px" v-if="!disabled">
      为了保证概率，强烈建议您开启无奖品组。
    </p>
    <div style="color: red; font-size: 14px; padding-top: 10px">
      {{ errorMsg }}
    </div>
  </Form>
</template>

<script>
import Input from 'kit/components/marketing/admin/input.vue'
import Table from 'kit/components/marketing/admin/table.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import { isAmountZero } from 'kit/helpers/isAmountZero'
import deepClone from 'kit/helpers/deepClone'

const validateBaseAmount = nextCallback => {
  return function (_rules, value, callback) {
    if (isAmountZero(value)) return callback('概率不能为0%')
    if (parseFloat(value) > 100) return callback('概率最大为100%')
    if (nextCallback) return nextCallback(_rules, value, callback)
    callback()
  }
}

export default {
  components: {
    Table,
    Input,
    Form,
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    ruleForm: {
      type: Object,
      default: () => {}
    },
    show: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {},
      formRules: {},
      tableData: [],
      noPrizeGroupItem: {
        name: '无奖品组',
        rate: ''
      }
    }
  },
  created() {
    if (this.disabled) {
      this.tableData = deepClone(this.value)
      this.tableData.forEach(item => {
        item.name = item.awardGroupName
      })
      return
    }
    this.initTableData()
    this.initFormRules()
    this.initFormData()
    this.$watch(() => this.tableData, this.onInput, { deep: true })
    this.$watch(() => this.ruleForm.groupList, this.onWatchGroupList, {
      deep: true
    })
    this.$watch(
      () => this.ruleForm.noneAwardEnabled,
      () => {
        this.noPrizeGroupItem.rate = ''
        this.onWatchGroupList()
      },
      {
        deep: true
      }
    )
    this.onInput(this.tableData)
  },
  computed: {
    errorMsg() {
      const totalProbability = this.tableData.reduce((a, b) => {
        let rate = parseFloat(b.rate)
        return a + rate
      }, 0)
      if (totalProbability === 100 || isNaN(totalProbability)) return ''
      return '中奖概率之和应该为100%'
    }
  },
  methods: {
    emitChange(groupItem, row) {
      if (groupItem.name === '无奖品组') return
      this.$emit('itemChange', {
        id: groupItem.id,
        ...row
      })
    },
    onWatchGroupList() {
      this.initTableData()
      this.initFormRules()
      this.initFormData()
    },
    clearValidate() {
      this.$refs.form.clearValidate()
    },
    initTableData() {
      const tableData = []
      this.ruleForm.groupList.forEach(item => {
        delete item.groupItem
        tableData.push({
          name: item.name,
          rate: item.rate || '',
          groupItem: item
        })
      })
      if (this.ruleForm.noneAwardEnabled) {
        const noPrizeGroupItem = { ...this.noPrizeGroupItem }
        delete noPrizeGroupItem.groupItem
        this.noPrizeGroupItem.groupItem = noPrizeGroupItem
        tableData.push(this.noPrizeGroupItem)
      }
      this.tableData = tableData
    },
    validate() {
      if (this.errorMsg) {
        const fieldEl = this.$refs.form.$el
        fieldEl.scrollIntoView({
          behavior: 'smooth'
        })
        return 'error'
      }
      return this.$refs.form.validate()
    },
    onInput(value) {
      this.$emit('input', value)
      this.initFormData()
    },
    initFormData() {
      this.form = this.tableData.reduce((acc, cur, index) => {
        acc[`rate${index}`] = cur.rate
        return acc
      }, {})
    },
    initFormRules() {
      this.formRules = this.tableData.reduce((acc, cur, index) => {
        acc[`rate${index}`] = [
          { required: true, message: '请输入概率', trigger: 'blur' },
          {
            validator: validateBaseAmount()
          }
        ]
        return acc
      }, {})
    },
    getRules(prop, index) {
      return this.formRules[`${prop}${index}`]
    }
  }
}
</script>

<style scoped>
::v-deep.form .el-form-item {
  margin-bottom: 0;
}
::v-deep.form .el-table__row .el-table__cell {
  padding: 7px 0;
}
::v-deep.form .el-form-item__error {
  position: inherit;
}
</style>
