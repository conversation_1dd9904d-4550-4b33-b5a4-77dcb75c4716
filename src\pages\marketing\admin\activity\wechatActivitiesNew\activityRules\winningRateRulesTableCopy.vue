<template>
  <Form
    class="form"
    ref="form"
    :model="form"
    :disabled="disabled"
    :rules="formRules"
    label-position="top"
  >
    <Table :data="tableData" style="width: 100%; margin-bottom: 8px">
      <el-table-column label="奖品组" prop="name">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.name"
            size="small"
            maxlength="6"
            @blur="emitChange(scope.row.groupItem, scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column prop="num" label="份额">
        <template slot-scope="scope">
          <div>
            <el-form-item
              v-if="show"
              :prop="'num' + scope.$index"
              :rules="getRules('num', scope.$index)"
            >
              <Input
                v-model="scope.row.num"
                maxlength="8"
                valueType="int"
                @blur="emitChange(scope.row.groupItem, scope.row)"
                placeholder="请输入份额"
              />
            </el-form-item>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="position" label="中奖位置">
        <template slot-scope="scope">
          <el-form-item
            style="width: 100%"
            :prop="'position' + scope.$index"
            :rules="getRules('position', scope.$index)"
          >
            <Select
              style="width: 100%"
              @change="emitChange(scope.row.groupItem, scope.row)"
              :options="winPositionOptions"
              v-model="scope.row.position"
            />
          </el-form-item>
        </template>
      </el-table-column>
    </Table>
    <!-- <p style="color: #999; padding-top: 10px; font-size: 14px" v-if="!disabled">
      为了保证概率，强烈建议您开启无奖品组。
    </p> -->
  </Form>
</template>

<script>
import { winPositionOptions } from '../../wechatActivityOptions'
import { WINNING_RATE_RULES_RANDOM } from '../../../constants'
import Select from 'kit/components/marketing/admin/select.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import Table from 'kit/components/marketing/admin/table.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import deepClone from 'kit/helpers/deepClone'

export default {
  components: {
    Table,
    Input,
    Form,
    Select
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    ruleForm: {
      type: Object,
      default: () => {}
    },
    noneAwardEnabled: {
      type: Boolean,
      default: false
    },
    show: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      winPositionOptions,
      form: {},
      formRules: {},
      tableData: [],
      noPrizeGroupItem: {
        name: '无奖品组',
        position: WINNING_RATE_RULES_RANDOM,
        num: ''
      }
    }
  },
  created() {
    if (this.disabled) {
      this.tableData = deepClone(this.value)
      this.tableData.forEach(item => {
        item.name = item.awardGroupName
      })
      return
    }
    this.initTableData()
    this.initFormRules()
    this.initFormData()
    this.$watch(() => this.tableData, this.onInput, { deep: true })
    this.$watch(() => this.ruleForm.groupList, this.onWatchGroupList, {
      deep: true
    })
    this.$watch(
      () => this.noneAwardEnabled,
      () => {
        this.noPrizeGroupItem.num = ''
        this.noPrizeGroupItem.position = WINNING_RATE_RULES_RANDOM
        this.onWatchGroupList()
      },
      {
        deep: true
      }
    )
    this.onInput(this.tableData)
  },
  methods: {
    emitChange(groupItem, row) {
      if (groupItem.name === '无奖品组') return
      this.$emit('itemChange', {
        id: groupItem.id,
        ...row
      })
    },
    onWatchGroupList() {
      this.initTableData()
      this.initFormRules()
      this.initFormData()
    },
    clearValidate() {
      this.$refs.form.clearValidate()
    },
    initTableData() {
      const tableData = []
      this.ruleForm.groupList.forEach(item => {
        tableData.push({
          name: item.name,
          position: item.position || WINNING_RATE_RULES_RANDOM,
          num: item.num,
          groupItem: item
        })
      })
      if (this.noneAwardEnabled) {
        this.noPrizeGroupItem.groupItem = { ...this.noPrizeGroupItem }
        tableData.push(this.noPrizeGroupItem)
      }
      this.tableData = tableData
    },
    validate() {
      return this.$refs.form.validate()
    },
    onInput(value) {
      this.$emit('input', value)
      this.initFormData()
    },
    initFormData() {
      this.form = this.tableData.reduce((acc, cur, index) => {
        acc[`num${index}`] = cur.num
        acc[`position${index}`] = cur.position || null
        return acc
      }, {})
    },
    initFormRules() {
      this.formRules = this.tableData.reduce((acc, cur, index) => {
        acc[`num${index}`] = [
          { required: true, message: '请输入份额', trigger: 'blur' }
        ]
        acc[`position${index}`] = [
          { required: true, message: '请选择中奖位置', trigger: 'change' }
        ]
        return acc
      }, {})
    },
    getRules(prop, index) {
      return this.formRules[`${prop}${index}`]
    }
  }
}
</script>

<style scoped>
::v-deep.form .el-form-item {
  margin-bottom: 0;
}
::v-deep.form .el-table__row .el-table__cell {
  padding: 7px 0;
}
::v-deep.form .el-form-item__error {
  position: inherit;
}
</style>
