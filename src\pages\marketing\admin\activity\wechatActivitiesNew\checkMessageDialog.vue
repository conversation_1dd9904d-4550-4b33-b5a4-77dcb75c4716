<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="560px"
    title="检测到您的活动与配置的权益存在冲突，请确认配置？"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
  >
    <ul class="message-list">
      <li v-for="item in messageList" :key="item">{{ item }}</li>
    </ul>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="confirm" :loading="isLoading"
        >仍要发布</el-button
      >
      <el-button size="small" type="primary" @click="handleEditClick">
        去修改
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { delay } from 'kit/helpers/delay'
export default {
  data() {
    return {
      dialogVisible: false,
      messageList: [],
      isLoading: false
    }
  },
  methods: {
    open(show = true) {
      this.dialogVisible = show
    },
    async confirm() {
      this.isLoading = true
      this.$emit('confirm')
      this.open(false)
      await delay(300)
      this.isLoading = false
    },
    handleEditClick() {
      this.open(false)
      this.$emit('edit')
    },
    setMessageList(messageList) {
      this.messageList = messageList || []
    }
  }
}
</script>
<style scoped>
.message-list li {
  font-size: 14px;
  color: #666;
  line-height: 26px;
}
</style>