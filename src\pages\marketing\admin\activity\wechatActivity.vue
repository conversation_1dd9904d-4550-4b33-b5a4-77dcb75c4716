<template>
  <Container :back="true" :title="$route.meta.title">
    <div slot="header-right" class="title-button">
      <el-button type="primary" size="small" @click="handleEditClick"
        >修改活动</el-button
      >
    </div>

    <main
      class="wechatActivityMain"
      v-loading="isLoading"
      :class="{ loading: isLoading }"
    >
      <div v-if="info.activityId">
        <!-- 活动基本信息 -->
        <ActivityInfo class="box" :info="info" />
        <!-- 活动后方式 -->
        <ActivityPromoteTypes
          :promoteTypes="info.promoteTypes"
          :quotaList="info.quotaList"
          :qrCodeInfo="info.qrCodeInfo"
        />
        <!-- 活动规则 -->
        <ActivityRules
          :disabled="true"
          :getWay="info.getWay"
          ref="ActivityRules"
        />
      </div>
    </main>
  </Container>
</template>

<script>
import { formatterActivityDetailResponseParams } from 'kit/formatters/marketing/formatWechatActivitiesParams.js'
import ActivityPromoteTypes from './wechatActivity/activityPromoteTypes.vue'
import Container from 'kit/components/marketing/admin/container.vue'
import ActivityRules from './wechatActivitiesNew/activityRules.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import ActivityInfo from './wechatActivity/activityInfo.vue'
import handleError from 'kit/helpers/handleError'
import { delay } from 'kit/helpers/delay'

const marketingClient = makeMarketingClient()

export default {
  components: {
    ActivityPromoteTypes,
    ActivityRules,
    ActivityInfo,
    Container
  },
  data() {
    return {
      info: {
        getWay: ''
      },
      isLoading: false
    }
  },
  computed: {
    activityId() {
      return this.$route.params.activityId
    }
  },
  created() {
    this.loadDetail()
  },
  methods: {
    async handleEditClick() {
      this.$router.push(`/activity/wechatActivitiesNew/${this.activityId}`)
    },
    async loadDetail() {
      this.isLoading = true
      const [err, { data }] = await marketingClient.activityDetail({
        body: {
          id: this.activityId
        }
      })
      await delay(200)
      this.isLoading = false
      if (err) return handleError(err)
      this.info = formatterActivityDetailResponseParams(data)
      await this.$nextTick()
      this.$refs.ActivityRules.setFormData(this.info)
    }
  }
}
</script>

<style scoped>
.title-button {
  display: flex;
  justify-content: flex-end;
  flex: 1;
}
.box {
  margin-bottom: 48px;
}
.wechatActivityMain {
  padding: 24px;
}
.wechatActivityMain.loading {
  overflow: hidden;
  height: 80vh;
}
.wechatActivityMain ::v-deep .mobilePreview {
  display: none;
}
.wechatActivityMain ::v-deep .activityRulesForm {
  padding: 0;
}
</style>
