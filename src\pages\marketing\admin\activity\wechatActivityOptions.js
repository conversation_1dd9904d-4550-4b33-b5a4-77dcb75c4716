import {
  COUPON,
  B<PERSON><PERSON>D_BOX,
  SCRATCH_CARD,
  NUMBER_BOMB,
  BIG_WHEEL_GAME,
  ACTIVITY_AVERAGE_TYPE,
  ACTIVITY_SHARE_TYPE,
  ACTIVITY_PROBABILITY_TYPE,
  ACTIVITY_H5_PROMOTION,
  ACTIVITY_TRIPARTITE_DOCKING,
  ACTIVITY_PROMOTION_OFFLINE,
  WINNING_RATE_RULES_RANDOM,
  WINNING_RATE_RULES_FRONT,
  WINNING_RATE_RULES_MIDDLE,
  WINNING_RATE_RULES_BACK
} from '../constants'
import tooltip1 from 'kit/assets/images/marketing/admin/tooltip/<EMAIL>'
import tooltip2 from 'kit/assets/images/marketing/admin/tooltip/<EMAIL>'
import tooltip3 from 'kit/assets/images/marketing/admin/tooltip/<EMAIL>'
import tooltip4 from 'kit/assets/images/marketing/admin/tooltip/<EMAIL>'
import tooltip5 from 'kit/assets/images/marketing/admin/tooltip/<EMAIL>'

export const activityTypeOptions = [
  {
    label: '直接领券',
    value: COUPON,
    description: '平铺展示多组奖品包，用户自由领取',
    image: tooltip1
  },
  {
    label: '抽盲盒',
    value: BLIND_BOX,
    description: '用户选择盲盒后获取奖品',
    image: tooltip2
  },
  {
    label: '刮刮乐',
    value: SCRATCH_CARD,
    description: '用户选择刮刮乐后，获取奖品',
    image: tooltip3
  },
  {
    label: '数字风暴',
    value: NUMBER_BOMB,
    description: '用户输入数字后，根据数字所在范围，获取奖品',
    image: tooltip4
  },
  {
    label: '大转盘游戏',
    value: BIG_WHEEL_GAME,
    description: '用户点击转动转盘后获得奖品',
    image: tooltip5
  }
]

export const activityStatusOptions = [
  { label: '待开始', value: '1' },
  { label: '投放中', value: '2' },
  { label: '已过期', value: '3' }
]

export const activitySenDetailStatusOptions = [
  { label: '待发放', value: '1' },
  { label: '发放成功', value: '2' },
  { label: '发放失败', value: '3' }
]

export const collectUserOptions = [
  { label: '姓名', value: 'NAME' },
  { label: '手机号', value: 'MOBILE' },
  { label: '手机号和验证码', value: 'SMS_CAPTCHA' },
  { label: '身份证号', value: 'ID_NO' },
  { label: '银行卡号', value: 'BANK_CARD_NO' }
]

export const collectUserRadioOptions = [
  { label: '不收集信息', value: false },
  { label: '收集信息', value: true }
]

export const activityProbabilityOptions = [
  { label: '均衡中奖', value: ACTIVITY_AVERAGE_TYPE },
  { label: '按奖品份数自动分配', value: ACTIVITY_SHARE_TYPE },
  { label: '按设定概率分配', value: ACTIVITY_PROBABILITY_TYPE }
]

export const winPositionOptions = [
  { label: '前部', value: WINNING_RATE_RULES_FRONT },
  { label: '中部', value: WINNING_RATE_RULES_MIDDLE },
  { label: '后部', value: WINNING_RATE_RULES_BACK },
  { label: '随机', value: WINNING_RATE_RULES_RANDOM }
]

export const activityPromotionConfigOptions = [
  { label: 'H5推广', value: ACTIVITY_H5_PROMOTION },
  { label: '推广员线下推广', value: ACTIVITY_PROMOTION_OFFLINE },
  { label: '嵌入自有系统', value: ACTIVITY_TRIPARTITE_DOCKING }
]
