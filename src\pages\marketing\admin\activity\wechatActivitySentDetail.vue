<template>
  <o-pc-list
    ref="pc-list"
    :title="$route.meta.title"
    :formJson="searchFormJson"
    :requestFn="getListApi"
    :titleBack="true"
    labelWidth="70px"
    :deleteNullApiParams="true"
    :tableHeaderActionButtons="tableHeaderActionButtons"
    :tableHeader="tableHeader"
    :beforeSearch="beforeSearch"
  />
</template>
<script>
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { activitySenDetailStatusOptions } from './wechatActivityOptions'
import handleError from 'kit/helpers/handleError'
import { exportExcel } from 'kit/helpers/exportExcel'
import {
  ACTIVITY_H5_PROMOTION,
  ACTIVITY_PROMOTION_OFFLINE,
  ACTIVITY_TRIPARTITE_DOCKING
} from 'kit/pages/marketing/admin/constants.js'

const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.activityQueryActivityAwardRecord({
    body: params
  })
  if (err) return handleError(err)
  return result.data
}

async function loadMemberList() {
  const [err, result] = await marketingClient.activityPromoterSelect({
    method: 'GET',
    params: `activityId=${this.$route.params.activityId}`
  })
  if (err) return handleError(err)
  const memberList = result.data.userList.map(item => {
    return {
      label: item.name,
      value: item.id
    }
  })
  return memberList
}

export default {
  data() {
    return {
      getListApi: loadList,
      searchFormJson: [
        {
          // type:"remoteSearchSelect",
          type: 'select',
          item: {
            prop: 'promoterId',
            label: '关联推广员',
            placeholder: '请选择关联推广员',
            options: loadMemberList.bind(this)
            // remoteMethod:loadMemberList
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            rangeSeparator: '~',
            prop: 'createTime',
            label: '发放时间',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd HH:mm:ss'
          }
        }
      ],
      isFirstLoad: true,
      tableHeader: [
        {
          prop: 'id',
          label: '发放唯一id',
          minWidth: 90,
          fixed: true
        },
        {
          prop: 'sendTime',
          label: '发放时间',
          type: 'DATE_TIME'
        },
        {
          prop: 'shortName',
          label: '发放渠道',
          minWidth: 120,
          formatter: row => {
            const nameMap = {
              ['3']: 'H5推广',
              ['1']: '推广员线下推广',
              ['2']: '嵌入自有app、网页或小程序',
              default: '-'
            }
            return nameMap[row.channel] || nameMap.default
          }
        },
        {
          prop: 'receiverName',
          label: '领取人姓名',
          width: 120
        },
        {
          prop: 'entName',
          label: '公司名称',
          width: 240
        },
        {
          prop: 'receiverOpenId',
          label: '领取人id',
          width: 220
        },
        {
          prop: 'rewardInfo',
          label: '奖品内容',
          minWidth: 120
        },
        {
          prop: 'status',
          label: '发放状态',
          minWidth: 120,
          formatter: row =>
            getOptionsItemLabel(activitySenDetailStatusOptions, row.status) ||
            '-'
        },
        {
          prop: 'failMessage',
          label: '发放结果反馈',
          minWidth: 120
        },
        {
          prop: 'promoter.name',
          label: '关联推广员'
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '导出',
          props: {
            loading: false,
            style: {
              'justify-content': 'center'
            }
          },
          click: async ({ props }) => {
            props.loading = true
            const body = this.oPcList.oTable.getRequestParams()
            const result =
              await marketingClient.activityExportActivityAwardRecord({
                body
              })
            await exportExcel(result)
            props.loading = false
          }
        }
      ]
    }
  },
  computed: {
    oPcList() {
      return this.$refs['pc-list']
    }
  },
  activated() {
    if (!this.isFirstLoad) this.reload()
  },
  methods: {
    // 刷新页面
    async reload() {
      this.oPcList.reload()
    },

    // 搜索之前对参数处理
    async beforeSearch(fData) {
      fData.activityId = this.$route.params.activityId
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')
      return fData
    }
  }
}
</script>
