<template>
  <div>
    <FormGroupTitle style="margin-bottom: 16px">活动信息</FormGroupTitle>
    <div class="table">
      <div class="row">
        <div class="cell title">活动名称</div>
        <div class="cell">
          <pre>{{ info.name }}</pre>
        </div>
        <div class="cell title">活动开始时间</div>
        <div class="cell">{{ formatDate(info.availableBeginTime) }}</div>
        <div class="cell title">活动结束时间</div>
        <div class="cell">{{ formatDate(info.availableEndTime) }}</div>
      </div>
      <div class="row">
        <div class="cell title">达标资格</div>
        <pre class="cell span-5">{{
          formatEmptyValue(info.qualification)
        }}</pre>
      </div>
      <div class="row">
        <div class="cell title">活动奖励</div>
        <pre class="cell span-5">{{ formatEmptyValue(info.rewardInfo) }}</pre>
      </div>
      <div class="row">
        <div class="cell title">备注</div>
        <pre class="cell span-5">{{ formatEmptyValue(info.remark) }}</pre>
      </div>
      <div class="row">
        <div class="cell title">活动方式</div>
        <div class="cell">{{ wayText }}</div>
        <div class="cell title">收集用户信息</div>
        <div class="cell span-3">
          {{ collectUserInfoText }}
        </div>
      </div>
      <div class="row" v-if="isCollectingInformation">
        <div class="cell title">是否进行参与人员资格接口校验</div>
        <div class="cell span-5">
          {{ info.checkQualification ? '是' : '否' }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  activityTypeOptions,
  collectUserOptions
} from '../wechatActivityOptions'
import FormGroupTitle from 'kit/components/marketing/admin/formGroupTitle.vue'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import formatDateTime from 'kit/formatters/dateTime'
import { formatEmptyValue } from 'kit/formatters/emptyValue'

export default {
  components: {
    FormGroupTitle
  },
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    wayText() {
      return getOptionsItemLabel(activityTypeOptions, this.info.getWay)
    },
    isCollectingInformation() {
      return Boolean(this.info.collectUserItemList.length)
    },
    collectUserInfoText() {
      if (!this.isCollectingInformation) return '不收集信息'
      const list = this.info.collectUserItemList.map(code =>
        getOptionsItemLabel(collectUserOptions, code)
      )
      return list.join('、')
    }
  },
  methods: {
    formatEmptyValue,
    formatDate(value) {
      return formatDateTime('yyyy-MM-dd', value)
    }
  }
}
</script>
<style scoped>
.table {
  border: 1px solid #e4e7edff;
  border-bottom: 0;
  border-right: 0;
}
.row {
  grid-auto-flow: row dense;
  display: grid;
  grid-template-columns: 195px repeat(5, 1fr);
  border-bottom: 1px solid #e4e7edff;
}
.span-5 {
  grid-column-end: span 5;
}
.span-3 {
  grid-column-end: span 3;
}
.cell {
  padding: 8px 24px;
  box-sizing: border-box;
  border-right: 1px solid #e4e7edff;
  min-height: 46px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
}
.title {
  background-color: #f7f9fc;
  color: #1e2228ff;
  line-height: 22px;
}
pre {
  line-height: 18px;
  white-space: pre-wrap;
}
</style>
