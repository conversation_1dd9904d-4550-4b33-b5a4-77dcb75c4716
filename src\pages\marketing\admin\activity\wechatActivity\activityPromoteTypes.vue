<template>
  <div>
    <FormGroupTitle style="margin-bottom: 16px">配置推广方式</FormGroupTitle>

    <Tabs
      :options="activityPromotionConfigOptions"
      v-model="tableValue"
      style="margin-bottom: 20px"
    />

    <H5PromoterInfo
      class="box"
      v-show="isH5PromoterInfo"
      v-if="qrCodeInfo"
      :qrCodeInfo="qrCodeInfo"
    />

    <PromoterTable
      class="box"
      v-show="isPromoterTable"
      :promoters="quotaList"
    />

    <TripartiteDocking class="box" v-show="isTripartiteDocking" />
  </div>
</template>

<script>
import FormGroupTitle from 'kit/components/marketing/admin/formGroupTitle.vue'
import { activityPromotionConfigOptions } from '../wechatActivityOptions'
import Tabs from 'kit/components/marketing/admin/tabs.vue'
import TripartiteDocking from './tripartiteDocking.vue'
import H5PromoterInfo from './h5PromoterInfo.vue'
import PromoterTable from './promoterTable.vue'

import {
  ACTIVITY_H5_PROMOTION,
  ACTIVITY_PROMOTION_OFFLINE,
  ACTIVITY_TRIPARTITE_DOCKING
} from '../../constants'

export default {
  props: {
    quotaList: {
      type: Array,
      default: () => []
    },
    promoteTypes: {
      type: Array,
      default: () => []
    },
    qrCodeInfo: {
      type: [Object, null],
      default: () => {}
    }
  },
  components: {
    TripartiteDocking,
    FormGroupTitle,
    H5PromoterInfo,
    PromoterTable,
    Tabs
  },
  computed: {
    isH5PromoterInfo() {
      return this.tableValue === ACTIVITY_H5_PROMOTION
    },
    isPromoterTable() {
      return this.tableValue === ACTIVITY_PROMOTION_OFFLINE
    },
    isTripartiteDocking() {
      return this.tableValue === ACTIVITY_TRIPARTITE_DOCKING
    }
  },
  data() {
    return {
      tableValue: '',
      activityPromotionConfigOptions
    }
  },
  created() {
    this.activityPromotionConfigOptions = activityPromotionConfigOptions.filter(
      item => {
        return this.promoteTypes.includes(item.value)
      }
    )
    this.tableValue = this.activityPromotionConfigOptions[0].value
  }
}
</script>
<style scoped>
.box {
  margin-bottom: 48px;
}
</style>