<template>
  <div>
    <div class="qrCode">
      <img :src="qrCodeImg" alt="" />
      <p
        style="
          color: #666;
          font-size: 14px;
          lin-height: 28px;
          margin-bottom: 20px;
          width: 100%;
          text-align: center;
        "
      >
        微信扫一扫预览效果
      </p>
      <el-button @click="handleDownLoadClick">下载图片</el-button>
    </div>

    <div class="link">
      <el-input :value="qrCodeInfo.url" disabled></el-input>
      <el-button style="margin-left: 10px" @click="handleCopyClick"
        >复制链接</el-button
      >
    </div>
    <!-- <div style="margin-top: 20px">
      <el-button @click="handleTestClick()"
        >测试代码：跳转到移动端抽奖</el-button
      >
    </div> -->
  </div>
</template>
<script>
import copyText from 'kit/helpers/copyText'
import { showMessage } from 'kit/helpers/showMessage'
import { downloadImage } from 'kit/helpers/downloadImage'

export default {
  props: {
    qrCodeInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    qrCodeImg() {
      return `data:image/png;base64,${this.qrCodeInfo.qrCode}`
    }
  },
  methods: {
    handleCopyClick() {
      copyText(this.qrCodeInfo.url)
      showMessage('复制成功，已粘贴到剪切板')
    },
    handleDownLoadClick() {
      const random = Math.random().toString(36).replace('0.', '')
      downloadImage(this.qrCodeImg, `${random}.png`)
    },
    handleTestClick() {
      const openid = 'o5qyP6YOlyPG8la-ztiam0sLWnvs'
      const fullURL = encodeURI(this.qrCodeInfo.url)
      const searchParams = new URLSearchParams(fullURL)
      const toURL = decodeURIComponent(searchParams.get('redirect_uri'))
      window.open(`${toURL}&test_openid=${openid}`)
    }
  }
}
</script>
<style scoped>
.qrCode {
  width: 200px;
  border: 1px solid #eee;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 4px;
}
img {
  display: block;
  width: 100%;
}
.link {
  display: flex;
  align-items: center;
  width: 50%;
}
</style>
