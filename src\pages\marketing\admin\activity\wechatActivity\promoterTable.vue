<template>
  <div>
    <Table :data="promoters" style="width: 100%; margin-bottom: 8px">
      <el-table-column label="推广人员">
        <template slot-scope="scope">
          {{ scope.row.promoter.name }}
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="额度" :formatter="formatter" />
      <el-table-column prop="useCount" label="已执行" />
    </Table>
  </div>
</template>

<script>
import Table from 'kit/components/marketing/admin/table.vue'
import formatAmount from 'kit/formatters/formatAmount'

export default {
  components: {
    Table
  },
  props: {
    promoters: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    formatter(row) {
      return formatAmount(row.amount)
    }
  }
}
</script>
