<template>
  <Container :back="true" :title="$route.meta.title">
    <div slot="header-right" class="title-button">
      <el-button type="primary" size="small" @click="handleEditClick"
        >修改</el-button
      >
    </div>
    <main style="padding: 24px; min-height: 60vh" v-loading="isLoading">
      <div v-if="info.couponsId">
        <el-button
          type="primary"
          style="margin-bottom: 16px"
          @click="redirectToViewDistributionDetails"
          >查看发放明细</el-button
        >
        <div class="table">
          <div class="row">
            <div class="cell title">批次名称</div>
            <div class="cell" style="width: 240px">
              <pre>{{ info.name }}</pre>
            </div>
            <div class="cell title">批次号</div>
            <div class="cell span-3">
              {{ info.stockId }}
            </div>
          </div>
          <div class="row">
            <div class="cell title">投放时间</div>
            <span class="cell" style="width: 240px"
              >{{ formatDate(info.availableBeginTime) }} ～
              {{ formatDate(info.availableEndTime) }}</span
            >
            <div class="cell title">有效期</div>
            <div class="cell span-3">{{ info.validDays }}天</div>
          </div>
          <div class="row">
            <div class="cell title">金额</div>
            <div class="cell span-5">
              <span>{{ amount }}</span>
            </div>
          </div>
          <div class="row">
            <div class="cell title">数量</div>
            <pre class="cell span-5">{{ info.num }}张</pre>
          </div>
          <div class="row">
            <div class="cell title">备注</div>
            <pre class="cell span-5">{{ info.remark || '-' }}</pre>
          </div>
        </div>
      </div>
    </main>
  </Container>
</template>

<script>
import Container from 'kit/components/marketing/admin/container.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import formatDateTime from 'kit/formatters/dateTime'
import handleError from 'kit/helpers/handleError'
import formatAmount from 'kit/formatters/formatAmount'
import { delay } from 'kit/helpers/delay'

const marketingClient = makeMarketingClient()

export default {
  components: {
    Container
  },
  data() {
    return {
      isLoading: false,
      info: {
        couponsId: '',
        name: '',
        stockId: '',
        validDays: '',
        num: '',
        remark: '',
        availableTime: '',
        availableBeginTime: '',
        availableEndTime: '',
        discountRule: {
          amount: '',
          discount: ''
        }
      }
    }
  },
  computed: {
    couponsId() {
      return this.$route.params.id
    },
    amount() {
      return `满${formatAmount(this.info.discountRule.amount)}减${formatAmount(
        this.info.discountRule.discount
      )}元`
    }
  },
  created() {
    this.loadDetail()
  },
  methods: {
    async handleEditClick() {
      this.$router.push(`/discount/meiTuanDiscountsNew/${this.couponsId}`)
    },
    formatDate(value) {
      if (!value) return '-'
      return formatDateTime('yyyy-MM-dd', value)
    },
    redirectToViewDistributionDetails() {
      this.$router.push(
        `/discount/meiTuanDiscountSentDetail?couponsId=${this.couponsId}`
      )
    },
    async loadDetail() {
      this.isLoading = true
      const [err, result] = await marketingClient.couponsMtGetCoupons({
        body: {
          id: this.couponsId
        }
      })
      if (err) {
        this.isLoading = false
        return handleError(err)
      }
      await delay(100)
      this.isLoading = false
      Object.assign(this.info, result.data)
    }
  }
}
</script>

<style scoped>
.table {
  border: 1px solid #e4e7edff;
  border-bottom: 0;
  border-right: 0;
}
.row {
  grid-auto-flow: row dense;
  display: grid;
  grid-template-columns: 195px repeat(5, 1fr);
  border-bottom: 1px solid #e4e7edff;
}
.span-5 {
  grid-column-end: span 5;
}
.span-3 {
  grid-column-end: span 3;
}

.cell {
  padding: 8px 24px;
  padding-right: 10px;
  box-sizing: border-box;
  border-right: 1px solid #e4e7edff;
  min-height: 46px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
}
.title {
  background-color: #f7f9fc;
  color: #1e2228ff;
  line-height: 22px;
}
.custom-rules .right {
  color: #1e2228ff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
}
.custom-rules .right .box {
  border-bottom: 1px solid #e4e7edff;
  line-height: 46px;
  padding-left: 24px;
  border-right: 1px solid #e4e7edff;
}
.custom-rules .right .box:last-child {
  border-bottom: 0;
}
pre {
  white-space: pre-wrap;
  line-height: 18px;
}
.title-button {
  flex: 1;
  display: flex;
  justify-content: end;
}
</style>
