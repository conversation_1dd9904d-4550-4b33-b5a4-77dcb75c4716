<template>
  <Container
    :back="true"
    @confirm="confirm"
    confirmButtonText="确定"
    :hideFootButton="isCreateSuccess"
    @cancel="cancel"
  >
    <div class="wrap" v-loading="isLoading">
      <div style="width: 560px; margin: 0 auto" v-if="!isCreateSuccess">
        <Form
          ref="form"
          :model="formData"
          :rules="formRules"
          label-position="top"
        >
          <el-form-item label="卡券名称" prop="name">
            <Input
              v-model="formData.name"
              :trim="true"
              :disabled="isFormItemDisabled"
              placeholder="请输入卡券名称"
              maxlength="20"
            />
          </el-form-item>

          <el-form-item label="卡券批次号" prop="stockId">
            <Input
              v-model.trim="formData.stockId"
              :disabled="isFormItemDisabled"
              :trimAll="true"
              placeholder="请联系供应商获取"
              maxlength="20"
            />
          </el-form-item>

          <el-form-item label="投放时间" prop="availableTime">
            <DateTimePicker
              :disabled="isFormItemDisabled"
              v-model="formData.availableTime"
              :picker-options="pickerOptions"
              :startTime.sync="formData.availableBeginTime"
              :endTime.sync="formData.availableEndTime"
              valueFormat="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="有效期" prop="validDays">
            <el-row type="flex">
              <!-- TODO 最大十万 -->
              <Input
                :disabled="isFormItemDisabled"
                v-model="formData.validDays"
                valueType="int"
                placeholder="请联系供应商获取"
                maxlength="7"
              />
              <span style="margin-left: 6px">天</span>
            </el-row>
          </el-form-item>

          <span class="amount-label">金额</span>
          <el-row type="flex" class="amount-form-item-wrap" align="middle">
            <span class="text">满</span>
            <el-form-item prop="discountRule.amount" style="flex: 1">
              <Input
                :disabled="isFormItemDisabled"
                v-model="formData.discountRule.amount"
                valueType="decimals_2"
                :autoRetainTwoDecimalPlaces="true"
                :trim="true"
                :allowZero="true"
                placeholder="请联系供应商获取"
                maxlength="12"
              />
            </el-form-item>
            <span class="text">元，减</span>
            <el-form-item prop="discountRule.discount">
              <Input
                :disabled="isFormItemDisabled"
                v-model="formData.discountRule.discount"
                valueType="decimals_2"
                :autoRetainTwoDecimalPlaces="true"
                @blur="validateFieldDiscountRuleAmount"
                :trim="true"
                :allowZero="true"
                placeholder="请联系供应商获取"
                maxlength="12"
              />
            </el-form-item>
            <span class="unit">元</span>
          </el-row>

          <el-form-item label="数量" prop="num">
            <el-row type="flex">
              <Input
                :disabled="isFormItemDisabled"
                v-model="formData.num"
                valueType="int"
                placeholder="请联系供应商获取"
                maxlength="5"
              />
              <span style="margin-left: 6px">张</span>
            </el-row>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <Textarea
              maxlength="1024"
              :trim="true"
              v-model="formData.remark"
              placeholder="请输入备注，不超过1024字"
            />
          </el-form-item>
        </Form>
      </div>
      <ActivityCreatedCompleted v-show="isCreateSuccess" />
    </div>
  </Container>
</template>

<script>
import ActivityCreatedCompleted from 'kit/components/marketing/admin/activityCreatedCompleted.vue'
import DateTimePicker from 'kit/components/marketing/admin/dateTimePicker.vue'
import Container from 'kit/components/marketing/admin/container.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import Textarea from 'kit/components/marketing/admin/textarea.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import { isAmountZero } from 'kit/helpers/isAmountZero'
import handleError from 'kit/helpers/handleError'
import deepClone from 'kit/helpers/deepClone'
import { delay } from 'kit/helpers/delay'
import { showMessage } from 'kit/helpers/showMessage'

const marketingClient = makeMarketingClient()

const formatRequestParams = params => {
  const cloneParams = deepClone(params)
  cloneParams.availableEndTime = cloneParams.availableEndTime.replace(
    '00:00:00',
    '23:59:59'
  )
  cloneParams.discountRule.type = 1
  return cloneParams
}

const formatResponseParams = params => {
  const newParams = deepClone(params)
  newParams.availableTime = [
    newParams.availableBeginTime,
    newParams.availableEndTime
  ]
  return newParams
}

export default {
  components: {
    ActivityCreatedCompleted,
    DateTimePicker,
    Container,
    Textarea,
    Input,
    Form
  },
  data() {
    return {
      isLoading: false,
      isCreateSuccess: false,
      formData: {
        name: '',
        stockId: '',
        validDays: '',
        num: '',
        remark: '',
        availableTime: '',
        availableBeginTime: '',
        availableEndTime: '',
        discountRule: {
          amount: '',
          discount: ''
        }
      },
      formRules: {
        name: [{ required: true, message: '请输入卡券名称', trigger: 'blur' }],
        stockId: [
          { required: true, message: '请输入卡券批次号', trigger: 'blur' }
        ],
        validDays: [
          { required: true, message: '请输入有效期', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              value = parseInt(value, 10)
              if (value > 1000000 || value < 1) {
                callback(new Error('有效期天数在1到1000000之间'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        num: [
          { required: true, message: '请输入数量', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              value = parseInt(value, 10)
              if (value > 10000 || value < 1) {
                callback(new Error('数量在1到10000之间'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        availableTime: [
          { required: true, message: '请选择投放时间', trigger: 'change' }
        ],
        discountRule: {
          amount: [
            { required: true, message: '请输入满减金额', trigger: 'blur' },
            {
              validator: (_rules, value, callback) => {
                if (isAmountZero(value)) return callback('金额不能为0')
                if (!this.formData.discountRule.discount) return callback()
                if (!value) return callback()
                // TODO 校验
                if (
                  Number(value) < Number(this.formData.discountRule.discount)
                ) {
                  callback('满减金额需大于等于减扣金额')
                  return
                }
                callback()
              },
              trigger: 'blur'
            }
          ],
          discount: [
            { required: true, message: '请输入减扣金额', trigger: 'blur' },
            {
              validator: (_rules, value, callback) => {
                if (isAmountZero(value)) return callback('金额不能为0')
                callback()
              },
              trigger: 'blur'
            }
          ]
        }
      },
      pickerOptions: {
        disabledDate(time) {
          const today = new Date()
          today.setHours(0, 0, 0, 0)
          return time.getTime() < today.getTime()
        }
      }
    }
  },
  computed: {
    isEdit() {
      return this.couponsId
    },
    isFormItemDisabled() {
      return Boolean(this.isEdit)
    },
    couponsId() {
      return this.$route.params.id
    }
  },
  mounted() {
    if (this.couponsId) this.loadDetail()
  },
  methods: {
    async loadDetail() {
      this.isLoading = true
      const [err, { data }] = await marketingClient.couponsMtGetCoupons({
        body: {
          id: this.couponsId
        }
      })
      await delay(200)
      this.isLoading = false
      if (err) return handleError(err)
      this.formData = formatResponseParams(data)
    },
    validateFieldDiscountRuleAmount() {
      this.$refs.form.validateField('discountRule.amount')
    },
    validate() {
      return this.$refs.form.validate()
    },
    cancel() {
      this.$router.back()
    },
    async confirm() {
      const error = await this.validate()
      if (error) return

      const params = formatRequestParams(this.formData)

      if (this.isEdit) {
        params.id = this.couponsId
        const [err] = await marketingClient.couponsMtUpdateCoupons({
          body: params
        })
        if (err) {
          return handleError(err)
        }
        this.cancel()
        showMessage('修改成功')
        return
      }

      const [err] = await marketingClient.couponsMtSaveCoupons({
        body: params
      })

      if (err) {
        handleError(err)
      }

      this.isCreateSuccess = true
    }
  }
}
</script>

<style scoped>
.wrap {
  padding: 24px 0;
}
.amount-label {
  font-size: 14px;
}
.amount-label:before {
  content: '*';
  color: #f8605b;
  margin-right: 4px;
}
.amount-form-item-wrap ::v-deep .el-form-item {
  margin-bottom: 6px;
}
.amount-form-item-wrap {
  gap: 10px;
  font-size: 14px;
  margin: 6px 0;
}
</style>
