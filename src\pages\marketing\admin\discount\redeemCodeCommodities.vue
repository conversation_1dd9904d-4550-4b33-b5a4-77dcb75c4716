<template>
  <o-pc-list
    ref="pc-list"
    :title="$route.meta.title"
    :formJson="searchFormJson"
    :requestFn="getListApi"
    labelWidth="70px"
    :deleteNullApiParams="true"
    :actionButtons="actionButtons"
    :tableHeaderActionButtons="tableHeaderActionButtons"
    :tableHeader="tableHeader"
    :beforeSearch="beforeSearch"
  />
</template>
<script>
import { showMessage } from 'kit/helpers/showMessage'
import handleError from 'kit/helpers/handleError'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { oConfirm } from 'kit/components/marketing/admin/messageBox'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.redeemcodeGoodsQuery({
    body: params
  })
  if (err) return handleError(err)
  return result.data
}

export default {
  data() {
    return {
      getListApi: loadList,
      searchFormJson: [
        {
          type: 'input',
          item: {
            prop: 'name',
            label: '商品名称',
            placeholder: '请输入商品名称'
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            prop: 'create',
            label: '创建时间',
            rangeSeparator: '~',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd 00:00:00'
          }
        }
      ],
      isFirstLoad: true,
      tableHeader: [
        {
          prop: 'vid',
          label: '商品id',
          fixed: true
        },
        {
          prop: 'name',
          label: '商品名称',
          minWidth: 150,
          click: row =>
            this.$router.push(`/discount/redeemCodeCommodity/${row.id}`)
        },
        {
          prop: 'availableCodeCount',
          label: '当前可兑换数量'
        }
      ],
      actionButtons: [
        {
          label: '兑换码详情',
          click: row => {
            this.$router.push(`/discount/redeemCodeSendDetail?id=${row.id}`)
          }
        },
        {
          label: '删除',
          click: row => {
            oConfirm(
              '删除后将无法找回此数据，请谨慎操作',
              '删除此兑换码商品？',
              {
                confirm: async () => {
                  const [err] = await marketingClient.redeemcodeGoodsDelete({
                    body: {
                      id: row.id
                    }
                  })
                  if (err) return handleError(err)
                  this.tableReload()
                  showMessage('操作成功')
                }
              }
            )
          }
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '新建兑换码商品',
          click: () => {
            this.$router.push('/discount/redeemCodeCommodityNew')
          }
        }
      ]
    }
  },
  computed: {
    oTable() {
      return this.$refs['pc-list']
    }
  },
  activated() {
    if (!this.isFirstLoad) this.reload()
  },
  methods: {
    // 刷新页面
    async tableReload() {
      this.oTable.reload()
    },

    // 搜索之前对参数处理
    async beforeSearch(fData) {
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')
      return fData
    }
  }
}
</script>
