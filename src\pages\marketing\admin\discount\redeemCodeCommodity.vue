<template>
  <Container :back="true" :title="$route.meta.title">
    <div slot="header-right" class="title-button">
      <el-button type="primary" size="small" @click="handleEditClick"
        >修改兑换码商品</el-button
      >
    </div>
    <main style="padding: 24px; min-height: 60vh" v-loading="isLoading">
      <div>
        <el-button
          type="primary"
          style="margin-bottom: 16px"
          @click="viewRedeemCodeDetails"
          >查看兑换码详情</el-button
        >
        <div class="table">
          <div class="row">
            <div class="cell title">商品名称</div>
            <div class="cell" style="width: 240px">{{ info.name }}</div>
            <div class="cell title">商品id</div>
            <div class="cell span-3">
              {{ info.vid }}
            </div>
          </div>
          <div class="row">
            <div class="cell title">备注</div>
            <pre class="cell span-5">{{ info.remark || '-' }}</pre>
          </div>
          <div class="row">
            <div class="cell title">是否同时发放短信</div>
            <pre class="cell span-5">{{
              info.sendSms ? '发放' : '无需发放'
            }}</pre>
          </div>
        </div>
      </div>
    </main>
  </Container>
</template>

<script>
import Container from 'kit/components/marketing/admin/container.vue'
import { delay } from 'kit/helpers/delay'
import handleError from 'kit/helpers/handleError'
import makeMarketingClient from 'kit/services/marketing/makeClient'

const marketingClient = makeMarketingClient()

export default {
  components: {
    Container
  },
  data() {
    return {
      isLoading: false,
      info: {
        id: '',
        name: '',
        remark: '',
        sendSms: ''
      }
    }
  },
  computed: {
    id() {
      return this.$route.params.id
    }
  },
  created() {
    this.loadDetail()
  },
  methods: {
    async handleEditClick() {
      this.$router.push(`/discount/redeemCodeCommodityNew/${this.id}`)
    },
    viewRedeemCodeDetails() {
      this.$router.push(`/discount/redeemCodeSendDetail?id=${this.id}`)
    },
    async loadDetail() {
      this.isLoading = true
      const [err, result] = await marketingClient.redeemcodeGoodsDetail({
        body: {
          id: this.id
        }
      })
      if (err) {
        this.isLoading = false
        return handleError(err)
      }
      await delay(100)
      this.isLoading = false
      Object.assign(this.info, result.data)
    }
  }
}
</script>

<style scoped>
.table {
  border: 1px solid #e4e7edff;
  border-bottom: 0;
  border-right: 0;
}
.row {
  grid-auto-flow: row dense;
  display: grid;
  grid-template-columns: 195px repeat(5, 1fr);
  border-bottom: 1px solid #e4e7edff;
}
.span-5 {
  grid-column-end: span 5;
}
.span-3 {
  grid-column-end: span 3;
}

.cell {
  padding: 8px 24px;
  padding-right: 10px;
  box-sizing: border-box;
  border-right: 1px solid #e4e7edff;
  min-height: 46px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
}
.title {
  background-color: #f7f9fc;
  color: #1e2228ff;
  line-height: 22px;
}
.custom-rules .right {
  color: #1e2228ff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
}
.custom-rules .right .box {
  border-bottom: 1px solid #e4e7edff;
  line-height: 46px;
  padding-left: 24px;
  border-right: 1px solid #e4e7edff;
}
.custom-rules .right .box:last-child {
  border-bottom: 0;
}
pre {
  white-space: pre-wrap;
  line-height: 18px;
  word-break: break-all;
  display: block;
}
.title-button {
  flex: 1;
  display: flex;
  justify-content: end;
}
</style>
