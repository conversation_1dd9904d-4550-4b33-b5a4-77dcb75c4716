<template>
  <Container
    :back="true"
    @confirm="confirm"
    confirmButtonText="提交"
    :hideFootButton="isCreateSuccess"
    @cancel="cancel"
  >
    <div class="wrap" v-loading="isLoading">
      <div style="width: 560px; margin: 0 auto" v-if="!isCreateSuccess">
        <Form
          ref="form"
          :model="formData"
          :rules="formRules"
          label-position="top"
        >
          <el-form-item label="商品名称" prop="name">
            <Input
              v-model="formData.name"
              :trim="true"
              placeholder="请输入商品名称"
              maxlength="20"
            />
          </el-form-item>
          <el-form-item label="商品id" prop="vid">
            <Input
              v-model="formData.vid"
              :trim="true"
              placeholder="请输入商品id"
              maxlength="20"
            />
          </el-form-item>
          <el-form-item label="商品备注" prop="remark">
            <Textarea
              maxlength="1024"
              :trim="true"
              v-model="formData.remark"
              placeholder="请输入商品备注"
            />
          </el-form-item>
          <el-form-item label="是否同时发放短信" prop="sendSms">
            <Radio v-model="formData.sendSms" :options="isSendSmsOptions" />
          </el-form-item>
          <el-form-item label="导入兑换码" v-if="!isEdit">
            <div
              style="
                padding: 24px;
                text-align: left;
                font-size: 14px;
                border-radius: 6px;
                background: #f7f9fcff;
              "
            >
              <div class="button" @click="download">
                <i
                  style="margin-right: 8px; color: #828b9b"
                  class="icon iconfont icon-direction-interaction-download"
                ></i
                >下载空的导入模板
              </div>
            </div>
            <div style="border-radius: 6px; margin: 16px 0; text-align: center">
              <el-upload
                width="100%"
                :headers="headerToken"
                ref="upload"
                class="upload"
                drag
                :data="uploadData"
                :file-list="fileList"
                :auto-upload="false"
                :on-change="onChange"
                :on-remove="onRemove"
                accept=".xls, .xlsx"
                :action="uploadUrl"
              >
                <div
                  style="
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 30px 0 24px;
                  "
                >
                  <img src="kit/assets/images/upload-excel.svg" alt="" />
                  <el-button
                    style="
                      width: 88px;
                      margin: 10px 0 8px;
                      border: 1px solid #cad0dbff;
                      color: #1e2228;
                      display: flex;
                      justify-content: center;
                    "
                    plain
                    >选择文件</el-button
                  >
                  <div style="color: #828b9b; font-size: 14px">
                    支持xlsx和xls文件，文件大小不超过5M
                  </div>
                </div>
              </el-upload>
            </div>
          </el-form-item>

          <el-form-item label="兑换码详情" v-if="isEdit">
            <el-button type="primary" @click="viewRedeemCode">
              查看兑换码
            </el-button>
          </el-form-item>
        </Form>
      </div>
      <ActivityCreatedCompleted v-show="isCreateSuccess" />
    </div>
  </Container>
</template>

<script>
import ActivityCreatedCompleted from 'kit/components/marketing/admin/activityCreatedCompleted.vue'
import Container from 'kit/components/marketing/admin/container.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import Textarea from 'kit/components/marketing/admin/textarea.vue'
import Radio from 'kit/components/marketing/admin/radio.vue'
import { isSendSmsOptions } from './wechatDiscountsOptions'
import { exportExcel } from 'kit/helpers/exportExcel'
import { getToken } from 'kit/helpers/token'
import deepClone from 'kit/helpers/deepClone'
import { delay } from 'kit/helpers/delay'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import handleError from 'kit/helpers/handleError'

const marketingClient = makeMarketingClient()

const formatRequestParams = params => {
  const formData = new FormData()
  Object.keys(params).forEach(key => {
    formData.append(key, params[key])
  })
  return formData
}

const formatResponseParams = params => {
  const newParams = deepClone(params)
  return newParams
}

export default {
  components: {
    ActivityCreatedCompleted,
    Container,
    Form,
    Input,
    Textarea,
    Radio
  },
  data() {
    return {
      isSendSmsOptions,
      isLoading: false,
      isCreateSuccess: false,
      headerToken: {
        Authorization: `${getToken()}`
      },
      uploadData: {},
      fileList: [],
      formData: {
        name: '',
        vid: '',
        remark: '',
        sendSms: true
      },
      formRules: {
        name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        vid: [{ required: true, message: '请输入商品id', trigger: 'blur' }]
      }
    }
  },
  computed: {
    isEdit() {
      return this.id
    },
    id() {
      return this.$route.params.id
    },
    uploadUrl() {
      return `${window.env.api}/marketing/redeemcode/goods/uploadCode`
    }
  },
  mounted() {
    if (this.id) this.loadDetail()
  },
  methods: {
    async loadDetail() {
      this.isLoading = true
      const [err, r] = await marketingClient.redeemcodeGoodsDetail({
        body: {
          id: this.id
        }
      })
      await delay(200)
      this.isLoading = false
      if (err) return handleError(err)
      this.formData = formatResponseParams(r.data)
    },
    async download() {
      const result = await marketingClient.fileDownloadTemplate({
        body: {
          tpl: 'TPL_IMPORT_REDEEM_CODE'
        }
      })
      await exportExcel(result)
    },
    viewRedeemCode() {
      this.$router.push({
        path: '/discount/redeemCodeSendDetail',
        query: {
          id: this.id
        }
      })
    },
    cancel() {
      this.$router.back()
    },
    updateFileList(file, fileList) {
      for (var c of fileList) {
        if (c.uid === file.uid) {
          fileList.splice(fileList.indexOf(c), 1)
        }
      }
    },
    onChange(file, fileList) {
      let suffix = this.getFileType(file.name) // 获取文件后缀名
      let suffixArray = ['xls', 'xlsx'] // 限制的文件类型
      if (suffixArray.indexOf(suffix) === -1) {
        this.$message.error('文件格式错误，请重新选择！')
        this.updateFileList(file, fileList)
        return
      }
      const isLimit = file.size / 1024 / 1024 < 5
      if (!isLimit) {
        this.$message.error('导入文件的大小最多支持5M')
        this.updateFileList(file, fileList)
        return
      }
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]]
      } else {
        this.fileList = []
      }
    },
    getFileType(name) {
      let startIndex = name.lastIndexOf('.')
      if (startIndex !== -1) {
        return name.slice(startIndex + 1).toLowerCase()
      } else {
        return ''
      }
    },
    onRemove() {
      this.fileList = []
    },
    async confirm() {
      const error = await this.$refs.form.validate()
      if (error) return

      if (!this.isEdit) {
        this.formData.codeFile = this.fileList.length
          ? this.fileList[0].raw
          : ''
      }
      const params = formatRequestParams(this.formData)

      const fetchConfig = {
        method: 'POST',
        body: params,
        requestInterceptor(resource, options) {
          delete options.headers['Content-Type']
          return [null, resource, options]
        }
      }
      if (this.isEdit) {
        this.formData.id = this.id
        const [err] = await marketingClient.redeemcodeGoodsUpdate({
          body: this.formData
        })
        if (err) return handleError(err)

        this.cancel()
        this.$message.success('修改成功')
        return
      }

      const [err] = await marketingClient.redeemcodeGoodsSave(fetchConfig)
      if (err) return handleError(err)
      this.isCreateSuccess = true
    }
  }
}
</script>

<style scoped>
.wrap {
  padding: 24px 0;
}
.amount-label {
  font-size: 14px;
}
.button {
  width: 170px;
  padding: 0 24px;
  cursor: pointer;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #cad0dbff;
  box-shadow: 0 2px 0 0 #00000005;
}
.amount-label:before {
  content: '*';
  color: #f8605b;
  margin-right: 4px;
}
.amount-form-item-wrap ::v-deep .el-form-item {
  margin-bottom: 0;
}
.amount-form-item-wrap {
  gap: 10px;
  margin: 6px 0;
}
::v-deep .el-upload {
  width: 100%;
}
::v-deep .el-upload-dragger {
  width: 100%;
  border: 1px dashed #d9d9d9;
  background-color: #f7f9fcff;
}
::v-deep .el-upload-dragger:hover {
  border-color: var(--o-primary-color);
}
</style>
