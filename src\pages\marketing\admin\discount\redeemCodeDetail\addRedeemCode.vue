<template>
  <div>
    <el-dialog width="55%" :visible.sync="visible" @close="close">
      <div
        style="color: #1e2228ff; font-size: 16px; font-weight: 600"
        slot="title"
      >
        新增兑换码
      </div>
      <div
        style="
          padding: 24px;
          text-align: left;
          font-size: 14px;
          border-radius: 6px;
          background: #f7f9fcff;
        "
      >
        <span style="color: #1e2228ff">下载导入模版</span>
        <div class="button" @click="download">
          <i
            style="margin-right: 8px; color: #828b9b"
            class="icon iconfont icon-direction-interaction-download"
          ></i
          >下载空的导入模板
        </div>
      </div>
      <div style="border-radius: 6px; margin: 16px 0; text-align: center">
        <el-upload
          width="100%"
          :headers="headerToken"
          ref="upload"
          class="upload"
          drag
          :data="uploadData"
          :file-list="fileList"
          :auto-upload="false"
          :on-change="onChange"
          :on-remove="onRemove"
          accept=".xls, .xlsx"
          :action="uploadUrl"
        >
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              padding: 30px 0 24px;
            "
          >
            <img src="kit/assets/images/upload-excel.svg" alt="" />
            <el-button
              style="
                width: 88px;
                margin: 10px 0 8px;
                border: 1px solid #cad0dbff;
                color: #1e2228;
                display: flex;
                justify-content: center;
              "
              plain
              >选择文件</el-button
            >
            <div style="color: #828b9b; font-size: 14px">
              支持xlsx和xls文件，文件大小不超过5M
            </div>
          </div>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          style="color: #1e2228; font-weight: 400"
          type="plain"
          @click="close"
          >取消</el-button
        >
        <el-button type="primary" @click="handleImport" :loading="loading"
          >导入</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { exportExcel } from 'kit/helpers/exportExcel'
import { getToken } from 'kit/helpers/token'
import { showMessage } from 'kit/helpers/showMessage'
import handleError from 'kit/helpers/handleError'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

const formatRequestParams = params => {
  const formData = new FormData()
  Object.keys(params).forEach(key => {
    formData.append(key, params[key])
  })
  return formData
}
export default {
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      headerToken: {
        Authorization: `${getToken()}`
      },
      uploadData: {},
      fileList: []
    }
  },
  created() {
    window.that = this
  },
  computed: {
    uploadUrl() {
      return `${window.env.api}/marketing/redeemcode/goods/uploadCode`
    }
  },
  methods: {
    open() {
      this.visible = true
    },
    close() {
      this.$refs.upload.clearFiles()
      this.visible = false
    },
    async download() {
      const result = await marketingClient.fileDownloadTemplate({
        body: {
          tpl: 'TPL_IMPORT_REDEEM_CODE'
        }
      })
      await exportExcel(result)
    },
    async handleImport() {
      if (!this.fileList.length) {
        return showMessage('请上传excel文件', 'error')
      }
      this.loading = true
      var params = {}
      params.id = this.id
      params.codeFile = this.fileList[0].raw
      params = formatRequestParams(params)

      const fetchConfig = {
        method: 'POST',
        body: params,
        requestInterceptor(resource, options) {
          delete options.headers['Content-Type']
          return [null, resource, options]
        }
      }

      const [err] = await marketingClient.redeemcodeGoodsUploadCode(fetchConfig)
      this.loading = false
      if (err) {
        err.errorCode = Number(err.errorCode)
        handleError(err)
        this.$refs.upload.clearFiles()
        return
      }

      this.close()
      showMessage('导入成功！')
      this.$emit('refresh')
    },
    updateFileList(file, fileList) {
      for (var c of fileList) {
        if (c.uid === file.uid) {
          fileList.splice(fileList.indexOf(c), 1)
        }
      }
    },
    onChange(file, fileList) {
      let suffix = this.getFileType(file.name) // 获取文件后缀名
      let suffixArray = ['xls', 'xlsx'] // 限制的文件类型
      if (suffixArray.indexOf(suffix) === -1) {
        this.$message.error('文件格式错误，请重新选择！')
        this.updateFileList(file, fileList)
        return
      }
      const isLimit = file.size / 1024 / 1024 < 5
      if (!isLimit) {
        this.$message.error('导入文件的大小最多支持5M')
        this.updateFileList(file, fileList)
        return
      }
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]]
      } else {
        this.fileList = []
      }
    },
    getFileType(name) {
      let startIndex = name.lastIndexOf('.')
      if (startIndex !== -1) {
        return name.slice(startIndex + 1).toLowerCase()
      } else {
        return ''
      }
    },
    onRemove() {
      this.fileList = []
    }
  }
}
</script>
<style scoped>
.button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 168px;
  height: 32px;
  border-radius: 6px;
  margin: 8px 0 4px;
  border: 1px solid #cad0dbff;
  background: #ffffffff;
  box-shadow: 0 2px 0 0 #00000005;
  cursor: pointer;
}
::v-deep .el-upload {
  width: 100%;
}
::v-deep .el-upload-dragger {
  width: 100%;
  border: 1px dashed #d9d9d9;
  background-color: #f7f9fcff;
}
::v-deep .el-dialog__header {
  padding: 16px 24px 16px;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
}
::v-deep .el-dialog__body {
  padding-bottom: 0;
}
::v-deep .el-dialog__footer {
  border-top: 1px solid #e4e7ed;
  padding: 10px 20px;
}
</style>
