<template>
  <div>
    <!-- 筛选区域 -->
    <o-top-select
      ref="top-select"
      :formJson="topSelectFormJson"
      :immediate="true"
      class="zp-mb-16 o-app"
      labelWidth="84px"
      @search="onSearch"
    />

    <!-- 表格区域 -->
    <o-table
      ref="o-table"
      :sticky="true"
      :pagination="{ fixed: true }"
      :showPagination="true"
      :deleteNullApiParams="true"
      :tableHeader="tableHeader"
      :requestFn="getListApi"
      emptyHeight="calc(100vh - 450px)"
      :tableHeaderActionButtons="tableHeaderActionButtons"
    />
  </div>
</template>
<script>
import { exportExcel } from 'kit/helpers/exportExcel'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import handleError from 'kit/helpers/handleError'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.redeemcodeGoodsQueryCode({
    body: params
  })
  if (err) return handleError(err)
  return result.data
}
export default {
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      getListApi: loadList,
      topSelectFormJson: [
        {
          type: 'input',
          item: {
            prop: 'code',
            label: '兑换码',
            placeholder: '请输入兑换码'
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            prop: 'createTime',
            rangeSeparator: '~',
            label: '添加时间',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd 00:00:00'
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            rangeSeparator: '~',
            prop: 'sendTime',
            label: '发放时间',
            startField: 'sendTimeBegin',
            endField: 'sendTimeEnd',
            valueFormat: 'yyyy-MM-dd 00:00:00'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'activityId',
            label: '发放活动ID',
            valueType: 'int',
            placeholder: '请输入发放活动ID'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'receiverName',
            label: '领取人姓名',
            placeholder: '请输入领取人姓名'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'receiverMobile',
            label: '领取人手机号',
            placeholder: '请输入领取人手机号'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'receiverOpenid',
            label: '领取人id',
            placeholder: '请输入领取人id'
          }
        }
      ],
      tableHeader: [
        {
          label: '兑换码',
          prop: 'code',
          fixed: true,
          minWidth: 150
        },
        {
          label: '添加时间',
          prop: 'createTime',
          type: 'DATE_TIME',
          minWidth: 130
        },
        {
          label: '发放时间',
          prop: 'sendTime',
          type: 'DATE_TIME',
          minWidth: 130
        },
        {
          label: '发放活动ID',
          prop: 'activity.id',
          minWidth: 130
        },
        {
          label: '领取人id',
          prop: 'receiverOpenid',
          minWidth: 130
        },
        {
          label: '领取人姓名',
          prop: 'receiverName',
          minWidth: 130
        },
        {
          label: '领取人手机号',
          prop: 'receiverMobile',
          minWidth: 130
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '导出',
          props: {
            loading: false,
            style: {
              'justify-content': 'center'
            }
          },
          click: async ({ props }) => {
            props.loading = true
            const body = this.oTable.getRequestParams()
            const result = await marketingClient.redeemcodeGoodsExportCode({
              body
            })
            await exportExcel(result)
            props.loading = false
          }
        }
      ]
    }
  },
  computed: {
    oTable() {
      return this.$refs['o-table']
    }
  },
  methods: {
    async onSearch() {
      const fData = await this.$refs['top-select'].getFormData()
      fData.goodsId = this.id
      fData.status = '2'
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')

      await this.oTable.appendRequestParams(fData)
      this.isFirstLoad = false
    }
  }
}
</script>
