<template>
  <div>
    <!-- 筛选区域 -->
    <o-top-select
      ref="top-select"
      :formJson="topSelectFormJson"
      :immediate="true"
      class="zp-mb-16 o-app"
      labelWidth="84px"
      @search="onSearch"
    />

    <!-- 表格区域 -->
    <o-table
      ref="o-table"
      :sticky="true"
      :pagination="{ fixed: true }"
      :showPagination="true"
      :deleteNullApiParams="true"
      :tableHeader="tableHeader"
      :requestFn="getListApi"
      emptyHeight="calc(100vh - 450px)"
      :actionButtons="actionButtons"
      :tableHeaderActionButtons="tableHeaderActionButtons"
    />

    <AddRedeemCode ref="addRedeemCode" :id="id" @refresh="tableReload" />
  </div>
</template>
<script>
import { exportExcel } from 'kit/helpers/exportExcel'
import AddRedeemCode from './addRedeemCode.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import handleError from 'kit/helpers/handleError'
import { showMessage } from 'kit/helpers/showMessage'
import { oConfirm } from 'kit/components/marketing/admin/messageBox'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.redeemcodeGoodsQueryCode({
    body: params
  })
  if (err) return handleError(err)
  return result.data
}
export default {
  components: {
    AddRedeemCode
  },
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      getListApi: loadList,
      isFirstLoad: true,
      topSelectFormJson: [
        {
          type: 'input',
          item: {
            prop: 'code',
            label: '兑换码',
            placeholder: '请输入兑换码'
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            rangeSeparator: '~',
            prop: 'createTime',
            label: '添加时间',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd 00:00:00'
          }
        }
      ],
      tableHeader: [
        {
          label: '兑换码',
          prop: 'code',
          fixed: true,
          minWidth: 150
        },
        {
          label: '添加时间',
          prop: 'createTime',
          type: 'DATE_TIME',
          minWidth: 150
        }
      ],
      actionButtons: [
        {
          label: '删除',
          click: row => {
            oConfirm('是否删除此兑换码，删除后将无法找回', '删除此兑换码？', {
              confirm: async () => {
                const [err] = await marketingClient.redeemcodeGoodsDeleteCode({
                  body: {
                    id: row.id
                  }
                })
                if (err) return handleError(err)
                this.tableReload()
                showMessage('操作成功')
              }
            })
          }
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '新增兑换码',
          props: {
            style: {
              'justify-content': 'center'
            }
          },
          click: () => {
            this.$refs.addRedeemCode.open()
          }
        },
        {
          align: 'left',
          type: 'button',
          label: '导出',
          props: {
            loading: false,
            style: {
              'justify-content': 'center'
            }
          },
          click: async ({ props }) => {
            props.loading = true
            const body = this.oTable.getRequestParams()
            const result = await marketingClient.redeemcodeGoodsExportCode({
              body
            })
            await exportExcel(result)
            props.loading = false
          }
        }
      ]
    }
  },
  activated() {
    if (!this.isFirstLoad) this.tableReload()
  },
  computed: {
    oTable() {
      return this.$refs['o-table']
    }
  },
  methods: {
    async tableReload() {
      this.oTable.reload()
    },
    // 搜索
    async onSearch() {
      const fData = await this.$refs['top-select'].getFormData()
      fData.goodsId = this.id
      fData.status = '1'
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')

      await this.oTable.appendRequestParams(fData)
      this.isFirstLoad = false
    }
  }
}
</script>
