<template>
  <o-container ref="container" :back="true" :title="$route.meta.title">
    <!-- 状态筛选tabs -->
    <AppTabs @change="filterStatusTabChange" />

    <UnissuedTable v-if="grantStatus === 'unissued'" :id="id" />
    <IssuedTable v-else :id="id" />
  </o-container>
</template>
<script>
import AppTabs from './redeemCodeDetail/appTabs.vue'
import UnissuedTable from './redeemCodeDetail/unissuedTable.vue'
import IssuedTable from './redeemCodeDetail/issuedTable.vue'

export default {
  components: {
    AppTabs,
    UnissuedTable,
    IssuedTable
  },
  data() {
    return {
      grantStatus: 'unissued'
    }
  },
  computed: {
    id() {
      return this.$route.query.id
    }
  },
  methods: {
    // 状态发生改变
    filterStatusTabChange(value) {
      console.log(value)
      this.grantStatus = value
      //   this.onSearch({
      //     status: value
      //   })
    }
  }
}
</script>
