<template>
  <Container :back="true" :title="$route.meta.title">
    <main style="padding: 24px; min-height: 60vh" v-loading="isLoading">
      <div v-if="info.couponsId">
        <el-button
          type="primary"
          style="margin-bottom: 16px"
          @click="redirectToViewDistributionDetails"
          >查看发放明细</el-button
        >
        <div class="table">
          <div class="row">
            <div class="cell title">卡券名称</div>
            <div class="cell" style="width: 240px">
              <pre>{{ info.name }}</pre>
            </div>
            <div class="cell title">卡券批次号</div>
            <div class="cell span-3">
              {{ info.stockId }}
            </div>
          </div>
          <div class="row">
            <div class="cell title">投放时间</div>
            <span class="cell" style="width: 240px"
              >{{ formatDate(info.availableBeginTime) }} ～
              {{ formatDate(info.availableEndTime) }}</span
            >
            <div class="cell title">活动银行</div>
            <div class="cell span-3">
              {{ info.bankName }}
            </div>
          </div>
          <div class="row">
            <div class="cell title">活动规则</div>
            <div class="cell span-5">
              <span>{{ getDiscountRuleName(info.discountRule.type) }}</span>
              <span v-if="isFixedAmount">
                ，单张优惠券满
                {{ formatAmount(info.discountRule.amount) }} 元可减
                {{ formatAmount(info.discountRule.discount) }} 元
              </span>
            </div>
          </div>
          <div class="row">
            <div class="cell title">活动预算</div>
            <div class="cell span-5">
              <span v-if="!isEdit">{{ budget }} 元</span>
              <div class="edit" v-else>
                <Input
                  :allowZero="true"
                  v-model="budget"
                  valueType="decimals_2"
                  style="width: 160px; margin-right: 8px"
                />
                <span>元</span>
              </div>
              <el-button
                type="text"
                @click="handleEditClick"
                v-if="!isEdit"
                style="font-weight: normal; margin-left: 10px"
                >编辑</el-button
              >
              <el-button
                type="text"
                @click="handleUpdateBudgetClick"
                :loading="isEditLoading"
                v-if="isEdit"
                style="margin-left: 16px"
                >保存</el-button
              >
              <el-button
                type="text"
                @click="handleCancelEditClick"
                v-if="isEdit"
                >取消</el-button
              >
            </div>
          </div>
          <div class="row">
            <div class="cell title">使用规则</div>
            <pre class="cell span-5">{{ info.remark || '-' }}</pre>
          </div>
          <!-- <div class="row custom-rules">
            <div class="cell title">自定义规则</div>
            <div class="span-5 right">
              <div class="box">|| formatEmptyValue(info.remark) ??</div>
              <div class="box">|| formatEmptyValue(info.remark) ??</div>
            </div>
          </div> -->
          <div class="row">
            <div class="cell title">微信规则</div>
            <pre class="cell span-5">{{
              getInterceptRuleName(info.interceptRule)
            }}</pre>
          </div>
        </div>
      </div>
    </main>
  </Container>
</template>

<script>
import {
  discountRuleOptions,
  interceptRuleOptions
} from './wechatDiscountsOptions'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import Container from 'kit/components/marketing/admin/container.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import Input from 'kit/components/marketing/admin/input.vue'
import formatDateTime from 'kit/formatters/dateTime'
import handleError from 'kit/helpers/handleError'
import formatAmount from 'kit/formatters/formatAmount'
import { FIXED_AMOUNT } from '../constants'
import { delay } from 'kit/helpers/delay'
import { showMessage } from 'kit/helpers/showMessage'

const marketingClient = makeMarketingClient()

export default {
  components: {
    Container,
    Input
  },
  data() {
    return {
      isLoading: false,
      isEdit: false,
      isEditLoading: false,
      budget: '',
      info: {
        couponsId: '',
        name: '',
        createTime: '',
        remark: '',
        availableBeginTime: '',
        availableEndTime: '',
        stockId: '',
        budget: '',
        discountRule: {
          type: '',
          amount: '',
          discount: ''
        },
        bankName: '',
        bankCardType: '',
        bindCardBin: '',
        interceptRule: [],
        status: ''
      }
    }
  },
  computed: {
    couponsId() {
      return this.$route.params.id
    },
    isFixedAmount() {
      return FIXED_AMOUNT === String(this.info.discountRule.type)
    }
  },
  created() {
    this.loadDetail()
  },
  methods: {
    formatAmount,
    updateEditStatus(edit) {
      this.isEdit = edit
    },
    formateBudget() {
      this.budget = formatAmount(this.info.budget)
    },
    handleEditClick() {
      this.updateEditStatus(true)
      this.budget = String(this.info.budget).replace(/,/g, '')
    },
    handleCancelEditClick() {
      this.formateBudget()
      this.updateEditStatus(false)
    },
    async handleUpdateBudgetClick() {
      if (this.budget === 0 || this.budget === '0') {
        showMessage('活动预算不能为0', 'error')
        return
      }

      if (!this.budget) {
        showMessage('请输入活动预算', 'error')
        return
      }

      if (Number(this.budget) < this.info.discountRule.discount) {
        showMessage('活动预算不能小于可减金额', 'error')
        return
      }

      this.isEditLoading = true
      const [err] = await marketingClient.couponsWxUpdateCoupons({
        body: {
          couponsId: this.couponsId,
          budget: this.budget
        }
      })

      this.isEditLoading = false
      if (err) {
        this.isLoading = false
        return handleError(err)
      }

      this.budget = formatAmount(this.budget)
      this.updateEditStatus(false)
      showMessage('操作成功')
      this.loadDetail()
    },
    getDiscountRuleName(type) {
      return getOptionsItemLabel(discountRuleOptions, String(type)) || '-'
    },
    getInterceptRuleName(interceptRule) {
      if (!interceptRule || !interceptRule.length) return '-'
      const names = interceptRule.map(item => {
        return getOptionsItemLabel(interceptRuleOptions, item)
      })
      return names.join('、')
    },
    formatDate(value) {
      if (!value) return '-'
      return formatDateTime('yyyy-MM-dd', value)
    },
    redirectToViewDistributionDetails() {
      this.$router.push(
        `/discount/wechatDiscountSentDetail?couponsId=${this.couponsId}`
      )
    },
    async loadDetail() {
      this.isLoading = true
      const [err, result] = await marketingClient.couponsWxGetCoupons({
        body: {
          id: this.couponsId
        }
      })
      if (err) {
        this.isLoading = false
        return handleError(err)
      }
      await delay(100)
      this.isLoading = false
      Object.assign(this.info, result.data)
      this.formateBudget()
    }
  }
}
</script>

<style scoped>
.table {
  border: 1px solid #e4e7edff;
  border-bottom: 0;
  border-right: 0;
}
.row {
  grid-auto-flow: row dense;
  display: grid;
  grid-template-columns: 195px repeat(5, 1fr);
  border-bottom: 1px solid #e4e7edff;
}
.span-5 {
  grid-column-end: span 5;
}
.span-3 {
  grid-column-end: span 3;
}

.cell {
  padding: 8px 24px;
  padding-right: 10px;
  box-sizing: border-box;
  border-right: 1px solid #e4e7edff;
  min-height: 46px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
}
.title {
  background-color: #f7f9fc;
  color: #1e2228ff;
  line-height: 22px;
}
.custom-rules .right {
  color: #1e2228ff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
}
.custom-rules .right .box {
  border-bottom: 1px solid #e4e7edff;
  line-height: 46px;
  padding-left: 24px;
  border-right: 1px solid #e4e7edff;
}
.custom-rules .right .box:last-child {
  border-bottom: 0;
}
pre {
  white-space: pre-wrap;
  line-height: 18px;
}
</style>
