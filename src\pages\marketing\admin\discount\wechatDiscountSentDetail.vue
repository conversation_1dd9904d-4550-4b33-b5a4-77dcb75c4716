<template>
  <o-pc-list
    ref="pc-list"
    :title="$route.meta.title"
    :formJson="searchFormJson"
    :requestFn="getListApi"
    :titleBack="true"
    labelWidth="116px"
    :deleteNullApiParams="true"
    :tableHeaderActionButtons="tableHeaderActionButtons"
    :tableHeader="tableHeader"
    :beforeSearch="beforeSearch"
  />
</template>
<script>
import AutoEllipsisTooltip from 'kit/components/marketing/admin/autoEllipsisTooltip.vue'
import { activitySenDetailStatusOptions } from '../activity/wechatActivityOptions'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { exportExcel } from 'kit/helpers/exportExcel'

const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.couponsWxQueryCouponsAwardRecord({
    body: params
  })
  if (err) return handleError(err)
  return result.data
}

const loadCouponsWxListActivity = async id => {
  const [err, result] = await marketingClient.couponsWxListActivity({
    body: {
      id
    }
  })
  if (err) return handleError(err)
  const options = result.data.map(item => {
    item.label = item.name
    item.value = item.id
    return item
  })
  return options
}

export default {
  data() {
    return {
      getListApi: loadList,
      searchFormJson: [
        // {
        //   type: 'input',
        //   item: {
        //     prop: 'openId',
        //     label: '领取人微信openID',
        //     placeholder: '请输入领取人微信openID'
        //   }
        // },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            prop: 'createTime',
            rangeSeparator: '~',
            label: '发放时间',
            editable: false,
            validateTrigger: 'blur',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd HH:mm:ss'
          }
        },
        // {
        //   type: 'input',
        //   item: {
        //     prop: 'stockId',
        //     label: '卡券批次号',
        //     placeholder: '请输入卡券批次号'
        //   }
        // },
        {
          type: 'select',
          item: {
            prop: 'activityId',
            label: '发放活动',
            placeholder: '请选择发放活动',
            labelMaxLength: '20',
            options: () => loadCouponsWxListActivity(this.couponsId)
          }
        }
      ],
      isFirstLoad: true,
      tableHeader: [
        {
          prop: 'id',
          label: '发放唯一id',
          width: 80,
          fixed: true
        },
        {
          prop: 'sendTime',
          label: '发放时间',
          minWidth: 240
        },
        {
          prop: 'activity.name',
          label: '发放活动',
          minWidth: 120
        },
        {
          prop: 'activity.id',
          label: '发放活动ID',
          width: 120
        },
        {
          prop: 'openId',
          label: '领取人微信openid',
          width: 240
        },
        {
          prop: 'stockId',
          label: '卡券批次号',
          minWidth: 120
        },
        {
          prop: '发放状态',
          label: '发放状态',
          minWidth: 120,
          formatter: row => {
            return (
              getOptionsItemLabel(activitySenDetailStatusOptions, row.status) ||
              '-'
            )
          }
        },
        {
          prop: 'failMessage',
          label: '发放结果反馈',
          minWidth: 220
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '导出',
          props: {
            loading: false,
            style: {
              'justify-content': 'center'
            }
          },
          click: async ({ props }) => {
            props.loading = true
            const body = this.oPcList.oTable.getRequestParams()
            const result =
              await marketingClient.couponsWxExportCouponsAwardRecord({
                body
              })
            await exportExcel(result)
            props.loading = false
          }
        }
      ]
    }
  },
  computed: {
    oPcList() {
      return this.$refs['pc-list']
    },
    couponsId() {
      return this.$route.query.couponsId
    }
  },
  activated() {
    if (!this.isFirstLoad) this.reload()
  },
  methods: {
    // 刷新页面
    async reload() {
      this.oPcList.reload()
    },

    // 搜索之前对参数处理
    async beforeSearch(fData) {
      fData.couponsId = this.couponsId
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')
      return fData
    }
  }
}
</script>
