<template>
  <o-pc-list
    ref="pc-list"
    :title="$route.meta.title"
    :formJson="searchFormJson"
    :requestFn="getListApi"
    labelWidth="70px"
    :deleteNullApiParams="true"
    :actionButtons="actionButtons"
    :tableHeaderActionButtons="tableHeaderActionButtons"
    :tableHeader="tableHeader"
    :beforeSearch="beforeSearch"
  />
</template>
<script>
import { activityDiscountStatusOptions } from './wechatDiscountsOptions'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { showMessage } from '../../../../helpers/showMessage'
import { isSafariBrowser } from 'kit/helpers/isSafariBrowser'
import handleError from 'kit/helpers/handleError'
import formatDateTime from 'kit/formatters/dateTime'
import { oConfirm } from 'kit/components/marketing/admin/messageBox'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.couponsWxQueryCoupons({
    body: params
  })
  if (err) return handleError(err)
  result.data.list.forEach(item => {
    item.remark = item.remark.replace(/\n/g, '')
  })
  return result.data
}

export default {
  data() {
    return {
      getListApi: loadList,
      searchFormJson: [
        {
          type: 'input',
          item: {
            prop: 'name',
            label: '卡券名称',
            placeholder: '请输入卡券名称'
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            prop: 'create',
            rangeSeparator: '~',
            label: '创建时间',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd 00:00:00'
          }
        },
        {
          type: 'select',
          formItem: {
            prop: 'status',
            label: '卡券状态',
            placeholder: '请选择卡券状态',
            options: activityDiscountStatusOptions
          }
        }
      ],
      isFirstLoad: true,
      tableHeader: [
        {
          prop: 'name',
          label: '卡券名称',
          width: 150,
          fixed: true,
          click: row =>
            this.$router.push(`/discount/wechatDiscount/${row.couponsId}`)
        },
        {
          prop: 'createTime',
          label: '创建时间',
          type: 'DATE_TIME'
        },
        {
          prop: 'availableTime',
          label: '投放时间',
          width: 200,
          formatter: row => {
            const yyyMMdd = date => formatDateTime('yyyy-MM-dd', date)
            return `${yyyMMdd(row.availableBeginTime)} ~ ${yyyMMdd(
              row.availableEndTime
            )}`
          }
        },
        {
          prop: 'remark',
          label: '使用规则',
          showOverflowTooltip: isSafariBrowser(),
          minWidth: 120,
          formatter: row =>
            row.remark
              ? `<div style="
            display: -webkit-box;
            overflow: hidden;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis; 
          ">${row.remark}</div>`
              : '-'
        },
        {
          prop: 'stockMerchantId',
          label: '创建批次的商户号',
          width: 130
        },
        {
          prop: 'stockId',
          label: '卡券批次号',
          width: 180
        },
        {
          prop: '状态',
          label: '状态',
          width: 80,
          formatter: row => {
            return (
              getOptionsItemLabel(activityDiscountStatusOptions, row.status) ||
              ''
            )
          }
        }
      ],
      actionButtons: [
        {
          label: '发放明细',
          click: row => {
            this.$router.push(
              `/discount/wechatDiscountSentDetail?couponsId=${row.couponsId}`
            )
          }
        },
        {
          label: '删除',
          click: row => {
            oConfirm(
              '删除后将无法找回此数据，请谨慎操作',
              '删除此微信立减金卡券?',
              {
                confirm: async () => {
                  const [err] = await marketingClient.couponsWxDeleteCoupons({
                    body: {
                      id: row.couponsId
                    }
                  })
                  if (err) return handleError(err)
                  this.tableReload()
                  showMessage('操作成功')
                }
              }
            )
          }
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '新建微信立减金',
          click: () => {
            this.$router.push('/discount/wechatDiscountsNew')
          }
        }
      ]
    }
  },
  computed: {
    oTable() {
      return this.$refs['pc-list']
    }
  },
  activated() {
    if (!this.isFirstLoad) this.reload()
  },
  methods: {
    // 刷新页面
    async tableReload() {
      this.oTable.reload()
    },

    // 搜索之前对参数处理
    async beforeSearch(fData) {
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')
      return fData
    }
  }
}
</script>
