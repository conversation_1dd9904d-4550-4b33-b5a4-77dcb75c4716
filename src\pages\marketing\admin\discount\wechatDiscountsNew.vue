<template>
  <Container :back="true" :hideFootButton="true">
    <div class="wrap" v-loading="isLoading">
      <TopProgressBar
        style="margin: 0 auto 24px"
        :steps="steps"
        v-model="activeStep"
      />
      <div style="width: 560px; margin: 0 auto">
        <WechatDiscountActivityInfo
          ref="WechatDiscountActivityInfo"
          :formData="formData"
          @next="handleNextClick"
          v-show="activeStep === 'WechatDiscountActivityInfo'"
        />
        <RiskControlConfiguration
          ref="RiskControlConfiguration"
          @goBack="handleGoBackClick"
          :formData="formData"
          @confirm="confirm"
          v-show="activeStep === 'RiskControlConfiguration'"
        />
        <ActivityCreatedCompleted
          ref="Completed"
          v-show="activeStep === 'Completed'"
        />
      </div>
    </div>
  </Container>
</template>

<script>
import ActivityCreatedCompleted from 'kit/components/marketing/admin/activityCreatedCompleted.vue'
import WechatDiscountActivityInfo from './wechatDiscountsNew/wechatDiscountActivityInfo.vue'
import RiskControlConfiguration from './wechatDiscountsNew/riskControlConfiguration.vue'
import TopProgressBar from 'kit/components/marketing/admin/topProgressBar.vue'
import Container from 'kit/components/marketing/admin/container.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import handleError from 'kit/helpers/handleError'
import deepClone from 'kit/helpers/deepClone'

const marketingClient = makeMarketingClient()

const steps = [
  {
    label: '微信立减金活动信息',
    value: 'WechatDiscountActivityInfo'
  },
  {
    label: '配置风险控制',
    value: 'RiskControlConfiguration'
  },
  {
    label: '完成',
    value: 'Completed'
  }
]

const formatRequestParams = params => {
  const cloneParams = deepClone(params)
  cloneParams.availableEndTime = cloneParams.availableEndTime.replace(
    '00:00:00',
    '23:59:59'
  )
  return cloneParams
}

export default {
  components: {
    WechatDiscountActivityInfo,
    RiskControlConfiguration,
    ActivityCreatedCompleted,
    TopProgressBar,
    Container
  },
  data() {
    return {
      steps,
      activeStep: 'WechatDiscountActivityInfo',
      isLoading: false,
      formData: {
        name: '',
        stockId: '',
        remark: '',
        availableTime: [],
        availableBeginTime: '',
        availableEndTime: '',
        budget: '',
        discountRule: {
          type: '1',
          amount: '',
          discount: ''
        },
        bankName: '',
        bankCardType: '',
        bindCardBin: false,
        interceptRule: ['PERSON']
      },
      createCouponsId: ''
    }
  },
  computed: {
    isEdit() {
      return this.couponsId && this.activeStep === 'RiskControlConfiguration'
    },
    couponsId() {
      return this.$route.params.id
    }
  },
  methods: {
    setActiveStep(status) {
      this.activeStep = status
    },
    handleNextClick(formData) {
      Object.assign(this.formData, formData)
      this.setActiveStep('RiskControlConfiguration')
    },
    handleGoBackClick() {
      this.setActiveStep('WechatDiscountActivityInfo')
    },
    async confirm(formData, showLoading) {
      Object.assign(this.formData, formData)

      showLoading()

      const params = formatRequestParams(this.formData)

      const [err] = await marketingClient.couponsWxSaveCoupons({
        body: params
      })
      showLoading(false)
      if (err) {
        handleError(err)
        return Promise.reject(err)
      }
      this.setActiveStep('Completed')
    }
  }
}
</script>

<style scoped>
.wrap {
  padding: 24px 0;
}
</style>
