<template>
  <div>
    <Form ref="form" :model="form" :rules="formRules" label-position="top">
      <el-form-item prop="interceptRule" label="微信规则 ">
        <CheckboxGroup
          v-model="form.interceptRule"
          :options="interceptRuleOptions"
        />
        <p class="interceptRuleDes">小号拦截会导致部分客诉，可按需选择</p>
      </el-form-item>
      <el-button type="plain" @click="handleGoBackClick">上一步</el-button>
      <el-button type="primary" :loading="isLoading" @click="handleNextClick"
        >提交</el-button
      >
    </Form>
  </div>
</template>
<script>
import CheckboxGroup from 'kit/components/marketing/admin/checkboxGroup.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import { interceptRuleOptions } from '../wechatDiscountsOptions'
import deepClone from 'kit/helpers/deepClone'

export default {
  components: {
    CheckboxGroup,
    Form
  },
  props: {
    formData: {
      type: Object,
      default: () => {
        return {
          interceptRule: []
        }
      }
    }
  },
  data() {
    return {
      form: {
        interceptRule: [...this.formData.interceptRule]
      },
      formRules: {
        interceptRule: [
          // { required: true, message: '请选择微信规则', trigger: 'change' }
        ]
      },
      interceptRuleOptions,
      isLoading: false
    }
  },
  methods: {
    validate() {
      return this.$refs.form.validate()
    },
    handleGoBackClick() {
      this.$emit('goBack')
    },
    async handleNextClick() {
      const showLoading = (show = true) => (this.isLoading = show)
      const error = await this.validate()
      if (error) return
      this.$emit('confirm', deepClone(this.form), showLoading)
    }
  }
}
</script>
<style scoped>
.interceptRuleDes {
  color: #828b9bff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
}
</style>
