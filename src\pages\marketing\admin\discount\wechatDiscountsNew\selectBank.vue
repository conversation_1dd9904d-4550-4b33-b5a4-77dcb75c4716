<template>
  <el-select
    ref="searchSelect"
    @input="onInput"
    :value="value"
    filterable
    remote
    @blur="onBlur"
    clearable
    reserve-keyword
    placeholder="请输入活动银行"
    :remote-method="remoteMethod"
    :loading="loading"
  >
    <template slot="empty">
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 10px;
          color: #999;
        "
      >
        <img
          width="100%"
          src="kit/assets/images/marketing/admin/no_data.svg"
          alt=""
        />
        <div>暂无数据</div>
      </div>
    </template>
    <el-option
      v-for="item in bankOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
    </el-option>
  </el-select>
</template>
<script>
import { delay } from 'kit/helpers/delay'
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      bankOptions: [],
      loading: false,
      selectValue: this.value
    }
  },
  watch: {
    selectValue: {
      handler(newVal, oldVal) {
        this.$emit('input', newVal)
      },
      deep: true
    }
  },
  methods: {
    async onBlur(e) {
      await delay(200)
      let str = e.target.value
      if (str.length > 50) {
        this.$message.error('输入银行名称长度限制为50！')
        this.selectValue = str.substr(0, 50)
        return
      }
      if (!this.value) this.remoteMethod('')
    },
    remoteMethod(query) {
      console.log({ query })
      if (query !== '') {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.selectValue = query
          this.bankOptions = this.options.filter(item => {
            return item.name.includes(query)
          })
        }, 200)
      } else {
        this.bankOptions = []
      }
    },
    onInput(value) {
      this.$emit('input', value)
    }
  }
}
</script>
<style scoped></style>
