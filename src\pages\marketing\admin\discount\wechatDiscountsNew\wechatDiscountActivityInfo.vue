<template>
  <div>
    <Form
      ref="form"
      class="discount-activity-form"
      :model="form"
      :rules="formRules"
      label-position="top"
    >
      <el-form-item label="卡券名称" prop="name" style="flex: 1">
        <Input
          v-model="form.name"
          :trim="true"
          placeholder="请输入卡券名称"
          maxlength="10"
        />
      </el-form-item>
      <el-form-item label="卡券批次号" prop="stockId" style="flex: 1">
        <Input
          v-model.trim="form.stockId"
          :trimAll="true"
          placeholder="请联系供应商获取"
          maxlength="20"
        />
      </el-form-item>
      <!-- <el-form-item label="卡券制券方?" prop="stockId" style="flex: 1">
        <Input
          v-model="form.stockId"
          :trim="true"
          placeholder="请联系供应商获取"
          maxlength="20"
        />
      </el-form-item> -->
      <el-form-item label="投放时间" prop="availableTime" style="flex: 1">
        <DateTimePicker
          v-model="form.availableTime"
          :picker-options="pickerOptions"
          :startTime.sync="form.availableBeginTime"
          :endTime.sync="form.availableEndTime"
          valueFormat="yyyy-MM-dd HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>

      <el-row type="flex" align="middle" class="bank">
        <el-form-item label="活动银行" prop="bankName">
          <SelectBank v-model="form.bankName" :options="bankOptions" />
        </el-form-item>
        <span
          style="margin: 0 8px; position: relative; top: 4px; color: #cad0dbff"
          >-</span
        >
        <el-form-item prop="bankCardType" label=" " class="no-require-icon">
          <Select
            clearable
            :options="cardTypeOptions"
            v-model="form.bankCardType"
            placeholder="请选择卡类型"
          />
        </el-form-item>

        <span
          style="margin: 0 8px; position: relative; top: 4px; color: #cad0dbff"
          >-</span
        >
        <el-form-item prop="bindCardBin" label=" " class="no-require-icon">
          <Select
            clearable
            placeholder="请选择指不指定bin"
            :options="isSpecifiedCardBinOption"
            v-model="form.bindCardBin"
          />
        </el-form-item>
      </el-row>

      <el-form-item prop="discountRule.type" label="优惠规则 ">
        <Radio
          v-model="form.discountRule.type"
          :options="discountRuleOptions"
          @change="onDiscountTypeChange"
        />
      </el-form-item>

      <el-row type="flex" class="rules" align="middle" v-if="showRuleInput">
        <span class="text">单张优惠满</span>
        <el-form-item prop="discountRule.amount" style="flex: 1">
          <Input
            v-model="form.discountRule.amount"
            valueType="decimals_2"
            :autoRetainTwoDecimalPlaces="true"
            :trim="true"
            :allowZero="true"
            placeholder="请输入门槛"
            maxlength="12"
          />
        </el-form-item>
        <span class="text">元可减</span>
        <el-form-item prop="discountRule.discount">
          <Input
            v-model="form.discountRule.discount"
            valueType="decimals_2"
            @blur="validateFieldDiscountRuleAmount"
            :autoRetainTwoDecimalPlaces="true"
            :trim="true"
            :allowZero="true"
            placeholder="请输入面额"
            maxlength="12"
          />
        </el-form-item>
        <span class="unit">元</span>
      </el-row>

      <el-form-item prop="budget" label="活动预算">
        <el-row type="flex">
          <Input
            v-model="form.budget"
            valueType="decimals_2"
            placeholder="请输入活动预算"
            :allowZero="true"
            @blur="onBlurBudget"
            maxlength="12"
          />
          <span style="margin-left: 6px">元</span>
        </el-row>
      </el-form-item>

      <el-form-item label="使用规则" prop="remark">
        <Textarea
          maxlength="1024"
          :trim="true"
          v-model="form.remark"
          placeholder="请输入代金券使用规则，不超过1024字"
        />
      </el-form-item>
      <el-button type="primary" @click="handleNextClick">下一步</el-button>
    </Form>
  </div>
</template>
<script>
import {
  cardTypeOptions,
  isSpecifiedCardBinOption,
  discountRuleOptions
} from '../wechatDiscountsOptions'
import DateTimePicker from 'kit/components/marketing/admin/dateTimePicker.vue'
import Textarea from 'kit/components/marketing/admin/textarea.vue'
import Select from 'kit/components/marketing/admin/select.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import Radio from 'kit/components/marketing/admin/radio.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import SelectBank from './selectBank.vue'

import makeMarketingClient from 'kit/services/marketing/makeClient'
import getFormRules from './wechatDiscountActivityInfoFormRules'
import deepClone from 'kit/helpers/deepClone'
import { FIXED_AMOUNT } from '../../constants'

import { roundToTwoDecimals } from 'kit/helpers/roundToTwoDecimals'

const marketingClient = makeMarketingClient()

function removeDuplicates(data) {
  const uniqueData = data.reduce((accumulator, current) => {
    const existingItem = accumulator.find(
      item => item.id === current.id && item.name === current.name
    )
    if (!existingItem) {
      accumulator.push(current)
    }
    return accumulator
  }, [])
  return uniqueData
}

export default {
  components: {
    DateTimePicker,
    SelectBank,
    Textarea,
    Select,
    Input,
    Radio,
    Form
  },
  props: {
    formData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: deepClone(this.formData),
      formRules: getFormRules(this),
      bankOptions: [],
      cardTypeOptions,
      discountRuleOptions,
      isSpecifiedCardBinOption,
      pickerOptions: {
        disabledDate(time) {
          const today = new Date()
          today.setHours(0, 0, 0, 0)
          return time.getTime() < today.getTime()
        }
      }
    }
  },
  computed: {
    showRuleInput() {
      return this.form.discountRule.type === FIXED_AMOUNT
    }
  },
  methods: {
    validateFieldDiscountRuleAmount() {
      this.$refs.form.validateField('discountRule.amount')
    },
    onDiscountTypeChange() {
      this.validateFieldDiscountRuleAmount()
      this.$refs.form.validateField('budget')
    },
    async loadOptions() {
      const [err, { data }] = await marketingClient.dictBankList({
        method: 'GET'
      })
      if (err) {
        handleError(err)
        return
      }
      this.bankOptions = data.map(item => {
        item.label = item.name
        item.value = item.id
        return item
      })
      this.bankOptions = removeDuplicates(this.bankOptions)
    },
    validate() {
      return this.$refs.form.validate()
    },
    async handleNextClick() {
      const error = await this.validate()
      if (error) return
      this.$emit('next', deepClone(this.form))
    },

    async onBlurBudget() {
      await this.$nextTick()
      this.form.budget = roundToTwoDecimals(this.form.budget)
    }
  },
  created() {
    this.loadOptions()
  }
}
</script>
<style scoped>
.discount-activity-form {
  color: #1e2228ff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
}
.discount-activity-form .rules .text {
  margin: 0 8px;
  margin-bottom: 20px;
}
.discount-activity-form .rules .unit {
  margin-top: -17px;
  margin-left: 6px;
}
.discount-activity-form .no-require-icon ::v-deep .el-form-item__label::before {
  display: none;
}
</style>
