import { NO_SPECIFIC_AMOUNT } from '../../constants'
import { isAmountZero } from 'kit/helpers/isAmountZero'

export default function (vm) {
  return {
    name: [{ required: true, message: '请输入卡券名称', trigger: 'blur' }],
    stockId: [{ required: true, message: '请输入卡券批次号', trigger: 'blur' }],
    availableTime: [
      { required: true, message: '请选择投放时间', trigger: 'change' }
    ],
    // bankName: [{ required: true, message: '请选择银行', trigger: 'change' }],
    bankCardType: [
      // { required: true, message: '请选择卡类型', trigger: 'change' }
    ],
    // bindCardBin: [{ required: true, message: '请选择', trigger: 'change' }],
    discountRule: {
      type: [{ required: true, message: '请选择优惠规则', trigger: 'blur' }],
      amount: [
        { required: true, message: '请输入门槛', trigger: 'blur' },
        {
          validator: (_rules, value, callback) => {
            if (isAmountZero(value)) return callback('金额不能为0')
            if (!vm.form.discountRule.discount) return callback()
            if (!value) return callback()
            if (Number(value) < Number(vm.form.discountRule.discount)) {
              callback('输入门槛不能小于面额')
              return
            }
            callback()
          },
          trigger: 'blur'
        }
      ],
      discount: [
        { required: true, message: '请输入面额', trigger: 'blur' },
        {
          validator: (_rules, value, callback) => {
            if (isAmountZero(value)) return callback('金额不能为0')
            callback()
          },
          trigger: 'blur'
        }
      ]
    },
    budget: [
      { required: true, message: '请输入活动预算', trigger: 'blur' },
      {
        validator: (_rules, value, callback) => {
          if (isAmountZero(value)) return callback('金额不能为0')
          if (vm.form.discountRule.type === NO_SPECIFIC_AMOUNT) callback()
          if (!vm.form.discountRule.discount) return callback()
          if (!value) return callback()
          if (Number(value) < Number(vm.form.discountRule.discount)) {
            callback('活动预算不能小于面额')
            return
          }
          callback()
        },
        trigger: 'blur'
      }
    ]
    // remark: [{ required: true, message: '请输入使用规则', trigger: 'blur' }]
  }
}
