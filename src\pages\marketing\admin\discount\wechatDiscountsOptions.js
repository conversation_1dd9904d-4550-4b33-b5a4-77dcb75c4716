import { FIXED_AMOUNT, NO_SPECIFIC_AMOUNT } from '../constants'

export const isSpecifiedCardBinOption = [
  { label: '不指定卡BIN', value: false },
  { label: '指定卡BIN', value: true }
]

export const cardTypeOptions = [
  { label: '储蓄卡', value: '1' },
  { label: '信用卡', value: '2' }
]

export const discountRuleOptions = [
  { label: '固定面额', value: FIXED_AMOUNT }
  // { label: '暂不指定面额，以API形式发券时指定', value: NO_SPECIFIC_AMOUNT }
]

export const interceptRuleOptions = [
  { label: '自然人防刷', value: 'PERSON' },
  { label: '小号拦截', value: 'ACCOUNT' }
]

export const isSendSmsOptions = [
  { label: '发放', value: true },
  { label: '无需发放', value: false }
]

export const activityDiscountStatusOptions = [
  { label: '待投放', value: '1' },
  { label: '投放中', value: '2' },
  { label: '已过期', value: '3' }
]
