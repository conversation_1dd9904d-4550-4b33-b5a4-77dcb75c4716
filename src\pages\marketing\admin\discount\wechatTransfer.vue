<template>
  <Container :back="true" :title="$route.meta.title">
    <div slot="header-right" class="title-button">
      <el-button type="primary" size="small" @click="handleEditClick"
        >修改</el-button
      >
    </div>
    <main style="padding: 24px; min-height: 60vh" v-loading="isLoading">
      <div v-if="info.id">
        <el-button
          type="primary"
          style="margin-bottom: 16px"
          @click="redirectToViewDistributionDetails"
          >查看发放明细</el-button
        >
        <div class="table">
          <div class="row">
            <div class="cell title">批次名称</div>
            <div class="cell" style="width: 240px">
              <pre>{{ info.name }}</pre>
            </div>
            <div class="cell title">发放类型</div>
            <div class="cell span-3">代发模式</div>
          </div>
          <div class="row">
            <div class="cell title">投放时间</div>
            <span class="cell" style="width: 240px"
              >{{ formatDate(info.availableBeginTime) }} ～
              {{ formatDate(info.availableEndTime) }}</span
            >
            <div class="cell title">金额</div>
            <div class="cell span-3">{{ amount }}元</div>
          </div>
          <div class="row">
            <div class="cell title">活动预算</div>
            <div class="cell span-5">
              <span>{{ budget }}元</span>
            </div>
          </div>
          <div class="row">
            <div class="cell title">是否需要姓名校验</div>
            <span class="cell span-5">{{ info.checkName ? '是' : '否' }}</span>
          </div>
          <div class="row">
            <div class="cell title">使用规则</div>
            <pre class="cell span-5">{{ info.remark || '-' }}</pre>
          </div>
        </div>
      </div>
    </main>
  </Container>
</template>

<script>
import { interceptRuleOptions } from './wechatDiscountsOptions'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import Container from 'kit/components/marketing/admin/container.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import formatDateTime from 'kit/formatters/dateTime'
import handleError from 'kit/helpers/handleError'
import formatAmount from 'kit/formatters/formatAmount'
import { delay } from 'kit/helpers/delay'

const marketingClient = makeMarketingClient()

export default {
  components: {
    Container
  },
  data() {
    return {
      isLoading: false,
      info: {
        id: '',
        name: '',
        createTime: '',
        remark: '',
        availableBeginTime: '',
        availableEndTime: '',
        stockId: '',
        budget: '',
        discountRule: {
          type: '',
          amount: '',
          discount: ''
        },
        bankName: '',
        bankCardType: '',
        bindCardBin: '',
        interceptRule: [],
        status: ''
      }
    }
  },
  computed: {
    id() {
      return this.$route.params.id
    },
    amount() {
      if (this.info.amountFixed) {
        return formatAmount(this.info.fixedAmount)
      }
      return '不定额'
    },
    budget() {
      return formatAmount(this.info.budget)
    }
  },
  created() {
    this.loadDetail()
  },
  methods: {
    formatAmount,
    async handleEditClick() {
      this.$router.push(`/discount/wechatTransferNew/${this.id}`)
    },
    formatDate(value) {
      if (!value) return '-'
      return formatDateTime('yyyy-MM-dd', value)
    },
    redirectToViewDistributionDetails() {
      this.$router.push(
        `/discount/wechatTransferSentDetail?couponsId=${this.id}`
      )
    },
    async loadDetail() {
      this.isLoading = true
      const [err, result] = await marketingClient.transferWxDetail({
        body: {
          id: this.id
        }
      })
      this.isLoading = false
      if (err) {
        return handleError(err)
      }
      await delay(100)
      Object.assign(this.info, result.data)
    }
  }
}
</script>

<style scoped>
.table {
  border: 1px solid #e4e7edff;
  border-bottom: 0;
  border-right: 0;
}
.row {
  grid-auto-flow: row dense;
  display: grid;
  grid-template-columns: 195px repeat(5, 1fr);
  border-bottom: 1px solid #e4e7edff;
}
.span-5 {
  grid-column-end: span 5;
}
.span-3 {
  grid-column-end: span 3;
}

.cell {
  padding: 8px 24px;
  padding-right: 10px;
  box-sizing: border-box;
  border-right: 1px solid #e4e7edff;
  min-height: 46px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
}
.title {
  background-color: #f7f9fc;
  color: #1e2228ff;
  line-height: 22px;
}
.custom-rules .right {
  color: #1e2228ff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
}
.custom-rules .right .box {
  border-bottom: 1px solid #e4e7edff;
  line-height: 46px;
  padding-left: 24px;
  border-right: 1px solid #e4e7edff;
}
.custom-rules .right .box:last-child {
  border-bottom: 0;
}
pre {
  white-space: pre-wrap;
  line-height: 18px;
}
.title-button {
  flex: 1;
  display: flex;
  justify-content: end;
}
</style>
