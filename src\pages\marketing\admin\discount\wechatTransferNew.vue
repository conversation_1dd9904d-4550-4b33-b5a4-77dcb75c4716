<template>
  <Container
    :back="true"
    @confirm="confirm"
    :hideFootButton="isCreateSuccess"
    confirmButtonText="提交"
    @cancel="cancel"
  >
    <div class="wrap" v-loading="isLoading">
      <div style="width: 560px; margin: 0 auto" v-if="!isCreateSuccess">
        <Form
          ref="form"
          :model="formData"
          :rules="formRules"
          label-position="top"
        >
          <el-form-item label="批次名称" prop="name">
            <Input
              v-model="formData.name"
              :trim="true"
              :disabled="isFormItemDisabled"
              placeholder="请输入批次名称"
              maxlength="20"
            />
          </el-form-item>

          <el-form-item label="发放类型" prop="sendType">
            <Radio
              v-model="formData.sendType"
              :disabled="isFormItemDisabled"
              :options="sendTypeOptions"
            />
          </el-form-item>

          <el-row type="flex">
            <el-form-item
              label="投放时间"
              prop="availableBeginTime"
              style="flex: 1"
            >
              <DatePicker
                style="width: 100%"
                :picker-options="startPickerOptions"
                @change="onChangeBeginTime"
                placeholder="请选择开始时间"
                :disabled="isFormItemDisabled"
                v-model="formData.availableBeginTime"
              />
            </el-form-item>
            <span style="margin: 0 10px; position: relative; top: 42px"
              >至</span
            >
            <el-form-item
              label=" "
              class="no-require-symbol"
              prop="availableEndTime"
              style="flex: 1"
            >
              <DatePicker
                style="width: 100%"
                :picker-options="endPickerOptions"
                @change="onChangeBeginTime"
                valueFormat="yyyy-MM-dd 23:59:59"
                placeholder="请选择结束时间"
                v-model="formData.availableEndTime"
              />
            </el-form-item>
          </el-row>

          <el-form-item label="金额" prop="amountFixed">
            <Radio
              v-model="formData.amountFixed"
              :options="amountOptions"
              @change="validateFieldAmount"
              :disabled="isFormItemDisabled"
            />
          </el-form-item>

          <el-form-item label="" prop="fixedAmount" v-if="isFixedAmount">
            <el-row type="flex">
              <Input
                v-model="formData.fixedAmount"
                valueType="decimals_2"
                :disabled="isFormItemDisabled"
                placeholder="请输入固定金额"
                :autoRetainTwoDecimalPlaces="true"
                @blur="validateFieldAmount"
                :allowZero="true"
                maxlength="12"
              />
              <span style="margin-left: 6px">元</span>
            </el-row>
          </el-form-item>

          <!-- 随机金额 -->
          <RandomAmountFields
            :disabled="isFormItemDisabled"
            v-else
            @blur="validateFieldAmount"
            v-model="formData"
          />

          <el-form-item prop="budget" label="活动预算">
            <el-row type="flex">
              <Input
                v-model="formData.budget"
                :autoRetainTwoDecimalPlaces="true"
                valueType="decimals_2"
                placeholder="请先联系供应商获取"
                @blur="validateFieldAmount"
                :allowZero="true"
                maxlength="9"
                style="flex: 1"
              />
              <span style="margin-left: 6px">元</span>
              <span style="margin-left: 20px; color: red" v-if="isEdit"
                >已发放：{{ formatAmount(formData.sendAmount) }}元</span
              >
            </el-row>
          </el-form-item>

          <el-form-item label="是否需要姓名校验" prop="checkName">
            <el-tooltip
              content="为了您的活动安全，请尽量选择需要参与人姓名校验"
              placement="top-start"
            >
              <i class="olading-iconfont oi-wenhao" />
            </el-tooltip>
            <Radio
              v-model="formData.checkName"
              :options="checkNameVailOptions"
            />
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <Textarea
              maxlength="1024"
              :trim="true"
              v-model="formData.remark"
              placeholder="请输入备注，不超过1024字"
            />
          </el-form-item>
        </Form>
      </div>
      <ActivityCreatedCompleted v-show="isCreateSuccess" />
    </div>
  </Container>
</template>

<script>
import ActivityCreatedCompleted from 'kit/components/marketing/admin/activityCreatedCompleted.vue'
import DatePicker from 'kit/components/marketing/admin/datePicker.vue'
import RandomAmountFields from './wechatTransferNew/randomAmountFields.vue'
import Container from 'kit/components/marketing/admin/container.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import Textarea from 'kit/components/marketing/admin/textarea.vue'
import Select from 'kit/components/marketing/admin/select.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import Radio from 'kit/components/marketing/admin/radio.vue'
import { getFormRules } from './wechatTransferNewFormRules'
import Form from 'kit/components/marketing/admin/form.vue'
import formatAmount from 'kit/formatters/formatAmount'
import handleError from 'kit/helpers/handleError'
import deepClone from 'kit/helpers/deepClone'
import { delay } from 'kit/helpers/delay'

import {
  sendTypeOptions,
  checkNameVailOptions,
  amountOptions
} from './wechatTransferOptions'
import { showMessage } from 'kit/helpers/showMessage'
import { oConfirm } from 'kit/components/marketing/admin/messageBox'

const marketingClient = makeMarketingClient()

const formatRequestParams = params => {
  const cloneParams = deepClone(params)
  return cloneParams
}

const formatResponseParams = params => {
  const newParams = deepClone(params)
  newParams.sendType = 1
  return newParams
}

export default {
  components: {
    ActivityCreatedCompleted,
    RandomAmountFields,
    DatePicker,
    Container,
    Textarea,
    Select,
    Form,
    Input,
    Radio
  },
  data() {
    return {
      amountOptions,
      isLoading: false,
      formData: {
        name: '',
        availableBeginTime: '',
        availableEndTime: '',
        amountFixed: true,
        fixedAmount: '',
        randomMinAmount: '',
        randomMaxAmount: '',
        randomMidAmount: '',
        checkName: true,
        budget: '',
        sendAmount: 0,
        remark: '',
        sendType: 1
      },
      isCreateSuccess: false,
      formRules: getFormRules(this),
      sendTypeOptions,
      checkNameVailOptions,
      startPickerOptions: {
        disabledDate(time) {
          const today = new Date()
          today.setHours(0, 0, 0, 0)
          return time.getTime() < today.getTime()
        }
      },
      endPickerOptions: {
        disabledDate: time => {
          const today = new Date()
          today.setHours(0, 0, 0, 0)
          return time.getTime() < today.getTime()
        }
      }
    }
  },
  computed: {
    isEdit() {
      return this.couponsId
    },
    couponsId() {
      return this.$route.params.id
    },
    isFormItemDisabled() {
      return Boolean(this.isEdit)
    },
    isFixedAmount() {
      return this.formData.amountFixed
    }
  },
  mounted() {
    if (this.couponsId) this.loadDetail()
  },
  methods: {
    formatAmount,
    validateFieldAmount() {
      this.$refs.form.validateField('budget')
      this.$refs.form.validateField('fixedAmount')
      this.$refs.form.validateField('randomMinAmount')
      this.$refs.form.validateField('randomMidAmount')
      this.$refs.form.validateField('randomMaxAmount')
    },
    async loadDetail() {
      this.isLoading = true
      const [err, { data }] = await marketingClient.transferWxDetail({
        body: {
          id: this.couponsId
        }
      })
      await delay(200)
      this.isLoading = false
      if (err) return handleError(err)
      const result = formatResponseParams(data)
      this.formData = deepClone(result)
      this.endPickerOptions = {
        disabledDate: time => {
          const today = new Date(result.availableEndTime)
          today.setHours(0, 0, 0, 0)
          return time.getTime() < today.getTime()
        }
      }
    },
    onChangeBeginTime() {
      this.$refs.form.validateField('availableBeginTime')
      this.$refs.form.validateField('availableEndTime')
    },
    validate() {
      return this.$refs.form.validate()
    },
    cancel() {
      this.$router.back()
    },
    showAmountErrorDialog(err) {
      if (!err?.message.includes('元')) return false
      oConfirm(err.message, `提示`, {
        type: 'warning',
        showCancelButton:false
      })
      return true
    },
    async confirm() {
      const error = await this.validate()
      if (error) return

      const params = formatRequestParams(this.formData)

      if (this.isEdit) {
        params.id = this.couponsId
        const [err] = await marketingClient.transferWxUpdate({
          body: params
        })
        if (err) {
          if (this.showAmountErrorDialog(err)) return
          return handleError(err)
        }
        this.cancel()
        showMessage('修改成功')
        return
      }

      const [err] = await marketingClient.transferWxSave({
        body: params
      })

      if (err) {
        if (this.showAmountErrorDialog(err)) return
        return handleError(err)
      }

      this.isCreateSuccess = true
    }
  }
}
</script>

<style scoped>
.wrap {
  padding: 24px 0;
}
.oi-wenhao {
  position: absolute;
  top: -35px;
  left: 118px;
  cursor: pointer;
  color: #666;
}
.no-require-symbol ::v-deep .el-form-item__label::before {
  content: none !important;
}
</style>
