import { isAmountZero } from 'kit/helpers/isAmountZero'

const validateBaseAmount = (
  nextCallback,
  minMessage = '金额必须大于等于0.1',
  maxMessage = '金额最大为100'
) => {
  return function (_rules, value, callback) {
    if (Number(value) < 0.1) return callback(minMessage)
    if (parseFloat(value) > 100) return callback(maxMessage)
    if (nextCallback) return nextCallback(_rules, value, callback)
    callback()
  }
}

export function getFormRules(vm) {
  return {
    name: [{ required: true, message: '请输入批次名称', trigger: 'blur' }],
    sendType: [{ required: true, message: '请选择发放类型', trigger: 'blur' }],
    availableBeginTime: [
      { required: true, message: '请选择开始时间', trigger: 'change' },
      {
        validator: (_rules, value, callback) => {
          const availableBeginTime = value
          const availableEndTime = vm.formData.availableEndTime
          const availableBeginTimestamp = new Date(availableBeginTime).getTime()
          const availableEndTimestamp = new Date(availableEndTime).getTime()
          if (availableEndTimestamp < availableBeginTimestamp) {
            return callback('开始时间不能大于结束时间')
          }
          callback()
        }
      }
    ],
    availableEndTime: [
      { required: true, message: '请选择结束时间', trigger: 'change' },
      {
        validator: (_rules, value, callback) => {
          const availableBeginTime = vm.formData.availableBeginTime
          const availableBeginTimestamp = new Date(availableBeginTime).getTime()
          const availableEndTimestamp = new Date(value).getTime()
          if (availableEndTimestamp < availableBeginTimestamp) {
            return callback('结束时间不能小于开始时间')
          }
          callback()
        }
      }
    ],
    amountFixed: [
      { required: true, message: '请选择金额类型', trigger: 'change' }
    ],
    fixedAmount: [
      { required: true, message: '请输入固定金额', trigger: 'blur' },
      {
        validator: validateBaseAmount(
          null,
          '固定金额大于0',
          '固定金额最大可输入100'
        )
      }
    ],
    randomMinAmount: [
      { required: true, message: '请输入最小值', trigger: 'blur' },
      {
        validator: validateBaseAmount()
      }
    ],
    randomMidAmount: [
      { required: true, message: '请输入中间值', trigger: 'blur' },
      {
        validator: validateBaseAmount((_rules, value, callback) => {
          if (parseFloat(value) <= parseFloat(vm.formData.randomMinAmount)) {
            return callback('中间值要大于最小值')
          }
          callback()
        })
      },
      {
        validator: validateBaseAmount((_rules, value, callback) => {
          if (parseFloat(value) >= parseFloat(vm.formData.randomMaxAmount)) {
            return callback('中间值要小于最大值')
          }
          callback()
        })
      }
    ],
    randomMaxAmount: [
      { required: true, message: '请输入最大值', trigger: 'blur' },
      {
        validator: validateBaseAmount()
      }
    ],

    budget: [
      { required: true, message: '请输入活动预算', trigger: 'blur' },
      {
        validator: (_rules, value, callback) => {
          value = parseFloat(value)

          const fixedAmount = parseFloat(vm.formData.fixedAmount)
          const randomMaxAmount = parseFloat(vm.formData.randomMaxAmount)
          const amountFixed = vm.formData.amountFixed

          if (fixedAmount > value && amountFixed) {
            callback('活动预算需要大于等于固定金额')
          }

          if (randomMaxAmount > value && !amountFixed) {
            callback('活动预算需要大于等于金额最大值')
          }

          if (value > 999999999.0) {
            callback('活动预算最大为999999999.00')
          }

          if (isAmountZero(value)) {
            return callback('活动预算金额不能为0')
          }
          callback()
        },
        trigger: 'blur'
      }
    ]
  }
}
