<template>
  <div class="randomAmountFields">
    <el-form-item label="" prop="randomMinAmount">
      <el-row type="flex">
        <span style="margin-right: 10px">最小值</span>
        <Input
          v-model="value.randomMinAmount"
          valueType="decimals_2"
          :disabled="disabled"
          @blur="$emit('blur')"
          placeholder="请输入最小值"
          :autoRetainTwoDecimalPlaces="true"
          :allowZero="true"
          style="flex: 1"
          maxlength="12"
        />
        <span style="margin-left: 6px">元</span>
      </el-row>
    </el-form-item>
    <el-form-item label="" prop="randomMidAmount">
      <el-row type="flex">
        <span style="margin-right: 10px">中间值</span>
        <Input
          v-model="value.randomMidAmount"
          valueType="decimals_2"
          :disabled="disabled"
          placeholder="请输入中间值"
          :autoRetainTwoDecimalPlaces="true"
          @blur="$emit('blur')"
          style="flex: 1"
          :allowZero="true"
          maxlength="12"
        />
        <span style="margin-left: 6px">元</span>
      </el-row>
    </el-form-item>
    <el-form-item label="" prop="randomMaxAmount">
      <el-row type="flex">
        <span style="margin-right: 10px">最大值</span>
        <Input
          v-model="value.randomMaxAmount"
          :disabled="disabled"
          valueType="decimals_2"
          placeholder="请输入最大值"
          :autoRetainTwoDecimalPlaces="true"
          @blur="$emit('blur')"
          :allowZero="true"
          style="flex: 1"
          maxlength="12"
        />
        <span style="margin-left: 6px">元</span>
      </el-row>
    </el-form-item>
  </div>
</template>

<script>
import Input from 'kit/components/marketing/admin/input.vue'

export default {
  components: {
    Input
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => {
        return {
          randomMinAmount: '',
          randomMaxAmount: '',
          randomMidAmount: ''
        }
      }
    }
  }
}
</script>
<style scoped>
.randomAmountFields ::v-deep .el-form-item__error {
  left: 52px;
}
</style>