<template>
  <o-pc-list
    ref="pc-list"
    :title="$route.meta.title"
    :formJson="searchFormJson"
    :requestFn="getListApi"
    labelWidth="70px"
    :deleteNullApiParams="true"
    :actionButtons="actionButtons"
    :tableHeaderActionButtons="tableHeaderActionButtons"
    :tableHeader="tableHeader"
    :beforeSearch="beforeSearch"
  />
</template>
<script>
import { activityDiscountStatusOptions } from './wechatDiscountsOptions'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { showMessage } from '../../../../helpers/showMessage'
import handleError from 'kit/helpers/handleError'
import formatDateTime from 'kit/formatters/dateTime'
import formatAmount from 'kit/formatters/formatAmount'
import { oConfirm } from 'kit/components/marketing/admin/messageBox'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.transferWxQuery({
    body: params
  })
  if (err) return handleError(err)
  result.data.list.forEach(item => {
    item.remark = item.remark.replace(/\n/g, '')
  })
  return result.data
}

export default {
  data() {
    return {
      getListApi: loadList,
      searchFormJson: [
        {
          type: 'input',
          item: {
            prop: 'name',
            label: '批次名称',
            placeholder: '请输入批次名称'
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            prop: 'create',
            rangeSeparator: '~',
            label: '创建时间',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd 00:00:00'
          }
        },
        {
          type: 'select',
          formItem: {
            prop: 'status',
            label: '状态',
            placeholder: '请选择状态',
            options: activityDiscountStatusOptions
          }
        }
      ],
      isFirstLoad: true,
      tableHeader: [
        {
          prop: 'sn',
          label: '批次编码',
          width: 150,
          fixed: true
        },
        {
          prop: 'name',
          label: '批次名称',
          minWidth: 130,
          click: row => this.$router.push(`/discount/wechatTransfer/${row.id}`)
        },
        {
          prop: 'createTime',
          label: '创建时间',
          type: 'DATE_TIME'
        },
        {
          prop: 'amount',
          label: '金额',
          width: 200,
          type: 'AMOUNT',
          formatter: row => {
            if (row.amountFixed) {
              return '￥' + formatAmount(row.fixedAmount)
            }
            return '不定额'
          }
        },
        {
          prop: 'budget',
          label: '预算金额',
          type: 'AMOUNT',
          width: 150
        },
        {
          prop: 'sendAvailableAmount',
          label: '剩余可发放金额',
          type: 'AMOUNT',
          width: 150
        },
        {
          prop: '状态',
          label: '状态',
          width: 80,
          formatter: row => {
            return (
              getOptionsItemLabel(activityDiscountStatusOptions, row.status) ||
              ''
            )
          }
        },
        {
          prop: 'createTime',
          label: '投放时间',
          width: 200,
          formatter: row => {
            const start = formatDateTime('yyyy-MM-dd', row.availableBeginTime)
            const end = formatDateTime('yyyy-MM-dd', row.availableEndTime)
            return `${start} ~ ${end}`
          }
        }
      ],
      actionButtons: [
        {
          label: '发放明细',
          click: row => {
            this.$router.push(
              `/discount/wechatTransferSentDetail?couponsId=${row.id}`
            )
          }
        },
        {
          label: '删除',
          click: row => {
            oConfirm(
              '删除后将无法找回此数据，请谨慎操作',
              '删除此微信转账至零钱计划？',
              {
                confirm: async () => {
                  const [err] = await marketingClient.transferWxDelete({
                    body: {
                      id: row.id
                    }
                  })
                  if (err) return handleError(err)
                  this.tableReload()
                  showMessage('操作成功')
                }
              }
            )
          }
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '新建微信转账至零钱',
          click: () => {
            this.$router.push('/discount/wechatTransferNew')
          }
        }
      ]
    }
  },
  computed: {
    oTable() {
      return this.$refs['pc-list']
    }
  },
  activated() {
    if (!this.isFirstLoad) this.reload()
  },
  methods: {
    // 刷新页面
    async tableReload() {
      this.oTable.reload()
    },

    // 搜索之前对参数处理
    async beforeSearch(fData) {
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')
      return fData
    }
  }
}
</script>
