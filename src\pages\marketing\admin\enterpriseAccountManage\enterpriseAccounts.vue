<template>
  <o-container
    ref="container"
    :title="$route.meta.title"
    style="position: relative"
  >
    <div style="position: absolute; top: 8px; right: 24px">
      <el-button style="height: 32px; padding: 0" @click="getMoney"
        >刷新</el-button
      >
    </div>
    <!-- 状态筛选tabs -->
    <MoneySummaries v-loading="isLoading" :moneyInfo="moneyInfo" />
    <!-- 筛选区域 -->
    <o-top-select
      style="margin-bottom: 16px"
      ref="top-select"
      :formJson="topSelectFormJson"
      :immediate="true"
      class="zp-mb-16 o-app"
      labelWidth="84px"
      @search="onSearch"
    />

    <!-- 表格区域 -->
    <o-table
      ref="o-table"
      :sticky="true"
      :pagination="{ fixed: true }"
      :showPagination="true"
      :deleteNullApiParams="true"
      :tableHeader="tableHeader"
      :requestFn="getListApi"
      emptyHeight="calc(100vh - 450px)"
    />
  </o-container>
</template>
<script>
import MoneySummaries from './moneySummaries.vue'
import formatAmount from 'kit/formatters/formatAmount'
import symbolAmount from 'kit/formatters/symbolAmount'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import { delay } from 'kit/helpers/delay'
import handleError from 'kit/helpers/handleError'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.accountQuery({
    body: params
  })
  if (err) return handleError(err)
  return result.data
}
const typeOptions = [
  { label: '增加', value: '1' },
  { label: '减少', value: '2' },
  { label: '锁定', value: '3' }
]
const itemOptions = [
  { label: '运营平台充值', value: '1' },
  { label: '运营平台授信额度增加', value: '2' },
  { label: '微信转账至零钱计划结束退回', value: '3' },
  { label: '微信转账至零钱计划删除被退回', value: '4' },
  { label: '运营平台退款', value: '101' },
  { label: '运营平台授信额度减少', value: '102' },
  { label: '微信转账至零钱计划占用', value: '201' },
  { label: '微信转账至零钱计划调整增加', value: '202' },
  { label: '微信转账至零钱计划调整减少', value: '203' }
]
export default {
  components: {
    MoneySummaries
  },
  data() {
    return {
      isLoading: false,
      topSelectFormJson: [
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            rangeSeparator: '~',
            prop: 'createTime',
            label: '创建时间',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd 00:00:00'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'sn',
            label: '流水号',
            placeholder: '请输入流水号'
          }
        },
        {
          type: 'select',
          item: {
            prop: 'type',
            label: '类型',
            placeholder: '请选择类型',
            options: typeOptions
          }
        },
        {
          type: 'select',
          item: {
            prop: 'item',
            label: '事项',
            placeholder: '请选择事项',
            options: itemOptions
          }
        }
      ],
      isFirstLoad: true,
      getListApi: loadList,
      moneyInfo: null,
      tableHeader: [
        {
          label: '流水号',
          prop: 'sn',
          fixed: true,
          minWidth: 150
        },
        {
          label: '时间',
          prop: 'createTime',
          type: 'DATE_TIME'
        },
        {
          label: '类型',
          prop: 'type',
          minWidth: 80,
          formatter: row => {
            return getOptionsItemLabel(typeOptions, row.type) || '-'
          }
        },
        {
          label: '事项',
          prop: 'item',
          minWidth: 120,
          formatter: row => {
            return getOptionsItemLabel(itemOptions, row.item) || '-'
          }
        },
        {
          label: '可用金额',
          prop: 'amount',
          type: 'AMOUNT',
          width: 200,
          formatter: row => {
            return symbolAmount(row.symbol) + '￥' + formatAmount(row.amount)
          }
        }
      ]
    }
  },
  computed: {
    oTable() {
      return this.$refs['o-table']
    }
  },
  created() {
    this.getMoney()
  },
  activated() {
    if (!this.isFirstLoad) this.reload()
  },
  methods: {
    async getMoney() {
      this.isLoading = true
      const [err, r] = await marketingClient.accountSummary({
        body: {}
      })
      if (err) {
        this.isLoading = false
        handleError(err)
        return
      }
      await delay(100)
      this.isLoading = false
      this.moneyInfo = r.data
    },
    // 搜索
    async onSearch() {
      const fData = await this.$refs['top-select'].getFormData()
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')

      await this.oTable.appendRequestParams(fData)
      this.isFirstLoad = false
    }
  }
}
</script>
<style scoped>
::v-deep .o-container-main {
  position: static;
}
</style>
