<template>
  <div class="summaries" v-if="moneyInfo">
    <Tooltip
      style="flex: 1 1 280px"
      title="当前账户余额（包含锁定），如需充值请联系我们"
    >
      <div class="container-box">
        <div class="icon" style="background: #cfe5ff">
          <img width="24px" src="kit/assets/images/money_b.png" />
        </div>
        <div>
          <div class="title">账户余额（元）</div>
          <AutoEllipsisTooltip
            class="money"
            placement="right"
            :content="formatNumber(moneyInfo.totalAmount)"
          />
        </div>
      </div>
    </Tooltip>
    <Tooltip style="flex: 1 1 280px" title="不包含锁定金额的当前可使用余额">
      <div
        class="container-box"
        style="background: linear-gradient(180deg, #f5fef2 0%, #e6feee 100%)"
      >
        <div class="icon" style="background: #d2fae4">
          <img width="24px" src="kit/assets/images/money_g.png" />
        </div>
        <div>
          <div class="title">可用金额（元）</div>
          <AutoEllipsisTooltip
            class="money"
            placement="right"
            :content="formatNumber(moneyInfo.availableAmount)"
          />
        </div>
      </div>
    </Tooltip>
    <Tooltip style="flex: 1 1 280px" title="未开始、投放中的计划的金额之和">
      <div class="container-box">
        <div class="icon" style="background: #cfe5ff">
          <img width="24px" src="kit/assets/images/money_b.png" />
        </div>
        <div>
          <div class="title">锁定金额（元）</div>
          <AutoEllipsisTooltip
            class="money"
            placement="right"
            :content="formatNumber(moneyInfo.lockedAmount)"
          />
        </div>
      </div>
    </Tooltip>
    <Tooltip style="flex: 1 1 280px" title="实际发放值之和">
      <div
        class="container-box"
        style="
          margin-right: 0;
          background: linear-gradient(180deg, #fffdf6 0%, #fff7ec 100%);
        "
      >
        <div class="icon" style="background: #fef1cc">
          <img width="24px" src="kit/assets/images/money_o.png" />
        </div>
        <div>
          <div class="title">历史发放总金额（元）</div>
          <AutoEllipsisTooltip
            class="money"
            placement="right"
            :content="formatNumber(moneyInfo.totalUsedAmount)"
          />
        </div>
      </div>
    </Tooltip>
  </div>
</template>
<script>
import Tooltip from './tooltip.vue'
import AutoEllipsisTooltip from 'kit/components/marketing/admin/autoEllipsisTooltip.vue'
import formatNumber from 'kit/formatters/number'
export default {
  components: {
    Tooltip,
    AutoEllipsisTooltip
  },
  props: {
    moneyInfo: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    formatNumber
  }
}
</script>
<style scoped>
.summaries {
  width: 100%;
  display: flex;
  padding: 16px 0;
}
.container-box {
  display: flex;
  /* width: 280px; */
  height: 78px;
  padding: 12px 20px;
  margin-right: 16px;
  box-sizing: border-box;
  align-items: center;
  gap: 16px;
  flex: 1 0 0;
  border-radius: 4px;
  background: linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%);
}
.icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  fill: var(--10-color-1, #37f);
  border-radius: 50px;
}
.title {
  color: #1e2228;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.money {
  width: 120px;
  color: #1e2228;
  font-family: 'DIN Alternate';
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 32px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
