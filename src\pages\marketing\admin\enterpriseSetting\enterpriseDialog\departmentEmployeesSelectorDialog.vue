<template>
  <div>
    <el-dialog
      width="800px"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :append-to-body="true"
      custom-class="departmentUsersSelectorDialog"
    >
      <MultipleEmployeeInsideDepartment
        v-if="visible"
        v-loading="isLoading"
        title="授权用户"
        :defaultSelectedEmployees="defaultSelectedEmployees"
        :departments="departments"
        :employees="employees"
        :breadcrumbDepartments="breadcrumbDepartments"
        @search="search"
        @selectDepartment="selectDepartment"
        @clickDepartmentSubdivision="handleClickDepartmentSubdivision"
        @clickBreadcrumbDepartment="handleClickBreadcrumbDepartment"
        @confirm="confirm"
        @cancel="cancel"
      />
    </el-dialog>
  </div>
</template>
<script>
import MultipleEmployeeInsideDepartment from 'kit/components/ui/picker/multipleEmployeeInsideDepartment.vue'
import handleError from 'kit/helpers/handleError'
import formatRootDepartment from 'kit/formatters/marketing/formatRootDepartment'
import formatDepartmentsEmployees from 'kit/formatters/marketing/formatDepartmentsEmployees'
import getAllDepartmentsFromRootDepartment from 'kit/helpers/marketing/getAllDepartmentsFromRootDepartment'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    MultipleEmployeeInsideDepartment
  },
  props: {
    defaultSelectedEmployeesIds: {
      type: Array,
      default() {
        return []
      }
    }
  },
  computed: {
    defaultSelectedEmployees() {
      const defaultSelectedEmployees = this.allEmployees.filter(item =>
        this.defaultSelectedEmployeesIds.includes(item.id)
      )
      return defaultSelectedEmployees
    }
  },
  data() {
    return {
      visible: false,
      isLoading: false,
      departments: [],
      rootDepartment: null,
      allEmployees: [],
      employees: [],
      breadcrumbDepartments: []
    }
  },
  created() {
    this.load()
  },

  methods: {
    async load() {
      this.isLoading = true
      const [err, r] = await marketingClient.merchantOrgTree({
        body: {
          withUserCount: true,
          withUser: true
        }
      })
      this.isLoading = false
      if (err) {
        handleError(err)
        return
      }
      this.rootDepartment = formatRootDepartment(r.data)
      this.departments = this.rootDepartment.children

      this.breadcrumbDepartments = [this.rootDepartment]

      this.allEmployees = formatDepartmentsEmployees(r.data)
      this.employees = this.allEmployees.filter(
        item => item.department.id === this.rootDepartment.id
      )
    },
    handleClickDepartmentSubdivision(v) {
      this.selectDepartment(v)

      this.departments = v.children

      const allDepartments = getAllDepartmentsFromRootDepartment(
        this.rootDepartment
      )
      this.breadcrumbDepartments = []
      for (var c of v.parentDepartments) {
        this.breadcrumbDepartments.push(
          allDepartments.find(item => item.id === c.id)
        )
      }
      this.breadcrumbDepartments.push(v)
    },
    handleClickBreadcrumbDepartment(v) {
      //因为点击某个面包屑的部门时候 也相当于点击了它的下一级
      this.handleClickDepartmentSubdivision(v)

      const index = this.breadcrumbDepartments.findIndex(
        item => item.id === v.id
      )
      if (index !== -1) {
        this.breadcrumbDepartments = this.breadcrumbDepartments.slice(
          0,
          index + 1
        )
      }
    },
    selectDepartment(v) {
      console.log('v===', v)
      // this.employees = this.allEmployees.filter(
      //   item => item.department.id === v.id
      // )
      this.employees = v.userList ? v.userList : []
    },
    search(keyword) {
      console.log('keyword===', keyword)

      const allDepartments = getAllDepartmentsFromRootDepartment(
        this.rootDepartment
      )
      this.departments = allDepartments.filter(item =>
        item.name.includes(keyword)
      )
      if (!keyword) {
        this.departments = this.rootDepartment.children
      }

      this.employees = this.allEmployees.filter(item =>
        item.name.includes(keyword)
      )
    },
    open() {
      this.visible = true
      this.load()
    },
    close() {
      Object.assign(this.$options.data, {
        departments: [],
        rootDepartment: null,
        allEmployees: [],
        employees: [],
        breadcrumbDepartments: []
      })
      this.visible = false
    },
    cancel() {
      this.close()
    },
    async confirm(selectedEmployees, showLoading) {
      var idList = []
      for (var c of selectedEmployees) {
        idList.push(c.id)
      }
      showLoading()
      const [err, r] = await marketingClient.merchantSetPromoter({
        body: {
          idList: idList
        }
      })
      showLoading(false)
      if (err) {
        handleError(err)
        loading = false
        return
      }
      this.$message.success('设置成功')
      this.close()
      this.$emit('refresh')
      window.location.reload()
    }
  }
}
</script>
<style>
.departmentUsersSelectorDialog .el-dialog__header {
  display: none;
}
.departmentUsersSelectorDialog .el-dialog__body {
  padding: 0;
}
</style>
