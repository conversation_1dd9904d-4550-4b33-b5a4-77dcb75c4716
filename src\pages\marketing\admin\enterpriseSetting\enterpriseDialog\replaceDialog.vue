<template>
  <div v-if="visible">
    <el-dialog title="更换管理员" :visible.sync="visible" width="560px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="管理员账号" prop="mobile">
          <el-input v-model="form.mobile" disabled></el-input>
        </el-form-item>
        <el-form-item label="图形验证码" prop="verifyCode">
          <Input
            v-model="form.verifyCode"
            :allowZero="true"
            valueType="int"
            placeholder="请输入图形验证码"
            maxlength="4"
          />
          <img class="captcha" @click="loadCaptcha" :src="src" />
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <Input
            v-model="form.code"
            :allowZero="true"
            valueType="int"
            placeholder="请输入短信验证码"
            maxlength="6"
          />
          <VaildCode
            :cellPhone="form.mobile"
            :verifyCode="form.verifyCode"
            :verifyCodeId="form.verifyCodeId"
            @getSmsCode="getSmsCode"
          />
        </el-form-item>
        <el-form-item label="新管理员" prop="adminName">
          <el-input
            v-model="form.adminName"
            placeholder="请选择新管理员"
            readonly
            @focus="choose"
          ></el-input>
        </el-form-item>
        <div style="text-align: right">
          <el-button style="color: #1e2228; font-weight: 400" @click="cancel"
            >取消</el-button
          >
          <el-button type="primary" @click="save">确定</el-button>
        </div>
      </el-form>
      <SelectEmployeeDialog
        ref="selectEmployeeDialog"
        title="选择管理员"
        @confirm="confirmChoose"
        :selectEmployee="selectEmployee"
      />
    </el-dialog>
  </div>
</template>
<script>
import Input from 'kit/components/marketing/admin/input.vue'
import VaildCode from 'kit/pages/marketing/admin/getValidCode.vue'
import SelectEmployeeDialog from 'kit/pages/marketing/admin/enterpriseSetting/enterpriseDialog/selectEmployeeDialog.vue'
import { validateTel } from '../../util/index.js'
import handleError from 'kit/helpers/handleError'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    Input,
    VaildCode,
    SelectEmployeeDialog
  },
  props: {
    mobile: String
  },
  data() {
    return {
      visible: false,
      form: {
        mobile: this.mobile,
        verifyCodeId: '',
        code: '',
        adminName: ''
      },
      rules: {
        mobile: [{ validator: validateTel, required: true, trigger: 'blur' }],
        verifyCode: [
          { required: true, trigger: 'blur', message: '请输入图形验证码' }
        ],
        code: [
          { required: true, trigger: 'blur', message: '请输入短信验证码' }
        ],
        adminName: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: '请选择新管理员'
          }
        ]
      },
      smsToken: '',
      selectEmployee: {
        id: '',
        name: ''
      }
    }
  },
  computed: {
    src() {
      const baseUrl = window.env.api
      if (!baseUrl) {
        handleError('无法获取平台的API地址')
        return
      }
      return `${baseUrl}/marketing/sms/imageCaptcha?token=${encodeURIComponent(
        this.form.verifyCodeId
      )}`
    }
  },
  methods: {
    open() {
      this.visible = true
      this.loadCaptcha()
      this.form.mobile = this.mobile
    },
    async loadCaptcha() {
      const [err, r] = await marketingClient.smsCreateImageCaptcha()
      if (err) {
        handleError(err)
        return
      }
      this.form.verifyCodeId = r.data
    },

    choose() {
      this.$refs.selectEmployeeDialog.open()
    },
    confirmChoose(item) {
      console.log('item===', item)
      this.form.adminName = item.name
      this.form.adminId = item.id
    },
    getSmsCode(val) {
      this.smsToken = val
    },
    cancel() {
      this.form = {
        mobile: this.mobile,
        verifyCodeId: '',
        code: '',
        adminName: ''
      }
      this.visible = false
    },
    async save() {
      await this.$refs.form.validate()
      if (!this.form.adminName) {
        this.$message.warning('请选择新管理员')
        return
      }
      this.loading = true
      const [err, r] = await marketingClient.merchantChangeAdmin({
        body: {
          mobile: this.form.mobile,
          smsCode: this.form.code,
          smsToken: this.smsToken,
          adminId: this.form.adminId
        }
      })
      if (err) {
        handleError(err)
        this.loading = false
        return
      }
      this.loading = false
      this.$message.success('变更成功')
      this.visible = false
      window.location.href = `${this.$router.options.base}/introduction`
    }
  }
}
</script>
<style scoped>
.captcha {
  position: absolute;
  width: 80px;
  height: 30px;
  right: 10px;
  top: 5px;
}
::v-deep .el-dialog__header {
  padding: 16px 24px;
  border-radius: 8px 8px 0 0;
}
::v-deep .el-input .el-input__inner {
  box-sizing: border-box;
  padding: 5px 12px;
  border-radius: 6px;
  opacity: 1;
  border: 1px solid #cad0dbff;
  /* background: #f7f9fcff; */
}
::v-deep .el-input.is-disabled .el-input__inner {
  color: #a6aebd;
}
</style>
