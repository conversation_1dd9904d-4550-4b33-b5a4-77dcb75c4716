<template>
  <el-dialog
    title="裁剪图片"
    :visible.sync="dialogLogo"
    custom-class="special-dialog"
    width="800px"
    @close="closeLogo"
  >
    <template>
      <el-upload
        style="margin-top: 10px; height: 55v"
        ref="upload"
        :headers="uploadHeaders"
        list-type="picture"
        :file-list="fileList"
        :on-change="handleChange"
        :before-upload="beforeUpload"
        :action="uploadUrl"
        accept=".jpg,.jpeg,.png"
        :show-file-list="true"
        :on-success="onSuccess"
        :auto-upload="false"
      >
        <div slot="tip" style="padding:14px 0 10px 0;" class="el-upload__tip">
          支持jpg、jpeg、png格式，建议上传png透明底的反白稿效果最佳，且小于10M
        </div>
        <div
          style="
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          "
          slot="file"
          slot-scope="{ file }"
        >
          <el-image
            style="width: 300px; height: 300px"
            src="kit/assets/images/default-logo.png"
            v-if="!logoImgSrc && !file.url"
          ></el-image>
          <el-image
            style="width: 300px; height: 300px"
            :src="file.url"
            v-else
          ></el-image>
        </div>
        <el-button size="small" type="primary">重新上传</el-button>
      </el-upload>
    </template>
    <span slot="footer" class="dialog-footer">
      <el-button style="color: #1e2228; font-weight: 400" size="small" @click="closeLogo"
        >取 消</el-button
      >
      <el-button type="primary" @click="onSubmit" size="small" :loading="loading"
        >保 存</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import { getToken } from 'kit/helpers/token'
import { showMessage } from 'kit/helpers/showMessage'

export default {
  props: {
    fileList: Array,
    logoImgSrc: String
  },
  data() {
    return {
      dialogLogo: false,
      loading: false,
      uploadHeaders: {
        Authorization: `Bearer ${getToken()}`
      }
    }
  },
  computed: {
    uploadUrl() {
      return `${window.env.api}/marketing/file/upload`
    }
  },
  methods: {
    open() {
      this.dialogLogo = true
    },
    closeLogo() {
      this.dialogLogo = false
    },
    handleChange(file, fileList) {
      if (this.beforeUpload(file.raw)) {
        if (fileList.length > 0) {
          this.fileList = [fileList[fileList.length - 1]]
        }
      } else {
        this.fileList = []
      }
    },
    beforeUpload(file) {
      const isType =
        file.type === 'image/jpeg' ||
        file.type === 'image/jpg' ||
        file.type === 'image/png'
      const isSize = file.size / 1024 / 1024 < 10
      if (!isType) {
        return showMessage('上传图片类型只支持jpg、jpeg、png格式', 'error')
      }
      if (!isSize) {
        return showMessage('上传图片大小不能超过10M', 'error')
      }
      return isType && isSize
    },
    onSubmit() {
      this.loading = true
      if (!this.fileList.length) {
        this.$message({
          type: 'warning',
          message: '您还没有上传logo，请上传后再保存'
        })
        this.loading = false
        return
      }
      if (this.fileList[0].status === 'success') {
        this.closeLogo()
        return
      }
      this.$refs.upload.submit()
    },
    onSuccess(res) {
      this.loading = false
      this.$message.success('上传成功！')
      this.closeLogo()
      this.$emit('getUrl', res.data.url)
    },
    onError() {
      this.loading = false
      showMessage('上传失败,请稍后再重新上传', 'error')
    }
  }
}
</script>
<style scoped>
.el-upload__tip {
  color: #888;
}
::v-deep .el-upload-list--picture .el-upload-list__item {
  padding: 0;
  height: 290px;
}
</style>
