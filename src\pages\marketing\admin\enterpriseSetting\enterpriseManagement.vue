<template>
  <div
    class="enterprise_manage"
    style="background: #ffffff"
    v-loading="isLoading"
  >
    <div
      style="
        margin: 0 24px;
        padding: 12px 0;
        color: #1e2228ff;
        font-size: 16px;
        line-height: 32px;
        font-weight: 600;
        border-bottom: 1px solid #e4e7edff;
      "
    >
      企业管理
    </div>
    <div
      class="customer-webkit-scrollbar"
      style="height: calc(100vh - 135px); overflow-y: auto"
    >
      <div style="padding: 24px 208px">
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
          "
        >
          <div style="display: flex">
            <div class="red"></div>
            <div style="color: #1e2228ff; font-size: 16px; font-weight: 600">
              基本信息
            </div>
          </div>

          <div>
            <el-button
              v-show="baseInfoDisabled"
              type="text"
              style="color: #f77234ff; font-size: 14px"
              @click="baseInfoDisabled = false"
              >编辑</el-button
            >
            <el-button
              v-show="!baseInfoDisabled"
              type="text"
              style="color: #f77234ff; font-size: 14px"
              @click="cancel"
              >取消</el-button
            >
            <el-button
              v-show="!baseInfoDisabled"
              type="text"
              style="color: #f77234ff; font-size: 14px"
              @click="save"
              >保存</el-button
            >
          </div>
        </div>
        <div style="display: flex">
          <div
            style="
              width: 86px;
              height: 86px;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 6px;
            "
            :class="isDefault ? 'background' : 'noBackground'"
          >
            <el-image
              style="border-radius: 6px"
              :class="isDefault ? 'default_width' : 'noBackground'"
              :src="logoImgSrc"
            ></el-image>
            <div class="shadow" v-show="!baseInfoDisabled" @click="openUpload">
              上传logo
            </div>
          </div>
          <div style="font-size: 14px; margin-left: 16px; flex: 1">
            <div style="color: #1e2228ff">企业名称</div>
            <el-input
              class="company_input"
              style="margin: 8px 0 7px"
              v-model="merchant.name"
              :disabled="baseInfoDisabled"
              maxlength="50"
            ></el-input>
            <div style="color: #828b9bff">
              注册时间：{{ merchant.createTime }}
            </div>
          </div>
        </div>
      </div>
      <div style="margin-top: 24px; padding: 0 208px">
        <div style="display: flex; margin-bottom: 16px">
          <div class="red"></div>
          <div style="color: #1e2228ff; font-size: 16px; font-weight: 600">
            角色管理
          </div>
        </div>
        <div style="display: flex">
          <div class="role" style="margin-right: 24px" v-if="isAdmin">
            <div>
              <div style="color: #1e2228ff; font-size: 18px; font-weight: 600">
                主管理员
              </div>
              <div
                style="
                  color: #4e5769ff;
                  font-size: 14px;
                  width: 236px;
                  height: 44px;
                  line-height: 22px;
                  margin-top: 8px;
                "
              >
                主管理员默认拥有全部权限
              </div>
              <el-button class="button" @click="replace"
                >变更主管理员</el-button
              >
              <div style="display: flex; align-items: center; padding: 3px 0">
                <div
                  style="
                    margin-right: 8px;
                    width: 32px;
                    height: 32px;
                    border-radius: 8px;
                    background: #fffae8ff;
                    color: #ff9a01ff;
                    font-size: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  {{ admin.petName }}
                </div>
                <div>
                  <div style="color: #1e2228ff; line-height: 22px">
                    {{ admin.name }}
                  </div>
                  <div
                    style="color: #828b9bff; font-size: 12px; line-height: 20px"
                  >
                    {{ handleEncipherPhone(admin.mobile) }}
                  </div>
                </div>
              </div>
            </div>
            <img
              width="70px"
              style="height: 101px; margin: 32px 0 0 26px"
              src="kit/assets/images/img_admin.png"
            />
          </div>
          <div class="role">
            <div>
              <div style="color: #1e2228ff; font-size: 18px; font-weight: 600">
                发放管理员
              </div>
              <div
                style="
                  color: #4e5769ff;
                  font-size: 14px;
                  width: 236px;
                  height: 44px;
                  line-height: 22px;
                  margin-top: 8px;
                "
              >
                发放管理员拥有发放的全部权限，但您可以自行修改
              </div>
              <el-button class="button" @click="authorize">授权用户</el-button>
              <div style="display: flex">
                <div
                  class="item"
                  style="font-size: 12px"
                  :class="bg[index]"
                  v-for="(item, index) in memberList"
                  :key="index"
                >
                  <div>{{ item }}</div>
                </div>
                <div
                  v-if="remainMemberCount"
                  class="item"
                  style="font-size: 14px; background: #f2f4f7; color: #828b9b"
                >
                  {{ remainMemberCount }}+
                </div>
              </div>
            </div>
            <img
              width="70px"
              style="height: 101px; margin: 32px 0 0 26px"
              src="kit/assets/images/img_distribution_admin.png"
            />
          </div>
        </div>
      </div>
    </div>
    <UploadLogo
      ref="uploadLogo"
      :fileList="fileList"
      :logoImgSrc="logoImgSrc"
      @getUrl="getUrl"
    />
    <ReplaceAdministrators
      ref="replaceAdministrators"
      :mobile="admin.mobile"
      @refresh="loadProfile"
    />
    <DepartmentEmployeesSelectorDialog
      ref="departmentEmployeesSelectorDialog"
      :defaultSelectedEmployeesIds="defaultSelectedEmployeesIds"
      @refresh="queryRoleUser"
    />
  </div>
</template>

<script>
import UploadLogo from './enterpriseDialog/uploadLogo.vue'
import ReplaceAdministrators from './enterpriseDialog/replaceDialog.vue'
import defaultLogo from 'kit/assets/images/logo.png'
import DepartmentEmployeesSelectorDialog from './enterpriseDialog/departmentEmployeesSelectorDialog.vue'
import handleError from 'kit/helpers/handleError'

import makeMarketingClient from 'kit/services/marketing/makeClient'
import { eventBus } from 'kit/helpers/eventBus'
const marketingClient = makeMarketingClient()

export default {
  components: {
    UploadLogo,
    ReplaceAdministrators,
    DepartmentEmployeesSelectorDialog
  },
  data() {
    return {
      isLoading: false,
      baseInfoDisabled: true,
      isAdmin: false,
      merchant: {},
      admin: {},
      logoImgSrc: '',
      fileList: [],
      uploadUrl: '',
      memberList: [],
      defaultSelectedEmployeesIds: [],
      bg: ['bg1', 'bg2', 'bg3', 'bg4', 'bg5', 'bg6', 'bg7'],
      roleId: '',
      remainMemberCount: 0
    }
  },
  computed: {
    isDefault() {
      if (this.logoImgSrc === defaultLogo) {
        return true
      }
      return false
    }
  },
  async created() {
    this.loadProfile()
    this.loadUserProfile()
    this.queryRoleUser()
  },
  methods: {
    async loadUserProfile() {
      const [err, r] = await marketingClient.userProfile({
        method: 'GET'
      })
      if (err) {
        handleError(err)
        return
      }
      this.isAdmin = r.data.isAdmin
    },
    async loadProfile() {
      this.isLoading = true
      const [err, r] = await marketingClient.merchantProfile({
        body: {}
      })
      this.isLoading = false
      if (err) {
        handleError(err)
        return
      }

      this.merchant = r.data.merchant
      this.uploadUrl = r.data.merchant.logo || ''
      this.logoImgSrc = r.data.merchant.logo
        ? r.data.merchant.logo
        : defaultLogo
      this.admin = r.data.admin
      this.admin.petName = r.data.admin.name.slice(-1)
    },
    getUrl(val) {
      this.uploadUrl = val
      this.logoImgSrc = val
    },
    async queryRoleUser() {
      const [err, r] = await marketingClient.merchantQueryRoleUser({
        body: {
          filters: {
            roleType: '2'
          },
          start: 0,
          limit: 100000,
          sorts: []
        }
      })
      if (err) {
        handleError(err)
        return
      }

      var list = []
      for (var c of r.data.list) {
        list.push(c.user.name.slice(-1))
        this.defaultSelectedEmployeesIds.push(c.user.id)
      }

      this.memberList = []
      this.memberList = list.slice(0, 7)
      if (r.data.total > 7 && r.data.total <= 106) {
        this.remainMemberCount = r.data.total - 7
      } else if (r.data.total > 106) {
        this.remainMemberCount = 99
      } else {
        this.remainMemberCount = 0
      }
    },
    handleEncipherPhone(phone) {
      if (phone) {
        let newPhone = phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
        return newPhone
      }
    },
    cancel() {
      this.loadProfile()
      this.baseInfoDisabled = true
    },
    async save() {
      const [err, r] = await marketingClient.merchantUpdateProfile({
        body: {
          name: this.merchant.name,
          logUrl: this.uploadUrl
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.loadProfile()
      this.baseInfoDisabled = true
      this.$message.success('保存成功')
      eventBus.$emit('appUpdateProfile')
    },
    openUpload() {
      this.$refs.uploadLogo.open()
      this.addFileList()
    },
    addFileList() {
      if (this.logoImgSrc) {
        this.fileList = [
          {
            status: 'success',
            name: this.logoImgSrc.substring(
              this.logoImgSrc.lastIndexOf('/') + 1,
              this.logoImgSrc.length
            ),
            url: this.logoImgSrc
          }
        ]
      } else {
        this.fileList = [{}]
      }
    },
    replace() {
      this.$refs.replaceAdministrators.open()
    },
    authorize() {
      this.$refs.departmentEmployeesSelectorDialog.open()
    }
  }
}
</script>

<style scoped>
.red {
  width: 4px;
  height: 16px;
  margin-right: 8px;
  border-radius: 1px;
  opacity: 1;
  background: #f77234ff;
}
.background {
  background: #fff3e8;
}
.default_width {
  width: 48px;
  height: 48px;
}
.noBackground {
  width: 100%;
  height: 100%;
  background: none;
}
.shadow {
  width: 86px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  font-size: 14px;
  border-radius: 0 0 8px 8px;
  background: #00000073;
  color: #ffffffff;
  position: absolute;
  top: 55px;
}
.role {
  display: flex;
  padding: 20px 32px 23px 24px;
  border: 2px solid #ffffffff;
  background: #ffffffff;
  box-shadow: 0 5px 12px 2px #0000000a, 0 3px 6px 0 #0000000a,
    0 1px 2px -2px #0000001a;
  border-radius: 8px;
}
.button {
  margin: 8px 0;
  color: #1e2228;
  border-radius: 6px;
  border: 1px solid #cad0dbff;
  background: #ffffffff;
  box-shadow: 0 2px 0 0 #00000005;
}
.bg1 {
  background: #f0f5ff;
  color: #ff2f54eb;
}
.bg2 {
  background: #fffae8;
  color: orange;
}
.bg3 {
  background: #e6fffb;
  color: #ff13c2c2;
}
.bg4 {
  background: #f5e8ff;
  color: #ff722ed1;
}
.bg5 {
  background: #fffae8;
  color: orange;
}
.bg6 {
  background: #f5e8ff;
  color: #ff722ed1;
}
.bg7 {
  background: #f5e8ff;
  color: #ff13c2c2;
}
.item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}
::v-deep.company_input .el-input__inner {
  border: 1px solid #f77234;
}
</style>
