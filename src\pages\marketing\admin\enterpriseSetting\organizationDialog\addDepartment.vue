<template>
  <div class="add-person" v-if="drawer">
    <el-drawer
      :title="title"
      :visible.sync="drawer"
      :direction="direction"
      @close="close"
    >
      <div class="content">
        <div class="person-form">
          <el-form :model="form" :rules="rules" ref="form" label-width="100px">
            <el-form-item label="部门名称" prop="name">
              <Input
                v-model="form.name"
                :allowZero="true"
                :trim="true"
                placeholder="请输入部门名称"
                maxlength="50"
              />
            </el-form-item>
            <el-form-item
              label="上级部门"
              prop="prevDepartment"
              v-if="
                currentDepartment.parentDepartments &&
                currentDepartment.parentDepartments.length
              "
            >
              <el-input
                suffix-icon="el-icon-arrow-down"
                v-model="form.prevDepartment.name"
                readonly
                placeholder="请选择上级部门"
                :disabled="title === '添加部门'"
                @focus="chooseDepartment"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="person-btn">
          <el-button style="color: #1e2228; font-weight: 400" @click="close"
            >取消</el-button
          >
          <el-button type="primary" @click="saveInfo" :loading="loading"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
    <ChoosePreDepartment ref="choosePreDepartment" @confirm="confirm" />
  </div>
</template>

<script>
import Input from 'kit/components/marketing/admin/input.vue'
import ChoosePreDepartment from './choosePreDepartment.vue'
import handleError from 'kit/helpers/handleError'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    Input,
    ChoosePreDepartment
  },
  props: {
    currentDepartment: Object
  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      form: {
        name: '',
        prevDepartment: {
          id: '',
          name: ''
        }
      },
      rules: {
        name: [{ required: true, trigger: 'blur', message: '请输入部门名称' }],
        prevDepartment: [
          { required: true, trigger: 'change', message: '请选择上级部门' }
        ]
      },
      loading: false
    }
  },
  watch: {
    drawer(val) {
      if (val) {
        if (this.title === '添加部门') {
          this.form.prevDepartment.name = this.currentDepartment.name
        } else {
          this.form.name = this.currentDepartment.name
          if (this.currentDepartment.parentDepartments.length) {
            this.form.prevDepartment =
              this.currentDepartment.parentDepartments[
                this.currentDepartment.parentDepartments.length - 1
              ]
          } else {
            this.form.prevDepartment = {
              id: '',
              name: ''
            }
          }
        }
      }
    }
  },
  methods: {
    open(title) {
      this.title = title
      this.drawer = true
    },
    close() {
      this.form = {
        name: '',
        prevDepartment: {
          id: '',
          name: ''
        }
      }
      this.drawer = false
    },
    chooseDepartment() {
      this.$refs.choosePreDepartment.open()
    },
    confirm(val) {
      if (val.id === this.currentDepartment.id) {
        this.$message.error('无法选择自己作为上级')
        return
      }
      this.form.prevDepartment.name = val.name
      this.form.prevDepartment.id = val.id
    },

    async saveInfo() {
      await this.$refs.form.validate()
      this.loading = true
      if (this.title === '添加部门') {
        const [err, r] = await marketingClient.merchantSaveOrg({
          body: {
            name: this.form.name,
            parentId: this.currentDepartment.id
          }
        })
        if (err) {
          handleError(err)
          this.loading = false
          return
        }
        this.loading = false
        this.$message({ type: 'success', message: '添加成功' })
        this.close()
        this.$emit('refresh', r.data.id)
        return
      }
      if (
        this.currentDepartment.parentDepartments.length &&
        !this.form.prevDepartment.name
      ) {
        this.$message.error('请选择上级部门')
        this.loading = false
        return
      }
      const [err, r] = await marketingClient.merchantUpdateOrg({
        body: {
          orgId: this.currentDepartment.id,
          parentId: this.form.prevDepartment.id,
          name: this.form.name
        }
      })
      if (err) {
        handleError(err)
        this.loading = false
        return
      }
      this.loading = false
      this.$message({ type: 'success', message: '修改成功' })
      this.close()
      this.$emit('refresh', this.currentDepartment.id)
    }
  }
}
</script>

<style scoped>
.content {
  padding: 0 24px;
}
.person-btn {
  padding-bottom: 20px;
  position: fixed;
  bottom: 0;
  right: 25px;
}
</style>
