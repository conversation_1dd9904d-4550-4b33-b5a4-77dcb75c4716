<template>
  <div class="add-person" v-if="drawer">
    <el-drawer
      :title="title"
      :visible.sync="drawer"
      :direction="direction"
      @close="close"
    >
      <div class="content">
        <div class="person-form">
          <el-form
            class="form"
            :model="form"
            :rules="rules"
            ref="form"
            label-width="80px"
          >
            <el-form-item label="姓名" prop="name">
              <Input
                v-model="form.name"
                :allowZero="true"
                :trim="true"
                placeholder="请输入姓名"
                maxlength="20"
              />
            </el-form-item>
            <el-form-item label="手机号" prop="mobile">
              <Input
                v-model="form.mobile"
                valueType="int"
                :allowZero="true"
                placeholder="请输入手机号"
                maxlength="11"
                :disabled="type === 'edit'"
              />
            </el-form-item>
            <el-form-item label="部门" prop="deptIds">
              <el-select
                style="width: 270px"
                v-model="form.deptIds"
                placeholder="请选择部门"
                multiple
              >
                <el-option
                  :label="val.name"
                  :value="val.orgId"
                  v-for="val in deptList"
                  :key="val.orgId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="角色" prop="roleIds" v-if="type === 'edit'">
              <el-select
                style="width: 270px"
                v-model="form.roleIds"
                placeholder="请选择"
                multiple
              >
                <el-option
                  v-for="item in roleList"
                  :key="item.roleId"
                  :label="item.name"
                  :value="item.roleId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="person-btn">
          <el-button style="color: #1e2228; font-weight: 400" @click="close"
            >取消</el-button
          >
          <el-button type="primary" @click="saveInfo" :loading="loading"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { validateTel } from '../../util/index.js'
import Input from 'kit/components/marketing/admin/input.vue'
import handleError from 'kit/helpers/handleError'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()
import { eventBus } from 'kit/helpers/eventBus'

export default {
  components: {
    Input
  },
  props: {
    userId: String,
    type: String,
    currentCheckedDepartment: Array
  },

  watch: {
    async drawer(val) {
      if (val) {
        if (this.type === 'add') {
          this.title = '添加人员'
          this.initDeptIds()
        } else {
          this.title = '编辑人员'
          await this.getDetails()
          await this.getRoleList()

          const roleList = []
          this.roleList.forEach(role => {
            if (this.form.roleIds.includes(role.roleId)) {
              roleList.push(role.roleId)
            }
          })
          this.form.roleIds = roleList
        }
        this.getDepartmentList()
      }
    }
  },
  data() {
    return {
      title: '',
      drawer: false,
      direction: 'rtl',
      deptList: [], //部门列表
      roleList: [], // 角色列表
      form: {
        name: '',
        mobile: '',
        deptIds: [],
        roleIds: []
      },
      rules: {
        name: [{ required: true, trigger: 'blur', message: '请输入姓名' }],
        mobile: [{ validator: validateTel, required: true, trigger: 'blur' }],
        deptIds: [{ required: true, trigger: 'change', message: '请选择部门' }]
      },
      loading: false
    }
  },
  methods: {
    async getDetails() {
      const [err, r] = await marketingClient.merchantGetMember({
        body: {
          id: this.userId
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.form.name = r.data.user.name
      this.form.mobile = r.data.user.mobile

      let arr1 = []
      if (r.data.orgList && r.data.orgList.length) {
        for (var c of r.data.orgList) {
          arr1.push(parseInt(c.id))
        }
        this.form.deptIds = arr1
      }
      let arr2 = []
      if (r.data.roleList && r.data.roleList.length) {
        for (var c of r.data.roleList) {
          arr2.push(parseInt(c.id))
        }
        this.form.roleIds = arr2
      }
    },
    async getDepartmentList() {
      const [err, r] = await marketingClient.merchantListOrg({
        body: {}
      })
      if (err) {
        handleError(err)
        return
      }
      this.deptList = r.data
    },
    async getRoleList() {
      const [err, r] = await marketingClient.merchantListRole({
        body: {}
      })
      if (err) {
        handleError(err)
        return
      }
      this.roleList = r.data
    },
    initDeptIds() {
      this.form.deptIds = []
      this.form.deptIds = this.currentCheckedDepartment
    },
    open() {
      this.drawer = true
    },
    close() {
      this.form = {
        name: '',
        mobile: '',
        deptIds: [],
        roleIds: []
      }
      this.drawer = false
    },
    async saveInfo() {
      await this.$refs.form.validate()
      this.loading = true
      if (this.type === 'add') {
        const [err, r] = await marketingClient.merchantSaveMember({
          body: {
            name: this.form.name,
            mobile: this.form.mobile,
            organizationIds: this.form.deptIds
          }
        })
        if (err) {
          handleError(err)
          this.loading = false
          return
        }
        this.loading = false
        this.$message({ type: 'success', message: '添加成功' })
        this.close()

        this.$emit('refresh')
        return
      }
      const [err, r] = await marketingClient.merchantUpdateMember({
        body: {
          userId: this.userId,
          name: this.form.name,
          organizationIds: this.form.deptIds,
          roleIds: this.form.roleIds
        }
      })
      if (err) {
        handleError(err)
        this.loading = false
        return
      }
      this.loading = false
      this.$message({ type: 'success', message: '修改成功' })
      this.close()
      eventBus.$emit('appUpdateProfile')
      this.$emit('refresh')
    }
  }
}
</script>

<style scoped>
.content {
  padding: 0 24px;
}
.form > div {
  width: 350px;
}
.person-btn {
  padding-bottom: 20px;
  position: fixed;
  bottom: 0;
  right: 25px;
}
</style>
