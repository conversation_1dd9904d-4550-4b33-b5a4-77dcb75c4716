<template>
  <div>
    <el-dialog width="55%" :visible.sync="visible" @close="close">
      <div
        style="color: #1e2228ff; font-size: 16px; font-weight: 600"
        slot="title"
      >
        {{ title }}
      </div>
      <div v-if="this.importType === 'dept'">
        <div
          style="
            padding: 24px;
            text-align: left;
            font-size: 14px;
            border-radius: 6px;
            background: #f7f9fcff;
          "
        >
          <span style="color: #1e2228ff">下载导入模版</span>
          <div class="button" @click="download">
            <i
              style="margin-right: 8px; color: #828b9b"
              class="icon iconfont icon-direction-interaction-download"
            ></i
            >下载空的导入模板
          </div>
          <span style="color: #828b9b">适合批量新增部门信息</span>
        </div>
      </div>
      <div v-else>
        <div
          style="
            padding: 24px;
            text-align: left;
            font-size: 14px;
            border-radius: 6px;
            background: #f7f9fcff;
          "
        >
          <span style="color: #1e2228ff">下载导入模板</span>
          <div class="button" @click="download">
            <i
              style="margin-right: 8px; color: #828b9b"
              class="icon iconfont icon-direction-interaction-download"
            ></i
            >下载空的导入模板
          </div>
          <span style="color: #828b9b">适合批量新增人员信息</span>
        </div>
      </div>
      <FileImportPrompt
        :successCount="exportResult.successCount"
        :failCount="exportResult.failCount"
        :failFileURL="exportResult.failFile"
        style="width: 100%; margin-top: 16px"
      />
      <div style="border-radius: 6px; margin: 16px 0; text-align: center">
        <el-upload
          width="100%"
          :headers="headerToken"
          ref="upload"
          class="upload"
          drag
          :data="uploadData"
          :file-list="fileList"
          :auto-upload="false"
          :on-success="onSuccess"
          :on-change="onChange"
          :on-error="onError"
          :on-remove="onRemove"
          accept=".xls, .xlsx"
          :action="uploadUrl"
        >
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              padding: 30px 0 24px;
            "
          >
            <img src="kit/assets/images/upload-excel.svg" alt="" />
            <el-button
              style="
                width: 88px;
                margin: 10px 0 8px;
                border: 1px solid #cad0dbff;
                color: #1e2228;
                display: flex;
                justify-content: center;
              "
              plain
              >选择文件</el-button
            >
            <div style="color: #828b9b; font-size: 14px">
              支持xlsx和xls文件，文件大小不超过5M
            </div>
          </div>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          style="color: #1e2228; font-weight: 400"
          type="plain"
          @click="close"
          >取消</el-button
        >
        <el-button type="primary" @click="handleImport" :loading="loading"
          >导入</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import FileImportPrompt from 'kit/components/marketing/admin/fileImportPrompt.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { exportExcel } from 'kit/helpers/exportExcel'
import handleError from 'kit/helpers/handleError'
import { getToken } from 'kit/helpers/token'
import { showMessage } from 'kit/helpers/showMessage'
const marketingClient = makeMarketingClient()

export default {
  components: {
    FileImportPrompt
  },
  props: {
    importType: String
  },
  data() {
    return {
      visible: false,
      loading: false,
      activeName: 'first',
      headerToken: {
        Authorization: `${getToken()}`
      },
      uploadData: {},
      fileList: [],
      exportResult: {
        successCount: 0,
        failCount: 0,
        failFile: ''
      }
    }
  },
  created() {
    window.that = this
  },
  computed: {
    isDept() {
      return this.importType === 'dept'
    },
    title() {
      return this.isDept ? '批量导入部门' : '批量导入用户'
    },
    uploadUrl() {
      const apiName = this.isDept ? 'importOrg' : 'importUser'
      return `${window.env.api}/marketing/merchant/${apiName}`
    }
  },
  methods: {
    resetExportResult() {
      this.exportResult.successCount = 0
      this.exportResult.failCount = 0
    },
    open() {
      this.visible = true
      this.resetExportResult()
    },
    close() {
      this.$refs.upload.clearFiles()
      this.resetExportResult()
      this.visible = false
    },
    async download() {
      const tpl =
        this.importType === 'dept' ? 'TPL_IMPORT_DEPT' : 'TPL_IMPORT_USER'
      const result = await marketingClient.fileDownloadTemplate({
        body: {
          tpl: tpl
        }
      })
      await exportExcel(result)
    },
    handleImport() {
      if (!this.fileList.length) {
        return showMessage('请上传excel文件', 'error')
      }
      this.loading = true
      this.fileList[0].status = 'ready'
      this.$refs.upload.submit()
    },

    async onSuccess(response, file, fileList) {
      this.loading = false
      this.fileList = fileList
      if (!response.success) {
        response.errorCode = Number(response.errorCode)
        handleError(response)
        this.$refs.upload.clearFiles()
        return
      }
      this.$nextTick(() => {
        this.exportResult = response.data
      })
      if (!response.data.failCount) {
        this.close()
        showMessage('导入成功！')
        this.$emit('refresh')
      }
    },
    onChange(file, fileList) {
      this.resetExportResult()
      this.fileList = fileList
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]] // 这一步，是展示最后一次选择的csv文件
      }
    },
    onError(error, file) {
      this.loading = false
      this.$message.error('上传失败，请检查网络')
    },
    onRemove() {
      this.fileList = []
    }
  }
}
</script>
<style scoped>
.button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 168px;
  height: 32px;
  border-radius: 6px;
  margin: 8px 0 4px;
  border: 1px solid #cad0dbff;
  background: #ffffffff;
  box-shadow: 0 2px 0 0 #00000005;
  cursor: pointer;
}
::v-deep .el-upload {
  width: 100%;
}
::v-deep .el-upload-dragger {
  width: 100%;
  border: 1px dashed #d9d9d9;
  background-color: #f7f9fcff;
}
::v-deep .el-dialog__header {
  padding: 16px 24px 16px;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
}
::v-deep .el-dialog__body {
  padding-bottom: 0;
}
::v-deep .el-dialog__footer {
  border-top: 1px solid #e4e7ed;
  padding: 10px 20px;
}
</style>
