<template>
  <el-dialog
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    custom-class="departmentSelectorDialog"
  >
    <SingleDepartment
      v-loading="isLoading"
      :departments="departments"
      :selectedDepartment="selectedDepartment"
      :breadcrumbDepartments="breadcrumbDepartments"
      @select="select"
      @unselect="unselect"
      @clickDepartmentSubdivision="handleClickDepartmentSubdivision"
      @clickBreadcrumbDepartment="handleClickBreadcrumbDepartment"
      @confirm="confirm"
      @cancel="cancel"
      @search="search"
    />
  </el-dialog>
</template>
<script>
import SingleDepartment from 'kit/components/ui/picker/department/single.vue'
import handleError from 'kit/helpers/handleError'
import formatRootDepartment from 'kit/formatters/marketing/formatRootDepartment'
import getAllDepartmentsFromRootDepartment from 'kit/helpers/marketing/getAllDepartmentsFromRootDepartment'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()
export default {
  components: {
    SingleDepartment
  },
  data() {
    return {
      isLoading: false,
      visible: false,
      departments: [],
      selectedDepartment: null,
      rootDepartment: null,
      defaultSelectedEmployees: [],
      breadcrumbDepartments: []
    }
  },

  methods: {
    async load() {
      this.isLoading = true
      const [err, r] = await marketingClient.merchantOrgTree({
        body: {
          withUserCount: true,
          withUser: true
        }
      })
      this.isLoading = false
      if (err) {
        handleError(err)
        return
      }
      const _rootDep = {
        id: 0,
        name: '根部门',
        children: [r.data]
      }

      this.rootDepartment = formatRootDepartment(_rootDep)
      this.departments = this.rootDepartment.children

      console.log('departments===', this.departments)

      this.breadcrumbDepartments = [this.rootDepartment]
    },

    select(v) {
      this.selectedDepartment = v
    },
    unselect() {
      this.selectedDepartment = null
    },

    handleClickDepartmentSubdivision(v) {
      console.log('v===', v)
      this.departments = v.children

      const allDepartments = getAllDepartmentsFromRootDepartment(
        this.rootDepartment
      )
      this.breadcrumbDepartments = []
      for (var c of v.parentDepartments) {
        this.breadcrumbDepartments.push(
          allDepartments.find(item => item.id === c.id)
        )
      }
      this.breadcrumbDepartments.push(v)
    },
    handleClickBreadcrumbDepartment(v) {
      //因为点击某个面包屑的部门时候 也相当于点击了它的下一级
      this.handleClickDepartmentSubdivision(v)

      const index = this.breadcrumbDepartments.findIndex(
        item => item.id === v.id
      )
      if (index !== -1) {
        this.breadcrumbDepartments = this.breadcrumbDepartments.slice(
          0,
          index + 1
        )
      }
    },
    search(keyword) {
      const allDepartments = getAllDepartmentsFromRootDepartment(
        this.rootDepartment
      )
      this.departments = allDepartments.filter(item =>
        item.name.includes(keyword)
      )
      if (!keyword) {
        this.departments = this.rootDepartment.children
      }
    },
    open() {
      this.visible = true
      this.load()
    },
    close() {
      this.visible = false
    },
    cancel() {
      this.selectedDepartment = null
      this.close()
    },
    confirm() {
      this.$emit('confirm', this.selectedDepartment)
      this.close()
    }
  }
}
</script>
<style>
.departmentSelectorDialog .el-dialog__header {
  display: none;
}
.departmentSelectorDialog .el-dialog__body {
  padding: 0;
}
</style>
