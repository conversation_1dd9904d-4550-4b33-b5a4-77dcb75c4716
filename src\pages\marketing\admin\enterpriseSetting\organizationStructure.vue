<template>
  <div
    class="organization"
    style="
      width: 100%;
      min-width: 1056px;
      height: calc(100vh - 78px);
      box-sizing: border-box;
    "
    v-loading="isLoading"
  >
    <div
      class="content"
      style="
        display: flex;
        background: #ffffff;
        box-sizing: border-box;
        border-radius: 8px 0 0 0;
      "
    >
      <div class="left_content" v-if="isExpand">
        <div
          style="
            display: flex;
            align-items: center;
            height: 49px;
            color: #1e2228ff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            padding-bottom: 12px;
            box-sizing: border-box;
            border-bottom: 1px solid #e4e7edff;
          "
        >
          组织架构
          <div class="left-arrow" v-if="isExpand">
            <i
              class="icon iconfont icon-direction-arrow-border-left"
              @click="retractExpand"
            ></i>
          </div>
        </div>
        <el-input
          style="margin-bottom: 16px"
          placeholder="部门/用户姓名"
          v-model="filterText"
        >
          <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <div class="showFilter" v-show="showFilter">
          <h3>部门列表</h3>
          <div v-if="departmentVoList.length !== 0" style="margin: 10px">
            <p
              style="
                margin-bottom: 8px;
                line-height: 30px;
                padding: 4px 14px;
                background: #f0f2f4;
                cursor: pointer;
                font-size: 14px;
                border-radius: 6px;
              "
              v-for="(item, index) in departmentVoList"
              :key="index"
              @click="searchDepartmentFetch(item)"
            >
              <span>{{ item.name }}</span>
            </p>
          </div>
          <div style="text-align: center; line-height: 50px" v-else>
            暂无数据
          </div>
          <h3 style="margin-bottom: 10px">人员列表</h3>
          <div v-if="personnelVoList.length !== 0">
            <p
              style="
                margin-bottom: 8px;
                line-height: 30px;
                padding: 4px 14px;
                background: rgb(240, 242, 244);
                cursor: pointer;
                font-size: 14px;
                border-radius: 6px;
              "
              v-for="(item, index) in personnelVoList"
              :key="index"
              @click="handleAddStaff('edit', item)"
            >
              <span>{{ item.name }}</span>
            </p>
          </div>
          <div style="text-align: center; line-height: 50px" v-else>
            暂无数据
          </div>
        </div>
        <div
          class="customer-webkit-scrollbar"
          v-show="!showFilter"
          style="height: calc(100vh - 211px); overflow-y: auto"
        >
          <el-tree
            ref="tree"
            :data="treeData"
            node-key="id"
            check-on-click-node
            highlight-current
            :default-expanded-keys="currentKeyLists"
            :expand-on-click-node="false"
            :current-node-key="currentKey"
            @node-click="handleNodeClick"
          >
            <span class="tree-node" slot-scope="{ data }">
              <i
                v-if="!data.parentDepartments.length"
                style="color: #828b9b; margin: 0 9px"
                class="icon iconfont icon-base-business-center"
              ></i>
              <i
                v-else
                style="color: #828b9b; margin: 0 9px"
                class="icon iconfont icon-application-hierarchy"
              ></i>
              <AutoEllipsisTooltip
                style="font-size: 14px"
                :content="data.name"
              />
              <el-dropdown
                trigger="click"
                placement="bottom"
                @command="handleAddDepartment"
                class="more"
              >
                <template>
                  <img src="kit/assets/images/more.png" alt="更多" />
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      command="编辑部门"
                      v-if="
                        data.parentDepartments && data.parentDepartments.length
                      "
                      >编辑</el-dropdown-item
                    >
                    <el-dropdown-item command="添加部门"
                      >添加子部门</el-dropdown-item
                    >
                    <el-dropdown-item
                      command="部门信息删除"
                      v-if="
                        data.parentDepartments && data.parentDepartments.length
                      "
                    >
                      删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </span>
          </el-tree>
        </div>
      </div>

      <div class="right_content">
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-bottom: 12px;
            box-sizing: border-box;
            border-bottom: 1px solid #e4e7ed;
          "
        >
          <div class="right-arrow" v-if="!isExpand">
            <i
              class="icon iconfont icon-direction-arrow-border-right"
              @click="retractExpand"
            ></i>
          </div>
          <div
            style="
              color: #1e2228ff;
              font-size: 18px;
              max-width: 500px;
              font-weight: 600;
            "
          >
            <AutoEllipsisTooltip :content="currentDepartment.name" />
          </div>
          <div class="staff_btn">
            <el-button
              style="color: #1e2228; font-weight: 400"
              @click="handleAddStaff('add')"
              >添加人员
            </el-button>
            <el-button
              style="color: #1e2228; font-weight: 400"
              @click="batchImport('user')"
              >批量导入人员</el-button
            >
            <el-button @click="batchImport('dept')" type="primary"
              >批量导入部门</el-button
            >
          </div>
        </div>
        <div
          class="customer-webkit-scrollbar"
          style="height: calc(100vh - 155px); overflow-y: auto"
        >
          <div class="secondTitle">
            <el-breadcrumb
              style="line-height: 28px"
              separator-class="el-icon-arrow-right"
            >
              <el-breadcrumb-item
                style="display: flex; align-items: center"
                v-for="(item, index) in breadcrumbs"
                :key="index"
              >
                <a
                  style="color: var(--color-primary)"
                  @click="clickBreadcrumbs(item, index)"
                  v-if="index !== breadcrumbs.length - 1"
                >
                  <AutoEllipsisTooltip :content="item.name" />
                </a>
                <div
                  style="color: #828b9b"
                  v-if="index == breadcrumbs.length - 1"
                >
                  <AutoEllipsisTooltip :content="item.name" />
                </div>
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div
            style="
              color: #1e2228ff;
              font-size: 16px;
              font-weight: 600;
              margin: 16px 0;
            "
          >
            下级部门
          </div>
          <el-button
            style="margin-bottom: 16px"
            type="primary"
            @click="handleAddDepartment('添加部门')"
          >
            添加子部门
          </el-button>
          <div class="department_table">
            <el-table
              :key="expandIndex"
              :data="departmentData"
              style="width: 100%"
              :header-cell-style="{ background: '#F0F2F4' }"
            >
              <el-table-column
                prop="name"
                label="子部门名称"
                width="218"
              ></el-table-column>
              <el-table-column
                prop="employeeTotal"
                label="子部门人数"
                sortable
              ></el-table-column>
            </el-table>
          </div>
          <div
            style="
              color: #1e2228ff;
              font-size: 16px;
              font-weight: 600;
              margin: 24px 0 16px;
            "
          >
            直属成员
          </div>

          <div :key="expandIndex">
            <el-table
              v-loading="loading"
              ref="multipleTable"
              :data="memberList"
              :header-cell-style="{ background: '#F0F2F4' }"
            >
              <el-table-column label="姓名" prop="user.name"></el-table-column>
              <el-table-column prop="user.mobile" label="手机号">
              </el-table-column>
              <el-table-column
                prop="roleNames"
                label="角色"
                show-overflow-tooltip
              />
              <el-table-column prop="disabled" label="用户状态">
                <template slot-scope="scope">
                  <div style="display: flex; align-items: center">
                    <i
                      class="dotClass"
                      :class="[scope.row.disabled ? 'grey' : 'green']"
                    ></i>
                    <span>{{ scope.row.disabled ? '禁用' : '启用' }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180">
                <template slot-scope="scope">
                  <div class="operation">
                    <el-button
                      type="text"
                      size="small"
                      @click="handleAddStaff('edit', scope.row)"
                      >编辑</el-button
                    >
                    <el-button
                      v-if="!scope.row.isAdmin"
                      type="text"
                      size="small"
                      @click="handleChangeStatus(scope.row)"
                      >{{ scope.row.disabled ? '启用' : '禁用' }}</el-button
                    >
                    <el-button
                      v-if="!scope.row.isAdmin"
                      type="text"
                      size="small"
                      @click="handleDeleteStaff(scope.row)"
                      >删除</el-button
                    >
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div
              class="pagination"
              style="margin: 30px 0 20px 22px; text-align: right"
            >
              <el-pagination
                class="pagination"
                :current-page.sync="searchForm.currPage"
                :page-size="searchForm.pageSize"
                :page-sizes="[10, 20, 50]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
    <BatchImport
      ref="batchImport"
      :importType="importType"
      @refresh="loadOrgTree"
    />
    <AddPerson
      ref="drawer"
      :userId="userId"
      :type="type"
      :currentCheckedDepartment="currentCheckedDepartment"
      @refresh="loadOrgTree"
    />
    <AddDepartment
      ref="departDrawer"
      :currentDepartment="currentDepartment"
      @refresh="loadOrgTree"
    />
  </div>
</template>

<script>
import formatRootDepartment from 'kit/formatters/marketing/formatRootDepartment'
import getAllDepartmentsFromRootDepartment from 'kit/helpers/marketing/getAllDepartmentsFromRootDepartment'
import handleError from 'kit/helpers/handleError'
import AutoEllipsisTooltip from 'kit/components/marketing/admin/autoEllipsisTooltip.vue'
import BatchImport from './organizationDialog/batchImport.vue'
import AddPerson from './organizationDialog/addPerson.vue'
import AddDepartment from './organizationDialog/addDepartment.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    AutoEllipsisTooltip,
    BatchImport,
    AddPerson,
    AddDepartment
  },
  data() {
    return {
      isLoading: false,
      isExpand: true, // 左侧树是否收起
      filterText: '',
      showFilter: false,
      departmentVoList: [],
      personnelVoList: [],
      treeData: [],
      currentKeyLists: [],
      currentKey: '',
      currentCheckedDepartment: [],
      expandIndex: 0,
      departmentData: [], // 下级部门列表
      memberList: [], // 直属成员列表
      searchForm: {
        currPage: 1,
        pageSize: 10,
        filters: { deptId: '' }
      }, // 分页参数
      total: 0, // 分页总数
      currentDepartment: {
        id: '',
        name: ''
      },
      breadcrumbs: [],
      userId: '',
      type: 'add',
      loading: false,
      importType: '', // 批量导入类型
      rootDepartment: null
    }
  },
  watch: {
    currentDepartment(val) {
      if (val.id) {
        this.getMemberList()
      }
    }
  },
  created() {
    this.loadOrgTree()
    this.$watch(
      'filterText',
      this.debounce(val => {
        if (val) {
          this.showFilter = true
          this.fetchDepartment(val)
        } else {
          this.showFilter = false
        }
      }, 500)
    )
  },
  methods: {
    debounce(fn, time) {
      let timer = null
      return function (...args) {
        if (timer) {
          clearTimeout(timer)
        }
        timer = setTimeout(() => {
          fn.apply(this, args)
        }, time)
      }
    },
    retractExpand() {
      this.isExpand = !this.isExpand
      this.expandIndex++
    },

    async loadOrgTree(id) {
      this.isLoading = true
      const [err, r] = await marketingClient.merchantOrgTree({
        body: {
          withUserCount: true,
          withUser: true
        }
      })
      this.isLoading = false
      if (err) {
        handleError(err)
        return
      }

      this.rootDepartment = formatRootDepartment(r.data)
      this.breadcrumbs = [this.rootDepartment]

      this.treeData = []
      this.treeData.push(r.data)
      const newDepartmentId = id ? id : r.data.id
      this.currentKey = newDepartmentId
      this.currentKeyLists = []
      this.currentKeyLists.push(newDepartmentId)

      this.currentCheckedDepartment = []
      this.currentCheckedDepartment.push(parseInt(newDepartmentId))

      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(this.currentKey)
        this.currentDepartment = this.$refs.tree.getCurrentNode()
        this.updateBreadcrumbs(this.currentDepartment)
        this.subordinateDepartmentData(this.$refs.tree.getCurrentNode())
      })
    },

    async fetchDepartment(val) {
      const [err, r] = await marketingClient.merchantQueryOrgAndMember({
        body: {
          keyword: val
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.departmentVoList = r.data.orgList
      this.personnelVoList = r.data.memberList
    },

    searchDepartmentFetch(item) {
      this.filterText = ''
      this.showFilter = false
      this.$refs.tree.setCurrentKey(item.id)
      this.subordinateDepartmentData(this.$refs.tree.getCurrentNode())
    },
    handleNodeClick(data) {
      //构建面包屑
      this.updateBreadcrumbs(data)

      this.currentKey = data.id
      this.currentCheckedDepartment = []
      this.currentCheckedDepartment.push(parseInt(data.id))
      this.subordinateDepartmentData(data)
    },

    updateBreadcrumbs(data) {
      if (data.parentDepartments && data.parentDepartments.length) {
        const allDepartments = getAllDepartmentsFromRootDepartment(
          this.rootDepartment
        )
        this.breadcrumbs = []
        for (var c of data.parentDepartments) {
          this.breadcrumbs.push(allDepartments.find(item => item.id === c.id))
        }
        this.breadcrumbs.push(data)
      }
    },
    // 下级部门列表
    subordinateDepartmentData(data) {
      this.currentDepartment = data
      if (!data.children) {
        this.departmentData = []
      } else {
        this.departmentData = data.children
      }
    },

    handleAddDepartment(title) {
      this.currentDepartment = this.$refs.tree.getCurrentNode()
      if (title === '部门信息删除') {
        this.handleDelete()
      } else {
        this.$refs.departDrawer.open(title)
      }
    },

    handleDelete() {
      this.$confirm('是否确认删除部门？', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false,
        closeOnPressEscape: false
      }).then(async res => {
        if (this.currentDepartment.employeeTotal) {
          this.$message.error('部门下存在人员，请先删除人员或调整部门！')
          return
        }
        const [err, r] = await marketingClient.merchantDeleteOrg({
          body: {
            id: this.currentDepartment.id
          }
        })
        if (err) {
          handleError(err)
          return
        }
        this.$message.success('删除成功')
        this.currentDepartment = {}
        this.loadOrgTree()
      })
    },

    batchImport(type) {
      this.importType = type
      this.$refs.batchImport.open()
    },

    clickBreadcrumbs(item, index) {
      this.breadcrumbs = this.breadcrumbs.slice(0, index + 1)
      this.$refs.tree.setCurrentKey(item.id)
      this.subordinateDepartmentData(this.$refs.tree.getCurrentNode())
    },
    updatePerson() {
      this.getMemberList()
      this.filterText = ''
    },

    handleSizeChange(val) {
      this.searchForm.pageSize = val
      this.searchForm.currPage = 1
      this.getMemberList()
    },
    handleCurrentChange(currentPage) {
      this.getMemberList(currentPage)
    },

    async getMemberList(currentPage) {
      var page = currentPage ? currentPage : 1
      const [err, r] = await marketingClient.merchantQueryOrgMember({
        body: {
          filters: {
            organizationId: this.currentDepartment.id
          },
          start: page,
          limit: this.searchForm.pageSize,
          sorts: []
        }
      })
      if (err) {
        handleError(err)
        return
      }

      r.data.list.forEach(item => {
        if (!item.roleList || !item?.roleList?.length)
          return (item.roleNames = '-')
        item.roleNames = item.roleList.map(item => item.name).join('、')
      })
      this.memberList = r.data.list
      this.total = r.data.total
    },
    handleAddStaff(type, row) {
      this.type = type
      this.$refs.drawer.open()
      if (type === 'edit') {
        this.userId = row.id ? row.id : row.user.id
      }
    },
    async handleDeleteStaff(row) {
      this.$confirm('是否确认删除成员？', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false,
        closeOnPressEscape: false
      }).then(async res => {
        const [err, r] = await marketingClient.merchantDeleteMember({
          body: {
            id: row.user.id
          }
        })
        if (err) {
          handleError(err)
          return
        }
        this.$message.success('删除成功')
        this.loadOrgTree(this.currentKey)
        this.getMemberList()
      })
    },

    async handleChangeStatus(row) {
      let message = row.disabled
        ? '您确认启用该人员吗？'
        : '您确认停用该人员吗？'
      this.$confirm(message, '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false,
        closeOnPressEscape: false
      }).then(async res => {
        if (row.disabled) {
          const [err, r] = await marketingClient.merchantEnableMember({
            body: {
              id: row.user.id
            }
          })
          if (err) {
            handleError(err)
            return
          }

          this.$message.success('启用成功')
          this.getMemberList()
          return
        }
        const [err2, r2] = await marketingClient.merchantDisableMember({
          body: {
            id: row.user.id
          }
        })
        if (err2) {
          handleError(err2)
          return
        }
        this.$message.success('禁用成功')
        this.getMemberList()
      })
    }
  }
}
</script>

<style scoped>
.left_content {
  width: 296px;
  padding: 12px 24px 0;
  box-sizing: border-box;
  border-right: 1px solid #e4e7ed;
  position: relative;
  flex-shrink: 0;
}
.left-arrow {
  display: flex;
  align-items: center;
  width: 16px;
  height: 32px;
  border-radius: 8px 0 0 8px;
  opacity: 1;
  border-top: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  border-left: 1px solid #e4e7ed;
  background: #ffffff;
  position: absolute;
  right: -1px;
}
.right-arrow {
  display: flex;
  align-items: center;
  width: 16px;
  height: 32px;
  border-radius: 0 8px 8px 0;
  opacity: 1;
  border-top: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
  background: #ffffff;
  position: absolute;
  left: -24px;
}
.right_content {
  flex: 1;
  padding: 12px 24px 0;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}
.tree-node {
  display: flex;
  align-items: center;
  margin-right: 5px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep .el-tree-node__content {
  height: 40px;
}
::v-deep .el-table tr {
  color: #1e2228ff;
}
::v-deep .el-breadcrumb__inner {
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.dotClass {
  width: 6px;
  height: 6px;
  display: block;
  margin-right: 8px;
  border-radius: 100px;
}
.grey {
  background: #cad0db;
}
.green {
  background: #07bb06;
}
.more {
  position: absolute;
  right: 8px;
}
::v-deep
  .content
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content,
.content .el-tree-node:focus > .el-tree-node__content,
.content .el-tree-node__content:hover {
  background: #fff3e8 !important;
}

::v-deep .el-table__header {
  width: 100% !important;
}
::v-deep .el-table__body {
  width: 100% !important;
}
</style>
