<template>
  <el-button
    class="button"
    :class="[buttonDisabled ? 'color' : 'disableColor']"
    :loading="loading"
    :disabled="buttonDisabled"
    @click="getCode"
  >
    {{ buttonText }}
  </el-button>
</template>
<script>
import handleError from 'kit/helpers/handleError'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()
var codeTimer = null

export default {
  props: {
    cellPhone: String,
    verifyCode: String,
    verifyCodeId: String
  },
  data() {
    return {
      loading: false,
      buttonText: '获取验证码',
      buttonDisabled: false,
      codeCountDown: 60,
      smsToken: ''
    }
  },
  methods: {
    async getCode() {
      if (this.buttonDisabled) {
        return
      }
      if (!this.cellPhone) {
        handleError('请输入手机号')
        return
      }
      if (!this.verifyCode || !this.verifyCodeId) {
        handleError('请先填写图形验证码')
        return
      }
      this.loading = true
      this.buttonDisabled = true
      const [err, r] = await marketingClient.smsSendSmsCode({
        body: {
          mobile: this.cellPhone,
          captcha: this.verifyCode,
          captchaToken: this.verifyCodeId
        }
      })

      if (err) {
        if (err.errorCode === 5) {
          this.$emit('refreshCaptcha')
        }
        handleError(err)
        this.loading = false
        this.buttonDisabled = false
        return
      }
      this.loading = false
      this.$emit('getSmsCode', r.data.smsToken)
      this.startCodeCountDown()
    },
    startCodeCountDown() {
      this.buttonText = `${this.codeCountDown} 秒后重新获取`

      codeTimer = setInterval(() => {
        this.codeCountDown--
        if (this.codeCountDown === 0) {
          clearInterval(codeTimer)
          this.buttonDisabled = false
          this.buttonText = '获取验证码'
          this.codeCountDown = 60
          this.$emit('refreshCaptcha')
        } else {
          this.buttonText = `${this.codeCountDown} 秒后重新获取`
        }
      }, 1000)
    }
  }
}
</script>
<style scoped>
.button {
  min-width: 0;
  padding: 0;
  border: none;
  height: 22px;
  line-height: 22px;
  margin-right: 12px;
  position: absolute;
  top: 10px;
  right: 0;
  color: #a6aebd;
  font-weight: 400;
}
::v-deep.button.is-loading {
  position: absolute !important;
}
.color {
  color: #a6aebd;
}
.disableColor {
  color: #f77234ff;
}
</style>
