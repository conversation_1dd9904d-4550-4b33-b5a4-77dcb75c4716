<template>
  <div
    class="customer-webkit-scrollbar"
    style="
      min-width: 1056px;
      height: calc(100vh - 78px);
      background: #ffffff;
      overflow: auto;
    "
  >
    <div class="header">
      <div class="pic-left">
        <img width="500px" src="kit/assets/images/Frame_left.png" alt="" />
      </div>
      <div style="flex: 1">
        <div class="pic-right"></div>
      </div>
    </div>
    <div class="tabs">
      <el-tabs style="width: 100%" v-model="activeName">
        <el-tab-pane label="营业策划" name="first"></el-tab-pane>
        <el-tab-pane label="活动执行" name="second"></el-tab-pane>
        <el-tab-pane label="活动反馈" name="third"></el-tab-pane>
      </el-tabs>
    </div>
    <div v-if="activeName === 'first'">
      <div class="content">
        <div class="item" style="margin-right: 24px">
          <span class="title">活动目标</span>
          <p style="font-size: 14px; color: #4e5769ff; margin-top: 8px">
            KPL、应用预算、效果预测
          </p>
        </div>
        <div class="item" style="margin-right: 24px">
          <span class="title">目标用户</span>
          <div class="content-box">
            <div>银行白名单用户</div>
            <div>系统自动鉴权</div>
          </div>
        </div>
        <div class="item">
          <span class="title">活动范围</span>
          <div style="font-size: 14px; margin-top: 8px; line-height: 22px">
            <div style="color: #1e2228ff">
              线上商城：<span style="color: #4e5769ff"
                >领券、售券、积分商城兑换、游戏抽奖...</span
              >
            </div>
            <div style="color: #1e2228ff">
              线下商圈：<span style="color: #4e5769ff"
                >银行、联通及第三方商户；
                按行业、地域、所属网点、档次等筛选</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="item" style="margin-right: 24px">
          <span class="title">活动形式</span>
          <div style="font-size: 14px; margin-top: 8px; line-height: 22px">
            <div style="color: #1e2228ff">
              线上：<span style="color: #4e5769ff"
                >领券、售券、积分商城兑换、游戏抽奖...</span
              >
            </div>
            <div style="color: #1e2228ff">
              线下：<span style="color: #4e5769ff"
                >客户经理发券/积分、扫码领券；积分消费、折扣满减、消费返券...</span
              >
            </div>
          </div>
        </div>
        <div class="item" style="margin-right: 24px">
          <span class="title">活动规则</span>
          <div style="font-size: 14px; margin-top: 8px; line-height: 22px">
            <div style="color: #1e2228ff">
              目标：<span style="color: #4e5769ff"
                >领券、售券、积分商城兑换、游戏抽奖...</span
              >
            </div>
            <div style="color: #1e2228ff">
              条件：<span style="color: #4e5769ff"
                >人数、次数、总量、时点、时段控制</span
              >
            </div>
          </div>
        </div>
        <div class="item">
          <span class="title">宣传推广</span>
          <div style="font-size: 14px; margin-top: 8px; line-height: 22px">
            <div style="color: #1e2228ff">
              线上：<span style="color: #4e5769ff"
                >公众号、短信、朋友圈、视频直播...</span
              >
            </div>
            <div style="color: #1e2228ff">
              线下：<span style="color: #4e5769ff"
                >银行网点及商户物料、支付广告</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="activeName === 'second'">
      <div class="content">
        <div class="item" style="margin-right: 24px">
          <span class="title">活动配置</span>
          <div style="font-size: 14px; margin-top: 8px; line-height: 22px">
            <div style="color: #1e2228ff">
              活动配置：<span style="color: #4e5769ff"
                >微信、支付宝、云闪付、联通活动</span
              >
            </div>
            <div style="color: #1e2228ff">
              活动上架：<span style="color: #4e5769ff"
                >公众号、小程序、银行APP特色商圈</span
              >
            </div>
          </div>
        </div>

        <div class="item" style="margin-right: 24px">
          <span class="title">活动预热</span>
          <div class="content-box">
            <div>物料投放、线上传播(公众号、朋友圈等)、支付后广告配置</div>
          </div>
        </div>
        <div class="item">
          <span class="title">活动路演</span>
          <div class="content-box">
            <div>产品培训、现场驻点、活动推广</div>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="item" style="margin-right: 24px">
          <span class="title">活动监控</span>
          <div style="font-size: 14px; margin-top: 8px; line-height: 22px">
            <div style="color: #4e5769ff">库存调整、活动策略变更</div>
            <div style="color: #1e2228ff">
              风险控制：<span style="color: #4e5769ff"
                >防羊毛党、机器攻击等</span
              >
            </div>
          </div>
        </div>
        <div class="item" style="margin-right: 24px">
          <span class="title">资金结算</span>
          <div class="content-box">
            <div>预付模式及后付模式</div>
          </div>
        </div>
        <div class="item" style="opacity: 0" />
      </div>
    </div>
    <div v-if="activeName === 'third'">
      <div class="content">
        <div class="item" style="margin-right: 24px">
          <span class="title">商户端</span>
          <div class="content-box">
            <div>实时查看活动效果</div>
          </div>
        </div>

        <div class="item" style="margin-right: 24px">
          <span class="title">银行数据看板</span>
          <div class="content-box">
            <div>预算余额监控</div>
            <div>活动实时数据查看</div>
            <div>活动统计报表</div>
          </div>
        </div>
        <div class="item">
          <span class="title">H5营销账单</span>
          <div class="content-box">
            <div>用户、商户、银行年度营销报告分析</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeName: 'first'
    }
  }
}
</script>

<style scoped>
.header {
  display: flex;
  height: 300px;
  background-image: url('kit/assets/images/<EMAIL>');
  background-size: cover;
}
.pic-left {
  margin: auto 70px;
  /* background-image: url('kit/assets/images/Frame_left.png'); */
  /* background-size: cover; */
}
.pic-right {
  width: 400px;
  height: 220px;
  margin: 79px auto 0;
  background-image: url('kit/assets/images/Frame_right.png');
  background-size: cover;
}
.tabs {
  display: flex;
  align-items: center;
  justify-content: space-around;
  color: #1e2228ff;
  font-size: 14px;
  background: #ffffff;
  border-bottom: 1px solid #e4e7edff;
}
.content {
  display: flex;
  padding: 24px 24px 0;
}
.content-box {
  font-size: 14px;
  color: #4e5769ff;
  margin-top: 8px;
  line-height: 22px;
}
.item {
  width: 390px;
  height: 140px;
  padding: 20px 24px 0;
  box-sizing: border-box;
  border-radius: 8px;
  opacity: 1;
  background: #f7f9fcff;
}
.title {
  color: #1e2228ff;
  font-size: 18px;
  font-weight: 600;
}
::v-deep .el-tabs__nav {
  width: 100%;
  display: flex;
  padding: 0 108px;
  box-sizing: border-box;
  gap: 32px;
}
::v-deep .el-tabs__header {
  margin: 0;
}
::v-deep .el-tabs__item {
  text-align: center;
  flex: 1;
  font-weight: 600;
}
::v-deep .el-tabs__item.is-active {
  border-bottom: 2px solid var(--o-primary-color);
}
::v-deep .el-tabs__active-bar {
  display: none;
}
</style>
