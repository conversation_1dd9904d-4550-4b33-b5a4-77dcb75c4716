<template>
  <div class="login-bg" style="width: 100%; position: relative">
    <img
      class="img-size"
      width="160px"
      style="position: absolute; left: 128px; top: 28px"
      src="kit/assets/images/<EMAIL>"
    />
    <div class="content" style="display: flex; height: 100%">
      <img
        style="height: 463px; position: absolute; left: 100px; bottom: 13%"
        width="634px"
        src="kit/assets/images/pic.png"
      />
      <div class="login-form">
        <div>
          <div
            style="
              color: #f77234ff;
              font-size: 24px;
              font-weight: 600;
              text-align: center;
              margin-bottom: 28px;
            "
          >
            厚客营销系统登录
          </div>
          <el-form :model="loginForm" :rules="loginFormRules" ref="loginForm">
            <el-form-item prop="cellPhone">
              <el-input
                v-model="loginForm.cellPhone"
                placeholder="请输入手机号"
                maxlength="11"
              ></el-input>
            </el-form-item>

            <el-form-item prop="verifyCode">
              <el-input
                v-model="loginForm.verifyCode"
                placeholder="请输入图形验证码"
                maxlength="4"
              />
              <img class="captcha" @click="loadCaptcha" :src="src" alt />
            </el-form-item>
            <el-form-item prop="code">
              <Input
                v-model="loginForm.code"
                :allowZero="true"
                valueType="int"
                placeholder="请输入短信验证码"
                maxlength="6"
              />
              <VaildCode
                :cellPhone="loginForm.cellPhone"
                :verifyCode="loginForm.verifyCode"
                :verifyCodeId="loginForm.verifyCodeId"
                @getSmsCode="getSmsCode"
                @refreshCaptcha="loadCaptcha"
              />
            </el-form-item>
            <el-button
              style="width: 100%; margin: 16px 0; border-radius: 6px"
              type="primary"
              :loading="loading"
              @click="login"
              >登 录</el-button
            >
          </el-form>
          <div style="font-size: 14px">
            <span style="color: #1e2228ff">没有账号？</span>
            <a
              style="color: #f77234ff; cursor: pointer"
              @click="$router.push('/register')"
              >立即注册</a
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Input from 'kit/components/marketing/admin/input.vue'
import VaildCode from './getValidCode.vue'
import loginImg from 'kit/assets/images/loginImage.png'
import { validateImgCode, validateTel } from './util/index.js'
import { loadProfile } from './util/profile'
import { setToken } from 'kit/helpers/token'
import store from 'kit/helpers/store'
import handleError from 'kit/helpers/handleError'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { VERIFICATION_IMG_CODE_REGEX } from 'kit/helpers/regexp'
const marketingClient = makeMarketingClient()

export default {
  components: {
    Input,
    VaildCode
  },
  data() {
    return {
      loginImg,
      loginForm: {
        cellPhone: '',
        code: '',
        verifyCode: '',
        verifyCodeId: ''
      },
      loginFormRules: {
        cellPhone: [
          { validator: validateTel, required: true, trigger: 'blur' }
        ],
        verifyCode: [
          { required: true, trigger: 'blur', message: '请输入图形验证码' },
          { validator: validateImgCode, required: true, trigger: 'blur' }
        ],
        code: [{ required: true, trigger: 'blur', message: '请输入短信验证码' }]
      },
      smsToken: '',
      loading: false
    }
  },
  computed: {
    src() {
      const baseUrl = window.env.api
      if (!baseUrl) {
        handleError('无法获取平台的API地址')
        return
      }
      return `${baseUrl}/marketing/sms/imageCaptcha?token=${encodeURIComponent(
        this.loginForm.verifyCodeId
      )}`
    }
  },
  created() {
    this.loadCaptcha()
    if (store.get('mobile')) {
      this.loginForm.cellPhone = store.get('mobile')
    }
  },
  methods: {
    async loadCaptcha() {
      const [err, r] = await marketingClient.smsCreateImageCaptcha()
      if (err) {
        handleError(err)
        return
      }
      this.loginForm.verifyCodeId = r.data
    },
    getSmsCode(val) {
      this.smsToken = val
    },
    async login() {
      await this.$refs.loginForm.validate()
      this.loading = true
      const [err, r] = await marketingClient.userSmsLogin({
        body: {
          mobile: this.loginForm.cellPhone,
          smsCode: this.loginForm.code,
          smsToken: this.smsToken
        }
      })

      if (err) {
        handleError(err)
        this.loading = false
        return
      }

      this.loading = false
      setToken(r.data.token)
      store.set('mobile', this.loginForm.cellPhone)
      await loadProfile()
      window.location.href = `${this.$router.options.base}/introduction`
      // this.$router.push('/introduction')
    }
  }
}
</script>

<style scoped>
.login-bg {
  background-image: url('kit/assets/images/<EMAIL>');
  background-size: cover;
}
.login-form {
  width: 440px;
  height: 540px;
  position: absolute;
  right: 15%;
  top: 12%;
  box-sizing: border-box;
  padding: 56px 44px 158px;
  background-color: #fff;
  border-radius: 13px;
}
.captcha {
  position: absolute;
  width: 80px;
  height: 30px;
  right: 10px;
  top: 5px;
}
::v-deep .el-form-item {
  margin-bottom: 16px;
}
</style>
