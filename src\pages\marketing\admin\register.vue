<template>
  <div class="login-bg" style="width: 100%; position: relative">
    <img
      width="160px"
      style="position: absolute; left: 128px; top: 28px"
      src="kit/assets/images/<EMAIL>"
    />
    <div class="content" style="height: 100%">
      <img
        style="height: 463px; position: absolute; left: 100px; bottom: 13%"
        width="634px"
        src="kit/assets/images/pic.png"
      />
      <div class="register-form">
        <div
          style="
            color: #f77234ff;
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 32px;
          "
        >
          注册企业
        </div>
        <el-form :model="registerForm" :rules="rules" ref="registerForm">
          <el-form-item prop="name">
            <Input
              v-model="registerForm.name"
              :allowZero="true"
              :trim="true"
              placeholder="请输入公司名称"
              maxlength="50"
            />
          </el-form-item>
          <el-form-item prop="adminName">
            <Input
              v-model="registerForm.adminName"
              :trim="true"
              placeholder="请输入主管理员姓名"
              maxlength="20"
            />
          </el-form-item>
          <el-form-item prop="cellPhone">
            <el-input
              v-model="registerForm.cellPhone"
              placeholder="请输入主管理员手机号"
              maxlength="11"
            >
            </el-input>
          </el-form-item>
          <el-form-item prop="verifyCode">
            <el-input
              v-model="registerForm.verifyCode"
              placeholder="请输入图形验证码"
              maxlength="4"
            />
            <img class="captcha" @click="loadCaptcha" :src="src" alt />
          </el-form-item>
          <el-form-item prop="code">
            <Input
              v-model="registerForm.code"
              :allowZero="true"
              valueType="int"
              placeholder="请输入短信验证码"
              maxlength="6"
              @change="check"
            />
            <VaildCode
              :cellPhone="registerForm.cellPhone"
              :verifyCode="registerForm.verifyCode"
              :verifyCodeId="registerForm.verifyCodeId"
              @getSmsCode="getSmsCode"
              @refreshCaptcha="loadCaptcha"
            ></VaildCode>
          </el-form-item>
          <el-form-item class="agreement" prop="agreement">
            <el-checkbox name="type" v-model="registerForm.agreement">
              同意
            </el-checkbox>
            <a style="color: #f77234; cursor: pointer" @click="showAgreement"
              >《用户注册服务协议》</a
            >
          </el-form-item>
          <el-button
            style="width: 100%; margin: 16px 0; border-radius: 6px"
            type="primary"
            :loading="loading"
            @click="register"
            >创建企业并登录</el-button
          >
        </el-form>
        <div style="font-size: 14px">
          <span style="color: #1e2228ff">已有账号?</span>
          &nbsp;
          <a
            style="color: #f77234ff; cursor: pointer"
            @click="$router.push('/login')"
            >去登录</a
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import VaildCode from './getValidCode.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import { validateImgCode, validateTel } from './util/index.js'
import { loadProfile } from './util/profile'
import { setToken } from 'kit/helpers/token'
import store from 'kit/helpers/store'
import handleError from 'kit/helpers/handleError'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

import makePlatformClient from 'kit/services/platform/makeClient'
import { delay } from 'kit/helpers/delay'
const platformClient = makePlatformClient()

export default {
  components: {
    VaildCode,
    Input
  },
  data() {
    return {
      registerForm: {
        cellPhone: '',
        code: '',
        verifyCode: '',
        verifyCodeId: '',
        agreement: false
      },
      rules: {
        name: [{ required: true, trigger: 'blur', message: '请输入公司名称' }],
        adminName: [
          { required: true, trigger: 'blur', message: '请输入主管理员姓名' }
        ],
        cellPhone: [
          { required: true, trigger: 'blur', message: '请输入主管理员手机号' },
          { validator: validateTel }
        ],
        verifyCode: [
          { required: true, trigger: 'blur', message: '请输入图形验证码' },
          { validator: validateImgCode, required: true, trigger: 'blur' }
        ],
        code: [{ required: true, trigger: 'blur', message: '请输入短信验证码' }]
      },
      smsToken: '',
      loading: false
    }
  },
  computed: {
    src() {
      const baseUrl = window.env.api
      if (!baseUrl) {
        handleError('无法获取平台的API地址')
        return
      }
      return `${baseUrl}/marketing/sms/imageCaptcha?token=${encodeURIComponent(
        this.registerForm.verifyCodeId
      )}`
    }
  },
  async created() {
    this.loadCaptcha()
  },
  methods: {
    async loadCaptcha() {
      const [err, r] = await marketingClient.smsCreateImageCaptcha()
      if (err) {
        handleError(err)
        return
      }
      this.registerForm.verifyCodeId = r.data
    },
    check() {
      if (
        this.registerForm.name &&
        this.registerForm.adminName &&
        this.registerForm.cellPhone &&
        this.registerForm.verifyCode &&
        !this.smsToken
      ) {
        this.$message.warning('请先获取短信验证码')
      }
    },
    //获取短信验证码
    getSmsCode(val) {
      this.smsToken = val
    },

    showAgreement() {
      window.open(`${this.$router.options.base}/privacyPolicy.html`)
    },

    async register() {
      await this.$refs.registerForm.validate()
      if (!this.registerForm.agreement) {
        this.$message.warning('请先勾选用户注册服务协议')
        return
      }
      this.loading = true
      const [err, r] = await marketingClient.merchantRegister({
        body: {
          name: this.registerForm.name,
          adminName: this.registerForm.adminName,
          mobile: this.registerForm.cellPhone,
          smsCode: this.registerForm.code,
          smsToken: this.smsToken
        }
      })
      if (err) {
        handleError(err)
        this.loading = false
        return
      }
      await delay(1000)
      setToken(r.data.token)
      store.set('mobile', this.registerForm.cellPhone)
      await loadProfile()
      window.location.href = `${this.$router.options.base}/introduction`
      this.loading = false
      // this.$router.push('/introduction')
    }
  }
}
</script>
<style scoped>
.login-bg {
  background-image: url('kit/assets/images/<EMAIL>');
  background-size: cover;
}
.register-form {
  width: 440px;
  height: 588px;
  position: absolute;
  right: 15%;
  top: 12%;
  box-sizing: border-box;
  padding: 56px 44px 56px;
  background-color: #fff;
  border-radius: 13px;
}
.captcha {
  position: absolute;
  width: 80px;
  height: 30px;
  right: 10px;
  top: 5px;
}
::v-deep .el-form-item {
  margin-bottom: 16px;
}
::v-deep .agreement .el-form-item__content {
  line-height: 22px;
}
</style>
