<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="560px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      custom-class="marketingAdminSelectEmployeeDialog"
      :append-to-body="true"
    >
      <DepartmentEmployeeSelector
        :title="title"
        v-loading="isLoading"
        placeholder="请输入员工姓名"
        :departments="departments"
        :employees="employees"
        :breadcrumbDepartments.sync="breadcrumbDepartments"
        :selectedEmployee="selectedEmployee"
        @select="select"
        @unselect="unselect"
        @clickDepartmentSubdivision="handleClickDepartmentSubdivision"
        @clickBreadcrumbDepartment="handleClickBreadcrumbDepartment"
        @confirm="confirm"
        @cancel="cancel"
        @search="search"
      />
    </el-dialog>
  </div>
</template>

<script>
import DepartmentEmployeeSelector from 'kit/components/ui/picker/singleEmployeeInsideDepartment.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import handleError from 'kit/helpers/handleError.js'
import formatRootDepartment from 'kit/formatters/marketing/formatRootDepartment'
import formatDepartmentsEmployees from 'kit/formatters/marketing/formatDepartmentsEmployees'
import deepClone from 'kit/helpers/deepClone'
const marketingClient = makeMarketingClient()

const getMerchantOrgTree = async () => {
  const [err, result] = await marketingClient.merchantOrgTree({
    body: {
      withUserCount: true,
      withUser: true
    }
  })
  if (err) {
    handleError(err)
    return
  }
  return result.data
}

export default {
  components: {
    DepartmentEmployeeSelector
  },
  props: {
    title: {
      type: String,
      default: '选择推广人员'
    },
    // 回显会用到，选择的人员数据
    selectEmployee: {
      type: Object,
      default: () => {}
    },
    // 禁用的人员列表
    employeeDisableIds: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    selectEmployee: {
      deep: true,
      handler(value) {
        this.selectedEmployee = {
          ...value
        }
      }
    },
    employeeDisableIds: {
      deep: true,
      handler() {
        this.setEmployeeDisableIds()
      }
    },
    dialogVisible(value) {
      if (value) {
        this.cacheSelectedEmployee = deepClone(this.selectedEmployee)
      }
    }
  },
  data() {
    return {
      cacheSelectedEmployee: {},
      isLoading: false,
      departments: [],
      employees: [],
      selectedEmployee: { ...this.selectEmployee },
      dialogVisible: false,
      breadcrumbDepartments: [],
      rootDepartment: null,
      allEmployees: null
    }
  },
  async created() {
    await this.loadTree()
    this.setEmployeeDisableIds()
  },
  methods: {
    setEmployeeDisableIds() {
      this.employees.forEach(item => {
        this.$set(item, 'disabled', this.employeeDisableIds.includes(item.id))
      })
    },
    async loadTree() {
      this.isLoading = true
      const data = await getMerchantOrgTree()
      this.isLoading = false
      this.rootDepartment = formatRootDepartment(data)
      this.departments = this.rootDepartment.children
      this.breadcrumbDepartments.push(this.rootDepartment)
      this.allEmployees = formatDepartmentsEmployees(data)
      this.employees = this.allEmployees.filter(
        item => item.department.id === this.rootDepartment.id
      )
    },
    open(show = true) {
      this.dialogVisible = show
    },
    confirm() {
      this.$emit('confirm', this.selectedEmployee)
      this.open(false)
    },
    cancel() {
      // this.selectedEmployee = null
      this.selectedEmployee = this.cacheSelectedEmployee
      this.open(false)
    },
    handleClickDepartmentSubdivision(item) {
      this.departments = item.children
      this.employees = item.userList
    },
    handleClickBreadcrumbDepartment(item) {
      this.departments = item.children
      this.employees = item.userList
    },
    search(keyword) {
      const newKeyword = keyword.trim()
      if (!newKeyword) {
        this.employees =
          this.breadcrumbDepartments[
            this.breadcrumbDepartments.length - 1
          ].userList
        return
      }
      this.employees = this.allEmployees.filter(item =>
        item.name.includes(newKeyword)
      )
    },
    unselect(v) {
      this.selectedEmployee = null
    },
    select(item) {
      this.selectedEmployee = item
    }
  }
}
</script>
<style>
.marketingAdminSelectEmployeeDialog .el-dialog__header,
.marketingAdminSelectEmployeeDialog
  .search
  .el-input__icon.el-input__validateIcon.el-icon-circle-check {
  display: none;
}
.marketingAdminSelectEmployeeDialog .el-dialog__body {
  padding: 0;
}
.marketingAdminSelectEmployeeDialog .search .el-input__validateIcon {
  display: none;
}
</style>
