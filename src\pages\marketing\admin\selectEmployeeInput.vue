<template>
  <div>
    <SelectEmployeeDialog
      ref="SelectEmployeeDialog"
      @confirm="confirm"
      :employeeDisableIds="employeeDisableIds"
      :selectEmployee="selectEmployee"
    />
    <div class="input-box" @click="handleClick" :class="{ disabled }">
      <span class="name" v-if="selectEmployee.name">{{
        selectEmployee.name
      }}</span>
      <span class="placeholder" v-else>{{ placeholder }}</span>
      <div
        class="close"
        @click.prevent.stop="handleClearClick"
        v-if="selectEmployee.name"
      >
        <i class="el-icon-circle-close"></i>
      </div>
    </div>
  </div>
</template>

<script>
import SelectEmployeeDialog from 'kit/pages/marketing/admin/selectEmployeeDialog.vue'

export default {
  components: {
    SelectEmployeeDialog
  },
  props: {
    name: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择人员'
    },
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    employeeDisableIds: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    name(newValue) {
      this.selectEmployee.name = newValue
    },
    value(newValue) {
      this.selectEmployee.id = newValue
    }
  },
  data() {
    return {
      selectEmployee: {
        id: this.value,
        name: this.name
      }
    }
  },
  methods: {
    handleClick() {
      if (this.disabled) return
      this.$refs.SelectEmployeeDialog.open()
    },
    confirm(item) {
      this.$emit('update:selectEmployee', {
        ...item
      })
      this.$emit('update:name', item?.name)
      this.onInput(item?.id)
    },
    handleClearClick() {
      this.$emit('update:selectEmployee', {})
      this.$emit('update:name', null)
      this.onInput(null)
    },
    onInput(value) {
      this.$emit('input', value)
      this.$nextTick(this.validate)
    },
    validate() {
      const prop = this?.$parent?.prop
      this.$parent?.elForm?.validateField(prop)
    }
  }
}
</script>
<style scoped>
.input-box {
  height: 32px;
  border-radius: 6px;
  opacity: 1;
  border: 1px solid #dee1e7;
  padding: 0 12px;
  padding-right: 0;
  box-sizing: border-box;
  line-height: 32px;
  cursor: pointer;
  background: #fff;
  display: flex;
  align-items: center;
}
.is-error .input-box,
.is-error .input-box:hover {
  border-color: #f56c6c;
}
.input-box:hover {
  border-color: #c0c4cc;
}

.input-box.disabled {
  background: #f5f8fb;
}
.input-box.disabled:hover {
  cursor: not-allowed;
}
.input-box .close {
  color: #cad0db;
  font-size: 14px;
  display: none;
  padding: 0 12px;
}
.input-box:hover .close {
  display: block;
}
.input-box.disabled:hover .close {
  display: none;
}
.input-box .placeholder,
.input-box .name {
  color: #c3c7ce;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  flex: 1;
}
.input-box .name {
  color: #1e2228;
}
</style>
