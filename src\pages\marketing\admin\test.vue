<template>
  <Container :back="false">
    <main style="padding: 24px">
      <section>
        <FormGroupTitle class="m-b-10">单选员工</FormGroupTitle>
        <div class="m-b-10">
          <span>姓名： {{ employeeName }} </span>
          <span>员工ID：{{ employeeId }}</span>
        </div>
        <el-button @click="handlePushEmployeeDisableIdsClick"
          >禁用人员ID {{ employeeDisableIds }}</el-button
        >
        <el-button @click="handleDeleteEmployeeDisableIdsClick"
          >删除禁用人员ID</el-button
        >
        <SelectEmployeeInput
          :employeeDisableIds="employeeDisableIds"
          :name.sync="employeeName"
          v-model="employeeId"
        />
      </section>

      <section>
        <FormGroupTitle class="m-b-10">copyText</FormGroupTitle>
        <el-button @click="copyText('http://nn22n.com')">复制链接</el-button>
      </section>

      <section>
        <FormGroupTitle class="m-b-10">步骤展示组件</FormGroupTitle>
        <TopProgressBar
          :steps="topProgressBarSteps"
          :value="String(topProgressBarValue)"
        />
        <p style="padding:10px 0;">topProgressBarValue:{{ topProgressBarValue }}</p>
        <el-button @click="handleTopProgressBarValueClick">切换</el-button>
      </section>
      <section>
        <FormGroupTitle class="m-b-10">导入提示</FormGroupTitle>
        <FileImportPrompt :successCount="0" :failCount="22" failFileURL="" />
      </section>

      <FormGroupTitle class="m-b-10">按钮loading</FormGroupTitle>
      <Button :click="handleSaveClick">确定</Button>
    </main>
  </Container>
</template>

<script>
import Button from 'kit/components/marketing/admin/button.vue'
import SelectEmployeeInput from 'kit/pages/marketing/admin/selectEmployeeInput.vue'
import FileImportPrompt from 'kit/components/marketing/admin/fileImportPrompt.vue'
import TopProgressBar from 'kit/components/marketing/admin/topProgressBar.vue'
import FormGroupTitle from 'kit/components/marketing/admin/formGroupTitle.vue'
import Container from 'kit/components/marketing/admin/container.vue'

import copyText from 'kit/helpers/copyText'
import { delay } from 'kit/helpers/delay'

export default {
  components: {
    Button,
    FileImportPrompt,
    SelectEmployeeInput,
    TopProgressBar,
    FormGroupTitle,
    Container
  },
  data() {
    return {
      isLoading: false,
      employeeDisableIds: [],
      employeeName: '任务',
      employeeId: '8',
      topProgressBarValue: '1',
      topProgressBarSteps: [
        {
          label: '填写活动信息',
          value: '1'
        },
        {
          label: '配置活动规则',
          value: '2'
        },
        {
          label: '完成',
          value: '3'
        }
      ]
    }
  },
  methods: {
    handleTopProgressBarValueClick(){
      this.topProgressBarValue = Math.min(Number(this.topProgressBarValue)+1,3)
    },
    async handleSaveClick() {
      await delay(1000)
      console.log('13')
    },
    copyText,
    handlePushEmployeeDisableIdsClick() {
      this.employeeDisableIds = ['8']
    },
    handleDeleteEmployeeDisableIdsClick() {
      this.employeeDisableIds = []
    }
  }
}
</script>

<style scoped>
.m-b-10 {
  margin-bottom: 10px;
}
.p-r-10 {
  padding-right: 10px;
}
section {
  margin-bottom: 20px;
}
</style>
