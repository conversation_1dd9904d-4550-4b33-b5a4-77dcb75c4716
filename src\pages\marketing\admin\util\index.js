import { VERIFICATION_IMG_CODE_REGEX } from 'kit/helpers/regexp'

//验证姓名
const validateName = (r, v, c) => {
  let rule = /^[\u4e00-\u9fa50-9a-zA-Z]*$/
  if (!v) return c(new Error('姓名不能为空'))
  if (v.length > 30) return c(new Error('最多可输入30个字符'))
  if (!rule.test(v))
    return c(new Error('输入的姓名格式不正确，支持中文，字母，数字'))
  c()
}

const validateImgCode = (rule, value, callback) => {
  if (!VERIFICATION_IMG_CODE_REGEX.test(value)) {
    return callback(new Error('验证码必须为4位数字'))
  }
  callback()
}

// 校验部门是否为空
const validateDeptIds = (rule, value, callback) => {
  if (!value.length) {
    return callback(new Error('请至少选择一个部门'))
  }
  callback()
}

//验证手机号
const validateTel = (rule, value, callback) => {
  if (value) {
    let reg = /^1\d{10}$/
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的手机号'))
    }
  } else {
    if (rule.required) {
      callback(new Error('请输入手机号'))
    } else {
      callback()
    }
  }
}

//验证证件号码
const validateidCard = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入证件号码'))
  } else {
    let reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的证件号码'))
    }
  }
}

export {
  validateName,
  validateTel,
  validateDeptIds,
  validateidCard,
  validateImgCode
}
