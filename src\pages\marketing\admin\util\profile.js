import makeMarketingClient from 'kit/services/marketing/makeClient'
import handleError from 'kit/helpers/handleError'
import store from 'kit/helpers/store'
const marketingClient = makeMarketingClient()

const PROFILE_INFO = '__APP_PROFILE_INFO__'
const CACHE_TIME = 2

function setLocalStorageWithExpiration(key, value, expirationInMinutes) {
  const expirationMS = expirationInMinutes * 1000
  const item = {
    value: value,
    expiration: Date.now() + expirationMS
  }
  localStorage.setItem(key, JSON.stringify(item))
}

function getLocalStorageWithExpiration(key) {
  const itemStr = localStorage.getItem(key)
  if (!itemStr) {
    return null
  }
  const item = JSON.parse(itemStr)
  // console.log(Date.now(), item.expiration)
  if (Date.now() > item.expiration) {
    localStorage.removeItem(key)
    return null
  }
  return item.value
}

export async function getProfileInfo() {
  const data = getLocalStorageWithExpiration(PROFILE_INFO)
  if (!data) {
    return loadProfile()
  }
  console.log(data)
  return data
}

function setProfileInfo(data) {
  setLocalStorageWithExpiration(PROFILE_INFO, data, CACHE_TIME)
}

export async function loadProfile() {
  const [err, result] = await marketingClient.userProfile({
    method: 'GET'
  })
  if (err) {
    handleError(err)
    return
  }
  setProfileInfo(result.data)
  store.set('userMobile', result.data.mobile)
  return result.data
}
