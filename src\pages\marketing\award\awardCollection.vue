<template>
  <div>
    <Coupon :info="info" v-if="COUPON === info.getWay" />
    <BlindBox
      :info="info"
      v-if="BLIND_BOX === info.getWay"
      :leftCount="info.leftCount"
    />
    <ScratchLotteryTicket
      :info="info"
      v-if="SCRATCH_CARD === info.getWay"
      :leftCount="info.leftCount"
    />
    <DigitalStorm
      :info="info"
      v-if="NUMBER_BOMB === info.getWay"
      :leftCount="info.leftCount"
    />
    <LuckyWheelDraw
      :info="info"
      v-if="BIG_WHEEL_GAME === info.getWay"
      :leftCount="info.leftCount"
    />
  </div>
</template>

<script>
import {
  COUPON,
  BLIND_BOX,
  SCRATCH_CARD,
  NUMBER_BOMB,
  BIG_WHEEL_GAME
} from '../admin/constants'

import ScratchLotteryTicket from './awardCollection/scratchLotteryTicket.vue'
import LuckyWheelDraw from './awardCollection/luckyWheelDraw.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import DigitalStorm from './awardCollection/digitalStorm.vue'
import { getWechatOpenId } from './utils/wechatOpenid'
import BlindBox from './awardCollection/blindBox.vue'
import Coupon from './awardCollection/coupon.vue'
import store from 'kit/helpers/store'
import handleErrorH5 from 'kit/helpers/handleErrorH5'

const marketingClient = makeMarketingClient()

export default {
  components: {
    ScratchLotteryTicket,
    LuckyWheelDraw,
    DigitalStorm,
    BlindBox,
    Coupon
  },
  data() {
    return {
      COUPON,
      BLIND_BOX,
      SCRATCH_CARD,
      NUMBER_BOMB,
      BIG_WHEEL_GAME,
      info: {
        id: 0,
        name: '',
        availableBeginTime: '2023-06-20T03:52:02.131Z',
        availableEndTime: '2023-06-20T03:52:02.131Z',
        getWay: '',
        getLimit: 0,
        bannerImageUrl: '',
        remark: '',
        status: '1',
        qualification: '',
        rewardInfo: '',
        amount: 0,
        useCount: 0,
        merchantLogo: '',
        leftCount: 0,
        noStock: true,
        couponsList: [],
        collectUserItem: []
      }
    }
  },
  async created() {
    this.loadInfo()
  },
  computed: {
    awardRemainingTimes() {
      const getLimit = this.info.getLimit

      const getCounts = this.info.couponsList.reduce((a, b) => {
        return a + b.getCount
      }, 0)

      if (getCounts >= getLimit) return 0

      return getLimit - getCounts
    }
  },
  methods: {
    async loadInfo() {
      // const infoResult = JSON.parse(store.get('__activity_info_result__') || {})
      const { sn, channel } = this.$route.query

      const [err, result] = await marketingClient.mobileActivityInfo({
        body: {
          sn,
          channel,
          openid: getWechatOpenId()
        }
      })

      if (err) return handleErrorH5(err)

      store.set('__activity_info_result__', JSON.stringify(result))

      this.info = result.data
    }
  }
}
</script>
