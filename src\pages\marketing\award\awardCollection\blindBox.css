.container {
  width: 100%;
  padding: 0 16px 0;
  box-sizing: border-box;
  position: relative;
}

.bg {
  width: 100%;
  content: '';
  display: block;
  position: absolute;
}

.prize {
  width: 100%;
  height: 450px;
  background: url('kit/assets/images/marketing/mobile/blindBox/<EMAIL>') no-repeat center;
  background-size: cover;
  margin: 0 auto 16px;
  position: relative;
}

.wrap {
  width: 100%;
  margin: 0;
  padding: 0;
  background-color: #86ecf7;
  padding-bottom: 40px;
}

.banner {
  display: block;
  width: 100%;
  margin: 0;
}

.custom-button {
  width: 196px;
  height: 68px;
  background: url('kit/assets/images/marketing/mobile/blindBox/<EMAIL>') no-repeat center;
  background-size: cover;
  color: #ffffffff;
  font-size: 16px;
  font-weight: 500;
  font-family: 'PingFang SC';
  text-align: center;
  padding: 0;
  border: 0;
  display: block;
  margin: 10px auto;
}

.custom-button span {
  display: block;
  margin-bottom: 12px;
}

.box,
.prize-wrap {
  border-radius: 12px;
  opacity: 1;
  border: 1px solid #ffffffff;
  background: #5684ffff;
  padding: 5px;
  box-sizing: border-box;
}

.ActivityRules {
  background: #fff;
  min-height: 348px;
  width: 100%;
  box-sizing: border-box;
  border-radius: 9px;
}

.prize-wrap {
  min-height: 450px;
  margin-bottom: 16px;
}

.prize-wrap-bg {
  background: #fff;
  border-radius: 9px;
  overflow: hidden;
  height: 100%;
}

.prize-wrap h2 {
  opacity: 1;
  color: #ffffffff;
  font-size: 16px;
  font-weight: 500;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 28px;
  width: 100%;
  text-align: center;
  background: url('kit/assets/images/marketing/mobile/blindBox/title.jpg') no-repeat center;
  background-size: cover;
  width: 134px;
  height: 28px;
  margin: 0 auto 10px;
}

.prize-box {
  display: flex;
  gap: 12px;
  justify-content: center;
  background: #6389ff;
  margin: 0 12px;
  border-radius: 9px;
  padding: 5px;
  flex-wrap: wrap;
  box-sizing: border-box;
}

.prize-box ul {
  display: grid;
  grid-template-columns: auto auto auto;
  gap: 12px;
  background: #8983f6;
  width: 100%;
  border: 1px solid #fff;
  border-radius: 9px;
  justify-content: center;
  padding: 10px 0;
}

.prize-box li {
  width: 1.6rem;
  height: 1.6rem;
  background: url('kit/assets/images/marketing/mobile/blindBox/<EMAIL>') no-repeat center;
  background-size: cover;
  color: #000000ff;
  font-size: .24rem;
  font-weight: 500;
  font-family: 'PingFang SC';
  text-align: left;
  text-align: center;
  line-height: .4rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding-bottom: .04rem;
  box-sizing: border-box;
}

.prize-box li::v-deep .van-image img {
  width: auto;
  margin-top: .16rem;
}

.remainingCount {
  height: 24px;
  opacity: 1;
  color: #6854ebff;
  font-size: 14px;
  font-weight: 500;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 24px;
  margin: 4px 0;
  width: 100%;
  text-align: center;
}

.prize-group-box {
  height: 3.64rem;
  width: 6.18rem;
  left: 0.12rem;
  background: url('kit/assets/images/marketing/mobile/blindBox/<EMAIL>') no-repeat center;
  background-size: cover;
  margin: 0 12px;
  position: relative;
  display: grid;
  grid-template-columns: repeat(3, 1.6rem);
  grid-template-rows: auto auto;
  justify-content: center;
  grid-gap: 0.24rem;
  grid-row-gap: 0;
  box-sizing: border-box;
  overflow: hidden;
  padding-top: 0.24rem;
}

.prize-strange-box {
  background: url('kit/assets/images/marketing/mobile/blindBox/<EMAIL>') no-repeat center;
  background-size: cover;
  height: 1.6rem;
  opacity: 0.5;
  transition: ease-out 0.3s;
}

.prize-strange-box:nth-child(5) {
  grid-row: 2;
  grid-column: 2;
  order: 5;
}

.prize-strange-box:nth-child(6) {
  grid-row: 2;
  grid-column: 1;
  order: 6;
}

.prize-strange-box.active {
  opacity: 1;
}

.prize-group-box .aura {
  content: '';
  position: absolute;
  width: 1.8rem;
  height: 1.8rem;
  background: url('kit/assets/images/marketing/mobile/blindBox/<EMAIL>') no-repeat center;
  background-size: cover;
  transition: ease-out 0.3s;
  animation: rotate 4s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(720deg);
  }
}

.count-box {
  display: flex;
  align-items: center;
  margin: .11rem auto 0;
  justify-content: center;
}

.count-box .winning-record {
  margin-left: 1rem;
}