<template>
  <div class="wrap">
    <PrizeDialog ref="PrizeDialog" />
    <!-- 领取失败得弹框 ， 样式跟领取成功UI不一致 -->
    <AwardFailDialog ref="AwardFailDialog" />
    <Loading v-if="isPageLoading" color="#4031F1" />

    <VantImage class="banner" :src="info.bannerImageUrl" />
    <VantImage class="bg" :src="wrapBgImg" @load="onBgLoad" />
    <div class="container">
      <div class="prize-wrap">
        <div class="prize-wrap-bg">
          <h2>奖品展示区</h2>
          <div class="prize-box">
            <ul>
              <li v-for="item in info.awardShowList" :key="item.position">
                <VantImage style="height: 0.8rem" :src="item.imageUrl" />
                <span>{{ item.name }}</span>
              </li>
            </ul>
          </div>
          <!-- <div class="remainingCount">剩余 {{ remainingCount }} 次机会</div> -->
          <div class="count-box remainingCount">
            <p class="count">剩余 {{ remainingCount }} 次机会</p>
            <p
              class="count winning-record"
              @click="$router.push('/winningRecords')"
            >
              中奖记录 <Icon name="arrow" />
            </p>
          </div>
          <div class="prize-group-box">
            <div
              class="prize-strange-box"
              v-for="(item, index) in prizeBoxNumber"
              :key="item"
              :class="{ active: auraActiveIndex == index }"
            />
            <i class="aura" :style="auraPosition"></i>
          </div>
          <button class="custom-button" @click="handleOpenClick">
            <span>开启盲盒</span>
          </button>
        </div>
      </div>
      <div class="box">
        <ActivityRules class="ActivityRules" :info="info" v-if="info.id" />
      </div>
    </div>
  </div>
</template>

<script>
import AwardFailDialog from 'kit/components/marketing/award/prizeDialog/awardFailDialog.vue'
import PrizeDialog from 'kit/components/marketing/award/prizeDialog/blindBox.vue'
import wrapBgImg from 'kit/assets/images/marketing/mobile/blindBox/<EMAIL>'
import ActivityRules from 'kit/components/marketing/award/activityRules.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import Loading from 'kit/components/marketing/award/loading.vue'
import { getWechatOpenId } from '../utils/wechatOpenid'
import handleErrorH5 from 'kit/helpers/handleErrorH5'
import { BLIND_BOX, NO_PRIZE_ERROR_CODE } from '../../admin/constants'
import { delay } from 'kit/helpers/delay'
import { Image, Icon } from 'vant'

const marketingClient = makeMarketingClient()

const auraMovePoint = [
  [5, 4],
  [34, 4],
  [64, 4],
  [65, 50],
  [35, 50],
  [5, 50]
]

export default {
  components: {
    VantImage: Image,
    AwardFailDialog,
    ActivityRules,
    PrizeDialog,
    Loading,
    Icon
  },
  props: {
    info: {
      type: Object,
      default: () => {}
    },
    leftCount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      wrapBgImg,
      isGetting: false,
      isPageLoading: true,
      remainingCount: this.leftCount,
      lotteryInProgress: false,
      auraIndex: 0,
      prizeBoxNumber: 6
    }
  },
  computed: {
    auraActiveIndex() {
      return this.auraIndex % this.prizeBoxNumber
    },
    auraPosition() {
      const [x, y] = auraMovePoint[this.auraActiveIndex] || {}
      return {
        left: `${x}%`,
        top: `${y}%`
      }
    }
  },
  methods: {
    async augmentAuraIndex(done) {
      if (this.lotteryInProgress) return
      this.lotteryInProgress = true
      let winningBoxIndex = Math.floor(Math.random() * 6) + 6

      for (let i = 0; i < winningBoxIndex; i++) {
        await delay(300)
        this.auraIndex++
      }
      done()
      await delay(500)
      this.lotteryInProgress = false
    },
    async handleOpenClick() {
      if (this.isGetting) return
      if (Number(this.remainingCount) === 0) {
        this.$refs.AwardFailDialog.showDialog('noMoreChances')
        return
      }

      if (this.lotteryInProgress) return

      const { sn, channel } = this.$route.query
      const openid = getWechatOpenId()

      const params = {
        openid,
        sn,
        channel,
        activityId: this.info.id,
        getWay: BLIND_BOX,
        digital: this.value
      }

      this.isGetting = true
      const [err, result] = await marketingClient.mobileActivityGetAward({
        body: params
      })

      this.isGetting = false

      if (err) {
        if (err?.errorCode === NO_PRIZE_ERROR_CODE) {
          this.augmentAuraIndex(async () => {
            await delay(500)
            this.remainingCount--
            this.$refs.AwardFailDialog.showErrCodeDialog(NO_PRIZE_ERROR_CODE)
          })
          return
        }
        if (this.$refs.AwardFailDialog.showErrCodeDialog(err?.errorCode)) return
        handleErrorH5(err)
        return
      }

      this.augmentAuraIndex(async () => {
        await delay(500)
        this.remainingCount--
        this.$refs.PrizeDialog.prizeName = result.data.name
        this.$refs.PrizeDialog.open()
      })
    },
    async onBgLoad() {
      await delay(300)
      this.isPageLoading = false
    }
  }
}
</script>

<style scoped>
@import './blindBox.css';
</style>
>
