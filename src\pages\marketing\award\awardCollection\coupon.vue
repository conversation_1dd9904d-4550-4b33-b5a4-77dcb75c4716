<template>
  <div style="min-height: 100vh; background-color: #f2f4f7ff">
    <!-- 领取成功得弹框 -->
    <PrizeDialog ref="PrizeDialog" />
    <!-- 领取失败得弹框 ， 样式跟领取成功UI不一致 -->
    <AwardFailDialog ref="AwardFailDialog" />

    <VantImage :src="info.bannerImageUrl" height="212" width="100%" />
    <div
      style="
        height: 68px;
        position: absolute;
        top: 150px;
        z-index: 1;
        width: 100%;
      "
    >
      <VantImage :src="themeBuffImg" alt="" style="width: 100%; height: 68px" />
    </div>
    <div style="height: 2px"></div>

    <div style="padding: 0 12px">
      <CouponItem
        v-for="item in info.couponsList"
        :disabled="disabled"
        :info="item"
        style="margin-bottom: 20px"
        :key="item.id"
        @collection="receive"
      />
    </div>

    <p class="count winning-record" @click="$router.push('/winningRecords')">
      中奖记录 <Icon name="arrow" />
    </p>
    <ActivityRules :info="info" v-if="info.id" />
  </div>
</template>
<script>
import AwardFailDialog from 'kit/components/marketing/award/prizeDialog/awardFailDialog.vue'
import PrizeDialog from 'kit/components/marketing/award/prizeDialog/luckyWheelDraw.vue'
import ActivityRules from 'kit/components/marketing/award/activityRules.vue'
import themeImg from 'kit/assets/images/<EMAIL>'
import CouponItem from 'kit/components/marketing/award/couponItem.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import themeBuffImg from 'kit/assets/images/<EMAIL>'
import { getWechatOpenId } from '../utils/wechatOpenid'
import handleErrorH5 from 'kit/helpers/handleErrorH5'
import { Dialog, Image, Icon } from 'vant'
import { COUPON } from '../../admin/constants'

const marketingClient = makeMarketingClient()

export default {
  components: {
    VantImage: Image,
    AwardFailDialog,
    ActivityRules,
    PrizeDialog,
    CouponItem,
    Dialog,
    Icon
  },
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      allCoupons: [],
      themeImg,
      themeBuffImg,
      isReceive: true
    }
  },
  computed: {
    disabled() {
      const getLimit = this.info.getLimit

      const getCounts = this.info.couponsList.reduce((a, b) => {
        return a + b.getCount
      }, 0)

      return getCounts >= getLimit
    }
  },
  methods: {
    async receive([item, done]) {
      const { sn, channel } = this.$route.query
      const openid = getWechatOpenId()

      const [err] = await marketingClient.mobileActivityGetAward({
        body: {
          openid,
          sn,
          channel,
          couponsGroupId: item.id,
          activityId: this.info.id,
          getWay: COUPON
        }
      })

      done()
      if (err) {
        if (this.$refs.AwardFailDialog.showErrCodeDialog(err.errorCode)) return
        handleErrorH5(err)
        return
      }

      this.$set(item, 'getCount', item.getCount + 1)

      this.$refs.PrizeDialog.showDialog(item.name)
    }
  }
}
</script>

<style scoped>
.count {
  width: 100%;
  text-align: center;
  color: #666;
}
</style>