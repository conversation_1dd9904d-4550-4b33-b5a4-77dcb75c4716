.container {
  width: 100%;
  padding: 0 16px 0;
  box-sizing: border-box;
  position: relative;
}

.bg {
  width: 100%;
  content: '';
  display: block;
  position: absolute;
}





.wrap {
  width: 100%;
  margin: 0;
  padding: 0;
  padding-bottom: 2.2rem;
  background-color: #ffc170;
}

.banner {
  display: block;
  width: 100%;
  margin: 0;
}

.custom-button {
  width: 196px;
  height: 68px;
  background: url('kit/assets/images/marketing/mobile/blindBox/<EMAIL>') no-repeat center;
  background-size: cover;
  color: #ffffffff;
  font-size: 16px;
  font-weight: 500;
  font-family: 'PingFang SC';
  text-align: center;
  padding: 0;
  border: 0;
  display: block;
  margin: 10px auto;
}

.custom-button span {
  display: block;
  margin-bottom: 12px;
}

.box {
  border-radius: 12px;
  opacity: 1;
  border: 1px solid #ffffffff;
  background: linear-gradient(to bottom, #fdc589, #e75c37);
  padding: 5px;
  box-sizing: border-box;
}

.ActivityRules {
  background: #fff;
  min-height: 348px;
  width: 100%;
  box-sizing: border-box;
  border-radius: 9px;
}

.prize-wrap {
  margin-bottom: 12px;
}

.prize-wrap img {
  width: 100%;
  display: block;
}

.input-box {
  width: 100%;
  height: 4.08rem;
  background: url('kit/assets/images/marketing/mobile/digitalStorm/<EMAIL>') no-repeat center;
  overflow: hidden;
  background-size: cover;
  margin-top: 12px;
}

.input-box h2 {
  height: 26px;
  opacity: 1;
  color: #ffffffff;
  font-size: 18px;
  font-weight: 600;
  font-family: 'PingFang SC';
  text-align: center;
  line-height: 26px;
  margin: 20px auto 8px;
}

.input-box ::v-deep.van-field {
  width: 260px;
  height: 40px;
  border-radius: 8px;
  opacity: 1;
  background: #ffffffff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  border: 0;
  text-align: center;
  margin: 0 auto 16px;
  display: block;
}

.input-box button {
  width: 173px;
  height: 52px;
  background: url('kit/assets/images/marketing/mobile/digitalStorm/<EMAIL>') no-repeat center;
  background-size: cover;
  border: 0;
  color: #ffffffff;
  font-size: 16px;
  font-weight: 500;
  font-family: 'PingFang SC';
  margin: 0 auto;
  display: block;
}

.input-box button span {
  position: relative;
  top: -4px;
}

.input-box .count {
  height: 20px;
  opacity: 1;
  color: #ffffffff;
  font-size: 12px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: center;
  line-height: 20px;
  margin: 0;
}

.count-box {
  display: flex;
  align-items: center;
  margin: .1rem;
  justify-content: center;
}

.count-box .winning-record {
  margin-left: 1.25rem;
}