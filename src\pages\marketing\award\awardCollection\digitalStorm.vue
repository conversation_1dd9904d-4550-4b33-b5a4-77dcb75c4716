<template>
  <div class="wrap">
    <PrizeDialog ref="PrizeDialog" />
    <!-- 领取失败得弹框 ， 样式跟领取成功UI不一致 -->
    <AwardFailDialog ref="AwardFailDialog" />
    <Loading v-if="isLoading" color="#42ADF9" />

    <VantImage class="banner" :src="info.bannerImageUrl" />
    <VantImage class="bg" :src="images.wrapBgImg" @load="onBgLoad" />
    <div class="container">
      <div class="prize-wrap">
        <VantImage :src="images.gameBg" @load="isLoading = false" />
        <div class="input-box">
          <h2>您认为本期的幸运数字是</h2>
          <Field
            type="number"
            v-model="value"
            :style="{
              fontSize: value.length > 0 ? '20px' : '14px',
              paddingTop: value.length > 0 ? '.08rem' : '.14rem'
            }"
            maxLength="12"
            input-align="center"
            placeholder="请填写数字"
          />
          <button @click="handleOpenClick"><span>确定</span></button>
          <div class="count-box">
            <p class="count">剩余 {{ remainingCount }} 次机会</p>
            <p
              class="count winning-record"
              @click="$router.push('/winningRecords')"
            >
              中奖记录 <Icon name="arrow" />
            </p>
          </div>
        </div>
      </div>
      <div class="box">
        <ActivityRules class="ActivityRules" :info="info" v-if="info.id" />
      </div>
    </div>
  </div>
</template>

<script>
import AwardFailDialog from 'kit/components/marketing/award/prizeDialog/awardFailDialog.vue'
import PrizeDialog from 'kit/components/marketing/award/prizeDialog/digitalStorm.vue'
import wrapBgImg from 'kit/assets/images/marketing/mobile/digitalStorm/<EMAIL>'
import gameBg from 'kit/assets/images/marketing/mobile/digitalStorm/<EMAIL>'
import ActivityRules from 'kit/components/marketing/award/activityRules.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import Loading from 'kit/components/marketing/award/loading.vue'
import { showLoading, hideLoading } from '../utils/loading'
import { getWechatOpenId } from '../utils/wechatOpenid'
import handleErrorH5 from 'kit/helpers/handleErrorH5'
import { NO_PRIZE_ERROR_CODE, NUMBER_BOMB } from '../../admin/constants'
import { Image, Toast, Field, Icon } from 'vant'
import { delay } from 'kit/helpers/delay'

const marketingClient = makeMarketingClient()

const images = {
  wrapBgImg,
  gameBg
}

function validateNumber(num) {
  num = parseInt(num, 10)
  return Number.isInteger(num) && num >= 0
}

export default {
  components: {
    VantImage: Image,
    AwardFailDialog,
    ActivityRules,
    PrizeDialog,
    Loading,
    Icon,
    Field
  },
  props: {
    info: {
      type: Object,
      default: () => {}
    },
    leftCount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      images,
      value: '',
      isLoading: true,
      remainingCount: this.leftCount,
      isGetting: false
    }
  },
  methods: {
    async handleOpenClick() {
      if (this.isGetting) return
      if (Number(this.remainingCount) === 0) {
        this.$refs.AwardFailDialog.showDialog('noMoreChances')
        return
      }

      const ok = validateNumber(this.value)
      if (!ok) {
        Toast('请输入大于等于0的整数', {
          forbidClick: true
        })
        return
      }

      const { sn, channel } = this.$route.query
      const openid = getWechatOpenId()

      showLoading('正在领取')
      this.isGetting = true
      const [err, result] = await marketingClient.mobileActivityGetAward({
        body: {
          sn,
          channel,
          openid,
          activityId: this.info.id,
          getWay: NUMBER_BOMB,
          digital: this.value
        }
      })
      this.isGetting = false
      hideLoading()
      if (err) {
        if (err?.errorCode === NO_PRIZE_ERROR_CODE) {
          this.remainingCount--
        }
        if (this.$refs.AwardFailDialog.showErrCodeDialog(err?.errorCode)) return
        handleErrorH5(err)
        return
      }

      this.remainingCount--
      this.$refs.PrizeDialog.prizeName = result.data.name
      this.$refs.PrizeDialog.open()
    },
    async onBgLoad() {
      await delay(300)
      this.isLoading = false
    }
  }
}
</script>

<style scoped>
@import './digitalStorm.css';
</style>
>
