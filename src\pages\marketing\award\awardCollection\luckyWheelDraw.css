.container {
  width: 100%;
  padding: 0 16px 0;
  box-sizing: border-box;
  position: relative;
}

.bg {
  width: 100%;
  content: '';
  display: block;
  position: absolute;
}

.wrap {
  width: 100%;
  margin: 0;
  padding: 0;
  padding-bottom: 2.2rem;
  background-color: #FF4729;
}

.banner {
  display: block;
  width: 100%;
  margin: 0;
}

.custom-button {
  width: 196px;
  height: 68px;
  background: url('kit/assets/images/marketing/mobile/blindBox/<EMAIL>') no-repeat center;
  background-size: cover;
  color: #ffffffff;
  font-size: 16px;
  font-weight: 500;
  font-family: 'PingFang SC';
  text-align: center;
  padding: 0;
  border: 0;
  display: block;
  margin: 10px auto;
}

.custom-button span {
  display: block;
  margin-bottom: 12px;
}

.box {
  border-radius: 12px;
  opacity: 1;
  border: 1px solid #ffffffff;
  background: linear-gradient(to bottom, #fdc589, #e75c37);
  padding: 5px;
  box-sizing: border-box;
}

.ActivityRules {
  background: #fff;
  min-height: 348px;
  width: 100%;
  box-sizing: border-box;
  border-radius: 9px;
}

.prize-wrap {
  margin-bottom: 12px;
}

.prize-wrap img {
  width: 100%;
  display: block;
}

.input-box {
  width: 100%;
  overflow: hidden;
  background-size: cover;
  padding-top: 6.8rem;
}

.input-box h2 {
  height: 26px;
  opacity: 1;
  color: #ffffffff;
  font-size: 18px;
  font-weight: 600;
  font-family: 'PingFang SC';
  text-align: center;
  line-height: 26px;
  margin: 20px auto 8px;
}

.input-box ::v-deep.van-field {
  width: 260px;
  height: 40px;
  border-radius: 8px;
  opacity: 1;
  background: #ffffffff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  border: 0;
  text-align: center;
  margin: 0 auto 16px;
  display: block;
}

.input-box button {
  width: 4.66rem;
  height: 1.26rem;
  background: url('kit/assets/images/marketing/mobile/luckyWheelDraw/<EMAIL>') no-repeat center;
  background-size: cover;
  border: 0;
  color: #ffffffff;
  font-size: 16px;
  font-weight: 500;
  font-family: 'PingFang SC';
  margin: 0 auto;
  display: block;
}

.input-box button span {
  position: relative;
  top: -4px;
}

.input-box .count {
  height: .4rem;
  opacity: 1;
  color: #ffffffff;
  font-size: .28rem;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: center;
  line-height: .4rem;
  margin: 0;
}

.count-box {
  display: flex;
  align-items: center;
  margin: .3rem 0 .2rem;
  justify-content: center;
}

.count-box .winning-record {
  margin-left: 1.25rem;
}

.pointer {
  position: absolute;
  top: 1.92rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.prize-group {
  position: absolute;
  top: .46rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, .9);
  background: url('kit/assets/images/marketing/mobile/luckyWheelDraw/zp.png') no-repeat center;
  background-size: cover;
  width: 5.1rem;
  height: 5.1rem;
  border-radius: 50%;
  margin-left: 0.02rem;
}

.rotate-enter-active {
  animation: rotate 5s;
}

@keyframes rotate {
  0% {
    transform: translateX(-50%) rotate(0deg);
  }

  100% {
    transform: translateX(-50%) rotate(1800deg);
  }
}

.prize-group li {
  display: flex;
  align-items: center;
  flex-direction: column;
  position: absolute;
  /* background: rgba(0, 0, 0, .3); */
  width: 2rem;
}

.prize-group li:nth-child(1) {
  left: 1.60rem;
  top: .26rem;
}

.prize-group li:nth-child(2) {
  left: 3.06rem;
  top: 1.2rem;
  transform: rotate(55deg);
}

.prize-group li:nth-child(3) {
  left: 3.2rem;
  top: 2.86rem;
  transform: rotate(120deg);
}

.prize-group li:nth-child(4) {
  left: 1.58rem;
  top: 3.80rem;
  transform: rotate(180deg);
}

.prize-group li:nth-child(5) {
  left: .1rem;
  top: 2.98rem;
  transform: rotate(236deg);
}

.prize-group li:nth-child(6) {
  left: .12rem;
  top: 1.2rem;
  transform: rotate(296deg);
}


.prize-group li span {
  color: #c25014;
  text-align: center;
  font-family: "PingFang SC";
  font-size: .28rem;
  font-style: normal;
  font-weight: 400;
  line-height: .44rem;
  margin-bottom: .1rem;
}