<template>
  <div class="wrap">
    <!-- 领取成功得弹框 -->
    <PrizeDialog ref="PrizeDialog" />
    <!-- 领取失败得弹框 ， 样式跟领取成功UI不一致 -->
    <AwardFailDialog ref="AwardFailDialog" />

    <Loading v-if="isLoading" color="#42ADF9" />

    <VantImage class="banner" :src="info.bannerImageUrl" />
    <VantImage class="bg" :src="images.wrapBgImg" @load="onBgLoad" />

    <div class="container">
      <div class="prize-wrap">
        <div class="input-box">
          <div class="pointer">
            <VantImage
              :src="images.pointerImg"
              style="width: 1.86rem; height: 2.18rem"
            />
          </div>
          <transition name="rotate">
            <ul class="prize-group" v-show="isStartAnimate">
              <li v-for="item in info.awardShowList" :key="item.position">
                <span>{{ item.name }}</span>
                <VantImage
                  :src="item.imageUrl"
                  style="width: 0.5rem; height: 0.5rem"
                />
              </li>
            </ul>
          </transition>
          <button @click="handleOpenClick"><span></span></button>
          <div class="count-box">
            <p class="count">剩余 {{ remainingCount }} 次机会</p>
            <p
              class="count winning-record"
              @click="$router.push('/winningRecords')"
            >
              中奖记录 <Icon name="arrow" />
            </p>
          </div>
        </div>
      </div>
      <div class="box">
        <ActivityRules class="ActivityRules" :info="info" v-if="info.id" />
      </div>
    </div>
  </div>
</template>

<script>
import AwardFailDialog from 'kit/components/marketing/award/prizeDialog/awardFailDialog.vue'
import pointerImg from 'kit/assets/images/marketing/mobile/luckyWheelDraw/<EMAIL>'
import PrizeDialog from 'kit/components/marketing/award/prizeDialog/luckyWheelDraw.vue'
import wrapBgImg from 'kit/assets/images/marketing/mobile/luckyWheelDraw/<EMAIL>'
import prizeImg from 'kit/assets/images/marketing/mobile/luckyWheelDraw/<EMAIL>'
import gameBg from 'kit/assets/images/marketing/mobile/digitalStorm/<EMAIL>'
import ActivityRules from 'kit/components/marketing/award/activityRules.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import Loading from 'kit/components/marketing/award/loading.vue'
import { getWechatOpenId } from '../utils/wechatOpenid'
import handleErrorH5 from 'kit/helpers/handleErrorH5'
import { BIG_WHEEL_GAME, NO_PRIZE_ERROR_CODE } from '../../admin/constants'
import { Image, Icon } from 'vant'
import { delay } from 'kit/helpers/delay'

const marketingClient = makeMarketingClient()

const images = {
  pointerImg,
  wrapBgImg,
  gameBg,
  prizeImg
}

export default {
  components: {
    VantImage: Image,
    AwardFailDialog,
    ActivityRules,
    PrizeDialog,
    Loading,
    Icon
  },
  props: {
    info: {
      type: Object,
      default: () => {}
    },
    leftCount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      isGetting: false,
      isDrawingLottery: false,
      value: '',
      isStartAnimate: true,
      isLoading: true,
      images,
      remainingCount: this.leftCount
    }
  },
  methods: {
    startDrawingAnimation() {
      this.isStartAnimate = false
      this.isDrawingLottery = true
      setTimeout(() => {
        this.isStartAnimate = true
      }, 30)
    },
    async handleOpenClick() {
      if (this.isGetting) return
      if (this.isDrawingLottery) return

      if (Number(this.remainingCount) === 0) {
        this.$refs.AwardFailDialog.showDialog('noMoreChances')
        return
      }

      const { sn, channel } = this.$route.query
      const openid = getWechatOpenId()

      this.isGetting = true
      const [err, result] = await marketingClient.mobileActivityGetAward({
        body: {
          openid,
          channel,
          sn,
          activityId: this.info.id,
          getWay: BIG_WHEEL_GAME,
          digital: this.value
        }
      })
      this.isGetting = false

      if (err) {
        if (err.errorCode === NO_PRIZE_ERROR_CODE) {
          this.startDrawingAnimation()
          this.remainingCount--
          await delay(3000)
          this.isDrawingLottery = false
          this.$refs.AwardFailDialog.showErrCodeDialog(NO_PRIZE_ERROR_CODE)
          return
        }
        if (this.$refs.AwardFailDialog.showErrCodeDialog(err?.errorCode)) return
        handleErrorH5(err)
        return
      }

      this.startDrawingAnimation()
      this.remainingCount--
      await delay(3000)
      this.$refs.PrizeDialog.showDialog(result.data.name)
      this.isDrawingLottery = false
    },
    async onBgLoad() {
      await delay(300)
      this.isLoading = false
    }
  }
}
</script>

<style scoped>
@import './luckyWheelDraw.css';
</style>
>
