.wrap {
  width: 100%;
  margin: 0;
  padding: 0;
  background-color: #ffb466;
  padding-bottom: 40px;
}

.container {
  width: 100%;
  padding: 0 20px 0;
  box-sizing: border-box;
  position: relative;
}

.prize-group {
  position: absolute;
  top: 3.46rem;
  display: flex;
  align-items: center;
  justify-content: center;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.prize-group li {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 1.84rem;
  /* background-color: rgba(0, 0, 0, .3); */
  height: 1.6rem;
  padding-top: .18rem;
}

.prize-group li p {
  margin: 0;
  position: relative;
  top: .22rem;
}

.prize-group ::v-deep img {
  width: auto;
}

.scratchCard {
  position: absolute;
  top: 52px;
  left: 50%;
  transform: translateX(-50%);
}

.prize-number {
  width: 238px;
  height: 74px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  font-size: 20px;
}

.bg {
  width: 100%;
  display: block;
  position: absolute;
  width: 100%;
}

.banner {
  display: block;
  width: 100%;
  margin: 0;
}

img {
  display: block;
}

.custom-button {
  width: 127px;
  height: 45px;
  background: url('kit/assets/images/marketing/mobile/scratchLotteryTicket/<EMAIL>') no-repeat center;
  background-size: cover;
  color: #ffffffff;
  font-size: 16px;
  font-weight: 500;
  font-family: 'PingFang SC';
  text-align: center;
  padding: 0;
  border: 0;
  display: block;
  margin: 10px auto;
  position: absolute;
  top: 52px;
  left: 50%;
  transform: translateX(-50%);
}

.custom-button span {
  display: block;
  margin-bottom: 8px;
}



.footer-rules {
  margin-left: 0.5rem;
  margin-right: 0.52rem;
}

.footer-rules img {
  width: 100%;
}

.ActivityRules {
  box-sizing: border-box;
  padding: 0 0.32rem;
  background-color: #fffdf7;
  margin-top: -0.26rem;
}

.remainingCount {
  height: 20px;
  opacity: 1;
  color: #4e5769;
  font-size: 12px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: center;
  line-height: 20px;
  position: absolute;
  top: 97px;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
}

.count-box {
  display: flex;
  align-items: center;
  margin: .1rem;
  justify-content: center;
}

.count-box .winning-record {
  margin-left: 1.25rem;
}