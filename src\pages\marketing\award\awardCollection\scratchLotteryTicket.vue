<template>
  <div class="wrap">
    <PrizeDialog ref="PrizeDialog" />
    <!-- 领取失败得弹框 ， 样式跟领取成功UI不一致 -->
    <AwardFailDialog ref="AwardFailDialog" />
    <Loading v-if="isLoading" />

    <!-- <ScratchCard
      style="position: absolute;z-index: 99;"
      :cardImgUrl="cardImgUrl"
      :radius="5"
      :scratchRadius="20"
      class="scratchCard"
      :scratchPercent="30"
      :isManualEraseEnabled="true"
      ref="ScratchCard"
      @scratchAll="scratchAll"
    >
      <div class="prize-number">恭喜您！中大奖了</div>
    </ScratchCard> -->

    <VantImage class="banner" :src="info.bannerImageUrl" />
    <VantImage class="bg" :src="images.wrapBgImg" @load="onBgLoad" />
    <div class="container">
      <div class="prize-wrap">
        <img
          style="width: 100%"
          src="kit/assets/images/marketing/mobile/scratchLotteryTicket/Frame_1321315397@3x_new.png"
          @load="onGameMachineImageLoaded"
        />
        <ul class="prize-group">
          <li v-for="item in info.awardShowList" :key="item.position">
            <VantImage :src="item.imageUrl" style="height: 0.8rem" />
            <p>{{ item.name }}</p>
          </li>
        </ul>
        <ScratchCard
          :cardImgUrl="cardImgUrl"
          :radius="5"
          :scratchRadius="20"
          class="scratchCard"
          v-if="isCanvas"
          :scratchPercent="30"
          ref="ScratchCard"
          @scratchAll="scratchAll"
        >
          <div class="prize-number">{{ prizeResult?.name }}</div>
        </ScratchCard>
        <div v-show="!isScratchLoading">
          <button class="custom-button" @click="handleAutoScratchClick">
            <span>快来刮一刮</span>
          </button>
          <!-- <p class="remainingCount">剩余 {{ remainingCount }} 次</p> -->
          <div class="count-box remainingCount">
            <p class="count">剩余 {{ remainingCount }} 次机会</p>
            <p
              class="count winning-record"
              @click="$router.push('/winningRecords')"
            >
              中奖记录 <Icon name="arrow" />
            </p>
          </div>
        </div>
      </div>
      <div class="footer-rules">
        <ActivityRules class="ActivityRules" :info="info" v-if="info.id" />
        <VantImage :src="images.rulesFootBg" />
      </div>
    </div>
  </div>
</template>

<script>
import cardImgUrl from 'kit/assets/images/marketing/mobile/scratchLotteryTicket/<EMAIL>'
import rulesFootBg from 'kit/assets/images/marketing/mobile/scratchLotteryTicket/<EMAIL>'
import PrizeDialog from 'kit/components/marketing/award/prizeDialog/scratchLotteryTicket.vue'
import AwardFailDialog from 'kit/components/marketing/award/prizeDialog/awardFailDialog.vue'
import wrapBgImg from 'kit/assets/images/marketing/mobile/scratchLotteryTicket/<EMAIL>'
import ActivityRules from 'kit/components/marketing/award/activityRules.vue'
import ScratchCard from 'kit/components/marketing/award/scratchCard.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import Loading from 'kit/components/marketing/award/loading.vue'
import { getWechatOpenId } from '../utils/wechatOpenid'
import handleErrorH5 from 'kit/helpers/handleErrorH5'
import { NO_PRIZE_ERROR_CODE, SCRATCH_CARD } from '../../admin/constants'
import { positions } from './positionsAnimation'
import { delay } from 'kit/helpers/delay'
import { Image, Icon } from 'vant'

const marketingClient = makeMarketingClient()

const images = {
  wrapBgImg,
  rulesFootBg
}

export default {
  components: {
    VantImage: Image,
    AwardFailDialog,
    ActivityRules,
    PrizeDialog,
    ScratchCard,
    Loading,
    Icon
  },
  props: {
    info: {
      type: Object,
      default: () => {}
    },
    leftCount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      images,
      cardImgUrl,
      remainingCount: this.leftCount,
      isScratchLoading: false,
      isCanvas: true,
      isLoading: true,
      prizeResult: null,
      isGetting: false,
      errorCode: 0
    }
  },
  methods: {
    async handleOpenClick() {
      this.$refs.PrizeDialog.prizeName = this.prizeResult.name
      this.$refs.PrizeDialog.open()
    },
    async scratchAll() {
      this.isScratchLoading = false
      this.scratchReset()
      if (this.errorCode === NO_PRIZE_ERROR_CODE) {
        this.$refs.AwardFailDialog.showErrCodeDialog(NO_PRIZE_ERROR_CODE)
        return
      }
      this.handleOpenClick()
    },
    async handleAutoScratchClick() {
      if (this.isScratchLoading) return
      if (this.isGetting) return

      if (!this.remainingCount) {
        return this.$refs.AwardFailDialog.showDialog('noMoreChances')
      }

      const { sn, channel } = this.$route.query
      const openid = getWechatOpenId()

      this.isGetting = true
      const [err, result] = await marketingClient.mobileActivityGetAward({
        body: {
          openid,
          sn,
          channel,
          activityId: this.info.id,
          getWay: SCRATCH_CARD
        }
      })
      this.isGetting = false
      if (err) {
        if (err.errorCode === NO_PRIZE_ERROR_CODE) {
          this.prizeResult = { name: '没有中奖' }
          this.isScratchLoading = true
          this.remainingCount--
          this.$refs.ScratchCard.autoDrawArc(positions)
          this.errorCode = NO_PRIZE_ERROR_CODE
          return
        }
        if (this.$refs.AwardFailDialog.showErrCodeDialog(err?.errorCode)) return
        handleErrorH5(err)
        return
      }

      this.errorCode = 0
      this.prizeResult = result.data
      this.isScratchLoading = true
      this.remainingCount--
      this.$refs.ScratchCard.autoDrawArc(positions)
    },
    async scratchReset() {
      this.isCanvas = false
      await this.$nextTick()
      this.isCanvas = true
    },
    async onGameMachineImageLoaded() {
      this.isLoading = false
    },
    async onBgLoad() {
      await delay(300)
      this.isLoading = false
    }
  }
}
</script>

<style scoped>
@import './scratchLotteryTicket.css';
</style>
