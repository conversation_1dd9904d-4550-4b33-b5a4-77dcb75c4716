import * as reg from 'kit/helpers/regexp'
import { isValidIdCard } from 'kit/helpers/isValidIdCard'

export default {
  name: [
    { required: true },
    {
      validator: value => !reg.ILLEGAL_CHARACTERS_PATTERN.test(value),
      message: '不能包含非法字符'
    },
    {
      pattern: reg.charLength2To20Regex,
      message: '请输入2-20位字符'
    }
  ],
  captcha: [
    { required: true },
    {
      pattern: reg.VERIFICATION_IMG_CODE_REGEX,
      message: '验证码必须为4位数字'
    }
  ],
  idCard: [
    { required: true },
    { validator: isValidIdCard, message: '请输入正确的身份证号' }
  ],
  phone: [
    { required: true },
    {
      pattern: reg.PHONE_NUMBER_REGEX,
      message: '请输入有效的手机号'
    }
  ],
  code: [
    { required: true },
    {
      pattern: reg.VERIFICATION_SMS_CODE_REGEX,
      message: '验证码必须为6位数字'
    }
  ],
  bankCardNo: [
    { required: true },
    {
      pattern: reg.BANK_CARD_NUMBER_REGEX,
      message: '银行卡号格式不正确'
    }
  ]
}
