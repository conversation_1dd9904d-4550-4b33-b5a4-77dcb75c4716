<template>
  <div>
    <ErrorDialog
      v-for="item in errorCodeTips"
      :key="item.code"
      :ref="'dialog-' + item.code"
      :title="item.title"
      :content="item.content"
    />
  </div>
</template>

<script>
import {
  ACTIVITY_ENDED_CODE,
  ACTIVITY_FULL_CODE,
  ACTIVITY_NOT_STARTED_CODE,
  INVALID_LINK_CODE
} from '../../admin/constants'

import ErrorDialog from 'kit/components/marketing/award/scanErrorDialog.vue'

const errorCodeTips = [
  {
    title: '活动已结束',
    content: '优惠活动已结束，请下次趁早哦~',
    code: ACTIVITY_ENDED_CODE
  },
  {
    type: 'close',
    title: '活动链接已失效',
    content: '您的活动链接已失效，请联系推广员重新获取',
    code: INVALID_LINK_CODE
  },
  {
    type: 'full',
    title: '活动额度已满',
    content: '活动额度已满，请联系推广员提升活动额度',
    code: ACTIVITY_FULL_CODE
  },
  {
    title: '活动尚未开始',
    content: '优惠活动尚未开始，请耐心等待~',
    code: ACTIVITY_NOT_STARTED_CODE
  }
]

export default {
  components: {
    ErrorDialog
  },
  data() {
    return {
      errorCodeTips
    }
  },
  methods: {
    open(errorCode) {
      this.$refs[`dialog-${errorCode}`][0].open()
    }
  }
}
</script>
