<script>
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { setWechatOpenId } from './utils/wechatOpenid'
import handleErrorH5 from 'kit/helpers/handleErrorH5'

const marketingClient = makeMarketingClient()

const createWechatAuthURL = url => {
  const APPID = 'wxe9816500f2e36ce0'
  const redirect_uri = encodeURIComponent(url)
  const response_type = 'code'
  const scope = 'snsapi_base'
  const state = 'OMP_STATE'
  const connect_redirect = '1#wechat_redirect'
  return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${APPID}&redirect_uri=${redirect_uri}&response_type=${response_type}&scope=${scope}&state=${state}&connect_redirect=${connect_redirect}`
}

export default {
  async created() {
    const isProduction = import.meta.env.MODE === 'production'

    const urlParams = new URLSearchParams(location.search)
    const queryParams = Object.fromEntries(urlParams.entries())
    const { code, state, sn } = queryParams

    // 本地测试用
    if (!isProduction) {
      setWechatOpenId('o5qyP6YOlyPG8la-ztiam0sLWnvs')
      this.$router.replace({
        path: 'informationRegistration',
        query: {
          sn
        }
      })
      return
    }

    if (!code) {
      window.location.replace(createWechatAuthURL(window.location.href))
      return
    }

    const [error, result] = await marketingClient.wechatGetOpenId({
      body: {
        code: code,
        state: state
      }
    })

    if (error) {
      handleErrorH5(error)
      return
    }

    setWechatOpenId(result.data.openId)

    this.$router.replace({
      path: '/informationRegistration',
      query: {
        sn
      }
    })
  },
  render() {
    return ''
  }
}
</script>
