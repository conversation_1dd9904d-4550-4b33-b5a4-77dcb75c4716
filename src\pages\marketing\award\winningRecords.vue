<template >
  <div class="wrap">
    <NavBar
      title="中奖记录"
      left-text="返回"
      fixed
      left-arrow
      @click-left="onClickLeft"
    />

    <Loading v-if="isPageLoading" color="#F77A3F" />

    <div class="container">
      <template v-if="list.length">
        <div class="group" v-for="(item, index) in list" :key="index">
          <h2>{{ item.name }}</h2>
          <ul>
            <li v-for="(coupon, index) in item.awards" :key="index">
              <div class="title">
                <h3>{{ coupon.name }}</h3>
                <span>x{{ coupon.num }}</span>
              </div>
              <div class="info">
                <p v-if="showExpirationDate(coupon)">
                  使用期限：{{ formatDate(coupon.availableBeginTime) }} ~
                  {{ formatDate(coupon.availableEndTime) }}
                </p>
                <p>使用方式：{{ coupon.description }}</p>
                <p
                  class="exchange-code"
                  v-for="(redeemCode, index) in coupon.redeemCodes"
                  :key="redeemCode"
                >
                  兑换码{{ index + 1 }}：{{ redeemCode }}
                  <span
                    @click="handleCopyClick(redeemCode)"
                    class="icon iconfont icon-edit-interaction-copy"
                  ></span>
                </p>
              </div>
            </li>
          </ul>
        </div>
      </template>
      <div v-else class="noWinnerRecord">暂无中奖记录~</div>
    </div>
  </div>
</template>

<script>
import makeMarketingClient from 'kit/services/marketing/makeClient'
import Loading from 'kit/components/marketing/award/loading.vue'
import { getWechatOpenId } from './utils/wechatOpenid'
import { NavBar, Toast } from 'vant'
import { delay } from 'kit/helpers/delay'
import copyText from 'kit/helpers/copyText'
import store from 'kit/helpers/store'
import formatDateTime from 'kit/formatters/dateTime'

const marketingClient = makeMarketingClient()

export default {
  components: {
    NavBar,
    Loading
  },
  data() {
    return {
      isPageLoading: false,
      list: []
    }
  },
  methods: {
    formatDate(date) {
      let newData = date ? date.replace(/-/g, '/') : date
      return formatDateTime('yyyy-MM-dd', newData)
    },
    showExpirationDate({ availableEndTime, availableBeginTime }) {
      return availableEndTime && availableBeginTime
    },
    onClickLeft() {
      this.$router.back()
    },
    handleCopyClick(redeemCode) {
      copyText(redeemCode)
      Toast('复制成功，到外部可粘贴', {
        forbidClick: true
      })
    },
    showLoading(show = true) {
      this.isPageLoading = show
    },
    async loadInfo() {
      this.showLoading()

      const { data } = JSON.parse(store.get('__activity_info_result__') || {})

      const [err, result] = await marketingClient.mobileActivityAwardDetail({
        body: {
          openid: getWechatOpenId(),
          activityId: data?.id
          // openid: 'o5qyP6YgzayOIBveYHmFrL26wRqI',
          // activityId: '209'
        }
      })
      this.showLoading(false)

      if (err) {
        return handleError(err)
      }
      await delay(100)

      this.list = result.data
    }
  },
  mounted() {
    window.scrollTo({
      top: 0,
      left: 0
    })
  },
  created() {
    this.loadInfo()
  }
}
</script>
<style scoped>
.wrap {
  background: #f3f4f7;
  min-height: 100vh;
}
.noWinnerRecord {
  line-height: 100px;
  color: #999;
  text-align: center;
  font-weight: normal;
}
.group {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  padding: 0 12px;
  margin-bottom: 16px;
}
.container {
  padding: 61px 16px;
}
.group h2 {
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 46px;
}
.group li {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 8px;
  background: #f2f4f7;
  box-shadow: 0 1px 6px 0 #899db040;
  background: #fff;
  margin-bottom: 12px;
}
.group li .title {
  display: flex;
  border-bottom: 1px dashed var(--color-border-input, #cad0db);
  width: 100%;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  margin-bottom: 12px;
  padding-right: 16px;
}
.group .title h3 {
  color: #1e2228;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  position: relative;
  display: flex;
  padding: 0 16px;
  align-items: center;
}
.group .title h3::before {
  width: 3px;
  height: 14px;
  border-radius: 1px;
  background: #f77234;
  margin-right: 4px;
  content: '';
  display: block;
}
.group li p {
  color: #4e5769;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  margin: 0;
  margin-bottom: 4px;
}
.group li .info {
  padding: 8px 16px;
}
.exchange-code {
  display: flex;
  align-items: center;
}
.exchange-code span {
  color: #607fff;
  padding: 0 6px;
}
</style>