<template>
  <Container :back="true" :hideFootButton="true">
    <div class="wrap" v-loading="isLoading">
      <el-table :data="downloadRecordList" style="width: 100%">
        <el-table-column prop="createTime" label="申请时间" min-width="180">
        </el-table-column>
        <el-table-column prop="file" label="文件名" min-width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.file?.name || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <span>
              {{ getOptionsItemLabel(statusOptions, scope.row.status) || '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.status === '1'">-</span>
            <div v-else>
              <el-button type="text" @click="handleDownload(scope.row)"
                >下载</el-button
              >
              <el-button
                type="text"
                :loading="loading"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div
        style="
          width: calc(100vw - 278px);
          height: 58px;
          margin-top: 2px;
          position: fixed;
          right: 30px;
          bottom: 0;
          z-index: 99;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background: #ffffff;
        "
      >
        <el-pagination
          class="pagination"
          :current-page.sync="searchForm.currPage"
          :page-size="searchForm.pageSize"
          :page-sizes="[10, 20, 50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </Container>
</template>
<script>
import Container from 'kit/components/marketing/admin/container.vue'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import { statusOptions } from '../options'
import { delay } from 'kit/helpers/delay'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import { handleError } from 'kit/helpers/marketingBossToken'
import { oConfirm } from 'kit/components/marketing/admin/messageBox'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    Container
  },
  data() {
    return {
      getOptionsItemLabel,
      statusOptions,
      searchForm: {
        currPage: 1,
        pageSize: 20,
        filters: { deptId: '' }
      }, // 分页参数
      total: 0, // 分页总数
      downloadRecordList: [],
      isLoading: false,
      loading: false
    }
  },
  created() {
    this.getRecord()
  },
  methods: {
    async getRecord(currentPage) {
      this.isLoading = true
      var page = currentPage ? currentPage : 1
      const [err, r] = await marketingClient.adminReceiptDownloadRecord({
        body: {
          filters: {},
          start: page,
          limit: this.searchForm.pageSize,
          sorts: []
        },
        ...authorizationToken()
      })
      if (err) {
        this.isLoading = false
        handleError(err)
        return
      }
      await delay(100)
      this.isLoading = false

      this.downloadRecordList = r.data.list
      this.total = r.data.total
    },
    handleSizeChange(val) {
      this.searchForm.pageSize = val
      this.searchForm.currPage = 1
      this.getRecord()
    },
    handleCurrentChange(currentPage) {
      this.getRecord(currentPage)
    },
    handleDownload(row) {
      window.open(row.file.url)
    },
    handleDelete(row) {
      oConfirm('点击确定后将会删除此条记录，不可恢复', '删除此下载记录?', {
        confirm: async () => {
          const [err, r] =
            await marketingClient.adminReceiptDeleteDownloadRecord({
              body: {
                downloadRecordId: row.downloadRecordId
              },
              ...authorizationToken()
            })
          if (err) return handleError(err)
          this.$message.success('删除成功')
          this.getRecord()
        }
      })
    }
  }
}
</script>
<style scoped>
.wrap {
  display: flex;
  flex-direction: column;
  padding: 24px 24px 0;
  box-sizing: border-box;
  margin: 0 auto;
}
::v-deep .cell {
  font-size: 12px;
  color: rgb(119, 124, 148);
  font-weight: 400;
}
::v-deep .el-table th.el-table__cell {
  background: #f8fafd;
  border-bottom: none;
}
::v-deep .el-table td.el-table__cell div {
  color: #070f29;
}
</style>
