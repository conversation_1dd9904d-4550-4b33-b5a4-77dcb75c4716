<template>
  <o-pc-list
    ref="pc-list"
    title="企业账户交易明细"
    :formJson="searchFormJson"
    :requestFn="getListApi"
    labelWidth="70px"
    :deleteNullApiParams="true"
    :tableHeaderActionButtons="tableHeaderActionButtons"
    :tableHeader="tableHeader"
  />
</template>
<script>
import AutoEllipsisTooltip from 'kit/components/marketing/admin/autoEllipsisTooltip.vue'
import { grantStatusOptions } from '../options'
import { exportExcel } from 'kit/helpers/exportExcel'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import formatAmount from 'kit/formatters/formatAmount'
import { handleError } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.adminTransactionList({
    body: params,
    ...authorizationToken()
  })
  if (err) return handleError(err)
  return result.data
}

export default {
  data() {
    return {
      getListApi: loadList,
      searchFormJson: [
        {
          type: 'input',
          item: {
            prop: 'merchantId',
            label: '企业id',
            placeholder: '请输入企业id'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'merchantName',
            label: '企业名称',
            placeholder: '请输入企业名称'
          }
        }
      ],
      isFirstLoad: true,
      tableHeader: [
        {
          prop: 'sn',
          label: '发放唯一id',
          width: 150,
          fixed: true
        },
        {
          prop: 'payReceipt.batchNo',
          label: '微信转账批次号',
          width: 150
        },
        {
          prop: 'merchant.id',
          label: '企业id',
          width: 150
        },
        {
          prop: 'merchant.name',
          label: '企业名称',
          width: 150
        },
        {
          prop: 'sendTime',
          label: '发放时间',
          type: 'DATE_TIME'
        },
        {
          prop: 'activity.name',
          label: '发放活动名称',
          width: 150
        },
        {
          prop: 'receiverName',
          label: '领取人姓名'
        },
        {
          prop: 'receiverOpenId',
          label: '领取人微信openID',
          width: 150
        },
        {
          prop: 'amount',
          label: '金额',
          type: 'AMOUNT',
          width: 100,
          formatter: row => {
            return `￥${formatAmount(row.amount)}`
          }
        },
        {
          prop: 'status',
          label: '发放状态',
          minWidth: 120,
          formatter: row => {
            return getOptionsItemLabel(grantStatusOptions, row.status) || '-'
          }
        },
        {
          prop: 'failMessage',
          label: '发放结果反馈',
          minWidth: 220,
          render: (h, row) => {
            if (!row.failMessage) return h('span', '-')
            return h(AutoEllipsisTooltip, {
              style: {
                lineHeight: '18px'
              },
              props: {
                content: row.failMessage,
                tag: 'span',
                ellipsis: 2
              }
            })
          }
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '导出',
          props: {
            loading: false,
            style: {
              'justify-content': 'center'
            }
          },
          click: async ({ props }) => {
            props.loading = true
            const body = this.oPcList.oTable.getRequestParams()
            const result = await marketingClient.adminTransactionExport({
              body,
              ...authorizationToken()
            })
            await exportExcel(result)
            props.loading = false
          }
        }
      ]
    }
  },
  computed: {
    oPcList() {
      return this.$refs['pc-list']
    }
  },
  activated() {
    if (!this.isFirstLoad) this.reload()
  },
  methods: {}
}
</script>
