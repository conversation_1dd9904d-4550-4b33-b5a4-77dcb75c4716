<template>
  <Container :back="true" :hideFootButton="true">
    <div class="wrap customer-webkit-scrollbar" v-loading="isLoading">
      <FormGroupTitle style="margin-bottom: 16px">批次信息</FormGroupTitle>
      <div class="table">
        <div class="row">
          <div class="cells title">微信商户</div>
          <div class="cells">
            <pre>{{ info.wechatMerchant }}</pre>
          </div>
          <div class="cells title">批次号</div>
          <AutoEllipsisTooltip
            class="cells"
            style="
              width: 300px;
              display: block;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              line-height: 30px;
            "
            :content="info.batchNo"
          />
          <div class="cells title">批次发起时间</div>
          <span class="cells">{{ formatDate(info.sendTime) }} </span>
        </div>
        <div class="row">
          <div class="cells title">回单附件下载</div>
          <pre
            class="cells span-5"
            :class="[info.file ? 'active' : '']"
            @click="packageDownload"
            >{{ info.file ? info.file.info.name : '-' }}</pre
          >
        </div>
      </div>

      <FormGroupTitle style="margin: 48px 0 16px">发放明细</FormGroupTitle>
      <div>
        <el-table :data="batchList" style="width: 100%" v-loading="listLoading">
          <el-table-column
            prop="sn"
            label="发放唯一id"
            show-overflow-tooltip
            min-width="120"
          >
          </el-table-column>
          <el-table-column
            prop="payReceipt.batchNo"
            label="所属批次号"
            show-overflow-tooltip
            min-width="180"
          >
          </el-table-column>
          <el-table-column prop="sendTime" label="发放时间" width="180">
            <template slot-scope="scope">
              <span>
                {{ scope.row.sendTime || '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="activity.name"
            label="发放活动名称"
            show-overflow-tooltip
            min-width="150"
          ></el-table-column>
          <el-table-column
            prop="receiverName"
            label="领取人姓名"
            show-overflow-tooltip
            min-width="130"
          >
          </el-table-column>
          <el-table-column
            prop="receiverOpenId"
            label="领取人微信openID"
            show-overflow-tooltip
            min-width="150"
          >
          </el-table-column>
          <el-table-column prop="amount" label="金额" align="right">
            <template slot-scope="scope">
              <span>
                {{ '¥' + formatAmount(scope.row.amount) || '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="发放状态">
            <template slot-scope="scope">
              <span>
                {{
                  getOptionsItemLabel(grantStatusOptions, scope.row.status) ||
                  '-'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="failMessage"
            label="发放结果反馈"
            show-overflow-tooltip
            min-width="150"
          >
            <template slot-scope="scope">
              <span>{{
                scope.row.failMessage ? scope.row.failMessage : '-'
              }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div
      style="
        width: 100%;
        height: 58px;
        margin-top: 2px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 0 24px 0;
        box-sizing: border-box;
      "
    >
      <el-pagination
        class="pagination"
        :current-page.sync="searchForm.currPage"
        :page-size="searchForm.pageSize"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </Container>
</template>
<script>
import Container from 'kit/components/marketing/admin/container.vue'
import FormGroupTitle from 'kit/components/marketing/admin/formGroupTitle.vue'
import Form from 'kit/components/marketing/admin/form.vue'
import AutoEllipsisTooltip from 'kit/components/marketing/admin/autoEllipsisTooltip.vue'
import formatDateTime from 'kit/formatters/dateTime'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import { grantStatusOptions } from '../options'
import { delay } from 'kit/helpers/delay'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import formatAmount from 'kit/formatters/formatAmount'
import { handleError } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    Container,
    FormGroupTitle,
    Form,
    AutoEllipsisTooltip
  },
  data() {
    return {
      getOptionsItemLabel,
      formatAmount,
      grantStatusOptions,
      info: {
        wechatMerchant: '',
        batchNo: '',
        sendTime: '',
        file: {
          info: {
            name: ''
          },
          url: ''
        }
      },
      searchForm: {
        currPage: 1,
        pageSize: 20,
        filters: { deptId: '' }
      }, // 分页参数
      total: 0, // 分页总数
      batchList: [],
      isLoading: false,
      listLoading: false
    }
  },
  computed: {
    batchId() {
      return this.$route.params.batchId
    }
  },
  created() {
    this.getDetail()
    this.getBatchDetailList()
  },
  methods: {
    async getDetail() {
      this.isLoading = true
      const [err, r] = await marketingClient.adminReceiptDetail({
        body: {
          batchId: this.batchId
        },
        ...authorizationToken()
      })
      this.isLoading = false

      if (err) return handleError(err)
      Object.assign(this.info, r.data)
    },
    formatDate(value) {
      if (!value) return '-'
      return formatDateTime('yyyy-MM-dd', value)
    },
    handleSizeChange(val) {
      this.searchForm.pageSize = val
      this.searchForm.currPage = 1
      this.getBatchDetailList()
    },
    handleCurrentChange(currentPage) {
      this.getBatchDetailList(currentPage)
    },
    async getBatchDetailList(currentPage) {
      var page = currentPage ? currentPage : 1
      this.listLoading = true
      const [err, r] = await marketingClient.adminTransactionList({
        body: {
          filters: {
            batchId: this.batchId
          },
          start: page,
          limit: this.searchForm.pageSize,
          sorts: []
        },
        ...authorizationToken()
      })
      if (err) {
        this.listLoading = false
        handleError(err)
        return
      }
      await delay(100)
      this.listLoading = false
      this.batchList = r.data.list
      this.total = r.data.total
    },
    packageDownload() {
      if (!this.info.file) return
      window.open(this.info.file.url)
    }
  }
}
</script>
<style scoped>
.wrap {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 195px);
  overflow-y: scroll;
  padding: 24px 24px 0;
  box-sizing: border-box;
}
.form-item {
  color: rgb(119, 124, 148);
  font-weight: 600;
}
.active {
  color: #01a7f0;
  cursor: pointer;
}
.table {
  border: 1px solid #e4e7edff;
  border-bottom: 0;
  border-right: 0;
}
.row {
  grid-auto-flow: row dense;
  display: grid;
  grid-template-columns: 195px repeat(5, 1fr);
  border-bottom: 1px solid #e4e7edff;
}
.span-5 {
  grid-column-end: span 5;
}
.span-3 {
  grid-column-end: span 3;
}

.cells {
  padding: 8px 24px;
  padding-right: 10px;
  box-sizing: border-box;
  border-right: 1px solid #e4e7edff;
  min-height: 46px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
}
.title {
  background-color: #f7f9fc;
  color: #1e2228ff;
  line-height: 22px;
}
pre {
  white-space: pre-wrap;
  line-height: 18px;
}
.el-table::before {
  display: none;
}
::v-deep .cell {
  font-size: 12px;
  color: rgb(119, 124, 148);
  font-weight: 400;
}
::v-deep .el-table th.el-table__cell {
  background: #f8fafd;
  border-bottom: none;
}
::v-deep .el-table td.el-table__cell div {
  color: #070f29;
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}
::v-deep .el-table--scrollable-x .el-table__body-wrapper {
  padding-bottom: 12px;
}
</style>
