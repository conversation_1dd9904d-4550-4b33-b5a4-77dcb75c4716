<template>
  <o-pc-list
    ref="pc-list"
    title="微信支付回单下载"
    :formJson="searchFormJson"
    :requestFn="getListApi"
    labelWidth="100px"
    :deleteNullApiParams="true"
    :tableHeaderActionButtons="tableHeaderActionButtons"
    :tableHeader="tableHeader"
    :beforeSearch="beforeSearch"
  />
</template>
<script>
import AutoEllipsisTooltip from 'kit/components/marketing/admin/autoEllipsisTooltip.vue'
import { downloadStatusOptions } from '../options'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import { showMessage } from 'kit/helpers/showMessage'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import { handleError } from 'kit/helpers/marketingBossToken'
import { oConfirm } from 'kit/components/marketing/admin/messageBox'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.adminReceiptList({
    body: params,
    ...authorizationToken()
  })
  if (err) return handleError(err)
  return result.data
}

export default {
  data() {
    return {
      getListApi: loadList,
      searchFormJson: [
        {
          type: 'input',
          item: {
            prop: 'batchNo',
            label: '微信转账批次号',
            placeholder: '请输入微信转账批次号'
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            prop: 'sendTime',
            label: '批次发起时间',
            rangeSeparator: '~',
            startField: 'sendTimeBegin',
            endField: 'sendTimeEnd',
            valueFormat: 'yyyy-MM-dd 00:00:00'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'wechatMerchant',
            label: '微信商户',
            placeholder: '请输入微信商户'
          }
        }
      ],
      isFirstLoad: true,
      tableHeader: [
        {
          prop: 'batchNo',
          label: '微信转账批次号',
          minWidth: 120,
          fixed: true,
          click: row =>
            this.$router.push(`/weChatPayReceiptDetail/${row.batchId}`)
        },
        {
          prop: 'sendTime',
          label: '批次发起时间',
          minWidth: 150
        },
        {
          prop: 'status',
          label: '状态',
          formatter: row => {
            return getOptionsItemLabel(downloadStatusOptions, row.status) || '-'
          }
        },
        {
          prop: 'wechatMerchant',
          label: '微信商户'
        },
        {
          prop: 'file',
          label: '附件下载',
          minWidth: 150,
          render: (h, row) => {
            if (!row.file?.info.name) return h('span', '-')
            return h(AutoEllipsisTooltip, {
              style: {
                color: '#01A7F0',
                lineHeight: '18px',
                cursor: 'pointer'
              },
              props: {
                content: row.file.info.name,
                tag: 'span',
                ellipsis: 2
              },
              on: {
                click: () => window.open(row.file.url)
              }
            })
          }
        }
      ],
      tableHeaderActionButtons: [
        {
          label: '打包下载',
          click: () => {
            oConfirm(
              '点击确认后将会生成下载任务，这将耗费一段时间，请稍后点击下载记录查看',
              '批量下载',
              {
                confirm: async () => {
                  if (!this.oPcList.oTable.context.store.tableData.length) {
                    return this.$message.warning('暂无数据可下载！')
                  }
                  const body = this.oPcList.oTable.getRequestParams()
                  const [err] =
                    await marketingClient.adminReceiptPackageDownload({
                      body,
                      ...authorizationToken()
                    })
                  if (err) return handleError(err)
                  showMessage('操作成功')
                }
              }
            )
          }
        },
        {
          align: 'left',
          type: 'button',
          label: '下载记录',
          click: () => {
            this.$router.push('/downloadRecord')
          }
        }
      ]
    }
  },
  computed: {
    oPcList() {
      return this.$refs['pc-list']
    }
  },
  activated() {
    if (!this.isFirstLoad) this.tableReload()
  },
  methods: {
    async tableReload() {
      this.oPcList.reload()
    },
    // 搜索之前对参数处理
    async beforeSearch(fData) {
      fData.sendTimeEnd = fData.sendTimeEnd.replace('00:00:00', '23:59:59')
      return fData
    }
  }
}
</script>
