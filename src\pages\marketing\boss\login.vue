<template>
  <div class="login-bg" style="width: 100%; position: relative">
    <img
      class="img-size"
      width="160px"
      style="position: absolute; left: 128px; top: 28px"
      src="kit/assets/images/header-logo.png"
    />
    <div class="content" style="display: flex; height: 100%">
      <img
        style="height: 426px; position: absolute; left: 100px; bottom: 13%"
        width="638px"
        src="kit/assets/images/operation-login.png"
      />
      <div class="login-form">
        <div>
          <div
            style="
              color: #f77234ff;
              font-size: 24px;
              font-weight: 600;
              text-align: center;
              margin-bottom: 28px;
            "
          >
            运营管理平台登录
          </div>
          <el-form :model="loginForm" :rules="loginFormRules" ref="loginForm">
            <el-form-item prop="cellPhone">
              <el-input
                v-model="loginForm.cellPhone"
                placeholder="请输入手机号"
                maxlength="11"
              ></el-input>
            </el-form-item>

            <el-form-item prop="verifyCode">
              <Input
                v-model="loginForm.verifyCode"
                :allowZero="true"
                placeholder="请输入图形验证码"
                maxlength="4"
              />
              <img class="captcha" @click="loadCaptcha" :src="src" alt />
            </el-form-item>
            <el-form-item prop="smsCode">
              <Input
                v-model="loginForm.smsCode"
                :allowZero="true"
                valueType="int"
                placeholder="请输入短信验证码"
                maxlength="6"
              />
              <MessageCode
                :cellPhone="loginForm.cellPhone"
                :verifyCode="loginForm.verifyCode"
                :verifyCodeId="loginForm.verifyCodeId"
                @getSmsCode="getSmsCode"
                @refreshCaptcha="loadCaptcha"
              />
            </el-form-item>
            <el-button
              style="width: 100%; margin: 16px 0; border-radius: 6px"
              type="primary"
              :loading="loading"
              @click="login"
              >登 录</el-button
            >
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Input from 'kit/components/marketing/admin/input.vue'
import MessageCode from '../admin/getValidCode.vue'
import loginImg from 'kit/assets/images/loginImage.png'
import { validateImgCode, validateTel } from '../admin/util/index.js'
import { loadProfile } from './util/profile'
import { setToken } from 'kit/helpers/marketingBossToken'
import store from 'kit/helpers/store'
import { handleError } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    Input,
    MessageCode
  },
  data() {
    return {
      loginImg,
      loginForm: {
        cellPhone: '',
        smsCode: '',
        verifyCode: '',
        verifyCodeId: ''
      },
      loginFormRules: {
        cellPhone: [
          { validator: validateTel, required: true, trigger: 'blur' }
        ],
        verifyCode: [
          { required: true, trigger: 'blur', message: '请输入图形验证码' },
          { validator: validateImgCode, required: true, trigger: 'blur' }
        ],
        smsCode: [
          { required: true, trigger: 'blur', message: '请输入短信验证码' }
        ]
      },
      smsToken: '',
      loading: false
    }
  },
  computed: {
    src() {
      const baseUrl = window.env.api
      if (!baseUrl) {
        handleError('无法获取平台的API地址')
        return
      }
      return `${baseUrl}/marketing/sms/imageCaptcha?token=${encodeURIComponent(
        this.loginForm.verifyCodeId
      )}`
    }
  },
  created() {
    window.that = this
    this.loadCaptcha()
    if (store.get('boss_login_mobile')) {
      this.loginForm.cellPhone = store.get('boss_login_mobile')
    }
  },
  methods: {
    async loadCaptcha() {
      const [err, r] = await marketingClient.smsCreateImageCaptcha()
      if (err) {
        handleError(err)
        return
      }
      this.loginForm.verifyCodeId = r.data
    },
    getSmsCode(val) {
      this.smsToken = val
    },
    async login() {
      await this.$refs.loginForm.validate()
      this.loading = true
      const [err, r] = await marketingClient.adminUserSmsLogin({
        body: {
          mobile: this.loginForm.cellPhone,
          smsCode: this.loginForm.smsCode,
          smsToken: this.smsToken
        }
      })
      this.loading = false
      if (err) {
        handleError(err)
        return
      }
      store.set('boss_login_mobile', this.loginForm.cellPhone)

      setToken(r.data.token)
      await loadProfile()
      window.location.href = `${this.$router.options.base}/merchantManagement/merchants`
    }
  }
}
</script>

<style scoped>
.login-bg {
  background-image: url('kit/assets/images/<EMAIL>');
  background-size: cover;
}
.login-form {
  width: 440px;
  height: 540px;
  position: absolute;
  right: 15%;
  top: 12%;
  box-sizing: border-box;
  padding: 56px 44px 158px;
  background-color: #fff;
  border-radius: 13px;
}
.captcha {
  position: absolute;
  width: 80px;
  height: 30px;
  right: 10px;
  top: 5px;
}
::v-deep .el-form-item {
  margin-bottom: 16px;
}
</style>
