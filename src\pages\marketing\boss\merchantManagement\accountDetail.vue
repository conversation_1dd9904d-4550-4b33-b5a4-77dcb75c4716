<template>
  <o-container ref="container" back="true" :title="$route.meta.title">
    <!-- 状态筛选tabs -->
    <Operate :info="info" :id="id" @refresh="refresh" />
    <!-- 筛选区域 -->
    <o-top-select
      style="border: 8px; margin-bottom: 16px"
      ref="top-select"
      :formJson="topSelectFormJson"
      :immediate="true"
      class="zp-mb-16 o-app"
      labelWidth="85px"
      @search="onSearch"
    />

    <!-- 表格区域 -->
    <o-table
      ref="o-table"
      :sticky="true"
      :pagination="{ fixed: true }"
      :showPagination="true"
      :deleteNullApiParams="true"
      :tableHeader="tableHeader"
      :requestFn="getListApi"
      emptyHeight="calc(100vh - 450px)"
    />
  </o-container>
</template>

<script>
import Operate from './accountDetail/operate.vue'
import AutoEllipsisTooltip from 'kit/components/marketing/admin/autoEllipsisTooltip.vue'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import { typeOptions, itemOptions } from '../options'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import formatAmount from 'kit/formatters/formatAmount'
import symbolAmount from 'kit/formatters/symbolAmount'
import { handleError } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.adminAccountBillList({
    body: params,
    ...authorizationToken()
  })
  if (err) return handleError(err)
  result.data.list.forEach(item => {
    item.remark = item.remark.replace(/\n/g, '')
  })
  return result.data
}
export default {
  components: {
    Operate
  },
  data() {
    return {
      info: {
        id: '',
        name: '',
        contactsName: '',
        contactsMobile: '',
        currentAmount: 0,
        creditAmount: 0,
        rechargeResidueAmount: 0,
        lockedAmount: 0,
        creditAvailableAmount: 0
      },
      topSelectFormJson: [
        {
          type: 'select',
          item: {
            prop: 'type',
            label: '类型',
            placeholder: '请选择类型',
            options: typeOptions
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            rangeSeparator: '~',
            endPlaceholder: '结束日期',
            rangeSeparator: '~',
            prop: 'createTime',
            label: '事项变更时间',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd 00:00:00'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'sn',
            label: '流水号',
            placeholder: '请输入流水号'
          }
        }
      ],
      isFirstLoad: true,
      getListApi: loadList,
      tableHeader: [
        {
          label: '流水号',
          prop: 'sn',
          fixed: true,
          minWidth: 150
        },
        {
          label: '事项变更时间',
          prop: 'createTime',
          type: 'DATE_TIME',
          minWidth: 150
        },
        {
          label: '类型',
          prop: 'type',
          formatter: row => {
            return getOptionsItemLabel(typeOptions, row.type) || '-'
          }
        },
        {
          label: '事项',
          prop: 'item',
          minWidth: 120,
          formatter: row => {
            return getOptionsItemLabel(itemOptions, row.item) || '-'
          }
        },
        {
          label: '金额',
          prop: 'amount',
          type: 'AMOUNT',
          minWidth: 120,
          formatter: row => {
            return symbolAmount(row.symbol) + '￥' + formatAmount(row.amount)
          }
        },
        {
          label: '操作人',
          prop: 'createBy.name'
        },
        {
          label: '备注',
          minWidth: 120,
          prop: 'remark'
        },
        {
          label: '附件',
          width: 150,
          prop: 'info',
          showOverflowTooltip: false,
          render: (h, row) => {
            if (!row.file) return h('span', '-')
            return h(AutoEllipsisTooltip, {
              style: {
                color: '#5374f6',
                lineHeight: '18px',
                cursor: 'pointer'
              },
              props: {
                content: row.file.info.name,
                tag: 'span',
                ellipsis: 2
              },
              on: {
                click: () => window.open(row.file.url)
              }
            })
          }
        }
      ]
    }
  },
  computed: {
    oTable() {
      return this.$refs['o-table']
    },
    id() {
      return this.$route.params.id
    }
  },
  created() {
    this.loadDetail()
  },
  methods: {
    refresh() {
      this.loadDetail()
      this.tableReload()
    },
    async tableReload() {
      this.oTable.reload()
    },
    async loadDetail() {
      const [err, r] = await marketingClient.adminAccountDetail({
        body: {
          merchantId: this.id
        },
        ...authorizationToken()
      })
      if (err) return handleError(err)
      this.info = r.data
    },

    async onSearch() {
      const fData = await this.$refs['top-select'].getFormData()
      fData.merchantId = this.id
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')
      await this.oTable.appendRequestParams(fData)
      this.isFirstLoad = false
    }
  }
}
</script>

<style scoped>
.wrap {
  padding: 24px 0;
}
</style>
