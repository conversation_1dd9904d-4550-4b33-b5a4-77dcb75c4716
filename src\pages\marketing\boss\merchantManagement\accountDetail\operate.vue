<template>
  <div style="background: #f7f9fc; margin: 16px 0; padding: 16px 24px">
    <div style="display: flex">
      <el-button class="button" @click="adjust">调整授信额度</el-button>
      <el-button class="button" @click="recharge">充值</el-button>
      <el-button class="button" @click="refund">退款</el-button>
    </div>
    <div
      style="
        display: flex;
        margin: 16px 0;
        padding: 16px;
        justify-content: space-between;
        background: #ffffff;
      "
    >
      <div>
        <div class="cell_title">企业id</div>
        <div class="cell">{{ info.id }}</div>
      </div>
      <div>
        <div class="cell_title">企业名称</div>
        <AutoEllipsisTooltip class="cell" :content="info.name" />
      </div>
      <div>
        <div class="cell_title">企业负责人姓名</div>
        <AutoEllipsisTooltip class="cell" :content="info.contactsName" />
      </div>
      <div>
        <div class="cell_title">企业负责人联系方式</div>
        <div class="cell">{{ info.contactsMobile }}</div>
      </div>
    </div>
    <div class="statistics">
      <div style="display: flex; align-items: center; flex: 1">
        <div class="icon" style="margin-right: 16px">
          <img width="24px" src="kit/assets/images/money_b.png" />
        </div>
        <div>
          <div class="title">账户当前余额（元）</div>
          <AutoEllipsisTooltip
            class="money"
            style="max-width: 162px; font-size: 32px; line-height: 42px"
            :content="formatNumber(info.currentAmount)"
          />
        </div>
      </div>

      <div style="display: flex; align-items: center; flex: 1">
        <div class="line"></div>
        <div>
          <div style="display: flex; align-items: center; margin-bottom: 8px">
            <div class="cell" style="flex: 0 0 170px">授信额度（元）</div>
            <AutoEllipsisTooltip
              class="money"
              :content="formatNumber(info.creditAmount)"
            />
          </div>
          <div style="display: flex; align-items: center">
            <div class="cell" style="flex: 0 0 170px">预充值剩余额度（元）</div>
            <AutoEllipsisTooltip
              class="money"
              :content="formatNumber(info.rechargeResidueAmount)"
            />
          </div>
        </div>
      </div>

      <div style="display: flex; align-items: center; flex: 1">
        <div class="line"></div>
        <div>
          <div style="display: flex; align-items: center; margin-bottom: 8px">
            <div class="cell" style="flex: 0 0 208px">
              账户当前锁定额度（元）
            </div>
            <AutoEllipsisTooltip
              class="money"
              :content="formatNumber(info.lockedAmount)"
            />
          </div>
          <div style="display: flex; align-items: center">
            <div class="cell" style="flex: 0 0 208px">
              账户当前剩余可用额度（元）
            </div>
            <AutoEllipsisTooltip
              class="money"
              :content="formatNumber(info.creditAvailableAmount)"
            />
          </div>
        </div>
      </div>
    </div>

    <AdjustDialog ref="adjustDialog" :id="id" @refresh="$emit('refresh')" />
    <RechargeDialog ref="rechargeDialog" :id="id" @refresh="$emit('refresh')" />
    <RefundDialog
      ref="refundDialog"
      :id="id"
      :refundAmount="info.refundAmount"
      @refresh="$emit('refresh')"
    />
  </div>
</template>
<script>
import AutoEllipsisTooltip from 'kit/components/marketing/admin/autoEllipsisTooltip.vue'
import AdjustDialog from './adjustDialog.vue'
import RechargeDialog from './rechargeDialog.vue'
import RefundDialog from './refundDialog.vue'
import formatNumber from 'kit/formatters/number'
export default {
  components: {
    AutoEllipsisTooltip,
    AdjustDialog,
    RechargeDialog,
    RefundDialog
  },
  props: {
    info: {
      type: Object,
      default: () => {}
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formatNumber
    }
  },
  methods: {
    adjust() {
      this.$refs.adjustDialog.open()
    },
    recharge() {
      this.$refs.rechargeDialog.open()
    },
    refund() {
      this.$refs.refundDialog.open()
    }
  }
}
</script>
<style scoped>
.button {
  padding: 0 16px;
  height: 32px;
  color: #393a3b;
  font-weight: 400;
  line-height: 32px;
  border-radius: 6px;
}
.cell_title {
  margin-bottom: 8px;
  color: #828b9b;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}
.cell {
  width: 240px;
  color: #1e2228;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.statistics {
  display: flex;
  padding: 16px;
  align-items: center;
  /* gap: 24px; */
  align-self: stretch;
  border-radius: 8px;
  background: #fff;
}
.icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  fill: var(--10-color-1, #37f);
  background: #cfe5ff;
  border-radius: 50px;
}
.title {
  margin-bottom: 2px;
  color: #1e2228;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}
.money {
  max-width: 75px;
  color: #1e2228;
  font-family: 'DIN Alternate';
  font-size: 20px;
  font-weight: 700;
  line-height: 32px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.line {
  width: 1px;
  height: 56px;
  background: #3377ff;
  opacity: 0.16;
  margin-right: 24px;
}
::v-deep .ellipsis-1 {
  white-space: pre;
}
</style>
