<template>
  <div>
    <el-dialog
      v-if="visible"
      title="充值"
      :visible="visible"
      width="560px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <Form
        ref="form"
        :model="formData"
        :rules="formRules"
        label-position="top"
      >
        <el-form-item label="额度" prop="amount">
          <el-row type="flex">
            <Input
              v-model="formData.amount"
              valueType="decimals_2"
              placeholder="请输入金额"
              :allowZero="true"
              :autoRetainTwoDecimalPlaces="true"
              maxlength="8"
            />
            <span style="margin-left: 6px">元</span>
          </el-row>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <Textarea
            maxlength="1024"
            :trim="true"
            v-model="formData.remark"
            placeholder="请输入备注，不超过1024字"
          />
        </el-form-item>
        <el-form-item style="margin-bottom: 40px" label="附件">
          <el-upload
            :headers="headerToken"
            ref="upload"
            class="upload"
            drag
            :data="uploadData"
            :file-list="fileList"
            :before-upload="beforeUpload"
            :on-success="onSuccess"
            :on-change="onChange"
            :on-remove="onRemove"
            :action="uploadUrl"
          >
            <div style="text-align: left; display: flex">
              <img
                width="112px"
                style="height: 32px"
                src="kit/assets/images/upload_button.png"
                alt=""
              />
              <span style="margin-left: 10px; color: #828b9b">
                请上传大小不超过50MB的附件
              </span>
            </div>
          </el-upload>
        </el-form-item>
      </Form>
      <span slot="footer" class="dialog-footer">
        <el-button class="button" @click="close">取 消</el-button>
        <el-button
          class="button"
          type="primary"
          :loading="loading"
          @click="confirm"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getToken } from 'kit/helpers/marketingBossToken'
import { showMessage } from 'kit/helpers/showMessage'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import Form from 'kit/components/marketing/admin/form.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import Textarea from 'kit/components/marketing/admin/textarea.vue'
import { handleError } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    Form,
    Input,
    Textarea
  },
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      headerToken: {
        Authorization: `${getToken()}`
      },
      uploadData: {},
      fileList: [],
      archiveId: '',
      formData: {
        amount: '',
        remark: ''
      },
      formRules: {
        amount: [{ required: true, message: '请输入金额', trigger: 'blur' }],
        remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
      }
    }
  },
  computed: {
    uploadUrl() {
      return `${window.env.api}/marketing/file/upload`
    }
  },
  methods: {
    open() {
      this.visible = true
    },
    close() {
      this.formData = {
        amount: '',
        remark: '',
        file: ''
      }
      this.fileList = []
      this.visible = false
    },
    async confirm() {
      const error = await this.$refs.form.validate()
      if (error) return
      this.loading = true
      this.formData.merchantId = this.id
      if (this.fileList.length) {
        this.formData.file = this.archiveId
      }

      const [err, r] = await marketingClient.adminAccountRecharge({
        body: this.formData,
        ...authorizationToken()
      })
      this.loading = false
      if (err) {
        handleError(err)
        return
      }
      showMessage('操作成功！')
      this.close()
      this.$emit('refresh')
    },
    beforeUpload(file) {
      const isSize = file.size / 1024 / 1024 < 50
      if (!isSize) {
        showMessage('只能上传大小不超过50MB的附件', 'error')
        return false
      }
    },
    async onSuccess(res) {
      this.archiveId = res.data.archiveId
    },
    onChange(file, fileList) {
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]] // 这一步，是展示最后一次选择的csv文件
      } else {
        this.fileList = []
      }
    },
    onRemove() {
      this.fileList = []
    }
  }
}
</script>
<style scoped>
::v-deep .el-dialog__header {
  padding: 16px 24px;
  box-sizing: border-box;
  border: none;
}

::v-deep .el-dialog__body {
  padding: 24px;
  border: 1px solid #f8f8f8;
}
::v-deep .el-dialog__title {
  color: #1e2228;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}
::v-deep .el-form-item__label {
  line-height: 22px;
  padding-bottom: 0px;
  margin-bottom: 8px;
}
::v-deep .el-upload-dragger {
  height: 32px;
  border: none;
}
::v-deep .el-dialog__footer {
  padding: 10px 24px 10px 0;
}
.upload {
  height: 32px;
}
.button {
  height: 32px;
  line-height: 32px;
  padding: 0;
}
</style>
