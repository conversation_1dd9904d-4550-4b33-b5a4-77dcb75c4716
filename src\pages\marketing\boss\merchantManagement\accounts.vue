<template>
  <o-container ref="container" :title="$route.meta.title">
    <MoneySummaries />
    <!-- 筛选区域 -->
    <o-top-select
      style="border-radius: 8px; margin-bottom: 16px"
      ref="top-select"
      :formJson="topSelectFormJson"
      :immediate="true"
      class="zp-mb-16 o-app"
      labelWidth="100px"
      @search="onSearch"
    />

    <!-- 表格区域 -->
    <o-table
      ref="o-table"
      :sticky="true"
      :pagination="{ fixed: true }"
      :showPagination="true"
      :deleteNullApiParams="true"
      :tableHeader="tableHeader"
      :requestFn="getListApi"
      emptyHeight="calc(100vh - 450px)"
    />
  </o-container>
</template>
<script>
import MoneySummaries from './accounts/moneySummaries.vue'
import formatAmount from 'kit/formatters/formatAmount'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { handleError } from 'kit/helpers/marketingBossToken'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.adminAccountList({
    body: params,
    ...authorizationToken()
  })
  if (err) return handleError(err)
  return result.data
}
export default {
  components: {
    MoneySummaries
  },
  data() {
    return {
      topSelectFormJson: [
        {
          type: 'input',
          item: {
            prop: 'merchantId',
            label: '企业id',
            placeholder: '请输入企业id'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'merchantName',
            label: '企业名称',
            placeholder: '请输入企业名称'
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            rangeSeparator: '~',
            prop: 'create',
            label: '创建时间',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd 00:00:00'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'contactsName',
            label: '企业负责人姓名',
            placeholder: '请输入企业负责人姓名'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'contactsMobile',
            label: '负责人联系方式',
            placeholder: '请输入负责人联系方式'
          }
        }
      ],
      isFirstLoad: true,
      getListApi: loadList,
      tableHeader: [
        {
          prop: 'id',
          label: '企业id',
          fixed: true
        },
        {
          prop: 'name',
          label: '企业名称',
          minWidth: 150,
          click: row => this.$router.push(`/accountDetail/${row.id}`)
        },
        {
          prop: 'contactsName',
          label: '企业负责人姓名'
        },
        {
          prop: 'contactsMobile',
          label: '负责人联系方式'
        },
        {
          prop: 'creditAvailableAmount',
          label: '账户可用总额度',
          type: 'AMOUNT'
        },
        {
          prop: 'creditAmount',
          label: '授信额度',
          type: 'AMOUNT'
        },
        {
          prop: 'rechargeAmount',
          label: '预充值额度',
          type: 'AMOUNT'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          type: 'DATE_TIME'
        }
      ]
    }
  },
  computed: {
    oTable() {
      return this.$refs['o-table']
    }
  },
  activated() {
    if (!this.isFirstLoad) this.tableReload()
  },
  methods: {
    async tableReload() {
      this.oTable.reload()
    },
    // 搜索
    async onSearch() {
      const fData = await this.$refs['top-select'].getFormData()
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')

      await this.oTable.appendRequestParams(fData)
      this.isFirstLoad = false
    }
  }
}
</script>
<style scoped></style>
