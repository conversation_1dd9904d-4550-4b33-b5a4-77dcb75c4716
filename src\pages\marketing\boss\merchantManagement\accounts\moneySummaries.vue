<template>
  <div class="summary" v-if="moneyInfo">
    <div class="container-box">
      <div class="icon">
        <img width="24px" src="kit/assets/images/operator.png" />
      </div>
      <div>
        <div class="title">运营企业账户数（家）</div>
        <div class="money">{{ moneyInfo.merchantCount }}</div>
      </div>
    </div>
    <div
      class="container-box"
      style="background: linear-gradient(180deg, #f5fef2 0%, #e6feee 100%)"
    >
      <div class="icon" style="background: #d2fae4">
        <img width="24px" src="kit/assets/images/money_g.png" />
      </div>
      <div>
        <div class="title">授信总额度（元）</div>
        <div class="money">{{ formatNumber(moneyInfo.creditAmount) }}</div>
      </div>
    </div>
    <div
      class="container-box"
      style="
        margin-right: 0;
        background: linear-gradient(180deg, #fffdf6 0%, #fff7ec 100%);
      "
    >
      <div class="icon" style="background: #fff2cd">
        <img width="24px" src="kit/assets/images/money_o.png" />
      </div>
      <div>
        <div class="title">预充值总额度（元）</div>
        <div class="money">{{ formatNumber(moneyInfo.rechargeAmount) }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import formatNumber from 'kit/formatters/number'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { handleError } from 'kit/helpers/marketingBossToken'
const marketingClient = makeMarketingClient()

export default {
  data() {
    return {
      moneyInfo: null
    }
  },
  async created() {
    this.loading = true
    const [err, r] = await marketingClient.adminAccountStatistics({
      body: {},
      ...authorizationToken()
    })
    this.loading = false
    if (err) {
      handleError(err)
      return
    }
    this.moneyInfo = r.data
  },
  methods: {
    formatNumber
  }
}
</script>
<style scoped>
.summary {
  display: flex;
  padding: 16px 0;
}
.container-box {
  display: flex;
  width: 280px;
  height: 78px;
  padding: 12px 20px;
  margin-right: 16px;
  box-sizing: border-box;
  align-items: flex-start;
  gap: 16px;
  flex: 1 0 0;
  border-radius: 4px;
  background: linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%);
}
.icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  fill: var(--10-color-1, #37f);
  background: #cfe5ff;
  border-radius: 50px;
}
.title {
  color: #1e2228;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.money {
  color: #1e2228;
  font-family: 'DIN Alternate';
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 32px;
}
</style>
