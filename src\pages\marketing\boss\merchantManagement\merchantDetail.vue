<template>
  <Container :back="true" :title="$route.meta.title">
    <main style="padding: 24px; min-height: 60vh" v-loading="isLoading">
      <div>
        <div class="table">
          <div class="row">
            <div class="cell title">企业id</div>
            <div class="cell" style="width: 240px">{{ info.id }}</div>
            <div class="cell title">企业名称</div>
            <div class="cell span-3">
              <pre>{{ info.name }}</pre>
            </div>
          </div>
          <div class="row">
            <div class="cell title">企业负责人姓名</div>
            <div class="cell" style="width: 240px">
              <pre>{{ info.contactsName }}</pre>
            </div>
            <div class="cell title">负责人联系方式</div>
            <div class="cell span-3">{{ info.contactsMobile }}</div>
          </div>
          <div class="row">
            <div class="cell title">创建时间</div>
            <span class="cell span-5">{{ info.createTime || '-' }} </span>
          </div>
          <div class="row">
            <div class="cell title">状态</div>
            <pre class="cell span-5">{{
              getOptionsItemLabel(merchantStatusOptions, info.status) || '-'
            }}</pre>
          </div>
        </div>
      </div>
    </main>
  </Container>
</template>

<script>
import Container from 'kit/components/marketing/admin/container.vue'
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import { merchantStatusOptions } from '../options'
import { delay } from 'kit/helpers/delay'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { handleError } from 'kit/helpers/marketingBossToken'

const marketingClient = makeMarketingClient()

export default {
  components: {
    Container
  },
  data() {
    return {
      isLoading: false,
      info: {
        id: '',
        name: '',
        contactsName: '',
        contactsMobile: '',
        createTime: ''
      },
      getOptionsItemLabel,
      merchantStatusOptions
    }
  },
  computed: {
    id() {
      return this.$route.params.id
    }
  },
  created() {
    this.loadDetail()
  },
  methods: {
    async loadDetail() {
      this.isLoading = true
      const [err, result] = await marketingClient.adminMerchantDetail({
        body: {
          merchantId: this.id
        },
        ...authorizationToken()
      })
      if (err) {
        this.isLoading = false
        return handleError(err)
      }
      await delay(100)
      this.isLoading = false
      Object.assign(this.info, result.data)
    }
  }
}
</script>

<style scoped>
.table {
  border: 1px solid #e4e7edff;
  border-bottom: 0;
  border-right: 0;
}
.row {
  grid-auto-flow: row dense;
  display: grid;
  grid-template-columns: 195px repeat(5, 1fr);
  border-bottom: 1px solid #e4e7edff;
}
.span-5 {
  grid-column-end: span 5;
}
.span-3 {
  grid-column-end: span 3;
}

.cell {
  padding: 8px 24px;
  padding-right: 10px;
  box-sizing: border-box;
  border-right: 1px solid #e4e7edff;
  min-height: 46px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
}
.title {
  background-color: #f7f9fc;
  color: #1e2228ff;
  line-height: 22px;
}
.custom-rules .right {
  color: #1e2228ff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 22px;
}
.custom-rules .right .box {
  border-bottom: 1px solid #e4e7edff;
  line-height: 46px;
  padding-left: 24px;
  border-right: 1px solid #e4e7edff;
}
.custom-rules .right .box:last-child {
  border-bottom: 0;
}
pre {
  white-space: pre-wrap;
  line-height: 18px;
}
.title-button {
  flex: 1;
  display: flex;
  justify-content: end;
}
</style>
