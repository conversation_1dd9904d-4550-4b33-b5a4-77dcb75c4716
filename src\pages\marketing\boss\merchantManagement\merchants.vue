<template>
  <o-pc-list
    ref="pc-list"
    :title="$route.meta.title"
    :formJson="searchFormJson"
    :requestFn="getListApi"
    labelWidth="100px"
    :deleteNullApiParams="true"
    :tableHeader="tableHeader"
    :beforeSearch="beforeSearch"
  />
</template>
<script>
import { getOptionsItemLabel } from 'kit/helpers/getOptionsItemLabel'
import { merchantStatusOptions } from '../options'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import { handleError } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.adminMerchantList({
    body: params,
    ...authorizationToken()
  })
  if (err) return handleError(err)
  return result.data
}

export default {
  data() {
    return {
      getListApi: loadList,
      searchFormJson: [
        {
          type: 'input',
          item: {
            prop: 'merchantId',
            label: '企业id',
            placeholder: '请输入企业id'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'merchantName',
            label: '企业名称',
            placeholder: '请输入企业名称'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'contactsName',
            label: '企业负责人姓名',
            placeholder: '请输入企业负责人姓名'
          }
        },
        {
          type: 'input',
          item: {
            prop: 'contactsMobile',
            label: '负责人联系方式',
            placeholder: '请输入负责人联系方式'
          }
        },
        {
          type: 'datePicker',
          item: {
            type: 'daterange',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            prop: 'createTime',
            label: '企业创建时间',
            rangeSeparator: '~',
            startField: 'createTimeBegin',
            endField: 'createTimeEnd',
            valueFormat: 'yyyy-MM-dd 00:00:00'
          }
        }
      ],
      isFirstLoad: true,
      tableHeader: [
        {
          prop: 'id',
          label: '企业id',
          fixed: true
        },
        {
          prop: 'name',
          label: '企业名称',
          minWidth: 150,
          click: row => this.$router.push(`/merchantDetail/${row.id}`)
        },
        {
          prop: 'contactsName',
          label: '企业负责人姓名'
        },
        {
          prop: 'contactsMobile',
          label: '负责人联系方式'
        },
        {
          prop: 'createTime',
          label: '企业创建时间',
          type: 'DATE_TIME'
        },
        {
          prop: 'status',
          label: '状态',
          formatter: row => {
            return getOptionsItemLabel(merchantStatusOptions, row.status) || '-'
          }
        }
      ]
    }
  },
  computed: {
    oTable() {
      return this.$refs['pc-list']
    }
  },
  activated() {
    if (!this.isFirstLoad) this.tableReload()
  },
  methods: {
    async tableReload() {
      this.oTable.reload()
    },
    // 搜索之前对参数处理
    async beforeSearch(fData) {
      fData.createTimeEnd = fData.createTimeEnd.replace('00:00:00', '23:59:59')
      return fData
    }
  }
}
</script>
<style scoped>
::v-deep .o-top-select {
  margin-bottom: 16px;
}
</style>
