<template>
  <div class="add-person" v-if="drawer">
    <el-drawer
      :title="title"
      :visible.sync="drawer"
      :direction="direction"
      :close-on-press-escape="false"
      :wrapperClosable="false"
      @close="close"
    >
      <div class="content">
        <div class="person-form">
          <el-form
            class="form"
            :model="form"
            :rules="rules"
            ref="form"
            label-width="80px"
          >
            <el-form-item label="姓名" prop="name">
              <Input
                v-model="form.name"
                :allowZero="true"
                :trim="true"
                placeholder="请输入姓名"
                maxlength="20"
              />
            </el-form-item>
            <el-form-item label="手机号" prop="mobile">
              <Input
                v-model="form.mobile"
                valueType="int"
                :allowZero="true"
                placeholder="请输入手机号"
                maxlength="11"
                :disabled="type === 'detail'"
              />
            </el-form-item>
            <el-form-item label="角色">
              <el-select
                style="width: 270px"
                v-model="form.roleIds"
                placeholder="请选择"
                disabled
                multiple
              >
                <el-option
                  v-for="item in roleList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="person-btn">
          <el-button style="color: #1e2228; font-weight: 400" @click="close"
            >取消</el-button
          >
          <el-button type="primary" @click="saveInfo" :loading="loading"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Input from 'kit/components/marketing/admin/input.vue'
import { showMessage } from 'kit/helpers/showMessage'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import { handleError } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

const validateTel = (rule, value, callback) => {
  if (value) {
    let reg = /^1[3|4|5|7|8][0-9]{9}$/
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的手机号'))
    }
  } else {
    if (rule.required) {
      callback(new Error('请输入手机号'))
    } else {
      callback()
    }
  }
}
export default {
  components: {
    Input
  },
  props: {
    id: Number,
    type: String
  },

  watch: {
    drawer(val) {
      if (val) {
        this.getRoleList()
        if (this.type !== 'add') this.getDetails()
        else this.form.roleIds = [1]
      }
    }
  },
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      roleList: [], // 角色列表
      form: {
        name: '',
        mobile: '',
        roleIds: []
      },
      rules: {
        name: [{ required: true, trigger: 'blur', message: '请输入姓名' }],
        mobile: [{ validator: validateTel, required: true, trigger: 'blur' }]
      },
      loading: false
    }
  },
  computed: {
    title() {
      return this.type === 'add' ? '添加人员' : '编辑人员'
    }
  },
  methods: {
    open() {
      this.drawer = true
    },
    close() {
      this.form = {
        name: '',
        mobile: '',
        roleIds: []
      }
      this.drawer = false
    },
    async getDetails() {
      const [err, r] = await marketingClient.adminUserDetail({
        body: {
          adminId: this.id
        },
        ...authorizationToken()
      })
      if (err) {
        handleError(err)
        return
      }

      this.form = r.data

      let arr = []
      if (r.data.role?.length) {
        for (var c of r.data.role) {
          arr.push(c.id)
        }
        this.form.roleIds = arr
      }
    },
    async getRoleList() {
      const [err, r] = await marketingClient.adminRoleList({
        body: {},
        ...authorizationToken()
      })
      if (err) {
        handleError(err)
        return
      }
      this.roleList = r.data
    },
    async saveInfo() {
      await this.$refs.form.validate()
      this.loading = true
      if (this.type === 'add') {
        const [err, r] = await marketingClient.adminUserAdd({
          body: this.form,
          ...authorizationToken()
        })

        this.loading = false
        if (err) return handleError(err)
        showMessage('操作成功！')
        this.close()
        this.$emit('refresh')
        return
      }
      const [err, r] = await marketingClient.adminUserUpdate({
        body: {
          adminId: this.id,
          name: this.form.name
        },
        ...authorizationToken()
      })

      this.loading = false
      if (err) return handleError(err)
      showMessage('操作成功！')
      this.close()
      this.$emit('refresh')
    }
  }
}
</script>

<style scoped>
.content {
  padding: 0 24px;
}
.form > div {
  width: 350px;
}
.person-btn {
  padding-bottom: 20px;
  position: fixed;
  bottom: 0;
  right: 25px;
}
</style>
