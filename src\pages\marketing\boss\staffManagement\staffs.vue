<template>
  <div>
    <o-pc-list
      ref="pc-list"
      :title="$route.meta.title"
      :formJson="searchFormJson"
      :requestFn="getListApi"
      :deleteNullApiParams="true"
      :ifShowTopSelect="false"
      :actionButtons="actionButtons"
      :tableHeaderActionButtons="tableHeaderActionButtons"
      :tableHeader="tableHeader"
    />
    <AddPerson ref="drawer" :id="id" :type="type" @refresh="tableReload" />
  </div>
</template>
<script>
import { showMessage } from 'kit/helpers/showMessage'
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import AddPerson from './addStaff.vue'
import { oConfirm } from 'kit/components/marketing/admin/messageBox'
import { handleError } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

const loadList = async params => {
  const [err, result] = await marketingClient.adminUserList({
    body: params,
    ...authorizationToken()
  })
  if (err) return handleError(err)
  return result.data
}

export default {
  components: {
    AddPerson
  },
  data() {
    return {
      getListApi: loadList,
      type: '',
      id: null,
      searchFormJson: [],
      isFirstLoad: true,
      tableHeader: [
        {
          prop: 'name',
          label: '姓名',
          fixed: true,
          minWidth: 150,
          click: row => {
            this.type = 'detail'
            this.id = row.id
            this.$refs.drawer.open()
          }
        },
        {
          prop: 'mobile',
          label: '手机号'
        },
        {
          prop: 'role',
          label: '角色',
          formatter: row => {
            if (row.role?.length) {
              return row.role.map(item => item.name).join(',')
            }
            return '-'
          }
        }
      ],
      actionButtons: [
        {
          label: '删除',
          click: row => {
            oConfirm('删除后用户将无法登录', '删除人员?', {
              confirm: async () => {
                const [err] = await marketingClient.adminUserDelete({
                  body: {
                    adminId: row.id
                  },
                  ...authorizationToken()
                })
                if (err) return handleError(err)
                this.tableReload()
                showMessage('操作成功')
              }
            })
          }
        }
      ],
      tableHeaderActionButtons: [
        {
          align: 'left',
          type: 'button',
          label: '添加人员',
          click: () => {
            this.type = 'add'
            this.$refs.drawer.open()
          }
        }
      ]
    }
  },
  computed: {
    oTable() {
      return this.$refs['pc-list']
    }
  },
  activated() {
    if (!this.isFirstLoad) this.tableReload()
  },
  methods: {
    tableReload() {
      this.oTable.reload()
    }
  }
}
</script>
