<template>
    <header>
        <h2>设置默认路由</h2>
        <el-row v-for="(item, index) in defaultRouterItems" :key="index" class="box" type="flex">
            <div class="label">{{ item.label }}：</div>
            <span v-if="!item.info.isEdit">{{ item.info.supplierName }}</span>
            <Select v-else size="small" style="width: 200px" :options="options[item.type]"
                v-model="item.info.supplierId" />
            <el-button type="text" v-if="!item.info.isEdit" @click="setDefRoute(item.info)">
                修改
            </el-button>
            <div v-else>
                <el-button type="text" style="margin-left:20px;" @click="saveDefRoute(item.info,item.type)">保存</el-button>
                <el-button type="text" @click="item.info.isEdit = false">取消</el-button>
            </div>
        </el-row>
    </header>
</template>

<script>
import Select from "kit/components/marketing/admin/select.vue";
import { oConfirm } from "kit/components/marketing/admin/messageBox.js";
import { authorizationToken } from "kit/helpers/marketingBossToken";
import { handleError } from "kit/helpers/marketingBossToken";
import makeMarketingClient from "kit/services/marketing/makeClient";
import deepClone from "kit/helpers/deepClone.js";
import { showMessage } from "kit/helpers/showMessage.js";
const marketingClient = makeMarketingClient();

export default {
    components: {
        Select,
    },
    props: {
        options: {
            type: Object,
            default: () => ({ wechatTransfer: [],wechatFund:[] }),
        },
        defaultRouter: {
            type: Object,
            default: () => ({
                wechatFund: {
                    supplierId: "",
                    supplierName: "",
                },
                wechatTransfer: {
                    supplierId: "",
                    supplierName: "",
                },
            }),
        },
    },
    watch:{
        defaultRouter(){
            this.initDefaultRouterItems()
        }
    },
    data(){
        return {
            defaultRouterItems:[]
        }
    },
    methods: {
        initDefaultRouterItems(){
            this.defaultRouterItems =  [
                { label: "默认微信红包资质", type:'wechatTransfer', info: deepClone(this.defaultRouter.wechatTransfer) },
                { label: "默认微信立减金资质", type:'wechatFund', info: deepClone(this.defaultRouter.wechatFund) },
            ];
        },
        setDefRoute(info) {
            this.$set(info, "isEdit", true);
        },

        async saveDefRoute(info,bizType) {
            oConfirm(
                "路由修改后业务会进行变更，请务必谨慎操作！",
                "提示",
                {
                    confirm: async () => {
                        const [err, result] = await marketingClient.adminTransferSupplierSetDefaultRoute(
                            {
                                body: {
                                    ordTransferSupplierId: this.defaultRouter[bizType].supplierId,
                                    newTransferSupplierId: info.supplierId,
                                    bizType: bizType
                                },
                                ...authorizationToken(),
                            }
                        );
                        if (err) return handleError(err);
                        this.$emit("save-success", result.data);
                        showMessage("操作成功")
                    },
                }
            );
        },
    },
};
</script>
<style scoped>
header {
    font-size: 14px;
    padding: 10px 0;
    padding-bottom: 0;
}

header h2 {
    margin-bottom: 10px;
    padding-top: 10px;
}

header .box {
    align-items: center;
    color: #333;
    line-height: 40px;
}
</style>