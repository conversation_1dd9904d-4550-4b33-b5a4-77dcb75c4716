<template>
    <div v-if="visible">
        <el-dialog :title="title" :visible.sync="visible" width="560px">
            <Form :model="form" :rules="rules" ref="form" label-width="130px">
                <el-form-item label="商户名称" prop="merchantId" >
                    <Select :disabled="isDisabled" style="width: 100%" placeholder="请选择商户名称" :options="options.merchantId" v-model="form.merchantId" />
                </el-form-item>

                <el-form-item label="商户id" v-show="form.merchantId">
                    {{ form.merchantId }}
                </el-form-item>

                <el-form-item label="资质地名称" prop="supplierId">
                    <Select style="width: 100%" placeholder="请选择资质地名称"  :options="options.supplierId" v-model="form.supplierId" />
                </el-form-item>

                <el-form-item label="资质地id" v-show="form.supplierId">
                    {{ form.supplierId }}
                </el-form-item>

                <el-form-item label="支持的业务类型" prop="bizType">
                    <Select style="width: 100%" multiple  placeholder="请选择支持的业务类型"  :options="options.bizTypeOptions" v-model="form.bizType" />
                </el-form-item>

                <div style="text-align: right">
                    <el-button style="color: #1e2228; font-weight: 400" @click="cancel">取消</el-button>
                    <el-button type="primary" @click="save">确定</el-button>
                </div>
            </Form>
        </el-dialog>
    </div>
</template>
<script>
import Input from 'kit/components/marketing/admin/input.vue'
import Select from 'kit/components/marketing/admin/select.vue';
import handleError from 'kit/helpers/handleError'
import { authorizationToken } from 'kit/helpers/marketingBossToken.js';
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { bizTypeOptions } from '../transferSupplier/options.js';
import Form from 'kit/components/marketing/admin/form.vue';
import { oConfirm } from 'kit/components/marketing/admin/messageBox.js';
const marketingClient = makeMarketingClient()

function checkSupportedBusinessTypes(userSelectedTypes, supportedTypes) {
    // 1. 使用 every 检查所有用户选择类型是否都包含在支持的类型中
    const isAllSupported = userSelectedTypes.every(type => supportedTypes.includes(type));

    // 2. 如果所有类型都被支持，返回 null
    if (isAllSupported) {
        return null; // 表示所有用户选择类型都被支持
    } else {
        // 3. 否则，返回当前支持的业务类型
        return supportedTypes;
    }
}

export default {
    components: {
        Input,
        Select,
        Form
    },
    props: {
        mobile: String
    },
    data() {
        return {
            visible: false,
            title: "",
            options: {
                bizTypeOptions,
                merchantId: [],
                supplierId: [],
            },
            form: {
                "merchantId": "",
                "supplierId": "",
                "bizType": ""
            },
            rules: {
                merchantId: [
                    {
                        required: true,
                        message: '请选择商户名称',
                        trigger: 'change'
                    }
                ],
                supplierId: [
                    {
                        required: true,
                        message: '请选择资质地名称',
                        trigger: 'change'
                    }
                ],
                bizType: [
                    {
                        required: true,
                        message: '请选择支持的业务类型',
                        trigger: 'change'
                    }
                ]
            },
        }
    },
    computed: {
        isDisabled(){
            return Boolean(this.form.id)
        }
    },
    methods: {
        open(title, data = {}) {
            this.title = title
            this.visible = true
            this.form = {
                ...data
            }
            this.loadOptions()
            setTimeout(()=>{
                this.$refs.form.clearValidate()
            })
        },
        cancel() {
            this.$refs.form.resetForm()
            
            this.visible = false
        },
        async loadOptions() {
            const [_err, result] = await marketingClient.adminTransferSupplierList({
                body: {},
                ...authorizationToken()
            })

            const [__err, merchantList] = await marketingClient.adminTransferSupplierMerchantList({
                body: {},
                ...authorizationToken()
            })

            this.options.supplierId = result.map(item => {
                return {
                    bizType: item.bizType,
                    label: item.supplierName,
                    value: item.supplierId
                }
            })
            this.options.merchantId = merchantList.map(item => {
                return {
                    label: item.name,
                    value: item.id
                }
            })

        },
        async save() {

            const error = await this.$refs.form.validate()
            if (error) return 

            // const { bizType } = this.options.supplierId.find(item => item.value === this.form.supplierId) || {}
            // const hasBizType = checkSupportedBusinessTypes(this.form.bizType, bizType)
            // if (hasBizType) {
            //     return handleError(`您选择的资质仅支持${hasBizType}业务，请重新选择`)
            // }

            oConfirm('路由修改后业务会进行变更，请务必谨慎操作',
                '提示',
                {
                    confirm: async () => {
                        if (this.form.id) {
                            const [err, r] = await marketingClient.adminTransferSupplierUpdateRoute({
                                body: this.form,
                                ...authorizationToken()
                            })

                            if (err) {
                                handleError(err)
                                return
                            }
                        } else {
                            const [err, r] = await marketingClient.adminTransferSupplierAddRoute({
                                body: this.form,
                                ...authorizationToken()
                            })

                            if (err) {
                                handleError(err)
                                return
                            }
                        }

                        this.$message.success('操作成功')
                        this.$emit('success')
                        this.cancel()
                    }
                })


        }
    }
}
</script>
<style scoped>
::v-deep .el-dialog__header {
    padding: 16px 24px;
    border-radius: 8px 8px 0 0;
}

::v-deep .el-dialog__header .el-dialog__title{
    font-weight: bold;
}

::v-deep .el-input .el-input__inner {
    box-sizing: border-box;
    padding: 5px 12px;
    border-radius: 6px;
    opacity: 1;
    border: 1px solid #cad0dbff;
    /* background: #f7f9fcff; */
}

::v-deep .el-input.is-disabled .el-input__inner {
    color: #a6aebd;
}
</style>