<template>
    <o-pc-list ref="pc-list" title="资质地维护" :formJson="searchFormJson" :requestFn="getListApi" labelWidth="120px"
        :deleteNullApiParams="true" :tableHeaderActionButtons="tableHeaderActionButtons" :tableHeader="tableHeader" />
</template>
<script>
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import { handleError } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import { bizTypeOptions } from './options.js'
const marketingClient = makeMarketingClient()

function addOneDay(dateTimeStr) {
  const date = new Date(dateTimeStr);
  date.setDate(date.getDate() + 1);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day} 00:00:00`;
}
const loadList = async params => {
    if (params.filters.createTimeEnd){
        console.log( params.filters.createTimeEnd)
        params.filters.createTimeEnd = addOneDay(params.filters.createTimeEnd)
    }

    const [err, result] = await marketingClient.adminTransferSupplierPage({
        body: params,
        ...authorizationToken()
    })
    if (err) return handleError(err)
    return result.data
}

export default {
    data() {
        return {
            getListApi: loadList,
            searchFormJson: [
                {
                    type: 'input',
                    item: {
                        prop: 'supplierId',
                        label: '资质地id',
                        placeholder: '请输入资质地id'
                    }
                },
                {
                    type: 'input',
                    item: {
                        prop: 'supplierName',
                        label: '资质地名称',
                        placeholder: '请输入资质地名称'
                    }
                },
                {
                    type: 'input',
                    item: {
                        prop: 'contactName',
                        label: '资质地负责人姓名',
                        placeholder: '请输入资质地负责人姓名'
                    }
                },
                {
                    type: 'input',
                    item: {
                        prop: 'contactPhone',
                        label: '负责人联系方式',
                        placeholder: '请输入负责人联系方式'
                    }
                },
                {
                    type: 'datePicker',
                    item: {
                        type: 'daterange',
                        startPlaceholder: '开始日期',
                        endPlaceholder: '结束日期',
                        rangeSeparator: '~',
                        prop: 'create',
                        label: '资质地创建时间',
                        startField: 'createTimeBegin',
                        endField: 'createTimeEnd',
                        valueFormat: 'yyyy-MM-dd 00:00:00'
                    }
                }, {
                    type: 'select',
                    formItem: {
                        prop: 'disabled',
                        label: '状态',
                        placeholder: '请选择状态',
                        defaultValue:1,
                        clearable:false,
                        options: [
                            {
                                label: '启用',
                                value: 1
                            },
                            {
                                label: '禁用',
                                value: 0
                            },
                        ]
                    }
                },
            ],
            isFirstLoad: true,
            tableHeader: [
            {
                    prop: 'supplierId',
                    label: '资质地id',
                    width: 150,
                    fixed: true,
                    click:row=>{
                        this.$router.push(`/transferSupplier/create?id=${row.id}`)
                    }
                },
                {
                    prop: 'supplierName',
                    label: '资质地名称',
                    minWidth: 150,
                },

                {
                    prop: 'contactName',
                    label: '资质地负责人姓名',
                    width: 150
                },
                {
                    prop: 'contactPhone',
                    label: '负责人联系方式',
                    width: 150
                },
                {
                    prop: 'createTime',
                    label: '资质地创建时间',
                    type: 'DATE_TIME',
                    width: 150
                },
                {
                    prop: 'bizType',
                    label: '业务类型',
                    width: 200,
                    formatter: row => {
                        return row.bizType.map(item => {
                            const { label } = bizTypeOptions.find(option => option.value === item) || {}
                            return label
                        }).join('、')
                    }
                },
                {
                    prop: 'disabled',
                    label: '状态',
                    width: 80,
                    formatter:({disabled})=>{
                        return disabled?'启用':"禁用"
                    }
                },
            ],
            tableHeaderActionButtons: [
                {
                    align: 'right',
                    type: 'button',
                    label: '新建',
                    props: {
                        style: {
                            'justify-content': 'center'
                        }
                    },
                    click: async ({ props }) => {
                       this.$router.push("/transferSupplier/create")
                    }
                }
            ]
        }
    },
    computed: {
        oPcList() {
            return this.$refs['pc-list']
        }
    },
    activated() {
        if (!this.isFirstLoad) this.reload()
    },
    methods: {}
}
</script>