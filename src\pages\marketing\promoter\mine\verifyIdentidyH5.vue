<template>
  <div class="phoneChangeStep1">
    <Form ref="form" :model="form">
      <Field
        style="border-radius: 8px 8px 0 0"
        readonly
        :value="handleEncipherPhone(form.mobile)"
        label="手机号"
      />
      <Captcha
        style="width: 100%"
        v-model="captcha"
        label="图形验证码"
        :rules="rules.captcha"
      />
      <Otp
        class="otp"
        style="width: 100%"
        v-model="form.code"
        :captcha="captcha"
        :phone="form.mobile"
        label="验证码"
        :rules="rules.code"
        @otp="onOtp"
      />
    </Form>
    <p
      style="
        padding: 12px;
        border-radius: 8px;
        background: #fff7e8ff;
        color: #ff7d00ff;
        font-size: 12px;
      "
    >
      温馨提示：若该手机号已无法使用或接收不到短信验证码，请联系客服
    </p>
    <div style="margin-top: 16px; display: flex; gap: 16px">
      <Button style="border-radius: 6px" @click="$router.back()" round block
        >返回</Button
      >
      <Button
        style="border-radius: 6px"
        @click="next"
        round
        block
        type="primary"
        :disabled="disabled"
        >下一步</Button
      >
    </div>
  </div>
</template>

<script>
import { Form, Field, Button } from 'vant'
import Otp from 'kit/pages/marketing/promoter/loginOtp.vue'
import Captcha from 'kit/pages/marketing/promoter/loginCaptcha.vue'
import handleError from 'kit/helpers/handleErrorH5'
import * as reg from 'kit/helpers/regexp'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    Form,
    Field,
    Otp,
    Captcha,
    Button
  },
  props: {
    nextStepUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        mobile: '',
        code: ''
      },
      captcha: {
        answer: '',
        token: ''
      },
      otp: {
        answer: '',
        token: ''
      },
      rules: {
        captcha: [
          { required: true },
          {
            pattern: reg.VERIFICATION_IMG_CODE_REGEX,
            message: '验证码必须为4位数字'
          }
        ],
        code: [
          { required: true },
          {
            pattern: reg.VERIFICATION_SMS_CODE_REGEX,
            message: '验证码必须为6位数字'
          }
        ]
      }
    }
  },
  computed: {
    disabled() {
      if (!this.captcha.answer) {
        return true
      }
      if (!this.otp.token) {
        return true
      }
      return false
    }
  },
  async created() {
    const [err, r] = await marketingClient.mobilePromoterProfile({
      method: 'GET'
    })
    if (err) {
      handleError(err)
      return
    }
    this.form.mobile = r.data.mobile
  },
  methods: {
    onOtp(otp) {
      this.otp = {
        ...otp
      }
    },
    async next() {
      const [err, r] = await marketingClient.mobilePromoterVerifyCode({
        body: {
          mobile: this.form.mobile,
          smsCode: this.otp.answer,
          smsToken: this.otp.token
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.$emit('next')
    },
    handleEncipherPhone(phone) {
      let newPhone = phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
      return newPhone
    }
  }
}
</script>

<style scoped>
::v-deep .van-cell {
  padding: 14px 16px 15px 17px;
}
::v-deep .otp .van-cell {
  border-radius: 0 0 8px 8px;
}
::v-deep .van-field__body {
  height: 22px;
  line-height: 22px;
}
::v-deep .van-field__label {
  font-size: 14px;
  color: #4e5769;
  line-height: 22px;
}
</style>
