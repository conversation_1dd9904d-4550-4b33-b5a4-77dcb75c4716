<template>
  <div
    class="attend"
    style="background: #f3f4f5; overflow: hidden"
    v-if="!loading && employee"
  >
    <div style="height: 10px"></div>
    <div style="padding: 0 15px 8px 15px" v-if="!employee.resigned">
      <EmployeeInfo
        :employee="employee"
        :attendGroup="attendGroup"
        @goAttendRule="handleGoAttendRule"
        @goAttendApply="handleGoAttendApply"
      />
    </div>
    <div class="box" ref="box" style="overflow: hidden; overflow-y: auto">
      <div
        class="tip"
        style="
          margin: 10px 0;
          text-align: center;
          height: 24px;
          line-height: 24px;
          color: #71788f;
        "
      >
        {{ tip }}
      </div>
      <template v-if="noAttendPlaceList">
        <Lock style="margin: 50px 0" />
        <div style="text-align: center">
          当前考勤组为考勤机打卡方式，请使用考勤机打卡。
        </div>
      </template>
      <template v-else>
        <Lock v-if="!attendGroup" style="margin: 50px 0" />
        <div
          v-if="employee.resigned || !attendGroup"
          class="lockTip"
          style="
            margin: 20px 0;
            text-align: center;
            height: 24px;
            line-height: 24px;
            color: #71788f;
          "
        >
          <span v-if="employee.resigned">员工已离职</span>
          <span v-if="!attendGroup">
            当前未在考勤组，请联系管理员或HR
            <br />
            将您添加到考勤组后进行打卡
          </span>
        </div>
        <div style="margin: 0 16px">
          <template v-if="attendSwithGroupItems.length > 1">
            <ShiftSwitch
              :attendSwithGroupItems="attendSwithGroupItems"
              @switchShift="handleSwitchShift"
            ></ShiftSwitch>
          </template>
          <Steps
            style="border-radius: 12px; background: #f3f4f5; margin-right: 5px"
            v-if="attendGroup"
            class="steps"
            direction="vertical"
            active-color="#5e647d"
            inactive-color="#4f71ff"
            finish-icon="checked"
            active-icon="clock"
            :active="activeStep"
          >
            <Step
              v-show="checkInRecord.shown()"
              :key="`checkIn${index}`"
              v-for="(checkInRecord, index) in checkInRecords"
            >
              <CheckInRecord
                style="border-bottom: 1px solid #eceff5"
                :checkInRecordsLength="checkInRecords.length"
                :attendGroup="attendGroup"
                :checkInRecord="checkInRecord"
                @updateWorkingTime="handleUpdateWorkingTime"
                @fixAttend="handleFixAttend"
              />
            </Step>
            <Step v-if="checkIn">
              <CheckIn
                :checkIn="checkIn"
                :location="location"
                :attendGroup="attendGroup"
                @checkIn="handleCheckIn"
                @reLocation="debounceHandleReLocation"
              />
            </Step>
            <Step
              v-show="!attendGroup.isFreeMode() && notCheckIns.length"
              :key="`notCheckIn${index}`"
              v-for="(notCheckIn, index) in notCheckIns"
            >
              <NotCheckIn
                :notCheckIn="notCheckIn"
                style="margin-bottom: 10px"
              />
            </Step>
          </Steps>
          <div
            style="
              border-radius: 6px;
              padding: 20px 15px;
              line-height: 24px;
              margin: 10px 15px 10px 30px;
              text-align: center;
            "
            v-if="!checkInRecords.length && !checkIn && attendGroup"
          >
            当前时间不可打卡
          </div>
        </div>
      </template>
    </div>
    <Actions :active="0" @goStats="handleGoStats(true)" />
    <CheckInPopup
      ref="checkInPopup"
      :address="location.standardAddress"
      @upload="handleUpload"
      @checkIn="handlePopupCheckIn"
    />
  </div>
  <div
    v-else
    style="
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    "
  >
    <Loading color="#00b4b3" /> loading
  </div>
</template>

<script>
import { Button, Tag, Steps, Step, Dialog, Popup, Loading } from 'vant'
import EmployeeInfo from 'kit/components/mpH5/attend/employeeInfo.vue'
import Lock from '../../components/mpH5/attend/lock.vue'
import ShiftSwitch from '../../components/mpH5/attend/shiftSwitchGroupButton.vue'
import CheckIn from '../../components/mpH5/attend/checkIn.vue'
import NotCheckIn from '../../components/mpH5/attend/notCheckIn.vue'
import CheckInRecord from '../../components/mpH5/attend/checkInRecord.vue'
import CheckInPopup from '../../components/mpH5/attend/checkInPopup.vue'
import Actions from '../../components/mpH5/attend/actions.vue'

import AttendService from 'kit/models/attend/attendService'
import TipService from 'kit/models/attend/tipService'
import Employee from 'kit/models/attend/employee'
import AttendGroup from 'kit/models/attend/attendGroup'
import LocationService from 'kit/models/attend/locationService'
import Location from 'kit/models/attend/location'
import Point from 'kit/models/attend/point'
import AttendPlace from 'kit/models/attend/attendPlace'
import CheckInModel from 'kit/models/attend/checkIn'
import NotCheckInModel from 'kit/models/attend/notCheckIn'
import CheckInRecordModel from 'kit/models/attend/checkInRecord'
import fixAttend from './fixAttend'
import AttendNetResModel from 'kit/models/attend/attendNetResModel'
import defaultConfig from 'kit/models/attend/attendDefultConifg'

import { isArray } from 'kit/helpers'
import handleSuccess from 'kit/helpers/handleSuccessH5'
import makeClient from 'kit/services/platform/makeClient'
import handleError from 'kit/helpers/handleErrorH5'
import debounce from 'kit/helpers/debounce.js'
import handleErrorH5 from 'kit/helpers/handleErrorH5'

const platformClient = makeClient()
const attendNetResModel = new AttendNetResModel()
var checkInDoing = false
//检查打卡最小间隔时间
const validCheckInMinDuration = () => {
  const now = new Date().getTime()
  const last = sessionStorage.getItem('lastCheckInTime')
  //小于15秒
  if (now - last < 1000 * 15) {
    return '打卡最小间隔为15秒'
  }
}

// 张有给的是否展示 打卡按钮逻辑
const hideCheckInBtn = attendNetRes => {
  const { todaySignList, currentSignVo } = attendNetRes || {}

  const todaySignListSort = todaySignList.sort((a, b) => {
    return new Date(a.signTime) - new Date(b.signTime)
  })
  const lastTodaySignItem =
    todaySignListSort[todaySignListSort.length - 1] || {}
  const { signType, workingShiftDetailId } = lastTodaySignItem

  if (
    signType === 'FROM_WORK' &&
    currentSignVo &&
    workingShiftDetailId === currentSignVo.workingShiftDetailId
  ) {
    return true
  }

  return false
}

function attendSwithGroupData(attendNetResModel) {
  const titleList = ['第一天', '第二天']
  return attendNetResModel.getNetAttendList().map((attend, index) => {
    return {
      title: titleList[index],
      date: attendNetResModel.getWorkDate(attend),
      index: index
    }
  })
}
export default {
  components: {
    CheckInPopup,
    CheckInRecord,
    ShiftSwitch,
    CheckIn,
    NotCheckIn,
    Lock,
    EmployeeInfo,
    Button,
    Tag,
    Steps,
    Step,
    Popup,
    Loading,
    Actions
  },
  computed: {
    tip() {
      return TipService.randomTip()
    }
  },
  data() {
    return {
      employee: null,
      attendGroup: null,
      location: null,
      attendPlaces: [],
      checkIn: null,
      checkInRecords: [],
      notCheckIns: [],

      attendSwithGroupItems: [],

      loading: true,
      activeStep: -1,
      noAttendPlaceList: false
    }
  },
  async created() {
    await this.load()
    const goStats = this.$route.query.goStats === 'true'
    if (goStats) {
      this.handleGoStats(true)
      return
    }
    this.loading = false

    this.activeStep = this.checkInRecords.length

    const needSubHeight = 80 + 80 + 20

    this.$nextTick(() => {
      if (this.$refs.box) {
        this.$refs.box.style.height = `calc(100vh - ${needSubHeight}px)`
        //总是滚动到底部 露出操作区域
        this.$refs.box.scroll({ top: 9999 })
      }
    })

    const reLocation = debounce(() => {
      this.handleReLocation()
    }, 1000)

    this.debounceHandleReLocation = () => {
      this.location.loading = true
      reLocation()
    }
  },
  methods: {
    async load() {
      const [err, r] = await platformClient.attendMobileSignQueryEmployee()
      if (err) {
        handleError(err)
        return
      }
      this.employee = new Employee(r.data)
      console.log(this.employee, 'employee')

      const { compId, departmentId, taxSubId } = this.employee
      const [err2, r2] =
        await platformClient.attendMobileSignQueryAttendanceGroup({
          body: {
            coId: compId,
            deptId: departmentId,
            taxsubId: taxSubId
          }
        })

      if (err2 && err2.errorCode === 1006 && err2.message.includes('离职')) {
        this.employee.setResigned(true)
      }

      if (!err2) {
        attendNetResModel.setNewAttendNetRes(r2)
        // attendNetResModel.setMock();  //debug

        this.updateDefaultAttendIfOnCreate()

        const attendPlaceList = r2?.data?.attendGroup?.attendPlaceList

        this.noAttendPlaceList = !attendPlaceList.length
        if (this.noAttendPlaceList) {
          return
        }
      }

      await this.handleReLocation()
    },
    updateDefaultAttendIfOnCreate() {
      this.initSwitchGroup()

      const defaultNetAttend =
        defaultConfig.getDefaultNetAttend(attendNetResModel)
      this.updateAttend(defaultNetAttend)
    },
    initSwitchGroup() {
      this.attendSwithGroupItems = attendSwithGroupData(attendNetResModel) || []
    },
    async updateAttend(attendNetRes) {
      if (!attendNetRes) {
        return
      }
      this.attendGroup = new AttendGroup(attendNetRes.attendGroup)
      this.attendPlaces = []
      for (var c of this.attendGroup.attendPlaceList) {
        this.attendPlaces.push(new AttendPlace(c))
      }
      this.checkIn = null

      if (attendNetRes.currentSignVo) {
        this.checkIn = new CheckInModel(attendNetRes.currentSignVo)
      }

      if (hideCheckInBtn(attendNetRes)) {
        this.checkIn = null
      }

      var checkInRecords = []
      for (var i = 0; i < attendNetRes.todaySignList.length; i++) {
        const l = attendNetRes.todaySignList.length

        checkInRecords.push(
          new CheckInRecordModel(attendNetRes.todaySignList[i], l - 1 === i)
        )
      }

      checkInRecords.sort((a, b) => (a.createdTime < b.createdTime ? -1 : 1))
      this.checkInRecords = checkInRecords

      this.notCheckIns = []
      attendNetRes.notSignWorkShiftList =
        attendNetRes.notSignWorkShiftList || []
      for (var i = 0; i < attendNetRes.notSignWorkShiftList.length; i++) {
        this.notCheckIns.push(
          new NotCheckInModel(attendNetRes.notSignWorkShiftList[i])
        )
      }

      await this.handleReLocation()
    },
    handleGoStats(replace = false) {
      if (!this.attendGroup) {
        handleError('未在考勤组，不可查看统计')
        return null
      }

      sessionStorage.setItem('employee', JSON.stringify(this.employee))
      const uri = `/attendStats?coId=${this.attendGroup.coId}&attendId=${this.attendGroup.attendId}`
      if (replace) {
        // 这里是为了解决来回切换，手机端卡住
        window.location.replace(window.env.staticPath + uri)
        return
      }

      window.location.href = window.env.staticPath + uri
    },
    handleSwitchShift(activeShiftItem) {
      const targetNetAttend =
        attendNetResModel.getNetAttendList()[activeShiftItem.index]
      this.updateAttend(targetNetAttend)
    },
    handleFixAttend(checkInRecord) {
      const err = fixAttend(this.attendGroup, checkInRecord)
      if (err) {
        handleError(err)
      }
    },
    handleCheckIn(checkIn) {
      if (this.location.failReason) {
        handleError(this.location.failReason)
        return
      }

      //是工作日，但是非正常工作状态时段
      if (!checkIn.timeAllowCheckIn()) {
        handleError('当前时间不可打卡')
        return
      }

      //是工作日，不支持外勤打卡，又不再
      if (!checkIn.locationAllowCheckIn(this.attendGroup, this.location)) {
        handleError('当前位置不可打卡')
        return
      }

      const err = validCheckInMinDuration()
      if (err) {
        handleError(err)
        return
      }

      const command = AttendService.buildCheckInCommand(
        this.employee,
        this.attendGroup,
        this.location,
        checkIn
      )

      //外勤
      if (
        this.location.isOutSide &&
        this.attendGroup.allowedOutside &&
        checkIn.isWorkDay
      ) {
        this.goOutSide(command)
        return
      }

      if (command.status === 'LEAVE_EARLIER') {
        this.$refs.checkInPopup.open()
        return
      }
      console.log('check in command', command)

      //   if (command.status == 'BE_LATE') {
      //     Dialog.confirm({
      //       title: '打卡提示',
      //       message: `确定迟到打卡吗？`
      //     }).then(() => {
      //       this.doCheckIn(command)
      //     })

      //     return
      //   }

      this.doCheckIn(command)
    },
    handleUpdateWorkingTime(checkInRecord) {
      if (this.location.failReason) {
        handleError(this.location.failReason)
        return
      }
      //不在可打卡范围 且 不允许外勤打卡 且 在工作日
      //非工作日有更宽松的打卡逻辑
      if (
        this.location.isOutSide &&
        !this.attendGroup.allowedOutside &&
        checkInRecord.isWorkDay
      ) {
        handleError('当前地址不可更新打卡')
        return
      }

      const err = validCheckInMinDuration()
      if (err) {
        handleError(err)
        return
      }

      const command = AttendService.buildUpdateCheckTimeCommand(
        this.attendGroup,
        this.location,
        checkInRecord
      )

      //外勤
      if (command.status === 'OUTSIDE_ATTEND') {
        this.goOutSide(command)
        return
      }

      console.log('command', command)
      if (command.status === 'BE_LATE' || command.status === 'LEAVE_EARLIER') {
        Dialog.confirm({
          title: '打卡提示',
          message: `确定${
            command.status == 'BE_LATE' ? '迟到' : '早退'
          }打卡吗？`
        }).then(() => {
          this.doCheckIn(command)
        })
        return
      }

      this.doCheckIn(command)
    },
    async handleReLocation() {
      this.location = new Location({ loading: true, lat: -1, lng: -1 })
      var [err3, point] = await LocationService.point()
      if (err3) {
        if (err3.includes('Only secure origins are allowed')) {
          err3 = 'https协议才能进行定位'
        }
        handleError(err3)
        this.location.loading = false
        this.location.failReason = err3
        return
      }
      console.log('point', point)

      const [err, address] = await LocationService.pointToAddress(point)
      if (err) {
        handleError(err)
        this.location.loading = false
        this.location.failReason = err
        return
      }

      // point.lng = 116.510768
      // point.lat = 39.904349

      this.location.point = new Point(point.lat, point.lng)
      this.location.address = address

      this.location.standardAddress = LocationService.locationStandardAddress(
        this.attendPlaces,
        this.location
      )

      this.location.isOutSide = LocationService.pointIsOutSideOfAttendPlaces(
        this.attendPlaces,
        this.location.point
      )

      this.location.loading = false
    },
    async doCheckIn(command) {
      if (checkInDoing) {
        return
      }
      checkInDoing = true

      const [err, r] = await platformClient.attendMobileSign({
        body: command
      })
      checkInDoing = false
      if (err) {
        handleError(err)
        return
      }

      sessionStorage.setItem('lastCheckInTime', new Date().getTime())

      if (r.success) {
        handleSuccess('打卡成功')
        window.location.reload()
      }
    },
    handlePopupCheckIn(comment) {
      this.checkIn.comment = comment
      var command = AttendService.buildCheckInCommand(
        this.employee,
        this.attendGroup,
        this.location,
        this.checkIn
      )

      console.log('handlePopupCheckIn', command)
      this.doCheckIn(command)
    },
    async handleUpload(files) {
      var images = []
      if (isArray(files)) {
        for (var c of files) {
          const image = await this.uploadFile(c.file)
          images.push(image)
        }
      } else {
        const image = await this.uploadFile(files.file)
        images.push(image)
      }

      this.checkIn.images = images
    },
    async uploadFile(file) {
      var formData = new FormData()
      formData.set('file', file)
      const [err, r] = await platformClient.attendMobileSignFilsUpload({
        body: formData,
        requestInterceptor(resource, options) {
          delete options.headers['Content-Type']
          return [null, resource, options]
        }
      })
      if (err) {
        handleError(err)
        return
      }

      return r.data
    },
    handleGoAttendRule() {
      sessionStorage.setItem('employee', JSON.stringify(this.employee))
      sessionStorage.setItem('attendGroup', JSON.stringify(this.attendGroup))
      this.$router.push('/attendRule')
    },
    goOutSide(command) {
      sessionStorage.setItem('checkInCommand', JSON.stringify(command))
      sessionStorage.setItem('attendGroup', JSON.stringify(this.attendGroup))
      this.$router.push('/attendOutSide')
    },
    handleGoAttendApply() {
      sessionStorage.setItem('attendGroup', JSON.stringify(this.attendGroup))
      this.$router.push('/attendApprovals')
    }
  }
}
</script>

<style scoped>
.attend ::v-deep .van-icon-clock {
  color: #4f71ff !important;
}
.attend ::v-deep .van-step__circle-container {
  top: 40px !important;
}
.attend ::v-deep .van-step__line {
  top: 40px !important;
}
.attend ::v-deep .van-step--vertical {
  padding: 0 10px 10px 0 !important;
}
</style>
