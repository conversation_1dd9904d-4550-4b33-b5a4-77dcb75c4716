<template>
  <div class="attendApprovals" style="background: #f3f4f5;">
    <div
      class="title"
      style="
        font-size: 14px;
        line-height: 40px;
        color: #5e647d;
        text-align: center;
      "
    >
      以下审批单，已和考勤智能关联，将会自动计入到考勤表
    </div>
    <div
      class="approvals"
      style="
        background: #fff;
        display: flex;
        padding: 15px;
        padding-right:0;
        flex-wrap: wrap;
      "
    >
      <div
        class="approval"
        :key="index"
        v-for="(c, index) in attendGroup.approvalResultList"
        @click="go(c)"
        style="
          cursor: pointer;
          height: 108px;
          width:30%;
          background: #f8fafe;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
        "
      >
        <div style="text-align: center">
          <i
            style="font-size: 32px"
            :class="c.bizSuitType | iconClass"
            :style="{
              color: color(c.bizSuitType)
            }"
          />
          <br />
          {{ c.processName | formatProcessName }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import goApproval from './goApproval'
export default {
  filters: {
    formatProcessName(v) {
      switch (v) {
        case 'buka':
          return '补卡'
        case 'qingjia':
          return '请假'
        case 'chuchai':
          return '出差'
        case 'waichu':
          return '外出'
        case 'jiaban':
          return '加班'

        default:
          return v
      }
    },
    iconClass(v) {
      switch (v) {
        case 'FIX_ATTENDANCE':
          return 'iconfont icon-color-attendance-make-up-card'
        case 'ASK_FOR_LEAVE':
          return 'iconfont icon-color-ask-for-leave'
        case 'BUSINESS_TRIP':
          return 'iconfont icon-color-be-away-on-official-business'
        case 'LEGWORK':
          return 'iconfont icon-color-application-for-going-out'
        case 'OVERTIME':
          return 'iconfont icon-color-work-overtime'
      }
    }
  },
  created() {
    const attendGroup = JSON.parse(sessionStorage.getItem('attendGroup'))
    this.attendGroup = attendGroup
  },
  data() {
    return {
      attendGroup: null
    }
  },
  methods: {
    color(v) {
      switch (v) {
        case 'FIX_ATTENDANCE':
          return '#2caaf2'
        case 'ASK_FOR_LEAVE':
          return '#2caaf2'
        case 'BUSINESS_TRIP':
          return '#e29f67'
        case 'LEGWORK':
          return '#6b91e8'
        case 'OVERTIME':
          return '#78d6ca'
      }
    },
    go(approval) {
      if (approval.processId) {
        goApproval({
          processId: approval.processId
        })
        return
      }

      const tmp = approval.defaultProcess.split('_')
      const receiptId = tmp[0]
      const designId = tmp[1]
      const processDesignId = tmp[2]
      goApproval({
        receiptId,
        designId,
        processDesignId
      })
    }
  }
}
</script>

<style scoped>
.approvals>div{
    margin-right: 10px;
    margin-bottom: 10px;
}
</style>