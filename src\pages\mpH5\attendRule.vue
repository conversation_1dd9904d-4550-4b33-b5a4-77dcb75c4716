<template>
  <div class="attendRule" style="background: #f3f4f5; overflow: hidden">
    <div style="height: 10px"></div>
    <div style="padding: 0 15px 8px 15px; margin-bottom: 40px">
      <EmployeeInfo
        :employee="employee"
        :showAttendGroup="false"
        style="margin-bottom: 10px"
      />
      <div
        style="
          background: #fff;
          padding: 10px;
          border-radius: 6px;
          box-shadow: 0 2px rgba(0, 0, 0, 0.05);
        "
      >
        <div class="rule-item">
          <h2>考勤时间</h2>
          <div
            class="item"
            v-for="(item, index) in attendResultList"
            :key="index"
          >
            <div v-if="item.workingShiftResult">
              <h3 v-if="item.workingShiftResult.shiftType != 'FREE'">
                {{ item.workingShiftResult.groupName }}
              </h3>
              <p class="item-name">
                {{ item.isRestDay ? '休息日' : '工作日' }}
              </p>
              <span class="item-name">
                {{ item.week }}
              </span>
              <span
                class="item-name"
                v-for="(it, i) in item.workingShiftResult
                  .workingShiftDetailResultList"
                :key="i"
                v-show="it.workingBegin && it.workingEnd"
              >
                {{ it.begin }}~{{ it.end }}；
              </span>
              <p class="item-name">{{ rest }} 休息</p>
            </div>
          </div>
        </div>
        <hr style="margin: 20px 0" color="#f5f5f5" />
        <div class="rule-item">
          <h2>考勤规则</h2>
          <h3>
            {{
              attendGroup.supplementRule.allowSupplement
                ? '允许补卡'
                : '不允许补卡'
            }}
          </h3>
          <p
            class="item-name"
            v-show="
              attendGroup.supplementRule.allowSupplement &&
              attendGroup.supplementRule.allowTimesLimit
            "
          >
            每月可提交{{ attendGroup.supplementRule.timesLimit }}次补卡
          </p>
          <p
            class="item-name"
            v-show="
              attendGroup.supplementRule.allowSupplement &&
              attendGroup.supplementRule.allowDateLimit
            "
          >
            可申请过去{{ attendGroup.supplementRule.dateLimit }}天内的补卡
          </p>
          <h3>{{ attendGroup.allowedOutside ? '允许外勤' : '不允许外勤' }}</h3>
          <p
            v-show="attendGroup.allowedOutside && attendGroup.outsideImages"
            class="item-name"
          >
            外勤必须添加图片
          </p>
          <p
            v-show="attendGroup.allowedOutside && attendGroup.outsideRemark"
            class="item-name"
          >
            外勤必须添加备注
          </p>
        </div>
        <hr style="margin: 20px 0" color="#f5f5f5" />
        <div class="rule-item">
          <h2>打卡方式</h2>
          <h3 v-if="attendGroup.enableFaceOcr">考勤机打卡</h3>
          <div v-if="attendGroup.attendPlaceList.length">
            <h4>位置打卡</h4>

            <h4>办公地址</h4>
            <div
              class="item-name"
              v-for="(item, index) in attendGroup.attendPlaceList"
              :key="index"
            >
              {{ item.placeName }}
            </div>
          </div>
        </div>
        <hr style="margin: 20px 0" color="#f5f5f5" />
        <div class="rule-item" style="padding-bottom: 40px">
          <h2>考勤组管理员</h2>
          <p class="ts">考勤组规则有问题，请联系管理员</p>
          <div style="display:flex; flex-wrap:wrap">
            <span
                class="admin"
                v-for="(item, index) in attendGroup.administratorList"
                :key="index"
                style="
                background: #d1e1ff;
                color: #3571fa;
                padding: 8px 12px;
                border-radius: 6px;
                margin-right:10px;
                margin-bottom:10px;
                "
            >
                <i class="iconfont icon-base-user-icon" />
                {{ item.name }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import EmployeeInfo from '../../components/mpH5/attend/employeeInfo.vue'
import Employee from 'kit/models/attend/employee'
import AttendGroup from 'kit/models/attend/attendGroup'
const weekList = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
function timeSub(time) {
  const h = String(time).substr(0, 2)
  const m = String(time).substr(2, 4)
  return `${h}:${m}`
}
export default {
  components: {
    EmployeeInfo
  },
  created() {
    const employee = JSON.parse(sessionStorage.getItem('employee'))
    this.employee = new Employee(employee)

    const attendGroup = JSON.parse(sessionStorage.getItem('attendGroup'))
    this.attendGroup = new AttendGroup(attendGroup)

    this.rest = this.handle(this.attendGroup.attendWorkingShiftResultList, true)
    this.week = this.handle(
      this.attendGroup.attendWorkingShiftResultList,
      false
    )

    let arr = this.attendGroup.attendWorkingShiftResultList.slice(1)

    let obj = {}
    let peon = arr.reduce((cur, next) => {
      obj[next.workingShiftId]
        ? ''
        : (obj[next.workingShiftId] = true && cur.push(next))
      return cur
    }, [])

    peon = peon.map(item => {
      if (item.workingShiftResult) {
        this.week.forEach(obj => {
          if (item.workingShiftResult.groupName == obj.type) {
            item.week = obj.res
          }
        })
        item.workingShiftResult.workingShiftDetailResultList.map(it => {
          it.begin = timeSub(it.workingBegin)
          it.end = timeSub(it.workingEnd)
          return it
        })
      }
      return item
    })

    console.log('peon', peon)
    console.log(this.rest)
    console.log(this.week)

    this.attendResultList = peon
  },
  data() {
    return {
      employee: null,
      attendGroup: null,
      rest: [],
      week: [],
      attendResultList: []
    }
  },
  methods: {
    handle(arr, type) {
      if (arr.length == 0) return []
      let rest = arr.slice(1).filter(item => item.isRestDay == type)
      if (type == false) {
        rest = this.groupArr(rest, 'workingShiftId')
        rest.map(item => {
          const resArr = []
          item.list.map(it => {
            resArr.push(weekList[it.shiftOrder])
          })
          item.res = resArr.join('、')
          return item
        })
        return rest
      } else {
        const resArr = []
        rest.map(item => {
          if (weekList[item.shiftOrder]) {
            resArr.push(weekList[item.shiftOrder])
          }
        })
        return resArr.join('、')
      }
    },
    groupArr(list, field) {
      var fieldList = [],
        att = []
      list.map(e => {
        fieldList.push(e[field])
      })
      //数组去重
      fieldList = fieldList.filter((e, i, self) => {
        return self.indexOf(e) == i
      })
      // console.log("fieldList",fieldList)
      for (var j = 0; j < fieldList.length; j++) {
        //过滤出匹配到的数据
        var arr = list.filter(e => {
          // console.log("e",e,fieldList[j])
          return e[field] == fieldList[j]
        })
        // console.log('arr>>',arr)
        att.push({
          type: arr[0].workingShiftResult.groupName,
          list: arr
        })
      }

      console.log(att)
      return att
    }
  }
}
</script>

<style>
</style>