<template>
  <div
    class="attendStats"
    style="background: #f3f4f5; height: 100vh"
    v-if="!loading"
  >
    <div class="box" ref="box" style="overflow: hidden; overflow-y: auto">
      <MonthStatsLite
        :shortMonth="shortMonth"
        @more="handleMore"
        :summaries="summaries"
      />
      <MonthStatsCalendar
        :defaultDate="this.currentDate"
        :dayColor="dayColor"
        @selectDate="handleSelectDate"
        @switchMonth="handleSwitchMonth"
        style="margin-top: 10px"
      />
      <div
        v-if="isRest"
        style="
          margin: 40px auto;
          width: 220px;
          text-align: center;
          color: #848587;
        "
      >
        <img :src="restIcon" />
        <br />
        您今天休息
      </div>
      <ShiftInfo v-if="!isRest" :shiftInfo="shiftInfo" style="margin: 20px" />
     
      <div
        v-if="approveDailyData && approveDailyData.length"
        style="padding: 0 15px 0 32px; margin-top: 10px"
      >
        <DayApprovalRecord
          :key="`dayApprovalRecord${index}`"
          :approvalRecord="approvalRecord"
          v-for="(approvalRecord, index) in approveDailyData"
          @goApproval="handleGoApproval"
          style="margin-bottom: 10px"
        />
      </div>
      <DayStats
        v-if="!isRest"
        :checkInRecords="checkInRecords"
        :attendGroup="attendGroup"
        @fixAttend="handleFixAttend"
      />
    </div>
    <Actions :active="1" @goCheckIn="handleGoAttend" />
  </div>
  <div
    v-else
    style="
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    "
  >
    <div style="display: flex; align-items: center">
      <Loading color="#00b4b3" /> loading
    </div>
  </div>
</template>

<script>
import { Loading } from 'vant'
import MonthStatsLite from '../../components/mpH5/attend/monthStatsSummaries.vue'
import MonthStatsCalendar from '../../components/mpH5/attend/monthStatsCalendar.vue'
import ShiftInfo from '../../components/mpH5/attend/shiftInfo.vue'
import Actions from '../../components/mpH5/attend/actions.vue'
import DayApprovalRecord from '../../components/mpH5/attend/dayApprovalRecord.vue'
import DayStats from '../../components/mpH5/attend/dayStats.vue'
import formatDateTime from 'kit/formatters/dateTime'
import attendDailyListToDayColor from 'kit/formatters/mpH5/attendDailyListToDayColor'
import CheckInRecord from 'kit/models/attend/checkInRecord'
import restIcon from 'kit/assets/images/icon/Icon-xiuxi.png'
import fixAttend from './fixAttend'
import makeClient from 'kit/services/platform/makeClient'
import handleError from 'kit/helpers/handleErrorH5'
import AttendGroup from 'kit/models/attend/attendGroup'
import { AttendScene } from 'kit/models/attend/attendDefine'

const platformClient = makeClient()
export default {
  components: {
    Loading,
    MonthStatsLite,
    MonthStatsCalendar,
    DayStats,
    DayApprovalRecord,
    ShiftInfo,
    Actions
  },
  computed: {
    isRest() {
      if (!this.employeeDailyData) {
        return false
      }
      if (Object.keys(this.employeeDailyData).length === 0) {
        return true
      }

      return false
    },
    shortMonth() {
      return formatDateTime(
        {
          format: 'MM'
        },
        this.currentDate
      )
    },
    summaries() {
      if (!this.employeeMonthlyData) {
        return []
      }
      const r = [
        {
          label: '迟到',
          count: this.employeeMonthlyData.totalStatisticVo.beLate.value,
          color: 'red'
        },
        {
          label: '早退',
          count: this.employeeMonthlyData.totalStatisticVo.leaveEarlier.value,
          color: 'red'
        },
        {
          label: '缺卡',
          count: this.employeeMonthlyData.totalStatisticVo.absentWork.value,
          color: 'red'
        },
        {
          label: '外勤',
          count: this.employeeMonthlyData.totalStatisticVo.outsideAttend.value,
          color: 'green'
        }
      ]

      return r
    },
    dayColor() {
      if (!this.employeeMonthlyData) {
        return null
      }

      const r = attendDailyListToDayColor(
        this.employeeMonthlyData.attendDailyList
      )

      return r
    },
    checkInRecords() {
      //周六日无数据
      if (
        !this.employeeDailyData ||
        !Object.keys(this.employeeDailyData).length ||
        !this.employeeDailyData.signRecordList ||
        !this.employeeDailyData.signRecordList.length
      ) {
        return []
      }
      var r = []

      for (var i = 0; i < this.employeeDailyData.signRecordList.length; i++) {
        const l = this.employeeDailyData.signRecordList.length
        const c = this.employeeDailyData.signRecordList[i]
        const duplicated = r.findIndex(item=>item.signType == c.signType && item.workingShiftOrder === c.workingShiftOrder)
        if(duplicated === -1){
          r.push(new CheckInRecord(c, l - 1 === i, AttendScene.ATTEND_STATS))
        }else{
          if(r[duplicated].id < c.id){
            r[duplicated] = new CheckInRecord(c, l - 1 === i, AttendScene.ATTEND_STATS)
          }
        }
      }

      return r
    },
    shiftInfo() {
      if (
        !this.employeeDailyData ||
        !Object.keys(this.employeeDailyData).length
      ) {
        return null
      }

      return this.employeeDailyData.workingShiftResult
    },
    attendGroup() {
      if (!this.attend) {
        return null
      }
      return new AttendGroup(this.attend.attendGroup)
    }
  },
  data() {
    return {
      restIcon,
      loading: true,
      employeeMonthlyData: null,
      employeeDailyData: null,
      approveDailyData: null,
      attend: null,
      currentDate: new Date()
    }
  },
  async created() {
    const passDate = this.$route.query.date
    if (passDate) {
      this.currentDate = new Date(passDate)
    }
    const statisticDate = formatDateTime(
      {
        format: 'yyyy-MM-01'
      },
      this.currentDate
    )

    await this.initEmployeeMonthlyData(statisticDate)
    await this.initEmployeeDailyData()
    await this.initApproveDailyData()
    await this.initAttend()

    this.loading = false
    this.$nextTick(() => {
      this.$refs.box.style.height = `calc(100vh - 79px)`
    })
  },
  methods: {
    handleGoAttend() {
      window.location.replace(window.env.staticPath + '/attend')
    },
    async initEmployeeMonthlyData(statisticDate) {
      const attendId = this.$route.query.attendId * 1
      const coId = this.$route.query.coId * 1
      if (!attendId || !coId) {
        handleError('未传入attendId与coId')
        return null
      }
      const [err, r] =
        await platformClient.attendMobileSignStatisticEmployeeMonthlyData({
          body: {
            attendId,
            coId,
            statisticDate
          }
        })
      if (err) {
        handleError(err)
        return
      }

      this.employeeMonthlyData = r.data
    },
    async initEmployeeDailyData() {
      const attendId = this.$route.query.attendId * 1
      const coId = this.$route.query.coId * 1
      if (!attendId || !coId) {
        handleError('未传入attendId与coId')
        return null
      }
      const [err, r] =
        await platformClient.attendMobileSignStatisticEmployeeDailyData({
          body: {
            attendId,
            coId,
            statisticDate: formatDateTime(
              { format: 'yyyy-MM-dd' },
              this.currentDate
            )
          }
        })
      if (err) {
        handleError(err)
        return
      }

      this.employeeDailyData = r.data
    },
    async initApproveDailyData() {
      const coId = this.$route.query.coId * 1
      const date = formatDateTime({ format: 'yyyy-MM-dd' }, this.currentDate)
      const [err, r] =
        await platformClient.attendMobileSignStatisticApproveDailyData({
          body: {
            coId,
            date
          }
        })

      if (err) {
        handleError(err)
        return
      }

      this.approveDailyData = r.data
    },
    async initAttend() {
      if (
        !this.employeeDailyData ||
        !this.employeeDailyData.signRecordList ||
        !this.employeeDailyData.signRecordList.length
      ) {
        return
      }

      const signRecord = this.employeeDailyData.signRecordList[0]
      const [err, r] =
        await platformClient.attendMobileSignQueryAttendanceGroup({
          body: {
            coId: signRecord.coId,
            deptId: signRecord.deptId,
            taxsubId: signRecord.taxsubId
          }
        })

      if (err) {
        if (err) {
          handleError(err)
          return
        }
      }
      this.attend = r.data
    },
    async handleSelectDate(date) {
      this.currentDate = date
      await this.initEmployeeDailyData()
      await this.initApproveDailyData()
      await this.initAttend()
      console.log('handleSelectDate1', date)
    },
    handleSwitchMonth(date) {
      this.initEmployeeMonthlyData(date)
      console.log('handleSwitchMonth', date)
    },
    handleFixAttend(checkInRecord) {
      const err = fixAttend(this.attendGroup, checkInRecord)
      if (err) {
        handleError(err)
      }
    },
    handleGoApproval(approvalRecord) {
      const processInstanceId = approvalRecord.processInstanceId
      const err = fixAttend(this.attendGroup, {
        fixAttendStatus: 'SUCCESS',
        approveId: processInstanceId
      })
      if (err) {
        handleError(err)
      }
    },
    handleMore() {
      const attendId = this.$route.query.attendId * 1
      const coId = this.$route.query.coId * 1

      sessionStorage.setItem(
        'totalStatisticVo',
        JSON.stringify(this.employeeMonthlyData.totalStatisticVo)
      )
      this.$router.push(`/attendStatsMore?coId=${coId}&attendId=${attendId}`)
    }
  }
}
</script>

<style scoped>
.attendStats ::v-deep .van-icon-clock {
  color: #4f71ff !important;
}
.attendStats ::v-deep .van-step__circle-container {
  top: 40px !important;
}
.attendStats ::v-deep .van-step__line {
  top: 40px !important;
}
.attendStats ::v-deep .van-step--vertical {
  padding: 0 10px 10px 0 !important;
}
</style>