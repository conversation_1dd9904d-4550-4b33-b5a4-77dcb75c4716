<template>
  <div
    class="attendStatsMore"
    style="background: #f3f4f5; padding: 10px 15px; color: #575c6c"
  >
    <EmployeeInfo :employee="employee" :showAttendGroup="false" />
    <div
      class="box"
      style="background: #fff; border-radius: 6px; margin-top: 10px"
    >
      <div style="" :key="index" v-for="(stat, index) in stats">
        <div style="padding: 0 15px">
          <div
            style="
              height: 60px;
              border-bottom: 1px solid #eceff5;
              display: flex;
              align-items: center;
            "
          >
            <span style="flex: 1">{{ stat.label }}</span>
            <span style="flex: 0 0 40px">{{ stat.count }} {{ stat.unit }}</span>
            <i
              style="flex: 0 0 12px"
              v-if="stat.checkInRecords.length"
              class="iconfont icon-direction-arrow-border-down"
              @click="e => handleShow(e, index)"
            />
          </div>
        </div>
        <div
          :id="`checkInRecords${index}`"
          v-if="stat.checkInRecords.length"
          style="display: none; background: #f3f4f5"
        >
          <div
            @click="goAttend(checkInRecord)"
            style="line-height: 60px; padding: 0 15px"
            :key="`ck${index}`"
            v-for="(checkInRecord, index) in stat.checkInRecords"
          >
            {{ checkInRecord | formatCheckInRecord }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import EmployeeInfo from '../../components/mpH5/attend/employeeInfo.vue'
import Employee from 'kit/models/attend/employee'
import handleError from 'kit/helpers/handleErrorH5'
import signResultToString from 'kit/formatters/mpH5/signResultToString'
import { isObject } from 'kit/helpers'
import formatDateTime from 'kit/formatters/dateTime'
import { to8601 } from 'kit/helpers/dateUtils'

function formatNormalCheckInRecord(v) {
  const date = new Date(to8601(v.date))
  const tmp = v.date.split(' ')
  const weekday = new Intl.DateTimeFormat('zh-CN', {
    weekday: 'long',
    timeZone: 'Asia/Shanghai'
  }).format(date)
  return `${tmp[0]}（${weekday}）${tmp[1]}`
}
function formatLeaveCheckInRecord(v) {
  return `${formatDateTime(
    'yyyy-MM-dd HH:mm',
    to8601(v.startDate)
  )} ~ ${formatDateTime('yyyy-MM-dd HH:mm', to8601(v.endDate))}`
}
function formatStrategyFactory(status) {
  if (status === 'LEAVE') {
    return formatLeaveCheckInRecord
  } else {
    return formatNormalCheckInRecord
  }
}

import 'dayjs/locale/zh-cn' // 导入中文语言环境
dayjs.locale('zh-cn')

export default {
  components: {
    EmployeeInfo
  },
  filters: {
    formatCheckInRecord(v) {
      const formater = formatStrategyFactory(v.status)
      return formater(v)
    }
  },
  created() {
    const employee = JSON.parse(sessionStorage.getItem('employee'))
    if (!employee) {
      handleError('没有员工数据')
      return
    }

    this.employee = new Employee(employee)

    const totalStatisticVo = JSON.parse(
      sessionStorage.getItem('totalStatisticVo')
    )
    if (!totalStatisticVo) {
      handleError('没有统计数据')
      return
    }

    this.totalStatisticVo = totalStatisticVo
  },
  computed: {
    //   {
    //     label:string,
    //     count:number,
    //     unit: 次 | 天,
    //     checkInRecords:[{
    //         date:string,
    //         weekNum:number
    //     }]
    //   }
    stats() {
      var r = [
        {
          label: '应出勤天数',
          count: this.totalStatisticVo.totalAttend,
          unit: '天',
          checkInRecords: []
        },
        {
          label: '工作日实际出勤天数',
          count: this.totalStatisticVo.actualAttend,
          unit: '天',
          checkInRecords: []
        },
        {
          label: '休息日出勤天数',
          count: this.totalStatisticVo.restDayAttend,
          unit: '天',
          checkInRecords: []
        }
      ]
      for (var key in this.totalStatisticVo) {
        const c = this.totalStatisticVo[key]
        if (!isObject(c)) {
          continue
        }
        c.dateList?.forEach(checkInRecord => (checkInRecord.status = c.status))

        const label = signResultToString(c.status)
        r.push({
          label,
          count: c.value,
          unit: '次',
          checkInRecords: c.dateList
        })
      }
      console.log('stats', r)
      return r
    }
  },
  data() {
    return {
      employee: null,
      totalStatisticVo: null
    }
  },
  methods: {
    handleShow(e, index) {
      const checkInRecordsEl = document.getElementById(`checkInRecords${index}`)
      const isShow = checkInRecordsEl.style.display !== 'none'
      if (isShow) {
        checkInRecordsEl.style.display = 'none'
        e.target.className = 'iconfont icon-direction-arrow-border-down'
      } else {
        checkInRecordsEl.style.display = ''
        e.target.className = 'iconfont icon-direction-arrow-border-up'
      }
    },
    goAttend(checkInRecord) {
      const attendId = this.$route.query.attendId * 1
      const coId = this.$route.query.coId * 1
      this.$router.push(
        `/attendStats?date=${checkInRecord.date}&attendId=${attendId}&coId=${coId}`
      )
    }
  }
}
</script>

<style>
</style>