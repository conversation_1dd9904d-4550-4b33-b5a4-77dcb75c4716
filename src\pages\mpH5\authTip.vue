<template>
  <div class="tip-alert" v-if="!isPersonAuth">
    <div class="tip">
      <i class="el-icon-message-solid tip-icon"></i>您的账号
      <span @click="goAuth">未认证</span>，请进行认证，以方便您完成更多操作
    </div>
  </div>

  <!-- <div
    class="authTip"
    v-if="isNeedShow && !loading && Object.keys(platformProfile)"
  >
    <div class="tip-alert" v-if="!isPersonAuth">
      <div class="tip">
        <i class="el-icon-message-solid tip-icon"></i>您的账号
        <span @click="goAuth">未认证</span>，请进行认证，以方便您完成更多操作
      </div>
    </div>

    <div class="tip-alert" v-else-if="auditStatus == 'NOT_IDENTIFIED'">
      <div class="tip">
        <i class="el-icon-message-solid tip-icon"></i>您尚未完成企业认证
        ，请先进行
        <span @click="goAuth">企业信息认证</span
        >，认证成功后可以开通使用更多功能
      </div>
    </div>

    <div class="tip-alert" v-else-if="auditStatus == 'WAIT'">
      <div class="tip">
        <i class="el-icon-message-solid tip-icon"></i>
        工作人员正在审核您的认证信息，请耐心等待
      </div>
    </div>

    <div class="tip-alert" v-else-if="auditStatus == 'NOT_PASS'">
      <div class="tip">
        <i class="el-icon-message-solid tip-icon"></i>
        您的企业信息认证没有通过审核，请
        <span @click="goAuth">重新认证</span>
      </div>
    </div>

    <div class="tip-alert" v-else-if="configStatus === 'CONFIG'">
      <div class="tip">
        <i class="el-icon-message-solid tip-icon"></i
        >工作人员正在为您进行系统初始化配置，请耐心等待
      </div>
    </div>
  </div> -->
</template>

<script>
import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'

const platformClient = makePlatformClient()
export default {
  computed: {
    // isAdmin() {
    //   return this.platformProfile.merchantMember.isAdmin === true
    // },
    // isNeedShow() {
    //   if (!this.userMerchantProfile || !this.userMerchantProfile.type) {
    //     return false
    //   }

    //   return this.userMerchantProfile.type === 'ENTERPRISE' && this.isAdmin
    // },
    // auditStatus() {
    //   return this.userMerchantProfile.auditStatus || ''
    // },
    isPersonAuth() {
      return this.platformProfile.user.isAuth === true
    }
  },
  async created() {
    if (!Object.keys(this.platformProfile).length) {
      return
    }
    // const [err, r] = await platformClient.oladingUserMerchantProfile({
    //   body: {}
    // })
    // if (err) {
    //   handleError(err)
    //   return
    // }

    // this.userMerchantProfile = r.data

    // const [err2, r2] = await platformClient.merchantNetGetMerchantInit({
    //   body: {
    //     merchantId: this.platformProfile.merchant.id
    //   }
    // })
    // if (err2) {
    //   handleError(err2)
    //   return
    // }
    // if (r2.data && r2.data.vo && r2.data.configureStatus) {
    //   this.configStatus = r2.data.vo.configureStatus
    // }
    // this.loading = false
  },
  props: {
    platformProfile: {
      type: Object
    }
  },
  data() {
    return {
      loading: true,
      userMerchantProfile: {},
      configStatus: ''
    }
  },
  methods: {
    goAuth() {
      this.$router.push('/ocr')
      // if (!this.isPersonAuth) {
      //   window.location.href = `${window.env.ssoURL}/personalAuthentication`
      //   return
      // }
      // window.location.href = `${window.env.ssoURL}/companyAuthentication`
    }
  }
}
</script>

<style scoped>
.tip-alert {
  background-color: #fff;
  margin-bottom: 16px;
  background: #ffe5c5;
  padding: 10px 16px;
  font-size: 12px;
}
.tip-alert .tip {
  color: #46485a;
  font-size: 13px;
  font-weight: 400;
}
.tip-alert .tip span {
  color: #feab05;
  cursor: pointer;
}
.tip-alert .tip-icon {
  color: #feab05;
  font-size: 16px;
  margin-right: 10px;
  position: relative;
  top: 1px;
}
</style>
