<template>
  <div class="contract-redirect">
    <div
      style="
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      "
    >
      <i class="el-icon-loading" /> <span id="tip">跳转中...</span>
    </div>
    <van-dialog v-model="show" title="提醒" show-cancel-button :beforeClose="closeDia">
      <div style="padding:20px;">
        <span class="msg">电子合同由第三方服务商提供，您将跳转到第三方服务商页面进行合同签署。</span>
        <span class="msgUrl" :title="msgUrl">{{msgUrl}}</span>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { Toast } from 'vant'
import { Dialog } from 'vant'

import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  components: {
    [Dialog.Component.name]: Dialog.Component,
  },
  data() {
    return {
      show: false,
      msgUrl:''
    };
  },
  async mounted() {
    const urlParams = new URLSearchParams(window.location.search)

    const silenceSignYn = urlParams.get('silenceSignYn')
    const contractId = urlParams.get('contractId')
    if (!silenceSignYn || !contractId) {
      handleError('缺少必要的参数')
      return
    }

    const [err, r] = await platformClient.elContractSign({
      body: { contractId, silenceSignYn, clientSource: 'WECHAT_APPLET' }
    })

    if (err) {
      handleError(err)
      return
    }

    Toast.clear()
    const url = `${r.data.mobileUrl}?token=${r.data.token}`
    this.msgUrl=url
    this.show=true
    
    // Dialog.confirm({
    //   title:'提醒',
    //   message:`电子合同由第三方服务商提供，您将跳转到第三方服务商页面进行合同签署！${r.data.mobileUrl}`
    // }).then(()=>{
    //   window.location.replace(url)
    // }).catch(()=>{
    //   this.$router.back()
    // })
    // window.location.href = url
    // window.location.replace(url)
  },
  methods:{
    closeDia(action,done){
      if(action==='confirm'){
        setTimeout(done,1000)
        window.location.replace(this.msgUrl)
      }else if(action==='cancel')
      this.$router.back()
    }
  }
}
</script>

<style>
.msg{
  max-height: 60vh;
  padding: 26px 24px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 20px;
  white-space: pre-wrap;
  text-align: center;
  word-wrap: break-word;
  color: #646566;
}
.msgUrl{
  width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
  color: #646566;
  display: inline-block;
}
</style>