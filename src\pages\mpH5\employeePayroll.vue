<template>
  <div
    class="employeePayrollBoHai"
    style="background: #f5f5f5; min-height: 100vh"
  >
    <div
      v-if="payroll.salaryMsg"
      style="background: #e4cfcf; padding: 10px 18px; text-align: center"
    >
      {{ payroll.salaryMsg }}
    </div>

    <div
      v-for="(group, index) in payroll.salaryStubsGroups"
      :key="index"
      style="padding: 10px; background: #fff; margin-bottom: 10px"
    >
      <h3
        style="border-bottom: 1px solid #eee; padding-bottom: 10px; margin: 0"
      >
        {{ group.salaryGroupName }}
      </h3>

      <div v-for="(item, index) in group.salaryStubsItems" :key="index">
        <div
          style="display: flex; border-bottom: 1px solid #eee; padding: 10px 0"
        >
          <span
            style="
              width: 130px;
              color: #8a8a8a;
              display: inline-block;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            "
            >{{ item.salaryItemName }}</span
          >
          <span style="flex: 1">{{ item.salary }}</span>
        </div>
      </div>
    </div>

    <div
      style="
        position: fixed;
        bottom: 0;
        width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
      "
    >
      <Button v-if="isWaitConfirm" @click="confirmSalary" type="primary" block
        >确认无误 {{ formatConfirmDuration(restOfConfirmTimeS) }}</Button
      >
      <Button v-if="isConfirmed" disabled type="primary" block>已确认</Button>
    </div>
  </div>
</template>

<script>
import { Button, Toast } from 'vant'
import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
import handleSuccess from '../../helpers/handleSuccessH5'
const platformClient = makePlatformClient()
// 1：无需确认；2：未确认；3：已确认。  优化：使用具名常量定义（枚举）；
const SALARY_NO_NEED_CONFIRM = 1
const SALARY_WAIT_CONFIRM = 2
const SALARY_CONFIRMED = 3

export default {
  components: {
    Button
  },
  computed: {
    isWaitConfirm() {
      return (
        this.salaryConfirmState === SALARY_WAIT_CONFIRM &&
        this.restOfConfirmTimeS > 0
      )
    },
    isConfirmed() {
      return (
        this.salaryConfirmState === SALARY_CONFIRMED ||
        (this.salaryConfirmState === SALARY_WAIT_CONFIRM &&
          this.restOfConfirmTimeS <= 0)
      )
    }
  },
  data() {
    return {
      payroll: {},
      payStubsId: this.$route.query.payStubsId,
      year: this.$route.query.year,
      merchantId: this.$route.query.merchantId,
      confirmTimeout: this.$route.query.confirmTimeout,
      confirmTimeUnit: this.$route.query.confirmTimeUnit,
      restOfConfirmTimeS: 0,
      salaryConfirmState: SALARY_NO_NEED_CONFIRM,
      timer: null
    }
  },
  methods: {
    async markSalaryRead() {
      const [err, r] = await platformClient.salaryStubsReviewPayStubs(
        { method: 'GET' },
        this.payroll.payStubsId
      )

      if (err) {
        handleError(err)
        return
      }
      console.log('标记工资已读')
    },
    async confirmSalary() {
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        duration: 60000
      })
      const [err, r] = await platformClient.salaryStubsConfirmedPayStubs(
        { method: 'GET' },
        this.payroll.payStubsId
      )

      if (err) {
        handleError(err)
        return
      }
      handleSuccess('确认成功')
      this.onConfirmSalary()
    },
    onConfirmSalary() {
      this.salaryConfirmState = SALARY_CONFIRMED
      this.clearTimer()
      this.payroll.confirmed = SALARY_CONFIRMED
    },
    countDown() {
      this.timer = setInterval(() => {
        this.restOfConfirmTimeS--
        if (this.restOfConfirmTimeS <= 0) {
          this.onConfirmSalary()
        }
      }, 1000)
    },
    clearTimer() {
      clearInterval(this.timer)
      this.timer = null
    },
    formatConfirmDuration(timeS) {
      const h = Math.floor(timeS / 3600)
      const m = Math.floor((timeS - h * 3600) / 60)
      const s = timeS % 60

      if (h > 0) return '(将于' + h + '小时' + m + '分钟' + s + '秒后自动确认)'
      else return '(将于' + m + '分钟' + s + '秒后自动确认)'
    },
    calRestOfConfirmTime(confirmTimeout, confirmTimeUnit, createTime) {
      let confirmTimeoutS
      if (confirmTimeUnit === 'SECOND') {
        confirmTimeoutS = confirmTimeout
      } else if (confirmTimeUnit === 'MINUTE') {
        confirmTimeoutS = confirmTimeout * 60
      } else if (confirmTimeUnit === 'HOURS') {
        confirmTimeoutS = confirmTimeout * 3600
      } else if (confirmTimeUnit === 'DAY') {
        confirmTimeoutS = confirmTimeout * 3600 * 24
      } else {
        confirmTimeoutS = 0
      }

      const nowMil = new Date().getTime()

      const createTimeMil = new Date(createTime.replace(/-/g, '/')).getTime()
      const passedTimeS = Math.floor((nowMil - createTimeMil) / 1000)
      const restOfConfirmTime = confirmTimeoutS - passedTimeS
      return restOfConfirmTime > 0 ? restOfConfirmTime : 0
    }
  },
  async created() {
    const [err, r] = await platformClient.hrSaasSalarySalaryStubsGetYearStubs(
      {
        method: 'GET'
      },
      this.year,
      this.merchantId
    )
    if (err) {
      handleError(err)
      return
    }

    const salaryStubsInfos = []

    for (let i of r.data) {
      for (let i1 of i.salaryMonthStubs) {
        salaryStubsInfos.push(...i1.salaryStubsInfos)
      }
    }

    this.payroll = salaryStubsInfos.find(
      salary => salary.payStubsId == this.payStubsId
    )
    this.restOfConfirmTimeS = this.calRestOfConfirmTime(
      this.confirmTimeout,
      this.confirmTimeUnit,
      this.payroll.createTime
    )
    this.salaryConfirmState = this.payroll.confirmed

    this.markSalaryRead()
  },
  mounted() {
    setTimeout(() => {
      if (this.isWaitConfirm) this.countDown()
    }, 1000)
  },
  beforeDestroy() {
    this.clearTimer()
  }
}
</script>

<style scoped>
</style>