<template>
  <div>
    <div style="display: flex; align-items: center; padding: 10px">
      <Picker
        style="width: 35px"
        :columns="years"
        :value="selectedYear"
        @confirm="changeYears"
      />
      <i
        style="font-size: 12px"
        class="iconfont icon-direction-arrow-border-down"
      ></i>
    </div>
    <div v-for="(salary, index) in salaryList" :key="index">
      <div v-for="(item, index) in salary.salaryMonthStubs" :key="index">
        <div
          style="
            display: flex;
            justify-content: space-between;
            background-color: #f6f6f6;
            color: #62666e;
            padding: 10px;
            font-weight: 600;
          "
        >
          <span>{{ salary.salaryYearStubsName }}年{{ item.monthName }}月</span>
          <span>{{ item.monthSalaryTotal }}</span>
        </div>
        <div v-for="(cell, index) in item.salaryStubsInfos" :key="index">
          <div
            style="padding: 0 10px"
            @click="
              payrollDetail(cell, salary.confirmTimeout, salary.confirmTimeUnit)
            "
          >
            <div
              style="
                display: flex;
                justify-content: space-between;
                color: #2f3336;
                font-size: 14px;
                align-items: center;
                padding: 15px 0;
                border-bottom: 1px solid #eee;
              "
            >
              <div style="display: flex; flex-direction: column">
                <span>{{ cell.salaryStubsName }}</span>
                <span
                  style="color: #a4a3a7; font-size: 8px; margin-top: 10px"
                  >{{ cell.createTime }}</span
                >
              </div>
              <div>
                <span>{{ cell.shouldSalary }}</span>
                <i
                  class="iconfont icon-direction-arrow-border-right"
                  style="color: #a4a3a7"
                ></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      style="
        height: calc(100vh - 90px);
        display: flex;
        align-items: center;
        justify-content: center;
      "
      v-if="!salaryList.length"
    >
      暂无数据
    </div>
  </div>
</template>

<script>
import Picker from './auth/ocr/picker.vue'
import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()
export default {
  components: {
    Picker
  },

  data() {
    return {
      salaryList: [],
      merchantId: '',
      selectedYear: new Date().getFullYear(),
      years: [
        '2023',
        '2022',
        '2021',
        '2020',
        '2019',
        '2018',
        '2017',
        '2016',
        '2015'
      ]
    }
  },
  async created() {
    const [err, r] = await platformClient.merchantPlatformProfile({
      body: {}
    })

    if (err) {
      handleError(err)
      return
    }

    const profile = r.data
    this.merchantId = profile.merchant.id
    this.getPayrolls()
  },
  methods: {
    async getPayrolls() {
      const [err, r] = await platformClient.hrSaasSalarySalaryStubsGetYearStubs(
        {
          method: 'GET'
        },
        this.selectedYear,
        this.merchantId
      )
      if (err) {
        handleError(err)
        return
      }
      // this.year = r.data.stubsList[0].salaryYearStubsName
      this.salaryList = r.data
    },
    changeYears(n) {
      this.selectedYear = n
      this.getPayrolls()
    },
    payrollDetail(payroll, confirmTimeout, confirmTimeUnit) {
      this.$router.push({
        path: '/employees/payroll',
        query: {
          payStubsId: payroll.payStubsId,
          year: this.selectedYear,
          merchantId: this.merchantId,
          confirmTimeout,
          confirmTimeUnit
        }
      })
    }
  }
}
</script>

<style  scoped>
::v-deep .van-cell {
  padding: 0 !important;
}
</style>
