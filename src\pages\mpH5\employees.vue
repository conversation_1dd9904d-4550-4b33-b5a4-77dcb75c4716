<template>
    <div class="employee-list">
        <van-list v-model="loading" :finished="finished" finished-text="">
            <employee-card v-for="employee in allEmployees" :key="employee.id" :employee="employee"
                @click="handleCardClick" />
        </van-list>

        <div v-if="allEmployees.length === 0 && !loading" class="empty">
            <img src="../../assets/images/mph5/employee/list-empty.png" alt="" />
            <p>暂无数据</p>
        </div>

        <footer v-if="isShowAddButton">
            <van-button block type="info" @click="addEmployee"> 添加员工 </van-button>
        </footer>
    </div>
</template>

<script>
import EmployeeCard from 'kit/components/mpH5/employees/EmployeeCard.vue'
import { List, Button } from 'vant'
import makeYrlClient from 'kit/services/yrl/makeClient.js'
import handleError from 'kit/helpers/handleErrorH5.js'

const yrlClient = makeYrlClient()

export default {
    components: {
        EmployeeCard,
        [List.name]: List,
        [Button.name]: Button
    },
    data() {
        return {
            allEmployees: [],
            displayedEmployees: [],
            page: 1,
            pageSize: 9999,
            loading: false,
            finished: true,
            isShowAddButton: false
        }
    },
    methods: {
        async loadEmployees() {
            if (this.loading) return

            this.loading = true

            this.finished = false
            const [err, r] = await yrlClient.getEmpListByUser({
                body: {}
            })
            if (err) return handleError(err)

            this.allEmployees = r.data
            this.finished = true
            this.loading = false
        },
        handleCardClick(employee) {
            this.$router.push({
                path: '/employee/detail',
                query: employee
            })
        },
        addEmployee() {
            this.$router.push('/employee/add')
        },
        async loadRole() {
            yrlClient.merchantPlatformProfile({
                body: {}
            })
            const [err, result] = await yrlClient.getMerchantUserPrivilege({
                body: {}
            })
            if (err) return handleError(err)
            const usableFunctionCodes = result.data[0].privilegeVOS || []
            for (let item of usableFunctionCodes) {
                if (item.code === 'hroEmployee.employee.roster.add') {
                    this.isShowAddButton = true
                    break
                }
            }
        }
    },
    mounted() {
        this.loadRole()
        document.title = '花名册'
        this.loadEmployees()
    }
}
</script>

<style scoped>
.employee-list {
    background: #f6f6f7;
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 100px;
}

footer {
    box-shadow: 0 -4px 10px 0 #0000000f;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 10px;
    padding-bottom: calc(10px + constant(safe-area-inset-bottom));
    padding-bottom: calc(10px + env(safe-area-inset-bottom));
}

.empty {
    width: 60vw;
    text-align: center;
    margin: 0 auto;
    padding-top: 100px;
    color: #999;
    font-size: 14px;
}

.empty p {
    margin-top: 10px;
}

.empty img {
    width: 100%;
    display: block;
}
</style>