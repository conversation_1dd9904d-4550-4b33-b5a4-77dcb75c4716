<template>
    <div class="add-employee">
        <ProgressBar v-model="stepActive" style="margin-bottom: 12px" :options="steps" />

        <!-- <pre style="font-size: 12px;">{{ JSON.stringify(formData, null, 4) }}</pre> -->

        <div v-show="stepActive === 'baseInfoForm'">
            <BaseInfoForm ref="baseInfoForm" />
            <MoreButton v-model="isMoreBaseInfo" />
            <MoreBaseInfoForm v-show="isMoreBaseInfo" ref="moreBaseInfoForm" />
        </div>

        <div v-show="stepActive === 'entryInfoForm'">
            <EntryInfoForm ref="entryInfoForm" />
            <MoreButton v-model="isMoreEntryInfo" />
            <MoreEntryInfoForm v-show="isMoreEntryInfo" ref="moreEntryInfoForm" />
        </div>

        <div v-show="stepActive === 'bankInfoForm'">
            <BankInfoForm ref="bankInfoForm" />
        </div>

        <div v-if="stepActive === 'done'" class="done">
            <img src="../../../assets/images/mph5/employee/done.png" alt="" />
            <span style="margin-bottom:20px;display:block">添加员工完成</span>
            <!-- <Button block @click="reloadPage"> 继续添加 </Button> -->
            <Button block @click="routerBack"> 返回列表 </Button>
        </div>

        <FloatingBottomButton v-if="stepActive !== 'done'">

            <Button block native-type="submit" @click="preStep" v-show="isPreStepButton" style="margin-right: 10px">
                上一步
            </Button>
            <Button block type="info" native-type="submit" @click="nextStep" v-show="isNextStepButton">
                下一步
            </Button>

            <Button block type="info" native-type="submit" @click="handleSubmitClick" :loading="isLoading"
                v-show="isSubmitButton">
                确认提交
            </Button>
        </FloatingBottomButton>
    </div>
</template>

<script>
import ProgressBar from 'kit/components/mpH5/employees/ProgressBar.vue'
import BaseInfoForm from 'kit/components/mpH5/employees/BaseInfoForm.vue'
import MoreBaseInfoForm from 'kit/components/mpH5/employees/MoreBaseInfoForm.vue'
import MoreEntryInfoForm from 'kit/components/mpH5/employees/MoreEntryInfoForm.vue'
import EntryInfoForm from 'kit/components/mpH5/employees/EntryInfoForm.vue'
import BankInfoForm from 'kit/components/mpH5/employees/BankInfoForm.vue'
import { Button, Notify } from 'vant'
import MoreButton from "kit/components/mpH5/employees/MoreButton.vue"
import FloatingBottomButton from 'kit/components/mpH5/employees/FloatingBottomButton.vue'

import makeYrlClient from 'kit/services/yrl/makeClient.js'
import handleError from 'kit/helpers/handleErrorH5.js'
import { steps } from './options.js'
import { Toast } from "vant"
const yrlClient = makeYrlClient()

const formateRequestParams = params => {
    delete params.idValidityPeriod
    delete params.idCardInfo
    return params
}

export default {
    components: {
        Button,
        BankInfoForm,
        MoreButton,
        ProgressBar,
        BaseInfoForm,
        MoreEntryInfoForm,
        MoreBaseInfoForm,
        EntryInfoForm,
        FloatingBottomButton
    },
    computed: {
        isNextStepButton() {
            if (this.stepActive === "done") return
            if (this.stepActive === "bankInfoForm") return
            return true
        },
        isPreStepButton() {
            return this.stepActiveIndex
        },
        isSubmitButton() {
            return this.stepActive === "bankInfoForm"
        },
        stepActive() {
            return steps[this.stepActiveIndex].value
        }
    },
    data() {
        return {
            steps,
            stepActiveIndex: 0,
            isLoading: false,
            isMoreBaseInfo: false,
            isMoreEntryInfo: false,
            formData: {
                empName: '',
                mobile: '',
                idNo: '',
                // 是否长期
                certificateStartTime: '',
                certificateEndTime: '',
                certificateIsLongTerm: '',
                // 民族
                nationality: '',
                gender: '',
                birthday: '',
                deptId: '',
                entryDate: '',
                // 工种
                workType: '',
                jobType: '',
                // 是否有重大病史
                medicalHistory: false,
                // 意外险
                insureYn: ''
            }
        }
    },
    watch: {
        stepActiveIndex() {
            window.scrollTo({
                top: 0,
            });
        }
    },
    methods: {
        routerBack() {
            window.history.back()
        },
        reloadPage() {
            window.location.reload()
        },
        handleMoreBaseInfo() {
            this.isMoreBaseInfo = !this.isMoreBaseInfo
        },
        preStep() {
            this.stepActiveIndex -= 1
        },
        async nextStep() {

            const errorMsg = await this.$refs[this.stepActive].validate()

            if (this.stepActiveIndex == 0) {
                const errorMsg = await this.$refs.moreBaseInfoForm.validate()
                if (errorMsg) return Notify(errorMsg)
            }

            if (this.stepActiveIndex == 1) {
                const errorMsg = await this.$refs.moreEntryInfoForm.validate()
                if (errorMsg) return Notify(errorMsg)
            }


            if (!errorMsg) {
                this.stepActiveIndex++
                // this.stepActive = 'entryInfoForm'
            }

            Object.keys(this.$refs).forEach(refKey => {
                this.formData = {
                    ...this.formData,
                    ...this.$refs[refKey].getFromData(),
                }
            })
        },
        async handleSubmitClick() {

            const errorMsg = await this.$refs[this.stepActive].validate()
            if (errorMsg) return Notify(errorMsg)

            Object.keys(this.$refs).forEach(refKey => {
                this.formData = {
                    ...this.formData,
                    ...this.$refs[refKey].getFromData(),
                }
            })

            Toast.loading({
                message: '个人实名认证中...',
                forbidClick: true,
                duration: 0
            });

            this.isLoading = true
            const [err, r] = await yrlClient.addEmpByBasicInfo({
                body: formateRequestParams(this.formData)
            })

            Toast.clear()

            this.isLoading = false

            if (err) return handleError(err)

            this.stepActiveIndex = 3
            window.history.back()
        }
    },
    mounted() {
        document.title = '添加员工'
        window.scrollTo({
            top: 0,
        });
    }
}
</script>

<style scoped>
.add-employee {
    background: #f6f6f7;
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 100px;
}

.add-employee ::v-deep .van-field__control::placeholder {
    font-weight: normal;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #c0c4cc;
}

.add-employee ::v-deep .van-radio__icon--checked .van-icon {
    background: #5878ff !important;
    border-color: #5878ff !important;
}

.done {
    width: 3rem;
    text-align: center;
    font-size: 12px;
    color: #333;
    margin: 100px auto;
}

.done img {
    width: 100%;
    display: block;
}
</style>
