<template>
    <div class="employee-detail">
        <!-- <pre style="font-size: 12px">
        {{ JSON.stringify(this.$route.query, null, 4) }}
    </pre> -->
        <van-cell-group title="身份信息">
            <van-cell title="姓名" :value="employeeInfo.empName" />
            <van-cell title="身份证号" :value="employeeInfo.idNo" />
            <van-cell title="证件有效期" :value="idNoTime" />
            <van-cell title="民族" :value="employeeInfo.nationality || '-'" />
            <van-cell title="性别" :value="gender" />
            <van-cell title="出生日期" :value="employeeInfo.birthday || '-'" />
            <van-cell title="手机号" :value="employeeInfo.mobile" />

            <van-cell title="头像" @click="handlePreview(employeeInfo.avatar)">
                <van-image style="border:0.5px solid #eee;border-radius: 4px;" width="2rem" height="2rem" fit="contain"
                    :src="employeeInfo.avatar" />
            </van-cell>
        </van-cell-group>
        <van-cell title="籍贯" :value="getByKeyText('nativePlace')" />
        <van-cell title="文化程度" :value="getByKeyEnum('eduLevel')" />
        <van-cell title="学历" :value="getByKeyEnum('educational')" />
        <van-cell title="学位" :value="getByKeyEnum('eduDegree')" />
        <van-cell title="人员类型" :value="getByKeyText('empType')" />
        <van-cell title="婚姻状况" :value="getByKeyEnum('maritalStatus')" />
        <van-cell title="特长" :value="getByKeyText('skill')" />
        <van-cell title="紧急联系人" :value="getByKeyText('emergencyPerson')" />
        <van-cell title="紧急联系人电话" :value="getByKeyText('emergencyNumber')" />

        <van-cell-group title="入职信息">
            <van-cell title="入职公司" :value="employeeInfo.taxSubName || '-'" />
            <van-cell title="服务客户" :value="employeeInfo.hroCustomerName" />
            <van-cell title="服务合同" :value="employeeInfo.hroContractName" />
            <van-cell title="入职部门" :value="employeeInfo.deptName || '-'" />
            <van-cell title="入职日期" :value="employeeInfo.entryDate" />
            <van-cell title="工种" :value="employeeInfo.workType" />
            <van-cell title="工人类型" :value="employeeInfo.jobType || '-'" />
            <van-cell title="班组长" :value="employeeInfo.monitorYn ? '是' : '否'" />
            <van-cell title="劳动合同" :value="employeeInfo.contractYn ? '有' : '无'" />
            <van-cell title="重大病史" :value="employeeInfo.medicalHistory ? '有' : '无'" />
            <van-cell title="工伤意外险" :value="employeeInfo.insureYn ? '有' : '无'" />

            <!--  新字段 -->
            <van-cell title="进场时间" :value="getByKeyText('entryTime')" />
            <van-cell title="进场确认附件" @click="handlePreview(employeeInfo.entryFile)">
                <van-image style="border:0.5px solid #eee;border-radius: 4px;" width="2rem" height="2rem" fit="contain"
                    :src="employeeInfo.entryFile" />
            </van-cell>
            <van-cell title="制卡时间" :value="getByKeyText('empCardStart')" />
            <van-cell title="制卡采集照片" @click="handlePreview(employeeInfo.empCardPhoto)">
                <van-image style="border:0.5px solid #eee;border-radius: 4px;" width="2rem" height="2rem" fit="contain"
                    :src="employeeInfo.empCardPhoto" />
            </van-cell>
            <!-- <van-cell title="入职日期" :value="getByKeyText('entryDate')" /> -->
            <van-cell title="考勤卡号" :value="getByKeyText('empCardNo')" />
            <van-cell title="是否加入工会" :value="employeeInfo.laborUnionYn ? '是' : '否'" />
            <van-cell title="加入工会时间" :value="getByKeyText('joinUnionDate')" />
        </van-cell-group>

        <!--  新字段 -->
        <van-cell-group title="银行卡信息">
            <van-cell title="工资银行卡号" :value="getByKeyText('wageCardNum')" />
            <van-cell title="工资银行名称" :value="getByKeyText('wageCardBank')" />
        </van-cell-group>
    </div>
</template>

<script>

import { getEnumByCodeOptions } from 'kit/components/mpH5/employees/options.js';
import { Cell, CellGroup, Tag, Image, ImagePreview } from 'vant'

export default {
    components: {
        [Cell.name]: Cell,
        [CellGroup.name]: CellGroup,
        [Image.name]: Image,
        [Tag.name]: Tag
    },
    data() {
        return {
            employeeInfo: {
                // name: '张大千',
                // idNumber: '110000000000000001',
                // idValidPeriod: '2020.05 - 2030.05',
                // ethnicity: '汉',
                // gender: '男',
                // birthDate: '2000-09-01',
                // phoneNumber: '***********',
                // company: '公司名称',
                // serviceClient: '客户名称',
                // serviceContract: '合同名称',
                // department: '建筑组',
                // entryDate: '2020-05-01',
                // jobType: '建筑工人',
                // workerType: '工人',
                // majorMedicalHistory: false,
                // accidentInsurance: false
                ...this.$route.query
            },
            options: {
                eduDegree: [],
                educational: [],
                eduLevel: [],
                empType: [],
                maritalStatus: []
            }
        }
    },
    methods: {
        getByKeyText(key) {
            return this.employeeInfo[key] || "-"
        },
        getByKeyEnum(key) {
            const value = this.employeeInfo[key]
            if (!value) return '-'
            const options = this.options[key]
            const option = options.find(item => item.optionEnumCode == value)
            return option ? option.optionEnumName : '-'
        },
        maskIdNumber(idNumber) {
            if (idNumber && idNumber.length > 10) {
                return idNumber.substr(0, 6) + '****' + idNumber.substr(-4)
            }
            return idNumber
        },
        handlePreview(src) {
            if (!src) return
            ImagePreview([
                src,
            ]);
        },
        async loadOptions() {
            const [eduType, eduLevel, empType, maritalStatus, degree] = await getEnumByCodeOptions(
                ['eduType', 'eduLevel', 'empType', 'maritalStatus', 'degree']
            )
            this.options.eduDegree = degree
            this.options.educational = eduType
            this.options.eduLevel = eduLevel
            this.options.empType = empType
            this.options.maritalStatus = maritalStatus
        }
    },
    computed: {
        idNoTime() {
            const {
                certificateStartTime,
                certificateEndTime,
                certificateIsLongTerm
            } = this.employeeInfo

            const startTime = (certificateStartTime || '').replace(/-/g, '.')
            const endTime = (certificateEndTime || '').replace(/-/g, '.')

            if (certificateIsLongTerm === 'true') {
                return `${startTime}-长期`
            }
            return `${startTime} 至 ${endTime}`
        },
        gender() {
            const { gender } = this.employeeInfo
            return gender === 'MALE' ? '男' : '女'
        }
    },
    created() {
        document.title = '员工详情'
        this.loadOptions()
    },
    mounted() {
        window.scrollTo({
            top: 0,
        });
    },
}
</script>

<style scoped>
.employee-detail {
    background-color: #f8f8f8;
}
</style>