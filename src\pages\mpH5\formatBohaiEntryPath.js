const formatBoHaiEntryPath = (app, entry) => {
  if (entry.code === 'CONTRACT_MANAGEMENT') {
    entry.path = `${window.env.host}/contract-h5/`
    return
  }
  if (entry.code === 'HR' && entry.name === '个人档案') {
    entry.path = `${window.env.host}/h5-hrsaas/employee-info/index`
    return
  }
  if (entry.code === 'INVOICES') {
    entry.path = `${window.env.host}/sheet-h5/invoices`
    return
  }
  if (entry.code === 'ENTERPRISE_TRAINING' && entry.name === '培训') {
    entry.path = `${window.env.host}/training-h5/`
    return
  }
  if (entry.code === 'RECRUIT' && entry.name === '招聘') {
    entry.path = `${window.env.host}/recruit-h5/interviewer/recruit`
    return
  }
  if (app.code === 'AUTOAPPROVAL' && entry.name === '更多') {
    entry.path = `${window.env.host}/sheet-h5/index`
    return
  }
  if (app.code === 'AUTOAPPROVAL' && app.name === '常用审批') {
    const path = entry.path.replace('processId', 'receiptId')
    const tmp = path.split('?')
    entry.path = `${window.env.host}/sheet-h5/start?${tmp[1]}`
    return
  }

  if (app.code === 'HRATTEND' && entry.name === '更多') {
    entry.path = `${window.env.host}/sheet-h5/index`
    return
  }
  if (app.code === 'HRATTEND' && app.name === '假勤服务') {
    if (entry.code === 'attendance') {
      entry.path = '/attend'
      entry['isCurrentProject'] = true
      return
    }
    if (entry.code === 'statistics') {
      entry.path = '/attend?goStats=true'
      entry['isCurrentProject'] = true
      return
    }
    const path = entry.path.replace('processId', 'receiptId')
    const tmp = path.split('?')
    entry.path = `${window.env.host}/sheet-h5/start?${tmp[1]}`
    return
  }
  if (app.code === 'SERVICE_SECTOR' && entry.code === 'SERVICE_SECTOR_MORE') {
    entry.path = `${window.env.host}/appstore-h5/`
    return
  }
  if (app.code === 'SERVICE_SECTOR' && entry.code === 'WELFARE') {
    entry.path = `/welfares`
    entry['isCurrentProject'] = true
    return
  }

  if (app.code === 'SPIRITUAL' && entry.code === 'WHTAX_FRREDOM') {
    entry.path = `${window.env.host}/lite-front-h5/`
    return
  }
}

export default formatBoHaiEntryPath
