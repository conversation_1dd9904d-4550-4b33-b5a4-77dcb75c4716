import store from 'kit/helpers/store'

const goApproval = ({
  processId,
  receiptId,
  designId,
  processDesignId,
  initTableData
}) => {
  const token = store.get('token')
  if (processId) {
    window.location.href =
      `${window.env.sheetH5URL}/detail?` + `id=${processId}&token=${token}`
    return
  }

  const toURL =  `${window.env.sheetH5URL}/start`
  const params = `?designId=${designId}&receiptId=${receiptId}&processDesignId=${processDesignId}&initTableData=${initTableData}&token=${token}`

  window.location.href = toURL+params
}

export default goApproval
