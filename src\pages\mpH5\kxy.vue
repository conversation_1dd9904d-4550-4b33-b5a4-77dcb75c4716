<template>
  <div class="citic">
    <div
      v-if="loading"
      class="loading"
      style="
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      "
    >
      <i class="el-icon-loading" /> <span id="tip">加载中</span>
    </div>
  </div>
</template>

<script>
import formatApps from '../../formatters/mpH5/apps'
import handleErrorH5 from '../../helpers/handleErrorH5'
import { setToken } from '../../helpers/token'
import makePlatformClient from '../../services/platform/makeClient'
import formatBoHaiEntryPath from './formatBohaiEntryPath'
import {Toast} from 'vant'

const platformClient = makePlatformClient()
export default {
  data() {
    return {
      loading: false
    }
  },
  async mounted() {
    const urlParams = new URLSearchParams(window.location.search)

    var to = urlParams.get('to')
    if (!to) {
      to = '/workbench'
    } else {
      to = window.atob(to)
    }

    const accessKey = urlParams.get('token')
    const kxyProtocolId = urlParams.get('INNPRTCNO')
    setTimeout(() => {
      this.loading = true
    }, 1000)
    // 进行发啦登录
    const [err, r] = await platformClient.merchantCiticFalaLogin({
      body: {
        accessToken: accessKey,
        kxyProtocolId
      }
    })
    if (err) {
      if(err.message && err.message.includes("所在的企业") && (err.message.includes("删除") || err.message.includes("未开通")|| err.message.includes("没有开通"))){
        Toast.fail({
          message:err.message,
          duration:0
        })
        return
      }

      this.loading = false
      handleErrorH5(err)
      return
    }

    var token = ""
    if(r.data){
      token = r.data
    }
    if(r.data && r.data.token) {
      token = r.data.token
    }

    // const token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwIjp7InIiOiJ0cnVlIiwidCI6IlBFUlNPTkFMIiwidyI6InIiLCJtIjoiNDEiLCJuIjoi5L2G5piv5oiR576k5aSaIiwibWsiOiIxIn0sInQiOiIwIiwidyI6InIiLCJleHAiOjE3MDkyODExOTYsIm8iOiIxMDQyMDAifQ.hi69pOPuFIdXPd3Ma51plc9Ksr9mMqeMN-FACuaotj8'
    setToken(token)

    //避免有些应用使用token的方式与mph5不同
    if (to.includes('?')) {
      to = to + '&token=' + token
    } else {
      to = to + '?token=' + token
    }

    const toURL = encodeURI(to)

    const tipEl = document.getElementById('tip')
    if (tipEl) {
      tipEl.innerText = '跳转中'
    }

    var go = ''
    if (toURL.includes('go=') && toURL.includes('http')) {
      const url = new URL(toURL)
      go = url.searchParams.get('go')
    }
    if (toURL.includes('go=') && !toURL.includes('http')) {
      const url = new URLSearchParams()
      go = url.searchParams.get('go')
    }
    if (go) {
      this.go(go)
      return
    }

    if (toURL.startsWith('http')) {
      window.location.replace(toURL)
      return
    }

    this.$router.replace(toURL)
  },
  methods: {
    async go(target) {
      const [err, r] = await platformClient.merchantPlatformListAppMenu()
      if (err) {
        handleErrorH5(err)
        return
      }
      const apps = formatApps(r.data.menuGroupVos)
      const approval = apps.find(item => item.code === 'AUTOAPPROVAL')
      const sheets = approval && approval.entryVos ? approval.entryVos : []
      var sheet = null
      var msg = ''
      if (target === 'freeApplication') {
        sheet = sheets.find(item => item.name.includes('费用申请'))
        msg = '费用申请'
      }
      if (target === 'reimbursement') {
        sheet = sheets.find(item => item.name.includes('费用报销'))
        msg = '费用报销'
      }
      if (!sheet) {
        handleErrorH5(
          `没有"${msg}"单据，请联系管理员添加, 将在3秒后跳转审批中心`
        )
        setTimeout(
          () => window.location.replace(window.env.host + '/sheet-h5/index'),
          3000
        )
        return
      }

      formatBoHaiEntryPath(approval, sheet)

      var url = `${sheet.path}`
      if (!sheet.isCurrentProject) {
        window.location.replace(url)
        return
      }

      this.$router.replace(url)
    }
  }
}
</script>

<style scoped>
</style>