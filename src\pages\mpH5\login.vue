<template>
  <div class="login" style="text-align: center; padding-top: 120px">
    <div v-show="!showAgreement">
      <img style="width: 93px; margin-bottom: 40px" :src="configInfo.logo" />
      <Field maxlength="11" placeholder="请输入手机号" v-model="phone" />
      <Captcha style="width: 100%" v-model="captcha" />
      <Otp
        style="width: 100%"
        v-model="otp"
        :captcha="captcha"
        :phone="phone"
      />
      <Button
        :disabled="disabled"
        style="width: calc(100vw - 32px); margin-top: 30px"
        @click="login"
        type="primary"
      >
        登录
      </Button>
      <div style="display: flex; justify-content: center; margin-top: 20px">
        <Checkbox shape="square" v-model="agreed">我已阅读并同意</Checkbox>
        <a style="color: #4f71ff" @click="showAgreement = true">
          《{{ configInfo.agreementName }}》
        </a>
      </div>
    </div>
    <div v-show="showAgreement" style="padding: 0 16px">
      <div v-html="configInfo.agreementContent" />
      <div
        @click="showAgreement = false"
        style="position: fixed; right: 26px; top: 20px; background: #fff"
      >
        关闭
      </div>
    </div>
  </div>
</template>

<script>
//渤海银行专有h5页面
import { Field, Button, Checkbox, Popup } from 'vant'
import Captcha from './captcha.vue'
import Otp from './otp.vue'
import handleError from '../../helpers/handleErrorH5'
import handleSuccess from '../../helpers/handleSuccessH5'
import store from '../../helpers/store'
import makePlatformClient from '../../services/platform/makeClient'

const platformClient = makePlatformClient()

export default {
  components: {
    Field,
    Captcha,
    Button,
    Checkbox,
    Popup,
    Otp
  },
  computed: {
    disabled() {
      if (!this.phone) {
        return true
      }
      if (!this.captcha.answer || !this.captcha.token) {
        return true
      }
      if (!this.otp.answer || !this.otp.token) {
        return true
      }
      if (!this.agreed) {
        return true
      }

      return false
    },
    needSwitchMerchantId() {
      const params = new URLSearchParams(window.location.search)
      const merchantId = params.get('merchantId') || ''
      return merchantId.trim()
    },
    toURL() {
      const params = new URLSearchParams(window.location.search)
      const to = params.get('to')
      if (to === null) {
        return ''
      }
      const url = window.atob(to)
      if (!url.includes('http')) {
        return ''
      }

      const toUrl = new URL(url)
      const toParams = new URLSearchParams(toUrl.search)

      toParams.append('back', 'login')

      for (const [key, value] of toParams) {
        if (value === 'null' || value === '') {
          toParams.delete(key)
        }
      }

      toUrl.search = toParams.toString()

      return toUrl.toString()
    }
  },
  data() {
    return {
      agreed: false,
      configInfo: {},
      showAgreement: false,
      openId: '',
      phone: '',
      captcha: {
        answer: '',
        token: ''
      },
      otp: {
        answer: '',
        token: ''
      }
    }
  },
  async created() {
    const [err, r] = await platformClient.merchantAppletsConfigGetConfigInfo()
    if (err) {
      handleError(err)
      return
    }
    this.configInfo = r.data

    const logined = store.get('token')
    if (logined && window.location.href.includes('reload=true')) {
      this.$router.push('/workbench')
      return
    }
    if (logined && this.needSwitchMerchantId) {
      const isAutoLogin = true
      this.switchMerchant(this.needSwitchMerchantId, logined, isAutoLogin)
      return
    }
    if (logined && this.toURL) {
      window.location.href = this.toURL
      return
    }
  },
  methods: {
    async login() {
      const [err, r] = await platformClient.merchantPlatformLogin({
        body: {
          device: 'WECHAT_APPLET',
          loginWay: 'CELLPHONE_SMS',
          account: this.phone,
          password: this.otp.answer,
          otpToken: this.otp.token,
          merchantId: '',
          openId: this.openId
        }
      })

      if (err) {
        handleError(err)
        return
      }
      const firstToken = r.data

      const [err2, r2] = await platformClient.merchantPlatformProfile({
        body: {},
        requestInterceptor: (resource, options) => {
          options.headers.Authorization = `Bearer ${firstToken}`
          return [null, resource, options]
        }
      })
      if (err2) {
        handleError(err2)
        return
      }
      const merchantId =
        this.needSwitchMerchantId || r2.data.loginDefaultMerchantId

      this.switchMerchant(merchantId, firstToken)
    },
    async switchMerchant(merchantId, firstToken, isAutoLogin) {
      const [err3, r3] = await platformClient.merchantPlatformRenewToken({
        body: { merchantId },
        requestInterceptor: (resource, options) => {
          options.headers.Authorization = `Bearer ${firstToken}`
          return [null, resource, options]
        }
      })

      if (err3 && isAutoLogin) {
        handleError('请重新登录')
        return
      }

      if (err3) {
        handleError(err3)
        return
      }

      store.set('token', r3.data.token)

      handleSuccess('登录成功')

      if (this.toURL) {
        window.location.href = this.toURL
        return
      }

      var url = window.location.href
      if (url.includes('?')) {
        url += `&reload=true`
      } else {
        url += `?reload=true`
      }

      window.location.replace(url)
    }
  }
}
</script>

<style></style>
