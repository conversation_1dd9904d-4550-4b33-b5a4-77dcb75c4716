<template>
  <div class="mine" style="background: #f5f5f5" v-if="profile">
    <div style="height: 158px; background: #4f71fe">
      <PersonalInfo
        style="padding: 40px 16px"
        @changeMerchant="changeMerchant"
        :user="profile.user"
        :merchant="profile.merchant"
        :joinedMerchant="profile.joinedMerchant"
      />
    </div>
    <div
      style="
        height: calc(100vh - 218px);
        background: #f5f5f5;
        border-radius: 20px 20px 0 0;
        padding: 30px 16px;
        position: relative;
        top: -30px;
      "
    >
      <Card title="账号与安全">
        <CellGroup style="border-radius: 8px">
          <Cell @click="goSignatures" size="large" title="手写签名" is-link />
          <Cell
            @click="goPhoneChange"
            size="large"
            title="手机号"
            :value="hiddenPhone(profile.user.cellPhone)"
            is-link
          />
          <Cell
            @click="goSecurityPassword"
            size="large"
            title="安全密码"
            :value="securityPasswordText"
            is-link
          />
          <Cell @click="goMyAgreement" size="large" title="我的协议" is-link />
          <Cell
            v-if="showElectronHistoryFile"
            @click="goElectronicHistoryFile"
            size="large"
            title="电子历史档案"
            is-link
          />
        </CellGroup>
      </Card>
      <Button
        style="
          width: 100%;
          border: 1px solid #a8acba;
          background: #f5f5f5;
          border-radius: 6px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 16px;
          color: #777c94;
          letter-spacing: 0;
          text-align: center;
          line-height: 16px;
          margin-top: 200px;
        "
        @click="logout"
        type="default"
        >退出登录</Button
      >

      <div
        style="
          width: 100vw;
          left: 0;
          position: fixed;
          bottom: 0;
          background: #ffffff;
        "
      >
        <Actions defaultAction="mine" />
      </div>
    </div>
    <CompanySwitcher
      ref="companySwitcher"
      :joinedMerchant="profile.joinedMerchant"
      :currentMerchantId="profile.merchant ? profile.merchant.id : 0"
    />
  </div>
</template>

<script>
import { Cell, CellGroup, Button, Dialog } from 'vant'
import Actions from '../../components/mpH5/workbench/menusH5.vue'
import Card from '../../components/mpH5/workbench/cardH5.vue'
import PersonalInfo from '../../components/mpH5/mine/personalInfoH5.vue'
import CompanySwitcher from './companySwitcher.vue'
import ExpertConsult from './workbench/expertConsult.vue'
import store from '../../helpers/store'
import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  components: {
    PersonalInfo,
    Actions,
    Card,
    Button,
    Cell,
    CellGroup,
    Dialog,
    CompanySwitcher,
    ExpertConsult
  },

  computed: {
    securityPasswordText() {
      if (this.isSetSecurityPassword === true) {
        return '已设置'
      }
      if (this.isSetSecurityPassword === false) {
        return '未设置'
      }
      return ''
    },
    joinedMerchant() {
      return this.profile.joinedMerchant || []
    },
    leaveMerchant() {
      let leave = []

      for (let merchant of this.joinedMerchant) {
        if (merchant.disabled) {
          leave.push(merchant)
        }
      }

      return leave
    },
    showElectronHistoryFile() {
      return this.leaveMerchant.length > 0 && this.profile?.user?.isAuth
    }
  },
  data() {
    return {
      profile: null,
      isSetSecurityPassword: null
    }
  },
  async created() {
    const [err, r] = await platformClient.merchantPlatformProfile({
      body: {}
    })

    if (err) {
      handleError(err)
      return
    }

    this.profile = r.data

    const [err2, r2] = await platformClient.merchantAuthenticatedProfile()

    if (err2) {
      handleError(err2)
      return
    }

    this.isSetSecurityPassword = r2.data.userBehavior.signPassword
  },
  methods: {
    goMyAgreement() {
      this.$router.push('/myAgreement')
    },
    goSignatures() {
      this.$router.push('/signatures')
    },
    goPhoneChange() {
      this.$router.push('/phoneChange')
    },
    goSetting() {
      this.$router.push('/setting')
    },
    goElectronicHistoryFile() {
      this.$router.push('/electronicHistoryFile')
    },
    goSecurityPassword() {
      if (this.isSetSecurityPassword) {
        this.$router.push('/securityPasswordChange')
        return
      }
      this.$router.push('/securityPasswordNew')
    },
    hiddenPhone(phone) {
      let newPhone = phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
      return newPhone
    },
    changeMerchant() {
      this.$refs.companySwitcher.open()
    },
    logout() {
      Dialog.confirm({
        title: '提示',
        message: '确定退出吗?'
      }).then(async () => {
        const token = store.get('token')
        await platformClient.merchantPlatformLogout({
          body: {
            token
          }
        })
        store.set('token', '')

        window.location.reload()
      })
    }
  }
}
</script>

<style></style>
