<template>
  <div
    style="
      background: #f5f5f5;
      min-height: 100vh;
      box-sizing: border-box;
      padding: 20px;
      padding-bottom: 50px;
    "
  >
    <TypeCard
      v-if="contractTodos.length"
      title="合同签约"
      :content="`有${contractTodos.length}份新的文件需要您签署`"
      :date="latestContractTodoDate"
      @click.native="goTodos('contract')"
    />
    <TypeCard
      v-if="approvalTodos.length"
      title="OA审批任务"
      :content="`有${approvalTodos.length}份新的审批需要您处理`"
      :date="latestApprovalTodoDate"
      @click.native="goTodos('approval')"
    />
    <div
      style="
        width: 100vw;
        left: 0;
        position: fixed;
        bottom: 0;
        background: #ffffff;
      "
    >
      <Actions defaultAction="todos" />
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'
import TypeCard from '../../components/mpH5/todos/typeCard.vue'
import Actions from '../../components/mpH5/workbench/menusH5.vue'

import makePlatformClient from '../../services/platform/makeClient'
import { approvalTodo, contractTodo } from '../../formatters/mpH5/constants'

const platformClient = makePlatformClient()
export default {
  components: {
    TypeCard,
    Actions
  },
  computed: {
    approvalTodos() {
      return this.todos.filter(
        todo =>
          todo.appCode === approvalTodo.appCode &&
          todo.appTaskCode === approvalTodo.appTaskCode
      )
    },
    contractTodos() {
      return this.todos.filter(
        todo =>
          todo.appCode === contractTodo.appCode &&
          todo.appTaskCode === contractTodo.appTaskCode
      )
    },
    latestContractTodoDate() {
      if (this.contractTodos.length === 0) {
        return ''
      }
      return this.formatTime(this.contractTodos[0].createTime)
    },
    latestApprovalTodoDate() {
      if (this.approvalTodos.length === 0) {
        return ''
      }
      return this.formatTime(this.approvalTodos[0].createTime)
    }
  },
  data() {
    return {
      todos: []
    }
  },
  
  async created() {
    window.onpageshow = function (event) {
      if (event.persisted) {
        window.location.reload()
      }
    }
    
    Toast.loading({
      message: '加载中...',
      forbidClick: true
    })

    const [err, r] = await platformClient.apiListTodo({
      body: {
        filters: {
          status: 'TODO'
        },
        sorts: [
          {
            field: 'createTime',
            direction: 'DESCENDING'
          }
        ],
        limit: 9999,
        start: 0,
        withTotal: true
      }
    })

    Toast.clear()

    if (err) {
      handleErrorH5(err)
      return
    }

    this.todos = r.data.list
  },
  methods: {
    goTodos(type) {
      this.$router.push(`/todos?type=${type}`)
    },
    formatTime(time) {
      if (!time) {
        return ''
      }
      const date = new Date(time)
      const formattedDateTime = new Intl.DateTimeFormat('zh-CN', {
        dateStyle: 'medium'
      }).format(date)
      return formattedDateTime
    }
  }
}
</script>

<style>
</style>