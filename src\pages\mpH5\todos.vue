<template>
  <div
    style="
      background: #f5f5f5;
      min-height: 100vh;
      padding: 20px;
      box-sizing: border-box;
      padding-bottom: 50px;
    "
  >
    <template v-if="todos.length">
      <Todo
        v-for="todo in todos"
        :key="todo.id"
        :todo="todo"
        @toDeal="toDeal"
      />
    </template>
    <NoData v-else style="margin: 100px auto; width: 200px" />
  </div>
</template>

<script>
import Todo from '../../components/mpH5/todos/todo.vue'
import { Toast } from 'vant'
import handleErrorH5 from '../../helpers/handleErrorH5'
import { approvalTodo, contractTodo } from '../../formatters/mpH5/constants'
import NoData from '../../components/ui/svgIcon/noData.vue'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  name: 'contracts',
  components: {
    NoData,
    Todo
  },
  data() {
    return {
      todos: []
    }
  },
  methods: {
    async toDeal(todo) {
      // 前往审批
      if (
        todo.appCode === approvalTodo.appCode &&
        todo.appTaskCode === approvalTodo.appTaskCode
      ) {
        window.location.href = `${window.env.host}${todo.url}`
        return
      }

      // 合同签署
      if (
        todo.appCode === contractTodo.appCode &&
        todo.appTaskCode === contractTodo.appTaskCode
      ) {
        window.location.href = `${window.env.host}${todo.url}`
        return
      }
    }
  },
  async created() {
    window.onpageshow = function (event) {
      if (event.persisted) {
        window.location.reload()
      }
    }

    Toast.loading({
      message: '加载中...',
      forbidClick: true
    })

    const type = this.$route.query.type
    const params = {}

    if (type === 'approval') {
      params.appCode = [approvalTodo.appCode]
      params.appTaskCode = [approvalTodo.appTaskCode]
    }

    if (type === 'contract') {
      params.appCode = [contractTodo.appCode]
      params.appTaskCode = [contractTodo.appTaskCode]
    }

    const [err, r] = await platformClient.apiListTodo({
      body: {
        filters: { status: 'TODO', withSponsorUser: true, ...params },
        limit: 9999,
        start: 0,
        withTotal: true
      }
    })

    Toast.clear()

    if (err) {
      handleErrorH5(err)
      return
    }

    this.todos = r.data.list
  }
}
</script>

<style>
</style>