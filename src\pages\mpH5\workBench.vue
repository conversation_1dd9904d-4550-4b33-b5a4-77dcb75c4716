<template>
  <Skeleton title avatar :row="13" :loading="loading" style="margin-top: 40px">
    <div>
      <AuthTip style="flex: 1 1 100%" :platformProfile="profile" />
      <Workbench
        :profile="profile"
        :apps="apps"
        :unreadCount="unreadCount"
        @go="go"
        @goNotifications="goNotifications"
      />
      <NotJoinMerchantDialog ref="notJoinMerchantDialog" />
      <SecurityCode ref="securityCode" />

    </div>
  </Skeleton>
</template>
"
<script>
import { Dialog, Skeleton, Toast } from 'vant'
import NotJoinMerchantDialog from '../../components/mpH5/workbench/notJoinMerchantDialog.vue'
import SecurityCode from './securityCode.vue'
import Workbench from '../../components/mpH5/workbench/workbenchH5.vue'
import handleError from '../../helpers/handleErrorH5'
import store from '../../helpers/store'
import makePlatformClient from '../../services/platform/makeClient'
import formatBoHaiEntryPath from './formatBohaiEntryPath'
import AuthTip from './authTip.vue'
import demoApps from './demoApps'
import formatApps from '../../formatters/mpH5/apps'
import ExpertConsult from './workbench/expertConsult.vue'
import handleErrorH5 from '../../helpers/handleErrorH5'
const platformClient = makePlatformClient()

const myApprovalApp = {
  code: 'APPROVAL',
  name: '我的审批',
  order: 0,
  entryVos: [
    {
      code: 'TODO',
      name: '待我审批',
      icon: 'icon_tobeapproved.png',
      path: `${window.env.sheetH5URL}/check?name=待我审批&type=TODO`,
      order: 1,
      type: 'TODO',
      quantity: 0
    },
    {
      code: 'MYCREATED',
      name: '我发起的',
      icon: 'icon_contract.png',
      path: `${window.env.sheetH5URL}/check?name=我发起的&type=MYCREATED`,
      order: 2,
      type: 'MYCREATED'
    },
    {
      code: 'ENTERPRISE_TRAINING',
      name: '抄送我的',
      icon: 'icon_training.png',
      path: `${window.env.sheetH5URL}/check?name=抄送我的&type=UNREADCC`,
      order: 3,
      type: 'MP'
    },
    {
      code: 'DONE',
      name: '我的已办',
      icon: 'icon_processed.png',
      path: `${window.env.sheetH5URL}/check?name=我的已办&type=DONE`,
      order: 7,
      type: 'DONE'
    }
  ]
}

export default {
  components: {
    Skeleton,
    Workbench,
    NotJoinMerchantDialog,
    SecurityCode,
    AuthTip,
    ExpertConsult,
  },
  data() {
    return {
      profile: {},
      apps: [],
      loading: true,
      isSetSecurityPassword: false,
      unreadCount: 0
    }
  },
  computed: {
    notJoinMerchant() {
      return this.profile.joinedMerchant.length === 0
    }
  },
  async created() {
    Toast.loading({
      message: '加载中...',
      forbidClick: true,
      duration: 0
    })

    const [err, r] = await platformClient.merchantPlatformProfile({
      body: {
        withRoles: true
      }
    })

    if (err) {
      handleError(err)
      return
    }

    //删除一些数据 或者渤海后端直接不返回
    this.profile = r.data

    // 如果当前是个人token 则切换成企业token
    if (
      this.profile.joinedMerchant.length > 0 &&
      this.profile.loginDefaultMerchantId &&
      !this.profile.merchant
    ) {
      const [err3, r3] = await platformClient.merchantPlatformRenewToken({
        body: { merchantId: this.profile.loginDefaultMerchantId }
      })
      if (err3) {
        handleError(err3)
        return
      }

      store.set('token', r3.data.token)

      window.location.reload()
    }

    if (this.profile.joinedMerchant.length > 0 && this.profile.merchant) {
      await this.loadMyApproval()
      await this.loadUnreadMessages()
    }

    const [err2, r2] = await platformClient.merchantPlatformListAppMenu()
    if (err2) {
      handleError(err2)
      return
    }

    this.apps = r2.data.menuGroupVos
    this.apps.unshift(myApprovalApp)
    this.apps = formatApps(this.apps)

    // this.profile.joinedMerchant = []

    if (this.notJoinMerchant) {
      this.apps = demoApps
    }
    this.loading = false

    const [err3, r3] = await platformClient.merchantAuthenticatedProfile()

    if (err3) {
      handleError(err3)
      return
    }

    this.isSetSecurityPassword = r3.data.userBehavior.signPassword

    Toast.clear()
  },
  mounted() {
    window.onpageshow = function (event) {
      if (event.persisted) {
        window.location.reload()
      }
    }
  },
  methods: {
    async loadMyApproval() {
      const [err, r] = await platformClient.sheetProcessDesignInstanceList({
        body: {
          filters: { type: 'TODO', keywords: '' },
          withTotal: true
        }
      })

      if (err) {
        handleErrorH5(err)
        return
      }

      myApprovalApp.entryVos[0].quantity = r.data.total
    },
    async loadUnreadMessages() {
      const [err, r] = await platformClient.apiPlatMsgGroup({
        body: {
          terminal: ['MOBILE'],
          isRead: false
        }
      })

      this.unreadCount = r.data.reduce((pre, cur) => {
        return (pre += cur.number)
      }, 0)
    },
    go(app, entry) {
      formatBoHaiEntryPath(app, entry)
      if (this.notJoinMerchant) {
        this.$refs.notJoinMerchantDialog.open()
        return
      }

      console.log('todo go', app, entry)

      var url = `${entry.path}`

      // 未实名认证
      if (!this.profile.user.isAuth) {
        this.$router.push('/ocr')
        return
      }

      // 工资条
      if (entry.code === 'SALARY_MANAGER') {
        // 这里要验证是否设置安全密码
        if (!this.isSetSecurityPassword) {
          Dialog.confirm({
            title: '设置安全密码',
            message: '您还未设置安全密码,是否前往设置',
            cancelButtonText: '暂不设置',
            confirmButtonText: '前往设置'
          }).then(() => {
            this.$router.push('/securityPasswordNew')
          })

          return
        }

        // 打开安全密码输入框
        this.$refs.securityCode.open(`/employees/payrolls`)
        return
      }

      if (entry.code === 'CONTRACT_MANAGEMENT') {
        this.$router.push('/contracts')
        return
      }

      if (!entry.isCurrentProject) {
        window.location.href = url
        return
      }

      this.$router.push(url)
    },
    goNotifications() {
      this.$router.push('/messages')
    }
  }
}
</script>

<style></style>
