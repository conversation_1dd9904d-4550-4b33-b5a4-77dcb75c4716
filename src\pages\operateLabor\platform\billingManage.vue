<template>
  <div
    class="corporations"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="110px"
    >
      <div
        class="lite"
        v-if="!fullShown"
        style="display: flex; align-items: center"
      >
        <div>
          <el-form-item label="作业主体">
            <el-input
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请输入作业主体"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="客户">
            <el-input
              v-model="conditions.filters.customerId"
              placeholder="请输入客户名称"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-button
            type="text"
            @click="fullShown = true"
            style="position: relative; top: 5px"
            >展开</el-button
          >
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="full" v-else>
        <div>
          <el-form-item label="作业主体">
            <el-input
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请输入作业主体"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="客户">
            <el-input
              v-model="conditions.filters.customerId"
              placeholder="请输入客户名称"
              style="width: 280px"
            ></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="账单月">
            <el-date-picker
              v-model="createTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="handleCreateTimeChange"
              style="width: 280px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="合同">
            <el-select
              filterable
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请输入合同名称"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="corp in corporationOptions"
                :key="corp.id"
                :label="corp.name"
                :value="corp.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="状态">
            <el-select
              v-model="conditions.filters.status"
              placeholder="请选择状态"
              style="width: 280px"
              clearable
            >
              <el-option label="服务中" value="INIT"></el-option>
              <el-option label="提前终止" value="TERMINATION"></el-option>
              <el-option label="已到期" value="EXPIRED"></el-option>
            </el-select>
          </el-form-item>
          <el-button
            type="text"
            style="position: relative; top: 5px"
            @click="fullShown = false"
            >收起</el-button
          >
        </div>
        <el-form-item label=" ">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </el-form-item>
      </div>
    </el-form>

    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus" />
        生成账单
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="结算账单ID"
        width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="customerName"
        label="客户名称"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="contractName"
        label="服务合同合同名称"
        width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="supplierCorporationName"
        label="作业主体名称"
        width="160"
      ></el-table-column>
      <el-table-column
        prop="totalReceivableAmount"
        label="总费用"
        width="160"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="生成时间"
        width="160"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column
        prop="confirmTime"
        label="确认时间"
        width="160"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.billStatus ? 'danger' : 'success'"
            size="small"
          >
            {{ formatterStatus(scope.row.billStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)">
            查看
          </el-button>
          <el-button type="text" size="small" @click="handleEdit(scope.row)">
            下载
          </el-button>
          <el-button type="text" size="small" @click="handleEdit(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>

    <el-dialog
      title="生成账单"
      :visible.sync="dialogVisible"
      width="560px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item prop="contractId" label="服务合同">
          <ServiceContractsSelector v-model="ruleForm.contractId" />
        </el-form-item>
        <el-form-item prop="billMonth" label="账单月">
          <el-date-picker
            style="width: 100%"
            v-model="ruleForm.billMonth"
            type="month"
            placeholder="请选择账单月"
            value-format="yyyy-MM-01"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ServiceContractsSelector from './selector/serviceContracts.vue'
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    ServiceContractsSelector
  },
  data() {
    return {
      fullShown: false,
      conditions: {
        offset: 0,
        limit: 10,
        // sorts: [
        //   {
        //     field: '',
        //     direction: ''
        //   }
        // ],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          // id: 0,
          // supplierCorporationId: '',
          // customerId: '',
          // contractId: '',
          // billMonthStart: '',
          // billMonthEnd: '',
          // billStatus: ''
        }
      },
      total: 0,
      data: [],
      ruleForm: {
        contractId: '',
        billMonth: ''
      },
      rules: {
        contractId: [
          { required: true, message: '请选择服务合同', trigger: 'change' }
        ],
        billMonth: [
          { required: true, message: '请选择账单月', trigger: 'change' }
        ]
      },
      contracts: [],
      loading: true,
      dialogVisible: false
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    formatterStatus(value) {
      switch (value) {
        case 'GENERATING':
          return '服务中'
        case 'GENERATED':
          return '提前终止'
        case 'PENDING_CONFIRM':
          return '已到期'
        case 'CONFIRMED':
          return '已到期'
        case 'INVOICED':
          return '已到期'
        case 'PAID':
          return '已到期'
        case 'CLOSED':
          return '已到期'
        default:
          return '-'
      }
    },
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.fullShown = false
      this.onSearch()
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.apiSupplierBillsList({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.conditions.filters.billMonthStart = value[0]
        this.conditions.filters.billMonthEnd = value[1]
      } else {
        this.conditions.filters.billMonthStart = null
        this.conditions.filters.billMonthEnd = null
      }
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    handleAdd() {
      this.dialogVisible = true
    },

    handleView(row) {
      this.$router.push(`/corporations/${row.id}`)
    },

    handleEdit(row) {
      this.$router.push(`/corporations/${row.id}/edit`)
    },

    async confirm() {
      await this.$refs.ruleForm.validate()
      const [err, r] = await client.apiSupplierBillsGenerate({
        body: this.ruleForm
      })
      if (err) {
        handleError(err)
        return
      }
      this.$message.success('账单生成成功')
      this.dialogVisible = false
      this.getList()
    }
  }
}
</script>
