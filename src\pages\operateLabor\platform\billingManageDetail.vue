<template>
  <div class="billingManageDetail">
    <div style="display: flex; margin: 20px 0">
      <div
        style="
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100px;
          height: 100px;
          background: #4f71ff;
          color: #fff;
          border-radius: 10px;
        "
      >
        <span>09 / 22</span>
        <span>账单月份</span>
      </div>
      <div
        style="
          display: flex;
          flex-direction: column;
          margin-left: 20px;
          justify-content: space-around;
        "
      >
        <span>广东东方旅文文化旅游集团有限公司</span>
        <span>账套名称：广东东方旅文文化旅游集团有限公司结算账套01</span>
        <span>所属服务合同：服务合同一 </span>
      </div>
    </div>
    <el-table
      size="small"
      :data="tableData"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="结算项目"
        min-width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="billMonth"
        label="结算月份"
        min-width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="personCount"
        label="办理人数"
        min-width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="totalAmount"
        label="应收金额"
        min-width="160"
      ></el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)">
            查看
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleDownload(scope.row)"
          >
            下载
          </el-button>
          <el-button type="text" size="small" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      info: {
        billMonth: '',
        customerName: '',
        contractName: ''
      },
      tableData: [],
      total: 0,
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          id: ''
        }
      }
    }
  },
  computed: {
    billId() {
      return this.$route.params.id
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      const [err, r] = await client.apiSupplierBillsBillId(this.billId)
      if (err) {
        handleError(err)
        return
      }
      this.info.billMonth = r.billMonth
      this.info.customerName = r.customerName
      this.info.contractName = r.contractName
      this.tableData = r.data.categories || []
      this.total = r.total
    },
    handleView(row) {
      const { id, feeType } = row
      if(feeType === 'SALARY') {
        this.$router.push('/billingManage/salary')
      }else if(feeType === 'MANAGEMENT_FEE') {
        this.$router.push('/billingManage/managementFee')
      }else {
        this.$router.push('/billingManage/otherFee')
      }
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该模板, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            const [err] = await client.updateTemplateStatus({
              body: {
                tempId: row.tempId,
                status: 'DELETED'
              }
            })

            if (err) {
              handleError(err)
              return
            }

            handleSuccess('删除成功')
            // 刷新列表
            await this.getList()
          } catch (error) {
            handleError(error)
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>

<style scoped></style>
