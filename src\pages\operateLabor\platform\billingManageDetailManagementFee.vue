<template>
  <div
    class="billingManageDetailSalary"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <div class="info" style="margin-bottom: 10px">
      <span>结算项目：{{ info.item }}</span>
      <span>结算月份：{{ info.billMonth }}</span>
      <span>所属服务合同：{{ info.contractName }}</span>
      <span>办理人数：{{ info.number }}</span>
      <span>应收金额：{{ info.payment }}</span>
    </div>
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="110px"
    >
      <el-input
        v-model="conditions.filters.name"
        placeholder="请输入员工姓名查询"
        style="width: 280px"
      ></el-input>
    </el-form>

    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="姓名"
        min-width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="id"
        label="工号"
        min-width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="idType"
        label="证件类型"
        min-width="120"
      ></el-table-column>
      <el-table-column
        prop="calculationRule"
        label="收费规则"
        min-width="160"
      ></el-table-column>
      <el-table-column
        prop="billMonth"
        label="收费月份"
        min-width="160"
      ></el-table-column>
      <el-table-column
        prop="managementFeeAmount"
        label="收费金额"
        min-width="160"
      ></el-table-column>
    </el-table>

    <!-- <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination> -->
  </div>
</template>

<script>
import ServiceContractsSelector from './selector/serviceContracts.vue'
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    ServiceContractsSelector
  },
  data() {
    return {
      info: {

      },
      conditions: {
        offset: 0,
        limit: 10,
        // sorts: [
        //   {
        //     field: '',
        //     direction: ''
        //   }
        // ],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          // id: 0,
          // supplierCorporationId: '',
          // customerId: '',
          // contractId: '',
          // billMonthStart: '',
          // billMonthEnd: '',
          // billStatus: ''
        }
      },
      total: 0,
      loading: false,
    }
  },
  async created() {
    // await this.getList()
  },
  methods: {
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.fullShown = false
      this.onSearch()
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.apiSupplierBillsManagementFeeDetails({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.conditions.filters.billMonthStart = value[0]
        this.conditions.filters.billMonthEnd = value[1]
      } else {
        this.conditions.filters.billMonthStart = null
        this.conditions.filters.billMonthEnd = null
      }
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
  }
}
</script>
<style scoped>
.info span{
  font-size: 14px;
  margin-right: 30px;
}
</style>
