<template>
  <div class="file-list-container">
    <div v-if="loading" class="loading-files">
      <i class="el-icon-loading"></i>
      <span>加载文件信息中...</span>
    </div>
    <div v-else-if="fileList.length === 0" class="no-files">暂无附件</div>
    <div v-else class="file-grid">
      <div v-for="(file, index) in fileList" :key="index" class="file-item">
        <div class="file-info">
          <i class="el-icon-document file-icon"></i>
          <span class="file-name" :title="file.name">{{ file.name }}</span>
        </div>
        <div class="file-actions">
          <el-button
            type="text"
            size="small"
            @click="downloadFile(file)"
            title="下载"
          >
            <i class="el-icon-download"></i>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import makeClient from '../../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  name: 'FileList',
  props: {
    fileIds: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileList: [],
      loading: false
    }
  },
  watch: {
    fileIds: {
      immediate: true,
      handler(newVal) {
        this.loadFileList(newVal)
      }
    }
  },
  methods: {
    async loadFileList(fileIds) {
      if (!fileIds) {
        this.fileList = []
        return
      }

      const ids = fileIds.split(',').filter(id => id.trim())
      if (ids.length === 0) {
        this.fileList = []
        return
      }

      this.loading = true
      this.fileList = []

      try {
        // 批量并行获取所有文件信息
        const fileInfoPromises = ids.map(async id => {
          try {
            const [err, response] = await client.describeFile({
              body: { id: id.trim() }
            })

            if (response && response.success && response.data) {
              return {
                id: id.trim(),
                name: response.data.name || `文件${id}`,
                url: `${
                  window.env?.apiPath
                }/api/public/downloadFile/${id.trim()}`
              }
            } else {
              // 如果获取文件信息失败，仍然添加到列表中，但使用默认名称
              return {
                id: id.trim(),
                name: `文件${id}`,
                url: `${
                  window.env?.apiPath
                }/api/public/downloadFile/${id.trim()}`
              }
            }
          } catch (error) {
            console.error('获取文件信息失败：', error)
            // 即使出错也添加到列表中
            return {
              id: id.trim(),
              name: `文件${id}`,
              url: `${window.env?.apiPath}/api/public/downloadFile/${id.trim()}`
            }
          }
        })

        // 等待所有文件信息获取完成
        const fileInfos = await Promise.all(fileInfoPromises)

        // 一次性设置文件列表
        this.fileList = fileInfos.filter(info => info !== null)
      } catch (error) {
        console.error('加载文件列表失败：', error)
        this.$message.error('加载文件列表失败')
      } finally {
        this.loading = false
      }
    },

    downloadFile(file) {
      try {
        const link = document.createElement('a')
        link.href = file.url
        link.download = file.name
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } catch (error) {
        console.error('下载文件失败：', error)
        this.$message.error('下载文件失败')
      }
    }
  }
}
</script>

<style scoped>
.file-list-container {
  width: 100%;
}

.loading-files {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.loading-files i {
  margin-right: 8px;
  font-size: 16px;
}

.no-files {
  color: #999;
  font-size: 14px;
}

/* 网格布局：一排两个 */
.file-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #f7fafd;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  transition: all 0.2s ease;
}

.file-item:hover {
  background: #ecf5ff;
  border-color: #b3d8ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.file-icon {
  color: #409eff;
  font-size: 18px;
  margin-right: 10px;
  flex-shrink: 0;
}

.file-name {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.file-actions {
  flex-shrink: 0;
  margin-left: 12px;
}

.file-actions .el-button {
  color: #409eff;
  padding: 6px;
  font-size: 16px;
  transition: all 0.2s ease;
}

.file-actions .el-button:hover {
  color: #66b1ff;
  transform: scale(1.1);
}

/* 响应式：小屏幕时改为单列 */
@media (max-width: 768px) {
  .file-grid {
    grid-template-columns: 1fr;
  }
}
</style>
