<template>
  <div class="surtax-component">
    <el-table
      :data="surtaxData"
      style="width: 100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column label="税种名称">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.name"
            placeholder="请输入税种名称"
            @change="updateModel"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="税率 (%)">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.rate"
            :min="0"
            :max="100"
            :precision="2"
            placeholder="请输入税率"
            style="width: 100%"
            @change="updateModel"
          ></el-input-number>
        </template>
      </el-table-column>
      <el-table-column label="优惠比例 (%)">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.discount_rate"
            :min="0"
            :max="100"
            :precision="2"
            placeholder="请输入优惠比例"
            style="width: 100%"
            @change="updateModel"
          ></el-input-number>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="deleteRow(scope.$index)"
            :disabled="surtaxData.length <= 1"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-button
      type="primary"
      plain
      icon="el-icon-plus"
      @click="addRow"
      style="margin-top: 10px; width: 100%"
    >
      新增一栏
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'SurtaxEditor',
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      surtaxData: []
    }
  },
  created() {
    this.initializeData()
  },
  methods: {
    initializeData() {
      if (this.value && this.value.length > 0) {
        // Use a deep copy to prevent modifying the prop directly
        this.surtaxData = JSON.parse(JSON.stringify(this.value))
      } else {
        // Default to 3 empty rows
        this.surtaxData = [
          { name: '', rate: 0, discount_rate: 0 },
          { name: '', rate: 0, discount_rate: 0 },
          { name: '', rate: 0, discount_rate: 0 }
        ]
      }
      this.updateModel() // Emit initial state
    },
    updateModel() {
      this.$emit('input', this.surtaxData)
    },
    addRow() {
      this.surtaxData.push({ name: '', rate: 0, discount_rate: 0 })
      this.updateModel()
    },
    deleteRow(index) {
      if (this.surtaxData.length > 1) {
        this.surtaxData.splice(index, 1)
        this.updateModel()
      }
    }
  }
}
</script>
