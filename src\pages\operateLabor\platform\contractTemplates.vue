<template>
  <div
    class="contract-templates"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px;
        border-radius: 5px;
        display: flex;
        justify-content: space-between;
      "
      label-position="right"
      label-width="110px"
    >
      <div style="display: flex; gap: 20px">
        <el-form-item label="模板名称" style="margin-bottom: 0">
          <el-input
            v-model="conditions.filters.tempName"
            placeholder="请输入模板名称"
            style="width: 280px"
          ></el-input>
        </el-form-item>
        <el-form-item label="模板状态" style="margin-bottom: 0">
          <el-select
            v-model="conditions.filters.tempStatus"
            placeholder="请选择模板状态"
            style="width: 200px"
            clearable
          >
            <el-option label="草稿" value="DRAFT"></el-option>
            <el-option label="错误" value="ERROR"></el-option>
            <el-option label="启用" value="ENABLED"></el-option>
            <el-option label="停用" value="DISABLED"></el-option>
          </el-select>
        </el-form-item>
      </div>
      <div>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </div>
    </el-form>

    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus" />
        新建模板
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto; min-width: 1000px"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="tempName"
        label="模板名称"
        width="250"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="状态" width="120">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.tempStatus)" size="small">
            {{ getStatusText(scope.row.tempStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="modifiedTime"
        label="更新时间"
        width="180"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="350">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            :loading="scope.row.editLoading"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button type="text" size="small" @click="handleDelete(scope.row)">
            删除
          </el-button>
          <el-button type="text" size="small" @click="handleSign(scope.row)">
            发起签署
          </el-button>
          <el-button
            v-if="scope.row.tempStatus === 'DISABLED'"
            type="text"
            size="small"
            :loading="scope.row.statusLoading"
            @click="handleToggleStatus(scope.row, 'ENABLED')"
          >
            启用
          </el-button>
          <el-button
            v-if="scope.row.tempStatus === 'ENABLED'"
            type="text"
            size="small"
            :loading="scope.row.statusLoading"
            @click="handleToggleStatus(scope.row, 'DISABLED')"
          >
            停用
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'ContractTemplates',
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: false,
        filters: {
          tempName: '',
          tempStatus: ''
        }
      },
      total: 0,
      data: [],
      loading: true
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions.filters.tempName = ''
      this.conditions.filters.tempStatus = ''
      this.onSearch()
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.getTemplateList({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      // 为每个项目添加loading状态
      this.data = (r.data.list || []).map(item => ({
        ...item,
        editLoading: false,
        statusLoading: false
      }))
      this.total = r.data.total || 0
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    getStatusType(status) {
      const statusMap = {
        DRAFT: 'info',
        ERROR: 'danger',
        ENABLED: 'success',
        DISABLED: 'warning'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        DRAFT: '草稿',
        ERROR: '错误',
        ENABLED: '启用',
        DISABLED: '停用'
      }
      return statusMap[status] || status
    },

    handleAdd() {
      this.$router.push('/contractTemplates/new')
    },

    async handleEdit(row) {
      // 设置编辑按钮loading状态
      this.$set(row, 'editLoading', true)

      try {
        const [err] = await client.canUpdateTemplate(row.tempId)

        if (err) {
          handleError(err)
          return
        }

        // 跳转到编辑页面
        this.$router.push(`/contractTemplates/${row.tempId}/edit`)
      } catch (error) {
        handleError(error)
      } finally {
        this.$set(row, 'editLoading', false)
      }
    },

    handleDelete(row) {
      // TODO: 实现删除功能
      console.log('删除模板:', row)
    },

    handleSign(row) {
      // TODO: 实现发起签署功能
      console.log('发起签署:', row)
    },

    async handleToggleStatus(row, newStatus) {
      this.$set(row, 'statusLoading', true)

      try {
        const [err] = await client.updateTemplateStatus({
          body: {
            tempId: row.tempId,
            status: newStatus
          }
        })

        if (err) {
          handleError(err)
          return
        }

        handleSuccess(newStatus === 'ENABLED' ? '启用成功' : '停用成功')
        // 刷新列表
        await this.getList()
      } catch (error) {
        handleError(error)
      } finally {
        this.$set(row, 'statusLoading', false)
      }
    }
  }
}
</script>

<style scoped>
.status-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}
</style>
