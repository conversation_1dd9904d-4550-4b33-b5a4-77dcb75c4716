// 合同模板新建/编辑页面测试文件
import ContractTemplatesNew from './contractTemplatesNew.vue'
import Step1 from './contractTemplatesNew/step1.vue'
import Step2 from './contractTemplatesNew/step2.vue'
import CorporationsSelector from './selector/corporations.vue'

describe('ContractTemplatesNew', () => {
  test('主组件能正常创建', () => {
    expect(ContractTemplatesNew).toBeDefined()
    expect(ContractTemplatesNew.name).toBe('ContractTemplatesNew')
  })

  test('包含必要的组件', () => {
    const components = ContractTemplatesNew.components
    expect(components.Step1).toBeDefined()
    expect(components.Step2).toBeDefined()
  })

  test('包含必要的数据属性', () => {
    const component = new ContractTemplatesNew()
    const data = component.$options.data()
    
    expect(data.currentStep).toBe(0)
    expect(data.isEdit).toBe(false)
    expect(data.templateId).toBe(null)
    expect(data.step1Data).toBeDefined()
    expect(data.step1Data.steps).toHaveLength(2)
  })
})

describe('ContractTemplateStep1', () => {
  test('Step1 组件能正常创建', () => {
    expect(Step1).toBeDefined()
    expect(Step1.name).toBe('ContractTemplateStep1')
  })

  test('包含必要的表单字段', () => {
    const component = new Step1()
    const data = component.$options.data()
    
    expect(data.formData.archiveId).toBe('')
    expect(data.formData.templateName).toBe('')
    expect(data.formData.templateType).toBe('')
    expect(data.formData.corporationIds).toEqual([])
  })

  test('包含表单验证规则', () => {
    const component = new Step1()
    const data = component.$options.data()
    
    expect(data.formRules.archiveId).toBeDefined()
    expect(data.formRules.templateName).toBeDefined()
    expect(data.formRules.templateType).toBeDefined()
    expect(data.formRules.corporationIds).toBeDefined()
  })
})

describe('ContractTemplateStep2', () => {
  test('Step2 组件能正常创建', () => {
    expect(Step2).toBeDefined()
    expect(Step2.name).toBe('ContractTemplateStep2')
  })

  test('包含必要的 props', () => {
    const props = Step2.props
    expect(props.succeed).toBeDefined()
    expect(props.failed).toBeDefined()
    expect(props.reason).toBeDefined()
    expect(props.isEdit).toBeDefined()
    expect(props.resultData).toBeDefined()
  })
})

describe('CorporationsSelector', () => {
  test('选择器组件能正常创建', () => {
    expect(CorporationsSelector).toBeDefined()
    expect(CorporationsSelector.name).toBe('CorporationsSelector')
  })

  test('包含必要的 props', () => {
    const props = CorporationsSelector.props
    expect(props.value).toBeDefined()
    expect(props.multiple).toBeDefined()
    expect(props.placeholder).toBeDefined()
  })
})
