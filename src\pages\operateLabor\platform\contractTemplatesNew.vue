<template>
  <div class="contract-templates-new">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>{{ isEdit ? '编辑合同模板' : '新建合同模板' }}</h2>
    </div>

    <!-- 步骤条 -->
    <div class="steps-container">
      <el-steps :active="currentStep" finish-status="success">
        <el-step title="基本信息"></el-step>
        <el-step title="完成创建"></el-step>
      </el-steps>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 第一步：基本信息 -->
      <step1
        v-if="currentStep === 0"
        v-model="step1Data"
        :is-edit="isEdit"
        :template-id="templateId"
        @next="handleStep1Next"
        @cancel="cancel"
      />

      <!-- 第二步：结果页面 -->
      <step2
        v-if="currentStep === 1"
        :succeed="submitSucceed"
        :failed="submitFailed"
        :reason="errorMessage"
        :is-edit="isEdit"
        :result-data="resultData"
        @go-to-list="goToList"
        @create-another="createAnother"
        @retry="retrySubmit"
      />
    </div>
  </div>
</template>

<script>
import Step1 from './contractTemplatesNew/step1.vue'
import Step2 from './contractTemplatesNew/step2.vue'

export default {
  name: 'ContractTemplatesNew',
  components: {
    Step1,
    Step2
  },

  data() {
    return {
      currentStep: 0,
      isEdit: false,
      templateId: null,
      step1Data: {
        archiveId: '',
        tempId: 0,
        templateName: '',
        templateType: '',
        corporationIds: [],
        steps: [
          {
            stepId: 0,
            operate: "SIGN",
            stepName: "个人签署方",
            compEmpName: "",
            compEmpId: "",
            filedList: [],
            radioVal: "2",
            signIndex: 0,
            sortBy: 0
          },
          {
            stepId: 0,
            operate: "SEAL",
            stepName: "企业签署方",
            compEmpName: "",
            compEmpId: "",
            filedList: [],
            radioVal: "1",
            sortBy: 1
          }
        ]
      },
      submitSucceed: false,
      submitFailed: false,
      resultData: null,
      errorMessage: ''
    }
  },

  async created() {
    // 检查是否为编辑模式
    if (this.$route.params.id) {
      this.isEdit = true
      this.templateId = parseInt(this.$route.params.id)
    }
  },

  methods: {
    handleStep1Next(templateData) {
      // 从 step1 接收模板数据
      this.resultData = templateData
      this.nextStep()
    },

    nextStep() {
      if (this.currentStep < 1) {
        this.currentStep++
      }
    },

    cancel() {
      this.$router.push('/contractTemplates')
    },

    goToList() {
      this.$router.push('/contractTemplates')
    },

    createAnother() {
      // 重置所有数据，回到第一步
      this.currentStep = 0
      this.step1Data = {
        archiveId: '',
        tempId: 0,
        templateName: '',
        templateType: '',
        corporationIds: [],
        steps: [
          {
            stepId: 0,
            operate: "SIGN",
            stepName: "个人签署方",
            compEmpName: "",
            compEmpId: "",
            filedList: [],
            radioVal: "2",
            signIndex: 0,
            sortBy: 0
          },
          {
            stepId: 0,
            operate: "SEAL",
            stepName: "企业签署方",
            compEmpName: "",
            compEmpId: "",
            filedList: [],
            radioVal: "1",
            sortBy: 1
          }
        ]
      }
      this.submitSucceed = false
      this.submitFailed = false
      this.resultData = null
      this.errorMessage = ''
      this.templateId = null
      this.isEdit = false
    },

    retrySubmit() {
      // 重新提交 - 回到第一步重新操作
      this.currentStep = 0
      this.submitSucceed = false
      this.submitFailed = false
    }
  }
}
</script>

<style scoped>
.contract-templates-new {
  padding: 20px;
  background: #fff;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.steps-container {
  margin-bottom: 40px;
  padding: 0 50px;
}

.step-content {
  background: #fff;
  border-radius: 6px;
  min-height: 500px;
}

::v-deep .el-steps {
  margin-bottom: 30px;
}

::v-deep .el-step__title {
  font-size: 14px;
  font-weight: 600;
}
</style>
