<template>
  <div class="step1-container">
    <el-form
      ref="templateForm"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      style="max-width: 600px; margin: 0 auto"
    >
      <el-form-item label="模板文件" prop="archiveId" required>
        <file-uploader
          v-model="formData.archiveId"
          :max-count="1"
          accept=".pdf,.doc,.docx"
          placeholder="请上传合同模板文件"
        />
        <div class="form-tip">
          支持 PDF、Word 格式文件，文件大小不超过 10MB
        </div>
      </el-form-item>

      <el-form-item label="模板名称" prop="templateName" required>
        <el-input
          v-model="formData.templateName"
          placeholder="请输入模板名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="模板类型" prop="templateType" required>
        <el-select
          v-model="formData.templateType"
          placeholder="请选择模板类型"
          style="width: 100%"
        >
          <el-option label="劳务合同" value="LABOR_CONTRACT"></el-option>
          <el-option label="服务合同" value="SERVICE_CONTRACT"></el-option>
          <el-option label="保密协议" value="CONFIDENTIALITY_AGREEMENT"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="作业主体" prop="corporationIds" required>
        <div style="display: flex; gap: 10px; align-items: center">
          <corporations-selector
            v-model="formData.corporationIds"
            multiple
            style="flex: 1"
            placeholder="请选择作业主体"
          />
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAddCorporation"
          >
            添加
          </el-button>
        </div>
        <div class="form-tip">
          可选择多个作业主体，点击"添加"按钮可快速创建新的作业主体
        </div>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        :loading="submitting"
        @click="handleNext"
      >
        下一步
      </el-button>
    </div>
  </div>
</template>

<script>
import FileUploader from '../uploader/file.vue'
import CorporationsSelector from '../selector/corporations.vue'
import makeClient from '../../../../services/operateLabor/makeClient'
import handleError from '../../../../helpers/handleError'
import handleSuccess from '../../../../helpers/handleSuccess'

const client = makeClient()

export default {
  name: 'ContractTemplateStep1',
  components: {
    FileUploader,
    CorporationsSelector
  },
  
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    templateId: {
      type: Number,
      default: null
    }
  },

  data() {
    return {
      formData: {
        archiveId: '',
        tempId: 0,
        templateName: '',
        templateType: '',
        corporationIds: [],
        steps: [
          {
            stepId: 0,
            operate: "SIGN",
            stepName: "个人签署方",
            compEmpName: "",
            compEmpId: "",
            filedList: [],
            radioVal: "2",
            signIndex: 0,
            sortBy: 0
          },
          {
            stepId: 0,
            operate: "SEAL",
            stepName: "企业签署方",
            compEmpName: "",
            compEmpId: "",
            filedList: [],
            radioVal: "1",
            sortBy: 1
          }
        ]
      },
      formRules: {
        archiveId: [
          { required: true, message: '请上传模板文件', trigger: 'change' }
        ],
        templateName: [
          { required: true, message: '请输入模板名称', trigger: 'blur' },
          { min: 2, max: 50, message: '模板名称长度为2-50个字符', trigger: 'blur' }
        ],
        templateType: [
          { required: true, message: '请选择模板类型', trigger: 'change' }
        ],
        corporationIds: [
          { required: true, message: '请选择作业主体', trigger: 'change' },
          { type: 'array', min: 1, message: '至少选择一个作业主体', trigger: 'change' }
        ]
      },
      submitting: false
    }
  },

  watch: {
    value: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.formData = { ...this.formData, ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
    formData: {
      handler(newVal) {
        this.$emit('input', newVal)
      },
      deep: true
    }
  },

  async created() {
    if (this.isEdit && this.templateId) {
      await this.loadTemplateDetail()
    }
  },

  methods: {
    async loadTemplateDetail() {
      try {
        const [err, response] = await client.getTemplateDetail(this.templateId)
        
        if (err) {
          handleError(err)
          return
        }

        const templateData = response.data
        this.formData = {
          ...this.formData,
          tempId: templateData.id,
          templateName: templateData.name,
          templateType: templateData.type,
          corporationIds: templateData.subjects?.map(s => s.corporation) || [],
          steps: templateData.steps || this.formData.steps
        }
      } catch (error) {
        handleError(error)
      }
    },

    handleAddCorporation() {
      // 跳转到新建作业主体页面
      this.$router.push('/corporations/new')
    },

    handleCancel() {
      this.$emit('cancel')
    },

    async handleNext() {
      this.$refs.templateForm.validate(async (valid) => {
        if (!valid) return

        this.submitting = true
        
        try {
          const submitData = {
            ...this.formData,
            tempId: this.isEdit ? this.templateId : 0
          }

          const [err, response] = await client.createTemplate({
            body: submitData
          })

          if (err) {
            handleError(err)
            return
          }

          handleSuccess(this.isEdit ? '模板更新成功' : '模板创建成功')
          
          // 传递结果数据到父组件
          this.$emit('next', {
            id: response.data?.id || this.templateId,
            name: this.formData.templateName,
            type: this.formData.templateType,
            createTime: new Date().toISOString()
          })

        } catch (error) {
          handleError(error)
        } finally {
          this.submitting = false
        }
      })
    }
  }
}
</script>

<style scoped>
.step1-container {
  padding: 40px 20px;
}

.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.form-actions {
  margin-top: 40px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 100px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
}

::v-deep .el-form-item {
  margin-bottom: 24px;
}
</style>
