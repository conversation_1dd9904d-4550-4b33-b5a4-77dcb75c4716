<template>
  <div class="corporations-new">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>{{ isEdit ? '编辑作业主体' : '新建作业主体' }}</h2>
    </div>

    <!-- 步骤条 -->
    <div class="steps-container">
      <el-steps :active="currentStep" finish-status="success">
        <el-step title="基本信息"></el-step>
        <el-step title="业务配置"></el-step>
        <el-step title="完成创建"></el-step>
      </el-steps>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 第一步：基本信息 -->
      <step1
        v-if="currentStep === 0"
        v-model="step1Data"
        :is-edit="isEdit"
        :corporation-id="corporationId"
        @next="nextStep"
        @cancel="cancel"
      />

      <!-- 第二步：业务配置 -->
      <step2
        v-if="currentStep === 1"
        v-model="step2Data"
        :is-edit="isEdit"
        :corporation-id="corporationId"
        @prev="prevStep"
        @next="nextStep"
      />

      <!-- 第三步：结果页面 -->
      <step3
        v-if="currentStep === 2"
        :succeed="submitSucceed"
        :failed="submitFailed"
        :reason="errorMessage"
        :is-edit="isEdit"
        :result-data="resultData"
        @go-to-list="goToList"
        @create-another="createAnother"
        @retry="retrySubmit"
      />
    </div>
  </div>
</template>

<script>
import Step1 from './corporationsNew/step1.vue'
import Step2 from './corporationsNew/step2.vue'
import Step3 from './corporationsNew/step3.vue'
import handleError from '../../../helpers/handleError'

import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  name: 'CorporationsNew',
  components: {
    Step1,
    Step2,
    Step3
  },

  data() {
    return {
      currentStep: 0,
      isEdit: false,
      corporationId: null,
      step1Data: {},
      step2Data: {
        configData: {
          minAgeLimit: 17,
          maxAgeLimit: 66,
          invoiceCategory: '',
          vatStart: 0,
          vatRate: 0,
          surtaxData: '',
          surtax: [],
          invoiceCategoryList: []
        },
        payChannelDataList: []
      },
      submitSucceed: false,
      submitFailed: false,
      resultData: null,
      errorMessage: ''
    }
  },

  async created() {
    // 检查是否为编辑模式
    if (this.$route.params.id) {
      this.isEdit = true
      this.corporationId = parseInt(this.$route.params.id)
    }
  },

  methods: {
    async nextStep() {
      if (this.currentStep === 1) {
        // 从第二步进入第三步时，开始提交数据
        this.currentStep = 2
        this.submitSucceed = false
        this.submitFailed = false
        await this.handleSubmit()
      } else if (this.currentStep < 1) {
        this.currentStep++
      }
    },

    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },

    cancel() {
      this.$router.push('/corporations')
    },

    async handleSubmit() {
      try {
        // 合并所有步骤的数据
        const submitData = {
          basicData: {
            ...this.step1Data,
            disabled: false
          },
          configData: this.step2Data.configData,
          payChannelDataList: this.step2Data.payChannelDataList || []
        }

        if (this.isEdit) {
          await this.updateCorporation(submitData)
        } else {
          await this.createCorporation(submitData)
        }
      } catch (error) {
        console.error('提交失败:', error)
        this.submitFailed = true
        this.submitSucceed = false
        this.errorMessage = error.message || '提交失败，请重试'
      }
    },

    async createCorporation(submitData) {
      // 第一步：创建作业主体基本信息
      const [err1, response1] = await client.addCorporation({
        body: submitData.basicData
      })

      if (err1) {
        throw new Error(err1.message || '创建作业主体失败')
      }

      const corporationId = response1.data.id
      this.resultData = {
        id: corporationId,
        name: submitData.basicData.name,
        createTime: new Date().toISOString()
      }

      // 如果有配置数据，继续提交第二步
      if (submitData.configData || submitData.payChannelDataList.length > 0) {
        const [err2] = await client.editCorporationBusiness({
          body: {
            corporationId,
            configData: submitData.configData,
            payChannelDataList: submitData.payChannelDataList
          }
        })

        if (err2) {
          console.error('业务配置保存失败:', err2)
          // 不抛出错误，因为主体已创建成功
        }
      }

      this.submitSucceed = true
      this.submitFailed = false
    },

    async updateCorporation(submitData) {
      // 第一步：更新作业主体基本信息
      const [err1] = await client.editCorporation({
        body: {
          ...submitData.basicData,
          id: this.corporationId
        }
      })

      if (err1) {
        throw new Error(err1.message || '更新作业主体失败')
      }

      // 第二步：更新业务配置
      const [err2] = await client.editCorporationBusiness({
        body: {
          corporationId: this.corporationId,
          configData: submitData.configData,
          payChannelDataList: submitData.payChannelDataList
        }
      })

      if (err2) {
        throw new Error(err2.message || '更新业务配置失败')
      }

      this.resultData = {
        id: this.corporationId,
        name: submitData.basicData.name,
        createTime: new Date().toISOString()
      }

      this.submitSucceed = true
      this.submitFailed = false
    },

    goToList() {
      this.$router.push('/corporations')
    },

    createAnother() {
      // 重置所有数据，回到第一步
      this.currentStep = 0
      this.step1Data = {}
      this.step2Data = {
        configData: {
          minAgeLimit: 17,
          maxAgeLimit: 66,
          invoiceCategory: '',
          vatStart: 0,
          vatRate: 0,
          surtaxData: '',
          surtax: [],
          invoiceCategoryList: []
        },
        payChannelDataList: []
      }
      this.submitSucceed = false
      this.submitFailed = false
      this.resultData = null
      this.errorMessage = ''
    },

    retrySubmit() {
      // 重新提交
      this.submitSucceed = false
      this.submitFailed = false
      this.handleSubmit()
    }
  }
}
</script>

<style scoped>
.corporations-new {
  padding: 20px;
  background: #fff;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.steps-container {
  margin-bottom: 40px;
  padding: 0 50px;
}

.step-content {
  background: #fff;
  border-radius: 6px;
  min-height: 500px;
}

::v-deep .el-steps {
  margin-bottom: 30px;
}

::v-deep .el-step__title {
  font-size: 14px;
  font-weight: 600;
}
</style>
