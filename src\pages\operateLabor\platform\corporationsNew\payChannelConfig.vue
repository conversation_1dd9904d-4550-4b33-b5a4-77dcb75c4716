<template>
  <div class="pay-channel-config">
    <div v-loading="loadingChannels" class="config-section">
      <el-select
        v-model="selectedChannelCode"
        placeholder="请选择支付通道"
        @change="handleChannelChange"
        style="width: 400px"
      >
        <el-option
          v-for="channel in channels"
          :key="channel.payChannel"
          :label="channel.channelName"
          :value="channel.payChannel"
        >
        </el-option>
      </el-select>
    </div>

    <div v-if="selectedChannel" class="config-section">
      <el-form
        ref="form"
        :model="formValues"
        :rules="formRules"
        label-width="180px"
        class="dynamic-form"
      >
        <el-form-item
          v-for="param in selectedChannel.payChannelConfig"
          :key="param.paramName"
          :label="param.paramDesc"
          :prop="param.paramName"
        >
          <el-input
            v-model="formValues[param.paramName]"
            :placeholder="param.paramDesc"
          ></el-input>
        </el-form-item>

        <el-form-item label="是否启用通道">
          <el-switch v-model="isOpen"></el-switch>
        </el-form-item>
      </el-form>
    </div>

    <div class="actions">
      <el-button
        type="primary"
        :loading="submitting"
        @click="submit"
        :disabled="!selectedChannel"
      >
        提交配置
      </el-button>
    </div>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  name: 'PayChannelConfig',
  props: {
    corporationId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      loadingChannels: false,
      submitting: false,
      channels: [],
      selectedChannelCode: '',
      formValues: {},
      formRules: {},
      isOpen: true
    }
  },
  computed: {
    selectedChannel() {
      if (!this.selectedChannelCode) {
        return null
      }
      return this.channels.find(c => c.payChannel === this.selectedChannelCode)
    }
  },
  async created() {
    await this.fetchChannels()
  },
  methods: {
    async fetchChannels() {
      this.loadingChannels = true
      const [err, res] = await client.supplierPayChannelList()
      this.loadingChannels = false
      if (err) {
        handleError(err)
        return
      }
      this.channels = res.data || []
    },
    handleChannelChange() {
      // Reset form values and rules when channel changes
      const newValues = {}
      const newRules = {}
      if (this.selectedChannel && this.selectedChannel.payChannelConfig) {
        this.selectedChannel.payChannelConfig.forEach(param => {
          this.$set(newValues, param.paramName, '') // Use $set for reactivity
          if (param.required) {
            newRules[param.paramName] = [
              {
                required: true,
                message: `请输入 ${param.paramDesc}`,
                trigger: 'blur'
              }
            ]
          }
        })
      }
      this.formValues = newValues
      this.formRules = newRules
    },
    async submit() {
      if (!this.selectedChannel) {
        this.$message.error('请先选择一个支付通道')
        return
      }

      this.$refs.form.validate(async valid => {
        if (valid) {
          this.submitting = true
          const payload = {
            corporationId: this.corporationId,
            id: this.corporationId,
            payChannel: this.selectedChannel.payChannel,
            channelConfig: this.formValues,
            open: this.isOpen,
            default: true
          }

          const [err] = await client.supplierCorporationConfigPayChannel({
            body: payload
          })
          this.submitting = false

          if (err) {
            handleError(err)
            return
          }
          this.$message.success('通道配置成功！')
          this.$emit('success')
        } else {
          this.$message.error('请检查表单必填项')
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
.pay-channel-config {
  padding: 20px;
}
.config-section {
  margin-bottom: 30px;
}
.dynamic-form {
  max-width: 600px;
  padding-top: 20px;
}
.actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
