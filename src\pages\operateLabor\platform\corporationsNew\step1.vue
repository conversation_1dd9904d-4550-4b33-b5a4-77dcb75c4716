<template>
  <div class="step1">
    <!-- 基本信息 -->
    <div class="section">
      <h3 class="section-title">基本信息</h3>
      
      <!-- 营业执照上传 -->
      <div class="form-item">
        <label class="required">营业执照照片</label>
        <span class="tip">请上传加盖公章的营业执照扫件或复印件</span>
        
        <div class="upload-area">
          <uploader-image
            v-model="formData.businessLicenseImage"
            :width="200"
            :height="280"
            accept=".jpg,.jpeg,.png,.pdf"
            :max-size="10"
          />
          <div class="upload-text">营业执照上传</div>
          <el-button type="text" @click="showExample('business')">查看示例</el-button>
        </div>
      </div>
      
      <!-- 公司名称 -->
      <div class="form-item">
        <label class="required">公司名称</label>
        <el-input
          v-model="formData.name"
          placeholder="测试企业1111"
          style="width: 400px"
        />
      </div>
      
      <!-- 统一社会信用代码 -->
      <div class="form-item">
        <label class="required">统一社会信用代码</label>
        <el-input
          v-model="formData.socialCreditCode"
          placeholder="请填写统一社会信用代码"
          style="width: 400px"
        />
      </div>
      
      <!-- 公司注册地址 -->
      <div class="form-item">
        <label class="required">公司注册地址</label>
        <el-input
          v-model="formData.registerAddress"
          type="textarea"
          :rows="3"
          placeholder="请填写公司注册地址"
          style="width: 400px"
        />
      </div>
    </div>
    
    <!-- 法人信息 -->
    <div class="section">
      <h3 class="section-title">法人信息</h3>
      
      <!-- 法人身份照片 -->
      <div class="form-item">
        <label class="required">法人身份照片</label>
        <span class="tip">请上传加盖公章的法人身份证扫件或复印件</span>
        
        <div class="id-card-upload">
          <div class="id-card-item">
            <uploader-image
              v-model="formData.certificateFrontImage"
              :width="200"
              :height="130"
              accept=".jpg,.jpeg,.png,.pdf"
              :max-size="10"
            />
            <div class="upload-text">人像面</div>
            <el-button type="text" @click="showExample('idFront')">查看示例</el-button>
          </div>
          
          <div class="id-card-item">
            <uploader-image
              v-model="formData.certificateBackImage"
              :width="200"
              :height="130"
              accept=".jpg,.jpeg,.png,.pdf"
              :max-size="10"
            />
            <div class="upload-text">国徽面</div>
            <el-button type="text" @click="showExample('idBack')">查看示例</el-button>
          </div>
        </div>
      </div>
      
      <!-- 法人姓名 -->
      <div class="form-item">
        <label class="required">法人姓名</label>
        <el-input
          v-model="formData.representativeName"
          placeholder="请填写法人姓名"
          style="width: 400px"
        />
      </div>
      
      <!-- 证件类型 -->
      <div class="form-item">
        <label class="required">证件类型</label>
        <el-select
          v-model="formData.certificateType"
          placeholder="请选择证件类型"
          style="width: 400px"
        >
          <el-option label="身份证" value="ID_CARD"></el-option>
          <el-option label="护照" value="PASSPORT"></el-option>
          <el-option label="港澳台证件" value="HONG_KONG_MACAO_TAIWAN"></el-option>
          <el-option label="外国人证件" value="FOREIGNER"></el-option>
        </el-select>
      </div>
      
      <!-- 法人证件号 -->
      <div class="form-item">
        <label class="required">法人证件号</label>
        <el-input
          v-model="formData.certificateNo"
          placeholder="请填写法人证件号"
          style="width: 400px"
        />
      </div>
    </div>
    
    <!-- 联系人信息 -->
    <div class="section">
      <h3 class="section-title">联系人信息</h3>
      
      <!-- 联系人姓名 -->
      <div class="form-item">
        <label class="required">联系人姓名</label>
        <el-input
          v-model="formData.contactName"
          placeholder="赵晓霞"
          style="width: 400px"
        />
      </div>
      
      <!-- 联系人手机号 -->
      <div class="form-item">
        <label class="required">联系人手机号</label>
        <el-input
          v-model="formData.contactMobile"
          placeholder="13233692181"
          style="width: 400px"
        />
      </div>
    </div>
    
    <!-- 操作角色配置 -->
    <div class="section">
      <h3 class="section-title">操作角色配置</h3>
      
      <div class="role-config">
        <el-select
          v-model="selectedRoles"
          multiple
          placeholder="请选择角色"
          style="width: 300px"
        >
          <el-option
            v-for="role in availableRoles"
            :key="role.id"
            :label="role.name"
            :value="role.id"
          ></el-option>
        </el-select>
        <el-button type="primary" @click="addRole">新增</el-button>
      </div>
      
      <div class="role-list">
        <el-tag
          v-for="roleId in formData.roleIds"
          :key="roleId"
          closable
          @close="removeRole(roleId)"
          style="margin-right: 10px; margin-bottom: 10px"
        >
          {{ getRoleName(roleId) }}
        </el-tag>
      </div>
      
      <div class="role-table-placeholder">
        <span>角色名称列表</span>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="actions">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="nextStep">下一步</el-button>
    </div>
  </div>
</template>

<script>
import UploaderImage from '../uploader/image.vue'

export default {
  name: 'CorporationStep1',
  components: {
    UploaderImage
  },
  
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      formData: {
        businessLicenseImage: '',
        name: '',
        socialCreditCode: '',
        registerAddress: '',
        certificateFrontImage: '',
        certificateBackImage: '',
        representativeName: '',
        certificateType: 'ID_CARD',
        certificateNo: '',
        contactName: '',
        contactMobile: '',
        roleIds: []
      },
      selectedRoles: [],
      availableRoles: [
        { id: 1, name: '管理员' },
        { id: 2, name: '财务' },
        { id: 3, name: '业务员' }
      ]
    }
  },
  
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.formData = { ...this.formData, ...val }
      }
    },
    formData: {
      deep: true,
      handler(val) {
        this.$emit('input', val)
      }
    }
  },
  
  methods: {
    showExample(type) {
      // TODO: 显示示例图片
      console.log('显示示例:', type)
    },
    
    addRole() {
      this.selectedRoles.forEach(roleId => {
        if (!this.formData.roleIds.includes(roleId)) {
          this.formData.roleIds.push(roleId)
        }
      })
      this.selectedRoles = []
    },
    
    removeRole(roleId) {
      const index = this.formData.roleIds.indexOf(roleId)
      if (index > -1) {
        this.formData.roleIds.splice(index, 1)
      }
    },
    
    getRoleName(roleId) {
      const role = this.availableRoles.find(r => r.id === roleId)
      return role ? role.name : ''
    },
    
    cancel() {
      this.$emit('cancel')
    },
    
    nextStep() {
      // 验证表单
      if (!this.validateForm()) {
        return
      }
      this.$emit('next')
    },
    
    validateForm() {
      const required = [
        'businessLicenseImage',
        'name',
        'socialCreditCode',
        'registerAddress',
        'certificateFrontImage',
        'certificateBackImage',
        'representativeName',
        'certificateNo',
        'contactName',
        'contactMobile'
      ]
      
      for (const field of required) {
        if (!this.formData[field]) {
          this.$message.error(`请填写${this.getFieldLabel(field)}`)
          return false
        }
      }
      
      return true
    },
    
    getFieldLabel(field) {
      const labels = {
        businessLicenseImage: '营业执照照片',
        name: '公司名称',
        socialCreditCode: '统一社会信用代码',
        registerAddress: '公司注册地址',
        certificateFrontImage: '法人身份证人像面',
        certificateBackImage: '法人身份证国徽面',
        representativeName: '法人姓名',
        certificateNo: '法人证件号',
        contactName: '联系人姓名',
        contactMobile: '联系人手机号'
      }
      return labels[field] || field
    }
  }
}
</script>

<style scoped>
.step1 {
  padding: 20px;
  max-width: 800px;
}

.section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.form-item {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
}

.form-item label {
  width: 120px;
  text-align: right;
  margin-right: 20px;
  line-height: 32px;
  color: #606266;
}

.form-item label.required::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.tip {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.upload-text {
  font-size: 14px;
  color: #606266;
}

.id-card-upload {
  display: flex;
  gap: 30px;
}

.id-card-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.role-config {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.role-list {
  margin-bottom: 15px;
}

.role-table-placeholder {
  padding: 20px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  text-align: center;
  color: #909399;
}

.actions {
  text-align: right;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.actions .el-button {
  margin-left: 10px;
}
</style>
