<template>
  <div class="step1" v-loading="loading">
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="140px"
      label-position="right"
    >
      <!-- 基本信息 -->
      <div class="section">
        <h3 class="section-title">基本信息</h3>

        <!-- 营业执照上传 -->
        <el-form-item label="营业执照照片" prop="businessLicenseImage" required>
          <UploaderImage
            v-model="formData.businessLicenseImage"
            :width="200"
            :height="280"
            accept=".jpg,.jpeg,.png,.pdf"
            :max="1"
            name="营业执照上传"
          />
          <div class="tip">
            请上传加盖公章的营业执照扫件或复印件
            <el-button type="text" @click="showExample('business')">
              查看示例
            </el-button>
          </div>
        </el-form-item>

        <!-- 公司名称 -->
        <el-form-item label="公司名称" prop="name" required>
          <el-input
            v-model="formData.name"
            placeholder="请填写公司全名"
            style="width: 400px"
          />
        </el-form-item>

        <!-- 统一社会信用代码 -->
        <el-form-item label="统一社会信用代码" prop="socialCreditCode" required>
          <el-input
            v-model="formData.socialCreditCode"
            placeholder="请填写统一社会信用代码"
            style="width: 400px"
          />
        </el-form-item>

        <!-- 公司注册地址 -->
        <el-form-item label="公司注册地址" prop="registerAddress" required>
          <el-input
            v-model="formData.registerAddress"
            type="textarea"
            :rows="3"
            placeholder="请填写公司注册地址"
            style="width: 400px"
          />
        </el-form-item>
      </div>

      <!-- 法人信息 -->
      <div class="section">
        <h3 class="section-title">法人信息</h3>

        <!-- 法人身份照片 -->
        <el-form-item label="法人身份照片" required>
          <div class="id-card-container">
            <div class="id-card-upload">
              <div class="id-card-item">
                <UploaderImage
                  v-model="formData.certificateFrontImage"
                  :width="200"
                  :height="130"
                  accept=".jpg,.jpeg,.png,.pdf"
                  :max="1"
                  name="人像面"
                />
                <el-button type="text" @click="showExample('idFront')"
                  >查看示例</el-button
                >
              </div>

              <div class="id-card-item">
                <UploaderImage
                  v-model="formData.certificateBackImage"
                  :width="200"
                  :height="130"
                  accept=".jpg,.jpeg,.png,.pdf"
                  :max="1"
                  name="国徽面"
                />
                <el-button type="text" @click="showExample('idBack')">
                  查看示例
                </el-button>
              </div>
            </div>
            <div class="tip">请上传加盖公章的法人身份证扫件或复印件</div>
          </div>
        </el-form-item>

        <!-- 法人姓名 -->
        <el-form-item label="法人姓名" prop="representativeName" required>
          <el-input
            v-model="formData.representativeName"
            placeholder="请填写法人姓名"
            style="width: 400px"
          />
        </el-form-item>

        <!-- 证件类型 -->
        <el-form-item label="证件类型" prop="certificateType" required>
          <el-select
            v-model="formData.certificateType"
            placeholder="请选择证件类型"
            style="width: 400px"
          >
            <el-option label="身份证" value="ID_CARD"></el-option>
            <el-option label="护照" value="PASSPORT"></el-option>
            <el-option
              label="港澳台证件"
              value="HONG_KONG_MACAO_TAIWAN"
            ></el-option>
            <el-option label="外国人证件" value="FOREIGNER"></el-option>
          </el-select>
        </el-form-item>

        <!-- 法人证件号 -->
        <el-form-item label="法人证件号" prop="certificateNo" required>
          <el-input
            v-model="formData.certificateNo"
            placeholder="请填写法人证件号"
            style="width: 400px"
          />
        </el-form-item>
      </div>

      <!-- 联系人信息 -->
      <div class="section">
        <h3 class="section-title">联系人信息</h3>

        <!-- 联系人姓名 -->
        <el-form-item label="联系人姓名" prop="contactName" required>
          <el-input
            v-model="formData.contactName"
            placeholder="请填写姓名"
            style="width: 400px"
          />
        </el-form-item>

        <!-- 联系人手机号 -->
        <el-form-item label="联系人手机号" prop="contactMobile" required>
          <el-input
            v-model="formData.contactMobile"
            placeholder="请填写手机号"
            style="width: 400px"
          />
        </el-form-item>
      </div>

      <!-- 操作角色配置 -->
      <div class="section">
        <h3 class="section-title">操作角色配置</h3>
        <el-form-item label="角色配置" prop="roleIds">
          <RolesSelector v-model="formData.roleIds" style="width: 400px" />
        </el-form-item>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="nextStep" :loading="loading"
          >下一步</el-button
        >
      </div>
    </el-form>
  </div>
</template>

<script>
import UploaderImage from '../uploader/image.vue'
import RolesSelector from '../selector/roles.vue'
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  name: 'CorporationStep1',
  components: {
    UploaderImage,
    RolesSelector
  },

  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    corporationId: {
      type: [String, Number],
      default: null
    }
  },

  data() {
    return {
      loading: false,
      formData: {
        businessLicenseImage: '',
        name: '',
        socialCreditCode: '',
        registerAddress: '',
        certificateFrontImage: '',
        certificateBackImage: '',
        representativeName: '',
        certificateType: 'ID_CARD',
        certificateNo: '',
        contactName: '',
        contactMobile: '',
        roleIds: []
      },
      rules: {
        businessLicenseImage: [
          { required: true, message: '请上传营业执照照片', trigger: 'blur' }
        ],
        name: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
        socialCreditCode: [
          {
            required: true,
            message: '请输入统一社会信用代码',
            trigger: 'blur'
          },
          {
            pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
            message: '请输入正确的统一社会信用代码',
            trigger: 'blur'
          }
        ],
        registerAddress: [
          { required: true, message: '请输入公司注册地址', trigger: 'blur' }
        ],
        representativeName: [
          { required: true, message: '请输入法人姓名', trigger: 'blur' }
        ],
        certificateType: [
          { required: true, message: '请选择证件类型', trigger: 'change' }
        ],
        certificateNo: [
          { required: true, message: '请输入法人证件号', trigger: 'blur' }
        ],
        contactName: [
          { required: true, message: '请输入联系人姓名', trigger: 'blur' }
        ],
        contactMobile: [
          { required: true, message: '请输入联系人手机号', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号',
            trigger: 'blur'
          }
        ]
      },
      loading: false
    }
  },

  async created() {
    if (this.isEdit && this.corporationId) {
      await this.loadCorporationData()
    }
  },

  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        // Prevent infinite loop by checking if data is already in sync.
        if (JSON.stringify(newVal) !== JSON.stringify(this.formData)) {
          this.formData = { ...this.formData, ...newVal }
        }
      }
    },
    formData: {
      deep: true,
      handler(newVal) {
        this.$emit('input', newVal)
      }
    }
  },

  methods: {
    async loadCorporationData() {
      this.loading = true

      try {
        const [err, response] = await client.corporationDetail({
          body: { id: this.corporationId }
        })

        if (err) {
          handleError(err)
          return
        }
        const data = response.data
        // 填充表单数据
        this.formData = {
          id: data.id,
          supplierId: data.supplierId,
          name: data.name,
          socialCreditCode: data.socialCreditCode,
          registerAddress: data.info?.registerAddress || '',
          businessLicenseImage: data.info?.businessLicenseImage || '',
          certificateFrontImage: data.info?.certificateFrontImage || '',
          certificateBackImage: data.info?.certificateBackImage || '',
          representativeName: data.info?.representativeName || '',
          certificateType: data.info?.certificateType || 'ID_CARD',
          certificateNo: data.info?.certificateNo || '',
          contactName: data.info?.contacts || '',
          contactMobile: data.info?.contactPhone || '',
          roleIds: data.roleData?.map(role => role.id) || []
        }
      } catch (error) {
        console.error('加载作业主体数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    showExample(type) {
      // TODO: 显示示例图片
      console.log('显示示例:', type)
    },

    cancel() {
      this.$emit('cancel')
    },

    nextStep() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          // 额外验证身份证照片
          if (!this.formData.certificateFrontImage) {
            this.$message.error('请上传法人身份证人像面')
            return
          }
          if (!this.formData.certificateBackImage) {
            this.$message.error('请上传法人身份证国徽面')
            return
          }

          this.loading = true

          if (this.isEdit && this.corporationId) {
            // 编辑模式：更新现有作业主体
            const [err] = await client.editCorporation({
              body: {
                ...this.formData,
                id: this.corporationId,
                disabled: false
              }
            })
            this.loading = false

            if (err) {
              handleError(err)
              return
            }

            this.$message.success('基本信息保存成功')
            this.$emit('next', this.corporationId)
          } else {
            // 新建模式：创建新作业主体
            const [err, response] = await client.addCorporation({
              body: this.formData
            })
            this.loading = false

            if (err) {
              handleError(err)
              return
            }

            const newId = response.data.id
            this.$message.success('作业主体创建成功')
            this.$emit('next', newId)
          }
        } else {
          this.$message.error('请完善表单信息')
        }
      })
    }
  }
}
</script>

<style scoped>
.step1 {
  padding: 20px;
  max-width: 800px;
}

.section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.upload-container {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.upload-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.upload-text {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.id-card-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.id-card-upload {
  display: flex;
  gap: 30px;
}

.id-card-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.role-config-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.role-config {
  display: flex;
  align-items: center;
  gap: 10px;
}

.role-list {
  min-height: 32px;
}

.role-table-placeholder {
  padding: 20px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.actions {
  text-align: right;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.actions .el-button {
  margin-left: 10px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
}

::v-deep .el-form-item {
  margin-bottom: 24px;
}
</style>
