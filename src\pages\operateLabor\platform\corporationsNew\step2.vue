<template>
  <div class="step2">
    <!-- 支付通道配置 -->
    <div class="section">
      <h3 class="section-title">支付通道配置</h3>
      
      <div class="pay-channel-config">
        <div class="channel-row">
          <div class="channel-item">
            <label>支付通道升通道</label>
            <el-input
              v-model="payChannelForm.channel1"
              placeholder="选择支付通道"
              style="width: 200px"
            />
          </div>
          
          <div class="channel-item">
            <label>连接默认支付通道</label>
            <el-input
              v-model="payChannelForm.defaultChannel"
              placeholder="选择支付通道"
              style="width: 200px"
            />
          </div>
        </div>
        
        <div class="channel-actions">
          <el-input
            v-model="payChannelForm.openCode"
            placeholder="输入开户代码"
            style="width: 200px"
          />
          <el-button type="text">取消</el-button>
          <el-button type="primary">确定</el-button>
        </div>
      </div>
    </div>
    
    <!-- 发票类目配置 -->
    <div class="section">
      <h3 class="section-title">发票类目配置</h3>
      
      <div class="invoice-config">
        <el-button type="text" @click="addInvoiceCategory">批量新增</el-button>
        
        <div class="invoice-tags">
          <el-tag
            v-for="(category, index) in configData.invoiceCategoryList"
            :key="index"
            closable
            @close="removeInvoiceCategory(index)"
            style="margin-right: 10px; margin-bottom: 10px"
          >
            {{ category }}
          </el-tag>
        </div>
      </div>
    </div>
    
    <!-- 增值税，增值附加税配置 -->
    <div class="section">
      <h3 class="section-title">增值税，增值附加税配置</h3>
      
      <div class="tax-config">
        <div class="tax-item">
          <label>增值税起征点</label>
          <el-input
            v-model="configData.vatStart"
            placeholder="请输入增值税起征点"
            style="width: 200px"
          />
          <span class="unit">万元</span>
        </div>
        
        <div class="tax-item">
          <label>增值税税率</label>
          <el-input
            v-model="configData.vatRate"
            placeholder="请输入增值税税率"
            style="width: 200px"
          />
          <span class="unit">%</span>
        </div>
        
        <div class="surtax-section">
          <label>增值附加税税率</label>
          
          <div class="surtax-table">
            <div class="table-header">
              <div class="col">税种名称</div>
              <div class="col">税率 (%)</div>
              <div class="col">优惠比例 (%)</div>
            </div>
            
            <div
              v-for="(tax, index) in configData.surtax"
              :key="index"
              class="table-row"
            >
              <div class="col">
                <el-input
                  v-model="tax.name"
                  placeholder="请输入税种名称"
                />
              </div>
              <div class="col">
                <el-input
                  v-model="tax.rate"
                  placeholder="请输入税率"
                />
              </div>
              <div class="col">
                <el-input
                  v-model="tax.discount_rate"
                  placeholder="请输入优惠比例"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 年龄配置 -->
    <div class="section">
      <h3 class="section-title">年龄配置</h3>
      
      <div class="age-config">
        <div class="age-item">
          <label>年龄下限</label>
          <span class="age-value">{{ configData.minAgeLimit }}</span>
          <span class="unit">岁</span>
          <el-button type="text" @click="editAge('min')">编辑</el-button>
        </div>
        
        <div class="age-item">
          <label>年龄上限</label>
          <span class="age-value">{{ configData.maxAgeLimit }}</span>
          <span class="unit">岁</span>
          <el-button type="text" @click="editAge('max')">编辑</el-button>
        </div>
      </div>
      
      <div class="age-tip">
        当开启年龄上限配置时，企业代付的人员年龄不能高于或等于设置的年龄，
        例如年龄上限设置为75岁，则代付人员年龄不能高于等于75岁。
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="actions">
      <el-button @click="prevStep">上一步</el-button>
      <el-button type="primary" @click="nextStep">下一步</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CorporationStep2',
  
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      payChannelForm: {
        channel1: '',
        defaultChannel: '',
        openCode: ''
      },
      configData: {
        vatStart: 0,
        vatRate: 0,
        minAgeLimit: 17,
        maxAgeLimit: 66,
        invoiceCategoryList: [
          '人力资源服务*服务费',
          '物流辅助服务*仓储服务',
          '信息技术服务*电路设计服务'
        ],
        surtax: [
          { name: '', rate: 0, discount_rate: 0 },
          { name: '', rate: 0, discount_rate: 0 },
          { name: '', rate: 0, discount_rate: 0 }
        ]
      },
      payChannelDataList: []
    }
  },
  
  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (val.configData) {
          this.configData = { ...this.configData, ...val.configData }
        }
        if (val.payChannelDataList) {
          this.payChannelDataList = [...val.payChannelDataList]
        }
      }
    }
  },
  
  methods: {
    addInvoiceCategory() {
      // TODO: 实现批量新增发票类目
      console.log('批量新增发票类目')
    },
    
    removeInvoiceCategory(index) {
      this.configData.invoiceCategoryList.splice(index, 1)
    },
    
    editAge(type) {
      const currentValue = type === 'min' ? this.configData.minAgeLimit : this.configData.maxAgeLimit
      
      this.$prompt(`请输入年龄${type === 'min' ? '下限' : '上限'}`, '编辑年龄', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: currentValue,
        inputPattern: /^\d+$/,
        inputErrorMessage: '请输入有效的年龄'
      }).then(({ value }) => {
        if (type === 'min') {
          this.configData.minAgeLimit = parseInt(value)
        } else {
          this.configData.maxAgeLimit = parseInt(value)
        }
      }).catch(() => {})
    },
    
    prevStep() {
      this.$emit('prev')
    },
    
    nextStep() {
      // 验证并保存配置数据
      const stepData = {
        configData: this.configData,
        payChannelDataList: this.payChannelDataList
      }
      
      this.$emit('input', stepData)
      this.$emit('next')
    }
  }
}
</script>

<style scoped>
.step2 {
  padding: 20px;
  max-width: 1000px;
}

.section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.pay-channel-config {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
}

.channel-row {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.channel-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.channel-item label {
  font-size: 14px;
  color: #606266;
}

.channel-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.invoice-config {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
}

.invoice-tags {
  margin-top: 15px;
}

.tax-config {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
}

.tax-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.tax-item label {
  width: 120px;
  font-size: 14px;
  color: #606266;
}

.unit {
  font-size: 14px;
  color: #909399;
}

.surtax-section {
  margin-top: 20px;
}

.surtax-section > label {
  display: block;
  margin-bottom: 15px;
  font-size: 14px;
  color: #606266;
}

.surtax-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.table-row:last-child {
  border-bottom: none;
}

.col {
  flex: 1;
  padding: 10px;
  border-right: 1px solid #ebeef5;
}

.col:last-child {
  border-right: none;
}

.table-header .col {
  font-weight: 600;
  color: #303133;
  text-align: center;
}

.age-config {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
}

.age-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.age-item label {
  width: 80px;
  font-size: 14px;
  color: #606266;
}

.age-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.age-tip {
  margin-top: 15px;
  padding: 15px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  font-size: 12px;
  color: #856404;
  line-height: 1.5;
}

.actions {
  text-align: right;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.actions .el-button {
  margin-left: 10px;
}
</style>
