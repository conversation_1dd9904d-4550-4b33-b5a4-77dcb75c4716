<template>
  <div class="step2" v-loading="loading">
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="140px"
      label-position="right"
    >
      <!-- 支付通道配置 -->
      <div class="section">
        <h3 class="section-title">支付通道配置</h3>

        <el-form-item label="支付通道升通道">
          <el-input
            v-model="payChannelForm.channel1"
            placeholder="选择支付通道"
            style="width: 300px"
          />
        </el-form-item>

        <el-form-item label="连接默认支付通道">
          <el-input
            v-model="payChannelForm.defaultChannel"
            placeholder="选择支付通道"
            style="width: 300px"
          />
        </el-form-item>

        <el-form-item label="开户代码">
          <div class="channel-actions">
            <el-input
              v-model="payChannelForm.openCode"
              placeholder="输入开户代码"
              style="width: 300px"
            />
            <el-button type="text">取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-form-item>
      </div>

      <!-- 发票类目配置 -->
      <div class="section">
        <h3 class="section-title">发票类目配置</h3>

        <el-form-item label="发票类目">
          <div class="invoice-config">
            <el-button type="text" @click="addInvoiceCategory"
              >批量新增</el-button
            >

            <div
              class="invoice-tags"
              v-if="formData.configData.invoiceCategoryList.length > 0"
            >
              <el-tag
                v-for="(category, index) in formData.configData
                  .invoiceCategoryList"
                :key="index"
                closable
                @close="removeInvoiceCategory(index)"
                style="margin-right: 10px; margin-bottom: 10px"
              >
                {{ category }}
              </el-tag>
            </div>

            <div v-else class="empty-placeholder">
              暂无发票类目，点击"批量新增"添加
            </div>
          </div>
        </el-form-item>
      </div>

      <!-- 增值税，增值附加税配置 -->
      <div class="section">
        <h3 class="section-title">增值税，增值附加税配置</h3>

        <el-form-item label="增值税起征点" prop="configData.vatStart">
          <div class="input-with-unit">
            <el-input-number
              v-model="formData.configData.vatStart"
              :min="0"
              :precision="2"
              placeholder="请输入增值税起征点"
              style="width: 200px"
            />
            <span class="unit">万元</span>
          </div>
        </el-form-item>

        <el-form-item label="增值税税率" prop="configData.vatRate">
          <div class="input-with-unit">
            <el-input-number
              v-model="formData.configData.vatRate"
              :min="0"
              :max="100"
              :precision="2"
              placeholder="请输入增值税税率"
              style="width: 200px"
            />
            <span class="unit">%</span>
          </div>
        </el-form-item>

        <el-form-item label="增值附加税税率">
          <div class="surtax-table">
            <div class="table-header">
              <div class="col">税种名称</div>
              <div class="col">税率 (%)</div>
              <div class="col">优惠比例 (%)</div>
            </div>

            <div
              v-for="(tax, index) in formData.configData.surtax"
              :key="index"
              class="table-row"
            >
              <div class="col">
                <el-input v-model="tax.name" placeholder="请输入税种名称" />
              </div>
              <div class="col">
                <el-input-number
                  v-model="tax.rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  placeholder="请输入税率"
                  style="width: 100%"
                />
              </div>
              <div class="col">
                <el-input-number
                  v-model="tax.discount_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  placeholder="请输入优惠比例"
                  style="width: 100%"
                />
              </div>
            </div>
          </div>
        </el-form-item>
      </div>

      <!-- 年龄配置 -->
      <div class="section">
        <h3 class="section-title">年龄配置</h3>

        <el-form-item label="年龄下限" prop="configData.minAgeLimit">
          <div class="age-item">
            <el-input-number
              v-model="formData.configData.minAgeLimit"
              :min="16"
              :max="100"
              style="width: 120px"
            />
            <span class="unit">岁</span>
          </div>
        </el-form-item>

        <el-form-item label="年龄上限" prop="configData.maxAgeLimit">
          <div class="age-item">
            <el-input-number
              v-model="formData.configData.maxAgeLimit"
              :min="16"
              :max="100"
              style="width: 120px"
            />
            <span class="unit">岁</span>
          </div>
        </el-form-item>

        <el-form-item label=" ">
          <div class="age-tip">
            当开启年龄上限配置时，企业代付的人员年龄不能高于或等于设置的年龄，
            例如年龄上限设置为75岁，则代付人员年龄不能高于等于75岁。
          </div>
        </el-form-item>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  name: 'CorporationStep2',

  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    corporationId: {
      type: [String, Number],
      default: null
    }
  },

  data() {
    return {
      payChannelForm: {
        channel1: '',
        defaultChannel: '',
        openCode: ''
      },
      formData: {
        configData: {
          vatStart: 0,
          vatRate: 0,
          minAgeLimit: 17,
          maxAgeLimit: 66,
          invoiceCategoryList: [
            '人力资源服务*服务费',
            '物流辅助服务*仓储服务',
            '信息技术服务*电路设计服务'
          ],
          surtax: [
            { name: '', rate: 0, discount_rate: 0 },
            { name: '', rate: 0, discount_rate: 0 },
            { name: '', rate: 0, discount_rate: 0 }
          ]
        },
        payChannelDataList: []
      },
      rules: {
        'configData.vatStart': [
          { required: true, message: '请输入增值税起征点', trigger: 'blur' }
        ],
        'configData.vatRate': [
          { required: true, message: '请输入增值税税率', trigger: 'blur' }
        ],
        'configData.minAgeLimit': [
          { required: true, message: '请设置年龄下限', trigger: 'blur' }
        ],
        'configData.maxAgeLimit': [
          { required: true, message: '请设置年龄上限', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },

  async created() {
    if (this.isEdit && this.corporationId) {
      await this.loadCorporationConfigData()
    }
  },

  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (val.configData) {
          this.formData.configData = {
            ...this.formData.configData,
            ...val.configData
          }
        }
        if (val.payChannelDataList) {
          this.formData.payChannelDataList = [...val.payChannelDataList]
        }
      }
    },
    formData: {
      deep: true,
      handler(val) {
        this.$emit('input', val)
      }
    }
  },

  methods: {
    async loadCorporationConfigData() {
      this.loading = true
      try {
        const [err, response] = await client.corporationConfigDetail({
          body: { corporationId: this.corporationId }
        })

        if (err) {
          handleError(err)
          return
        }

        const data = response.data

        // 填充配置数据
        if (data.configData) {
          this.formData.configData = {
            ...this.formData.configData,
            ...data.configData
          }
        }

        if (data.payChannelDataList) {
          this.formData.payChannelDataList = data.payChannelDataList
        }
      } catch (error) {
        console.error('加载作业主体配置数据失败:', error)
        this.$message.error('加载配置数据失败')
      } finally {
        this.loading = false
      }
    },

    addInvoiceCategory() {
      // TODO: 实现批量新增发票类目
      console.log('批量新增发票类目')
    },

    removeInvoiceCategory(index) {
      this.formData.configData.invoiceCategoryList.splice(index, 1)
    },

    prevStep() {
      this.$emit('prev')
    },

    nextStep() {
      // 使用 el-form 验证
      this.$refs.form.validate(valid => {
        if (valid) {
          // 验证年龄配置逻辑
          if (
            this.formData.configData.minAgeLimit >=
            this.formData.configData.maxAgeLimit
          ) {
            this.$message.error('年龄下限不能大于或等于年龄上限')
            return
          }

          this.$emit('next')
        } else {
          this.$message.error('请完善表单信息')
        }
      })
    }
  }
}
</script>

<style scoped>
.step2 {
  padding: 20px;
  max-width: 1000px;
}

.section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.channel-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.invoice-config {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.invoice-tags {
  min-height: 32px;
}

.empty-placeholder {
  padding: 20px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.input-with-unit {
  display: flex;
  align-items: center;
  gap: 10px;
}

.unit {
  font-size: 14px;
  color: #909399;
}

.age-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.surtax-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.table-row:last-child {
  border-bottom: none;
}

.col {
  flex: 1;
  padding: 10px;
  border-right: 1px solid #ebeef5;
}

.col:last-child {
  border-right: none;
}

.table-header .col {
  font-weight: 600;
  color: #303133;
  text-align: center;
}

.age-tip {
  padding: 15px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  font-size: 12px;
  color: #856404;
  line-height: 1.5;
  max-width: 500px;
}

.actions {
  text-align: right;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.actions .el-button {
  margin-left: 10px;
}
</style>
