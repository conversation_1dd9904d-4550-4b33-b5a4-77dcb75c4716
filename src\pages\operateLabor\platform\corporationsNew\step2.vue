<template>
  <div class="step2" v-loading="loading">
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="140px"
      label-position="right"
    >
      <!-- 支付通道配置 -->
      <div class="section">
        <h3 class="section-title">支付通道配置</h3>
        <PayChannelConfig
          v-if="corporationId"
          :corporation-id="corporationId"
          @success="handleConfigSuccess"
        />
        <div v-else class="empty-placeholder">
          请先完成第一步以获取作业主体ID
        </div>
      </div>

      <!-- 选择默认支付通道 -->
      <div class="section">
        <h3 class="section-title">选择默认支付通道</h3>
        <span style="color: #ccc"
          >如果您这里的列表为空，请先在支付通道进行配置</span
        >
        <el-form-item label="默认支付通道" prop="defaultPayChannel">
          <el-select
            v-model="formData.defaultPayChannel"
            placeholder="请选择默认支付通道"
            style="width: 400px"
            clearable
            value-key="payChannel"
          >
            <el-option
              v-for="channel in enabledChannels"
              :key="channel.payChannel"
              :label="channel.channelName"
              :value="channel.payChannel"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>

      <!-- 发票类目配置 -->
      <div class="section">
        <h3 class="section-title">发票类目配置</h3>

        <div>
          <el-button type="text" @click="openInvoiceDialog">批量新增</el-button>

          <div
            class="invoice-tags"
            v-if="formData.configData.invoiceCategoryList.length > 0"
          >
            <el-tag
              v-for="(category, index) in formData.configData
                .invoiceCategoryList"
              :key="index"
              closable
              @close="removeInvoiceCategory(index)"
              style="
                margin-right: 10px;
                margin-bottom: 10px;
                background: #f0f2f5;
                color: #808180;
              "
            >
              {{ category }}
            </el-tag>
          </div>

          <div v-else style="color: #ccc">暂无发票类目，点击"批量新增"添加</div>
        </div>
      </div>

      <!-- 增值税，增值附加税配置 -->
      <div class="section">
        <h3 class="section-title">增值税，增值附加税配置</h3>

        <el-form-item label="增值税起征点" prop="configData.vatStart">
          <div class="input-with-unit">
            <el-input-number
              v-model="formData.configData.vatStart"
              :min="0"
              :precision="2"
              placeholder="请输入增值税起征点"
              style="width: 200px"
            />
            <span class="unit">万元</span>
          </div>
        </el-form-item>

        <el-form-item label="增值税税率" prop="configData.vatRate">
          <div class="input-with-unit">
            <el-input-number
              v-model="formData.configData.vatRate"
              :min="0"
              :max="100"
              :precision="2"
              placeholder="请输入增值税税率"
              style="width: 200px"
            />
            <span class="unit">%</span>
          </div>
        </el-form-item>

        <el-form-item label="增值附加税税率" prop="configData.surtax">
          <Surtax v-model="formData.configData.surtax"></Surtax>
        </el-form-item>
      </div>

      <!-- 年龄配置 -->
      <div class="section">
        <h3 class="section-title">年龄配置</h3>
        <div style="display: flex; gap: 10px; align-items: center">
          <div>
            <el-form-item label="年龄下限" prop="configData.minAgeLimit">
              <div class="age-item">
                <el-input-number
                  v-model="formData.configData.minAgeLimit"
                  :min="16"
                  :max="100"
                  style="width: 120px"
                />
                <span class="unit">岁</span>
              </div>
            </el-form-item>

            <el-form-item label="年龄上限" prop="configData.maxAgeLimit">
              <div class="age-item">
                <el-input-number
                  v-model="formData.configData.maxAgeLimit"
                  :min="16"
                  :max="100"
                  style="width: 120px"
                />
                <span class="unit">岁</span>
              </div>
            </el-form-item>
          </div>
          <div class="age-tip" style="position: relative; top: -10px">
            当开启年龄上限配置时，企业代付的人员年龄不能高于或等于设置的年龄，
            例如年龄上限设置为75岁，则代付人员年龄不能高于等于75岁。
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </el-form>

    <!-- Invoice Category Dialog -->
    <el-dialog
      title="批量新增发票类目"
      :visible.sync="isInvoiceDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <p style="color: #909399; font-size: 12px; margin-top: 0">
        一行为一个发票类目
      </p>
      <el-input
        type="textarea"
        :rows="10"
        placeholder="请输入发票类目，每行一个"
        v-model="invoiceCategoryText"
      >
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isInvoiceDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleConfirmInvoiceCategory"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'
import PayChannelConfig from './payChannelConfig.vue'
import Surtax from '../components/surtax.vue'

const client = makeClient()

export default {
  name: 'CorporationStep2',
  components: { PayChannelConfig, Surtax },

  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    corporationId: {
      type: [String, Number],
      default: null
    }
  },

  data() {
    return {
      isInvoiceDialogVisible: false,
      invoiceCategoryText: '',
      formData: {
        configData: {
          vatStart: 0,
          vatRate: 0,
          minAgeLimit: 17,
          maxAgeLimit: 66,
          invoiceCategoryList: [
            '人力资源服务*服务费',
            '物流辅助服务*仓储服务',
            '信息技术服务*电路设计服务'
          ],
          surtax: [
            { name: '', rate: 0, discount_rate: 0 },
            { name: '', rate: 0, discount_rate: 0 },
            { name: '', rate: 0, discount_rate: 0 }
          ]
        },
        payChannelDataList: [],
        defaultPayChannel: 'CMBCLOUD'
      },
      rules: {
        'configData.vatStart': [
          { required: true, message: '请输入增值税起征点', trigger: 'blur' }
        ],
        'configData.vatRate': [
          { required: true, message: '请输入增值税税率', trigger: 'blur' }
        ],
        'configData.surtax': [
          { required: true, message: '请输入增值附加税税率', trigger: 'blur' }
        ],
        'configData.minAgeLimit': [
          { required: true, message: '请设置年龄下限', trigger: 'blur' }
        ],
        'configData.maxAgeLimit': [
          { required: true, message: '请设置年龄上限', trigger: 'blur' }
        ],
        defaultPayChannel: [
          {
            required: true,
            message: '请选择一个默认支付通道',
            trigger: 'change'
          }
        ]
      },
      loading: false
    }
  },
  computed: {
    enabledChannels() {
      return this.formData.payChannelDataList.filter(c => c.isOpen)
    }
  },

  async created() {
    if (this.isEdit && this.corporationId) {
      await this.loadCorporationConfigData()
    }
  },

  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (val.configData) {
          this.formData.configData = {
            ...this.formData.configData,
            ...val.configData
          }
        }
        debugger
        if (val.payChannelDataList) {
          this.formData.payChannelDataList = [...val.payChannelDataList]
        }
      }
    },
    formData: {
      deep: true,
      handler(val) {
        this.$emit('input', val)
      }
    }
  },

  methods: {
    async loadCorporationConfigData() {
      this.loading = true
      try {
        const [err, response] = await client.corporationConfigDetail({
          body: { id: this.corporationId }
        })

        if (err) {
          handleError(err)
          return
        }

        const data = response.data

        // 填充配置数据
        if (data.configData) {
          this.formData.configData = {
            ...this.formData.configData,
            ...data.configData
          }
        }

        if (data.payChannelDataList) {
          this.formData.payChannelDataList = data.payChannelDataList
          const defaultChannel = data.payChannelDataList.find(c => c.isDefault)
          if (defaultChannel) {
            this.formData.defaultPayChannel = defaultChannel.payChannel
          }
        }
      } catch (error) {
        console.error('加载作业主体配置数据失败:', error)
        this.$message.error('加载配置数据失败')
      } finally {
        this.loading = false
      }
    },

    openInvoiceDialog() {
      this.invoiceCategoryText =
        this.formData.configData.invoiceCategoryList.join('\n')
      this.isInvoiceDialogVisible = true
    },

    handleConfirmInvoiceCategory() {
      const categories = this.invoiceCategoryText
        .split('\n')
        .map(line => line.trim())
        .filter(line => line) // Filter out empty lines
      this.formData.configData.invoiceCategoryList = [...new Set(categories)] // Ensure uniqueness
      this.isInvoiceDialogVisible = false
    },

    removeInvoiceCategory(index) {
      this.formData.configData.invoiceCategoryList.splice(index, 1)
    },

    async saveCorporationConfigData() {
      this.loading = true
      try {
        // Create a deep copy to build the payload without modifying form data directly
        const configDataForPayload = JSON.parse(
          JSON.stringify(this.formData.configData)
        )

        // Convert surtax array to surtaxData JSON string
        configDataForPayload.surtaxData = JSON.stringify(
          configDataForPayload.surtax || []
        )

        // Convert invoiceCategoryList array to invoiceCategory string (using comma as separator)
        configDataForPayload.invoiceCategory = (
          configDataForPayload.invoiceCategoryList || []
        ).join(',')

        // Clean up properties that are not part of the configData payload
        delete configDataForPayload.surtax
        delete configDataForPayload.invoiceCategoryList

        const payload = {
          corporationId: this.corporationId,
          configData: configDataForPayload,
          defaultPayChannel: this.formData.defaultPayChannel
        }

        const [err] = await client.editCorporationBusiness({
          body: payload
        })

        if (err) {
          handleError(err)
          throw new Error(err.message || '保存配置失败')
        }

        this.$message.success('业务配置保存成功')
      } catch (error) {
        console.error('保存作业主体配置数据失败:', error)
        this.$message.error('保存配置失败，请重试')
        throw error
      } finally {
        this.loading = false
      }
    },

    handleConfigSuccess() {
      // When a channel is configured, refresh the list to get latest status
      this.loadCorporationConfigData()
    },

    prevStep() {
      this.$emit('prev')
    },

    async nextStep() {
      // 使用 el-form 验证
      this.$refs.form.validate(async valid => {
        if (valid) {
          // 验证年龄配置逻辑
          if (
            this.formData.configData.minAgeLimit >=
            this.formData.configData.maxAgeLimit
          ) {
            this.$message.error('年龄下限不能大于或等于年龄上限')
            return
          }

          // 如果是编辑模式，先保存配置数据
          if (this.isEdit && this.corporationId) {
            await this.saveCorporationConfigData()
          }

          this.$emit('next')
        } else {
          this.$message.error('请完善表单信息')
        }
      })
    }
  }
}
</script>

<style scoped>
.step2 {
  padding: 20px;
  max-width: 1000px;
}

.section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.channel-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.invoice-config {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.invoice-tags {
  min-height: 32px;
}

.empty-placeholder {
  padding: 20px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.input-with-unit {
  display: flex;
  align-items: center;
  gap: 10px;
}

.unit {
  font-size: 14px;
  color: #909399;
}

.age-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.surtax-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.table-row:last-child {
  border-bottom: none;
}

.col {
  flex: 1;
  padding: 10px;
  border-right: 1px solid #ebeef5;
}

.col:last-child {
  border-right: none;
}

.table-header .col {
  font-weight: 600;
  color: #303133;
  text-align: center;
}

.age-tip {
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  color: #808181;
  line-height: 1.5;
  max-width: 500px;
}

.actions {
  text-align: right;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.actions .el-button {
  margin-left: 10px;
}
</style>
