<template>
  <div class="step3">
    <!-- 成功状态 -->
    <div v-if="succeed" class="result-container success">
      <div class="result-icon">
        <i class="el-icon-success"></i>
      </div>
      <div class="result-title">
        {{ isEdit ? '作业主体修改成功！' : '作业主体创建成功！' }}
      </div>
      <div class="result-description">
        {{
          isEdit
            ? '作业主体信息已成功更新'
            : '新的作业主体已成功创建并保存到系统中'
        }}
      </div>

      <!-- 创建结果信息 -->
      <div class="result-info" v-if="resultData">
        <div class="info-item">
          <label>作业主体名称：</label>
          <span>{{ resultData.name }}</span>
        </div>
        <div class="info-item">
          <label>作业主体ID：</label>
          <span>{{ resultData.id }}</span>
        </div>
        <div class="info-item">
          <label>创建时间：</label>
          <span>{{ formatDateTime(resultData.createTime) }}</span>
        </div>
      </div>

      <div class="result-actions">
        <el-button @click="goToList">返回列表</el-button>
        <el-button type="primary" @click="createAnother" v-if="!isEdit">
          继续创建
        </el-button>
      </div>
    </div>

    <!-- 失败状态 -->
    <div v-else-if="failed" class="result-container error">
      <div class="result-icon">
        <i class="el-icon-error"></i>
      </div>
      <div class="result-title">
        {{ isEdit ? '作业主体修改失败' : '作业主体创建失败' }}
      </div>
      <div class="result-description">
        {{ reason || '操作过程中发生错误，请重试' }}
      </div>

      <div class="result-actions">
        <el-button @click="retry">重试</el-button>
        <el-button type="primary" @click="goToList">返回列表</el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else class="result-container loading">
      <div class="result-icon">
        <i class="el-icon-loading"></i>
      </div>
      <div class="result-title">
        {{ isEdit ? '正在保存修改...' : '正在创建作业主体...' }}
      </div>
      <div class="result-description">请稍候，系统正在处理您的请求</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CorporationStep3',

  props: {
    succeed: {
      type: Boolean,
      default: false
    },
    failed: {
      type: Boolean,
      default: false
    },
    reason: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    resultData: {
      type: Object,
      default: null
    }
  },

  methods: {
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      return new Date(dateTime).toLocaleString('zh-CN')
    },

    goToList() {
      this.$emit('go-to-list')
    },

    createAnother() {
      this.$emit('create-another')
    },

    retry() {
      this.$emit('retry')
    }
  }
}
</script>

<style scoped>
.step3 {
  padding: 40px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.result-container {
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.result-icon {
  margin-bottom: 24px;
}

.result-icon i {
  font-size: 64px;
}

.success .result-icon i {
  color: #67c23a;
}

.error .result-icon i {
  color: #f56c6c;
}

.loading .result-icon i {
  color: #409eff;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.result-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #303133;
}

.result-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 32px;
  line-height: 1.5;
}

.result-info {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 24px;
  margin-bottom: 32px;
  text-align: left;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  width: 120px;
  font-weight: 500;
  color: #606266;
}

.info-item span {
  color: #303133;
}

.result-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.result-actions .el-button {
  min-width: 100px;
}
</style>
