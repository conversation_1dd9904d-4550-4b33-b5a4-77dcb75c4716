<template>
  <div class="step3">
    <!-- 银行信息 -->
    <div class="section">
      <h3 class="section-title">银行信息</h3>
      
      <div class="bank-form">
        <div class="form-item">
          <label class="required">开户行</label>
          <el-input
            v-model="formData.bankName"
            placeholder="请输入开户行名称"
            style="width: 400px"
          />
        </div>
        
        <div class="form-item">
          <label class="required">银行账号</label>
          <el-input
            v-model="formData.bankAccount"
            placeholder="请输入银行账号"
            style="width: 400px"
          />
        </div>
        
        <div class="form-item">
          <label>企业电话</label>
          <el-input
            v-model="formData.companyTel"
            placeholder="请输入企业电话"
            style="width: 400px"
          />
        </div>
        
        <div class="form-item">
          <label>平台UUID</label>
          <el-input
            v-model="formData.taxUuid"
            placeholder="请输入平台UUID"
            style="width: 400px"
          />
        </div>
      </div>
    </div>
    
    <!-- 信息确认 -->
    <div class="section">
      <h3 class="section-title">信息确认</h3>
      
      <div class="info-summary">
        <div class="summary-item">
          <label>公司名称：</label>
          <span>{{ step1Data.name || '未填写' }}</span>
        </div>
        
        <div class="summary-item">
          <label>统一社会信用代码：</label>
          <span>{{ step1Data.socialCreditCode || '未填写' }}</span>
        </div>
        
        <div class="summary-item">
          <label>法人姓名：</label>
          <span>{{ step1Data.representativeName || '未填写' }}</span>
        </div>
        
        <div class="summary-item">
          <label>联系人：</label>
          <span>{{ step1Data.contactName || '未填写' }}</span>
        </div>
        
        <div class="summary-item">
          <label>联系电话：</label>
          <span>{{ step1Data.contactMobile || '未填写' }}</span>
        </div>
        
        <div class="summary-item">
          <label>开户行：</label>
          <span>{{ formData.bankName || '未填写' }}</span>
        </div>
        
        <div class="summary-item">
          <label>银行账号：</label>
          <span>{{ formData.bankAccount || '未填写' }}</span>
        </div>
        
        <div class="summary-item">
          <label>年龄限制：</label>
          <span>{{ step2Data.configData?.minAgeLimit || 0 }}岁 - {{ step2Data.configData?.maxAgeLimit || 0 }}岁</span>
        </div>
        
        <div class="summary-item">
          <label>增值税起征点：</label>
          <span>{{ step2Data.configData?.vatStart || 0 }}万元</span>
        </div>
        
        <div class="summary-item">
          <label>增值税税率：</label>
          <span>{{ step2Data.configData?.vatRate || 0 }}%</span>
        </div>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="actions">
      <el-button @click="prevStep">上一步</el-button>
      <el-button type="primary" @click="submit" :loading="submitting">
        {{ isEdit ? '保存修改' : '创建作业主体' }}
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CorporationStep3',
  
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    step1Data: {
      type: Object,
      default: () => ({})
    },
    step2Data: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      formData: {
        bankName: '',
        bankAccount: '',
        companyTel: '',
        taxUuid: ''
      },
      submitting: false
    }
  },
  
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.formData = { ...this.formData, ...val }
      }
    },
    formData: {
      deep: true,
      handler(val) {
        this.$emit('input', val)
      }
    }
  },
  
  methods: {
    prevStep() {
      this.$emit('prev')
    },
    
    async submit() {
      // 验证表单
      if (!this.validateForm()) {
        return
      }
      
      this.submitting = true
      
      try {
        // 合并所有步骤的数据
        const submitData = {
          ...this.step1Data,
          ...this.formData,
          disabled: false
        }
        
        // 发送提交事件
        this.$emit('submit', {
          basicData: submitData,
          configData: this.step2Data.configData,
          payChannelDataList: this.step2Data.payChannelDataList || []
        })
        
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败，请重试')
      } finally {
        this.submitting = false
      }
    },
    
    validateForm() {
      const required = ['bankName', 'bankAccount']
      
      for (const field of required) {
        if (!this.formData[field]) {
          this.$message.error(`请填写${this.getFieldLabel(field)}`)
          return false
        }
      }
      
      return true
    },
    
    getFieldLabel(field) {
      const labels = {
        bankName: '开户行',
        bankAccount: '银行账号',
        companyTel: '企业电话',
        taxUuid: '平台UUID'
      }
      return labels[field] || field
    }
  }
}
</script>

<style scoped>
.step3 {
  padding: 20px;
  max-width: 800px;
}

.section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.bank-form {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
}

.form-item {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.form-item label {
  width: 120px;
  text-align: right;
  margin-right: 20px;
  color: #606266;
}

.form-item label.required::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.info-summary {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
}

.summary-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.summary-item label {
  width: 150px;
  font-weight: 600;
  color: #606266;
}

.summary-item span {
  color: #303133;
}

.actions {
  text-align: right;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.actions .el-button {
  margin-left: 10px;
}
</style>
