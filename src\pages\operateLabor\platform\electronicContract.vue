<template>
  <div
    class="contract-templates"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px;
        border-radius: 5px;
        display: flex;
        justify-content: space-between;
      "
      label-position="right"
      label-width="110px"
    >
      <div style="display: flex; gap: 20px">
        <el-form-item label="合同名称" style="margin-bottom: 0">
          <el-input
            v-model="conditions.filters.tempName"
            placeholder="请输入模板名称"
            style="width: 280px"
          ></el-input>
        </el-form-item>
      </div>
      <div>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </div>
    </el-form>

    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus" />
        发起签约
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="tempName"
        label="合同编号"
        width="250"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="tempName"
        label="合同名称"
        width="250"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="tempName"
        label="姓名"
        width="250"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="tempName"
        label="身份证号"
        width="250"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="tempName"
        label="作业主体"
        width="250"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="合同开始时间"
        width="160"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="合同结束时间"
        width="160"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column label="签署状态" width="120">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.tempStatus)" size="small">
            {{ getStatusText(scope.row.tempStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            :loading="scope.row.editLoading"
            @click="handleDownload(scope.row)"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'ContractTemplates',
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: false,
        filters: {
          tempName: '',
          tempStatus: ''
        }
      },
      total: 0,
      data: [],
      loading: true
    }
  },
  created() {
    this.getList()
  },
  methods: {
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions.filters.tempName = ''
      this.conditions.filters.tempStatus = ''
      this.onSearch()
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.getTemplateList({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      // 为每个项目添加loading状态
      this.data = (r.data.list || []).map(item => ({
        ...item,
        editLoading: false,
        statusLoading: false
      }))
      this.total = r.data.total || 0
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    getStatusType(status) {
      const statusMap = {
        DRAFT: 'info',
        ERROR: 'danger',
        ENABLED: 'success',
        DISABLED: 'warning'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        DRAFT: '草稿',
        ERROR: '错误',
        ENABLED: '启用',
        DISABLED: '停用'
      }
      return statusMap[status] || status
    },

    handleAdd() {
      this.$router.push('/startSign')
    },

    async handleDownload(row) {
      try {
        const [err] = await client.canUpdateTemplate(row.tempId)
        if (err) {
          handleError(err)
          return
        }
      } catch (error) {
        handleError(error)
      }
    }
  }
}
</script>

<style scoped>
.status-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}
</style>
