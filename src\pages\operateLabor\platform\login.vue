<template>
  <div
    :style="{
      display: 'flex',
      fontFamily: 'sans-serif',
      backgroundColor: '#F7F8FA',
      height: '100vh'
    }"
  >
    <slogo />

    <!-- Right Panel -->
    <div
      :style="{
        flex: '1',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative'
      }"
    >
      <!-- Header Links -->
      <div
        :style="{
          position: 'absolute',
          top: '20px',
          right: '40px',
          display: 'flex',
          alignItems: 'center'
        }"
      >
        <a
          @click="goToLogin"
          :style="{
            color: '#666',
            textDecoration: 'none',
            cursor: 'pointer',
            margin: '0 15px'
          }"
          >登录</a
        >
        <span :style="{ color: '#ccc' }">|</span>
        <a
          @click="goToRegister"
          :style="{
            color: '#666',
            textDecoration: 'none',
            cursor: 'pointer',
            margin: '0 15px'
          }"
          >注册</a
        >
        <span :style="{ color: '#ccc' }">|</span>
        <a
          @click="goToWebsite"
          :style="{
            color: '#666',
            textDecoration: 'none',
            cursor: 'pointer',
            margin: '0 15px'
          }"
          >返回官网</a
        >
      </div>

      <!-- Login Form -->
      <div
        :style="{
          width: '420px',
          padding: '40px',
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 10px 40px rgba(0,0,0,0.05)'
        }"
      >
        <!-- Logo -->
        <img
          src="https://s.lad-tech.com/prod/assets/img/logo-v3.543f545.png"
          alt="logo"
          :style="{ width: '48px', display: 'block', margin: '0 auto 30px' }"
        />

        <h2
          :style="{
            fontSize: '28px',
            fontWeight: 'bold',
            color: '#333',
            textAlign: 'center',
            margin: '0 0 30px 0'
          }"
        >
          登录
        </h2>

        <div
          :style="{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'baseline',
            marginBottom: '25px'
          }"
        >
          <span :style="{ fontSize: '18px', color: '#333' }">密码登录</span>
          <a
            @click="switchLoginType"
            :style="{
              fontSize: '14px',
              color: '#F56C6C',
              cursor: 'pointer',
              textDecoration: 'none'
            }"
            >切换验证码登录</a
          >
        </div>

        <el-form :model="form" ref="form" :rules="rules">
          <el-form-item prop="account">
            <el-input
              v-model="form.account"
              placeholder="请输入手机号"
              :style="{ width: '100%' }"
            ></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
            ></el-input>
          </el-form-item>
          <el-form-item prop="captcha">
            <Captcha v-model="form.captcha" />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handleLogin"
              :style="{
                width: '100%',
                backgroundColor: '#D93030',
                borderColor: '#D93030',
                fontSize: '16px',
                padding: '12px'
              }"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>

        <div
          :style="{
            display: 'flex',
            justifyContent: 'space-between',
            marginTop: '20px'
          }"
        >
          <a
            @click="goToRegister"
            :style="{
              color: '#F56C6C',
              fontSize: '14px',
              textDecoration: 'none',
              cursor: 'pointer'
            }"
            >没有账号? 立即注册</a
          >
          <a
            @click="goToForgotPassword"
            :style="{
              color: '#666',
              fontSize: '14px',
              textDecoration: 'none',
              cursor: 'pointer'
            }"
            >找回密码</a
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Slogo from './slogo.vue'
import Captcha from './captcha.vue'
import makeClient from 'kit/services/operateLabor/makeClient'
import { setToken } from 'kit/helpers/token'
import handleError from 'kit/helpers/handleError'
import { setSupplierUserProfile } from './context'

const client = makeClient()

export default {
  name: 'Login',
  components: {
    Slogo,
    Captcha
  },
  data() {
    return {
      form: {
        account: '',
        password: '',
        captcha: {
          token: '',
          value: ''
        },
        type: 'SUPPLIER',
        smsLogin: false
      },
      rules: {
        account: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        captcha: [
          { required: true, message: '请输入图形验证码', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async handleLogin() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      const [err, r] = await client.login({
        body: {
          account: this.form.account,
          password: this.form.password,
          captchaToken: this.form.captcha.token,
          captchaAnswer: this.form.captcha.value,
          type: this.form.type
        }
      })
      if (err) {
        handleError(err)
        return
      }

      setToken(r.data.token)

      const [err2, r2] = await client.supplierProfile()
      if (err2) {
        handleError(err2)
        return
      }
      setSupplierUserProfile(r2.data)
      //因为进入login页面时候没有加载菜单，导致跳转不过去
      await this.$parent.loadNavigations()
      this.$router.push('/roles')
    },
    switchLoginType() {
      this.$router.push({ path: '/loginWithCaptcha' })
    },
    goToLogin() {
      // todo: Implement navigation if this is a separate view/component
      console.log('Navigate to Login')
    },
    goToRegister() {
      this.$router.push({ path: '/register' })
    },
    goToForgotPassword() {
      this.$router.push({ path: '/findPassword' })
    },
    goToWebsite() {
      // todo: Implement navigation to the main website
      console.log('Navigate to the official website')
    }
  }
}
</script>
