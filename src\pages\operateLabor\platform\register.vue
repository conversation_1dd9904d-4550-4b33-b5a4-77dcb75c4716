<template>
  <div
    :style="{
      display: 'flex',
      fontFamily: 'sans-serif',
      backgroundColor: '#F7F8FA',
      height: '100vh'
    }"
  >
    <slogo />

    <!-- Right Panel -->
    <div
      :style="{
        flex: '1',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative'
      }"
    >
      <!-- Header Links -->
      <div
        :style="{
          position: 'absolute',
          top: '20px',
          right: '40px',
          display: 'flex',
          alignItems: 'center'
        }"
      >
        <a
          @click="goToLogin"
          :style="{
            color: '#666',
            textDecoration: 'none',
            cursor: 'pointer',
            margin: '0 15px'
          }"
          >登录</a
        >
        <span :style="{ color: '#ccc' }">|</span>
        <a
          @click="goToRegister"
          :style="{
            color: '#666',
            textDecoration: 'none',
            cursor: 'pointer',
            margin: '0 15px'
          }"
          >注册</a
        >
        <span :style="{ color: '#ccc' }">|</span>
        <a
          @click="goToWebsite"
          :style="{
            color: '#666',
            textDecoration: 'none',
            cursor: 'pointer',
            margin: '0 15px'
          }"
          >返回官网</a
        >
      </div>

      <!-- Registration Form -->
      <div
        :style="{
          width: '420px',
          padding: '40px',
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 10px 40px rgba(0,0,0,0.05)'
        }"
      >
        <img
          src="https://s.lad-tech.com/prod/assets/img/logo-v3.543f545.png"
          alt="logo"
          :style="{ width: '48px', display: 'block', margin: '0 auto 30px' }"
        />
        <h2
          :style="{
            fontSize: '28px',
            fontWeight: 'bold',
            color: '#333',
            textAlign: 'center',
            margin: '0 0 10px 0'
          }"
        >
          免费注册
        </h2>
        <p
          :style="{
            textAlign: 'center',
            color: '#999',
            fontSize: '14px',
            marginBottom: '30px'
          }"
        >
          个人注册成功后,可通过创建企业功能,完成企业的入网
        </p>

        <el-form :model="registerForm" ref="registerForm">
          <el-form-item prop="phone">
            <el-input
              v-model="registerForm.phone"
              placeholder="请输入手机号"
            ></el-input>
          </el-form-item>
          <el-form-item prop="captcha">
            <div :style="{ display: 'flex' }">
              <el-input
                v-model="registerForm.captcha"
                placeholder="请输入图形验证码"
                :style="{ flex: 1, marginRight: '10px' }"
              ></el-input>
              <img
                @click="refreshCaptcha"
                :src="captchaImage"
                alt="captcha"
                :style="{
                  width: '100px',
                  height: '40px',
                  cursor: 'pointer',
                  border: '1px solid #DCDFE6',
                  borderRadius: '4px'
                }"
              />
            </div>
          </el-form-item>
          <el-form-item prop="smsCode">
            <div :style="{ display: 'flex' }">
              <el-input
                v-model="registerForm.smsCode"
                placeholder="请输入短信验证码"
                :style="{ flex: 1, marginRight: '10px' }"
              ></el-input>
              <el-button @click="getSmsCode">获取验证码</el-button>
            </div>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="registerForm.agreed">
              已阅读并同意
              <a href="#" :style="{ color: '#F56C6C', textDecoration: 'none' }"
                >《用户服务协议》</a
              >
            </el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handleRegister"
              :style="{
                width: '100%',
                backgroundColor: '#D93030',
                borderColor: '#D93030',
                fontSize: '16px',
                padding: '12px'
              }"
            >
              注册
            </el-button>
          </el-form-item>
        </el-form>

        <div :style="{ textAlign: 'center', marginTop: '20px' }">
          <a
            @click="goToLogin"
            :style="{
              color: '#666',
              fontSize: '14px',
              textDecoration: 'none',
              cursor: 'pointer'
            }"
            >已有账号? 去登录</a
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Slogo from './slogo.vue'

export default {
  name: 'Register',
  components: {
    Slogo
  },
  data() {
    return {
      registerForm: {
        phone: '',
        captcha: '',
        smsCode: '',
        agreed: false
      },
      captchaImage:
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAAAoCAYAAAAIe35DAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGESURBVHhe7ZHBAYAgDEMf+k/rYQdQaBv2328EDmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhm-9335.png'
    }
  },
  methods: {
    handleRegister() {
      // todo: Validate form and send registration request
      console.log('Attempting to register with:', this.registerForm)
    },
    refreshCaptcha() {
      // todo: Implement logic to fetch a new captcha image
      console.log('Refreshing captcha')
      this.captchaImage =
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAAAoCAYAAAAIe35DAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGESURBVHhe7ZHBAYAgDEMf+k/rYQdQaBv2328EDmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhmEYhm-9335.png'
    },
    getSmsCode() {
      // todo: Implement logic to send SMS code
      console.log('Getting SMS code for:', this.registerForm.phone)
    },
    goToLogin() {
      this.$router.push({ path: '/login' })
    },
    goToRegister() {
      // Already on register page
    },
    goToWebsite() {
      // todo: Implement navigation to the main website
      console.log('Navigate to the official website')
    }
  }
}
</script>
