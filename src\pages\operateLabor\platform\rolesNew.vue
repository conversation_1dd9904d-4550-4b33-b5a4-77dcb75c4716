<template>
  <div class="rolesNew">
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="100px"
      style="width: 600px"
    >
      <Title title="基本信息" />
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入角色名称"></el-input>
      </el-form-item>
      <el-form-item label="角色描述" prop="remark">
        <el-input
          type="textarea"
          v-model="form.remark"
          placeholder="请输入角色描述"
        ></el-input>
      </el-form-item>
      <Title title="权限管理" />
      <el-form-item label="权限列表" prop="authorities">
        <el-tree
          style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 10px"
          :data="authorityTree"
          show-checkbox
          node-key="authority"
          ref="authorityTree"
          :props="{ label: 'title', children: 'children' }"
          :default-expand-all="true"
          @check="handleCheckChange"
        >
        </el-tree>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">{{
          isEdit ? '保存' : '创建'
        }}</el-button>
        <el-button @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'
const client = makeClient()

export default {
  components: { Title },
  computed: {
    isEdit() {
      return !!this.$route.params.id
    },
    roleId() {
      return this.$route.params.id
    }
  },
  data() {
    const validateAuthorities = (rule, value, callback) => {
      if (this.$refs.authorityTree.getCheckedKeys(true).length === 0) {
        callback(new Error('请至少选择一个权限'))
      } else {
        callback()
      }
    }
    return {
      form: {
        name: '',
        remark: ''
      },
      rules: {
        name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
        authorities: [{ validator: validateAuthorities, trigger: 'change' }]
      },
      authorityTree: []
    }
  },
  async created() {
    const [err, r] = await client.supplierGetAuthorityTree({ body: {} })
    if (err) {
      handleError(err)
      return
    }
    this.authorityTree = r.data.children || []

    if (this.isEdit) {
      await this.getRoleDetails()
    }
  },
  methods: {
    async getRoleDetails() {
      const [err, r] = await client.roleDetail({
        body: { roleId: this.roleId }
      })
      if (err) {
        handleError(err)
        return
      }
      this.form.name = r.data.name
      this.form.remark = r.data.remark
      this.$nextTick(() => {
        this.$refs.authorityTree.setCheckedKeys(r.data.authorities || [], true)
      })
    },
    handleCheckChange() {
      this.$refs.form.validateField('authorities')
    },
    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }
      const checkedAuthorities = this.$refs.authorityTree.getCheckedKeys(true)
      const roleId = this.$route.params.id

      const payload = {
        name: this.form.name,
        remark: this.form.remark,
        authorities: checkedAuthorities
      }

      if (this.isEdit) {
        payload.roleId = roleId
        const [err] = await client.editRole({ body: payload })
        if (err) {
          handleError(err)
          return
        }
      }

      if (!this.isEdit) {
        const [err] = await client.createRole({ body: payload })
        if (err) {
          handleError(err)
          return
        }
      }

      this.$message.success(this.isEdit ? '更新成功' : '创建成功')
      this.$router.push('/roles')
    }
  }
}
</script>
