<template>
  <div class="staff-choose" style="padding: 20px">
    <el-input
      v-model.trim="searchForm.key"
      @keyup.enter.native="getList"
      placeholder="请输入姓名/手机号"
      class="search-staff-input"
    ></el-input>
    <!-- 人员列表 -->
    <el-main v-loading="loading">
      <div style="text-align: center">
        <div
          class="staff-item"
          v-for="(item, index) in staffList"
          :key="index"
          :class="[
            'staff-item',
            {
              active: styleActive === item.platformUserId
            },
            { disableStyle: !item.platformUserId || item.platformDisabledYn }
          ]"
          @click="handleSelectItem(item)"
        >
          <p>
            <span
              style="margin-right: 60px; width: 140px"
              class="staff-name ellipsis"
              >{{ item.empName }}</span
            >
            <span class="staff-mobile">{{ item.mobile }}</span>
          </p>
          <p style="margin-top: 5px">
            <span>
              {{ item.authYn ? '已认证' : '未认证' }}
            </span>
            <span style="width: 200px" class="ellipsis">{{ item.role }}</span>
          </p>
          <i
            style="
              font-size: 26px;
              position: absolute;
              right: 10px;
              top: 20px;
              color: #333;
            "
            :class="[
              styleActive === item.platformUserId
                ? 'redColor el-icon-success'
                : 'iconfont iconyuanxingweixuanzhong before-checked'
            ]"
          ></i>
        </div>
        <div v-if="noInfo" style="height: 250px; margin-top: 50px">
          <div style="text-align: center">
            <!-- <img
              style="width: 200px; height: 115px"
              src="../../../assets/images/empty.png"
              alt=""
            /> -->
          </div>
          <div class="tips-content" style="margin-top: 30px">
            <h2>未设置企业公章签署人</h2>
            <p>请在组织架构->组织管理中，添加或设置人员角色为公章签署人</p>
          </div>
        </div>
      </div>
    </el-main>
    <div class="footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        @click="handleSave"
        :disabled="!this.currentSelectItem.platformUserId"
        >确定</el-button
      >
    </div>
  </div>
</template>
<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'chooseSealStaff',
  data() {
    return {
      loading: false,
      searchForm: {
        key: ''
      },
      staffList: [],
      currentSelectItem: {},
      noInfo: false,
      styleActive: '',
      chooseSealStaffData: {},
      flowStep: {}
    }
  },
  created() {
    this.currentSelectItem.platformUserId = this.chooseSealStaffData
      ? this.chooseSealStaffData.platformUserId
      : ''
    this.getList()
    setTimeout(() => {
      this.compareStaff()
    }, 300)
  },
  methods: {
    compareStaff() {
      this.flowStep.steps.map(item => {
        this.staffList.map(staff => {
          if (item.compEmpId == staff.platformUserId) {
            this.styleActive = item.compEmpId
            this.$set(this.currentSelectItem, 'platformUserId', item.compEmpId)
          }
        })
      })
    },
    async getList() {
      this.loading = true
      const [err, r] = await client.apiContractSealUserList({
        body: {
          offset: 0,
          limit: 1000,
          withTotal: false,
          withDisabled: false,
          withDeleted: false,
          filters: {
            tempName: name,
            tempStatus: 'ENABLED' // 只显示启用的模板
          }
        }
      })
      this.loading = false
      if (err) {
        handleError(err)
        return
      }
      this.staffList = r.data
      if (!r.data || !r.data.length) {
        this.noInfo = true
      } else {
        this.noInfo = false
      }
    },
    handleSelectItem(item) {
      if (!item.platformUserId || item.platformDisabledYn) {
        this.$message.warning('该员工帐号异常，无法发起签约')
        return
      }
      if (item.platformUserId === this.currentSelectItem.platformUserId) {
        this.currentSelectItem = {}
        this.styleActive = ''
      } else {
        this.currentSelectItem = item
        this.styleActive = item.platformUserId
      }
    },
    handleSave() {
      this.$store.commit(
        'contractManageStore/SET_CHOOSESEALSTAFFDATA',
        this.currentSelectItem
      )
      this.$parent.drawer = false
    },
    handleCancel() {
      this.$parent.drawer = false
    }
  }
}
</script>
<style scoped>
.tips-content h2 {
  height: 16px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #24262a;
  text-align: center;
  line-height: 16px;
  margin-bottom: 20px;
}
.tips-content p {
  /* width: 396px; */
  text-align: center;
  height: 14px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #46485a;
  letter-spacing: 0;
  line-height: 14px;
}
.footer {
  box-sizing: border-box;
  width: 100%;
  height: 60px;
  line-height: 60px;
  box-shadow: 10px -2px 35px 0px rgba(222, 214, 214, 1);
  position: absolute;
  left: 0;
  bottom: 0;
  background: #fff;
  text-align: right;
}
.search-staff-input {
  padding: 0;
  margin-bottom: 10px;
}
.staff-item :hover {
  border: 1px solid;
}
.active {
  border: 1px solid #4f71ff;
  box-shadow: 0 0 20px #f1f6ff;
}
.redColor {
  color: #4f71ff;
}
.before-checked {
  margin-right: 10px;
  color: #dcdfe6;
}
.staff-item {
  position: relative;
  cursor: pointer;
  border: 1px solid #d8d8d8;
  padding: 10px;
  margin: 10px 0;
  height: 50px;
}
.staff-item p {
  overflow: hidden;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
}
.ellipsis {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.disableStyle p {
  color: #d8d8d8;
}
.disableStyle p:hover {
  border: 1px solid #d8d8d8;
}
.disableStyle i {
  color: #d8d8d8;
}

.disableStyle .staff-name,
.staff-mobile {
  color: #d8d8d8;
}
</style>
