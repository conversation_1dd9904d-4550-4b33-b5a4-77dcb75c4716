<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :multiple="multiple"
    :clearable="clearable"
    :filterable="filterable"
    :loading="loading"
    :remote="remote"
    :remote-method="handleRemoteSearch"
    @change="handleChange"
    @visible-change="handleVisibleChange"
  >
    <el-option
      v-for="item in options"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    >
      <span style="float: left">{{ item.name }}</span>
      <span style="float: right; color: #8492a6; font-size: 13px">
        {{ item.socialCreditCode }}
      </span>
    </el-option>
  </el-select>
</template>

<script>
import makeClient from '../../../../services/operateLabor/makeClient'
import handleError from '../../../../helpers/handleError'

const client = makeClient()

export default {
  name: 'CorporationsSelector',
  
  props: {
    value: {
      type: [Number, Array],
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择作业主体'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: <PERSON><PERSON>an,
      default: true
    },
    remote: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      selectedValue: this.multiple ? [] : null,
      options: [],
      loading: false,
      searchKeyword: ''
    }
  },

  watch: {
    value: {
      handler(newVal) {
        this.selectedValue = newVal
      },
      immediate: true
    }
  },

  async created() {
    await this.loadCorporations()
  },

  methods: {
    async loadCorporations(keyword = '') {
      this.loading = true
      
      try {
        const [err, response] = await client.listCorporation({
          body: {
            offset: 0,
            limit: 100,
            withTotal: false,
            withDisabled: false,
            withDeleted: false,
            filters: {
              name: keyword
            }
          }
        })

        if (err) {
          handleError(err)
          return
        }

        this.options = response.data?.list || []
      } catch (error) {
        handleError(error)
      } finally {
        this.loading = false
      }
    },

    handleChange(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    },

    handleVisibleChange(visible) {
      if (visible && this.options.length === 0) {
        this.loadCorporations()
      }
    },

    handleRemoteSearch(keyword) {
      if (keyword) {
        this.searchKeyword = keyword
        this.loadCorporations(keyword)
      } else {
        this.loadCorporations()
      }
    }
  }
}
</script>

<style scoped>
::v-deep .el-select {
  width: 100%;
}

::v-deep .el-select-dropdown__item {
  height: auto;
  line-height: 1.4;
  padding: 8px 20px;
}

::v-deep .el-select-dropdown__item span {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
