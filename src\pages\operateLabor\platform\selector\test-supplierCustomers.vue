<template>
  <div class="test-supplier-customers">
    <h2>SupplierCustomers 组件测试</h2>
    
    <div class="test-section">
      <h3>单选模式</h3>
      <p>当前选中的客户ID: {{ singleValue }}</p>
      <SupplierCustomersSelector
        v-model="singleValue"
        :multiple="false"
        style="width: 300px"
      />
    </div>

    <div class="test-section">
      <h3>多选模式</h3>
      <p>当前选中的客户ID数组: {{ JSON.stringify(multipleValue) }}</p>
      <SupplierCustomersSelector
        v-model="multipleValue"
        :multiple="true"
        style="width: 300px"
      />
    </div>

    <div class="test-section">
      <h3>预设值测试</h3>
      <p>预设单选值: {{ presetSingleValue }}</p>
      <SupplierCustomersSelector
        v-model="presetSingleValue"
        :multiple="false"
        style="width: 300px"
      />
      
      <p>预设多选值: {{ JSON.stringify(presetMultipleValue) }}</p>
      <SupplierCustomersSelector
        v-model="presetMultipleValue"
        :multiple="true"
        style="width: 300px"
      />
    </div>

    <div class="test-section">
      <h3>操作按钮</h3>
      <el-button @click="resetValues">重置所有值</el-button>
      <el-button @click="setTestValues">设置测试值</el-button>
    </div>
  </div>
</template>

<script>
import SupplierCustomersSelector from './supplierCustomers.vue'

export default {
  name: 'TestSupplierCustomers',
  components: {
    SupplierCustomersSelector
  },
  data() {
    return {
      singleValue: null,
      multipleValue: [],
      presetSingleValue: 1, // 假设存在ID为1的客户
      presetMultipleValue: [1, 2] // 假设存在ID为1和2的客户
    }
  },
  methods: {
    resetValues() {
      this.singleValue = null
      this.multipleValue = []
      this.presetSingleValue = null
      this.presetMultipleValue = []
    },
    setTestValues() {
      this.singleValue = 1
      this.multipleValue = [1, 2, 3]
      this.presetSingleValue = 2
      this.presetMultipleValue = [2, 3, 4]
    }
  }
}
</script>

<style scoped>
.test-supplier-customers {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
}

.test-section p {
  margin: 10px 0;
  color: #666;
}
</style>
