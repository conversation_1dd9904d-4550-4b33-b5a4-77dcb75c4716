<template>
  <div
    class="service-contracts"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div class="lite" style="display: flex; align-items: center">
        <el-form-item label="合同">
          <el-input
            v-model="conditions.filters.name"
            placeholder="请输入合同名称"
            style="width: 280px"
          ></el-input>
        </el-form-item>
        <el-form-item label="编号">
          <el-input
            v-model="conditions.filters.sn"
            placeholder="请输入合同编号"
            style="width: 280px"
          ></el-input>
        </el-form-item>
        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
          <el-button type="text" @click="toggleExpanded">
            {{ expanded ? '收起' : '展开' }}
          </el-button>
        </div>
      </div>
      
      <!-- 展开的搜索条件 -->
      <div v-if="expanded" style="margin-top: 10px">
        <el-form-item label="作业主体">
          <el-input
            v-model="conditions.filters.supplierCorporationName"
            placeholder="请输入作业主体"
            style="width: 280px"
          ></el-input>
        </el-form-item>
        <el-form-item label="客户">
          <el-input
            v-model="conditions.filters.customerName"
            placeholder="请输入客户名称"
            style="width: 280px"
          ></el-input>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="createTimeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="handleCreateTimeChange"
            style="width: 280px"
          ></el-date-picker>
        </el-form-item>
      </div>
    </el-form>
    
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus" />
        新建合同
      </el-button>
    </div>
    
    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="sn"
        label="合同编号"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="合同名称"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="customerName"
        label="客户"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="supplierCorporationName"
        label="作业主体"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="startDate"
        label="开始时间"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="endDate"
        label="结束时间"
        width="120"
      ></el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="getStatusType(scope.row)"
            size="small"
          >
            {{ getStatusText(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)">
            查看
          </el-button>
          <el-button 
            type="text" 
            size="small" 
            @click="handleStop(scope.row)"
            v-if="!scope.row.stopped"
          >
            提前中止
          </el-button>
          <el-button type="text" size="small" @click="handleEdit(scope.row)">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      expanded: false,
      createTimeRange: [],
      conditions: {
        start: 0,
        offset: 0,
        limit: 10,
        // sorts: [
        //   {
        //     field: '',
        //     direction: ''
        //   }
        // ],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: '',
          sn: '',
          customerName: '',
          supplierCorporationName: '',
          status: '',
          createTimeStart: '',
          createTimeEnd: ''
        }
      },
      total: 0,
      data: [],
      loading: true
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    toggleExpanded() {
      this.expanded = !this.expanded
    },
    
    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.conditions.filters.createTimeStart = value[0]
        this.conditions.filters.createTimeEnd = value[1]
      } else {
        this.conditions.filters.createTimeStart = ''
        this.conditions.filters.createTimeEnd = ''
      }
    },
    
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    
    onReset() {
      this.expanded = false
      this.createTimeRange = []
      this.conditions = {
        start: 0,
        offset: 0,
        limit: 10,
        // sorts: [
        //   {
        //     field: '',
        //     direction: ''
        //   }
        // ],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: '',
          sn: '',
          customerName: '',
          supplierCorporationName: '',
          status: '',
          createTimeStart: '',
          createTimeEnd: ''
        }
      }
      this.getList()
    },
    
    async getList() {
      this.loading = true

      const [err, r] = await client.listContract({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    
    getStatusType(row) {
      if (row.stopped) {
        return 'danger'
      }
      // TODO: 根据实际业务逻辑判断状态
      const now = new Date()
      const endDate = new Date(row.endDate)
      
      if (endDate < now) {
        return 'info' // 已过期
      }
      return 'success' // 正常
    },
    
    getStatusText(row) {
      if (row.stopped) {
        return '已中止'
      }
      // TODO: 根据实际业务逻辑判断状态
      const now = new Date()
      const endDate = new Date(row.endDate)
      
      if (endDate < now) {
        return '已过期'
      }
      return '正常'
    },
    
    handleAdd() {
      // TODO: 实现新建合同功能
      console.log('新建合同')
    },
    
    handleView(row) {
      // TODO: 实现查看合同详情功能
      console.log('查看合同:', row)
    },
    
    handleStop(row) {
      // TODO: 实现提前中止合同功能
      console.log('提前中止合同:', row)
    },
    
    handleEdit(row) {
      // TODO: 实现编辑合同功能
      console.log('编辑合同:', row)
    }
  }
}
</script>
