<template>
  <div
    class="service-contracts"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 10px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div
        class="lite"
        v-if="!fullShown"
        style="display: flex; align-items: center"
      >
        <div>
          <el-form-item label="合同">
            <el-input
              v-model="conditions.filters.name"
              placeholder="请输入合同名称"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="createTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="handleCreateTimeChange"
              style="width: 280px"
            ></el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="编号">
            <el-input
              v-model="conditions.filters.id"
              placeholder="请输入合同编号"
              style="width: 280px"
            ></el-input>
          </el-form-item> -->
          <el-button
            type="text"
            @click="fullShown = true"
            style="position: relative; top: 5px"
            >展开</el-button
          >
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="full" v-else>
        <div>
          <el-form-item label="合同">
            <el-input
              v-model="conditions.filters.name"
              placeholder="请输入合同名称"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="createTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="handleCreateTimeChange"
              style="width: 280px"
            ></el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="编号">
            <el-input
              v-model="conditions.filters.id"
              placeholder="请输入合同编号"
              style="width: 280px"
            ></el-input>
          </el-form-item> -->
        </div>
        <div>
          <el-form-item label="作业主体">
            <el-select
              filterable
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请选择作业主体"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="corp in corporationOptions"
                :key="corp.id"
                :label="corp.name"
                :value="corp.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户">
            <el-select
              filterable
              v-model="conditions.filters.customerId"
              placeholder="请选择客户"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="customer in customerOptions"
                :key="customer.id"
                :label="customer.name"
                :value="customer.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div>
          
          <el-form-item label="状态">
            <el-select
              v-model="conditions.filters.status"
              placeholder="请选择状态"
              style="width: 280px"
              clearable
            >
              <el-option label="服务中" value="INIT"></el-option>
              <el-option label="提前终止" value="TERMINATION"></el-option>
              <el-option label="已到期" value="EXPIRED"></el-option>
            </el-select>
          </el-form-item>
          <el-button
            type="text"
            style="position: relative; top: 5px"
            @click="fullShown = false"
            >收起</el-button
          >
        </div>
        <el-form-item label=" ">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </el-form-item>
      </div>
    </el-form>

    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus" />
        添加合同
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="合同编号"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="合同名称"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="customerName"
        label="客户"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="supplierName"
        label="作业主体"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="startDate"
        label="开始时间"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="endDate"
        label="结束时间"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        width="150"
        label="创建日期"
      ></el-table-column>
      <el-table-column prop="status" width="120" label="状态">
        <template slot-scope="scope">
          <span :class="['status-tag', getStatusClass(scope.row.status)]">
            {{ getStatusText(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="180">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)">
            查看
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleStop(scope.row)"
            v-if="scope.row.status === 'INIT'"
          >
            提前终止
          </el-button>
          <el-button type="text" size="small" @click="handleEdit(scope.row)">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>

    <!-- 提前终止对话框 -->
    <el-dialog
      title="提前终止合同"
      :visible.sync="terminateDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="terminateForm"
        :rules="terminateRules"
        ref="terminateForm"
        label-width="100px"
      >
        <el-form-item label="合同编号">
          <el-input
            v-model="terminateForm.contractId"
            :disabled="true"
            style="width: 100%"
          ></el-input>
        </el-form-item>

        <el-form-item label="合同名称">
          <el-input
            v-model="terminateForm.contractName"
            :disabled="true"
            style="width: 100%"
          ></el-input>
        </el-form-item>

        <el-form-item label="终止原因" prop="stopReason">
          <el-input
            v-model="terminateForm.stopReason"
            type="textarea"
            :rows="4"
            placeholder="请输入提前终止原因"
            style="width: 100%"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="terminateDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmTerminate"
          :loading="terminateLoading"
          >确定终止</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      fullShown: false,
      createTimeRange: [],
      // 客户选项
      customerOptions: [],
      // 业务主体选项
      corporationOptions: [],
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: '',
          id: '',
          supplierCorporationId: '',
          customerId: '',
          createTimeStart: null,
          createTimeEnd: null,
          status: ''
        }
      },
      total: 0,
      data: [],
      loading: true,
      // 提前终止相关数据
      terminateDialogVisible: false,
      terminateLoading: false,
      terminateForm: {
        id: null,
        contractId: '',
        contractName: '',
        stopReason: ''
      },
      terminateRules: {
        stopReason: [
          { required: true, message: '请输入提前终止原因', trigger: 'blur' }
        ]
      }
    }
  },
  async created() {
    await this.loadCustomerOptions()
    await this.loadCorporationOptions()
    await this.getList()
  },
  methods: {
    // 加载客户选项
    async loadCustomerOptions() {
      const conditions = {
        filters: {
          corporationIds: [],
        }
      }
      try {
        const [err, response] = await client.supplierListCustomer({
          body: { filters: conditions.filters }
        })

        if (response.success && response.data) {
          this.customerOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载客户选项失败：', error)
      }
    },

    // 加载业务主体选项
    async loadCorporationOptions() {
      const conditions = {
        filters: {
          corporationIds: []
        }
      }
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: conditions.filters }
        })

        if (response.success && response.data) {
          this.corporationOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载业务主体选项失败：', error)
      }
    },

    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.conditions.filters.createTimeStart = value[0]
        this.conditions.filters.createTimeEnd = value[1]
      } else {
        this.conditions.filters.createTimeStart = null
        this.conditions.filters.createTimeEnd = null
      }
    },

    onSearch() {
      this.getList()
    },

    onReset() {
      // this.fullShown = false
      this.createTimeRange = []
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: '',
          id: '',
          supplierCorporationId: '',
          customerId: '',
          createTimeStart: null,
          createTimeEnd: null,
          status: ''
        }
      }
      this.getList()
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.supplierListContract({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    getStatusText(status) {
      const statusMap = {
        INIT: '服务中',
        TERMINATION: '提前终止',
        EXPIRED: '已到期'
      }
      return statusMap[status] || status
    },

    getStatusClass(status) {
      const classMap = {
        INIT: 'status-ongoing',
        TERMINATION: 'status-stopped',
        EXPIRED: 'status-negotiation'
      }
      return classMap[status] || 'status-default'
    },

    handleAdd() {
      this.$router.push('/serviceContracts/new')
    },

    handleView(row) {
      this.$router.push(`/serviceContracts/${row.id}`)
    },

    handleStop(row) {
      // 设置表单数据
      this.terminateForm.id = row.id
      this.terminateForm.contractId = row.id
      this.terminateForm.contractName = row.name
      this.terminateForm.stopReason = ''

      // 显示对话框
      this.terminateDialogVisible = true

      // 重置表单验证状态
      this.$nextTick(() => {
        if (this.$refs.terminateForm) {
          this.$refs.terminateForm.clearValidate()
        }
      })
    },

    async confirmTerminate() {
      // 表单验证
      const valid = await new Promise(resolve => {
        this.$refs.terminateForm.validate(resolve)
      })

      if (!valid) {
        return
      }

      this.terminateLoading = true

      try {
        const [err, response] = await client.terminateContract({
          body: {
            id: this.terminateForm.id,
            stopReason: this.terminateForm.stopReason
          }
        })

        this.terminateLoading = false

        if (err) {
          handleError(err)
          return
        }

        if (response.success) {
          this.$message.success('合同提前终止成功')
          this.terminateDialogVisible = false
          // 刷新列表
          await this.getList()
        } else {
          this.$message.error(response.message || '操作失败')
        }
      } catch (error) {
        this.terminateLoading = false
        console.error('提前终止合同失败：', error)
        this.$message.error('操作失败，请稍后重试')
      }
    },

    handleEdit(row) {
      this.$router.push(`/serviceContracts/edit/${row.id}`)
    }
  }
}
</script>

<style scoped>
.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
}

.status-ongoing {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-stopped {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-negotiation {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-default {
  background-color: #fafafa;
  color: #666666;
  border: 1px solid #d9d9d9;
}
</style>
