<template>
  <div>
    <el-form :model="form" :rules="rules" ref="form" label-width="120px" style="width: 1000px; margin-left: 50px;">

      <Title title="基本信息" />
      
      <!-- 第一行：合同名称 和 客户 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="合同名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入合同名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户" prop="customerId">
            <el-select v-model="form.customerId" placeholder="请选择客户" style="width: 100%">
              <el-option
                v-for="customer in customerOptions"
                :key="customer.id"
                :label="customer.name"
                :value="customer.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：合同期限 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="合同期限" prop="timeFixed">
            <el-radio-group v-model="form.timeFixed">
              <el-radio :label="true">固定期限</el-radio>
              <el-radio :label="false">无固定期限</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="作业主体" prop="supplierCorporationId">
            <el-select v-model="form.supplierCorporationId" placeholder="请选择作业主体" style="width: 100%">
              <el-option
                v-for="corp in corporationOptions"
                :key="corp.id"
                :label="corp.name"
                :value="corp.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：合同有效期 -->
      <el-row :gutter="40" v-if="form.timeFixed">
        <el-col :span="12">
          <el-form-item label="合同有效期" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              @change="handleDateRangeChange">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="操作角色" prop="roleIds">
        <el-select v-model="form.roleIds" placeholder="请选择操作角色" multiple style="width: 100%">
          <el-option
            v-for="role in roleOptions"
            :key="role.id"
            :label="role.name"
            :value="role.id">
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 合同附件上传 -->
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="合同附件上传">
            <div style="display: inline-flex; flex-direction: column; align-items: center;">
              <FileUploader v-model="form.fileIds" :max="5" name="上传文件" />
              <div style="margin-top: 8px; font-size: 12px; color: #999;">支持多个文件上传</div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <Title title="发票信息" />
      
      <!-- 发票信息第一行 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="抬头" prop="invoiceTitle">
            <el-input v-model="form.invoiceTitle" placeholder="请输入发票抬头"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纳税人识别号" prop="invoiceTaxNo">
            <el-input v-model="form.invoiceTaxNo" placeholder="请输入纳税人识别号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 发票信息第二行 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="开户行" prop="invoiceBankName">
            <el-input v-model="form.invoiceBankName" placeholder="请输入开户行"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账号" prop="invoiceBankAccount">
            <el-input v-model="form.invoiceBankAccount" placeholder="请输入银行账号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 发票信息第三行 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="注册地址" prop="invoiceRegisterAddress">
            <el-input v-model="form.invoiceRegisterAddress" placeholder="请输入注册地址"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业电话" prop="invoiceCompanyTel">
            <el-input v-model="form.invoiceCompanyTel" placeholder="请输入企业电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 发票备注 -->
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="发票备注" prop="invoiceRemark">
            <el-input v-model="form.invoiceRemark" placeholder="请输入发票备注"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <Title title="业务配置" />
      
      <!-- 业务配置 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="计算规则" prop="manageCalculationRule">
            <el-select v-model="form.manageCalculationRule" placeholder="按服务人数计算" style="width: 100%">
              <el-option label="按服务人数计算" value="BY_PERSON_COUNT"></el-option>
              <el-option label="按发生金额计算" value="BY_AMOUNT"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计算规则" prop="manageCalculationRule2">
            <el-select v-model="form.manageCalculationRule2" placeholder="按发生金额计算" style="width: 100%">
              <el-option label="按发生金额计算" value="BY_AMOUNT"></el-option>
              <el-option label="按服务人数计算" value="BY_PERSON_COUNT"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 金额和费率 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="金额" prop="manageAmount">
            <el-input v-model="form.manageAmount" placeholder="请输入服务费金额">
              <template slot="append">元/人月</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="费率" prop="manageRate">
            <el-input v-model="form.manageRate" placeholder="请输入费率分比">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 操作按钮 -->
      <el-form-item style="margin-top: 40px;">
        <el-button type="primary" @click="onSubmit" :loading="submitting">保存</el-button>
        <el-button @click="onCancel">取消</el-button>
      </el-form-item>

    </el-form>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'
import FileUploader from './uploader/file.vue'

const client = makeClient()

export default {
  components: { Title, FileUploader },
  data() {
    return {
      submitting: false,
      dateRange: null,
      
      // 客户选项
      customerOptions: [],

      // 业务主体选项
      corporationOptions: [],

      // 操作角色选项列表
      roleOptions: [],

      form: {
        customerId: '',
        supplierCorporationId: '',
        name: '',
        timeFixed: true,
        startDate: '',
        endDate: '',
        remark: '',
        fileIds: '',
        invoiceTitle: '',
        invoiceTaxNo: '',
        invoiceBankName: '',
        invoiceBankAccount: '',
        invoiceRegisterAddress: '',
        invoiceCompanyTel: '',
        invoiceRemark: '',
        manageCalculationRule: '',
        manageCalculationRule2: '',
        manageAmount: '',
        manageRate: '',
        businessType: '',
        stopped: false,
        stopTime: '',
        stopReason: '',
        roleIds: []
      },

      rules: {
        name: [
          { required: true, message: '请输入合同名称', trigger: 'blur' }
        ],
        customerId: [
          { required: true, message: '请选择客户', trigger: 'change' }
        ],
        supplierCorporationId: [
          { required: true, message: '请选择作业主体', trigger: 'change' }
        ],
        timeFixed: [
          { required: true, message: '请选择合同期限类型', trigger: 'change' }
        ]
      }
    }
  },

  async created() {
    await this.loadCustomerOptions()
    await this.loadCorporationOptions()
    await this.loadRoleOptions()
  },

  methods: {
    // 加载客户选项
    async loadCustomerOptions() {
      try {
        const [err, response] = await client.queryCustomerBySupplier({
          body: {}
        })

        if (response.success && response.data) {
          this.customerOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载客户选项失败：', error)
      }
    },

    // 加载业务主体选项
    async loadCorporationOptions() {
     const conditions = {
        filters: {
          corporationIds: [],
        }
      }
      try {
       const [err, response] = await client.listCorporation({
          body: {filters: conditions.filters}
        })

        if (response.success && response.data) {
          this.corporationOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载业务主体选项失败：', error)
      }
    },

    // 加载操作角色选项
    async loadRoleOptions() {
      try {
        const conditions = {
          offset: 0,
          limit: 1000,
          sorts: [],
          withTotal: false,
          withDisabled: false,
          withDeleted: false,
          filters: {
            name: ''
          }
        }

        const [err, response] = await client.listRoles({
          body: conditions
        })

        if (response.success && response.data) {
          console.log('角色数据：', response.data)
          this.roleOptions = response.data.list || response.data || []
        } else {
          console.error('获取角色列表失败：', response.message)
          this.$message.error(response.message || '获取角色列表失败')
        }
      } catch (error) {
        console.error('加载角色选项异常：', error)
        this.$message.error('加载角色选项失败')
      }
    },

    // 处理日期范围变化
    handleDateRangeChange(dateRange) {
      if (dateRange && dateRange.length === 2) {
        this.form.startDate = this.formatDate(dateRange[0])
        this.form.endDate = this.formatDate(dateRange[1])
      } else {
        this.form.startDate = ''
        this.form.endDate = ''
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 提交表单
    async onSubmit() {
      try {
        // 表单验证
        const valid = await this.$refs.form.validate()
        if (!valid) {
          return
        }

        this.submitting = true

        // 调用新增接口
        const response = await client.addContract({
          body: this.form
        })

        if (response.success) {
          this.$message.success('合同创建成功')
          // 返回列表页面
          this.$router.push('/serviceContracts')
        } else {
          this.$message.error(response.message || '创建失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.submitting = false
      }
    },

    // 取消
    onCancel() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
</style>
