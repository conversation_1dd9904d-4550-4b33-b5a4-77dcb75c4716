<template>
  <div class="startSign">
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="120px"
      style="width: 800px"
    >
      <!-- 选择模板 -->
      <el-form-item label="选择模板" prop="templateId">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-input
            v-model="selectedTemplateName"
            placeholder="请选择模板"
            readonly
            style="flex: 1"
          />
          <el-button type="primary" @click="showTemplateDialog"
            >选择模板</el-button
          >
        </div>
      </el-form-item>

      <Title title="基本信息" />

      <el-form-item label="文件名称" prop="fileName">
        <el-input v-model="form.fileName" placeholder="请输入文件名称" />
      </el-form-item>

      <el-form-item label="选择作业主体" prop="corporationId">
        <CorporationsSelector
          v-model="form.corporationId"
          :multiple="false"
          placeholder="请选择作业主体"
        />
      </el-form-item>

      <el-form-item label="选择客户" prop="customerId">
        <SupplierCustomersSelector
          v-model="form.customerId"
          :multiple="false"
          placeholder="请选择客户"
        />
      </el-form-item>

      <el-form-item label="选择服务合同" prop="serviceContractId">
        <ServiceContractsSelector
          v-model="form.serviceContractId"
          :customer-id="form.customerId"
          :corporation-id="form.corporationId"
          placeholder="请选择服务合同"
        />
      </el-form-item>

      <el-form-item label="合同开始时间" prop="startDate">
        <el-date-picker
          v-model="form.startDate"
          type="date"
          placeholder="请选择开始时间"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="合同结束时间" prop="endDate">
        <el-date-picker
          v-model="form.endDate"
          type="date"
          placeholder="请选择结束时间"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="width: 100%"
        />
      </el-form-item>

      <Title title="配置流程" />

      <div style="font-size: 14px">
        <span style="color: #f8605b; margin: 0 4px 0 10px">*</span>
        <span>签署顺序</span>
      </div>
      <div class="sign-process">
        <div class="sign-parties">
          <!-- 企业签署方 -->
          <div class="sign-party">
            <div class="party-header">企业签署方</div>
            <div class="party-content">
              <el-button
                type="text"
                icon="el-icon-plus"
                @click="showEnterpriseSignerDialog"
                class="add-signer-btn"
              >
                选择人员
              </el-button>
              <div
                v-if="form.enterpriseSigners.length > 0"
                class="signers-list"
              >
                <div
                  v-for="(signer, index) in form.enterpriseSigners"
                  :key="index"
                  class="signer-item"
                >
                  <span>{{ signer.name }}</span>
                  <el-button
                    type="text"
                    icon="el-icon-delete"
                    @click="removeEnterpriseSigner(index)"
                    class="remove-btn"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 个人签署方 -->
          <div class="sign-party">
            <div class="party-header">个人签署方</div>
            <div class="party-content">
              <el-button
                type="text"
                icon="el-icon-plus"
                @click="showPersonalSignerDialog"
                class="add-signer-btn"
              >
                选择人员
              </el-button>
              <div v-if="form.personalSigners.length > 0" class="signers-list">
                <div
                  v-for="(signer, index) in form.personalSigners"
                  :key="index"
                  class="signer-item"
                >
                  <span>{{ signer.name }}</span>
                  <el-button
                    type="text"
                    icon="el-icon-delete"
                    @click="removePersonalSigner(index)"
                    class="remove-btn"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <el-form-item style="margin-top: 40px">
        <el-button type="primary" @click="onSubmit" :loading="submitting">
          发起签署
        </el-button>
        <el-button @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>

    <!-- 模板选择对话框 -->
    <TemplateDialog
      :visible.sync="templateDialogVisible"
      @confirm="handleTemplateSelect"
    />

    <el-drawer title="选择人员" :visible.sync="drawer" direction="rtl">
      <ChooseSealStaff ref="chooseSealStaffRef"></ChooseSealStaff>
    </el-drawer>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import Title from './components/title.vue'
import CorporationsSelector from './selector/corporations.vue'
import SupplierCustomersSelector from './selector/supplierCustomers.vue'
import ServiceContractsSelector from './selector/serviceContracts.vue'
import TemplateDialog from './selector/templateDialog.vue'
import ChooseSealStaff from './selector/chooseSealStaff.vue'

export default {
  name: 'StartSign',
  components: {
    Title,
    CorporationsSelector,
    SupplierCustomersSelector,
    ServiceContractsSelector,
    TemplateDialog,
    ChooseSealStaff
  },
  data() {
    return {
      form: {
        templateId: '',
        fileName: '',
        corporationId: null,
        customerId: null,
        serviceContractId: null,
        startDate: '',
        endDate: '',
        enterpriseSigners: [],
        personalSigners: []
      },
      rules: {
        templateId: [
          { required: true, message: '请选择模板', trigger: 'change' }
        ],
        fileName: [
          { required: true, message: '请输入文件名称', trigger: 'blur' }
        ],
        corporationId: [
          { required: true, message: '请选择作业主体', trigger: 'change' }
        ],
        customerId: [
          { required: true, message: '请选择客户', trigger: 'change' }
        ],
        serviceContractId: [
          { required: true, message: '请选择服务合同', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择合同开始时间', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择合同结束时间', trigger: 'change' }
        ]
      },
      selectedTemplateName: '',
      templateDialogVisible: false,
      submitting: false,
      drawer: false
    }
  },
  methods: {
    showTemplateDialog() {
      this.templateDialogVisible = true
    },
    handleTemplateSelect(template) {
      this.form.templateId = template.tempId
      this.selectedTemplateName = template.tempName
      this.$refs.form.validateField('templateId')
    },
    showEnterpriseSignerDialog() {
      this.drawer = true
    },
    showPersonalSignerDialog() {
      // TODO: 实现个人签署人选择对话框
      this.$message.info('个人签署人选择功能待实现')
    },
    removeEnterpriseSigner(index) {
      this.form.enterpriseSigners.splice(index, 1)
    },
    removePersonalSigner(index) {
      this.form.personalSigners.splice(index, 1)
    },
    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      // 验证签署人
      if (
        this.form.enterpriseSigners.length === 0 &&
        this.form.personalSigners.length === 0
      ) {
        this.$message.error('请至少选择一个签署人')
        return
      }

      this.submitting = true

      try {
        // TODO: 调用发起签署的API
        const payload = {
          templateId: this.form.templateId,
          fileName: this.form.fileName,
          corporationId: this.form.corporationId,
          customerId: this.form.customerId,
          serviceContractId: this.form.serviceContractId,
          startDate: this.form.startDate,
          endDate: this.form.endDate,
          enterpriseSigners: this.form.enterpriseSigners,
          personalSigners: this.form.personalSigners
        }

        console.log('发起签署参数:', payload)

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        this.$message.success('发起签署成功')
        this.$router.back()
      } catch (error) {
        handleError(error)
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.startSign {
  padding: 20px;
}

.sign-process {
  margin: 20px 0;
}

.sign-parties {
  display: flex;
  gap: 40px;
}

.sign-party {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.party-header {
  background: #f5f7fa;
  padding: 15px 20px;
  font-weight: 500;
  color: #303133;
  text-align: center;
}

.party-content {
  padding: 20px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.add-signer-btn {
  color: #409eff;
  font-size: 14px;
}

.signers-list {
  margin-top: 15px;
  width: 100%;
}

.signer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 8px;
}

.signer-item:last-child {
  margin-bottom: 0;
}

.remove-btn {
  color: #f56c6c;
  padding: 0;
}
</style>
