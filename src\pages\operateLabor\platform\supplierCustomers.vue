<template>
  <div
    class="supplierCustomers"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 10px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div
        class="lite"
        v-if="!fullShown"
        style="display: flex; align-items: center"
      >
        <div>
          <el-form-item label="客户">
            <el-input
              v-model="conditions.filters.name"
              placeholder="请输入客户名称"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="编号">
            <el-input
              v-model="conditions.filters.id"
              placeholder="请输入客户编号"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-button
            type="text"
            @click="fullShown = true"
            style="position: relative; top: 5px"
            >展开</el-button
          >
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="full" v-else>
        <div>
          <el-form-item label="客户">
            <el-input
              v-model="conditions.filters.name"
              placeholder="请输入客户名称"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="编号">
            <el-input
              v-model="conditions.filters.id"
              placeholder="请输入客户编号"
              style="width: 280px"
            ></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="conditions.filters.createTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 280px"
            ></el-date-picker>
          </el-form-item>
          <el-button
            type="text"
            style="position: relative; top: 5px"
            @click="fullShown = false"
            >收起</el-button
          >
        </div>
        <el-form-item label=" ">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </el-form-item>
      </div>
    </el-form>
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleAddCustomer">
        <i class="el-icon-plus" />
        添加客户
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="客户编号"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="客户名称"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="address"
        label="详细地址"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="enterpriseContacts"
        label="客户联系人"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="createTime"
        width="150"
        label="创建日期"
      ></el-table-column>
      <el-table-column prop="status" width="150" label="状态">
        <template slot-scope="scope">
          <span :class="['status-tag', getStatusClass(scope.row.status)]">
            {{ getStatusText(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleUpdateStatus(scope.row)"
            >更新状态</el-button
          >
          <el-button type="text" size="small" @click="handleEdit(scope.row)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>

    <!-- 修改状态对话框 -->
    <el-dialog
      title="更新客户状态"
      :visible.sync="statusDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="statusForm" label-width="100px">
        <el-form-item label="客户编号">
          <el-input
            v-model="statusForm.id"
            readonly
            style="width: 300px"
          ></el-input>
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input
            v-model="statusForm.name"
            readonly
            style="width: 300px"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" required>
          <el-select
            v-model="statusForm.status"
            placeholder="请选择状态"
            style="width: 300px"
          >
            <el-option
              v-for="(label, value) in statusOptions"
              :key="value"
              :label="label"
              :value="value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="statusDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmUpdateStatus"
          :loading="statusUpdating"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      fullShown: false,
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: '',
          id: '',
          createTimeRange: [],
          salesName: '',
          serviceMobile: '',
          regionId: '',
          status: ''
        }
      },
      total: 0,
      data: [],
      loading: true,
      // 状态修改对话框相关
      statusDialogVisible: false,
      statusUpdating: false,
      statusForm: {
        id: '',
        name: '',
        status: ''
      },
      statusOptions: {
        NOT: '未合作',
        NEGOTIATION: '洽谈中',
        ONGOING: '合作中',
        STOPPED: '停止合作'
      }
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    getStatusText(status) {
      const statusMap = {
        NOT: '未合作',
        NEGOTIATION: '洽谈中',
        ONGOING: '合作中',
        STOPPED: '停止合作'
      }
      return statusMap[status] || status
    },

    getStatusClass(status) {
      const classMap = {
        NOT: 'status-not',
        NEGOTIATION: 'status-negotiation',
        ONGOING: 'status-ongoing',
        STOPPED: 'status-stopped'
      }
      return classMap[status] || 'status-default'
    },

    onSearch() {
      this.getList()
    },
    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: '',
          sn: '',
          createTimeRange: [],
          salesName: '',
          serviceMobile: '',
          regionId: '',
          status: ''
        }
      }

      this.getList()
    },
    async getList() {
      this.loading = true

      const queryConditions = { ...this.conditions }

      if (queryConditions.filters.createTimeRange?.length === 2) {
        queryConditions.filters.createTimeStart =
          queryConditions.filters.createTimeRange[0] || null
        queryConditions.filters.createTimeEnd =
          queryConditions.filters.createTimeRange[1] || null
      } else {
        queryConditions.filters.createTimeStart = null
        queryConditions.filters.createTimeEnd = null
      }

      delete queryConditions.filters.createTimeRange

      const [err, r] = await client.supplierListCustomer({
        body: queryConditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCurrentChange(page) {
      this.getList()
    },
    handleAddCustomer() {
      this.$router.push('/supplierCustomers/new')
    },
    handleView(row) {
      this.$router.push(`/supplierCustomers/${row.id}`)
    },
    handleEdit(row) {
      this.$router.push(`/supplierCustomers/${row.id}/edit`)
    },

    // 打开修改状态对话框
    handleUpdateStatus(row) {
      this.statusForm = {
        id: row.id,
        name: row.name,
        status: row.status
      }
      this.statusDialogVisible = true
    },

    // 确认修改状态
    async confirmUpdateStatus() {
      if (!this.statusForm.status) {
        this.$message.error('请选择状态')
        return
      }

      this.statusUpdating = true

      try {
        const [err, response] = await client.updateCustomerStatus({
          body: {
            id: this.statusForm.id,
            status: this.statusForm.status
          }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success) {
          this.$message.success('客户状态修改成功')
          this.statusDialogVisible = false
          // 刷新列表
          await this.getList()
        } else {
          this.$message.error(response.message || '修改失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.statusUpdating = false
      }
    }
  }
}
</script>

<style scoped>
.status-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.status-not {
  background-color: #f5f5f5;
  color: #666666;
  border: 1px solid #d9d9d9;
}

.status-negotiation {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-ongoing {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-stopped {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-default {
  background-color: #fafafa;
  color: #666666;
  border: 1px solid #d9d9d9;
}
</style>
