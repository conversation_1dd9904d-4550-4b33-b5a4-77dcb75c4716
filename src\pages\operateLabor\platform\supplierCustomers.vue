<template>
  <div
    class="supplierCustomers"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div
        class="lite"
        v-if="!fullShown"
        style="display: flex; align-items: center"
      >
        <div>
          <el-form-item label="客户">
            <el-input
              v-model="conditions.filters.name"
              placeholder="请输入客户名称"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="编号">
            <el-input
              v-model="conditions.filters.id"
              placeholder="请输入客户编号"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-button
            type="text"
            @click="fullShown = true"
            style="position: relative; top: 5px"
            >展开</el-button
          >
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="full" v-else>
        <div>
          <el-form-item label="客户">
            <el-input
              v-model="conditions.filters.name"
              placeholder="请输入客户名称"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="编号">
            <el-input
              v-model="conditions.filters.id"
              placeholder="请输入客户编号"
              style="width: 280px"
            ></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="conditions.filters.createTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 280px"
            ></el-date-picker>
          </el-form-item>
          <el-button
            type="text"
            style="position: relative; top: 5px"
            @click="fullShown = false"
            >收起</el-button
          >
        </div>
        <el-form-item label=" ">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </el-form-item>
      </div>
    </el-form>
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px; margin-bottom: 10px;">
      <el-button type="primary" @click="handleAddCustomer">
        <i class="el-icon-plus" />
        新增客户
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="客户编号"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="客户名称"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column 
        prop="address" 
        label="详细地址"  
        width="300"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column 
        prop="enterpriseContacts" 
        label="客户联系人"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="createTime" width="200" label="创建日期"></el-table-column>
      <el-table-column prop="status" width="200" label="状态">
        <template slot-scope="scope">
          <span :class="['status-tag', getStatusClass(scope.row.status)]">
            {{ getStatusText(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small">查看</el-button>
          <el-button type="text" size="small">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      fullShown: false,
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: '',
          id: '',
          createTimeRange: [],
          salesName: '',
          serviceMobile: '',
          regionId: '',
          status: ''
        }
      },
      total: 0,
      data: [],
      loading: true
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    getStatusText(status) {
      const statusMap = {
        'NOT': '未合作',
        'NEGOTIATION': '洽谈中',
        'ONGOING': '合作中',
        'STOPPED': '停止合作'
      }
      return statusMap[status] || status
    },

    getStatusClass(status) {
      const classMap = {
        'NOT': 'status-not',
        'NEGOTIATION': 'status-negotiation',
        'ONGOING': 'status-ongoing',
        'STOPPED': 'status-stopped'
      }
      return classMap[status] || 'status-default'
    },

    onSearch() {
      this.conditions.offet = 0
      this.getList()
    },
    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: '',
          sn: '',
          createTimeRange: [],
          salesName: '',
          serviceMobile: '',
          regionId: '',
          status: ''
        }
      }

      this.getList()
    },
    async getList() {
      this.loading = true

      const queryConditions = { ...this.conditions }

      if (queryConditions.filters.createTimeRange?.length === 2) {
        queryConditions.filters.createTimeStart = queryConditions.filters.createTimeRange[0] || null
        queryConditions.filters.createTimeEnd = queryConditions.filters.createTimeRange[1] || null
      } else {
        queryConditions.filters.createTimeStart = null
        queryConditions.filters.createTimeEnd = null
      }

      delete queryConditions.filters.createTimeRange

      const [err, r] = await client.supplierListCustomer({
        body: queryConditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCurrentChange(page) {
      this.conditions.offet = (page - 1) * this.conditions.limit
      this.getList()
    },
    handleAddCustomer() {
      this.$router.push('/supplierCustomers/new')
    }
  }
}
</script>

<style scoped>
.status-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.status-not {
  background-color: #f5f5f5;
  color: #666666;
  border: 1px solid #d9d9d9;
}

.status-negotiation {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-ongoing {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-stopped {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-default {
  background-color: #fafafa;
  color: #666666;
  border: 1px solid #d9d9d9;
}
</style>
