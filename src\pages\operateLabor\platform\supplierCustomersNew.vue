<template>
  <div class="supplierCustomersNew">
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="120px"
      style="width: 800px"
    >
      <Title title="联系人信息" />
      <el-form-item label="联系人姓名" prop="contactName">
        <el-input
          v-model="form.contactName"
          placeholder="请输入联系人姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系人手机" prop="contactMobile">
        <el-input
          v-model="form.contactMobile"
          placeholder="请输入联系人手机"
        ></el-input>
      </el-form-item>

      <Title title="操作角色" />
      <el-form-item label="操作角色" prop="operationRoleIds">
        <el-select
          v-model="form.operationRoleIds"
          placeholder="请选择操作角色"
          multiple
          style="width: 100%"
        >
          <!-- 操作角色选项将在这里动态加载 -->
        </el-select>
      </el-form-item>

      <Title title="基本信息" />
      <el-form-item label="客户名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入客户名称"></el-input>
      </el-form-item>

      <el-form-item label="营业执照照片" prop="businessLicenseImage">
        <ImagerUploader :width="300" :height="300" />
        <div
          style="
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            width: 300px;
            height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background-color: #fafafa;
          "
        >
          <i class="el-icon-plus" style="font-size: 28px; color: #8c939d"></i>
          <div style="margin-top: 8px; color: #8c939d">上传营业执照</div>
        </div>
        <div style="margin-top: 8px; color: #999; font-size: 12px">
          营业执照上传
        </div>
      </el-form-item>

      <el-form-item label="公司名称">
        <el-input
          v-model="form.name"
          placeholder="请输入公司名称"
          disabled
        ></el-input>
      </el-form-item>

      <el-form-item label="统一社会信用代码" prop="socialCreditCode">
        <el-input
          v-model="form.socialCreditCode"
          placeholder="请输入统一社会信用代码"
        ></el-input>
      </el-form-item>

      <el-form-item label="公司注册地址" prop="registerAddress">
        <el-input
          v-model="form.registerAddress"
          placeholder="请输入公司注册地址"
        ></el-input>
      </el-form-item>

      <Title title="法人信息" />
      <el-form-item label="法人身份照片" prop="certificateFrontImage">
        <div style="display: flex; gap: 20px">
          <div>
            <div
              style="
                border: 1px dashed #d9d9d9;
                border-radius: 6px;
                width: 150px;
                height: 100px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                background-color: #fafafa;
              "
            >
              <i
                class="el-icon-plus"
                style="font-size: 20px; color: #8c939d"
              ></i>
              <div style="margin-top: 4px; color: #8c939d; font-size: 12px">
                上传人像面
              </div>
            </div>
            <div
              style="
                text-align: center;
                margin-top: 4px;
                color: #999;
                font-size: 12px;
              "
            >
              人像面
            </div>
          </div>
          <div>
            <div
              style="
                border: 1px dashed #d9d9d9;
                border-radius: 6px;
                width: 150px;
                height: 100px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                background-color: #fafafa;
              "
            >
              <i
                class="el-icon-plus"
                style="font-size: 20px; color: #8c939d"
              ></i>
              <div style="margin-top: 4px; color: #8c939d; font-size: 12px">
                上传国徽面
              </div>
            </div>
            <div
              style="
                text-align: center;
                margin-top: 4px;
                color: #999;
                font-size: 12px;
              "
            >
              国徽面
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="法人姓名" prop="representativeName">
        <el-input
          v-model="form.representativeName"
          placeholder="请输入法人姓名"
        ></el-input>
      </el-form-item>

      <el-form-item label="法人证件类型" prop="certificateType">
        <el-select
          v-model="form.certificateType"
          placeholder="请选择证件类型"
          style="width: 100%"
        >
          <el-option label="身份证" value="ID_CARD"></el-option>
          <el-option label="护照" value="PASSPORT"></el-option>
          <el-option label="港澳通行证" value="HK_MACAO_PASS"></el-option>
          <el-option label="台湾通行证" value="TAIWAN_PASS"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="法人证件号" prop="certificateNo">
        <el-input
          v-model="form.certificateNo"
          placeholder="请输入法人证件号"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSubmit">确定</el-button>
        <el-button @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'
import ImagerUploader from './uploader/image.vue'
const client = makeClient()

export default {
  components: { Title, ImagerUploader },
  data() {
    return {
      form: {
        userId: 0,
        customerId: 0,
        businessLicenseImage: '',
        name: '',
        socialCreditCode: '',
        registerAddress: '',
        certificateFrontImage: '',
        certificateBackImage: '',
        representativeName: '',
        certificateType: 'ID_CARD',
        certificateNo: '',
        contactName: '',
        contactMobile: '',
        operationRoleIds: [],
        supplierId: 0
      },
      rules: {
        contactName: [
          { required: true, message: '请输入联系人姓名', trigger: 'blur' }
        ],
        contactMobile: [
          { required: true, message: '请输入联系人手机', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ],
        name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        socialCreditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
        ],
        registerAddress: [
          { required: true, message: '请输入公司注册地址', trigger: 'blur' }
        ],
        representativeName: [
          { required: true, message: '请输入法人姓名', trigger: 'blur' }
        ],
        certificateType: [
          { required: true, message: '请选择证件类型', trigger: 'change' }
        ],
        certificateNo: [
          { required: true, message: '请输入法人证件号', trigger: 'blur' }
        ]
      }
    }
  },
  async created() {
    // 这里可以加载操作角色选项等初始化数据
  },
  methods: {
    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      const [err] = await client.addCustomer({
        body: this.form
      })

      if (err) {
        handleError(err)
        return
      }

      this.$message.success('创建成功')
      this.$router.push('/supplier-customers')
    }
  }
}
</script>
