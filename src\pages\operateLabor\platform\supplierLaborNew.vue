<template>
  <div>
    <el-form :model="form" :rules="rules" ref="form" label-width="120px" style="width: 1000px; margin-left: 50px;">
      
      <Title title="添加人员" />

      <!-- 第一行：姓名 和 身份证件号码 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证件号码" prop="idCard">
            <el-input v-model="form.idCard" placeholder="请输入身份证件号码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：银行卡号 和 手机号 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="银行卡号" prop="bankCard">
            <el-input v-model="form.bankCard" placeholder="请输入银行卡号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="cellPhone">
            <el-input v-model="form.cellPhone" placeholder="请输入手机号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：所属作业主体 和 所属客户 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="所属作业主体" prop="supplierId">
            <el-select
              filterable
              v-model="form.supplierId"
              placeholder="请选择所属作业主体"
              style="width: 100%"
              @change="onSupplierChange"
            >
              <el-option v-for="supplier in supplierOptions" :key="supplier.id" :label="supplier.name"
                :value="supplier.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属客户" prop="customerId">
            <el-select
              filterable
              v-model="form.customerId"
              placeholder="请选择所属客户"
              style="width: 100%"
              @change="onCustomerChange"
            >
              <el-option v-for="customer in customerOptions" :key="customer.id" :label="customer.name"
                :value="customer.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属服务合同" prop="contractId">
            <el-select
              filterable
              v-model="form.contractId"
              placeholder="请选择所属服务合同"
              style="width: 100%"
              :disabled="!form.supplierId || !form.customerId"
            >
              <el-option v-for="contract in contractOptions" :key="contract.id" :label="contract.name"
                :value="contract.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>


      <!-- 操作按钮 -->
      <div style="text-align: center; margin-top: 40px;">
          <el-form-item label-width="0">
            <el-button type="default" @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onSubmit" :loading="submitting">确定</el-button>
          </el-form-item>
      </div>

    </el-form>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'

const client = makeClient()

export default {
  components: { Title },
  data() {
    return {
      submitting: false,
      // 选项数据
      supplierOptions: [],
      customerOptions: [],
      contractOptions: [],

      form: {
        name: '',
        idCard: '',
        bankCard: '',
        cellPhone: '',
        supplierId: '',
        customerId: '',
        contractId: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        idCard: [
          { required: true, message: '请输入身份证件号码', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的18位身份证号码', trigger: 'blur' }
        ],
        bankCard: [
          { required: true, message: '请输入银行卡号', trigger: 'blur' },
          { pattern: /^[1-9]\d{11,19}$/, message: '请输入正确的12-20位银行卡号', trigger: 'blur' }
        ],

        cellPhone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号码', trigger: 'blur' }
        ],
        supplierId: [
          { required: true, message: '请选择所属作业主体', trigger: 'change' }
        ],
        customerId: [
          { required: true, message: '请选择所属客户', trigger: 'change' }
        ],
        contractId: [
          { required: true, message: '请选择所属服务合同', trigger: 'change' }
        ]
      }
    }
  },
  async created() {
    await this.loadSupplierOptions()
    await this.loadCustomerOptions()
  },

  methods: {
    // 加载业务主体选项
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载业务主体选项失败：', error)
      }
    },

    // 加载客户选项
    async loadCustomerOptions() {
      const conditions = {
        filters: {
          corporationIds: [],
        }
      }
      try {
        const [err, response] = await client.supplierListCustomer({
          body: { filters: conditions.filters }
        })

        if (response && response.success && response.data) {
          this.customerOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载客户选项失败：', error)
      }
    },

    // 加载合同选项
    async loadContractOptions() {
      if (!this.form.supplierId || !this.form.customerId) {
        this.contractOptions = []
        return
      }

      try {
        const [err, response] = await client.listContractByCustomerAndCorporation({
          body: { filters: {  
            customerId: this.form.customerId,
            supplierCorporationId: this.form.supplierId
          } }
        })

        if (response && response.success && response.data) {
          this.contractOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载合同选项失败：', error)
        this.contractOptions = []
      }
    },

    // 业务主体变化时
    onSupplierChange() {
      this.form.contractId = ''
      this.loadContractOptions()
    },

    // 客户变化时
    onCustomerChange() {
      this.form.contractId = ''
      this.loadContractOptions()
    },

    // 提交表单
    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      try {
        this.submitting = true

        // 根据后端SupplierLaborVo字段映射转换数据
        const submitData = {
          name: this.form.name,
          idCard: this.form.idCard,
          bankCard: this.form.bankCard,  // 银行卡号
          cellphone: this.form.cellPhone,
          corporationId: this.form.supplierId,
          customerId: this.form.customerId,
          contractId: this.form.contractId,
        }

        const [err, response] = await client.addLabor({
          body: submitData
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success) {
          this.$message.success('人员添加成功')
          this.$router.push('/supplierLabor')
        } else {
          this.$message.error(response?.message || '添加失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.submitting = false
      }
    },

    // 取消
    onCancel() {
      this.$router.back()
    }
  }
}
</script>

<style scoped></style>
