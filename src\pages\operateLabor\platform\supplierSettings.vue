<template>
  <div class="supplier-settings">
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="120px"
      style="width: 800px"
    >
      <!-- 基本信息 -->
      <Title title="基本信息" />
      <el-form-item label="企业名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入企业名称"></el-input>
      </el-form-item>
      <el-form-item label="统一信用代码" prop="socialCreditCode">
        <el-input
          v-model="form.socialCreditCode"
          placeholder="请输入统一信用代码"
        ></el-input>
      </el-form-item>

      <!-- 品牌信息 -->
      <Title title="品牌信息" />
      <el-form-item label="品牌名称" prop="brandName">
        <el-input
          v-model="form.brandName"
          placeholder="请输入品牌名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="全域名" prop="domainName">
        <el-input
          v-model="form.domainName"
          placeholder="请输入全域名"
        ></el-input>
      </el-form-item>
      <el-form-item label="企业 logo" prop="logoUrl">
        <ImageUploader
          v-model="form.logoUrl"
          :max="1"
          name="上传企业logo"
          :width="120"
          :height="120"
        />
      </el-form-item>
      <el-form-item label="短信签名" prop="signatureCode">
        <el-input
          v-model="form.signatureCode"
          placeholder="请输入短信签名"
        ></el-input>
      </el-form-item>

      <!-- 灵工市场配置 -->
      <Title title="灵工市场配置" />
      <el-form-item label="登录页 logo" prop="h5LogoUrl">
        <ImageUploader
          v-model="form.h5LogoUrl"
          :max="1"
          name="上传登录页logo"
          :width="120"
          :height="120"
        />
      </el-form-item>
      <el-form-item label="H5域名" prop="h5DomainName">
        <el-input
          v-model="form.h5DomainName"
          placeholder="请输入H5域名"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSubmit" :loading="submitting">
          保存设置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'
import ImageUploader from './uploader/image.vue'

const client = makeClient()

export default {
  components: { Title, ImageUploader },

  data() {
    return {
      submitting: false,
      supplierData: null,
      form: {
        id: 0,
        supplierNo: '',
        name: '',
        socialCreditCode: '',
        contacts: '',
        contactPhone: '',
        signatureCode: '',
        domainName: '',
        slogan: '',
        logoUrl: '',
        brandName: '',
        h5DomainName: '',
        h5LogoUrl: '',
        h5ServiceAgreement: [],
        attachments: [],
        disabled: false
      },
      rules: {
        name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
        socialCreditCode: [
          { required: true, message: '请输入统一信用代码', trigger: 'blur' }
        ],
        brandName: [
          { required: true, message: '请输入品牌名称', trigger: 'blur' }
        ],
        domainName: [
          { required: true, message: '请输入全域名', trigger: 'blur' }
        ],
        signatureCode: [
          { required: true, message: '请输入短信签名', trigger: 'blur' }
        ],
        h5DomainName: [
          { required: true, message: '请输入H5域名', trigger: 'blur' }
        ]
      }
    }
  },

  async created() {
    await this.loadSupplierDetails()
  },

  methods: {
    // 加载供应商详情
    async loadSupplierDetails() {
      try {
        const [err, response] = await client.supplierDetail({ body: {} })

        if (err) {
          handleError(err)
          return
        }

        if (response.success && response.data) {
          this.supplierData = response.data
          this.populateForm(response.data)
        }
      } catch (error) {
        handleError(error)
      }
    },

    // 填充表单数据
    populateForm(data) {
      this.form.id = data.id || 0
      this.form.supplierNo = data.supplierNo || ''
      this.form.name = data.info?.name || ''
      this.form.socialCreditCode = data.info?.socialCreditCode || ''
      this.form.contacts = data.info?.contacts || ''
      this.form.contactPhone = data.info?.contactPhone || ''
      this.form.signatureCode = data.signatureCode || ''
      this.form.domainName = data.domain?.domainName || ''
      this.form.slogan = data.domain?.slogan || ''
      this.form.logoUrl = data.domain?.logoUrl || ''
      this.form.brandName = data.domain?.brandName || ''
      this.form.h5DomainName = data.domain?.h5DomainName || ''
      this.form.h5LogoUrl = data.domain?.h5LogoUrl || ''
      this.form.h5ServiceAgreement = data.domain?.h5ServiceAgreement || []
      this.form.attachments = data.info?.attachments || []
      this.form.disabled = data.disabled || false
    },

    // 重置表单
    resetForm() {
      if (this.supplierData) {
        this.populateForm(this.supplierData)
      }
    },

    // 提交表单
    async onSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) {
          return
        }

        this.submitting = true

        const [err] = await client.editSupplier({ body: this.form })

        if (err) {
          handleError(err)
          return
        }

        this.$message.success('设置成功')

        // 重新加载设置
        await this.loadSupplierDetails()
      } catch (error) {
        handleError(error)
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.supplier-settings {
  padding: 20px;
}
</style>
