<template>
  <div class="test-start-sign">
    <h2>StartSign 组件测试</h2>
    
    <div class="test-section">
      <h3>合同模板选择器测试</h3>
      <p>当前选中的模板ID: {{ templateId }}</p>
      <ContractTemplatesSelector
        v-model="templateId"
        style="width: 300px"
      />
    </div>

    <div class="test-section">
      <h3>服务合同选择器测试</h3>
      <p>客户ID: {{ customerId }}</p>
      <p>作业主体ID: {{ corporationId }}</p>
      <p>当前选中的服务合同ID: {{ serviceContractId }}</p>
      
      <div style="margin-bottom: 10px">
        <label>客户ID: </label>
        <el-input v-model="customerId" style="width: 200px; margin-right: 10px" />
        <label>作业主体ID: </label>
        <el-input v-model="corporationId" style="width: 200px" />
      </div>
      
      <ServiceContractsSelector
        v-model="serviceContractId"
        :customer-id="customerId"
        :corporation-id="corporationId"
        style="width: 300px"
      />
    </div>

    <div class="test-section">
      <h3>模板选择对话框测试</h3>
      <el-button type="primary" @click="showTemplateDialog">打开模板选择对话框</el-button>
      <p v-if="selectedTemplate">选中的模板: {{ selectedTemplate.tempName }}</p>
      
      <TemplateDialog
        :visible.sync="templateDialogVisible"
        @confirm="handleTemplateSelect"
      />
    </div>

    <div class="test-section">
      <h3>完整的 StartSign 组件</h3>
      <el-button type="success" @click="showStartSign">查看完整组件</el-button>
    </div>

    <div class="test-section">
      <h3>操作按钮</h3>
      <el-button @click="resetValues">重置所有值</el-button>
      <el-button @click="setTestValues">设置测试值</el-button>
    </div>
  </div>
</template>

<script>
import ContractTemplatesSelector from './selector/contractTemplates.vue'
import ServiceContractsSelector from './selector/serviceContracts.vue'
import TemplateDialog from './selector/templateDialog.vue'

export default {
  name: 'TestStartSign',
  components: {
    ContractTemplatesSelector,
    ServiceContractsSelector,
    TemplateDialog
  },
  data() {
    return {
      templateId: null,
      customerId: '',
      corporationId: '',
      serviceContractId: null,
      templateDialogVisible: false,
      selectedTemplate: null
    }
  },
  methods: {
    showTemplateDialog() {
      this.templateDialogVisible = true
    },
    handleTemplateSelect(template) {
      this.selectedTemplate = template
      console.log('选中的模板:', template)
    },
    showStartSign() {
      this.$router.push('/startSign')
    },
    resetValues() {
      this.templateId = null
      this.customerId = ''
      this.corporationId = ''
      this.serviceContractId = null
      this.selectedTemplate = null
    },
    setTestValues() {
      this.templateId = 1
      this.customerId = '1'
      this.corporationId = '1'
      this.serviceContractId = 1
    }
  }
}
</script>

<style scoped>
.test-start-sign {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
}

.test-section p {
  margin: 10px 0;
  color: #666;
}
</style>
