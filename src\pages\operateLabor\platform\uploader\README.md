# 帮助文档

本文档提供了有关此目录中组件的说明。

## image.vue

`image.vue` 组件用于处理图片上传。

### 参数 (Props)

| 参数 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| `value` | `String` \| `Array` | `null` | v-model, 绑定的文件ID或ID数组 |
| `name` | `String` | `'上传图片'` | 上传按钮中显示的文字 |
| `width` | `Number` | `120` | 上传组件的宽度 (px) |
| `height` | `Number` | `120` | 上传组件的高度 (px) |
| `multi` | `Boolean` | `false` | 是否允许选择多个文件 |
| `accept` | `String` | `'.jpg,.jpeg,.png,.gif,.webp'` | 允许上传的文件类型 |
| `max` | `Number` | `1` (单选) / `10` (多选) | 允许上传的最大文件数量 |
| `maxSize` | `Number` | `5` | 单个文件的最大尺寸 (MB) |
| `disabled` | `Boolean` | `false` | 是否禁用上传功能 |

## file.vue

`file.vue` 组件用于处理通用文件上传。

### 参数 (Props)

| 参数 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| `value` | `String` \| `Array` | `null` | v-model, 绑定的文件ID或ID数组 |
| `name` | `String` | `'上传文件'` | 上传按钮中显示的文字 |
| `multi` | `Boolean` | `false` | 是否允许选择多个文件 |
| `accept` | `String` | `''` | 允许上传的文件类型 (空字符串代表不限制) |
| `max` | `Number` | `1` (单选) / `10` (多选) | 允许上传的最大文件数量 |
| `maxSize` | `Number` | `10` | 单个文件的最大尺寸 (MB) |
| `disabled` | `Boolean` | `false` | 是否禁用上传功能 |
