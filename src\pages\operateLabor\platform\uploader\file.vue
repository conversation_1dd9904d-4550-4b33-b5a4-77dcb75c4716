<template>
  <div class="uploader-file">
    <!-- 已上传文件列表 -->
    <div class="file-list" v-if="fileList.length > 0">
      <div
        v-for="(fileItem, index) in fileList"
        :key="index"
        class="file-item"
        :class="{ uploading: fileItem.status === 'uploading' }"
      >
        <!-- 文件名（可点击下载） -->
        <span
          class="file-name"
          @click="handleFileClick(fileItem)"
          :class="{ clickable: fileItem.status === 'success' }"
        >
          <i v-if="fileItem.status === 'uploading'" class="el-icon-loading"></i>
          {{ fileItem.name }}
        </span>

        <!-- 删除按钮 -->
        <i
          class="el-icon-close file-remove"
          @click="removeFile(fileItem)"
          v-if="!disabled"
        ></i>
      </div>
    </div>

    <!-- 上传按钮 -->
    <div
      class="upload-button-wrapper"
      v-if="!isMaxReached || fileList.length === 0"
    >
      <el-button
        :loading="uploading"
        :disabled="disabled"
        size="small"
        @click="triggerFileSelect"
      >
        <i class="el-icon-upload2" v-if="!uploading"></i>
        {{ uploading ? '上传中...' : buttonText }}
      </el-button>

      <!-- 隐藏的文件选择input -->
      <input
        ref="fileInput"
        type="file"
        :accept="accept"
        :multiple="multi"
        @change="handleFileSelect"
        style="display: none"
      />
    </div>
  </div>
</template>

<script>
import uploaderMixin from './uploader.js'

export default {
  name: 'UploaderFile',
  mixins: [uploaderMixin],

  props: {
    name: {
      type: String,
      default: '上传文件'
    }
  },

  computed: {
    buttonText() {
      return this.name
    }
  },

  methods: {
    /**
     * 触发文件选择
     */
    triggerFileSelect() {
      if (this.disabled) return
      this.$refs.fileInput.click()
    },

    /**
     * 处理文件点击（下载）
     */
    handleFileClick(fileItem) {
      if (fileItem.status === 'success' && fileItem.id) {
        this.downloadFile(fileItem.id)
      }
    }
  }
}
</script>

<style scoped>
.uploader-file {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.file-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #606266;
  max-width: 200px;
}

.file-item.uploading {
  background: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 4px;
}

.file-name.clickable {
  cursor: pointer;
  color: #409eff;
}

.file-name.clickable:hover {
  text-decoration: underline;
}

.file-remove {
  cursor: pointer;
  color: #c0c4cc;
  font-size: 12px;
  margin-left: 4px;
}

.file-remove:hover {
  color: #f56c6c;
}

.upload-button-wrapper {
  display: inline-block;
}

.el-icon-loading {
  margin-right: 4px;
}
</style>
