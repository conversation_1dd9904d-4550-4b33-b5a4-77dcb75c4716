<template>
  <div class="uploader-image">
    <!-- 图片列表 -->
    <div class="image-list" v-if="fileList.length > 0">
      <div
        v-for="(fileItem, index) in fileList"
        :key="index"
        class="image-item"
        :style="{ width: width + 'px', height: height + 'px' }"
      >
        <!-- 上传中状态 -->
        <div v-if="fileItem.status === 'uploading'" class="image-uploading">
          <i class="el-icon-loading"></i>
          <div class="upload-text">上传中...</div>
        </div>

        <!-- 已上传的图片 -->
        <div v-else class="image-preview" @click="previewImage(fileItem)">
          <img
            :src="getPreviewUrl(fileItem.id)"
            :alt="fileItem.name"
            class="preview-img"
          />

          <!-- 删除按钮（hover显示） -->
          <div
            v-if="!disabled"
            class="image-remove"
            @click.stop="removeFile(fileItem)"
          >
            <i class="el-icon-close"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传按钮 -->
    <div
      v-if="!isMaxReached"
      class="upload-trigger"
      :style="{ width: width + 'px', height: height + 'px' }"
      @click="triggerFileSelect"
      :class="{ disabled: disabled }"
    >
      <div class="upload-content">
        <div v-if="!uploading" style="text-align: center">
          <i class="el-icon-plus"></i>
          <div style="position: relative; top: -5px">{{ name }}</div>
        </div>
        <i class="el-icon-loading" v-else></i>
        <div class="upload-text" v-if="uploading">上传中...</div>
      </div>

      <!-- 隐藏的文件选择input -->
      <input
        ref="fileInput"
        type="file"
        :accept="accept"
        :multiple="multi"
        @change="handleFileSelect"
        style="display: none"
      />
    </div>

    <!-- 图片预览弹窗 -->
    <el-dialog
      :visible.sync="previewVisible"
      :show-close="true"
      :modal="true"
      :close-on-click-modal="true"
      width="auto"
      custom-class="image-preview-dialog"
    >
      <img
        v-if="previewImageUrl"
        :src="previewImageUrl"
        style="max-width: 100%; max-height: 70vh"
      />
    </el-dialog>
  </div>
</template>

<script>
import uploaderMixin from './uploader.js'

export default {
  name: 'UploaderImage',
  mixins: [uploaderMixin],

  props: {
    name: {
      type: String,
      default: '上传图片'
    },
    width: {
      type: Number,
      default: 120
    },
    height: {
      type: Number,
      default: 120
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png,.gif,.webp'
    },
    maxSize: {
      type: Number,
      default: 5 // 图片默认5MB限制
    }
  },

  data() {
    return {
      previewVisible: false,
      previewImageUrl: ''
    }
  },

  methods: {
    /**
     * 触发文件选择
     */
    triggerFileSelect() {
      if (this.disabled) return
      this.$refs.fileInput.click()
    },

    /**
     * 预览图片
     */
    previewImage(fileItem) {
      if (fileItem.status === 'success' && fileItem.id) {
        this.previewImageUrl = this.getPreviewUrl(fileItem.id)
        this.previewVisible = true
      }
    }
  }
}
</script>

<style scoped>
.uploader-image {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-item {
  position: relative;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
  background: #fafafa;
}

.image-uploading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #409eff;
  font-size: 12px;
}

.image-uploading .el-icon-loading {
  font-size: 24px;
  margin-bottom: 8px;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.image-remove {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-item:hover .image-remove {
  opacity: 1;
}

.image-remove:hover {
  background: rgba(0, 0, 0, 0.7);
}

.image-remove .el-icon-close {
  color: white;
  font-size: 12px;
}

.upload-trigger {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.3s;
}

.upload-trigger:hover:not(.disabled) {
  border-color: #409eff;
}

.upload-trigger.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #8c939d;
}

.upload-content .el-icon-plus,
.upload-content .el-icon-loading {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 12px;
}

/* 预览弹窗样式 */
::v-deep .image-preview-dialog {
  text-align: center;
}

::v-deep .image-preview-dialog .el-dialog__body {
  padding: 20px;
}
</style>
