import makeClient from 'kit/services/operateLabor/makeClient'
import handleError from 'kit/helpers/handleError'

const client = makeClient()

/**
 * 通用上传逻辑
 */
export default {
  data() {
    return {
      uploading: false,
      fileList: [] // 内部文件列表，包含文件信息和上传状态
    }
  },

  props: {
    value: {
      type: [String, Array, Object],
      default: () => null
    },
    valueField: {
      type: String,
      default: 'id'
    },
    multi: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String,
      default: ''
    },
    max: {
      type: Number,
      default() {
        return this.multi ? 10 : 1
      }
    },
    maxSize: {
      type: Number,
      default: 10 // MB
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    // 当前已上传的文件ID列表
    currentFileIds() {
      if (!this.value) return []
      return this.multi ? this.value || [] : [this.value]
    },

    // 是否达到最大文件数量
    isMaxReached() {
      // 计算已上传成功的文件数量 + 正在上传中的文件数量
      const successCount = this.fileList.filter(
        item => item.status === 'success'
      ).length
      const uploadingCount = this.fileList.filter(
        item => item.status === 'uploading'
      ).length
      return successCount + uploadingCount >= this.max
    }
  },

  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.initFileList(newVal)
      }
    }
  },

  methods: {
    /**
     * 初始化文件列表 - 智能差异更新 + 批量加载
     */
    async initFileList(value) {
      if (!value) {
        this.fileList = []
        return
      }

      // 如果是数组且为空，清空文件列表
      if (Array.isArray(value) && value.length === 0) {
        this.fileList = []
        return
      }

      // 如果是对象但不是数组，跳过处理
      if (typeof value === 'object' && !Array.isArray(value)) {
        return
      }

      const newIds = this.multi
        ? Array.isArray(value)
          ? value
          : [value]
        : [value]
      const currentIds = this.fileList.map(item => item.id)

      // 找出需要删除的文件（在当前列表中但不在新列表中）
      const toRemove = currentIds.filter(id => !newIds.includes(id))

      // 找出需要添加的文件（在新列表中但不在当前列表中）
      const toAdd = newIds.filter(id => id && !currentIds.includes(id))

      // 删除不需要的文件
      if (toRemove.length > 0) {
        this.fileList = this.fileList.filter(
          item => !toRemove.includes(item.id)
        )
      }

      // 如果没有新文件需要添加，直接返回
      if (toAdd.length === 0) {
        return
      }

      // 批量并行获取新文件信息
      const fileInfoPromises = toAdd.map(async id => {
        try {
          const [err, r] = await client.describeFile({
            body: { id }
          })

          if (err) {
            console.error('获取文件信息失败:', err)
            return null
          }

          return {
            id,
            name: r.data.name,
            status: 'success',
            url:
              this.$options.name === 'UploaderImage'
                ? this.imageURL(id)
                : this.fileURL(id)
          }
        } catch (error) {
          console.error('处理文件ID失败:', id, error)
          return null
        }
      })

      // 等待所有文件信息获取完成
      const newFileInfos = await Promise.all(fileInfoPromises)

      // 过滤掉失败的文件，一次性添加到列表中
      const validNewFiles = newFileInfos.filter(info => info !== null)
      if (validNewFiles.length > 0) {
        this.fileList.push(...validNewFiles)
      }

      // 按照新ID列表的顺序重新排序文件列表
      this.fileList.sort((a, b) => {
        const indexA = newIds.indexOf(a.id)
        const indexB = newIds.indexOf(b.id)
        return indexA - indexB
      })
    },

    /**
     * 文件选择处理
     */
    handleFileSelect(event) {
      const files = Array.from(event.target.files)
      if (files.length > 0) {
        this.$emit('file-change', files[0])
      }
      this.uploadFiles(files)
      // 清空input值，允许重复选择同一文件
      event.target.value = ''
    },

    /**
     * 批量上传文件 - 优化用户体验，避免文件名逐个出现
     */
    async uploadFiles(files) {
      if (this.disabled) return

      // 检查文件数量限制
      const remainingSlots = this.max - this.currentFileIds.length
      if (files.length > remainingSlots) {
        this.$message.warning(`最多只能上传${this.max}个文件`)
        files = files.slice(0, remainingSlots)
      }

      // 验证文件
      const validFiles = files.filter(file => this.validateFile(file))

      if (validFiles.length === 0) return

      // 先为所有文件创建占位项（显示上传中状态）
      const uploadingItems = validFiles.map(file => ({
        id: null,
        name: file.name,
        status: 'uploading',
        file: file
      }))

      // 一次性添加所有上传中的文件到列表
      this.fileList.push(...uploadingItems)
      this.uploading = true

      // 并行上传所有文件
      const uploadPromises = validFiles.map(async (file, index) => {
        const fileItem = uploadingItems[index]
        return this.uploadSingleFileInternal(file, fileItem)
      })

      // 等待所有上传完成
      await Promise.all(uploadPromises)
      this.uploading = false
    },

    /**
     * 验证单个文件
     */
    validateFile(file) {
      // 检查文件大小
      if (file.size > this.maxSize * 1024 * 1024) {
        this.$message.error(`文件"${file.name}"大小超过${this.maxSize}MB限制`)
        return false
      }

      // 检查文件类型
      if (this.accept) {
        const acceptTypes = this.accept.split(',').map(type => type.trim())
        const fileExt = '.' + file.name.split('.').pop().toLowerCase()
        const mimeType = file.type

        const isValidType = acceptTypes.some(type => {
          if (type.startsWith('.')) {
            return fileExt === type.toLowerCase()
          }
          return mimeType.includes(type)
        })

        if (!isValidType) {
          this.$message.error(`文件"${file.name}"格式不支持`)
          return false
        }
      }

      return true
    },

    /**
     * 上传单个文件（保持原有接口兼容性）
     */
    async uploadSingleFile(file) {
      // 添加到文件列表（显示上传中状态）
      const fileItem = {
        id: null,
        name: file.name,
        status: 'uploading',
        file: file
      }
      this.fileList.push(fileItem)
      this.uploading = true

      try {
        await this.uploadSingleFileInternal(file, fileItem)
      } finally {
        this.uploading = false
      }
    },

    /**
     * 内部上传单个文件方法
     */
    async uploadSingleFileInternal(file, fileItem) {
      try {
        const formData = new FormData()
        formData.append('file', file)
        const [err, response] = await client.uploadFile({
          body: formData
        })

        if (err) {
          throw new Error(err.message || '上传失败')
        }

        // 上传成功，更新文件信息
        fileItem.id = response.data.fileId
        fileItem.status = 'success'

        if (this.$options.name === 'UploaderImage') {
          fileItem.url = this.imageURL(fileItem.id)
        } else {
          fileItem.url = this.fileURL(fileItem.id)
        }

        // 更新v-model值
        this.updateValue()

        this.$message.success(`文件"${file.name}"上传成功`)
      } catch (error) {
        // 上传失败，移除文件项
        const index = this.fileList.indexOf(fileItem)
        if (index > -1) {
          this.fileList.splice(index, 1)
        }

        this.$message.error(`文件"${file.name}"上传失败: ${error.message}`)
      }
    },

    /**
     * 删除文件
     */
    removeFile(fileItem) {
      const index = this.fileList.indexOf(fileItem)
      if (index > -1) {
        this.fileList.splice(index, 1)
        this.updateValue()
      }
    },
    fileURL(fileId) {
      return `${window.env?.apiPath}/api/public/downloadFile/${fileId}`
    },
    imageURL(fileId) {
      return `${window.env?.apiPath}/api/public/previewFile/${fileId}`
    },
    /**
     * 下载文件
     */
    downloadFile(fileId) {
      const downloadUrl = this.fileURL(fileId)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = ''
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    /**
     * 获取预览URL
     */
    getPreviewUrl(fileId) {
      return this.imageURL(fileId)
    },

    /**
     * 更新v-model值
     */
    updateValue() {
      const successFiles = this.fileList.filter(
        item => item.status === 'success'
      )
      const files = successFiles.map(item => {
        return {
          id: item.id,
          name: item.name,
          status: item.status,
          url: item.url
        }
      })
      if (this.multi) {
        this.$emit(
          'input',
          files.map(item => {
            if (this.valueField) {
              return item[this.valueField]
            }

            return item
          }) || []
        )
      } else {
        var r = files[0]
        if (r && this.valueField) {
          r = r[this.valueField]
        }
        this.$emit('input', r || null)
      }
    }
  }
}
