import makeClient from 'kit/services/operateLabor/makeClient'

const client = makeClient()

/**
 * 通用上传逻辑
 */
export default {
  data() {
    return {
      uploading: false,
      fileList: [] // 内部文件列表，包含文件信息和上传状态
    }
  },

  props: {
    value: {
      type: [String, Array],
      default: () => null
    },
    multi: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String,
      default: ''
    },
    max: {
      type: Number,
      default() {
        return this.multi ? 10 : 1
      }
    },
    maxSize: {
      type: Number,
      default: 10 // MB
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    }
  },

  computed: {
    // 当前已上传的文件ID列表
    currentFileIds() {
      if (!this.value) return []
      return this.multi ? this.value || [] : [this.value]
    },

    // 是否达到最大文件数量
    isMaxReached() {
      return this.currentFileIds.length >= this.max
    }
  },

  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.initFileList(newVal)
      }
    }
  },

  methods: {
    /**
     * 初始化文件列表
     */
    initFileList(value) {
      if (!value) {
        this.fileList = []
        return
      }

      const ids = this.multi ? value || [] : [value]
      this.fileList = ids.map(id => ({
        id,
        name: `文件_${id}`, // 简化处理，实际可能需要从后端获取文件名
        status: 'success'
      }))
    },

    /**
     * 文件选择处理
     */
    handleFileSelect(event) {
      const files = Array.from(event.target.files)
      this.uploadFiles(files)
      // 清空input值，允许重复选择同一文件
      event.target.value = ''
    },

    /**
     * 批量上传文件
     */
    async uploadFiles(files) {
      if (this.disabled) return

      // 检查文件数量限制
      const remainingSlots = this.max - this.currentFileIds.length
      if (files.length > remainingSlots) {
        this.$message.warning(`最多只能上传${this.max}个文件`)
        files = files.slice(0, remainingSlots)
      }

      // 验证文件
      const validFiles = files.filter(file => this.validateFile(file))

      // 逐个上传文件
      for (const file of validFiles) {
        await this.uploadSingleFile(file)
      }
    },

    /**
     * 验证单个文件
     */
    validateFile(file) {
      // 检查文件大小
      if (file.size > this.maxSize * 1024 * 1024) {
        this.$message.error(`文件"${file.name}"大小超过${this.maxSize}MB限制`)
        return false
      }

      // 检查文件类型
      if (this.accept) {
        const acceptTypes = this.accept.split(',').map(type => type.trim())
        const fileExt = '.' + file.name.split('.').pop().toLowerCase()
        const mimeType = file.type

        const isValidType = acceptTypes.some(type => {
          if (type.startsWith('.')) {
            return fileExt === type.toLowerCase()
          }
          return mimeType.includes(type)
        })

        if (!isValidType) {
          this.$message.error(`文件"${file.name}"格式不支持`)
          return false
        }
      }

      return true
    },

    /**
     * 上传单个文件
     */
    async uploadSingleFile(file) {
      // 添加到文件列表（显示上传中状态）
      const fileItem = {
        id: null,
        name: file.name,
        status: 'uploading',
        file: file
      }
      this.fileList.push(fileItem)
      this.uploading = true

      try {
        const formData = new FormData()
        formData.append('file', file)
        const [err, response] = await client.uploadFile({
          body: formData
        })

        if (err) {
          throw new Error(err.message || '上传失败')
        }

        // 上传成功，更新文件信息
        fileItem.id = response.data.fileId
        fileItem.status = 'success'

        // 更新v-model值
        this.updateValue()

        this.$message.success(`文件"${file.name}"上传成功`)
      } catch (error) {
        // 上传失败，移除文件项
        const index = this.fileList.indexOf(fileItem)
        if (index > -1) {
          this.fileList.splice(index, 1)
        }

        this.$message.error(`文件"${file.name}"上传失败: ${error.message}`)
      } finally {
        this.uploading = false
      }
    },

    /**
     * 删除文件
     */
    removeFile(fileItem) {
      const index = this.fileList.indexOf(fileItem)
      if (index > -1) {
        this.fileList.splice(index, 1)
        this.updateValue()
      }
    },

    /**
     * 下载文件
     */
    downloadFile(fileId) {
      const downloadUrl = `/api/public/downloadFile/${fileId}`
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = ''
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    /**
     * 获取预览URL
     */
    getPreviewUrl(fileId) {
      return `/api/public/previewFile/${fileId}`
    },

    /**
     * 更新v-model值
     */
    updateValue() {
      const successFiles = this.fileList.filter(
        item => item.status === 'success'
      )
      const fileIds = successFiles.map(item => item.id)

      if (this.multi) {
        this.$emit('input', fileIds)
      } else {
        this.$emit('input', fileIds[0] || null)
      }
    }
  }
}
