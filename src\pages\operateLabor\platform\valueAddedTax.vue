<template>
  <div
    class="valueAddedTax"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 10px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div class="lite" style="display: flex; align-items: center">
        <div>
          <el-form-item label="作业主体">
            <el-select
              filterable
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请选择所属作业主体"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="税款所属期">
            <el-date-picker
              v-model="conditions.filters.taxPaymentPeriod"
              type="month"
              placeholder="选择月份"
              value-format="yyyy-MM"
              style="width: 280px"
            ></el-date-picker>
          </el-form-item>
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
    </el-form>
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleGenerate"> 生成申报表 </el-button>
    </div>
    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="增值税申报表ID"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatText(scope.row.id) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="supplierCorporationName"
        label="作业主体名称"
        width="200"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatText(scope.row.supplierCorporationName) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="taxPaymentPeriod"
        label="税款所属期"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatText(scope.row.taxPaymentPeriod) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="incomeTaxMonth"
        label="增值税申报月"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatText(scope.row.incomeTaxMonth) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="taxpayersCount"
        label="纳税人数"
        width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatText(scope.row.taxpayersCount) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="totalVatAmount"
        label="增值税总额"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatAmount(scope.row.totalVatAmount) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="totalSurtaxAmount"
        label="附加税总额"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatAmount(scope.row.totalSurtaxAmount) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" width="100" label="生成状态">
        <template slot-scope="scope">
          <span :class="['status-tag', getStatusClass(scope.row.status)]">
            {{ getStatusText(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" width="150" label="生成日期">
        <template slot-scope="scope">
          {{ formatText(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="180">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleView(scope.row)"
            :disabled="scope.row.status === 'GENERATING'"
            >查看</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleDownload(scope.row)"
            :disabled="scope.row.status === 'GENERATING'"
            >导出申报表</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>

    <!-- 生成申报表对话框 -->
    <el-dialog
      title="生成申报表"
      :visible.sync="generateDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="generateForm"
        :rules="generateRules"
        ref="generateForm"
        label-width="100px"
      >
        <el-form-item label="作业主体" prop="supplierCorporationId" required>
          <el-select
            filterable
            v-model="generateForm.supplierCorporationId"
            placeholder="请选择所属作业主体"
            style="width: 300px"
            clearable
          >
            <el-option
              v-for="item in supplierOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="税款所属期" prop="taxPaymentPeriod" required>
          <el-date-picker
            v-model="generateForm.taxPaymentPeriod"
            type="month"
            placeholder="选择月份"
            value-format="yyyy-MM"
            style="width: 300px"
            @change="validateTaxPeriod"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="generateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmGenerate" :loading="generating"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import { getToken } from '../../../helpers/token'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          id: '',
          supplierCorporationId: '',
          taxPaymentPeriod: '',
          incomeTaxMonth: '',
          supplierCorporationName: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      },
      total: 0,
      data: [],
      loading: true,
      supplierOptions: [],
      // 生成申报表对话框相关
      generateDialogVisible: false,
      generating: false,
      generateForm: {
        supplierCorporationId: '',
        taxPaymentPeriod: ''
      },
      generateRules: {
        supplierCorporationId: [
          {
            required: true,
            message: '请选择作业主体',
            trigger: ['change', 'blur']
          }
        ],
        taxPaymentPeriod: [
          {
            required: true,
            message: '请选择税款所属期',
            trigger: ['change', 'blur']
          }
        ]
      }
    }
  },
  async created() {
    await this.loadSupplierOptions()
    await this.getList()
  },
  methods: {
    // 加载作业主体选项
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载业务主体选项失败：', error)
      }
    },
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          id: '',
          supplierCorporationId: '',
          taxPaymentPeriod: '',
          incomeTaxMonth: '',
          supplierCorporationName: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      }
      this.getList()
    },
    async getList() {
      this.loading = true

      const queryConditions = { ...this.conditions }

      const [err, r] = await client.valueAddedTaxList({
        body: queryConditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    handleGenerate() {
      // 重置表单
      this.generateForm = {
        supplierCorporationId: '',
        taxPaymentPeriod: ''
      }
      this.$nextTick(() => {
        if (this.$refs.generateForm) {
          this.$refs.generateForm.clearValidate()
        }
      })
      this.generateDialogVisible = true
    },
    // 验证税款所属期
    validateTaxPeriod() {
      if (this.$refs.generateForm) {
        this.$refs.generateForm.validateField('taxPaymentPeriod')
      }
    },
    // 确认生成申报表
    async confirmGenerate() {
      // 表单验证
      const valid = await new Promise(resolve => {
        this.$refs.generateForm.validate(valid => {
          resolve(valid)
        })
      })

      if (!valid) {
        return
      }

      // 额外验证确保数据完整
      if (!this.generateForm.supplierCorporationId) {
        this.$message.error('请选择作业主体')
        return
      }

      if (!this.generateForm.taxPaymentPeriod) {
        this.$message.error('请选择税款所属期')
        return
      }

      this.generating = true

      try {
        const requestData = {
          id: 0,
          supplierCorporationId: this.generateForm.supplierCorporationId,
          taxPaymentPeriod: this.generateForm.taxPaymentPeriod,
          incomeTaxMonth: '',
          taxpayersCount: '',
          currentIncome: ''
        }

        const [err, response] = await client.addValueAddedTax({
          body: requestData
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success) {
          this.$message.success('申报表生成成功')
          this.generateDialogVisible = false
          // 重新查询列表
          await this.getList()
        } else {
          this.$message.error(response.message || '生成失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.generating = false
      }
    },
    handleView(row) {
      this.$router.push(`/valueAddedTax/${row.id}`)
    },
    async handleDownload(row) {
      try {
        // 使用fetch API携带token下载文件
        const token = `Bearer ${getToken()}`
        const response = await fetch(
          `${window.env?.apiPath}/api/supplier/valueaddedtax/download/declarationRecord`,
          {
            method: 'POST',
            headers: {
              Authorization: token,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              id: row.id
            })
          }
        )

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${row.taxPaymentPeriod}_增值税申报表.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)

        this.$message.success('申报表导出成功')
      } catch (error) {
        console.error('导出申报表失败：', error)
        this.$message.error('导出申报表失败')
      }
    },
    getStatusText(status) {
      const statusMap = {
        GENERATING: '生成中',
        GENERATED: '已生成'
      }
      return statusMap[status] || status
    },
    getStatusClass(status) {
      const classMap = {
        GENERATING: 'status-generating',
        GENERATED: 'status-generated'
      }
      return classMap[status] || 'status-default'
    },
    formatAmount(amount) {
      if (amount === null || amount === undefined || amount === '') {
        return '-'
      }
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatText(text) {
      if (text === null || text === undefined || text === '') {
        return '-'
      }
      return text
    }
  }
}
</script>

<style scoped>
.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
}

.status-generating {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-generated {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-default {
  background-color: #fafafa;
  color: #666666;
  border: 1px solid #d9d9d9;
}
</style>
