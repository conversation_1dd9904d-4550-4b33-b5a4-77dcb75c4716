<template>
  <div class="id-page">
    <div class="header">
      <h2>身份信息确认</h2>
      <p class="subtitle">请确认您的身份信息</p>
    </div>

    <div class="form-section">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-position="left"
        label-width="80px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入姓名"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="身份证号" prop="idNumber">
          <el-input
            v-model="formData.idNumber"
            placeholder="请输入身份证号码"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="formData.gender">
            <el-radio label="male">男</el-radio>
            <el-radio label="female">女</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="出生日期" prop="birthDate">
          <el-date-picker
            v-model="formData.birthDate"
            type="date"
            placeholder="选择出生日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="地址" prop="address">
          <el-input
            v-model="formData.address"
            placeholder="请输入地址"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="签发机关" prop="issuingAuthority">
          <el-input
            v-model="formData.issuingAuthority"
            placeholder="请输入签发机关"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="有效期" prop="validPeriod">
          <el-date-picker
            v-model="formData.validPeriod"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <!-- 下一步按钮 -->
    <div class="button-section">
      <button class="next-btn" :disabled="!canProceed" @click="handleNext">
        下一步
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskId',

  data() {
    // 身份证号码验证
    const validateIdNumber = (rule, value, callback) => {
      const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      if (!value) {
        return callback(new Error('请输入身份证号码'))
      } else if (!reg.test(value)) {
        return callback(new Error('身份证号码格式不正确'))
      } else {
        callback()
      }
    }

    return {
      formData: {
        name: '',
        idNumber: '',
        gender: '',
        birthDate: '',
        address: '',
        issuingAuthority: '',
        validPeriod: []
      },
      rules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        idNumber: [
          { required: true, message: '请输入身份证号码', trigger: 'blur' },
          { validator: validateIdNumber, trigger: 'blur' }
        ],
        gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
        birthDate: [
          { required: true, message: '请选择出生日期', trigger: 'change' }
        ],
        address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
        issuingAuthority: [
          { required: true, message: '请输入签发机关', trigger: 'blur' }
        ],
        validPeriod: [
          { required: true, message: '请选择有效期', trigger: 'change' }
        ]
      }
    }
  },

  computed: {
    canProceed() {
      // 简单检查必填字段是否都有值
      return (
        this.formData.name &&
        this.formData.idNumber &&
        this.formData.gender &&
        this.formData.birthDate &&
        this.formData.address &&
        this.formData.issuingAuthority &&
        this.formData.validPeriod &&
        this.formData.validPeriod.length === 2
      )
    }
  },

  created() {
    // 如果有从上一步传来的数据，填充表单
    const { frontImage, backImage } = this.$route.query
    if (frontImage && backImage) {
      // 这里应该是调用API解析身份证信息
      // 现在只是模拟一下
      this.mockOcrData()
    }
  },

  methods: {
    mockOcrData() {
      // 模拟OCR识别结果
      this.formData = {
        name: '张三',
        idNumber: '110101199001011234',
        gender: 'male',
        birthDate: '1990-01-01',
        address: '北京市朝阳区某某街道某某小区1号楼1单元101室',
        issuingAuthority: '北京市公安局朝阳分局',
        validPeriod: ['2020-01-01', '2030-01-01']
      }
    },

    handleNext() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          this.$message.error('请完善表单信息')
          return
        }

        // TODO: 提交身份信息
        console.log('提交身份信息', this.formData)

        // 跳转到人脸认证页面
        this.$router.push({
          path: '/task/faceAuth',
          query: {
            name: this.formData.name,
            idNumber: this.formData.idNumber
          }
        })
      })
    }
  }
}
</script>

<style scoped>
.id-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding-top: 20px;
}

.header h2 {
  font-size: 24px;
  color: #333;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.form-section {
  flex: 1;
  max-width: 500px;
  margin: 0 auto;
  width: 100%;
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.button-section {
  padding: 30px 0;
  max-width: 500px;
  margin: 0 auto;
  width: 100%;
}

.next-btn {
  width: 100%;
  height: 50px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.next-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.next-btn:not(:disabled):hover {
  background: #3367d6;
  transform: translateY(-1px);
}

/* 覆盖Element UI的一些样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__inner) {
  border-radius: 8px;
}

:deep(.el-radio__label) {
  font-size: 14px;
}
</style>
