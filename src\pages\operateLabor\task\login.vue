<template>
  <div class="login-page">
    <!-- Logo区域 -->
    <div class="logo-section">
      <div class="logo-placeholder">
        <div class="logo-circle">
          <div class="logo-waves">
            <div class="wave wave1"></div>
            <div class="wave wave2"></div>
            <div class="wave wave3"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录表单 -->
    <div class="login-form">
      <!-- 手机号输入 -->
      <div class="input-group">
        <div class="input-wrapper">
          <i class="icon-phone"></i>
          <input
            v-model="loginForm.phone"
            type="tel"
            placeholder="请通过手机号登录"
            maxlength="11"
          />
        </div>
      </div>

      <!-- 验证码输入 -->
      <div class="input-group">
        <div class="input-wrapper">
          <input
            v-model="loginForm.captcha"
            type="text"
            placeholder="请输入图形验证码"
            maxlength="4"
          />
          <div class="captcha-image" @click="refreshCaptcha">
            <span class="captcha-text">3457</span>
          </div>
        </div>
      </div>

      <!-- 短信验证码 -->
      <div class="input-group">
        <div class="input-wrapper">
          <i class="icon-shield"></i>
          <input
            v-model="loginForm.smsCode"
            type="text"
            placeholder="请输入验证码"
            maxlength="6"
          />
          <button
            class="sms-btn"
            :disabled="smsCountdown > 0"
            @click="sendSmsCode"
          >
            {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
          </button>
        </div>
      </div>

      <!-- 密码登录链接 -->
      <div class="password-login">
        <span @click="showPasswordLogin = !showPasswordLogin">密码登录</span>
      </div>

      <!-- 登录按钮 -->
      <button class="login-btn" :disabled="!canLogin" @click="handleLogin">
        登录
      </button>

      <!-- 协议勾选 -->
      <div class="agreement-section">
        <label class="agreement-checkbox">
          <input v-model="agreedToTerms" type="checkbox" />
          <span class="checkmark"></span>
          <span class="agreement-text">
            阅读并同意
            <span class="agreement-link" @click="showAgreement"
              >《隐私数据协议》</span
            >
            <span class="agreement-link" @click="showUserAgreement"
              >《用户服务协议》</span
            >
          </span>
        </label>
      </div>
    </div>

    <!-- 协议弹窗 -->
    <div
      v-if="agreementVisible"
      class="agreement-modal"
      @click="closeAgreement"
    >
      <div class="agreement-content" @click.stop>
        <div class="agreement-header">
          <h3>{{ agreementTitle }}</h3>
          <button class="close-btn" @click="closeAgreement">×</button>
        </div>
        <div class="agreement-body">
          <iframe
            src="/agreement.vue"
            frameborder="0"
            width="100%"
            height="100%"
          ></iframe>
        </div>
        <div class="agreement-footer">
          <button class="read-btn" @click="confirmRead">已阅读</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskLogin',

  data() {
    return {
      loginForm: {
        phone: '',
        captcha: '',
        smsCode: ''
      },
      agreedToTerms: false,
      showPasswordLogin: false,
      smsCountdown: 0,
      smsTimer: null,
      agreementVisible: false,
      agreementTitle: ''
    }
  },

  computed: {
    canLogin() {
      return (
        this.loginForm.phone.length === 11 &&
        this.loginForm.captcha.length === 4 &&
        this.loginForm.smsCode.length === 6 &&
        this.agreedToTerms
      )
    }
  },

  beforeDestroy() {
    if (this.smsTimer) {
      clearInterval(this.smsTimer)
    }
  },

  methods: {
    refreshCaptcha() {
      // TODO: 刷新图形验证码
      console.log('刷新验证码')
    },

    sendSmsCode() {
      if (this.loginForm.phone.length !== 11) {
        this.$message.error('请输入正确的手机号')
        return
      }
      if (this.loginForm.captcha.length !== 4) {
        this.$message.error('请输入图形验证码')
        return
      }

      // TODO: 发送短信验证码
      console.log('发送短信验证码')

      // 开始倒计时
      this.smsCountdown = 60
      this.smsTimer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer)
          this.smsTimer = null
        }
      }, 1000)
    },

    handleLogin() {
      if (!this.canLogin) return

      // TODO: 执行登录逻辑
      console.log('登录', this.loginForm)

      // 跳转到实名认证页面
      this.$router.push('/task/ocr')
    },

    showAgreement() {
      this.agreementTitle = '隐私数据协议'
      this.agreementVisible = true
    },

    showUserAgreement() {
      this.agreementTitle = '用户服务协议'
      this.agreementVisible = true
    },

    closeAgreement() {
      this.agreementVisible = false
    },

    confirmRead() {
      this.agreedToTerms = true
      this.closeAgreement()
    }
  }
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-section {
  margin-bottom: 60px;
  margin-top: 40px;
}

.logo-placeholder {
  width: 80px;
  height: 80px;
  position: relative;
}

.logo-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.logo-waves {
  position: relative;
  width: 60%;
  height: 60%;
}

.wave {
  position: absolute;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.6);
}

.wave1 {
  width: 100%;
  height: 100%;
  animation: wave 2s infinite;
}

.wave2 {
  width: 70%;
  height: 70%;
  top: 15%;
  left: 15%;
  animation: wave 2s infinite 0.5s;
}

.wave3 {
  width: 40%;
  height: 40%;
  top: 30%;
  left: 30%;
  animation: wave 2s infinite 1s;
}

@keyframes wave {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}

.login-form {
  width: 100%;
  max-width: 320px;
}

.input-group {
  margin-bottom: 20px;
}

.input-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  height: 50px;
}

.input-wrapper input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #333;
  outline: none;
}

.input-wrapper input::placeholder {
  color: #999;
}

.icon-phone::before,
.icon-shield::before {
  content: '📱';
  margin-right: 10px;
  font-size: 18px;
}

.icon-shield::before {
  content: '🛡️';
}

.captcha-image {
  width: 80px;
  height: 32px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 10px;
}

.captcha-text {
  font-size: 16px;
  font-weight: bold;
  color: #666;
  letter-spacing: 2px;
}

.sms-btn {
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 15px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  margin-left: 10px;
  white-space: nowrap;
}

.sms-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.password-login {
  text-align: right;
  margin-bottom: 30px;
}

.password-login span {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  cursor: pointer;
  text-decoration: underline;
}

.login-btn {
  width: 100%;
  height: 50px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 20px;
  transition: all 0.3s;
}

.login-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.login-btn:not(:disabled):hover {
  background: #3367d6;
  transform: translateY(-1px);
}

.agreement-section {
  text-align: center;
}

.agreement-checkbox {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.agreement-checkbox input {
  display: none;
}

.checkmark {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 3px;
  margin-right: 8px;
  position: relative;
}

.agreement-checkbox input:checked + .checkmark {
  background: #4285f4;
  border-color: #4285f4;
}

.agreement-checkbox input:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  color: white;
  font-size: 12px;
  top: -2px;
  left: 2px;
}

.agreement-link {
  color: #87ceeb;
  text-decoration: underline;
  cursor: pointer;
}

.agreement-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.agreement-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.agreement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.agreement-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
}

.agreement-body {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}

.agreement-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.read-btn {
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 40px;
  font-size: 16px;
  cursor: pointer;
}
</style>
