<template>
  <el-dialog
    width="800px"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    custom-class="departmentEmployeesSelectorDialog"
  >
    <MultipleEmployeeInsideDepartment
      :title="title"
      :loading="loading"
      :breadcrumbDepartments="breadcrumbDepartments"
      :departments="departments"
      :employees="employees"
      :allSelectedEmployees="allSelectedEmployees"
      :selectedEmployees="selectedEmployees"
      :isAllChecked="isAllChecked"
      :selectedDepartment="currentDepartment"
      @search="search"
      @selectDepartment="selectDepartment"
      @unselectDepartment="unselectDepartment"
      @clickBreadcrumbDepartment="clickBreadcrumbDepartment"
      @clickDepartmentSubdivision="clickDepartmentSubdivision"
      @selectEmployee="selectEmployee"
      @unselectEmployee="unselectEmployee"
      @selectAllEmployees="selectAllEmployees"
      @unselectAllEmployees="unselectAllEmployees"
      @unselectSelectedEmployee="unselectSelectedEmployee"
      @confirm="confirm"
      @cancel="cancel"
      @clear="clear"
      @leftListBottomReached="leftListBottomReached"
      @rightListBottomReached="rightListBottomReached"
    />
  </el-dialog>
</template>
<script>
import MultipleEmployeeInsideDepartment from 'kit/components/ui/picker/multipleEmployeeInsideDepartment.vue'
import handleError from 'kit/helpers/handleError'
import formatDepartments from './formatDepartments'
import formatEmployees from './formatEmployees'
import getRootDepartment from './getRootDepartment'
import getDepartmentsByParentID from './getDepartmentsByParentID'
import getDepartmentByID from './getDepartmentByID'

import makePlatformClient from 'kit/services/platform/makeClient'
import { walkDepartments } from './helpers'
const platformClient = makePlatformClient()

var allDepartments = []
var allEmployees = []
const limit = 10
export default {
  components: {
    MultipleEmployeeInsideDepartment
  },
  computed: {
    breadcrumbDepartments() {
      if (!this.currentDepartment) {
        return []
      }

      if(!this.currentDepartment.parentDepartments.length){
        return [this.currentDepartment]
      }

      return this.currentDepartment.parentDepartments.concat(this.currentDepartment)
    },
    selectedEmployees() {
      return this.allSelectedEmployees.slice(0, this.selectedEmployeesEnd)
    },
    isAllChecked() {
      if (!this.allSelectedEmployees.length) {
        return false
      }

      const departmentEmployees =
        allEmployees.filter(item =>
          item.departmentIds.includes(this.currentDepartment.id)
        ) || []

      const allSelectedEmployeesMap = new Map()
      this.allSelectedEmployees.forEach(item => {
        allSelectedEmployeesMap.set(item.id, true)
      })

      for (var c of departmentEmployees) {
        if (!allSelectedEmployeesMap.has(c.id)) {
          return false
        }
      }

      return true
    }
  },
  props: {
    title: {
      type: String,
      default: '授权用户'
    },
    defaultSelectedUserIds: {
      type: Array,
      default() {
        return []
      }
    },
    from:{
      type: String,
      default:''
    }
  },
  data() {
    return {
      visible: false,
      loading: true,
      //放这里是避免 同页面多实例 导致选择错乱
      allSelectedEmployees: [],
      currentDepartment: null,
      employees: [],
      departments: [],
      employeesEnd: limit,
      selectedEmployeesEnd: limit
    }
  },
  created() {
    this.loadOnce()
  },
  methods: {
    async loadOnce() {
      setTimeout(() => {
        //1秒内 没有读取完毕 才展示loading
        if (this.loading) {
          this.loading = true
        }
      }, 1000)

      const [err, departments] = await this.loadDepartments()
      if (err) {
        this.loading = false
        handleError(err)
        return
      }

      allDepartments = formatDepartments(departments)

      const tmp = getRootDepartment(allDepartments)
      //选人不需要选择根部门 从组织架构下一级开始
      const rootDepartment = getDepartmentByID(allDepartments, tmp.children[0].id)

      const [err2, employees] = await this.loadEmployees(rootDepartment)
      if (err2) {
        this.loading = false
        handleError(err2)
        return
      }
      allEmployees = formatEmployees(employees, rootDepartment)

      this.currentDepartment = rootDepartment
      //之类为什么不根据rootDepartment筛选，是因为后端并没有将跟部门写入到每个employee的departmentIds中
      this.employees = allEmployees
        .filter(item => item.departmentIds.includes(this.currentDepartment.id))
        .slice(0, this.employeesEnd)

      this.departments = getDepartmentsByParentID(
        allDepartments,
        rootDepartment.id
      )

      this.allSelectedEmployees = allEmployees.filter(item =>
        this.defaultSelectedUserIds.includes(item.id)
      )

      this.$emit("load",this)
      this.loading = false
    },
    async loadDepartments(departmentIDs = []) {
      //load all departments
      var filters = {
        withChildren: true
      }
      if (departmentIDs && departmentIDs.length) {
        filters.id = departmentIDs
      }

      const [err, r] = await platformClient.merchantPlatformListDept({
        body: {
          filters
        }
      })

      return [err, r.data?.list || []]
    },
    async loadEmployees(department, keywords) {
      const filters = {
        withDeptMember: true
      }
      if(department){
        filters.deptId = department.id
      }
      if(keywords){
        filters.keywords = keywords
      }
      const [err, r] =
        await platformClient.merchantPlatformListMerchantMemberLite({
          body: {
            filters: filters
          }
        })

      return [err, r.data.list]
    },
    clickBreadcrumbDepartment(v) {
      return this.clickDepartmentSubdivision(v)
    },
    clickDepartmentSubdivision(v) {
      this.currentDepartment = getDepartmentByID(allDepartments, v.id)
      this.employees = allEmployees
        .filter(item => {
          return item.departmentIds.includes(this.currentDepartment.id)
        })
        .slice(0, this.employeesEnd)
      this.departments = this.currentDepartment.children
    },
    async search(keywords) {
      //恢复状态
      if (!keywords) {
        this.employeesEnd = limit

        this.employees = allEmployees
          .filter(item => {
            return item.departmentIds.includes(this.currentDepartment.id)
          })
          .slice(0, this.employeesEnd)

        this.departments = getDepartmentsByParentID(
          allDepartments,
          this.currentDepartment.id
        )

        return
      }

      this.loading = true

      var filteredDepartments = []
      walkDepartments(allDepartments, v => {
        if (v.name.includes(keywords)) {
          filteredDepartments.push(v)
        }
      })
      this.departments = filteredDepartments

      const [err,r]  = await this.loadEmployees(null, keywords)
      this.loading = false
      if(err){
        handleErr(err)
        return
      }

      if(!r || !r.length){
        this.employees = []
        return
      }

      const foundUserIds = r.map(c=>c.userId)
      var filteredEmployees = []

      allEmployees.filter(v => {

        if (foundUserIds.includes(v.userId)) {
          filteredEmployees.push(v)
        }
      })
      this.employees = filteredEmployees
    },
    selectDepartment(department) {
      this.currentDepartment = department
      this.employees = allEmployees
        .filter(item => item.departmentIds.includes(this.currentDepartment.id))
        .slice(0, this.employeesEnd)
    },
    unselectDepartment(department) {
      const lastDepartment =
        this.breadcrumbDepartments[this.breadcrumbDepartments.length - 1]
      this.employees = allEmployees
        .filter(item => item.departmentIds.includes(lastDepartment.id))
        .slice(0, this.employeesEnd)
    },
    open(info) {
      if(info){this.from=info}
      this.visible = true
    },
    close() {
      this.visible = false
      this.$emit("close")
    },
    cancel() {
      if(this.from&&this.from=='baseInfo'){
        this.$stats.webEvent('SalaryTax.SalaryCompute.SalaryCompute.AddPayroll.BaseInfo.Add.DialogButtonClick',`btnType=取消`)
      }
      this.close()
    },
    clear() {
      this.allSelectedEmployees = []
      this.selectedEmployeesEnd = limit
    },
    async confirm() {
      this.$emit('confirm', this.allSelectedEmployees)
      this.close()
    },
    selectEmployee(v) {
      this.allSelectedEmployees.push(v)
      this.selectedEmployeesEnd++
    },
    unselectEmployee(v) {
      this.allSelectedEmployees = this.allSelectedEmployees.filter(
        item => item.id !== v.id
      )

      this.selectedEmployeesEnd--
    },
    selectAllEmployees() {
      const currentDepartmentEmployees = allEmployees.filter(item =>
        item.departmentIds.includes(this.currentDepartment.id) && !item.disabled
      )

      for (var c of currentDepartmentEmployees) {
        if (!this.allSelectedEmployees.find(item => item.id === c.id)) {
          this.allSelectedEmployees.push(c)
        }
      }
    },
    unselectAllEmployees() {
      this.allSelectedEmployees = this.allSelectedEmployees.filter(
        item => !item.departmentIds.includes(this.currentDepartment.id)
      )
    },
    unselectSelectedEmployee(v) {
      return this.unselectEmployee(v)
    },
    leftListBottomReached() {
      this.employeesEnd += limit
      this.employees = allEmployees
        .filter(item => {
          return item.departmentIds.includes(this.currentDepartment.id)
        })
        .slice(0, this.employeesEnd)
    },
    rightListBottomReached() {
      this.selectedEmployeesEnd += limit
    }
  }
}
</script>
<style >
.departmentEmployeesSelectorDialog .el-dialog__header {
  display: none !important;
}
.departmentEmployeesSelectorDialog .el-dialog__body {
  padding: 0 !important;
}
</style>
