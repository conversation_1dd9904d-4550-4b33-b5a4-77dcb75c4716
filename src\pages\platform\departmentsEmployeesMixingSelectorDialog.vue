<template>
  <el-dialog
    width="800px"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    class="departmentsEmployeesMixingSelectorDialog"
  >
    <Mixing
      :title="'请选择范围'"
      :loading="loading"
      :breadcrumbDepartments="breadcrumbDepartments"
      :departments="departments"
      :employees="employees"
      :selectedDepartments="selectedDepartments"
      :allSelectedEmployees="allSelectedEmployees"
      :selectedEmployees="selectedEmployees"
      :isAllEmployeesChecked="isAllEmployeesChecked"
      @clickBreadcrumbDepartment="clickBreadcrumbDepartment"
      @clickDepartmentSubdivision="clickDepartmentSubdivision"
      @selectEmployee="selectEmployee"
      @unselectEmployee="unselectEmployee"
      @selectAllEmployees="selectAllEmployees"
      @unselectAllEmployees="unselectAllEmployees"
      @selectDepartment="selectDepartment"
      @unselectDepartment="unselectDepartment"
      @selectAllDepartments="selectAllDepartments"
      @unselectAllDepartments="unselectAllDepartments"
      @confirm="confirm"
      @cancel="cancel"
      @search="search"
      @clear="clear"
      @leftListBottomReached="leftListBottomReached"
      @rightListBottomReached="rightListBottomReached"
    />
  </el-dialog>
</template>

<script>
import Mixing from 'kit/components/ui/picker/mixing.vue'
import formatDepartments from './formatDepartments'
import getRootDepartment from './getRootDepartment'
import formatEmployees from './formatEmployees'
import makePlatformClient from 'kit/services/platform/makeClient'
import getDepartmentsByParentID from './getDepartmentsByParentID'
import getDepartmentByID from './getDepartmentByID'
import getDepartmentsByIDs from './getDepartmentsByIDs'
import { walkDepartments } from './helpers'
const platformClient = makePlatformClient()
var allDepartments = []
var allEmployees = []
const limit = 10
export default {
  components: {
    Mixing
  },
  props: {
    defaultSelectedEmployeeIDs: {
      type: Array,
      default() {
        return []
      }
    },
    defaultSelectedDepartmentIDs: {
      type: Array,
      default() {
        return []
      }
    }
  },
  computed: {
    breadcrumbDepartments() {
      if (!this.currentDepartment) {
        return []
      }

      if (!this.currentDepartment.parentDepartments.length) {
        return [this.currentDepartment]
      }

      return this.currentDepartment.parentDepartments.concat({
        id: this.currentDepartment.id,
        name: this.currentDepartment.name
      })
    },
    selectedEmployees() {
      return this.allSelectedEmployees.slice(0, this.selectedEmployeesEnd)
    },
    isAllEmployeesChecked() {
      if (!this.allSelectedEmployees.length) {
        return false
      }

      if (this.allSelectedEmployees.length === allEmployees.length) {
        return true
      }

      return false
    }
  },
  data() {
    return {
      visible: false,
      loading: true,
      //放这里是避免 同页面多实例 导致选择错乱, 之所以有allxx是因为右侧的用户过多，需要进行分页
      allSelectedEmployees: [],
      selectedDepartments: [],
      currentDepartment: null,
      departments: [],
      //内存里面模拟分页，避免页面卡死
      employees: [],
      employeesEnd: limit,
      selectedEmployeesEnd: limit,
      currentDepartment: null
    }
  },
  created() {
    this.loadOnce()
  },
  methods: {
    async loadOnce() {
      setTimeout(() => {
        //1秒内 没有读取完毕 才展示loading
        if (this.loading) {
          this.loading = true
        }
      }, 1000)

      const [err, departments] = await this.loadDepartments()
      if (err) {
        this.loading = false
        handleError(err)
        return
      }

      allDepartments = formatDepartments(departments)
      const rootDepartment = getRootDepartment(allDepartments)
      const [err2, employees] = await this.loadEmployees(rootDepartment)

      if (err2) {
        this.loading = false
        handleError(err2)
        return
      }
      allEmployees = formatEmployees(employees, rootDepartment)
      this.currentDepartment = rootDepartment
      //之类为什么不根据rootDepartment筛选，是因为后端并没有将跟部门写入到每个employee的departmentIds中
      this.employees = allEmployees
        // .filter(item => item.departmentIds.includes(this.currentDepartment.id))
        .slice(0, this.employeesEnd)

      this.departments = getDepartmentsByParentID(
        allDepartments,
        rootDepartment.id
      )
     
      this.allSelectedEmployees = allEmployees.filter(item =>
        this.defaultSelectedEmployeeIDs.includes(item.id)
      )
      
      this.selectedDepartments =  getDepartmentsByIDs(allDepartments, this.defaultSelectedDepartmentIDs)

      this.loading = false
    },
    async loadDepartments(departmentIDs = []) {
      //load all departments
      var filters = {
        withChildren: true
      }
      if (departmentIDs && departmentIDs.length) {
        filters.id = departmentIDs
      }

      const [err, r] = await platformClient.merchantPlatformListDept({
        body: {
          filters
        }
      })

      return [err, r.data?.list || []]
    },
    async loadEmployees(department, keywords) {
      const filters = {
        withDeptMember: true
      }

      if(keywords){
        filters.keywords = keywords
      }

      const [err, r] =
        await platformClient.merchantPlatformListMerchantMemberLite({
          body: {
            filters: filters
          }
        })

      return [err, r.data.list]
    },
    open() {
      this.visible = true
    },
    close() {
      this.visible = false
      this.$emit('close')
    },
    confirm() {
      this.$emit('confirm', this.selectedDepartments, this.allSelectedEmployees)

      this.close()
    },
    cancel() {
      this.selectedDepartments = []
      this.allSelectedEmployees = []

      this.close()
    },
    clickBreadcrumbDepartment(v) {
      if (v.id === this.currentDepartment.id) {
        return
      }
      return this.clickDepartmentSubdivision(v)
    },
    clickDepartmentSubdivision(v) {
      this.currentDepartment = getDepartmentByID(allDepartments, v.id)
      this.departments = this.currentDepartment.children
    },
    async search(keywords, activatedTab) {
       //恢复状态
      if (!keywords) {
        this.employeesEnd = limit

        this.employees = allEmployees.slice(0, this.employeesEnd)

        this.departments = getDepartmentsByParentID(
          allDepartments,
          this.currentDepartment.id
        )

        return
      }

      if (activatedTab === '部门') {
        var filteredDepartments = []
        walkDepartments(allDepartments, keywords => {
          if (c.name.includes(keywords)) {
            filteredDepartments.push(c)
          }
        })
        this.departments = filteredDepartments
        return
      }

      this.loading = true

      const [err,r]  = await this.loadEmployees(null, keywords)
      this.loading = false
      if(err){
        handleErr(err)
        return 
      }
      
      if(!r || !r.length){
        this.employees = []
        return
      }
 
      const foundUserIds = r.map(c=>c.userId)
      var filteredEmployees = []
      allEmployees.filter(v => {
        if (foundUserIds.includes(v.userId)) {
          filteredEmployees.push(v)
        }
      })

      this.employees = filteredEmployees
    },
    selectEmployee(v) {
      this.allSelectedEmployees.push(v)
      this.selectedEmployeesEnd++
    },
    unselectEmployee(v) {
      this.allSelectedEmployees = this.allSelectedEmployees.filter(
        item => item.id !== v.id
      )

      this.selectedEmployeesEnd--
    },
    selectAllEmployees() {
      for (var c of allEmployees) {
        if (!this.allSelectedEmployees.find(item => item.id === c.id)) {
          this.allSelectedEmployees.push(c)
        }
      }
    },
    unselectAllEmployees() {
      this.allSelectedEmployees = []
    },
    selectDepartment(v) {
      const n = [...this.selectedDepartments]
      n.push(v)
      this.selectedDepartments = n
    },
    unselectSelectedEmployee(v) {
      return this.unselectEmployee(v)
    },
    unselectDepartment(v) {
      console.log('unselect', v)
      this.selectedDepartments = this.selectedDepartments.filter(
        item => item.id !== v.id
      )
    },
    selectAllDepartments() {
      this.selectedDepartments = this.departments.filter(item => !item.disabled)
    },
    unselectAllDepartments() {
      this.selectedDepartments = []
    },
    clear() {
      this.selectedEmployees = []
      this.allSelectedEmployees = []
      this.selectedDepartments = []
    },
    leftListBottomReached() {
      this.employeesEnd += limit
      this.employees = allEmployees
        // .filter(item => {
        //   return item.departmentIds.includes(this.currentDepartment.id)
        // })
        .slice(0, this.employeesEnd)
    },
    rightListBottomReached() {
      this.selectedEmployeesEnd += limit
    }
  }
}
</script>

<style>
.departmentsEmployeesMixingSelectorDialog .el-dialog__header {
  display: none !important;
}
.departmentsEmployeesMixingSelectorDialog .el-dialog__body {
  padding: 0 !important;
}
</style>
