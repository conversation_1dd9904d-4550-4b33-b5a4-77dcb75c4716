const findParentDepartment = (departments, parentID) => {
  var r = null
  for (var c of departments) {
    if (c.id === parentID) {
      r = c
      break
    }

    if (c.children && c.children.length) {
      r = findParentDepartment(c.children, parentID)
    }
  }

  return r
}
const fillParentDepartments = (department, paths, namePaths) => {
  var parents = []
  //最后一层是自己
  for (var i = 0; i < paths.length - 1; i++) {
    parents.push({
      id: paths[i],
      name: namePaths[i]
    })
  }

  department.parentDepartments = parents
}
const formatDepartments = data => {
  var r = []

  for (const c of data) {
    const parent = findParentDepartment(r, c.parentId)
    const department = {
      id: c.id,
      name: c.name,
      //增补字段
      parentId: parent ? parent.id : 0,
      //不改为后端一致了，因为后端有叫userCount 有叫memberCount这里统一为employeeTotal
      employeeTotal: c.memberCount ? c.memberCount : c.member<PERSON>um,
      children: []
    }

    fillParentDepartments(department, c.path, c.namePath)
    if (!parent) {
      r.push(department)
    } else {
      parent.children.push(department)
    }
  }

  return r
}

export default formatDepartments
