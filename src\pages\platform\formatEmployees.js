const formatEmployees = employees => {
  if (!employees || !employees.length) {
    return []
  }
  var r = []
  for (const c of employees) {
    var departmentIds = []
    if (c.deptMember && c.deptMember.length) {
      departmentIds = c.deptMember.map(item => item.deptId)
    }
    if (c.deptId && c.deptId.length) {
      departmentIds = c.deptId
    }
    const employee = {
      id: c.id ? c.id : c.userId,
      userId: c.userId,
      name: c.name,
      disabled: c.disabled === null ? !c.enable : c.disabled,
      departmentIds
    }
    //后端传入了错误的数据，先忽略
    if(!c.name){
      continue
    }
    r.push(employee)
  }

  return r
}
export default formatEmployees
