<template>
  <div v-if="!loading">
    <div style="display: flex">
      <span style="color: #657180; margin-right: 4px">展示完整信息</span>
      <el-switch :value="!masking" @click.native="openDialog" />
    </div>

    <el-dialog
      title="数据安全认证"
      :visible.sync="dialogVisible"
      width="400px"
      :show-close="false"
    >
      <div class="content">
        <el-input :value="phone" disabled style="margin-bottom: 10px" />
        <!-- <Captcha
          style="width: 100%; margin-bottom: 10px; padding: 0"
          v-model="captcha"
          label="图形验证码"
        /> -->
        <div style="position: relative">
          <el-input
            id="smsCode"
            v-model="smsCode"
            placeholder="短信验证码"
            maxlength="6"
          />
          <div class="sms-code-button">
            <el-button
              type="text"
              :loading="isLoading"
              @click="send"
              class="send-button"
              v-show="!countdown"
            >
              获取验证码
            </el-button>
            <div v-show="countdown" style="padding-top: 5px; color: #999">
              <Countdown
                template="%ds后重新获取"
                ref="countdown"
                @finish="countDownFinish"
              />
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <Button type="primary" :click="handleConfirmClick">确 定</Button>
      </span>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="refreshShown" :show-close="false">
      <p>您在其他页面改变了展示信息的状态，是否刷新当前页面？</p>
      <div slot="footer" style="text-align: right">
        <el-button @click="refreshShown = false">取 消</el-button>
        <el-button type="primary" @click="handleRefresh">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Countdown from 'kit/components/ui/countdown.vue'
import handleError from 'kit/helpers/handleError'
import makePlatformClient from 'kit/services/platform/makeClient'
import Captcha from 'kit/pages/contract/captcha.vue'
import { desensitizationManager } from 'kit/helpers/dataMaskingHandler.js'
import Button from 'kit/components/marketing/admin/button.vue'
import { saveToken } from '@olading/olading-business-ui'
// import helpImg from './helpIcon.png'
// import helpImg2 from './helpIconHover.png'
import jsCookie from 'js-cookie'
const platformClient = makePlatformClient()

export default {
  components: {
    Button,
    Countdown,
    Captcha
  },
  data() {
    return {
      loading: true,
      value: true,
      dialogVisible: false,
      refreshShown: false,
      smsCode: '',
      isLoading: false,
      countdown: false,
      phone: '',
      masking: false,
      captcha: {
        token: '',
        answer: ''
      },
      otp: {
        answer: '',
        token: ''
      }
    }
  },
  async mounted() {
    //为了保证login一定在所有api之前
    if (window.location.href.includes('/kxy?')) {
      return
    }
    const [error, result] = await platformClient.merchantPlatformProfile({
      body: {}
    })
    if (error) return handleError(error)

    console.log('拿到的数据1111', result)

    sessionStorage.setItem('HRUserId', result.data.user.id)

    sessionStorage.setItem(
      '__stats',
      JSON.stringify({
        businessId: result.data.merchant.externalId,
        uc: result.data.user.externalId
        // userIden:result.data.user.id
      })
    )

    const { user, masking } =
      desensitizationManager.generateSensitiveFieldFromCipher(result.data)

    // this.phone = user.cellPhone
    this.phone = result.data.cellPhoneTyy
    this.masking = masking
    this.loading = false
    //为了跨越项目在编辑时候先弹出验证
    window.globalDesensitizationSwitch = this
    localStorage.setItem('masking', this.masking)

    window.addEventListener('storage', data => {
      if (data.key !== 'masking') {
        return
      }

      this.refreshShown = true
    })
  },

  methods: {
    handleRefresh() {
      //todo 导致返回出问题 大概率
      this.$router.go(0)
      this.refreshShown = false
    },

    async handleConfirmClick() {
      if (!this.otp.token) {
        handleError('请先获取验证码')
        return
      }

      if (!this.smsCode) {
        handleError('请输入短信验证码')
        return
      }

      const params = {
        masking: !this.masking,
        challenge: this.smsCode,
        otpToken: this.otp.token
      }

      const [error, result] = await platformClient.merchantPlatformRenewToken({
        body: params
      })
      if (error) return handleError(error)

      const token = result.data.token
      this.reloadPage(token)
      // saveToken(token)
      // setTimeout(()=>window.location.reload(),300)
    },
    reloadPage(token) {
      localStorage.setItem('masking', !this.masking)
      localStorage.setItem('token', token)
      jsCookie.set('__token__', token)

      const url = new URL(window.location.href)
      url.searchParams.set('token', token)
      url.searchParams.set('r', 'r' + Math.random())
      setTimeout(() => window.location.replace(url.toString()), 100)
    },
    async openDialog() {
      if (!this.masking) {
        const params = {
          masking: !this.masking
        }

        const [error, result] = await platformClient.merchantPlatformRenewToken(
          {
            body: params
          }
        )
        if (error) return handleError(error)

        const token = result.data.token
        this.reloadPage(token)
        return
      }
      this.dialogVisible = true
      this.resetDialogForm()
    },
    countDownFinish() {
      this.countdown = false
      this.otp.token = ''
      this.smsCode = ''
    },
    resetDialogForm() {
      // this.countdown = false
      // this.otp.token = ""
      // this.smsCode = ""
    },
    async send() {
      if (!this.phone) {
        handleError('请输入手机号')
        return
      }

      // if (!this.captcha.token || !this.captcha.answer) {
      //   handleError('请先完成图形验证码')
      //   return
      // }

      this.isLoading = true
      await this._send()
      document.getElementById('smsCode').focus()
    },
    async _send() {
      const params = {
        otpType: 'SMS',
        cellPhone: this.phone,
        captchaToken: this.captcha.token,
        captchaAnswer: this.captcha.answer
      }
      params.receiver = params.cellPhone
      delete params.cellPhone

      const [err, r] = await platformClient.merchantPlatformCreateOtp({
        body: params
      })

      this.isLoading = false

      if (err) {
        handleError(err)
        return
      }

      if (!r.data) {
        handleError('验证码获取失败')
        return
      }

      this.countdown = true
      this.$refs.countdown.start()
      this.otp.token = r.data.token
    }
  }
}
</script>
<style scoped>
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}
.content {
  padding: 20px 50px;
}
.sms-code-button {
  position: absolute;
  top: 0;
  right: 10px;
}
.image-container {
  /* position: relative;
  display: inline-block; */
  margin-right: 4px;
  margin-top: 3px;
}
</style>