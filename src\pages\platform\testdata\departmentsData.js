const r = {
  success: true,
  message: null,
  errorCode: '0',
  data: {
    list: [
      {
        id: 101005,
        parentId: null,
        effectiveTime: null,
        typeId: null,
        name: '组织架构',
        parentName: null,
        index: 0,
        path: [101005],
        namePath: ['组织架构'],
        members: null,
        children: [
          {
            id: 101006,
            parentId: 101005,
            effectiveTime: null,
            typeId: null,
            name: 'KXY9999',
            parentName: '组织架构',
            index: 0,
            path: [101005, 101006],
            namePath: ['组织架构', 'KXY9999'],
            members: null,
            children: null,
            childrenNum: 2,
            descendantMemberNum: 4,
            deleted: null,
            memberNum: 3,
            externalId: null,
            dingDeptId: null,
            descendantMemberCount: 4,
            memberCount: 3,
            parentDeptName: '组织架构',
            principalUserIds: [],
            principalUserName: '',
            typeName: null
          }
        ],
        childrenNum: 1,
        descendantMemberNum: 4,
        deleted: null,
        memberNum: 0,
        externalId: null,
        dingDeptId: null,
        descendantMemberCount: 4,
        memberCount: 0,
        parentDeptName: null,
        principalUserIds: [],
        principalUserName: '',
        typeName: null
      },
      {
        id: 101006,
        parentId: 101005,
        effectiveTime: null,
        typeId: null,
        name: 'KXY9999',
        parentName: '组织架构',
        index: 0,
        path: [101005, 101006],
        namePath: ['组织架构', 'KXY9999'],
        members: null,
        children: [
          {
            id: 101201,
            parentId: 101006,
            effectiveTime: null,
            typeId: null,
            name: 'A部门',
            parentName: 'KXY9999',
            index: 0,
            path: [101005, 101006, 101201],
            namePath: ['组织架构', 'KXY9999', 'A部门'],
            members: null,
            children: null,
            childrenNum: 1,
            descendantMemberNum: 1,
            deleted: null,
            memberNum: 1,
            externalId: null,
            dingDeptId: null,
            descendantMemberCount: 1,
            memberCount: 1,
            parentDeptName: 'KXY9999',
            principalUserIds: [],
            principalUserName: '',
            typeName: null
          },
          {
            id: 101802,
            parentId: 101006,
            effectiveTime: null,
            typeId: null,
            name: 'B',
            parentName: 'KXY9999',
            index: 1,
            path: [101005, 101006, 101802],
            namePath: ['组织架构', 'KXY9999', 'B'],
            members: null,
            children: null,
            childrenNum: 0,
            descendantMemberNum: 0,
            deleted: null,
            memberNum: 0,
            externalId: null,
            dingDeptId: null,
            descendantMemberCount: 0,
            memberCount: 0,
            parentDeptName: 'KXY9999',
            principalUserIds: [],
            principalUserName: '',
            typeName: null
          }
        ],
        childrenNum: 2,
        descendantMemberNum: 4,
        deleted: null,
        memberNum: 3,
        externalId: null,
        dingDeptId: null,
        descendantMemberCount: 4,
        memberCount: 3,
        parentDeptName: '组织架构',
        principalUserIds: [],
        principalUserName: '',
        typeName: null
      },
      {
        id: 101201,
        parentId: 101006,
        effectiveTime: null,
        typeId: null,
        name: 'A部门',
        parentName: 'KXY9999',
        index: 0,
        path: [101005, 101006, 101201],
        namePath: ['组织架构', 'KXY9999', 'A部门'],
        members: null,
        children: [
          {
            id: 101800,
            parentId: 101201,
            effectiveTime: null,
            typeId: null,
            name: 'A1',
            parentName: 'A部门',
            index: 0,
            path: [101005, 101006, 101201, 101800],
            namePath: ['组织架构', 'KXY9999', 'A部门', 'A1'],
            members: null,
            children: null,
            childrenNum: 1,
            descendantMemberNum: 0,
            deleted: null,
            memberNum: 0,
            externalId: null,
            dingDeptId: null,
            descendantMemberCount: 0,
            memberCount: 0,
            parentDeptName: 'A部门',
            principalUserIds: [],
            principalUserName: '',
            typeName: null
          }
        ],
        childrenNum: 1,
        descendantMemberNum: 1,
        deleted: null,
        memberNum: 1,
        externalId: null,
        dingDeptId: null,
        descendantMemberCount: 1,
        memberCount: 1,
        parentDeptName: 'KXY9999',
        principalUserIds: [],
        principalUserName: '',
        typeName: null
      },
      {
        id: 101802,
        parentId: 101006,
        effectiveTime: null,
        typeId: null,
        name: 'B',
        parentName: 'KXY9999',
        index: 1,
        path: [101005, 101006, 101802],
        namePath: ['组织架构', 'KXY9999', 'B'],
        members: null,
        children: [],
        childrenNum: 0,
        descendantMemberNum: 0,
        deleted: null,
        memberNum: 0,
        externalId: null,
        dingDeptId: null,
        descendantMemberCount: 0,
        memberCount: 0,
        parentDeptName: 'KXY9999',
        principalUserIds: [],
        principalUserName: '',
        typeName: null
      },
      {
        id: 101800,
        parentId: 101201,
        effectiveTime: null,
        typeId: null,
        name: 'A1',
        parentName: 'A部门',
        index: 0,
        path: [101005, 101006, 101201, 101800],
        namePath: ['组织架构', 'KXY9999', 'A部门', 'A1'],
        members: null,
        children: [
          {
            id: 101801,
            parentId: 101800,
            effectiveTime: null,
            typeId: null,
            name: 'A11',
            parentName: 'A1',
            index: 0,
            path: [101005, 101006, 101201, 101800, 101801],
            namePath: ['组织架构', 'KXY9999', 'A部门', 'A1', 'A11'],
            members: null,
            children: null,
            childrenNum: 0,
            descendantMemberNum: 0,
            deleted: null,
            memberNum: 0,
            externalId: null,
            dingDeptId: null,
            descendantMemberCount: 0,
            memberCount: 0,
            parentDeptName: 'A1',
            principalUserIds: [],
            principalUserName: '',
            typeName: null
          }
        ],
        childrenNum: 1,
        descendantMemberNum: 0,
        deleted: null,
        memberNum: 0,
        externalId: null,
        dingDeptId: null,
        descendantMemberCount: 0,
        memberCount: 0,
        parentDeptName: 'A部门',
        principalUserIds: [],
        principalUserName: '',
        typeName: null
      },
      {
        id: 101801,
        parentId: 101800,
        effectiveTime: null,
        typeId: null,
        name: 'A11',
        parentName: 'A1',
        index: 0,
        path: [101005, 101006, 101201, 101800, 101801],
        namePath: ['组织架构', 'KXY9999', 'A部门', 'A1', 'A11'],
        members: null,
        children: [],
        childrenNum: 0,
        descendantMemberNum: 0,
        deleted: null,
        memberNum: 0,
        externalId: null,
        dingDeptId: null,
        descendantMemberCount: 0,
        memberCount: 0,
        parentDeptName: 'A1',
        principalUserIds: [],
        principalUserName: '',
        typeName: null
      }
    ],
    total: null
  }
}

export r.data