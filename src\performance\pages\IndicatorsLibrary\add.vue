<template>
  <div class="library-detail">
    <header class="per-header">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span v-if="!$route.query.id">新增考核指标</span>
          <span v-if="$route.query.id">编辑考核指标</span>
        </el-col>
      </el-row>
    </header>
    <div class="addform-mian">
      <el-form
        :model="addForm"
        ref="addForm"
        label-width="150px"
        class="add-class"
        :rules="rules"
      >
        <el-form-item prop="bankId" label="考核指标分组">
          <el-cascader
            v-model="addForm.bankId"
            placeholder="考核指标分组"
            :options="treeData"
            :props="cascaderProps"
            clearable
            style="width:420px"
            :show-all-levels="false"
          ></el-cascader>
        </el-form-item>
        <el-form-item prop="name" label="考核指标名称">
          <el-input
            v-model.trim="addForm.name"
            ref="input"
            placeholder="请输入考核指标名称"
            style="width:420px"
            @input="handleInput(addForm.name,'input',50,'考核指标名称')"
          ></el-input>
        </el-form-item>
        <el-form-item prop="type">
          <span slot="label"
            >考核指标类型
            <el-popover placement="bottom" width="800" trigger="hover">
              <legeng :type="addForm.type"></legeng>
              <i class="iconfont-per icon-tubiao1" slot="reference"></i>
            </el-popover>
          </span>
          <el-radio-group v-model="addForm.type" @change="changeType(addForm.type)">
            <el-radio :label="1">定量考核指标</el-radio>
            <el-radio :label="2">定性考核指标</el-radio>
            <el-radio :label="3">加分项</el-radio>
            <el-radio :label="4">减分项</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="description" label="考核指标说明">
          <el-input
            :autosize="{ minRows: 5, maxRows: 5 }"
            type="textarea"
            maxlength="300"
            show-word-limit
            v-model="addForm.description"
            ref="description"
            style="width:420px"
            placeholder="请输入考核指标说明"
          ></el-input>
        </el-form-item>
        <el-form-item prop="scoreStandard" label="评价标准">
          <el-input
            :autosize="{ minRows: 5, maxRows: 5 }"
            type="textarea"
            maxlength="300"
            show-word-limit
            ref="scoreStandard"
            style="width:420px"
            v-model="addForm.scoreStandard"
            placeholder="请输入评价标准"
          ></el-input>
        </el-form-item>
        <el-form-item prop="maxScore" label="评分上限">
          <span slot="label">
            <span v-if="addForm.type=='1'||addForm.type=='2'">评分上限</span>
            <span v-if="addForm.type=='3'">加分上限</span>
            <span v-if="addForm.type=='4'">减分上限</span>
          </span>
          <el-input
              v-model="addForm.maxScore"
              placeholder="请输入评分上限"
              oninput="value = value.toString().match(new RegExp('^\\d+(?:\\.\\d{0,2})?'))"
              @change="handleBlur"
            >
              <template slot="append"
                >分</template
              >
            </el-input>
        </el-form-item>
        <el-form-item prop="weight" label="考核指标权重" v-if="addForm.type=='1'||addForm.type=='2'">
          <span slot="label"
            >考核指标权重
            <el-popover placement="bottom" width="500" trigger="hover">
              <legeng type="5"></legeng>
              <i class="iconfont-per icon-tubiao1" slot="reference"></i>
            </el-popover>
          </span>
          <el-input
            v-model="addForm.weight"
            placeholder="请输入考核指标权重"
            style="width:420px"
          >
            <template slot="append"
              >％</template
            >
          </el-input>
        </el-form-item>
        <el-form-item prop="scoreType" label="评分方式" v-if="addForm.type==1">
          <el-radio-group v-model="addForm.scoreType" @change="handleChange">
            <el-radio :label="1">直接输入</el-radio>
            <el-radio :label="2">公式计算</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="计算公式" v-if="addForm.scoreType == '2'" prop="dataRuleList">
          <old-table
            class="table-main"
            :data="addForm.dataRuleList"
            style="line-height:20px"
            :headerData="headerData"
            :isShowOperation="isShowOperation"
            v-if="addForm.dataRuleList&&addForm.dataRuleList.length>0"
          >
            <template slot="date-header">
                <div v-if="addForm.dataRuleType == '1'">
                  {{ addForm.dataSource }}
                </div>
                <div v-if="addForm.dataRuleType == '2'">
                  目标达成率
                  <el-tooltip
                    content="目标达成率 = （实际完成值 ÷ 目标值） x 100%"
                  >
                    <i class="iconfont-per icon-help" style="color:#9EA5BD;font-size:16px"></i>
                  </el-tooltip>
                </div>
              </template>
            <template slot="date" slot-scope="scope">
              <div v-if="scope.msg.row.max">
                <div v-if="addForm.dataRuleType == '1'">
                  {{ scope.msg.row.min + addForm.dataUnit }}~{{
                    scope.msg.row.max + addForm.dataUnit
                  }}
                </div>
                <div v-if="addForm.dataRuleType == '2'">
                  {{ scope.msg.row.min}}%~{{
                    scope.msg.row.max
                  }}%
                </div>
              </div>
              <div v-if="!scope.msg.row.max">
                {{ scope.msg.row.min + addForm.dataUnit }}以上
              </div>
            </template>
            <template slot="score" slot-scope="scope">
              <div>{{ scope.msg.row.score }}分</div>
            </template>
          </old-table>
          <el-button type="text" @click="handleShow">设置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="mian-footer">
      <el-button  @click="handleDelete">取消</el-button>
      <el-button type="primary" @click="handleSave('addForm')">保存</el-button>
    </div>
    <indicators
      :addForm="addForm"
      :depList='depList'
      :userList='userList'
      v-if="isShow"
      @close="isShow = false"
      @save="commit"
    ></indicators>
  </div>
</template>

<script>
import legeng from "./components/legeng.vue";
import indicators from "./components/indicators.vue";
import { getIndicatorTree,getDepartmentTree,IndicatorAdd,getIndicatorDetail,updateIndicatorDetail,indicatorCheckName,getUserList} from "performance/store/api.js";
export default {
  components: {
    legeng,
    indicators
  },
  data() {
    return {
      isShow: false,
      direction: "rtl",
      isShowOperation: false,
      treeData: [],
      userList:[],
      depList:[],
      cascaderProps: {
        children: "children",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false
      },
      name:'',
      addForm: {
        name: "", //考核指标名称
        type: 1, //指标类型:1-定量考核指标;2-定型考核指标;3-加分项;4-减分项
        bankId: "", //考核指标分组id
        description: "", //考核指标说明
        scoreStandard: "", //评分标准
        weight: null, //指标权重
        maxScore: "", //评分上限
        scoreType: 1, //评分方式:1-直接打分;2-公式计算得分
        dataRuleList: [
          // //计算公式
          // {
          //   max: 1, //规则范围结束值
          //   min: 0, //规则范围开始值
          //   score: 10 //规则范围内的评分
          // },
          // {
          //   max: 2, //规则范围结束值
          //   min: 1, //规则范围开始值
          //   score: 20 //规则范围内的评分
          // },
          // {
          //   max: 3, //规则范围结束值
          //   min: 2, //规则范围开始值
          //   score: 30 //规则范围内的评分
          // }
        ],
        dataUnit: "", //单位
        dataSource: "", //数据来源
        dataMarkerId: null, //数据来源指定人id
        dataRuleType: 1 //计算规则:1-按实际完成值计算;2-按目标达成率计算
      },
      rules: {
        name: {
          required: true,
          validator: (rule, value, callback) => {
            if (value === "") {
              callback("请输入考核指标名称");
            } else {
              if(value!==this.name){
                indicatorCheckName({name:value}).then(res=>{
                  console.log(res.data);
                  if(res.data.nameExist){
                    callback('该指标名称已存在')
                  }else{
                    callback();
                  }
                })
              }else{
                callback();
              }
            }
          },
          trigger: ["blur"]
        },
        bankId: {
          required: true,
          message: "请输入考核指标分组",
          trigger: "change"
        },
        scoreStandard: {
          required: true,
          message: "请输入评价标准",
          trigger: "change"
        },
        maxScore: {
          required: true,
          validator: (rule, value, callback) => {
            console.log(value);
            if (value === "" || value === null) {
              callback(new Error("请输入评分上限"));
            } else {
              const status = !new RegExp('^[1-9]\\d{0,2}(\\.\\d{1,2})?$').test(value);
              console.log(value, status);
              if (status) {
                callback(new Error("评分上限为 1~999"));
              }
              callback();
            }
          },
          trigger: "blur"
        },
        weight:{
          required: false,
          validator: (rule, value, callback) => {
          if (value === "") {
              callback();
          }else{
            if (!new RegExp('(^[0-9]\\d*$)').test(value)) {
              callback(new Error("请输入正整数"));
            } else {
              if (!new RegExp('^(?:[1-9]?\\d|100)$').test(value)) {
                callback(new Error("考核指标权重为0～100"));
              }
              callback();
            }
          }
          },
          trigger: ["change", "blur"]
        },
        dataRuleList:{
          required: true,
          message: "请输入计算公式",
          trigger: "change"
        }
      },
      headerData: [
        { title: "销售额", label: "date", align: "left", slot: "date", slotHeader: "date-header", },
        {
          title: "指标评分",
          label: "score",
          align: "left",
          slot: "score"
        }
      ]
    };
  },
  created() {
    let { bankId } = this.$route.query;
    if(bankId){
      this.addForm.bankId = Number(bankId);
    }
    if(this.$route.query.id){
      this.getIndicatorDetail()
    }
    this.getCatalogTree();
    this.getDepartmentTree()
    this.getUserList()
  },
  mounted() {
  },
  watch: {
    addForm(val) {
      console.log(val);
    }
  },
  methods: {
    //姓名查重
    handleName(value){
      console.log(value);
      indicatorCheckName({name:value}).then(res=>{
        console.log(res.data);
        if(res.data.nameExist){
          this.$message.error('该指标名称已存在')
          this.addForm.name=''
        }
      })
    },
    changeType(value){
      console.log(value);
      this.addForm.scoreType=1
      if (value == '3' || value == '4') {
        this.addForm.weight = null;
      }
    },
    //评分方式改变
    handleChange(type) {
      if(type === 2) {
       this.$nextTick(() => {
        const box = this.$el.querySelector(".addform-mian");
        box.scrollTop = box.scrollHeight;
      });
      }

    },
    //获取部门树
    async getDepartmentTree() {
      const res = await getDepartmentTree();
      if (res.success) {
        this.depList = res.data;
      } else {
        this.$message.error(res.msg);
      }
    },
    //获取人员列表
    async getUserList() {
      const res = await getUserList();
      if (res.success) {
        this.userList = res.data;
      } else {
        this.$message.error(res.msg);
      }
    },
    oninput(value) {
      if (!isNaN(value)) {
        return null;
      }
      console.log(value);
      return value.toString().match(new RegExp('^\\d+(?:\\.\\d{0,2})?'));
    },
    async getIndicatorDetail() {
      let { id } = this.$route.query;
      const res = await getIndicatorDetail({id:id});
      if (res.success) {
        let data=res.data
        this.addForm={
          id:data.id,
          name: res.data.name, //考核指标名称
          type: data.type, //指标类型:1-定量考核指标;2-定型考核指标;3-加分项;4-减分项
          bankId: data.bankId, //考核指标分组id
          description: data.description, //考核指标说明
          scoreStandard: data.scoreStandard, //评分标准
          weight: data.weight, //指标权重
          maxScore: data.maxScore, //评分上限
          scoreType: data.scoreType, //评分方式:1-直接打分;2-公式计算得分
          dataRuleList: data.dataRule,
          dataUnit: data.dataUnit, //单位
          dataSource: data.dataSource, //数据来源
          dataMarkerId: data.dataMarkerId, //数据来源指定人id
          dataRuleType: data.dataRuleType//计算规则:1-按实际完成值计算;2-按目标达成率计算
        }
        this.name=JSON.parse(JSON.stringify(data.name))
      } else {
        this.$message.error(res.msg);
      }
    },
    //获取分类树
    async getCatalogTree() {
      let { bankType } = this.$route.query;
      let res = await getIndicatorTree();
      if (res.success) {
        if (bankType) {
          res.data[0].children.map(item => {
            if (item.type == bankType) {
              this.treeData.push(item);
            }
          });
        } else {
          this.treeData = res.data[0].children;
        }
      }
      this.treeData = this.setChildren(this.treeData);
    },
    //无children设置null
    setChildren(arr) {
      arr.map(item => {
        if (item.children.length === 0) {
          item.children = null;
        } else {
          this.setChildren(item.children);
        }
      });
      return arr;
    },
    handleShow() {
      if(this.addForm.maxScore){
        this.isShow = true;
      }else{
        this.$message.error('请先设置评分上限')
      }
    },
    handleBlur(val){
      this.addForm.maxScore = val
      if(val==''){
        this.addForm.dataRuleList.map(item=>{
          item.score=''
        })
      }
    },
    handleClick(data) {
      console.log(data);
    },
    commit(val) {
      this.addForm = JSON.parse(JSON.stringify(val));
      this.isShow = false;
    },
    handleDelete(){
      this.$router.go(-1);
    },
    handleSave(formName){
      console.log(this.addForm);
      this.$refs[formName].validate(async valid => {
        if (valid) {
          this.addForm.description=this.addForm.description.trim()
          this.addForm.scoreStandard=this.addForm.scoreStandard.trim()
          if(this.addForm.weight==''){
            this.addForm.weight=null
          }
          if(!this.$route.query.id){
            const res = await IndicatorAdd({...this.addForm});
            if (res.success) {
              this.$message.success('新增成功')
              this.$router.go(-1);
            }else {
              this.$message.error(res.msg);
            }
          }else{
            const res = await updateIndicatorDetail({...this.addForm});
            if (res.success) {
              this.$message.success('编辑成功')
              this.$router.go(-1);
            }else {
              this.$message.error(res.msg);
            }
          }
        } else {
          console.log("error submit!!");
            this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
          return false;
        }
      });
    },
    handleInput(data,ref,num,name){
       if(data.length>num){
         if(name=='考核指标名称'){
           this.addForm.name= data.substr(0, num - 1)
         }else if(name=='考核指标说明'){
           this.addForm.description= data.substr(0, num - 1)
         }else{
           this.addForm.scoreStandard= data.substr(0, num - 1)
         }
         this.$message.error(name+'最大不超过'+num+'个字符')
          console.log(data);
         this.$refs[ref].blur();
       }
    },
  }
};
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";
.library-detail{
  height: calc(100vh - 81px);
}
.main {
  min-width: 0;
  flex: 1;
  margin-left: 24px;
  display: flex;
}
.per-header {
  font-size: 16px;
  height: 61px;
  margin: 0 20px;
  border-bottom: 1px solid #eaeaea;
  line-height: 61px;
  .row {
    justify-content: space-between;
    align-items: center;
  }
}
.add-class {
  width: 580px;
  margin: 0 auto;
  margin-top: 20px;
}
.is-title {
  margin-top: 10px;
  font-size: 16px;
  color: #070f29;
  letter-spacing: 0;
  line-height: 16px;
}
.addName-bn {
  background: #f1f1f1;
  border-radius: 16px;
  display: flex;
  width: 100px;
  height: 32px;
  margin: 0 0 5px 10px;
}
.icon-tianjiachengyuan {
  font-size: 32px;
}
.drawer-main {
  .el-form-item {
    margin-bottom: 12px;
  }
}
.source-input {
  /deep/.el-input-group__append {
    padding: 0 5px !important;
  }
}
.cource-span {
  height: 32px;
  line-height: 32px;
}
.add-bn {
  color: $mainColor;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}
.addform-mian{
  height: calc(100vh - 210px);
  overflow-y: auto;
  overflow-x: hidden;
}
.mian-footer {
  position: fixed;
  bottom: 0;
  width: calc(100% - 223px);
  height: 30px;
  padding: 20px 0 20px 0px;
  border-top: 1px solid #e5e5e5;
  background: #fff;
  text-align: center;
  z-index: 99;
  .btn {
    margin: 0 auto;
  }
}
/deep/ .el-textarea__inner {
  padding: 5px 15px 30px;
}
/deep/ .el-textarea .el-input__count {
  width: 92%;
  right: 20px;
  height: 30px;
  bottom: 1px;
  text-align: right;
}
.iconfont-per{
  color:#9EA5BD
}
.table-main{
  width:420px
}
</style>
