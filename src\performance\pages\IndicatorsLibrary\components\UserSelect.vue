<template>
  <div class="addPerson">
    <el-dialog
      :title="isUser ? '选择部门' : '选择人员'"
      :visible.sync="showDialog"
      @close="closeDialog"
      width="740px"
      :destroy-on-close="true"
    >
      <span class="dialogContent">
        <div class="left" v-loading="loading">
          <el-input
            v-model="keyAdmin"
            :placeholder="isUser ? '请输入部门名称' : '请输入员工姓名'"
            autocomplete="off"
            suffix-icon="iconiconfonticonfontsousuo1 iconfont"
            @keydown.enter.native="handleSearch"
          ></el-input>

          <div class="down-tree">
            <div class="tabs" v-if="clickList">
              <span
                v-for="(item, index) in clickList"
                :key="item.id"
                @click="handleTab(item, index)"
              >
                <span v-if="index == 0">{{ item.name }}</span>
                <span v-if="index != 0"
                  ><i class="el-icon-arrow-right"></i>{{ item.name }}</span
                >
              </span>
            </div>
            <div class="anniu" v-if="!isOnly">
              <el-checkbox
                style="margin-right: 20px"
                v-model="checkAll"
                @change="handleCheckAllChange(checkAll, rightList)"
                :disabled="isOnly"
                >全选</el-checkbox
              >
              <div>
                <span class="zibumen">同时选中子部门成员</span>
                <el-switch
                  style="margin-bottom: 3px"
                  v-model="isChildren"
                  :disabled="isOnly"
                  @change="handleCheckChildren(isChildren)"
                ></el-switch>
              </div>
            </div>
            <div class="left-class">
              <div v-for="item in currLeftData" :key="item.id">
                <div
                  v-if="!item.employeeName"
                  class="item-left"
                  @click="handleSelect(item)"
                >
                  <div class="left-name">
                    <i class="icon iconfont-per icon-bumen"></i>
                    {{ item.name }}
                  </div>
                  <div style="color: #9ea3ba">
                    <span style="color: #cccccc">|</span
                    ><span class="xiaji">下级</span>
                  </div>
                </div>
                <span v-if="item.employeeName">
                  <el-checkbox
                    v-model="item.checked"
                    :class="isOnly ? 'isOnly' : ''"
                    @change="handleCheckChange(item)"
                    v-if="item.employeeName"
                  ></el-checkbox>
                  <i class="icon iconfont-per icon-yuangong1"></i>
                  <span :title="item.name" class="show-ellipsis">{{
                    item.name.substr(0, 10)
                  }}</span>
                  <span v-if="item.mobile">{{ item.mobile }}</span>

                  <span class="groups">{{ item.subsidiaryName }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
        <i class="divider"></i>
        <ul class="right">
          <div class="statistics">
            <span class="num"
              >已选
              <span :class="{ orange: rightList.length }">{{
                rightList.length
              }}</span
              ><span v-if="!isUser">人</span
              ><span v-if="isUser">部门</span></span
            >
            <span
              class="clear"
              @click="handleAllDelete"
              v-if="rightList.length > 0"
              >清空</span
            >
          </div>

          <div class="right-list">
            <li v-for="(item, index) in rightList" :key="item.id">
              <el-tooltip v-if="item.name.length > 10" placement="top-start">
                <p slot="content">
                  {{ item.name }}
                  {{ item.mobile }}
                </p>
                <p>
                  {{ item.name.substr(0, 11) + "..." }}
                  {{ item.mobile }}
                </p>
              </el-tooltip>

              <p v-else>{{ item.name }} {{ item.mobile }}</p>

              <p class="subject" v-if="isDifferent">
                {{ item.subsidiaryName }}
              </p>

              <!-- <span>{{ item.name }}</span> -->

              <i class="el-icon-close" @click="handleDelete(item, index)"></i>
            </li>
            <p class="tip" v-if="!rightList.length && !isUser">
              请在左侧选择员工
            </p>
            <p class="tip" v-if="!rightList.length && isUser">
              请在左侧选择部门
            </p>
          </div>
        </ul>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button
          type="primary"
          :disabled="!rightList.length"
          @click="confirmPerson"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { data1 } from "./data";
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    select: {
      type: Array,
      default: () => [],
    },
    selectIdList: {
      type: Array,
      default: () => [],
    },
    isOnly: {
      type: Boolean,
      default: false,
    },
    isDifferent: {
      type: Boolean,
      default: false,
    },
    isUser: {
      type: Boolean,
      default: false,
    },
    userList: {
      type: Array,
      default: () => [],
    },
    isMapDepartmentTree: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showDialog: true,
      loading: false,
      keyAdmin: "", //key
      currLeftData: [], //当前左侧已选
      rightList: [], //考右侧数据
      currRightData: [], //当前已选数据
      checked: true,
      data: [], //考勤人员
      selectOrg: [],
      clickList: [],
      checkAll: false,
      isChildren: true,
      alllist: [],
    };
  },
  mounted() {
    this.loading = true;
    this.getPersonList();
  },
  watch: {
    isUser(val) {
      console.log("isUser", val);
    },
  },
  methods: {
    handleSearch() {
      this.currLeftData = [];
      if (this.keyAdmin) {
        this.clickList = [];
        let user = JSON.parse(JSON.stringify(this.userList));
        user.map((itm) => {
          if (itm.employeeName.indexOf(this.keyAdmin) != -1) {
            if (
              this.rightList.some((ite) => {
                return ite.employeeId == itm.employeeId;
              })
            ) {
              itm.checked = true;
            }
            itm.name = itm.employeeName;
            this.currLeftData.push(itm);
          }
        });
      } else {
        this.getUserNode(this.list, this.list[0].id);
        this.getUser(this.list[0].id);
        this.clickList.push(this.list[0]);
      }
    },
    handleCheckRadio(item) {
      console.log(item);
    },
    handleCheckChange(item) {
      // console.log(item);
      this.alllist=[]
      this.currLeftData.splice();
      if (!item.effective) {
        this.$message.error("该人员账号异常，无法参与考核");
        item.checked = false;
        return;
      }
      if (this.selectIdList && this.selectIdList.length > 0) {
        this.selectIdList.map((val) => {
          console.log(val);
          if (val == item.employeeId) {
            this.$message.error("该人员已存在，不能重复选择");
            item.checked = false;
            return;
          }
        });
      }
      if (this.isOnly) {
        this.currLeftData.forEach((dataItem) => {
          if (dataItem.checked && dataItem.id != item.id) {
            dataItem.checked = false;
          }
        });
        this.rightList = [];
      }
      if (this.isDifferent) {
        if (item.checked) {
          this.rightList.push(item);
        } else {
          this.rightList.map((rightItem, index) => {
            if (rightItem.id == item.id) {
              this.rightList.splice(index, 1);
            }
          });
        }
      } else {
        if (item.checked) {
          this.rightList.push(item);
          this.currLeftData.forEach((curItem) => {
            if (curItem.employeeId == item.employeeId) {
              curItem.checked = true;
            }
          });
        } else {
          this.rightList.map((rightItem, index) => {
            if (rightItem.employeeId == item.employeeId) {
              this.rightList.splice(index, 1);
            }
          });
          this.currLeftData.forEach((curItem) => {
            if (curItem.employeeId == item.employeeId) {
              curItem.checked = false;
            }
          });
        }
      }
      this.handleCheckAllChange(true, this.alllist, "s");
      this.checkAll = this.alllist.every((item) => {
        return this.rightList.some((it) => {
          return item.employeeId == it.employeeId;
        });
      });
    },
    selectChildren(data, itemlist) {
      for (let i in data) {
        if (
          data[i].id == this.clickList[this.clickList.length - 1].id ||
          data[i].isAll
        ) {
          data[i].children &&
            data[i].children.forEach((item) => {
              item.isAll = true;
            });
          let user = JSON.parse(JSON.stringify(this.userList));
          user.map((item) => {
            if (item.deptId == data[i].id) {
              item.name = item.employeeName;
              if(item.effective){
                if (itemlist.length > 0) {
                  if (
                    !itemlist.some((ite) => {
                      return ite.id == item.id;
                    })
                  ) {
                    console.log(item);
                    itemlist.push(item);
                  }
                } else {
                  itemlist.push(item);
                }
              }
            }
          });
        }
        if (data[i].children.length > 0) {
          this.selectChildren(data[i].children, itemlist);
        }
      }
    },
    selectIsChildren(data, itemlist) {
      for (let i in data) {
        if (
          data[i].id == this.clickList[this.clickList.length - 1].id ||
          data[i].isAll
        ) {
          data[i].children &&
            data[i].children.forEach((item) => {
              item.isAll = true;
            });
          let user = JSON.parse(JSON.stringify(this.userList));
          user.map((item) => {
            if (item.deptId == data[i].id) {
              if(item.effective){
                for (let i = this.rightList.length - 1; i >= 0; i--) {
                  if (item.id == this.rightList[i].id
                  ) {
                    this.rightList.splice(i, 1);
                  }
                }
              }
            }
          });
        }
        if (data[i].children.length > 0) {
          this.selectIsChildren(data[i].children, itemlist);
        }
      }
    },
    handleCheckChildren(item) {
      if (this.checkAll) {
        if (item) {
          if (!this.keyAdmin) {
            this.selectChildren(
              JSON.parse(JSON.stringify(this.list)),
              this.rightList
            );
          }
          this.currLeftData.forEach((dataItem) => {
            if (dataItem.employeeName && dataItem.effective) {
              dataItem.checked = true;
              if (this.rightList.length > 0) {
                if (
                  !this.rightList.some((ite) => {
                    return ite.id == dataItem.id;
                  })
                ) {
                  this.rightList.push(dataItem);
                }
              } else {
                this.rightList.push(dataItem);
              }
            }
          });
        } else {
          if (!this.keyAdmin) {
            this.selectIsChildren(
              JSON.parse(JSON.stringify(this.list)),
              this.rightList
            );
          }
          this.currLeftData.forEach((dataItem) => {
            if (dataItem.employeeName && dataItem.effective) {
              dataItem.checked = true;
              if (this.rightList.length > 0) {
                if (
                  !this.rightList.some((ite) => {
                    return ite.id == dataItem.id;
                  })
                ) {
                  this.rightList.push(dataItem);
                }
              } else {
                this.rightList.push(dataItem);
              }
            }
          });
        }
      }
    },
    handleCheckAllChange(item, itemlist, type) {
      if (item) {
        if (this.isChildren && !this.keyAdmin) {
          this.selectChildren(JSON.parse(JSON.stringify(this.list)), itemlist);
        }
        this.currLeftData.forEach((dataItem) => {
          if (dataItem.employeeName && dataItem.effective) {
            if (!type) {
              dataItem.checked = true;
            }
            if (itemlist.length > 0) {
              if (
                !itemlist.some((ite) => {
                  return ite.id == dataItem.id;
                })
              ) {
                itemlist.push(dataItem);
              }
            } else {
              itemlist.push(dataItem);
            }
          }
        });
      } else {
        this.currLeftData.forEach((dataItem) => {
          if (dataItem.employeeName) {
            dataItem.checked = false;
          }
        });
        console.log("123");
        if (!this.keyAdmin) {
          if (this.isChildren) {
            this.deleteChildren(JSON.parse(JSON.stringify(this.list)));
          }
          for (let i = itemlist.length - 1; i >= 0; i--) {
            if (
              itemlist[i].deptId == this.clickList[this.clickList.length - 1].id
            ) {
              itemlist.splice(i, 1);
            }
          }
          console.log(itemlist);
        } else {
          for (let i = itemlist.length - 1; i >= 0; i--) {
            if (
              this.currLeftData.some((leItem) => {
                return leItem.id == itemlist[i].id;
              })
            ) {
              itemlist.splice(i, 1);
            }
          }
        }
      }
    },
    deleteChildren(data) {
      for (let i in data) {
        if (
          data[i].id == this.clickList[this.clickList.length - 1].id ||
          data[i].isAll
        ) {
          data[i].children &&
            data[i].children.forEach((item) => {
              item.isAll = true;
            });
          for (let j = this.rightList.length - 1; j >= 0; j--) {
            if (this.rightList[j].deptId == data[i].id) {
              this.rightList.splice(j, 1);
            }
          }
        }
        if (data[i].children.length > 0) {
          this.deleteChildren(data[i].children);
        }
      }
    },
    handleSelect(item) {
      if (!item.employeeName) {
        this.currLeftData = [];
        this.getUserNode(this.list, item.id);
        this.getUser(item.id);
        this.clickList.push(item);
        if (this.checkAll == true && this.isChildren == false) {
          this.checkAll = false;
        }
        if (this.checkAll == true && this.isChildren == true) {
          this.checkAll = true;
        }
        let isAll = this.currLeftData.every((ite) => {
          if (ite.employeeId && ite.effective) {
            return ite.checked;
          } else {
            return true;
          }
        });
        this.checkAll = isAll;
      }
    },
    handleTab(item, itemIndex) {
      this.currLeftData = [];
      this.alllist = [];
      let drapList = JSON.parse(JSON.stringify(this.list));
      let arr = [];
      this.getUserNode(drapList, item.id);
      this.getUser(item.id);
      console.log(this.currLeftData);
      this.clickList.forEach((clItem, clIndex) => {
        if (clIndex <= itemIndex) {
          arr.push(clItem);
        }
      });
      this.clickList = JSON.parse(JSON.stringify(arr));
      this.handleCheckAllChange(true, this.alllist, "s");
      this.checkAll = this.alllist.every((item) => {
        return this.rightList.some((it) => {
          return item.employeeId == it.employeeId;
        });
      });
    },

    getUserNode(data, ItemId) {
      for (let i in data) {
        if (data[i].id == ItemId) {
          data[i].children &&
            data[i].children.map((item) => {
              this.currLeftData.push(item);
            });
          return;
        }
        if (data[i].children.length > 0) {
          this.getUserNode(data[i].children, ItemId);
        }
      }
    },
    getUser(itemId) {
      let user = JSON.parse(JSON.stringify(this.userList));
      user.map((item) => {
        if (item.deptId === itemId) {
          item.name = item.employeeName;
          if (
            this.rightList.some((ite) => {
              if (this.isDifferent) {
                return ite.id == item.id;
              } else {
                return ite.employeeId == item.employeeId;
              }
            })
          ) {
            item.checked = true;
          } else {
            item.checked = false;
          }
          this.currLeftData.push(item);
        }
      });
    },
    //获取人员
    getPersonList() {
      this.loading = false;
      let user = JSON.parse(JSON.stringify(this.userList));
      console.log(this.select);
      this.getUserNode(this.list, this.list[0].id);
      this.getUser(this.list[0].id);
      this.clickList.push(this.list[0]);
      user.map((item) => {
        if (this.isDifferent) {
          if (
            this.select.some((ite) => {
              return (
                ite.employeeId == item.employeeId &&
                ite.subsidiaryId == item.subsidiaryId
              );
            })
          ) {
            item.name = item.employeeName;
            this.rightList.push(item);
          }
        } else {
          if (
            this.select.some((ite) => {
              return ite.employeeId == item.employeeId;
            }) &&
            !this.rightList.some((ite) => {
              return ite.employeeId == item.employeeId;
            })
          ) {
            item.name = item.employeeName;
            this.rightList.push(item);
          }
        }
      });
    },
    //关闭弹窗清空记录
    closeDialog() {
      this.keyNeed = "";
      this.keyAdmin = "";
      this.rightList = [];
      this.currLeftData = [];
      this.$emit("close");
    },
    //全部清空
    handleAllDelete() {
      this.rightList = [];
      this.currLeftData.forEach((item) => {
        if (item.checked) {
          item.checked = false;
        }
      });
      this.checkAll = false;
    },
    //删除人员已选
    handleDelete(item, index) {
      this.rightList.splice(index, 1);
      this.currLeftData.forEach((leftItem) => {
        if (this.isDifferent) {
          if (leftItem.id == item.id && leftItem.checked) {
            leftItem.checked = false;
          }
        } else {
          if (leftItem.employeeId == item.employeeId && leftItem.checked) {
            leftItem.checked = false;
          }
        }
      });
    },
    //确定人员选择
    confirmPerson() {
      let arr = JSON.parse(JSON.stringify(this.rightList));
      this.$emit("commit", arr);
    },
    //取消人员选择
    cancelPerson() {},
  },
};
</script>

<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";

.dialogContent {
  .divider {
    width: 1px;
    height: 396px;
    background: #eaeaea;
    position: absolute;
    left: 50%;
    top: 56px;
    // top: 16%;
  }
  .icon {
    display: inline-block;
    position: relative;
    top: 1px;
    width: 18px;
    height: 18px;
    color: #9ea3ba;
  }

  .groups {
    display: block;
    margin-left: 40px;
    margin-bottom: 10px;
    color: rgba(0, 0, 0, 0.25);
  }

  .el-input {
    width: 330px;
  }

  .left {
    width: 340px;
    // overflow-x: scroll;
    .show-ellipsis {
      width: 50px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .left-class {
      height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      .item-left {
        height: 32px;
        display: flex;
        width: 100%;
        justify-content: space-between;
        .left-name {
          width: 200px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .xiaji {
          margin: 0 15px 0 10px;
        }
        .xiaji:hover {
          color: $mainColor;
        }
      }
    }
  }
  .right {
    position: relative;
    font-size: 14px;
    float: right;
    width: 330px;
    height: 360px;
    .statistics {
      width: 330px;
      background: #fff;
      z-index: 99;
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      .num {
        color: #6a6f7f;
      }
      .orange {
        color: #ff9500;
        margin-right: 5px;
      }
      .clear {
        color: $mainColor;
        cursor: pointer;
      }
    }
    .right-list {
      height: 330px;
      overflow-y: auto;
      overflow-x: hidden;
      li {
        position: relative;
        background: #d9eafc;
        padding: 9px 10px;
        box-sizing: border-box;
        margin-bottom: 10px;
        // width: 300px;
        height: 58px;
        background: #f4f4f4;
        border-radius: 4px;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #6a6f7f;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        // align-items: center;
        .subject {
          margin-top: 3px;
          color: rgba(0, 0, 0, 0.25);
        }
        .el-icon-close {
          position: absolute;
          font-size: 18px;
          right: 10px;
          color: #909399;
          cursor: pointer;
        }
      }
      .tip {
        text-align: center;
        margin-top: 150px;
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }
}
.addPerson {
  /deep/ .el-dialog__body {
    height: 355px;
    padding: 10px 20px 30px;
  }
  /deep/ .el-dialog {
    height: 516px;
    .el-tree {
      margin-top: 10px;
      height: 320px;
      overflow: auto;
    }
  }
}
/deep/ .dialog-footer {
  position: absolute;
  text-align: right;
  display: flex;
  flex-direction: row;
  bottom: 10px;
  right: 20px;
}
// /deep/ .el-tree-node {
//   overflow-x: auto;
// }

.isOnly {
  /deep/.el-checkbox__inner {
    border-radius: 50%;
    margin-bottom: 2px;
  }
}
.down-tree {
  cursor: pointer;
  .tabs {
    // min-height: 14px;
    margin: 16px 0;
    font-size: 12px;
    color: #6a6f7f;
  }
  .anniu {
    display: flex;
    margin-bottom: 21px;
    height: 20px;
    line-height: 20px;
    justify-content: space-between;
    margin-right: 13px;
    .zibumen {
      line-height: 100%;
      font-size: 12px;
      height: 100%;
      margin-top: 5px;
      color: #888888;
    }
  }
}
// /deep/ .el-tree-node {
//   height: 100px;
// }
/deep/.el-switch__core {
  width: 28px !important;
  height: 14px;
  border-radius: 7px;
}
/deep/.el-switch__core::after {
  width: 12px;
  height: 12px;
  margin-top: -1.5px;
  margin-bottom: 3px;
}
/deep/.el-switch.is-checked .el-switch__core::after {
  margin-left: -13px;
}
/deep/.el-dialog__footer {
  border-top: 1px solid #eaeaea;
}
/deep/.el-input__icon {
  margin-right: 10px;
}
</style>
