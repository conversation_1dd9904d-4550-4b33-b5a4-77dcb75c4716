<template>
  <div class="tree-container">
    <i
      class="icon iconfont-per isshow"
      :class="isShowTree ? 'icon-Icon-shouqi' : 'icon-Icon-zhankai'"
      @click="handleShowTree"
    ></i>
    <tree v-show="isShowTree" :name="name" @handClickNode="handClickNode"></tree>
  </div>
</template>
<script>
import tree from "./tree";
export default {
  props: ["name"],
  components: { tree },
  data() {
    return {
      isShowTree: true
    };
  },
  created() {},
  methods: {
    //展开隐藏
    handleShowTree() {
      this.isShowTree = !this.isShowTree;
    },
    handClickNode(val) {
      this.$emit("handClickNode", val);
    },
  }
};
</script>
<style lang="scss" scoped>
.tree-container {
  display: flex;
  position: relative;
}
.isshow {
  position: absolute;
  left: -15px;
  top: 10px;
  font-size: 40px;
  cursor: pointer;
  color: #858c9f;
  z-index: 999;
}
</style>
