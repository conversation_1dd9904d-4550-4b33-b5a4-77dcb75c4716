<template>
  <div class="class-tree">
    <div class="tree-title">
      <h5>考核指标分组</h5>
      <el-tooltip
        placement="top"
        content="您可通过分组对考核指标库内指标进行自定义分组，便于使用时快速查找"
      >
        <i class="iconfont-per icon-help"></i>
      </el-tooltip>
    </div>
    <div class="ic-type">
      <i class="iconfont-per icon-xiajibumen"></i>
      <span>全部分组</span>
    </div>
    <el-tree
      ref="tree"
      class="user-tree"
      :data="treeData"
      :props="defaultProps"
      node-key="id"
      :default-expand-all="true"
      :default-expanded-keys="[0]"
      @node-click="handClickNode"
    >
      <div class="custom-tree-node" slot-scope="{ node, data }">
        <div class="node-title">
          <span :title="node.label">{{ node.label }}</span>
        </div>
        <span class="operate-btns" v-if="!data.isEdit">
          <i
            v-if="(node.level == 2 || node.level == 3)&&havePrivilege('kpi.performance.indicatorBank.groupAdd')"
            class="iconfont-per icon-tianjia1 operate-btn"
            @click.stop="handleAddNode(data)"
          ></i>
          <i
            v-if="node.level !== 1 && node.level !== 2&&havePrivilege('kpi.performance.indicatorBank.groupUpdate')"
            class="iconfont-per icon-bianji operate-btn"
            @click.stop="handleEditNode(data,node)"
          ></i>
          <i
            v-if="node.level !== 1 && node.level !== 2&&havePrivilege('kpi.performance.indicatorBank.groupDelete')"
            class="iconfont-per icon-shanchu operate-btn"
            @click.stop="showDelete(data.id)"
          ></i>
        </span>
      </div>
    </el-tree>
    <el-dialog :visible.sync="dialogVisible" width="522px" :show-close="false">
      <div slot="title">
        <div class="header">
          <span class="title" v-if="updateType == 'add'">新增分组</span>
          <span class="title" v-if="updateType == 'update'">编辑分组</span>
          <i class="iconfont-per icon-close1" @click="handleClose"></i>
        </div>
      </div>
      <el-form
        :model="addForm"
        ref="addForm"
        label-width="150px"
        class="add-class"
        :rules="rules"
        v-if="updateType !== 'delete'"
      >
        <el-form-item label="考核指标分组" prop="name">
          <el-input
            ref="input"
            v-model.trim="addForm.name"
            placeholder="请输入考核指标分组名称"
            autocomplete="off"
            style="width:298px"
            @input="handleInput(addForm.name)"
          ></el-input>
        </el-form-item>
        <el-form-item label="上级分组" prop="parentId">
          <el-cascader
            :key="isResouceShow"
            v-model="addForm.parentId"
            placeholder="请选择上级分组"
            :options="selectList"
            :props="cascaderProps"
            clearable
            style="width:298px"
            :show-all-levels="false"
            :disabled="disable"
          ></el-cascader>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm('addForm')"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible1" width="522px" :show-close="false">
      <div slot="title">
        <div class="header">
          <span class="title">删除分组 </span>
          <i class="iconfont-per icon-close1" @click="closeDelete"></i>
        </div>               
      </div>
      <div>
        <i class="iconfont-per icon-jingshi-qiangtishi1 delete-icon"></i>
        <span style="color:#555555"
          >确认要删除吗？删除后该指标分组内的指标将统一归属至其父分组</span
        >
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDelete">取 消</el-button>
        <el-button type="primary" @click="handleDeleteNode(deleteId)"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  getIndicatorTree,
  addIndicatorTree,
  removeIndicatorTree,
  updateIndicatorTree
} from "performance/store/api.js";
import {havePrivilege} from "performance/utils/util.js";
export default {
  props: ["name"],
  data() {
    return {
      havePrivilege,
      isResouceShow: 0,
      treeData: [],
      editName:'',
      defaultProps: {
        children: "children",
        label: "name"
      },
      cascaderProps: {
        children: "children",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false
      },
      disable: false,
      updateType: "",
      currInputRef: "",
      dialogVisible: false,
      dialogVisible1: false,
      isDiable: false,
      deleteId: "",
      addForm: {
        name: "",
        parentId: "",
        id: ""
      },
      selectList: [],
      isRepetition: 0,
      rules: {
        name: {
          required: true,
          validator: (rule, value, callback) => {
            if (!value) {
              callback("请输入考核指标分组名称");
            } else {
              this.isRepetition = 0;
              if(!this.editName||(this.editName&&value!==this.editName)){
                this.getDepartmentIds(this.treeData, value);
              }
              console.log(this.isRepetition);
              if (this.isRepetition > 0) {
                callback(value + "已存在");
              }
              callback();
            }
          },
          trigger: ["blur"]
        },
        parentId: {
          required: true,
          message: "请选择上级分组",
          trigger: "change"
        }
      }
    };
  },
  created() {
    this.getCatalogTree();
  },
  methods: {
    //获取分类树
    async getCatalogTree() {
      let res = await getIndicatorTree();
      if (res.success) {
        res.data.forEach(item => {
          item.children &&
            item.children.forEach(itx => {
              itx.children &&
                itx.children.forEach(i => {
                  i.children &&
                    i.children.forEach(i => {
                      i.children = [];
                    });
                });
            });
        });
        this.treeData = res.data;
      }
    },
    //增加分组
    handleAddNode(data) {
      let arr=JSON.parse(JSON.stringify(this.treeData[0]))
      this.selectList = [];
      this.editName=''
      this.updateType = "add";
      this.dialogVisible = true;
      this.addForm.parentId = data.id;
      arr.children.map(item => {
        if (item.type == data.type) {
          this.selectList.push(item);
        }
      });
      this.selectList.forEach(item => {
        item.children &&
          item.children.forEach(itx => {
            delete itx.children;
          });
      });
      console.log(this.selectList);
    },
    //上级分组处理
    handleCheckCascader(value) {
      this.addForm.parentId = value[value.length - 1];
    },
    //保存当前节点
    async handleSaveNode(data) {
      let res;
      if (this.updateType === "add") {
        delete this.addForm.id;
        res = await addIndicatorTree({ ...this.addForm });
      } else {
        res = await updateIndicatorTree({ ...this.addForm });
      }
      console.log(res);
      if (res.success) {
        this.$message.success("保存成功");
        ++this.isResouceShow;
        this.getCatalogTree();
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          console.log(this.addForm);
          this.handleSaveNode();
          this.handleClose();
        } else {
          console.log("error submit!!");
            this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
          return false;
        }
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.disable = false;
      this.editName=''
      this.updateType = "";
      this.isRepetition = 0;
      this.isResouceShow = 0;
      this.addForm = {
        name: "",
        parentId: "",
        id: ""
      };
      this.$refs.addForm.resetFields();
    },
    //编辑当前节点
    handleEditNode(data,node) {
      console.log(data);
      this.selectList = [];
      let arr=JSON.parse(JSON.stringify(this.treeData[0]))
      this.updateType = "update";
      this.dialogVisible = true;
      this.addForm.parentId = data.parentId;
      this.addForm.id = data.id;
      this.addForm.name = data.name;
      this.editName=JSON.parse(JSON.stringify(data.name))
      if (node.level == 3 && data.children.length > 0) {
        this.disable = true;
      }
      arr.children.map((item, index) => {
        if (item.type == data.type) {
          console.log(item);
          this.selectList.push(JSON.parse(JSON.stringify(item)));
        }
      });
      this.recursionNode(this.selectList,data.id)
      if(node.level == 3){
        this.selectList.map(irem=>{
          if(irem.children){
            irem.children.map(item=>{
              delete item.children
            })
          }
        })
      }
    },
    recursionNode(list,dataId) {
      list.forEach((item,i)=>{
        if (item.id == dataId) {
          list.splice(i, 1);
        } 
        if(list[i]&&list[i].children){
          this.recursionNode(list[i].children,dataId);
        }
      })
      return list;
    },
    showDelete(data) {
      this.dialogVisible1 = true;
      this.deleteId = data;
    },
    closeDelete() {
      this.dialogVisible1 = false;
      this.deleteId = "";
    },
    //删除当前节点
    handleDeleteNode(data) {
      removeIndicatorTree({ id: data }).then(res => {
        if (res.success) {
          this.$message.success("删除成功");
          this.getCatalogTree();
          this.$emit("handClickNode", null);
        }
      });
      this.closeDelete();
    },
    //点击节点
    handClickNode(data) {
      console.log(data);
      this.$emit("handClickNode", data);
    },
    handleInput(data) {
      if (data.length > 50) {
        this.addForm.name = data.substr(0, data.length - 1);
        this.$message.error("考核指标分组不能多于50字");
        this.$refs.input.blur();
      }
    },
    getDepartmentIds(list, data) {
      for (let i = 0; i < list.length; i++) {
        if (list[i].name == data) {
          this.isRepetition++;
        }
        if(list[i].children){
          this.getDepartmentIds(list[i].children, data);
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.class-tree {
  min-width: 255px;
  width: 21%;
  // margin: 32px 0;
  padding-right: 14px;
  padding-left: 20px;
  border-right: 1px solid #e8eaf3;
  box-sizing: border-box;
  position: relative;
  .tree-title {
    width: 100%;
    display: flex;
    align-items: center;
    height: 60px;
    border-bottom: 1px solid #eaeaea;
    h5 {
      font-size: 14px;
    }
  }
  .user-tree{
    height: calc(100vh - 240px);
    overflow-y: auto;
  }
  .ic-type {
    font-size: 14px;
    color: #6A6F7F;
    line-height: 32px;
    margin-top: 24.3px;
  }
  .operate-btns {
    position: absolute;
    right: 0;
    top: 0;
    background: #eef1ff;
    padding: 0 14px;
    height: 32px;
    line-height: 32px;
    opacity: 0;
    .iconfont-per {
      color: $mainColor;
      font-size: 16px;
    }
    .iconfont-per:last-child {
      margin-right: 0;
    }
  }
  .custom-tree-node {
    overflow-x: hidden;
    i {
      font-size: 17px;
      margin-right: 5px;
    }
    .node-title {
      display: inline-block;
      font-size: 14px;
      color: #070f29;
      opacity: 0.6;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      width: 150px;
    }
    /deep/.el-input__inner {
      height: 32px;
      line-height: 32px;
    }
    /deep/.el-input__suffix-inner {
      line-height: 32px;
    }
    .iconqueren {
      color: $mainColor;
      font-size: 12px;
    }
  }
  /deep/.el-tree-node__content {
    position: relative;
    height: 32px;
  }
  /deep/.el-tree-node__content:hover {
    background-color: #EEF1FF;
    border-radius: 4px;
    .el-tree-node__expand-icon {
      color: $mainColor !important;
    }
    .el-tree-node__expand-icon.is-leaf {
      color: transparent !important;
    }
    .node-title {
      color: $mainColor !important;
      opacity: 1;
      width: 90px;
    }
    .operate-btns {
      opacity: 1;
    }
  }
  /deep/.el-tree-node__expand-icon {
    font-size: 18px;
    margin-bottom: 4px;
    color: #858C9F;
  }
  /deep/.el-tree-node__expand-icon.is-leaf{
    color: transparent !important;
  }
}
::-webkit-scrollbar {
  width: 0;
}
.header {
  display: flex;
  justify-content: space-between;
  .title {
    font-weight: Medium;
    font-size: 16px;
    color: #070f29;
  }
}
.delete-icon {
  color: $lineBorderPointer;
}
.icon-help{
  margin-left: 10px;
  color: #9EA5BD;
}
.icon-xiajibumen{
  color: #858C9F;
}
.icon-close1{
  font-size: 16px;
  color:#9EA5BD ;
}
</style>
