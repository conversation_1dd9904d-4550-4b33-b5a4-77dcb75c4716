export const data = {
    success: true,
    errorCode: 0,
    message: '成功',
    data: [
      {
        id: 13692,
        name: '测试环境有限公司',
        isRoot: false,
        children: [
          {
            id: 13693,
            name: '未分组',
            isRoot: false,
            peopleNumber: 2,
            children: [
              {
                id: 2602,
                name: '王一11',
                groups: '阿拉钉',
                isRoot: false,
                parentId: 13693,
                hasChildren: false,
                isDept: false
              },
              {
                id: 87674,
                name: '喜喜',
                isRoot: false,
                parentId: 13693,
                hasChildren: false,
                isDept: false
              }
            ],
            parentId: 13692,
            isDept: true
          },
          {
            id: 13694,
            name: '外部合作者',
            isRoot: false,
            peopleNumber: 2,
            children: [
              {
                id: 99813,
                name: '好哈哈哈刚发的删除下是的覅刚好发个百分点是不放过的生成1',
                isRoot: false,
                parentId: 13694,
                hasChildren: false,
                isDept: false
              },
              {
                id: 101001,
                name: '张杰',
                isRoot: false,
                parentId: 13694,
                hasChildren: false,
                isDept: false
              }
            ],
            parentId: 13692,
            isDept: true
          },
          {
            id: 13700,
            name: '产品部门',
            isRoot: false,
            peopleNumber: 2,
            children: [
              {
                id: 13781,
                name: '需求分析设计部门',
                isRoot: false,
                children: [
                  {
                    id: 57911,
                    name: '东',
                    isRoot: false,
                    parentId: 13781,
                    hasChildren: false,
                    isDept: false
                  }
                ],
                parentId: 13700,
                isDept: true
              },
              {
                id: 1264,
                name: '无名',
                isRoot: false,
                parentId: 13700,
                hasChildren: false,
                isDept: false
              },
              {
                id: 1499,
                name: '李梦芸',
                isRoot: false,
                parentId: 13700,
                hasChildren: false,
                isDept: false
              },
              {
                id: 58435,
                name: '刘丽丽',
                isRoot: false,
                parentId: 13700,
                hasChildren: false,
                isDept: false
              }
            ],
            parentId: 13692,
            isDept: true
          },
          {
            id: 13782,
            name: '测试部门',
            isRoot: false,
            children: [
              {
                id: 69282,
                name: '啦啦啦',
                isRoot: false,
                parentId: 13782,
                hasChildren: false,
                isDept: false
              }
            ],
            parentId: 13692,
            isDept: true
          },
          {
            id: 13954,
            name: '应用技术部门哈哈哈哈电饭锅合计卡梅伦，们卡耐基看美女',
            isRoot: false,
            children: [
              {
                id: 15062,
                name: '研发部门',
                isRoot: false,
                children: [
                  {
                    id: 103100,
                    name: '前端部门',
                    isRoot: false,
                    children: [
                      {
                        id: 104502,
                        name: '设计下地铁浴柜还几百块',
                        isRoot: false,
                        children: [
                          {
                            id: 104503,
                            name:
                              '顾好几次盗墓笔记快餐店那好吧内测的那几款北京吗',
                            isRoot: false,
                            children: [
                              {
                                id: 100474,
                                name: '用户user',
                                isRoot: false,
                                parentId: 104503,
                                hasChildren: false,
                                isDept: false
                              }
                            ],
                            parentId: 104502,
                            isDept: true
                          }
                        ],
                        parentId: 103100,
                        isDept: true
                      },
                      {
                        id: 99817,
                        name: '小燕子',
                        isRoot: false,
                        parentId: 103100,
                        hasChildren: false,
                        isDept: false
                      }
                    ],
                    parentId: 15062,
                    isDept: true
                  },
                  {
                    id: 103300,
                    name: '后端部门',
                    isRoot: false,
                    children: [
                      {
                        id: 4800,
                        name: '张争超',
                        isRoot: false,
                        parentId: 103300,
                        hasChildren: false,
                        isDept: false
                      },
                      {
                        id: 9999,
                        name: '思锦',
                        isRoot: false,
                        parentId: 103300,
                        hasChildren: false,
                        isDept: false
                      },
                      {
                        id: 60505,
                        name: '初代',
                        isRoot: false,
                        parentId: 103300,
                        hasChildren: false,
                        isDept: false
                      },
                      {
                        id: 60763,
                        name: 'wsds',
                        isRoot: false,
                        parentId: 103300,
                        hasChildren: false,
                        isDept: false
                      }
                    ],
                    parentId: 15062,
                    isDept: true
                  },
                  {
                    id: 99810,
                    name: '张依依qwert',
                    isRoot: false,
                    parentId: 15062,
                    hasChildren: false,
                    isDept: false
                  }
                ],
                parentId: 13954,
                isDept: true
              }
            ],
            parentId: 13692,
            isDept: true
          },
          {
            id: 4800,
            name: '张争超',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 9999,
            name: '思锦',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 13806,
            name: '张三',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 34338,
            name: '赵合作者',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 35521,
            name: '基金',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 58436,
            name: 'adsda',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 60505,
            name: '初代',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 91853,
            name: '销售1',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 91854,
            name: '销售2',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 91855,
            name: '销售3',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 91856,
            name: '销售0',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 91878,
            name: '牛根',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 91879,
            name: '牛老跟',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 91882,
            name: '牛小根',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 91890,
            name: '牛小根',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 91893,
            name: '牛大牛',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 91895,
            name: '牛二牛',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 92200,
            name: '钱合作者',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 92202,
            name: '孙合作者',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 92212,
            name: '王二',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 92213,
            name: '王三',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 92214,
            name: '王四',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 92215,
            name: '王五',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 92216,
            name: '王六',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 92248,
            name: '刘琦',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 92252,
            name: '销售one',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          },
          {
            id: 92253,
            name: '销售two',
            isRoot: false,
            parentId: 13692,
            hasChildren: false,
            isDept: false
          }
        ],
        parentId: 13691,
        isDept: true
      }
    ]
  };
  
  export const data1 = {
    success: true,
    code: 0,
    msg: '成功',
    data: [
      {
        id: 111,
        name: '雪小逗测试',
        children: [
          {
            id: 112,
            name: '未分组',
            children: [],
            parentId: 111
          },
          {
            id: 113,
            name: '外部合作者',
            children: [],
            parentId: 111
          },
          {
            id: 130,
            name: '测试一部',
            children: [
              {
                id: 10002,
                name: '88777',
                parentId: 130,
                children: []
              },
              {
                id: 10268,
                name: '薪税公司1',
                parentId: 130,
                children: []
              },
              {
                id: 10269,
                name: '未认证公司人1',
                parentId: 130,
                children: []
              },
              {
                id: 10354,
                name: '测试啊啊',
                parentId: 130,
                children: []
              }
            ],
            parentId: 111
          },
          {
            id: 136,
            name: '999999999',
            children: [],
            parentId: 111
          },
          {
            id: 100020,
            name: '新部门',
            children: [
              {
                id: 10005,
                name: '测试',
                parentId: 100020,
                children: []
              }
            ],
            parentId: 111
          },
          {
            id: 10000,
            name: '阿拉钉',
            parentId: 111,
            children: []
          },
          {
            id: 10248,
            name: '薪税一',
            parentId: 111,
            children: []
          },
          {
            id: 10249,
            name: '薪税二',
            parentId: 111,
            children: []
          },
          {
            id: 10273,
            name: '3333',
            parentId: 111,
            children: []
          }
        ],
        parentId: 110
      }
    ]
  };
  
  export const tree = [
    {
      id: 0,
      parentId: 0,
      name: '考核指标库',
      bankLevel: 0,
      children: [
        {
          id: 1,
          parentId: 0,
          name: '公司考核',
          type: 1,
          bankLevel: 1,
          children: []
        },
        {
          id: 2,
          parentId: 0,
          name: '部门考核',
          type: 2,
          bankLevel: 1,
          children: [
            {
              id: 4,
              parentId: 2,
              name: '研发部',
              type: 2,
              bankLevel: 2,
              children: [
                {
                  id: 8,
                  parentId: 4,
                  name: '后端组',
                  type: 2,
                  bankLevel: 3,
                  children: []
                }
              ]
            },
            {
              id: 5,
              parentId: 2,
              name: '产品部',
              type: 2,
              bankLevel: 2,
              children: [
                {
                  id: 7,
                  parentId: 5,
                  name: 'ui',
                  type: 2,
                  bankLevel: 3,
                  children: []
                }
              ]
            },
            {
              id: 6,
              parentId: 2,
              name: '测试部',
              type: 2,
              bankLevel: 2,
              children: []
            }
          ]
        },
        {
          id: 3,
          parentId: 0,
          name: '个人考核',
          type: 3,
          bankLevel: 1,
          children: []
        }
      ]
    }
  ];
  