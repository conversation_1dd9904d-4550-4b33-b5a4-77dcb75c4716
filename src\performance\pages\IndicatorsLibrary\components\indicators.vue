<template>
  <div>
    <el-dialog
      :visible.sync="isShow"
      :direction="direction"
      :before-close="handleClose"
      width="740px"
    >
      <div slot="title">
        <span class="is-title">设置计算公式</span>
      </div>
      <div class="drawer-main">
        <div class="flex">
          <i class="iconfont-per icon-shujuyichang"></i>
          <span>考核启动后，数据来源指定人需录入考核指标实际完成值</span>
        </div>
        <el-form
          :model="formulas"
          ref="formulas"
          style="margin:20px;20px"
          label-width="130px"
          :rules="rules"
        >
          <div style="display: flex; width: 100%">
            <el-form-item prop="dataSource" label="数据来源">
              <el-input
                v-model.trim="formulas.dataSource"
                placeholder="如“销售额”"
                style="width: 150px"
                ref="nameInput"
                @input="
                  handleNameInput(
                    formulas.dataSource,
                    'nameInput',
                    50,
                    '数据来源'
                  )
                "
              ></el-input>
            </el-form-item>
            <el-form-item prop="dataUnit" label="单位" label-width="60px">
              <el-input
                v-model.trim="formulas.dataUnit"
                placeholder="如“万元”"
                style="width: 100px"
                ref="input"
                @input="handleNameInput(formulas.dataUnit, 'input', 10, '单位')"
              ></el-input>
            </el-form-item>
          </div>
          <el-form-item prop="dataMarkerId" label="数据来源指定人">
            <div style="height: 40px; display: flex">
              <i
                class="iconfont-per icon-tianjiachengyuan"
                @click="showDialog = true"
                v-if="selectList.length == 0"
              ></i>
              <div class="addName-bn" v-if="selectList.length > 0">
                <span class="addName-name" @click="showDialog = true">{{
                  selectList[0].name
                }}</span>
                <i
                  class="iconfont-per icon-close1"
                  style="width: 10px"
                  @click="handleDelete"
                ></i>
              </div>
            </div>
          </el-form-item>
          <el-form-item prop="dataRuleType" label="计算规则">
            <el-radio-group
              v-model="formulas.dataRuleType"
              @change="handleDataRuleType"
            >
              <el-radio :label="1">按实际完成值来算</el-radio>
              <el-radio :label="2">按目标达成率来算</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="dataRuleList" label="计算公式">
            <old-table
              class="table-main"
              style="line-height: 20px"
              :data="formulas.dataRuleList"
              :headerData="headerFormulas"
              isShowOperation
              :operaOptions="operaOptions"
              @operaClick="handleOperaClick"
            >
              <template slot="number-header">
                <div v-if="formulas.dataRuleType == '1'">
                  {{ formulas.dataSource || "数据来源" }}({{
                    formulas.dataUnit || "单位"
                  }})
                </div>
                <div v-if="formulas.dataRuleType == '2'">
                  目标达成率(%)
                  <el-tooltip
                    content="目标达成率 = （实际完成值 ÷ 目标值） x 100%"
                  >
                    <i class="iconfont-per icon-help"></i>
                  </el-tooltip>
                </div>
              </template>
              <template slot="number" slot-scope="scope">
                <div style="display: flex; height: 45px; width: 310px">
                  <el-form-item
                    :prop="'dataRuleList.' + scope.msg.$index + '.min'"
                    :rules="rules.min"
                  >
                    <el-input
                      type="number"
                      v-model="scope.msg.row.min"
                      placeholder="下限"
                      class="source-input"
                      style="width: 90px; margin-bottom: 5px"
                      size="small"
                      :disabled="scope.msg.$index == 0 ? true : false"
                      @input="
                        handleChange(scope.msg.row, scope.msg.$index, 'min')
                      "
                      @blur="handleBlur(scope.msg.row, scope.msg.$index, 'min')"
                      oninput="value=value.toString().match(new RegExp('^\\d+(?:\\.\\d{0,2})?'))"
                    >
                    </el-input>
                  </el-form-item>
                  <span class="cource-span" v-if="scope.msg.$index !== 0"
                    >＜{{
                      formulas.dataRuleType == "1" ? "完成值" : "达成率"
                    }}≤</span
                  >
                  <span class="cource-span" v-if="scope.msg.$index == 0"
                    >≤{{
                      formulas.dataRuleType == "1" ? "完成值" : "达成率"
                    }}≤</span
                  >
                  <el-form-item
                    :prop="'dataRuleList.' + scope.msg.$index + '.max'"
                    :rules="rules.max"
                  >
                    <el-input
                      v-model="scope.msg.row.max"
                      placeholder="上限"
                      style="width: 90px; margin-bottom: 5px"
                      class="source-input"
                      size="small"
                      @input="
                        handleChange(scope.msg.row, scope.msg.$index, 'max')
                      "
                      @blur="handleBlur(scope.msg.row, scope.msg.$index, 'max')"
                      oninput="value=value.toString().match(new RegExp('^\\d+(?:\\.\\d{0,2})?'))"
                    >
                    </el-input>
                  </el-form-item>
                </div>
              </template>
              <template slot="score" slot-scope="scope">
                <el-form-item
                  :prop="'dataRuleList.' + scope.msg.$index + '.score'"
                  style="width: 100px; margin-bottom: 0; height: 45px"
                  :rules="rules.score"
                >
                  <el-input
                    type="number"
                    v-model="scope.msg.row.score"
                    placeholder="请输入"
                    class="source-input"
                    style="width: 100px; margin-bottom: 5px"
                    size="small"
                    oninput="value=value.toString().match(new RegExp('^\\d+(?:\\.\\d{0,2})?'))"
                  >
                  </el-input>
                </el-form-item>
              </template>
            </old-table>
          </el-form-item>
          <el-form-item>
            <span class="add-bn" @click="handleAdd">
              <i class="iconfont-per icon-add1"></i>
              添加行
            </span>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm('formulas')"
          >保 存</el-button
        >
      </span>
    </el-dialog>
    <user-select
      v-if="showDialog"
      :list="depList"
      :userList="userList"
      :isOnly="true"
      :select="selectList"
      @close="showDialog = false"
      @commit="commitSelect"
    ></user-select>
  </div>
</template>

<script>
import UserSelect from "./UserSelect.vue";
export default {
  props: {
    addForm: {
      type: Object,
      default: () => {},
    },
    userList: {
      type: Array,
      default: () => [],
    },
    depList: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    UserSelect,
  },
  data() {
    validatorNumber: return {
      isShow: true,
      showDialog: false,
      direction: "rtl",
      isShowOperation: false,
      formulas: {},
      selectList: [],
      rules: {
        dataSource: {
          required: true,
          message: "请输入数据来源",
          trigger: "change",
        },
        dataUnit: {
          required: true,
          message: "请输入单位",
          trigger: "change",
        },
        dataMarkerId: {
          required: true,
          validator: (rule, value, callback) => {
            if (value === "" || value === 0 || value === null) {
              callback("请输入数据来源指定人");
            } else {
              callback();
            }
          },
          trigger: ["blur", "change"],
        },
        dataRuleList: {
          required: true,
          message: "",
          trigger: "change",
        },
        min: {
          required: true,
          validator: (rule, value, callback) => {
            if (value === "") {
              callback("下限值不能为空");
            } else {
              if (this.formulas.dataRuleType == "1") {
                if (!new RegExp("^([1-9]\\d*|0)(\\.\\d{1,2})?$").test(value)) {
                  callback(new Error("请输入数字值"));
                } else {
                  callback();
                }
              } else if (this.formulas.dataRuleType == "2") {
                if (!new RegExp("^([1-9]\\d*|0)(\\.\\d{1,2})?$").test(value)) {
                  callback(new Error("请输入数字值"));
                } else if (value.indexOf(".") != "-1") {
                  callback(new Error("请输入正整数"));
                } else {
                  callback();
                }
              }
            }
          },
          trigger: ["blur", "change"],
        },
        max: {
          required: true,
          validator: (rule, value, callback) => {
            if (value === "") {
              callback("上限值不能为空");
            } else {
              if (this.formulas.dataRuleType == "1") {
                if (!new RegExp("^([1-9]\\d*|0)(\\.\\d{1,2})?$").test(value)) {
                  callback(new Error("请输入数字值"));
                } else {
                  callback();
                }
              } else if (this.formulas.dataRuleType == "2") {
                if (!new RegExp("^([1-9]\\d*|0)(\\.\\d{1,2})?$").test(value)) {
                  callback(new Error("请输入数字值"));
                } else if (value.indexOf(".") != "-1") {
                  callback(new Error("请输入正整数"));
                } else {
                  callback();
                }
              }
            }
          },
          trigger: ["blur", "change"],
        },
        lastMax: {
          required: false,
          validator: (rule, value, callback) => {
            if (value) {
              if (!new RegExp("^([1-9]\\d*|0)(\\.\\d{1,2})?$").test(value)) {
                callback(new Error("请输入数字值"));
              } else {
                callback();
              }
            } else {
              callback();
            }
          },
          trigger: ["change", "blur"],
        },
        score: {
          required: true,
          validator: (rule, value, callback) => {
            if (value === "") {
              callback("请输入评分");
            } else {
              if (Number(value) > Number(this.formulas.maxScore)) {
                callback("指标评分为0～" + this.formulas.maxScore);
              } else if (Number(value) < 0) {
                callback(new Error("请输入数字值"));
              } else if (
                !new RegExp("^([1-9]\\d*|0)(\\.\\d{1,2})?$").test(value)
              ) {
                callback(new Error("请输入数字值"));
              } else {
                callback();
              }
            }
          },
          trigger: ["blur", "change"],
        },
      },
      headerFormulas: [
        {
          title: "数据来源",
          label: "number",
          align: "left",
          slot: "number",
          slotHeader: "number-header",
          width: "310",
        },
        {
          title: "考核指标评分",
          label: "score",
          slot: "score",
          align: "left",
        },
      ],
      operaOptions: {
        title: "操作", //名称
        width: 80, //宽度
        fixed: "right", // right - 固定在右侧
        buttonList: [
          //按钮列表
          {
            title: "删除",
            // type: true,
            isShow: (row, btn, scope) => {
              return scope.$index !== 0 ? true : false;
            },
          },
        ],
      },
    };
  },
  created() {
    this.handleShow();
  },
  watch: {
    addForm(val) {
      console.log(val);
    },
  },
  methods: {
    handleClose() {
      this.formulas = {};
      this.selectList = [];
      this.$emit("close");
    },
    handleDelete() {
      this.selectList = [];
      this.formulas.dataMarkerId = "";
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.formulas.dataRuleList = this.formulas.dataRuleList.map(
            (item) => {
              item.min = Number(item.min);
              if (item.max) {
                item.max = Number(item.max);
              } else {
                item.max = null;
              }
              item.score = Number(item.score);
              return item;
            }
          );
          delete this.formulas.practicalList;
          delete this.formulas.targetList;
          this.$emit("save", this.formulas);
        } else {
          console.log("error submit!!");
            this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
          return false;
        }
      });
    },
    commitSelect(val) {
      this.formulas.dataMarkerId = "";
      this.selectList = val;
      if (val.length > 0) {
        val.forEach((item) => {
          this.formulas.dataMarkerId = item.employeeId;
        });
      }
      if (this.formulas.dataMarkerId) {
        this.$refs["formulas"].clearValidate(["dataMarkerId"]);
      }
      this.showDialog = false;
      console.log(this.formulas);
    },
    handleShow() {
      this.formulas = JSON.parse(JSON.stringify(this.addForm));
      if (this.formulas.dataRuleType == null) {
        this.formulas.dataRuleType = 1;
      }
      console.log(this.formulas);
      if (
        (this.formulas.dataRuleList &&
          this.formulas.dataRuleList.length == 0) ||
        this.formulas.dataRuleList == null
      ) {
        this.formulas.dataRuleList = [];
        this.formulas.dataRuleList.push({
          max: "", //规则范围结束值
          min: "0", //规则范围开始值
          score: "", //规则范围内的评分
        });
      }
      this.formulas.dataRuleList = this.formulas.dataRuleList.map((item) => {
        item.min = String(item.min);
        if (item.max || item.max == 0) {
          item.max = String(item.max);
        }
        item.score = String(item.score);
        return item;
      });
      if (this.formulas.dataRuleType == "1") {
        this.formulas.practicalList = JSON.parse(
          JSON.stringify(this.formulas.dataRuleList)
        );
        this.formulas.targetList = [];
      } else {
        this.formulas.targetList = JSON.parse(
          JSON.stringify(this.formulas.dataRuleList)
        );
        this.formulas.practicalList = [];
      }
      if (this.formulas.dataMarkerId) {
        this.getSelectList(this.userList);
      }
    },
    handleDataRuleType(val) {
      if (val == 1) {
        this.formulas.dataRuleList = this.formulas.practicalList;
      } else {
        this.formulas.dataRuleList = this.formulas.targetList;
      }
      if (this.formulas.dataRuleList.length == 0) {
        this.formulas.dataRuleList.push({
          max: "", //规则范围结束值
          min: "0", //规则范围开始值
          score: "", //规则范围内的评分
        });
      }
      this.$nextTick(() => {
        this.$refs["formulas"].clearValidate();
      });
    },
    getSelectList(data) {
      this.userList.forEach((item) => {
        if (item.employeeId == this.formulas.dataMarkerId) {
          item.name = item.employeeName;
          this.selectList.push(item);
        }
      });
    },
    handleClick(data) {
      console.log(data);
    },
    handleOperaClick(btn, row) {
      if (btn == "删除") {
        let arr = this.formulas.dataRuleList;
        let index = arr.findIndex(function (item) {
          return item.min === row.min && item.max === row.max;
        });
        console.log(index);
        if (arr[index + 1] && arr[index - 1] && arr[index - 1].max) {
          arr[index + 1].min = arr[index - 1].max;
        } else if (!arr[index - 1]) {
          arr[index + 1].min = "0";
        }
        arr.splice(index, 1);
      }
    },
    handleNameInput(data, ref, num, name) {
      if (data.length > num) {
        if (name == "数据来源") {
          this.formulas.dataSource = data.substr(0, data.length - 1);
        } else {
          this.formulas.dataUnit = data.substr(0, data.length - 1);
        }
        this.$message.error(name + "最大不超过" + num + "个字符");
        this.$refs[ref].blur();
      }
    },
    handleAdd() {
      let item = {
        max: "", //规则范围结束值
        min: "", //规则范围开始值
        score: "", //规则范围内的评分
      };
      let lastItem =
        this.formulas.dataRuleList[this.formulas.dataRuleList.length - 1];
      console.log(lastItem);
      if (lastItem.max) {
        item.min = lastItem.max;
      }
      this.formulas.dataRuleList.push(item);
    },
    handleChange(row, index, code) {
      let arr = this.formulas.dataRuleList;
      if (code == "min") {
        arr[index - 1].max = row.min;
      } else {
        if (arr[index + 1]) {
          arr[index + 1].min = row.max;
        }
      }
    },
    handleBlur(row, index, code) {
      let arr = this.formulas.dataRuleList;
      if (code == "min") {
        if (index !== 1) {
          if (
            Number(arr[index - 1].min) >= Number(row.min) ||
            Number(row.min) >= Number(row.max)
          ) {
            this.$message.error("上限值不能小于下限值");
            console.log("1");
            arr[index].min = "";
            arr[index - 1].max = "";
          }
        } else {
          if (
            Number(arr[index - 1].min) > Number(row.min) ||
            Number(row.min) >= Number(row.max)
          ) {
            this.$message.error("上限值不能小于下限值");
            console.log("1");
            arr[index].min = "";
            arr[index - 1].max = "";
          }
        }
      } else {
        if (arr[index + 1]) {
          if (index !== 0) {
            if (
              (arr[index + 1].max &&
                Number(arr[index + 1].max) <= Number(row.max)) ||
              (row.min && Number(row.min) >= Number(row.max))
            ) {
              this.$message.error("上限值不能小于下限值");
              console.log("2");
              arr[index].max = "";
              arr[index + 1].min = "";
            }
          } else {
            if (
              (arr[index + 1].max &&
                Number(arr[index + 1].max) <= Number(row.max)) ||
              (row.min && Number(row.min) > Number(row.max))
            ) {
              this.$message.error("上限值不能小于下限值");
              console.log("2");
              arr[index].max = "";
              arr[index + 1].min = "";
            }
          }
        } else {
          if (index !== 0) {
            if (row.min && Number(row.min) >= Number(row.max)) {
              this.$message.error("上限值不能小于下限值");
              console.log("3");
              arr[index].max = "";
            }
          } else {
            if (row.min && Number(row.min) > Number(row.max)) {
              this.$message.error("上限值不能小于下限值");
              console.log("3");
              arr[index].max = "";
            }
          }
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.main {
  min-width: 0;
  flex: 1;
  margin-left: 24px;
  display: flex;
}
.add-class {
  width: 450px;
  margin: 0 auto;
  margin-top: 20px;
}
.is-title {
  margin-top: 10px;
  font-size: 16px;
  color: #070f29;
  letter-spacing: 0;
  line-height: 16px;
}

.addName-bn {
  width: 100px;
  height: 32px;
  margin-top: 5px;
  background: #f1f1f1;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  box-sizing: border-box;
  .addName-name {
    width: 70px;
    display: inline-block;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    margin-right: 5px;
  }
  .icon-close1 {
    font-size: 12px;
    color: #9ea5bd;
  }
}

.icon-tianjiachengyuan {
  font-size: 32px;
  cursor: pointer;
  color: $mainColor;
}
.drawer-main {
  .el-form-item {
    margin-bottom: 12px;
  }
}
.flex {
  display: flex;
  align-items: center;
  color: #6a6f7f;
}
.icon-jingshi-qiangtishi1,
.icon-help {
  font-size: 20px;
  margin-right: 10px;
  color: #9ea5bd;
}
.icon-help {
  font-size: 14px;
}
.cource-span {
  height: 32px;
  line-height: 32px;
  margin: 0 10px;
}
.add-bn {
  color: $mainColor;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}
.icon-add1 {
  font-size: 14px;
}
.table-main {
  /deep/.el-form-item__error {
    top: 78%;
    width: 150px;
    z-index: 999;
  }
  /deep/.el-form-item__content {
    line-height: 30px;
    position: relative;
    font-size: 14px;
  }
}
.icon-shujuyichang {
  margin-right: 10px;
}
/deep/.el-table .el-table__cell {
  padding-top: 15px !important;
  padding-bottom: 0 !important;
}
/deep/.el-button--text {
  padding-bottom: 20px;
}
/deep/.el-input.is-disabled .el-input__inner {
  background-color: #fafafa !important;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}
/deep/.el-button--small {
  font-size: 14px;
}
</style>
