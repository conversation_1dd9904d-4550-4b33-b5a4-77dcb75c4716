<template>
  <div class="inspectionSetup">
    <section class="title-content" v-if="type !== '5'">
      <span class="title-line"></span>
      <span class="title-text">{{ text }}</span>
    </section>
    <div class="title" v-if="type == '5'">
      如果设置了指标权重，系统将加权计算总分
    </div>
    <old-table
      :data="questionData"
      :headerData="headerData"
      :isShowOperation="isShowOperation"
    >
      <template slot="date" slot-scope="scope">
        <div class="weight">
          {{ scope.msg.row.date }}
        </div>
      </template>
    </old-table>
    <div class="footer" v-if="type == '5'">
      考核评分=评分1*<span class="weight">40%</span>+评分2*<span class="weight"
        >20%</span
      >+评分3*<span class="weight">5%</span>+评分4*<span class="weight">5%</span
      >+评分5*<span class="weight">30%</span>
    </div>
  </div>
</template>

<script>
import { defTitle } from "../../personalPerformance/components";
export default {
  props: ["type"],
  components: {
    defTitle,
  },
  data() {
    return {
      headerData: [],
      text: "",
      rationHeader: [
        { title: "指标名称", label: "name", align: "left", width: "100px" },
        {
          title: "指标说明",
          label: "description",
          align: "left",
          width: "100px",
        },
        {
          title: "评价标准",
          label: "scoreStandard",
          align: "left",
          width: "200px",
        },
        {
          title: "评分上限",
          label: "maxScore",
          align: "left",
        },
        {
          title: "权重",
          label: "weight",
          align: "left",
        },
        {
          title: "目标值",
          label: "target",
          align: "left",
        },
        {
          title: "实际完成值",
          label: "complete",
          align: "left",
          width: "120px",
        },
      ],
      qualitativeHeader: [
        { title: "指标名称", label: "name", align: "left", width: "100px" },
        {
          title: "指标说明",
          label: "description",
          align: "left",
          width: "250px",
        },
        {
          title: "评价标准",
          label: "scoreStandard",
          align: "left",
          width: "250px",
        },
        {
          title: "评分上限",
          label: "maxScore",
          align: "left",
        },
        {
          title: "权重",
          label: "weight",
          align: "left",
        },
      ],
      addHeader: [
        { title: "指标名称", label: "name", align: "left", width: "100px" },
        {
          title: "指标说明",
          label: "description",
          align: "left",
          width: "250px",
        },
        {
          title: "评价标准",
          label: "scoreStandard",
          align: "left",
          width: "250px",
        },
        {
          title: "加分上限",
          label: "maxScore",
          align: "left",
        },
      ],
      deleteHeader: [
        { title: "指标名称", label: "name", align: "left", width: "100px" },
        {
          title: "指标说明",
          label: "description",
          align: "left",
          width: "250px",
        },
        {
          title: "评价标准",
          label: "scoreStandard",
          align: "left",
          width: "250px",
        },
        {
          title: "减分上限",
          label: "maxScore",
          align: "left",
        },
      ],
      weightHeader: [
        { title: "指标名称", label: "name", align: "left", width: "166px" },
        {
          title: "指标权重",
          label: "date",
          align: "left",
          width: "166px",
          slot: "date",
        },
        {
          title: "指标评分",
          label: "scoreStandard",
          align: "left",
        },
      ],
      isShowOperation: false, //是否显示操作列
      questionData: [],
      rationData: [
        {
          name: "客服工作计划完成率",
          description: "客户经理考核指标",
          scoreStandard: "考核期内客服工作计划完成率在____%以上",
          maxScore: "20.00分",
          weight: "40%",
          target: "90%",
          complete: "指定人员录入",
        },
        {
          name: "客服费用预算节省率",
          description: "考核期内客服费用预算节省率达____%",
          scoreStandard: "考核期内客服工作计划完成率在____%以上",
          maxScore: "20.00分",
          weight: "30%",
          target: "90%",
          complete: "指定人员录入",
        },
        {
          name: "客户意见反馈及时率",
          description: "客户经理考核指标",
          scoreStandard: "考核期内对客户意见在标准时间内的反馈率达____%以上",
          maxScore: "20.00分",
          weight: "30%",
          target: "90%",
          complete: "指定人员录入",
        },
      ],
      qualitativeData: [
        {
          name: "工作素质",
          description: "热爱集体，最终领导，配合支持工作",
          scoreStandard: "请列举4个实例，每个实例5分",
          maxScore: "20.00分",
          weight: "40%",
        },
        {
          name: "团结精神",
          description: "关心他人，团结协作",
          scoreStandard: "请列举4个实例，每个实例5分",
          maxScore: "20.00分",
          weight: "30%",
        },
        {
          name: "服务态度",
          description: "对内、外用户服务周到热情",
          scoreStandard: "请列举4个实例，每个实例5分",
          maxScore: "20.00分",
          weight: "30%",
        },
      ],
      addData: [
        {
          name: "工作创新",
          description: "含业务创新、技术创新和管理创新等",
          scoreStandard:
            "根据收益及影响大小等因素，在当月或次月有公议小组酌定。特殊情况的，由总经理另定",
          maxScore: "10.00分",
        },
        {
          name: "劳动模范",
          description: "兼职岗位或参与岗职责外项目",
          scoreStandard: "依据工作量和重要度等因素由总经理酌定",
          maxScore: "10.00分",
        },
      ],
      deleteData: [
        {
          name: "客户投诉",
          description: "发生客户投诉行为",
          scoreStandard: "有效投诉一经核实，每起投诉扣1分，最高扣10分",
          maxScore: "10.00分",
        },
        {
          name: "月度计划提交",
          description: "未按要求提交月度计划",
          scoreStandard: "每迟交一次扣1分，最高扣12分",
          maxScore: "10.00分",
        },
      ],
      weightData: [
        {
          name: "指标1",
          date: "40%",
          scoreStandard: "评分1",
        },
        {
          name: "指标2",
          date: "20%",
          scoreStandard: "评分2",
        },
        {
          name: "指标3",
          date: "5%",
          scoreStandard: "评分3",
        },
        {
          name: "指标4",
          date: "5%",
          scoreStandard: "评分4",
        },
        {
          name: "指标5",
          date: "30%",
          scoreStandard: "评分5",
        },
      ],
    };
  },
  methods: {
    getList() {
      switch (this.type) {
        case 1:
          this.headerData = this.rationHeader;
          this.questionData = this.rationData;
          this.text = "定量考核指标";
          break;
        case 2:
          this.headerData = this.qualitativeHeader;
          this.questionData = this.qualitativeData;
          this.text = "定性考核指标";
          break;
        case 3:
          this.headerData = this.addHeader;
          this.questionData = this.addData;
          this.text = "加分项";
          break;
        case 4:
          this.headerData = this.deleteHeader;
          this.questionData = this.deleteData;
          this.text = "减分项";
          break;
        case "5":
          this.headerData = this.weightHeader;
          this.questionData = this.weightData;
          break;
        default:
          break;
      }
    },
  },
  created() {
    this.getList();
  },
  watch: {
    type(val) {
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  width: 270px;
  padding-bottom: 10px;
}
.footer {
  width: 450px;
  margin-top: 10px;
}
.weight {
  color: #ff9500;
}
.title-content {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .title-line {
    width: 3px;
    height: 14px;
    background: var(--color-primary);
    border-radius: 1px;
  }
  .title-text {
    margin-left: 10px;
    font-size: 14px;
    color: #070f29;
    letter-spacing: 0;
    line-height: 14px;
  }
}
.inspectionSetup{
  margin: 4px 4px;
}
</style>
