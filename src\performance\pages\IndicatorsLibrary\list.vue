<template>
  <div class="main">
    <classTree name="题库" @handClickNode="handClickNode"></classTree>
    <div class="list-main">
      <div class="header">
        <div class="left-content">
          <div class="name">{{ title }}</div>
        </div>
        <div class="right-content">
          <el-button
            type="primary"
            @click="handleAdd"
            v-if="havePrivilege('kpi.performance.indicatorBank.indicatorAdd')"
            >新增考核指标</el-button
          >
        </div>
      </div>
      <div class="question-content">
        <div class="search">
          <el-input
            placeholder="请输入考核指标名称"
            v-model="searchForm.name"
            class="search-question"
            style="width: 260px"
            @keydown.enter.native="fetchData(1)"
          >
            <i slot="suffix" class="iconfont-per icon-sousuo"></i>
          </el-input>
          <el-select
            v-model="searchForm.type"
            placeholder="请选择"
            style="width: 260px"
            @change="fetchData(1)"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="questionTable">
          <old-table
            :data="questionData"
            :headerData="headerData"
            :isShowOperation="isShowOperation"
            :operaOptions="operaOptions"
            :isShowPagination="true"
            @operaClick="handleOperaClick"
            :pageOptions="pageOptions"
            @sizeChange="handleSizeChange"
            @currentChange="handleCurrentChange"
          >
            <template slot="maxScore-header">
              <span>评分上限</span>
              <el-tooltip
                content="加分项：加分上限; 减分项：减分上限"
                placement="top"
              >
                <i class="icon iconfont-per icon-help"></i>
              </el-tooltip>
            </template>
            <template slot="weight-header">
              考核指标权重
              <el-tooltip
                effect="dark"
                content="加分项、减分项没有考核指标权重，不能设置"
                placement="top"
              >
                <i class="iconfont-per icon-help" />
              </el-tooltip>
            </template>
            <template slot="description" slot-scope="scope">
              <el-tooltip
                placement="top"
                :disabled="scope.msg.row.description.length < 70"
              >
                <div slot="content">
                  {{ scope.msg.row.description }}
                </div>
                <p class="text">{{ scope.msg.row.description }}</p>
              </el-tooltip>
            </template>
            <template slot="scoreStandard" slot-scope="scope">
              <el-tooltip
                placement="top"
                :disabled="scope.msg.row.scoreStandard.length < 70"
              >
                <div slot="content">
                  {{ scope.msg.row.scoreStandard }}
                </div>
                <p class="text">{{ scope.msg.row.scoreStandard }}</p>
              </el-tooltip>
            </template>
            <template slot="scoreType" slot-scope="scope">
              <div v-if="scope.msg.row.scoreType == 1">直接输入</div>
              <div v-if="scope.msg.row.scoreType == 2">
                <el-tooltip placement="top">
                  <p style="text-align: left" class="text">
                    <span>公式计算</span><br />
                    <span v-if="scope.msg.row.dataRuleType == 1"
                      >按实际完成值来算:</span
                    >
                    <span v-if="scope.msg.row.dataRuleType == 2"
                      >按目标达成率计算:</span
                    ><br />
                    <span
                      v-for="(dataRuleItem, index) in scope.msg.row.dataRule"
                      :key="index"
                    >
                      <span v-if="scope.msg.row.dataRuleType == 1"
                        >{{ index + 1 }}、{{
                          dataRuleItem.min + scope.msg.row.dataUnit
                        }}<span v-if="index !== 0">＜</span
                        ><span v-if="index == 0">≤</span>完成值≤{{
                          dataRuleItem.max + scope.msg.row.dataUnit
                        }}</span
                      ><span v-if="scope.msg.row.dataRuleType == 2"
                        >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                          v-if="index !== 0"
                          >＜</span
                        ><span v-if="index == 0">≤</span>目标达成率≤{{
                          dataRuleItem.max
                        }}%</span
                      >,得分：{{ dataRuleItem.score }}分;<br />
                    </span>
                  </p>

                  <p slot="content">
                    <span>公式计算</span><br />
                    <span v-if="scope.msg.row.dataRuleType == 1"
                      >按实际完成值来算:</span
                    >
                    <span v-if="scope.msg.row.dataRuleType == 2"
                      >按目标达成率计算:</span
                    ><br />
                    <span
                      v-for="(dataRuleItem, index) in scope.msg.row.dataRule"
                      :key="index"
                    >
                      <span v-if="scope.msg.row.dataRuleType == 1"
                        >{{ index + 1 }}、{{
                          dataRuleItem.min + scope.msg.row.dataUnit
                        }}<span v-if="index !== 0">＜</span
                        ><span v-if="index == 0">≤</span>完成值≤{{
                          dataRuleItem.max + scope.msg.row.dataUnit
                        }}</span
                      ><span v-if="scope.msg.row.dataRuleType == 2"
                        >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                          v-if="index !== 0"
                          >＜</span
                        ><span v-if="index == 0">≤</span>目标达成率≤{{
                          dataRuleItem.max
                        }}%</span
                      >,得分：{{ dataRuleItem.score }}分;<br />
                    </span>
                  </p>
                </el-tooltip>
              </div>
            </template>
          </old-table>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible1" width="522px" :show-close="false">
      <div slot="title">
        <div class="z-header">
          <span class="title">删除指标 </span>
          <i class="iconfont-per icon-close1" @click="closeDelete"></i>
        </div>
      </div>
      <div>
        <i class="iconfont-per icon-jingshi-qiangtishi1 delete-icon"></i>
        <span style="color: #555555">删除后不可恢复，确认要删除吗？</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDelete">取 消</el-button>
        <el-button type="primary" :loading="isDeleteLoading" @click="handleDeleteNode(deleteId)"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import classTree from "./components/classTree/index.vue";
import { getIndicatorList, IndicatorRemove } from "../../store/api";
import store from "../../store";
import { havePrivilege } from "performance/utils/util.js";
import { mapState } from "vuex";
const indicatorType = {
  1: "定量考核指标",
  2: "定性考核指标",
  3: "加分项",
  4: "减分项",
};
const bankList = {
  1: "公司考核",
  2: "部门考核",
  3: "个人考核",
};
export default {
  components: {
    classTree,
  },
  computed: {
    ...mapState({
      librarySearchForm: (state) => store.state.librarySearchForm,
    }),
  },
  beforeRouteEnter: (to, from, next) => {
    if (from.path !== "/performance/libraryAdd") {
      store.commit("SET_LIBRARYSEARCHFORM", {
        bankId: "",
        currentPage: 1,
        name: "",
        pageSize: 10,
        type: null,
      });
    }
    next();
  },
  data() {
    return {
      havePrivilege,
      dialogVisible1: false,
      title: "考核指标库",
      deleteId: "",
      tableHeight: document.body.offsetHeight - 300 + "px",
      isDeleteLoading:false,
      searchForm: {
        bankId: "",
        currentPage: 1,
        name: "",
        pageSize: 10,
        type: null,
      },
      pageOptions: {
        currPage: 1, //当前页码
        total: 10, //数据总数
        pageSize: 10, //每页显示条数
        pageSizes: [10, 20, 50, 100], //每页显示个数选择器选项设置
      },
      bankType: "",
      options: [
        {
          value: null,
          label: "全部类型",
        },
        {
          value: "1",
          label: "定量考核指标",
        },
        {
          value: "2",
          label: "定性考核指标",
        },
        {
          value: "3",
          label: "加分项",
        },
        {
          value: "4",
          label: "减分项",
        },
      ],
      headerData: [
        {
          title: "考核指标名称",
          label: "name",
          align: "left",
          width: 180,
          fixed: "left",
        },
        {
          title: "考核类型",
          label: "examineeType",
          align: "left",
          minWidth: "100px",
          formatter: (row, column, cellVal) => {
            //formatter - 自定义单元格内容
            for (let ke in bankList) {
              if (ke == cellVal) {
                return bankList[ke];
              }
            }
          },
        },
        {
          title: "考核指标类型",
          label: "type",
          align: "left",
          minWidth: "120px",
          formatter: (row, column, cellVal) => {
            //formatter - 自定义单元格内容
            for (let ke in indicatorType) {
              if (ke == cellVal) {
                return indicatorType[ke];
              }
            }
          },
        },
        {
          title: "考核指标说明",
          label: "description",
          slot: "description",
          align: "left",
          minWidth: "150px",
        },
        {
          title: "评价标准",
          label: "scoreStandard",
          align: "left",
          slot: "scoreStandard",
          minWidth: "150px",
        },
        {
          title: "评分上限",
          label: "maxScore",
          align: "left",
          minWidth: "120px",
          slotHeader: "maxScore-header",
        },
        {
          title: "考核指标权重",
          label: "weight",
          align: "left",
          minWidth: "140px",
          showTooltip: true,
          slotHeader: "weight-header",
        },
        {
          title: "评分方式",
          label: "scoreType",
          align: "left",
          minWidth: "300px",
          slot: "scoreType",
        },
      ],
      isShowOperation: true, //是否显示操作列
      operaOptions: {
        title: "操作", //名称
        width: 120, //宽度
        fixed: "right", // right - 固定在右侧
        buttonList: [
          //按钮列表
          {
            title: "编辑",
            isShow: (row, btn, scope) => {
              return havePrivilege(
                "kpi.performance.indicatorBank.indicatorUpdate"
              )
                ? true
                : false;
            },
          }, // type - 是否为 否定含义：表格里所有否定含义的操作都用红色，比如“删除”、“停用”、“撤销”、“拒绝”等等
          {
            title: "删除",
            isShow: (row, btn, scope) => {
              return havePrivilege(
                "kpi.performance.indicatorBank.indicatorDelete"
              )
                ? true
                : false;
            },
          },
        ],
      },
      questionData: [],
      total: 10,
    };
  },
  created() {
    console.log(this.librarySearchForm);
    if (this.librarySearchForm) {
      this.searchForm = JSON.parse(JSON.stringify(this.librarySearchForm));
      this.pageOptions.pageSize = this.searchForm.pageSize;
      this.pageOptions.currPage = this.searchForm.currentPage;
      if (this.librarySearchForm.title) {
        this.title = JSON.parse(JSON.stringify(this.librarySearchForm.title));
      }
      delete this.searchForm.title;
    }
    this.fetchData();
  },
  mounted() {
    window.addEventListener("keydown", (event) => {
      if (event.keyCode === 9) {
        event.preventDefault();
      }
    });
  },
  destroyed() {
    window.removeEventListener("keydown", (event) => {
      event.preventDefault();
    });
  },
  methods: {
    closeDelete() {
      this.dialogVisible1 = false;
      this.deleteId = "";
    },
    //获取指标库分组数
    //操作-点击事件
    handleOperaClick(btn, row) {
      if (btn == "编辑") {
        console.log(row);
        this.searchForm.title = this.title;
        store.commit("SET_LIBRARYSEARCHFORM", this.searchForm);
        this.$router.push({
          path: "/performance/libraryAdd",
          query: { id: row.id, bankType: row.examineeType },
        });
      }
      if (btn == "删除") {
        this.dialogVisible1 = true;
        this.deleteId = row.id;
      }
    },
    //删除当前节点
    handleDeleteNode(data) {
      this.isDeleteLoading = true
      IndicatorRemove({ id: data }).then((res) => {
        if (res.success) {
          this.$message.success("删除成功");
          this.fetchData();
        }
      }).finally(()=>{
        setTimeout(()=>{
          this.isDeleteLoading = false
        },300)
      })
      this.closeDelete();
    },
    fetchData(curr) {
      this.searchForm.currentPage = curr || this.searchForm.currentPage;
      getIndicatorList(this.searchForm).then((res) => {
        if (res.success) {
          this.pageOptions.total = res.data.total;
          this.questionData = res.data.records;
          this.questionData.map((item) => {
            item.description = item.description || "--";
            item.weight = item.weight + "%";
            item.maxScore = this.toDecimal2(item.maxScore) + "分";
          });
        }
      });
    },
    toDecimal2(x) {
      var f = parseFloat(x);
      if (isNaN(f)) {
        return false;
      }
      var f = Math.round(x * 100) / 100;
      var s = f.toString();
      var rs = s.indexOf(".");
      if (rs < 0) {
        rs = s.length;
        s += ".";
      }
      while (s.length <= rs + 2) {
        s += "0";
      }
      return s;
    },

    handleSizeChange(val) {
      this.searchForm.pageSize = val;
      this.searchForm.currentPage = 1;
      this.pageOptions.pageSize = val;
      this.pageOptions.currPage = 1;
      this.fetchData(1);
    },
    handleCurrentChange(val) {
      console.log(val);
      this.searchForm.currentPage = val;
      this.pageOptions.currPage = val;
      this.fetchData();
    },
    handClickNode(val) {
      console.log(val);
      if (val) {
        this.searchForm.bankId = val.id;
        this.title = val.name;
        this.bankType = val.type;
      } else {
        this.searchForm.bankId = "";
        this.title = "考核指标库";
        this.bankType = "";
      }
      this.fetchData();
    },
    //新增考核指标
    handleAdd() {
      this.searchForm.title = this.title;
      store.commit("SET_LIBRARYSEARCHFORM", this.searchForm);
      this.$router.push({
        path: "/performance/libraryAdd",
        query: { bankId: this.searchForm.bankId, bankType: this.bankType },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.main {
  display: flex;
  height: calc(100vh - 81px);
}
.iconfont-per {
  color: #9ea5bd;
}
.list-main {
  min-width: 0;
  flex: 1;
  padding: 0 20px 20px 10px;
  padding-right: 30px;
  overflow-y: scroll;
}
.header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  border-bottom: 1px solid #eaeaea;
  .left-content {
    display: flex;
    align-items: center;
    .line {
      height: 24px;
      width: 2px;
      background: #888;
      margin: 0 20px;
    }
    .name {
      color: #070f29;
      font-size: 16px;
      width: 200px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .right-content {
    // margin-right: 20px;
  }
}
.icon-sousuo {
  line-height: 40px;
}
.search {
  margin: 24px 0 20px;
  display: flex;
  .search-question {
    width: 280px;
    margin-right: 10px;
  }
  .choose-style {
    width: 240px;
  }
}
.pages {
  text-align: right;
  margin-top: 20px;
}
/deep/.el-table__fixed-right::before {
  display: none;
}
.text {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
}
/deep/.el-tooltip__popper .is-dark {
  background: bisque;
}
.z-header {
  display: flex;
  justify-content: space-between;
  .title {
    font-weight: Medium;
    font-size: 16px;
    color: #070f29;
  }
}
.delete-icon {
  color: $lineBorderPointer;
}
.icon-close1 {
  font-size: 16px;
  color: #9ea5bd;
}
/deep/.el-button--small {
  font-size: 14px;
}
</style>
