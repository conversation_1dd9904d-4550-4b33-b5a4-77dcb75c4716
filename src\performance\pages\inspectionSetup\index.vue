<template>
  <div class="inspectionSetup">
    <header class="header main-title">
      <el-row type="flex">
        <el-col :span="12">
          <span>考核设置</span>
        </el-col>
      </el-row>
    </header>
    <div class="main">
      <div class="title">
        <i class="icon iconfont-per icon-shujuyichang"></i>
        <span
          >设置默认等级分布规则，系统将根据此规则换算分数等级，等级变更不影响已启动考核的计划</span
        >
      </div>
      <el-form
        :model="addform"
        ref="addform"
        label-width="130px"
        :rules="rules"
      >
        <el-table :data="addform.tableData" class="add-class">
          <el-table-column prop="name" label="等级名称" width="180">
            <template slot="header">
              <span>等级名称</span>
              <el-tooltip
                placement="top"
                content="设置考核总评分对应的等级名称。如：90分-100分的员工，对应等级为S"
              >
                <i class="iconfont-per icon-help"></i>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <el-form-item
                :prop="'tableData.' + scope.$index + '.level'"
                class="table-form"
                :rules="rules.level"
                label-width="0"
              >
                <el-input
                  style="width: 150px"
                  size="small"
                  :ref="'level' + scope.$index"
                  v-model.trim="scope.row.level"
                  @input="
                    handleInput(
                      scope.row.level,
                      scope.$index,
                      10,
                      '等级名称',
                      'level' + scope.$index
                    )
                  "
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="range" label="分数区间范围" width="350">
            <template slot-scope="scope">
              <div style="display: flex; padding-top: 10px">
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.min'"
                  class="table-form"
                  :rules="rules.min"
                  label-width="0"
                >
                  <el-input
                    v-model="scope.row.min"
                    style="width: 120px"
                    size="small"
                    disabled
                  >
                    <template slot="append">分</template>
                  </el-input>
                </el-form-item>
                <span class="cource-span">≤分数＜</span>
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.max'"
                  class="table-form"
                  :rules="scope.$index !== 0 ? rules.max : rules.lastMax"
                  label-width="0"
                >
                  <el-input
                    v-model="scope.row.max"
                    style="width: 120px"
                    size="small"
                    :disabled="scope.$index == 0 ? true : false"
                    @input="handleChange(scope.row.max, scope.$index)"
                    @blur="handleBlur(scope.row.max, scope.$index)"
                  >
                    <template slot="append">分</template>
                  </el-input>
                </el-form-item>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="desc" label="等级说明" width="420">
            <template slot-scope="scope">
              <el-form-item
                style="margin-bottom: 0; height: 55px; width: 100%"
                label-width="0"
              >
                <el-input
                  size="small"
                  :ref="'desc' + scope.$index"
                  v-model="scope.row.desc"
                  @input="
                    handleInput(
                      scope.row.desc,
                      scope.$index,
                      100,
                      '等级说明',
                      'desc' + scope.$index
                    )
                  "
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column width="50">
            <template slot-scope="scope">
              <i
                class="el-icon-delete"
                v-if="
                  scope.$index > 1 &&
                  scope.$index == addform.tableData.length - 1 &&
                  havePrivilege('kpi.performance.planSetting.delete')
                "
                @click="handleDelete(scope.row, scope.$index)"
              ></i>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div
        class="add-btn"
        v-if="havePrivilege('kpi.performance.planSetting.add')"
      >
        <div @click="handleAdd" style="width: 120px; cursor: pointer">
          <i class="iconfont-per icon-add1"></i>
          新增等级
        </div>
      </div>
      <div class="mian-footer">
        <el-button
          type="primary"
          @click="saveBefore('addform')"
          v-if="havePrivilege('kpi.performance.planSetting.save')"
          >保存</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import { getLevelSetting, updateLevelSetting } from "../../store/api";
import { havePrivilege } from "performance/utils/util.js";
export default {
  data() {
    return {
      havePrivilege,
      addform: {
        tableData: [],
      },
      rules: {
        level: {
          required: true,
          validator: (rule, value, callback) => {
            if (!value) {
              callback("等级名称不能为空");
            } else {
              let arr = [];
              this.addform.tableData.map((item) => {
                if (item.level == value) {
                  arr.push(item);
                }
              });
              if (arr.length > 1) {
                callback(value + "已存在");
              } else {
                callback();
              }
            }
          },
          trigger: ["change", "blur"],
        },
        min: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                callback("请输入下限");
              } else {
                if (
                  !new RegExp("^[+]{0,1}(\\d+)$|^[+]{0,1}(\\d+\\.\\d+)$").test(
                    value
                  )
                ) {
                  callback(new Error("请输入数字值"));
                } else {
                  callback();
                }
              }
            },
            trigger: ["change", "blur"],
          },
        ],
        lastMax: {
          required: false,
          validator: (rule, value, callback) => {
            if (value) {
              if (
                !new RegExp("^[+]{0,1}(\\d+)$|^[+]{0,1}(\\d+\\.\\d+)$").test(
                  value
                )
              ) {
                callback(new Error("请输入数字值"));
              } else {
                callback();
              }
            } else {
              callback();
            }
          },
          trigger: ["change", "blur"],
        },
        max: {
          required: true,
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error("请输入上限"));
            } else if (!new RegExp("(^[0-9]\\d*$)").test(value)) {
              callback(new Error("请输入正整数"));
            } else {
              if (Number(value) > 1000) {
                callback(new Error("考核指标权重为0～1000"));
              }
              callback();
            }
          },
          trigger: ["change", "blur"],
        },
      },
    };
  },
  created() {
    this.getSelection();
  },
  methods: {
    getSelection(curr) {
      getLevelSetting().then((res) => {
        if (res.success) {
          this.addform.tableData = res.data;
        }
      });
    },
    handleInput(data, index, num, name, ref) {
      if (data.length > num) {
        if (name == "等级名称") {
          this.addform.tableData[index].level = data.substr(0, num - 1);
        } else {
          this.addform.tableData[index].desc = data.substr(0, num - 1);
        }
        this.$message.error(name + "最大不超过" + num + "个字符");
        this.$refs[ref].blur();
      }
    },
    handleChange(data, dataIndex) {
      this.addform.tableData[dataIndex - 1].min = data;
    },
    handleBlur(data, dataIndex) {
      if (Number(data) <= this.addform.tableData[dataIndex].min) {
        this.$message.error("分数上限必须大于分数下限");
        this.addform.tableData[dataIndex].max = "";
      }
      if (this.addform.tableData[dataIndex - 1].max) {
        if (Number(data) >= this.addform.tableData[dataIndex - 1].max) {
          this.$message.error("分数上限必须大于分数下限");
          this.addform.tableData[dataIndex].max = "";
        }
      }
    },
    handleDelete(data, dataIndex) {
      // for (let i = this.addform.tableData.length - 1; i >= 0; i--) {
      //   if (i == dataIndex) {
      //     if (this.addform.tableData[i + 1]) {
      //       this.addform.tableData[i + 1].max = this.addform.tableData[
      //         i - 1
      //       ].min;
      //     }
      //   }
      // }
      this.addform.tableData[dataIndex - 1].min = "0";
      this.addform.tableData.splice(dataIndex, 1);
    },
    handleAdd() {
      let item = {
        level: "",
        max: "",
        min: "0",
        desc: "",
      };
      this.addform.tableData.push(item);
      this.$nextTick(() => {
        const box = this.$el.querySelector(".main");
        box.scrollTop = box.scrollHeight;
      });
    },
    saveBefore(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // this.addform.tableData.map((item, index) => {
          //   item.min = Number(item.min);
          //   item.max = Number(item.max);
          // });
          delete this.addform.tableData[0].max;
          updateLevelSetting(this.addform.tableData).then((res) => {
            if (res.success) {
              this.$message.success("保存成功");
              this.getSelection();
            }
          });
        } else {
          console.log("error submit!!");
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.main {
  margin: 10px 20px;
  height: calc(100vh - 230px);
  overflow-y: auto;
  overflow-x: hidden;
}
.inspectionSetup {
  height: calc(100vh - 81px);
}
/deep/.el-table__row > td {
  border: none;
}
/deep/.el-table::before {
  height: 0px;
}
/deep/.el-table th.is-leaf,
.el-table td {
  border: none;
}
/deep/.el-form-item__error {
  padding-top: 0;
  top: 100%;
  width: 150px;
  z-index: 9999;
}
.iconfont-per {
  color: #9ea5bd;
}
.table-form {
  width: 120px;
  margin-bottom: 0;
  height: 55px;
}
.cource-span {
  height: 32px;
  line-height: 32px;
  margin: 0 10px;
}
.mian-footer {
  position: fixed;
  bottom: 0;
  width: calc(100% - 250px);
  // width: 100%;
  height: 30px;
  padding: 20px 0 20px 0px;
  border-top: 1px solid #e5e5e5;
  background: #fff;
  text-align: center;
  z-index: 9;
  .btn {
    margin: 0 auto;
  }
}
.el-icon-delete {
  margin-bottom: 20px;
}
.el-icon-delete:hover {
  color: $mainColor;
}
.add-btn {
  color: $mainColor;
  font-size: 14px;
  font-weight: 500;
  width: 1000px;
  margin: 0 auto;
  margin-bottom: 20px;
}
.icon-add1 {
  color: $mainColor;
}
.add-class {
  width: 1000px;
  margin: 0 auto;
  margin-top: 5px;
}
.title {
  width: 1000px;
  margin: 0 auto;
  margin-top: 10px;
}
/deep/.el-input.is-disabled .el-input__inner {
  background-color: #fafafa !important;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}
/deep/.el-table .el-table__cell {
  padding-top: 15px !important;
  padding-bottom: 0 !important;
}
</style>
