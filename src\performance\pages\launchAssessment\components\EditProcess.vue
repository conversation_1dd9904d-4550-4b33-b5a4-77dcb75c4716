<template>
  <div class="edit-process">
    <el-drawer
      title="修改流程"
      :visible.sync="isShowDrawer"
      :before-close="handleDrawerClose"
      size="650px"
      :wrapperClosable="false"
      custom-class="def_edit-process-radius"
    >
      <span slot="title" class="edit-proces-title">修改流程</span>
      <section class="edit-process-content" v-loading="loading">
        <section style="flex:1;">
          <section class="def_per_section">
            <el-divider></el-divider>
          </section>
          <section class="def_per_section" style="margin-top:20px;">
            <def-title :text="examineeName" />
            <section class="def_per_alignItems" style="margin-top:20px;">
              <i class="iconfont-per icon-shujuyichang tip-color" style="font-size:22px"></i>
              <span class="tip-text" style="margin-left:8px">关联人员删除后，将同步变更考核计划中的关联人员。</span>
            </section>
            <section class="def_per_TopBottom" style="margin-top:20px;" v-if="examineeRelations.length!==0">
              <section class="glry-text">关联人员</section>
              <section style="flex:1;">
                <template v-for="(item,index) in examineeRelations">
                  <section :key="item.taskId" class="def_per_leftRight def_per_alignItems" style="justify-content: space-between;">
                    <section class="glry-yg-text def_per_alignItems" >
                      <def-photo class="node-right-photo" :isRandomColor="true" :name="item.employeeName" boxSize="48px" textSize="14px" />
                      <span v-html="handleData(item,'employeeName','status')" style="margin-left:10px;"></span>
                    </section>
                    <section style="min-width: 80px;">
                      <el-link v-if="handleIsshowDelGlryBtn(item.status)" type="primary" :underline="false" @click="handleDelGlry(item)">删除</el-link>
                    </section>
                  </section>
                </template>
              </section>
            </section>
          </section>
          <section class="def_per_section" style="margin-top:10px">
            <def-title text="考核确认流程" />
            <section style="margin-top:37px">
              <template v-for="(item,index) in confirmProcess">
                <def-node :key="item.nodeSort" style="width:100%" 
                  :showLine="index==confirmProcess.length-1 ? false : true" 
                  :showIcon="false"
                  :showNode="true"
                  :showDefPhoto="false"
                  nodeColor="#4F71FF"
                  lineColor="#EAEAEA"
                  margin="3px 0"
                  v-if="handleIsshowConfirmNode(item['nodeProcessors'])"
                >
                  <section class="slot-header" slot="node-header-text">
                    <span class="slot-header-name">{{handleConfirmProcessNodeName(item.nodeProcessors)}}</span>
                    <!-- <span class="slot-header-text">{{handleConfirmProcessNodeName(item.nodeProcessors)}}</span> -->
                  </section>

                  <section class="node-right" slot="node-content" style="margin-top:10px;">
                    <template v-for="(nodeItem,nodeIndex) in item.nodeProcessors">
                      <section :key="nodeItem.taskId" class="def_per_leftRight def_per_alignItems" style="justify-content: space-between;margin-bottom:20px;">
                        <section class="def_per_alignItems">
                          <def-photo class="node-right-photo" :isRandomColor="true" :name="nodeItem.processorName" boxSize="48px" textSize="14px" />
                          <section class="def_per_TopBottom" style="margin-left:20px">
                            <!-- <span class="slot-header-name">{{processorType[nodeItem.processorType]}}</span> -->
                            <span v-html="handleData(nodeItem,'processorName','processorStatus')"></span>
                          </section>
                        </section>
                        <section class="def_per_leftRight" style="min-width: 120px;">
                          <section v-if="handleIsshowEditQrrBtn(nodeItem['processorType'],nodeItem['status'])">
                            <el-link type="primary" :underline="false" @click="handleEditRy({item,index},{nodeItem,nodeIndex})">修改确认人</el-link>
                          </section>
                          <section v-if="handleIsshowDelQrrBtn(nodeItem['status'])" style="margin-left:20px">
                            <el-link type="primary" :underline="false" @click="handleDelQrr({item,index},{nodeItem,nodeIndex})">删除</el-link>
                          </section>
                        </section>
                      </section>
                    </template>
                  </section>

                </def-node>
              </template>
            </section>

          </section>
        </section>
        
        <section class="def_per_section footer">
          <el-divider></el-divider>
          <section class="footer-btn">
            <el-button @click="handleDrawerClose">取消</el-button>
            <el-button type="primary" @click="handleConfirm">确认</el-button>
          </section>
        </section>
      </section>
    </el-drawer>
    
    <!-- <select-staff
      v-if="showDialog"
      :list="employeeTree"
      @close="handleRyClose"
      @commit="handleRyData"
      :select="def_select"
      :isOnly="true"
    ></select-staff> -->
    <user-select
      v-if="showDialog"
      :userList="employeeTree"
      :list="departmentTree"
      @close="handleRyClose"
      @commit="handleRyData"
      :select="def_select"
      :isOnly="true"
    ></user-select>
  </div>
</template>

<script>
import { defTitle,defNode,defHeader,defPhoto } from '../../personalPerformance/components';
import { getEmployeeTree,getExamineePlanProcess,apiExamineePlanProcess,getPlanBaseInfo,getUserList,getDepartmentTree } from 'performance/store/api.js'
import { processorType,ryztStatus } from 'performance/utils/enum.js'
import SelectStaff from 'performance/pages/performanceManage/components/SelectStaff'
import UserSelect from "performance/pages/IndicatorsLibrary/components/UserSelect.vue";

export default {
  name: 'edit-process',
  components: {
    defTitle,
    defNode,
    defHeader,
    defPhoto,
    SelectStaff,
    UserSelect
  },
  props:{
    data:{
      type:Object,
      default:()=>{
        return {
          examineePlanId:null,
          planId:null,
          examineeName:""
        }
      }
    },
    isShowDrawer:{
      type:Boolean,
      dafault:false
    }
  },
  data() {
    return {
      examineePlanId:null,//考核对象id
      planId:null,//考核计划id
      examineeName:'',//考核对象名称
      examineeRelations:[],//关联人员
      confirmProcess:[],//确认流程
      processorType:processorType,//流程节点类型描述
      loading:true,
      removeRelationIds:[],
      removeConfirmTaskIds:[],
      showDialog:false,
      employeeTree:[],
      departmentTree:[],
      // isShowDrawer:true,
      confirmStatus:null,//考核确认状态

      edit_node:{},//正在修改的节点信息
      edit_employee:{},//正在修改的人员信息
      def_select:[],//当前选择人员
    };
  },
  mounted() {
    
  },
  methods: {
    handleInit(){
      this.handleGetPlanBaseInfo()
      this.handleGetExamineePlanProcess();
    },
    async handleGetExamineePlanProcess(){
      let obj = {
        examineePlanId:this.examineePlanId,//考核对象id
        planId:this.planId,//考核计划id
      }
      const { data } = await getExamineePlanProcess(obj)
      const { examineeRelations,confirmProcess } = data
      this.examineeRelations = examineeRelations;
      this.confirmProcess = confirmProcess;
      this.$nextTick(()=>{this.loading = false})
    },
    //考核基本信息
    async handleGetPlanBaseInfo(){
      const { examineePlanId,planId,examineeName } = this.data
      this.examineePlanId = examineePlanId;
      this.planId = planId;
      this.examineeName = `考核对象：${examineeName}`;
      
      let obj = { planId:this.planId }
      const { data } = await getPlanBaseInfo(obj)
      const { confirmStatus } = data
      this.confirmStatus = confirmStatus
    },
    //处理异常数据
    handleData(item,employeeNameId,statusId){
      if([2,3,4,5,6].includes(item[statusId])){
        return `
          <span style="color:red">${item[employeeNameId]}（${ryztStatus[item[statusId]]}）</span>
        `
      }else{
        return `${item[employeeNameId]}`
      }
    },
    //修改确认人
    async handleEditRy({item,index},{nodeItem,nodeIndex}){
      await getDepartmentTree().then(res1=>{
        this.departmentTree = res1.data;
        getUserList().then(res2=>{
          console.log(res1,res2)
          this.def_select = [{employeeId:nodeItem["processorId"]}]
          this.employeeTree = res2.data;
          this.showDialog = true;
          this.edit_node = {
            index:index,
            item:item
          };
          this.edit_employee = {
            index:nodeIndex,
            item:nodeItem
          };
        }).catch(err=>{
        })
      })
      
    },
    //选择人员回调
    handleRyData(val){
      console.log(val[0])
      if(val.length == 1){
        const { employeeId,name } = val[0]
        let nodeIndex = this.edit_node["index"],
            employeeIndex = this.edit_employee["index"],
            nodeProcessors = this.confirmProcess[nodeIndex]["nodeProcessors"];
        let def_nodeProcessors = nodeProcessors.map((v,i)=>{
          if(i == employeeIndex){
            v.processorId = employeeId;
            v.processorName = name;
            v.processorStatus = null;
          }
          return v
        })
        // console.log(def_nodeProcessors)
        this.$set(this.confirmProcess, nodeIndex, {
            ...this.confirmProcess[nodeIndex],
            nodeProcessors: def_nodeProcessors
        });
      }
      this.showDialog = false
    },
    //选择人员取消操作
    handleRyClose(){
      this.showDialog = false;
      this.edit_node = {};
      this.edit_employee = {};
    },
    //确认修改信息
    handleConfirm(){
      let obj = {
        examineePlanId:this.examineePlanId,//考核对象id
        relationList:this.requestExamineeRelations,//关联人员
        confirmProcessors:this.requestConfirmProcess,//确认流程
      }
      apiExamineePlanProcess(obj).then(res=>{
        if(res.success){
          this.$message({message: '修改成功',type: 'success'});
          this.$emit('def_close')
        }else{
          this.$message.error(res.msg);
        }
      }).catch(err=>{
        console.log(err)
      })
    },
    //关闭抽屉
    handleDrawerClose(){
      this.$emit('def_close')
      // this.$confirm('确认关闭？')
      // .then(_ => {
      //   this.$emit('def_close')
      //   // this.isShowDrawer = false
      // })
      // .catch(_ => {});
    },
    //设置考核确认流程节点名称
    handleConfirmProcessNodeName(items){
      // console.log(processorType)
      if(items.length!==0){
        // console.log(processorType[items[0]["processorType"]])
        // return processorType[items[0]["processorType"]]
        return items[0]["processorTitle"]
      }else{
        return '--'
      }
    },
    //删除关联人员
    handleDelGlry(item){
      this.examineeRelations = this.examineeRelations.filter(v=>{
        return v.taskId !== item.taskId
      })
    },
    //是否展示考核确认节点
    handleIsshowConfirmNode(items){
      return items.length === 0 ? false : true;
    },
    //是否展示 关联人员 删除按钮
    handleIsshowDelGlryBtn(){
      /**
       * 至少保留一个关联人员
      */
      return this.examineeRelations.length!==1
    },
    //是否展示确认流程 修改确认人按钮
    handleIsshowEditQrrBtn(type,status){
      /**
       * type:处理人类型 1:被考核者,2:上级,3:指定人员 - 被考核者隐藏修改按钮
       * status:当前状态 1:处理中,2:等待处理,3:已处理 - 已处理隐藏按钮
      */
      return type === 1||status === 3 ? false : true
    },
    //是否展示确认流程 删除按钮
    handleIsshowDelQrrBtn(status){
      /**
       * status:当前状态 1:处理中,2:等待处理,3:已处理 - 已处理隐藏按钮
      */
      let baseArr = [];
      this.confirmProcess.map(v=>{
        baseArr = [...baseArr,...v.nodeProcessors]
      })
      return baseArr.length === 1||status === 3 ? false : true
    },
    //删除确认人
    handleDelQrr({item,index},{nodeItem,nodeIndex}){
      this.edit_node = {
        index:index,
        item:item
      };
      this.edit_employee = {
        index:nodeIndex,
        item:nodeItem
      };
      let nodeChooseIndex = this.edit_node["index"],
          employeeIndex = this.edit_employee["index"],
          nodeProcessors = this.confirmProcess[nodeChooseIndex]["nodeProcessors"];
      let def_nodeProcessors = nodeProcessors.filter((v)=>{
        return v.taskId !== nodeItem.taskId
      })
      console.log(def_nodeProcessors)
      this.$set(this.confirmProcess, nodeChooseIndex, {
          ...this.confirmProcess[nodeChooseIndex],
          nodeProcessors: def_nodeProcessors
      });
    },
    handleRequestExamineeRelations(){
      let arr = []
      this.examineeRelations.map(v=>{
        arr.push({
          employeeId:v.employeeId,
          taskId:v.taskId
        })
      })
      return arr
    },
    handleRequestConfirmProcess(){
      let arr = [],
          baseArr = [];
      this.confirmProcess.map(v=>{
        baseArr = [...baseArr,...v.nodeProcessors]
      })
      console.log(baseArr)
      baseArr.map(v=>{
        arr.push({
          employeeId:v.processorId,
          taskId:v.taskId
        })
      })
      return arr
    }
  },
  computed:{
    // examineeRelations:[],//关联人员
    // confirmProcess:[],//确认流程
    requestExamineeRelations:{//保存流程 关联人员参数
      get(){
        return this.handleRequestExamineeRelations()
      },
      set(val){
      }
    },
    requestConfirmProcess:{//保存流程 确认流程参数
      get(){
        return this.handleRequestConfirmProcess()
      },
      set(val){
      }
    },
  },
  watch:{
    isShowDrawer: {
      handler (val) {
        if(val){
          this.handleInit()
        }
      },
      deep: true
    },
  }
}
</script>
<style lang='scss' scoped>
.edit-process{
  .edit-process-content{
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .edit-proces-title{
    font-size: 16px;
    color: #070F29;
    letter-spacing: 0;
    line-height: 16px;
  }
  .footer{
    .footer-btn{
      display: flex;
      justify-content: flex-end;
    }
    padding-bottom:20px;
  }
  // .def_per_section{
    /deep/.el-divider--horizontal{
      margin: 16px 0 20px 0;;
    }
  // }
  .tip-color{
    color:#9EA5BD;
  }
  .tip-text{
    font-size: 14px;
    color: #6A6F7F;
    letter-spacing: 0;
    line-height: 14px;
  }
  .glry-text{
    font-size: 14px;
    color: #888888;
    letter-spacing: 0;
    // text-align: right;
    line-height: 14px;
  }
  .glry-yg-text{
    font-size: 14px;
    color: #070F29;
    letter-spacing: 0;
    line-height: 14px;
    margin:10px 0;
  }
  .slot-header{
    width:56px;
    // text-align: center;
    .slot-header-name{
      font-size: 14px;
      color: #888888;
      letter-spacing: 0;
      line-height: 14px;
    }
  }
}

</style>