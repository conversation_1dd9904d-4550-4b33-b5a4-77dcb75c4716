<template>
  <div class="def_launchAssessment">
    <section class="def_per_alignItems launchAssessment-tip">
      <i class="iconfont-per icon-jingshi-qiangtishi1 text-color" style="font-size:16px"></i>
      <span class="launchAssessment-text" style="margin-left:8px">发起考核确认后，考核表将分发给待处理人员依次处理</span>
    </section>
    <section class="launchAssessment-content">
      <p class="def_per_alignItems">
        <span class="title">{{khdxzs}}</span>
        <span class="msg" style="margin-left:5px">{{data.total}}</span>
      </p>
      <div class="def_per_alignItems" style="margin-top:23px;align-items: flex-start;">
        <p class="def_per_alignItems">
          <span class="title">{{yclc}}</span>
          <span class="msg" style="margin-left:5px">{{data.errorNum}}</span>
        </p>
        <span class="tip" style="margin-left:10px">您可以发起考核确认后，再对异常考核对象单独调整</span>
      </div>
    </section>
  </div>
</template>

<script>

export default {
  name: 'def_launchAssessment',
  components: {},
  props:{
    data:{
      type:Object,
      default:()=>{
        return {
          errorNum:"",
          total:""
        }
      }
    }
  },
  data() {
    return {
      khdxzs:"考核对象总数",
      yclc:"\u3000\u3000异常流程"
    };
  },
  mounted() {},
  methods: {},
}
</script>
<style lang='scss' scoped>
.def_launchAssessment{
  // padding: 0 20px;
  .launchAssessment-tip{
    .launchAssessment-text{
      color:#888888;
      font-size:14px;
    }
    .text-color{
      color:#FF9500;
    }
  }
  
  
  .launchAssessment-content{
    // display: flex;
    // flex-direction: row;
    margin-top:23px;
    .title{
      min-width:84px;
      font-size: 14px;
      color: #555555;
      letter-spacing: 0;
      text-align: right;
      line-height: 14px;
    }
    .msg{
      font-size: 14px;
      color: #FF9B0E;
      letter-spacing: 0;
    }
    .tip{
      font-size: 12px;
      color: #888888;
      letter-spacing: 0;
      line-height: 18px;
    }
  }
}
</style>