<template>
  <div class="def_removeAssessment">
    <section class="def_per_alignItems removeAssessment-tip">
      <i class="iconfont-per icon-jingshi-qiangtishi1 text-color" style="font-size:16px"></i>
      <span class="removeAssessment-text" style="margin-left:8px">确定将考核对象：{{data.examineeName}} 移除本次考核吗？</span>
    </section>
    <!-- <section class="removeAssessment-content">
      <p class="tip-color">移除后，你可以从"无需考核对象"将其重新加入考核</p>
    </section> -->
  </div>
</template>

<script>

export default {
  name: 'def_removeAssessment',
  components: {},
  props:{
    data:{
      type:Object,
      default:()=>{
        return {
          examineeName:""
        }
      }
    }
  },
  data() {
    return {

    };
  },
  mounted() {},
  methods: {},
}
</script>
<style lang='scss' scoped>
.def_removeAssessment{
  .removeAssessment-tip{
    .removeAssessment-text{
      color:#888888;
      font-size:14px;
    }
    .text-color{
      color:#FF9500;
    }
  }
  .removeAssessment-content{
    margin-left:30px;
    margin-top:15px;
    .text-color{
      color: #555555;
    }
    .data-color{
      color:#FF9500;
    }
    .tip-color{
      margin-top:10px;
      color:#888888;
    }
  }
}
</style>