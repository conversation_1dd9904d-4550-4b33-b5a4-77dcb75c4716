<template>
  <div class="launch-assessment-detail def_per_height" v-loading="loading">
    <def-header 
      :headerText="def_HeaderData.headerText" 
      :isBack="true"
      :isShowTag="true"
      :headerTag="def_HeaderData.headerTag"
    />
    <section class="def_per_section def_per_section-top table-header">
      <section class="launch-assessment-detail-search">
        <el-select v-model="valueGs" clearable placeholder="请选择公司" style="width:250px;margin-right:10px" 
          v-if="type === 1"
          @change="handleGetPlanDetailList"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
        <el-input v-model="valueYg" placeholder="请输入员工姓名/手机号" style="width:250px;margin-right:10px" 
          v-if="type === 3"
          @keyup.enter.native="handleGetPlanDetailList"
          suffix-icon="el-icon-search"
        ></el-input>
        <!-- <def-department v-model="valueBm" placeholder="请选择部门" style="width:250px;margin-right:10px" 
          v-if="type === 2||type === 3" 
          @input="handleGetPlanDetailList"
        /> -->
        <el-input
            v-if="type === 2||type === 3"
            v-model="valueBmName"
            placeholder="请选择部门"
            style="width:250px;margin-right:10px"
            @focus="handleFocusDepart()"
            @clear="handleClearDepart"
            clearable
          >
            <i slot="suffix" v-if="!valueBmName" class="el-input__icon el-icon-plus"></i>
          </el-input>
        <el-radio-group v-model="processStatus"
          @change="handleGetPlanDetailList"
          style="display: flex;"
        >
          <el-radio-button :label="null">全部</el-radio-button>
          <!-- <el-radio-button label="待确认"></el-radio-button> -->
          <el-radio-button :label="2">确认中</el-radio-button>
          <el-radio-button :label="3">已确认</el-radio-button>
        </el-radio-group>
      </section>
      <select-staff
        v-if="showDialog"
        :list="data"
        :isUser="isUser"
        :isOnly="true"
        :isDifferent="true"
        @close="showDialog = false"
        @commit="handleCommit"
        :select="def_select"
      ></select-staff>
      <section>
        <!-- <el-button type="primary" @click="handleLaunchAssessment">确认发起</el-button> -->
      </section>
    </section>

    <section class="def_per_section def_per_section-top">
      <def-etable 
        :tableHeader="tableHeader" 
        :tableData="tableData" 
        @formatter="handleFormatter" 
        @btnColumn="handleBtnColumn"
        @search="handleSearch"
        :total="total"
        :isShowIndex="true"
        :def_height="tableHeight"
        :isHidePage="false"
      />
    </section>

    <!-- 修改流程 -->
    <def-edit-process
      :isShowDrawer="isShowDrawer"
      :data=drawerData
      @def_close="handleDrawerClose"
    />

    <!--移除考核对象-->
    <el-dialog
      title="移除考核对象"
      :visible.sync="isShowRemoveAssessment"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="420px"
      :show-close="false"
    >
      <def-remove-assessment 
        :data="removeAssessmentData"
      />
      <div slot="footer">
        <el-button @click="isShowRemoveAssessment = false">取消</el-button>
        <el-button type="primary" @click="handleRemoveAssessmentConfirm" :loading="isDialogLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { defHeader,defTable,defEtable,defDepartment } from '../personalPerformance/components'
import { getDepartmentTree,getPlanDetailList,getSubsidiaryList,getPlanBaseInfo,getExamineePlanRemove } from 'performance/store/api.js'
import { khlxType,ryztStatus,khqrStatus } from 'performance/utils/enum.js'
import defEditProcess from './components/EditProcess'
import defRemoveAssessment from './components/RemoveAssessment'
import SelectStaff from "performance/pages/performanceManage/components/SelectStaff";
import UserSelect from "performance/pages/IndicatorsLibrary/components/UserSelect.vue";


export default {
  name: 'launch-assessment-detail',
  components: {
    defHeader,
    defTable,
    defEtable,
    defDepartment,
    defEditProcess,
    defRemoveAssessment,
    SelectStaff,
    UserSelect
  },
  data() {
    return {
      loading:true,
      isDialogLoading:false,
      showDialog:false,
      isUser: true,
      data:[],
      def_select:[],//当前选择部门
      /**
       * 筛选条件相关
      */
      options: [],
      valueGs: null,
      valueYg: "",
      valueBm: null,
      valueBmName:"",
      processStatus:null,

      tableHeight:document.body.clientHeight - 305+'px',
      tableHeaderBase:[
        { label: "考核对象", prop: "khdx" },
        { label: "关联人员", prop: "glry" },
        { label: "公司名称", prop: "gsmc" },
        { label: "部门", prop: "bm" },
        { label: "确认人", prop: "qrr" ,type:"addRow"},
        { label: "考核确认状态", prop: "khqrzt" },
        { label: "待处理人", prop: "dclr" },
        { prop: "def_cz", label: "操作" ,width:"180px",btn:[
          {prop:"def_xq",label:"详情",type:"def_btn",fun:"handleDetail"},
          {prop:"def_xglc",label:"修改流程",type:"def_btn",fun:"handleEditProcess"},
          {prop:"def_yc",label:"移除",type:"def_btn",fun:"handleRemoveAssessment"},
        ]},
      ],
      tableHeader:[
        { label: "考核对象", prop: "khdx" },
        { label: "关联人员", prop: "glry" },
        { label: "确认人", prop: "qrr" ,type:"addRow"},
        { label: "考核确认状态", prop: "khqrzt" },
        { label: "待处理人", prop: "dclr" },
        { prop: "def_cz", label: "操作" ,width:"180px",btn:[
          {prop:"def_xq",label:"详情",type:"def_btn",fun:"handleDetail"},
          {prop:"def_xglc",label:"修改流程",type:"def_btn",fun:"handleEditProcess"},
          {prop:"def_yc",label:"移除",type:"def_btn",fun:"handleRemoveAssessment"},
        ]},
      ],
      tableData:[],
      total:null,
      limit:10,
      start:0,
      page:1,

      isShowDrawer:false,
      drawerData:{},
      isShowRemoveAssessment:false,
      removeAssessmentData:{},

      /**
       * 当前行信息
      */
      examineePlanId:null,//考核对象id
      /**
       * 渲染数据相关
      */
      planId:null,//考核计划id
      listType:1,//考核计划列表类型 1:考核确认列表,2:启动考核列表,3:审核列表
      type:null,//考核类型 1:公司考核,2:部门考核,3:个人考核
      khlxType:khlxType,//考核类型枚举-辅助
      def_HeaderData:{},
    };
  },
  mounted() {
    this.handleInit();
    this.handleTableResize();
  },
  methods: {
    handleInit(){
      const { planId } = this.$route.query
      this.planId = planId
      this.handleGetSubsidiaryList();//子公司(用工主体)
      this.handleGetPlanBaseInfo();//考核基本信息
      this.handleGetPlanDetailList();//考核计划详情列表
    },
    handleTableResize(){
      window.onresize = () => {
        return (() => {
          this.tableHeight = document.body.clientHeight - 350+'px';
        })();
      };
    },
    handleCommit(list) {
      if(list && list.length>0){
        this.valueBm = list[0].id;
        this.valueBmName=list[0].name
        this.def_select = list
      }else{
        this.valueBm=null
        this.valueBmName=""
        this.def_select = []
      }
      this.handleInit()
      this.showDialog = false;
    },
    handleClearDepart(){
      this.valueBm=null
      this.valueBmName=""
      this.def_select = []
      this.handleInit()
    },
    async handleGetPlanDetailList(){
      let obj = {
        currentPage:this.page,//当前页
        deptId:this.valueBm,//部门id
        employeeName:this.valueYg,//员工名称
        listType:this.listType,//列表类型
        pageSize:this.limit,//每页大小
        planId:this.planId,//考核计划id
        processStatus:this.processStatus,
        subsidiaryId:this.valueGs,//子公司（用工主体）
        // scoreLevel:"",//等级筛选
      }
      const { data } = await getPlanDetailList(obj)
      const { records,total } = data;
      this.tableData = records;
      this.total = total

      this.loading = false
    },
    //考核基本信息
    async handleGetPlanBaseInfo(){
      let obj = { planId:this.planId }
      const { data } = await getPlanBaseInfo(obj)
      const { name,type,confirmStatus } = data
      this.def_HeaderData = {
        headerText:`${name}考核确认详情`,
        headerTag:`${khqrStatus[confirmStatus]}`
        // headerTag:`待确认`
      }
      console.log(this.khlxType[type])
      this.type = type;
    },
    async handleGetSubsidiaryList(){
      const { data } = await getSubsidiaryList()
      this.options = data
    },
    async handleFocusDepart() {
      await this.getDepartmentTree();
      this.showDialog = true;
    },
    //获取部门树
    async getDepartmentTree() {
      const res = await getDepartmentTree();
      if (res.success) {
        this.data = res.data;
      } else {
        this.$message.error(res.msg);
      }
    },
    handleFormatter({prop,data,btnItem},callback){
      let _this = this
      if(prop=="def_cz"){
        let boo = false
        switch(btnItem){
          case "def_xq":
            boo = true
            callback(boo)
            break;
          case "def_xglc":
            boo = data["confirmStatus"] !== 3; //完成状态不显示修改流程按钮
            callback(boo)
            break;
          case "def_yc":
            boo = data["confirmStatus"] !== 3; //完成状态不显示移除按钮
            callback(boo)
            break;
        }
      }else{
        switch(prop){
          case "khdx":
            if(this.type === 1){
              // callback(`${data["subsidiaryName"]}`)
              callback(`${data["examineeName"]}`||'--')
            }else if(this.type === 2){
              // callback(`${data["deptName"]}`)
              callback(`${data["examineeName"]}`||'--')
            }else if(this.type === 3){
              callback(_this.handleKhdxData(data["examineeName"],data["employeeStatus"]))
            }
            break;
          case "glry":
            callback(_this.handleGlryData(data["relations"]))
            break;
          case "gsmc":
            callback(data["subsidiaryName"]||'--')
            break;
          case "bm":
            callback(data["deptName"]||'--')
            break;
          case "qrr":
            callback(_this.handleRyData(data["confirmerList"]))
            break;
          case "khqrzt":
            callback(khqrStatus[data["confirmStatus"]]||'--')
            break;
          case "dclr":
            callback(_this.handleDclrData(data["pendingOperator"]))
            break;
        }
      }
    },
    handleGlryData(list){
      if(list.length == 0){
        return '--'
      }
      let arr = []
      list.map(v=>{
        if([2,3,4,5,6].includes(v.status)){
           arr.push(`
            <span style="color:red">${v.name}(${ryztStatus[v.status]})</span>
          `)
        }else{
          arr.push(`${v.name}`)
        }
      })
      return arr.join(',')
    },
    //考核对象是否异常
    handleKhdxData(val,type){
      // console.log(val,type)
      if([2,3,4,5,6].includes(type)){
        return `<span style="color:red">${val}(${ryztStatus[type]})</span>`
      }else{
        return `${val}`||'--'
      }
    },
    //人员是否异常
    handleRyData(list){
      if(list.length == 0){
        return '--'
      }
      let arr = [];
      list.map(v=>{
        let arrOperators = [],
            objOperatores = {};
        v.operators && v.operators.forEach(vf=>{
          if(Object.keys(objOperatores).includes(vf.title)){
            objOperatores[vf.title].operatores.push(this.handleOperatorState(vf))
            if([2,3,4,5,6].includes(vf.status)){
               objOperatores[vf.title].isYc = true
            }
          }else{
            objOperatores[vf.title] = {
              title:vf.title,
              operatores:[this.handleOperatorState(vf)],
              isYc:[2,3,4,5,6].includes(vf.status) ? true : false
            }
          }
        })
        Reflect.ownKeys(objOperatores).map(vm=>{
          let title = objOperatores[vm].isYc ? `<span style="color:red">${objOperatores[vm].title}：</span>` : `<span>${objOperatores[vm].title}：</span>`;
          let text = objOperatores[vm].operatores.join('，')
          arrOperators.push(title+text)
        })
        arr.push(arrOperators.join('，'))
      })
      return arr
    },
    handleOperatorState(v){
      if([2,3,4,5,6].includes(v.status)){
        return `<span style="color:red">${v.name}(${ryztStatus[v.status]})</span>`
      }else{
        return `${v.name}`
      } 
    },
    //待处理人是否异常
    handleDclrData(list){
      if(list.length == 0){
        return '--'
      }
      let arr = []
      list.map(v=>{
        if([2,3,4,5,6].includes(v.status)){
           arr.push(`
            <span style="color:red">${v.name}(${ryztStatus[v.status]})</span>
          `)
        }else{
          // arr.push(`${v.name}(${v.status})`)
          arr.push(`${v.name}`)
        }
      })
      return arr.join(',')
    },
    handleBtnColumn(val,type){
      switch(type){
        case "handleDetail":
          this.examineePlanId = val["examineePlanId"]
          // this.$router.push({path: "/my-performance/detail",query: {planId:this.planId,examineePlanId:this.examineePlanId}})
          this.$router.push({path: "/launch-assessment/launch-detail",query: {planId:this.planId,examineePlanId:this.examineePlanId}})
          break;
        case "handleEditProcess":
          this.examineePlanId = val["examineePlanId"]
          this.drawerData = {
            examineePlanId:this.examineePlanId,
            planId:this.planId,
            examineeName:val["examineeName"]
          };
          this.$nextTick(()=>{this.isShowDrawer = true})
          break;
        case "handleRemoveAssessment":
          this.removeAssessmentData = {
            examineeName:val["examineeName"]
          }
          this.examineePlanId = val["examineePlanId"]
          this.isShowRemoveAssessment = true
          break;
      }
    },
    handleSearch({limit,start,page}){
      this.page = page
      this.limit = limit
      this.start = start
      this.handleGetPlanDetailList();//考核计划详情列表
    },
    //移除考核对象-确定
    handleRemoveAssessmentConfirm(){
      let obj = {
        examineePlanId:this.examineePlanId,//考核对象id
        planId:this.planId,//考核计划id
      }
      this.isDialogLoading = true
      getExamineePlanRemove(obj).then(res=>{
        this.handleInit();
        this.isShowRemoveAssessment = false
      }).catch(err=>{
        this.handleInit();
        this.isShowRemoveAssessment = false
      }).finally(()=>{
        setTimeout(()=>{
          this.isDialogLoading = false
        },300)
      })
    },
    //关闭抽屉-before
    handleDrawerClose(){
      this.isShowDrawer = false
      this.handleInit()
    },
    //确定表头信息
    hanldeFilterTableHeader(val){
      this.tableHeader = this.tableHeaderBase.filter(v=>{
        if([1,2].includes(val)){
          return ['khdx','glry','qrr','khqrzt','dclr','def_cz'].includes(v.prop)
        }
        if([3].includes(val)){
          return ['khdx','gsmc','bm','qrr','khqrzt','dclr','def_cz'].includes(v.prop)
        }
      })
    },
  },
  watch:{
    type: {
      handler (val) {
        this.hanldeFilterTableHeader(val)
      },
      deep: true
    },
  }
}
</script>
<style lang='scss' scoped>
.launch-assessment-detail{
  /deep/.el-drawer__header{
    margin-bottom:0px;
  }
  .section{
    // padding:0 20px;
  }
  .table-header{
    // margin-top:20px;
    display: flex;
    justify-content: space-between;
  }
  .table-content{
    // margin-top:20px;
  }
  .launch-assessment-detail-search{
    display:flex;
  }
}
</style>