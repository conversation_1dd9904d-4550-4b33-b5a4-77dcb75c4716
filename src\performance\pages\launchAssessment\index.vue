<template>
  <div class="launch-assessment def_per_height" v-if="!loading" v-loading="loading">
    <def-header 
      :headerText="def_HeaderData.headerText" 
      :isBack="true"
      :isShowTag="true"
      :headerTag="def_HeaderData.headerTag"
    >
    <el-button slot="btnArea" type="primary" :loading="launchAssessmentLoading" @click="handleLaunchAssessment">确认发起</el-button>
    </def-header>
    <section class="def_per_section def_per_section-top table-header">
      <section class="launch-assessment-search" >
        <el-select 
          v-model="valueGs" placeholder="请选择公司" style="width:250px;margin-right:10px;" 
          v-if="type === 1"
          @change="handleGetPlanDetailList"
          clearable
        >
          <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <el-input 
          v-model="valueYg" placeholder="请输入员工姓名" style="width:250px;margin-right:10px;" 
          v-if="type === 3"
          @keyup.enter.native="handleGetPlanDetailList"
        ></el-input>
        <def-department 
          v-model="valueBm" placeholder="请选择部门" style="width:250px;margin-right:10px;" 
          v-if="type === 2||type === 3" 
          @input="handleGetPlanDetailList"
        />
      </section>
      <section>
        
      </section>
    </section>

    <section class="def_per_section def_per_section-top table-content">
      <def-etable 
        :tableHeader="tableHeader" 
        :tableData="tableData" 
        @formatter="handleFormatter" 
        @btnColumn="handleBtnColumn"
        @search="handleSearch"
        :total="total"
        :isShowIndex="true"
        :def_height="tableHeight"
        :isHidePage="false"
      />
    </section>

    <!-- 修改流程 -->
    <def-edit-process
      v-if="isShowDrawer"
      :data=drawerData
      @def_close="handleDrawerClose"
    />

    <!--发起考核确认-->
    <el-dialog
      title="发起考核确认"
      :visible.sync="isShowLaunchAssessment"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      :show-close="false"
    >
      <def-launch-assessment 
        :data="launchAssessmentData"
      />
      <div slot="footer">
        <el-button @click="isShowLaunchAssessment = false">取消</el-button>
        <el-button type="primary" @click="handleLaunchAssessmentConfirm">确定</el-button>
      </div>
    </el-dialog>

    <!--移除考核对象-->
    <el-dialog
      title="移除考核对象"
      :visible.sync="isShowRemoveAssessment"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      :show-close="false"
    >
      <def-remove-assessment 
        :data="removeAssessmentData"
      />
      <div slot="footer">
        <el-button @click="isShowRemoveAssessment = false">取消</el-button>
        <el-button type="primary" @click="handleRemoveAssessmentConfirm" :loading="isDialogLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { defHeader,defTable,defEtable,defDepartment } from '../personalPerformance/components'
import defLaunchAssessment from './components/LaunchAssessment'
import defRemoveAssessment from './components/RemoveAssessment'
import defEditProcess from './components/EditProcess'
import { getEmployeeTree,getPlanBaseInfo,getPlanDetailList,getSubsidiaryList,getPlanDetail,getPlanStartConfirmCheck,getPlanStartConfirm,getExamineePlanRemove } from 'performance/store/api.js'
import { khlxType,ryztStatus,khjhStatus } from 'performance/utils/enum.js'
export default {
  name: 'launch-assessment',
  components: {
    defHeader,
    defTable,
    defEtable,
    defLaunchAssessment,
    defRemoveAssessment,
    defEditProcess,
    defDepartment
  },
  data() {
    return {
      loading:true,
      isDialogLoading:false,
      /**
       * table相关
       * */ 
      tableHeight:document.body.clientHeight - 300+'px',
      tableHeaderBase:[
        { label: "考核对象", prop: "khdx" },
        { label: "关联人员", prop: "glry" },
        { label: "公司名称", prop: "gsmc" },
        { label: "部门", prop: "bm" },
        { label: "确认人", prop: "qrr" ,type:"addRow"},
        { prop: "def_cz", label: "操作" ,width:"200px",btn:[
          {prop:"def_xglc",label:"修改流程",type:"def_btn",fun:"handleEditProcess"},
          {prop:"def_yc",label:"移除",type:"def_btn",fun:"handleRemoveAssessment"},
        ]},
      ],
      tableHeader:[
        { label: "考核对象", prop: "khdx" },
        { label: "关联人员", prop: "glry" },
        { label: "公司名称", prop: "gsmc" },
        { label: "部门", prop: "bm" },
        { label: "确认人", prop: "qrr" ,type:"addRow"},
        { prop: "def_cz", label: "操作" ,width:"200px",btn:[
          {prop:"def_xglc",label:"修改流程",type:"def_btn",fun:"handleEditProcess"},
          {prop:"def_yc",label:"移除",type:"def_btn",fun:"handleRemoveAssessment"},
        ]},
      ],
      tableData:[],
      total:null,
      limit:10,
      start:0,
      page:1,
      tableHeaderEnum:{
        khdx:"examineeName",
        glry:"relations",
        gsmc:"subsidiaryName",
        bm:"deptName",
        qrr:"confirmerList",
      },
      /**
       * 筛选条件相关
      */
      valueGs: null,
      valueYg: '',
      valueBm: null,
      /**
       * 弹框状态
      */
      isShowLaunchAssessment:false,//确认发起
      launchAssessmentLoading:false,
      launchAssessmentData:{},
      isShowRemoveAssessment:false,//移除
      removeAssessmentData:{},
      isShowDrawer:false,//修改流程
      drawerData:{},
      /**
       * 当前行信息
      */
      examineePlanId:null,//考核对象id
      /**
       * 基础数据
      */
      basicInfo:{},

      /**
       * 全局状态
      */
      planId:null,//考核计划id
      listType:1,//考核计划列表类型
      options: [],//公司选项数据源
      type:null,//考核类型 1:公司考核,2:部门考核,3:个人考核
      status:null,//考核计划 1:未开始,2:进行中,3:已完成
      khlxType:khlxType,//考核类型枚举-辅助
      /**
       * 渲染数据相关
      */
      def_HeaderData:{},
    };
  },
  mounted() {
    console.log(this.$route.query)
    this.handleInit();
    this.handleTableResize();
  },
  methods: {
    handleInit(){
      const { planId } = this.$route.query
      this.planId = planId
      this.handleGetPlanBaseInfo();//考核基本信息
      this.handleGetPlanDetailList();//考核计划详情列表
      // this.handleGetPlanDetail();//考核计划详情
    },
    handleTableResize(){
      window.onresize = () => {
        return (() => {
          this.tableHeight = document.body.clientHeight - 300+'px';
        })();
      };
    },
    //子公司(用工主体)
    async handleGetSubsidiaryList(){
      const { data } = await getSubsidiaryList()
      this.options = data
    },
    //考核基本信息
    async handleGetPlanBaseInfo(){
      let obj = { planId:this.planId }
      const { data } = await getPlanBaseInfo(obj)
      const { name,type,status } = data
      this.def_HeaderData = {
        headerText:`${name}发起考核确认`,
        // headerTag:`${khjhStatus[status]}`
        headerTag:`待确认`
      }
      this.type = type;
      this.status = status;
    },
    //考核计划详情列表
    async handleGetPlanDetailList(){
      let obj = {
        planId:this.planId,//考核计划id
        currentPage:this.page,//当前页
        pageSize:this.limit,//每页大小
        deptId:this.valueBm,//部门id
        employeeName:this.valueYg,//员工名称
        listType:this.listType,//列表类型
        subsidiaryId:this.valueGs,//子公司（用工主体）
        // scoreLevel:"",//等级筛选
      }
      const { data } = await getPlanDetailList(obj)
      const { records,total } = data
      this.tableData = records
      this.total = total
      
      this.loading = false
    },
    async handleGetPlanDetail(){
      const { data } = await getPlanDetail()
      const { basicInfo } = data;
      this.basicInfo = basicInfo
    },
    //格式化table数据
    handleFormatter({prop,data,btnItem},callback){
      let _this = this
      if(prop=="def_cz"){
        switch(btnItem){
          case "def_xglc":
            callback(true)
            break;
          case "def_yc":
            callback(true)
            break;
        }
      }else{
        let val = data[this.tableHeaderEnum[prop]]
        switch(prop){
          case "khdx":
            if(this.type === 1){
              // callback(`${data["subsidiaryName"]}`)
              callback(`${data["examineeName"]}`)
            }else if(this.type === 2){
              // callback(`${data["deptName"]}`)
              callback(`${data["examineeName"]}`)
            }else if(this.type === 3){
              callback(_this.handleKhdxData(data["examineeName"],data["employeeStatus"]))
            }
            break;
          case "glry":
            callback(_this.handleGlryData(val))
            break;
          case "gsmc":
            callback(val)
            break;
          case "bm":
            callback(val)
            break;
          case "qrr":
            callback(_this.handleQrrData(val))
            break;
        }
      }
    },
    //考核对象异常处理
    handleKhdxTip(val,data){
      console.log(data["relations"])
      // if(data["relations"])
    },
    //考核对象是否异常
    handleKhdxData(val,type){
      if([2,3,4,5,6].includes(type)){
        return `<span style="color:red">${val}(${ryztStatus[type]})</span>`
      }else{
        return `${val}`
      }
    },
    //关联人员是否异常
    handleGlryData(list){
      let arr = []
      list.map(v=>{
        if([2,3,4,5,6].includes(v.status)){
           arr.push(`
            <span style="color:red">${v.name}(${ryztStatus[v.status]})</span>
          `)
        }else{
          arr.push(`${v.name}`)
        }
      })
      return arr.join(',')
    },
    //确认人是否异常
    handleQrrData(list){
      let arr = []
      list.map(v=>{
        if([2,3,4,5,6].includes(v.status)){
          arr.push(`
          <span style="color:red">${v.title}:${v.name}(${ryztStatus[v.status]})</span>
        `)
        }else{
          arr.push(`${v.title}:${v.name}`)
        } 
      })
      return arr
    },
    //处理table操作按钮
    handleBtnColumn(val,type){
      switch(type){
        case "handleEditProcess":
          this.examineePlanId = val["examineePlanId"]
          this.drawerData = {
            examineePlanId:this.examineePlanId,
            planId:this.planId,
          };
          this.isShowDrawer = true
          break;
        case "handleRemoveAssessment":
          this.removeAssessmentData = {
            examineeName:val[this.tableHeaderEnum["khdx"]]
          }
          this.examineePlanId = val["examineePlanId"]
          this.isShowRemoveAssessment = true
          break;
      }
    },
    handleSearch({limit,start,page}){
      this.page = page
      this.limit = limit
      this.start = start
      this.handleGetPlanDetailList();//考核计划详情列表
    },
    //发起确认
    handleLaunchAssessment(){
      this.launchAssessmentLoading = true;
      let obj = {
        planId:this.planId
      }
      getPlanStartConfirmCheck(obj).then(res=>{
        const { errorNum,total } = res.data
        this.launchAssessmentData = { errorNum,total }
        this.launchAssessmentLoading = false;
        this.isShowLaunchAssessment = true
      }).catch(err=>{
        this.launchAssessmentLoading = false;
      })
    },
    //发起确认-确定
    handleLaunchAssessmentConfirm(){
      let obj = {
        planId:this.planId,//考核计划id
      }
      getPlanStartConfirm(obj).then(res=>{
        // this.handleInit();
        this.isShowLaunchAssessment = false
        this.$router.push({path: "/launch-assessment/detail",query: {planId:this.planId}})
      }).catch(err=>{
        this.handleInit();
        this.isShowLaunchAssessment = false
      })
    },
    //移除考核对象-确定
    handleRemoveAssessmentConfirm(){
      this.isDialogLoading = true
      let obj = {
        examineePlanId:this.examineePlanId,//考核对象id
        planId:this.planId,//考核计划id
      }
      getExamineePlanRemove(obj).then(res=>{
        this.handleInit();
        this.isShowRemoveAssessment = false
      }).catch(err=>{
        this.handleInit();
        this.isShowRemoveAssessment = false
      }).finally(()=>{
        setTimeout(()=>{
          this.isDialogLoading = false
        },300)
      })
    },
    //关闭抽屉-before
    handleDrawerClose(){
      this.isShowDrawer = false
      this.handleInit()
    },
    //确定表头信息
    hanldeFilterTableHeader(val){
      this.tableHeader = this.tableHeaderBase.filter(v=>{
        if([1,2].includes(val)){
          return ['khdx','glry','qrr','def_cz'].includes(v.prop)
        }
        if([3].includes(val)){
          return ['khdx','gsmc','bm','qrr','def_cz'].includes(v.prop)
        }
      })
    }
  },
  watch:{
    type: {
      handler (val) {
        this.hanldeFilterTableHeader(val)
        if(val === 1){
          this.handleGetSubsidiaryList();//子公司(用工主体)
        }
      },
      deep: true
    },
  }
}
</script>
<style lang='scss' scoped>
.launch-assessment{
  .assessment-drawer{
    font-size: 16px;
    color: #070F29;
    letter-spacing: 0;
    line-height: 16px;
  }
  /deep/.el-drawer__header{
    margin-bottom:0px;
  }
  .section{
    // padding:0 20px;
  }
  .table-header{
    // margin-top:20px;
    display: flex;
    justify-content: space-between;
    .launch-assessment-search{
      display:flex;
    }
  }
  .table-content{
    // margin-top:30px;
  }
}
</style>