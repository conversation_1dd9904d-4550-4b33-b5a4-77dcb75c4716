<template>
  <div class="performance_detail def_per_height" v-if="!loading" v-loading="loading">
    <def-header
      :headerText="def_HeaderData.headerText" 
      :isBack="true"
      :isShowTag="true"
      :headerTag="def_HeaderData.headerTag"
    >
      <div slot="btnArea" v-if="basicInfo.confirmStatus !== 3">
        <el-button @click="isShowDrawer = true" type="primary">修改流程</el-button>
      </div>
    </def-header>
    <section class="def_per_section def_per_section-top">
      <def-card 
        :isShowPhoto="handleIsshowCardPhoto()"
        :cardPhoto="def_CardData.cardPhoto"
        :lineOne="def_CardData.cardDataLineOne"
        :lineTwo="def_CardData.cardDataLineTwo"
        :lineThree="def_CardData.cardDataLineThree"
        :lineFour="def_CardData.cardDataLineFour"
        :isShowRate="false"
        :rate="def_CardData.cardRate"
        :isShowStep="true"
        :steps="def_CardData.cardSteps"
      />

    </section>

    <section class="def_per_section def_per_section-top" v-if="handleSourceSection('考核指标明细')">
      <def-title text="考核指标明细" />
      <section class="detail-table">
        <def-etable 
          :tableHeader="tableHeader_khdxqrxq" 
          :tableData="def_TableData" 
          @formatter="handleFormatterKhdx" 
          @btnColumn="handleBtnColumnKhdx"
          @search="handleSearchKhdx"
          :total="total"
          :isShowIndex="true"
          :def_height="tableHeight"
          :isHidePage="true"
        />
      </section>
    </section>

    <section class="def_per_section def_per_section-top khzbxzjl" v-if="handleSourceSection('考核指标修正记录')&&def_ChangeProcessData.length!==0">
      <def-title text="考核指标修正记录" />
      <section class="khzbxzjl-node">
        <template v-for="(item,index) in def_ChangeProcessData">
          <def-new-node :key="index" style="width:100%" 
            :showLine="index==def_ChangeProcessData.length-1 ? false : true" 
            :showIcon="false"
            :showNode="true"
            margin="0"
            nodeMargin="5px 0"
            :nodeIcon="false"
          >
            <section class="node-right def_per_TopBottom" slot="node-content">
              <section class="node-header">{{handleXzjlTime(item.operateTime)}}</section>
              <section class="def_per_TopBottom" style="margin:10px 0">
                <section class="def_per_leftRight">
                  <def-photo class="" :name="item.operatorName" boxSize="48px" textSize="14px" :isRandomColor="true" />
                  <section class="slot-header">
                    <span class="slot-header-name">{{item.operatorName}}</span>
                    <span class="slot-header-title">{{item.title}}</span>
                    <el-link style="margin-left:5px" type="primary" :underline="false" @click="handleIsShowHide(item,index)">{{item.def_isshow ? '收起':'展开'}}</el-link>
                  </section>
                </section>
                <section>
                  <el-collapse-transition>
                    <div v-show="item.def_isshow" v-html="item.content" style="margin-left: 120px;"></div>
                  </el-collapse-transition>
                </section>
              </section>
            </section>
          </def-new-node>
        </template>
      </section>
    </section>

    <section class="def_per_section def_per_section-top khqrlc" v-if="handleSourceSection('考核确认流程')">
      <def-title text="考核确认流程" />
      <detail-lc 
        :list="def_ConfirmProcessData"
      />
    </section>

    <el-divider></el-divider>

    <section class="detail-btn" v-if="false">
      <el-button>取消</el-button>
      <el-button>暂存</el-button>
      <el-button type="primary">提交考核</el-button>
    </section>

    <!-- 修改流程 -->
    <def-edit-process
      :isShowDrawer="isShowDrawer"
      :data=drawerData
      @def_close="handleDrawerClose"
    />
  </div>
</template>

<script>
import { defHeader,defCard,defNode,defTitle,defTable,defPhoto,defEtable,defNewNode } from 'performance/pages/personalPerformance/components'
import { getMyPlanDetail,getPlanDetail,getExamineePlanDetail } from 'performance/store/api.js'
import { khqrStatus,khlxType,khqrztStatus,khzqPeriodType,jdzxztNodetype,jdtType,zblxType,pffsScoreType,khqrjdztStatus,ryztStatus } from 'performance/utils/enum.js'
import { date2Str } from "performance/utils/util.js";
import defEditProcess from './components/EditProcess'
import detailLc from "performance/pages/personalPerformance/components/compDetail/DetailLc"

export default {
  name:"performance_detail",
  components:{
    defHeader,
    defCard,
    defNode,
    defTitle,
    defTable,
    defPhoto,
    defEtable,
    defNewNode,
    defEditProcess,
    detailLc
  },
  data(){
    return {
      $dayjs:this.$dayjs,
      loading:true,
      type:null,
      /*
        个人考核详情
      */ 
      basicInfo:{},//考核对象基本信息
      indicatorList:[],//考核指标信息
      process:{},//考核对象的流程数据
      resultSetting:{},//考核结果设置
      scoreLevel:"",//总分Level
      totalScore:null,//总分

      

      tableHeight:'auto',
      // tableHeight:document.body.clientHeight - 500 +'px',
      tableHeader:[
        { label: "考核指标名称", prop: "khzbmc" },
        { label: "考核指标说明", prop: "khzbsm",isTip:true },
        { label: "评价标准", prop: "pjbz",isTip:true },
        { label: "评分上限", prop: "pfsx", align:"right"},
        { label: "考核指标权重", prop: "khzbqz" },
        { label: "目标值", prop: "mbz", align:"right" },
        { label: "实际完成值", prop: "sjwcz", align:"right" },
        {
          label: "绩效评分",
          prop: "jxpf",
          children: [
            { label: "评分人", prop: "pfr",type:"addRow",isTip:true },
            { label: "考核指标评分", prop: "khzbpf" ,type:"addRow",align:"right"},
            { label: "考核指标评语", prop: "khzbpy" ,type:"addRow",isTip:true},
          ]
        },
        // { label: "考核指标总分", prop: "khzbzf" },
      ],
      total:null,
      limit:10,
      start:0,
      page:1,
      tableData:[],

      /**
       * 考核对象确认详情
      */
      tableHeader_khdxqrxq:[
        { label: "考核指标名称", prop: "khzbmc" },
        { label: "考核指标类型", prop: "khzblx" },
        { label: "考核指标说明", prop: "khzbsm",isTip:true },
        { label: "评价标准", prop: "pjbz",isTip:true },
        { label: "评分上限", prop: "pfsx",align:"right" },
        { label: "考核指标权重", prop: "khzbqz",align:"right" },
        { label: "目标值", prop: "mbz",align:"right" },
        { label: "评分方式", prop: "pffs",isTip:true },
        { label: "考核指标评分人", prop: "khzbpfr" },
      ],

      khqrjdztStatus:khqrjdztStatus,//考核确认节点状态


      def_HeaderData:{},
      def_CardData:{},
      def_TableData:[],
      def_CommentData:[],
      def_ScoreProcessData:[],
      def_ConfirmProcessData:[],
      def_ChangeProcessData:[],
      def_StartInfo: {},

      /**
       * khqrxq:"考核确认详情"
      */
      source:'khqrxq',
      sourceShowSection:{
        khqrxq:['考核指标明细','考核指标修正记录','考核确认流程']
      },

      def_confirmStatus:{
        1:"待确认",
        2:"未开始",
        3:"确认完成"
      },

      isShowDrawer:false,
      drawerData:{},
      isShowRemoveAssessment:false,
    }
  },
  mounted(){
    this.handleInit();
  },
  methods:{
    handleInit(){
      this.handleGetExamineePlanDetail()
    },
    handleSourceSection(name){
      return this.sourceShowSection[this.source].includes(name)
    },
    //关闭抽屉-before
    handleDrawerClose(){
      this.isShowDrawer = false
      this.handleInit()
    },
    async handleGetExamineePlanDetail(){
      const { examineePlanId,planId } = this.$route.query
      
      let obj = {
        planId,examineePlanId
      }
      if(true){
        const { data } = await getExamineePlanDetail(obj)
        const { basicInfo, indicatorList,process,resultSetting,scoreLevel,totalScore } = data
        this.basicInfo = basicInfo;//考核对象基本信息
        this.indicatorList = indicatorList;//考核指标信息
        this.process = process;//考核对象的流程数据
        this.resultSetting = resultSetting;//考核结果设置
        this.scoreLevel = scoreLevel;//总分Level
        this.totalScore = totalScore;//总分

        this.type = this.basicInfo.type;//考核类型 1:公司考核,2:部门考核,3:个人考核
      }
      this.handleKhdxBase()

      this.loading = false
    },
    handleArrBaseData(list,key){
      // console.log(list,key)
      let arr = []
      list.map(v=>{
        arr.push(v[key])
      })
      console.log(arr)
      return arr
    },
    /**
     * 考核对象确认详情
    */
   //考核对象确认详情
    handleKhdxBase(){
      let isGr = this.type === 3 ? true : false
      const { examineePlanId,planId } = this.$route.query
      this.drawerData = {
        examineePlanId:examineePlanId,
        planId:planId,
        examineeName:this.handleXglcKhdx()
      };
      this.def_HeaderData = {
        headerText:this.handleHeaderName(),
        headerTag:`${khqrStatus[this.basicInfo.confirmStatus]}`
      }
      this.def_CardData = {
        cardPhoto:this.basicInfo.employeeName,
        cardDataLineOne:{
          name:this.handleCardName(),
          // phone:this.basicInfo.mobile ? this.basicInfo.mobile.substr(0, 3) + '****' + this.basicInfo.mobile.substr(7) : this.basicInfo.mobile,
          phone:this.basicInfo.mobile,
          tag:khlxType[this.basicInfo.type],
        },
        cardDataLineTwo:{
          label:isGr ? "用工主体名称:" : "考核关联人员:",
          value:isGr ? this.basicInfo.subsidiaryName : this.handleExamineeRelations(this.basicInfo.examineeRelations)
        },
        cardDataLineThree:{
          label:"考核计划名称:",
          value: this.basicInfo.name
        },
        cardDataLineFour:{
          label:"考核周期:",
          value: date2Str(this.basicInfo.period,this.basicInfo.startDate,this.basicInfo.endDate)
        },
        cardRate:{
          score:this.totalScore,
          grade:this.scoreLevel||"无"
        },
        cardSteps:this.process.statusProcess.map(v=>{return{
          id:v.key,
          state:v.nodeType,
          text: this.handleStepsNodeName(v.nodes, v.nodeType),
          tips: this.handleStepsNodeTips(v.nodes, v.nodeType),
        }})
      }
      this.def_StartInfo = this.process.startInfo; //考核对象各个阶段启动数据
      this.def_TableData = this.indicatorList;//考核指标信息
      this.def_CommentData = this.process.commentProcess;//总评数据
      this.def_ScoreProcessData = this.process.scoreProcess;//评分流程
      this.def_ConfirmProcessData = this.process.confirmProcess;//确认流程
      this.def_ChangeProcessData = JSON.parse(JSON.stringify(this.process.changeProcess))
      this.def_ChangeProcessData.map(v=>{
        v.def_isshow = false
      });//考核指标修正记录

      this.handleAddStartInfo(); //拼接考核确认流程
    },

    //拼接考核确认流程
    handleAddStartInfo() {
      if (Object.keys(this.def_StartInfo).length !== 0) {
        if (this.def_StartInfo["confirmStarterId"] !== 0) {
          this.def_ConfirmProcessData.unshift({
            det_nodeProcessors: true,
            nodeType:3,
            nodeProcessors: [
              {
                processorName: this.def_StartInfo["confirmStarterName"],
                processorText: "发起了考核确认",
                status: 3,
                time:this.$dayjs(this.def_StartInfo["confirmStartTime"]).format('YYYY-MM-DD HH:mm'),
              }
            ]
          });
        }
        if (this.def_StartInfo["scoreStarterId"] !== 0) {
          this.def_ScoreProcessData.unshift({
            det_nodeProcessors: true,
            nodeType:3,
            nodeProcessors: [
              {
                processorName:this.def_StartInfo['scoreStarterName'],
                processorText:"发起了考核评分",
                status:3,
                time:this.$dayjs(this.def_StartInfo["scoreStartTime"]).format('YYYY-MM-DD HH:mm'),
              }
            ]
          });
        }
      }
    },
    handleCardName(){
      const { subsidiaryName,deptName,employeeName } = this.basicInfo
      switch(this.type){
        case 1 :
          return `${subsidiaryName}`
        case 2 :
          return `${deptName}`
        case 3 :
          return `${employeeName}`
      }
    },
    handleXglcKhdx(){
      const { subsidiaryName,deptName,employeeName } = this.basicInfo
      switch(this.type){
        case 1 :
          return `${subsidiaryName}`
        case 2 :
          return `${deptName}`
        case 3 :
          return `${employeeName}`
      }
    },
    handleHeaderName(){
      const { subsidiaryName,deptName,employeeName } = this.basicInfo
      switch(this.type){
        case 1 :
          return `${subsidiaryName}考核详情`
        case 2 :
          return `${deptName}考核详情`
        case 3 :
          return `${employeeName}考核详情`
      }
    },
    //card 进度条文案
    handleStepsNodeName(items, nodeType) {
      let objColor = {
        1: "#4F71FF",
        2: "#BBBBBB",
        3: "#555555"
      };
      let arr = [],
          baseObj={};
      items.map(v => {
        const { key, name, type } = v;
        if(Reflect.ownKeys(baseObj).includes(key)){
          baseObj[key].push(v)
        }else{
          baseObj[key] = [v]
        }
      });
      for (const i in baseObj) {
        let def_key = `<span style="color:${objColor[nodeType]}">${i} </sapn>`;
        let def_addTextArr = [];
        baseObj[i].map(v=>{
          const { key, name, type } = v;
          let def_name = (i && name) ? `<span style="color:${objColor[nodeType]}">：${name} </sapn>` : `<span style="color:${objColor[nodeType]}">${name} </sapn>`;
          let def_type = "";
          if (type === 1) {
            def_type = `<span style="color:#FF9500">${this.handleStepsNodeState(type)}</sapn>`;
          }
          def_addTextArr.push(def_name + def_type)
        })
        let def_text = def_key + def_addTextArr.join(`<span style="color:${objColor[nodeType]}">、</sapn>`)
        arr.push(def_text)
      }
      return arr.join(`<span style="color:${objColor[nodeType]}">，</sapn>`);
    },
    handleStepsNodeTips(items, nodeType){
      let arr = [],
          baseObj={};
      items.map(v => {
        const { key, name, type } = v;
        if(Reflect.ownKeys(baseObj).includes(key)){
          baseObj[key].push(v)
        }else{
          baseObj[key] = [v]
        }
      });
      for (const i in baseObj) {
        let def_key = `${i} `;
        let def_addText = "";
        let def_addTextArr = [];
        baseObj[i].map(v=>{
          const { key, name, type } = v;
          let def_name = (i && name) ? `：${name} `: `${name}`;
          let def_type = "";
          if (type === 1) {
            def_type = `${this.handleStepsNodeState(type)}`;
          }
          def_addTextArr.push(def_name + def_type)
        })
        let def_text = def_key + def_addTextArr.join(`、`)
        arr.push(def_text)
      }
      return arr.join(`、`);
    },
    //进度条状态
    handleStepsNodeState(type){
      return this.def_confirmStatus[type]
    },
    handleStepsNodeTips(items, nodeType){
      let arr = []
      items.map(v => {
        const { key, name, type } = v;
        let def_key = key ? 
          `${key}：` :
          `${key}`
        let def_name = `${name}`;
        let def_type = "";
        if (type === 1) {
          def_type = `${this.handleStepsNodeState(type)}`;
        }
        let def_text = `${def_key} ${def_name} ${def_type}`;
        // val += def_text;
        arr.push(def_text)
      });
      return arr.join(`、`);
    },
    handleFormatterKhdx({prop,data,btnItem},callback){
      switch(prop){
        case "khzbmc":
          callback(data["name"]==null||data["name"]=="" ? '--':data["name"])
          break;
        case "khzblx":
          callback(zblxType[data["type"]]||'--')
          break;
        case "khzbsm":
          callback(data["description"]==null||data["description"]==""?'--':data["description"])
          break;
        case "pjbz":
          callback(data["scoreStandard"]==null||data["scoreStandard"]==""?'--':data["scoreStandard"])
          break;
        case "pfsx":
          callback(data["maxScore"]==null||data["maxScore"]==""?'--':data["maxScore"]+'分')
          break;
        case "khzbqz":
          callback(data["weight"]==null||data["weight"]=="" ? '--' : data["weight"]+"%")
          break;
        case "mbz":
          callback(data["targetValue"]== null||data["targetValue"]== "" ? '--' : data["targetValue"]+data['dataUnit'])
          break;
        case "pffs":
          // callback(pffsScoreType[data["scoreType"]]||'--')
          callback(this.handleTableDataPffs(data));
          break;
        case "khzbpfr":
          // if(data["scoreType"]===2){
          //   callback(`<span style="color:#BBBBBB">系统评分</span>`);
          // }else{
          //   callback(this.handleArrBaseDataKhqr(data["scoreData"],"employeeName"))
          // }
          callback(this.handleArrBaseDataKhqr(data["scoreData"],"employeeName"))
          break;
      }
    },
    handleTableDataPffs(data){
      let text = ""
      if(data.scoreType == 1){
        return '直接输入'
      }
      if(data.scoreType == 2){
        text = '公式计算<br>';
        if(data.dataRuleType == 1){
          text += `按实际完成值来算:<br>`
        }
        if(data.dataRuleType == 2){
          text += `按目标达成率计算:<br>`
        }
        data.dataRuleList.map((v,i)=>{
          if(data.dataRuleType == 1){
            text += `${i+1}、${v.min}${data.dataUnit}`
            if(i!==0){
              text += `<`
            }
            if(i==0){
              text += `≤`
            }
            text += `完成值≤${v.max}${data.dataUnit}`
          }
          if(data.dataRuleType == 2){
            text += `${i+1}、${v.min}%`
            if(i!==0){
              text += `<`
            }
            if(i==0){
              text += `≤`
            }
            text += `目标达成率≤${v.max}%,得分：${v.score}分；`
          }
          text += `<br>`
        })
      }
      return text
    },
    handleBtnColumnKhdx(val,type){
      console.log(val,type)
      switch(type){

      }
    },
    handleSearchKhdx({limit,start,page}){

    },
    handleArrBaseDataKhqr(list,key){
      if(list == null||list.length==0 ){
        return '--'
      }
      let arr = []
      if(key=='employeeName'){
        list.map(v=>{
          if([2,3,4,5,6].includes(v.employeeStatus)){
            arr.push(`
                <span style="color:red">${v["employeeTitle"]}:${v["employeeName"]}(${ryztStatus[v.employeeStatus]})</span>
              `)
          }else{
              arr.push(`${v["employeeName"]}`)
          }
        })
      }
      
      // list.map(v=>{
      //   arr.push(v[key])
      // })
      return arr.join('，')
    },
    //是否展示卡片头像
    handleIsshowCardPhoto(){
      /**
       * 个人考核展示头像
       * 1:公司考核,2:部门考核,3:个人考核
      */
      return this.type === 3 ? true : false
    },
    handleExamineeRelations(list){
      let arr = [];
      list.map(v=>{
        arr.push(v.employeeName)
      })
      return arr.join('，')
    },
    handleXzjlTime(time){
      return this.$dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    handleIsShowHide(item,index){
      this.$set(this.def_ChangeProcessData,index,{
        ...this.def_ChangeProcessData[index],
        def_isshow:!item.def_isshow
      })
    }
  },
  watch:{
    process: {
      handler (val) {
        // console.log(val)
      },
      deep: true
    },
    def_ConfirmProcessData: {
      handler (val) {
        // console.log(val)
      },
      deep: true
    },
  }
}
</script>

<style lang="scss" scoped>
@media screen and (min-width: 1300px) {
  .def_per_section {
    padding:0 100px;
  }
}
.performance_detail{
  /deep/.el-drawer__header{
    margin-bottom: 0px;
  }
  .khzbxzjl{
    .khzbxzjl-node{
      margin-top: 20px;
      .node-header{
        font-size: 14px;
        color: #888888;
        letter-spacing: 0;
        line-height: 14px;
      }
      .slot-header{
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 48px;
        .slot-header-name{
          margin-left:16px;
          font-size: 16px;
          color: #070F29;
          letter-spacing: 0;
          line-height: 14px;
        }
        .slot-header-title{
          margin-left:10px;
          font-size: 16px;
          color: #070F29;
          letter-spacing: 0;
          line-height: 14px;
        }
      }
    }
  }
  .khqrlc{
    .khqr-node{
      margin-top: 20px;
      .node-right{
        .node-right-photo{
          margin:10px 0;
        }
        .def_icon{
          position:absolute;
          bottom: 0px;
          right: -10px;
          width:20px;
          height:20px;
          border-radius: 50%;
          border: 1px solid #fff;
          background: #fff;
        }
        .right-header{
          display: flex;
          flex-direction: row;
          align-items: center;
          height: 48px;
          .right-header-name{
            margin-left:16px;
            font-size: 16px;
            color: #070F29;
            letter-spacing: 0;
            line-height: 14px;
          }
          .right-header-text{
            margin-left:20px;
            font-size: 12px;
            color: #6A6F7F;
            letter-spacing: 0;
            line-height: 14px;
          }
        }
      }
      .slot-header{
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 48px;
        .slot-header-name{
          margin-left:16px;
          font-size: 16px;
          color: #070F29;
          letter-spacing: 0;
          line-height: 14px;
        }
        .slot-header-text{
          margin-left:20px;
          font-size: 12px;
          color: #6A6F7F;
          letter-spacing: 0;
          line-height: 14px;
        }
      }
    }
    
  }
  .detail-table{
    margin-top:20px;
  }
  .detail-evaluate{
    display: flex;
    align-items: center;
    margin-top:20px;
  }
  .evaluate-score{
    margin-left:16px;
    .score-one{
      font-size: 14px;
      color: #888888;
      letter-spacing: 0;
      text-align: right;
      line-height: 14px;
    }
    .score-two,.score-three{
      margin-left: 10px;
      font-size: 14px;
      color: #555555;
      letter-spacing: 0;
      line-height: 14px;
    }
  }
  .evaluate-comment{
    display: flex;
    flex-direction: row;
    margin-top:20px;
    .comment-one{
      margin-left:4px;
      .one-required{
        font-size: 14px;
        color: #FF6051;
        letter-spacing: 0;
        line-height: 14px;
      }
      .one-text{
        margin-left:6px;
        font-size: 14px;
        color: #888888;
        letter-spacing: 0;
        text-align: right;
        line-height: 14px;
      }
    }
    .comment-two{
      margin-left:10px;
      flex-grow: 1;
    }
  }
  .evaluate-node{
    margin-top:20px;
    .node-right{
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .slot-header{
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 48px;
      .slot-header-name{
        margin-left:16px;
        font-size: 16px;
        color: #070F29;
        letter-spacing: 0;
        line-height: 14px;
      }
      .slot-header-text{
        margin-left:20px;
        font-size: 12px;
        color: #6A6F7F;
        letter-spacing: 0;
        line-height: 14px;
      }
    }
    .node-right{
      .node-right-photo{
        margin:10px 0;
      }
      .def_icon{
        position:absolute;
        bottom: 0px;
        right: -10px;
        width:20px;
        height:20px;
        border-radius: 50%;
        border: 1px solid #fff;
        background: #fff;
      }
      .right-header{
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 48px;
        .right-header-name{
          margin-left:16px;
          font-size: 16px;
          color: #070F29;
          letter-spacing: 0;
          line-height: 14px;
        }
        .right-header-text{
          margin-left:20px;
          font-size: 12px;
          color: #6A6F7F;
          letter-spacing: 0;
          line-height: 14px;
        }
      }
    }
    .evaluate-name{
      font-size: 16px;
      color: #070F29;
      letter-spacing: 0;
      line-height: 14px;
      margin-left:16px;
    }
    .evaluate-tag{
      margin-left:10px;
      padding:6px 12px;
      background: #F1F1F1;
      border-radius: 14px;

      font-size: 14px;
      color: #6A6F7F;
      letter-spacing: 0;
      line-height: 14px;
    }
    .evaluate-proportion{
      font-size: 14px;
      color: #6A6F7F;
      letter-spacing: 0;
      line-height: 14px;
      margin-left:10px;
    }
  }
  
  .detail-btn{
    display: flex;
    justify-content: center;
    padding: 0 0 24px 0;
  }
}
</style>