<template>
  <div class="basic">
    <span class="info-title">计划基础信息</span>
    <div class="basic-form" v-loading="loading">
      <el-form :model="form" ref="form" :rules="rules" label-width="170px">
        <el-row>
          <el-col :span="12">
            <el-form-item prop="name" label="考核计划名称">
              <el-input
                class="form-name"
                v-model="form.name"
                placeholder="请输入考核计划名称"
                maxlength="50"
                show-word-limit
                ref="nameInput"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划名称后缀">
              <!-- <span slot="label">
                <span> 计划名称后缀 </span>
                <el-tooltip
                  effect="dark"
                  content="若一次性制定多个考核周期的计划，可通过增加后缀来区分考核计划"
                  placement="top"
                >
                  <i class="iconfont-per icon-help" />
                </el-tooltip>
              </span> -->

              <el-checkbox v-model="form.suffix">考核周期</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="period" label="考核周期类型">
              <el-select
                v-model="form.period"
                placeholder="请选择"
                @change="handleChange"
              >
                <el-option
                  v-for="item in periodType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item prop="startDate" label="考核周期" v-if="type">
              <el-input
                v-model="form.startDate"
                v-show="false"
                :validate-event="false"
              ></el-input>
              <date-picker
                :type="type"
                :dateObj="dateObj"
                @setDate="setDate"
              ></date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="type" label="考核类型">
              <p v-if="disabled">{{ dd.checkType[form.type] }}</p>
              <el-select
                v-else
                v-model="form.type"
                placeholder="请选择考核类型"
                @change="handleTypeChange"
              >
                <el-option
                  v-for="item in assessoType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="temporary" label="考核对象" v-if="form.type">
              <el-select
                multiple
                v-model="form.temporary"
                placeholder="请选择考核对象"
                v-if="form.type == 1"
                @change="handleCompanyChange"
                :validate-event="false"
              >
                <el-option
                  v-for="item in companyList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>

              <el-input
                v-if="form.type == 2 || form.type == 3"
                v-model="form.temporary"
                placeholder="请选择考核对象"
                @focus="handleFocus()"
                :readonly="readonly"
                suffix-icon="el-icon-plus"
                :validate-event="false"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="关联人员"
              v-if="form.type == 1 || form.type == 2"
              prop="tableData"
            >
              <!-- <el-input
                v-model="form.name"
                placeholder="输入考核计划名称"
              ></el-input> -->
              <old-table
                style="width: 300px; line-height: 20px"
                :data="form.tableData"
                :headerData="headerData"
                :isShowOperation="isShowOperation"
                :operaOptions="operaOptions"
                @operaClick="handleOperaClick"
              ></old-table>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="description" label="考核计划说明">
              <el-input
                style="width: 300px"
                :autosize="{ minRows: 4, maxRows: 4 }"
                type="textarea"
                ref="textarea"
                :rows="2"
                maxlength="200"
                show-word-limit
                placeholder="请输入考核计划说明"
                v-model="form.description"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <select-staff
        v-if="showDialogDept"
        :list="section"
        :isUser="isUser"
        :isDifferent="true"
        :select="selectStaffList"
        @close="showDialogDept = false"
        @commit="commit"
      ></select-staff>

      <user-select
        v-if="showDialogPerson"
        :list="section"
        :isUser="isUser"
        :isDifferent="true"
        :select="selectStaffList"
        :userList="userList"
        @close="showDialogPerson = false"
        @commit="commit"
      ></user-select>

      <user-select
        v-if="showDialog1"
        :list="section"
        :select="select1"
        :isUser="false"
        :userList="userList"
        :isDifferent="true"
        @close="showDialog1 = false"
        @commit="commit1"
      ></user-select>

      <!-- <add-department ref="addDepartment"></add-department> -->
    </div>
  </div>
</template>

<script>
import DatePicker from "./components/DatePicker";
import SelectStaff from "./components/SelectStaff";
import UserSelect from "performance/pages/IndicatorsLibrary/components/UserSelect";
import {
  getSubsidiaryList,
  // getEmployeeTree,
  getDepartmentTree,
  getUserList,
  getPlanDetail,
  setBasic,
} from "performance/store/api.js";
import dd from "performance/utils/dataDictionary.js";
import store from "performance/store";

const initData = () => {
  return {
    name: "", //考核计划名称
    period: "", //周期类型
    type: "", //考核类型
    description: null, //说明
    suffix: false, //考核计划名称后缀
    startDate: "", //考核开始日期
    endDate: "", //考核结束日期
    deptList: [], //部门考核对象列表
    employeeList: [], //个人考核对象列表
    subsidiaryList: [], //公司考核对象列表
    temporary: [],
    tableData: [],
    userList: [],
  };
};

export default {
  components: {
    SelectStaff,
    DatePicker,
    UserSelect,
  },
  data() {
    var checkAssociation = (rule, value, callback) => {
      const status = value.some((item) => !item.personList);
      if (status) {
        callback(new Error("请添加关联人员"));
      }
      callback();
    };

    var checkName = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入考核计划名称"));
      } else if (value.length > 50) {
        this.$refs.nameInput.blur();
        callback(new Error("考核计划名称不能多于50字"));
      }
      callback();
    };

    var checkDescription = (rule, value, callback) => {
      if (value && value.length > 200) {
        this.$refs.textarea.blur();
        callback(new Error("考核计划说明不能多于200字"));
      }
      callback();
    };
    return {
      loading: true,
      showDialogDept: false,
      showDialogPerson: false,
      showDialog1: false,
      type: null,
      isUser: false,
      data: [],
      section: [],
      person: [],
      dateObj: {
        startDate: "",
        endDate: "",
      },
      dd,
      readonly: true,
      form: initData(),
      periodType: [
        { label: "年度", value: 1 },
        { label: "半年度", value: 2 },
        { label: "季度", value: 3 },
        { label: "月度", value: 4 },
        { label: "自定义", value: 5 },
      ],
      assessoType: [
        { value: 1, label: "公司考核" },
        { value: 2, label: "部门考核" },
        { value: 3, label: "个人考核" },
      ],
      value: "",
      showTime2: false,
      showTime1a: false,
      year: new Date().getFullYear(),
      fullMonth: ["第一季度", "第二季度", "第三季度", "第四季度"],
      choseQuarter: "",
      headerData: [
        { title: "考核对象", label: "name" },
        { title: "关联人员", label: "person" },
      ],
      isShowOperation: true, //是否显示操作列
      operaOptions: {
        title: "操作", //名称
        // width: 100, //宽度
        // fixed: "right", // right - 固定在右侧
        buttonList: [
          //按钮列表
          { title: "选择人员" },
        ],
      },
      companyList: [
        // { id: "a", name: "A公司" },
        // { id: "b", name: "B公司" },
        // { id: "c", name: "C公司" }
      ], //考核对象公司列表
      value1: [],
      selectStaffList: [],
      selectStaffList1: {},
      currentId: "", //关联人员行id
      select1: [],
      baseInfo: initData(),
      count: 0,
      disabled: false,
      rules: {
        name: [{ required: true, trigger: "change", validator: checkName }],
        period: [
          { required: true, message: "请选择核周期类型", trigger: "change" },
        ],

        type: [{ required: true, message: "考选择核类型", trigger: "change" }],

        startDate: [
          {
            required: true,
            message: "请选择考核周期",
            // trigger: "change"
          },
        ],
        temporary: [
          {
            required: true,
            message: "请选择考核对象",
          },
        ],
        description: [{ trigger: "change", validator: checkDescription }],

        tableData: [
          { required: true, validator: checkAssociation, trigger: "change" },
        ],
      },
    };
  },
  watch: {
    form: {
      handler(val) {
        if (val) {
          if (
            JSON.stringify(val) == JSON.stringify(this.baseInfo) &&
            val.name
          ) {
            this.$emit("getStatus", true);
          } else {
            this.$emit("getStatus", false);
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.getSubsidiaryList();
    // this.getEmployeeTree();
    this.getDepartmentTree();
    this.getUserList();
    console.log("this.$parent.baseId", this.$parent.baseId);

    if (this.$route.query.planId || this.$parent.baseId) {
      // this.getPlanBaseInfo();
      this.getPlanDetail();
    } else {
      this.loading = false;
    }
  },
  methods: {
    setDate(val) {
      this.form.startDate = val.startDate;
      this.form.endDate = val.endDate;
    },

    async getPlanDetail() {
      const res = await getPlanDetail({
        planId: this.$route.query.planId || this.$parent.baseId,
      });
      setTimeout(() => {
        this.loading = false;
      }, 300);

      if (res.success) {
        const data = res.data.basicInfo;
        this.disabled = true;
        for (var k in data) {
          for (var j in this.form) {
            if (k == j) {
              this.form[j] = data[k];
            }
          }
        }
        this.$emit("editName", {
          name: data.name,
          status: dd.affirmType[data.status],
          suffix: data.nameSuffix,
        });

        if (data.suffix) {
          var n = data.name.indexOf("-");
          var m = data.name.substring(0, n);
          this.form.name = m;
        }

        store.commit("SET_BASEINFO", data);
        sessionStorage.setItem("baseInfo", JSON.stringify(data));

        this.type = data.period;
        this.form.tableData = [];
        this.dateObj = {
          startDate: data.startDate,
          endDate: data.endDate,
        };
        if (data.deptList.length) {
          const list = JSON.parse(JSON.stringify(data.deptList));
          this.form.temporary = list.map((it) => it.deptName).join("、");
          list.forEach((item) => {
            this.form.tableData.push({
              personList: item.relatedEmployeeList,
              person: item.relatedEmployeeList
                .map((it) => it.employeeName)
                .join("、"),
              id: item.deptId,
              name: item.deptName,
            });
            console.log(item);
          });

          this.form.tableData.forEach((el) => {
            this.selectStaffList1[el.id] = el.personList.map((it) => {
              it.id = it.employeeId;
              it.name = it.employeeName;
              return it;
            });
          });
          console.log("this.form.tableData>>>>>>>>", this.form.tableData);

          console.log("selectStaffList1", this.selectStaffList1);
          this.selectStaffList = this.form.tableData;
          this.form.deptList = [];
          this.isUser = true;
        }

        if (data.subsidiaryList.length) {
          const list = JSON.parse(JSON.stringify(data.subsidiaryList));
          this.form.temporary = list.map((it) => it.subsidiaryId);

          list.forEach((item) => {
            this.form.tableData.push({
              personList: item.relatedEmployeeList,
              person: item.relatedEmployeeList
                .map((it) => it.employeeName)
                .join("、"),
              id: item.subsidiaryId,
              name: item.subsidiaryName,
            });
            console.log(item);
          });

          this.form.tableData.forEach((el) => {
            this.selectStaffList1[el.id] = el.personList.map((it) => {
              it.id = it.employeeId;
              it.name = it.employeeName;
              return it;
            });
          });
          this.selectStaffList = this.form.tableData;
          this.form.subsidiaryList = [];
        }

        if (data.employeeList.length) {
          const list = JSON.parse(JSON.stringify(data.employeeList));
          this.form.temporary = list.map((it) => it.employeeName).join("、");
          list.forEach((item) => {
            this.form.tableData.push({
              ...item,
              id: item.employeeId,
              subsidiaryId: item.subsidiaryId,
              name: item.employeeName,
            });
            console.log(item);
          });
          this.selectStaffList = this.form.tableData;
          this.form.employeeList = [];
        }

        this.baseInfo = JSON.parse(JSON.stringify(this.form));
        this.baseList = JSON.parse(JSON.stringify(this.form.tableData));
      } else {
        this.$$message.error(res.msg);
      }
    },

    async getUserList() {
      const res = await getUserList();
      if (res.success) {
        this.userList = res.data;
      } else {
        this.$message.error(res.msg);
      }
      console.log(res);
    },

    //获取员工树
    // async getEmployeeTree() {
    //   const res = await getEmployeeTree();
    //   if (res.success) {
    //     this.person = res.data;
    //   } else {
    //     this.$message.error(res.msg);
    //   }
    // },

    //获取部门树
    async getDepartmentTree() {
      const res = await getDepartmentTree();
      if (res.success) {
        this.section = res.data;
      } else {
        this.$message.error(res.msg);
      }
      console.log(res);
    },

    handleCompanyChange(val) {
      console.log("valval", val);
      // 当添加的有重复的时候去掉重复项
      if (this.form.tableData.length > 0) {
        this.form.temporary.forEach((el) => {
          this.form.tableData.forEach((item, index) => {
            if (el == item.id) {
              this.form.tableData.splice(index, 1);
            }
          });
        });
      }

      this.form.temporary.forEach((el) => {
        this.companyList.forEach((item) => {
          if (el == item.id) {
            let obj = {
              name: item.name,
              id: item.id,
            };
            this.form.tableData.push(obj);
            this.form.tableData.forEach((ele) => {
              console.log(ele, this.selectStaffList1);
              if (JSON.stringify(this.selectStaffList1) != "{}") {
                if (this.selectStaffList1[ele.id]) {
                  ele.personList = this.selectStaffList1[ele.id];
                  ele.person = this.selectStaffList1[ele.id]
                    .map((v) => v.name)
                    .join("、");
                }
              }
            });
          }
        });
      });

      let deleteArr = [];
      this.form.tableData.forEach((item) => {
        deleteArr.push(item);
      });

      val.forEach((el) => {
        deleteArr.forEach((item, index) => {
          if (el == item.id) {
            console.log("elelelelle", el);
            deleteArr.splice(index, 1);
          }
        });
      });
      this.form.tableData.forEach((item, index) => {
        if (deleteArr.length == 1) {
          if (item.id == deleteArr[0].id) {
            console.log("item.id ", item.id);
            this.form.tableData.splice(index, 1);
            delete this.selectStaffList1[item.id];
          }
        }
      });
    },
    handleFocus() {
      if (this.form.type == 2) {
        this.showDialogDept = true;
      }
      if (this.form.type == 3) {
        this.showDialogPerson = true;
      }
    },

    handleTypeChange(val) {
      this.form.tableData = [];
      this.selectStaffList = [];
      if (val == 1) {
        this.form.temporary = [];
      }
      if (val == 2) {
        this.form.temporary = "";
        this.isUser = true;
      }
      if (val == 3) {
        this.form.temporary = "";
        this.isUser = false;
      }
      console.log("this.isUse", this.isUser);
      this.$refs["form"].clearValidate(["temporary", "tableData"]);
    },

    //获取选中对象
    commit(val) {
      this.selectStaffList = JSON.parse(JSON.stringify(val));
      const value = JSON.parse(JSON.stringify(val));

      if (value.length) {
        const arr = value.map((it) => it.id);
        for (const key in this.selectStaffList1) {
          console.log(arr.includes(Number(key)), key, arr);
          if (!arr.includes(Number(key))) {
            delete this.selectStaffList1[key];
          }
        }
      }

      console.log("this.selectStaffList", this.selectStaffList);
      console.log("1", this.selectStaffList1);
      // this.value1 = value.map(it => it.name).join("、");
      this.form.temporary = value.map((it) => it.name).join("、");

      this.form.tableData = value;
      this.form.tableData.forEach((item) => {
        if (JSON.stringify(this.selectStaffList1) != "{}") {
          if (this.selectStaffList1[item.id]) {
            item.personList = this.selectStaffList1[item.id];
            item.person = this.selectStaffList1[item.id]
              .map((it) => it.name)
              .join("、");
          }
        }
      });

      console.log("table>>>>>", this.form.tableData);
      this.showDialogPerson = false;
      this.showDialogDept = false;
    },

    //选择关联人员
    commit1(val) {
      const value = JSON.parse(JSON.stringify(val));
      this.form.tableData = this.form.tableData.map((item) => {
        if (item.id == this.currentId) {
          item.person = val.map((it) => it.name).join("、");
          item.personList = value;
          console.log("val>>>>>>>>>>", value);
          this.selectStaffList1[item.id] = value;
        }
        console.log("item", item);
        console.log(">>>>>>>>>>", this.selectStaffList1);
        return item;
      });

      this.showDialog1 = false;
    },

    //考核周期类型选择
    handleChange(val) {
      this.type = val;
      this.form.startDate = "";
      this.$refs["form"].clearValidate(["startDate"]);
    },
    //获取考核类型为公司列表
    async getSubsidiaryList() {
      const res = await getSubsidiaryList();
      if (res.success) {
        this.companyList = res.data || [];
      } else {
        this.$message.error(res.msg);
      }
    },
    //提交
    checkFormData() {
      console.log("basic");
      console.log(this.form);

      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.formatList();
          const planId = this.$route.query.planId || this.$parent.baseId;
          if (planId) {
            this.form.planId = planId;
            if (
              JSON.stringify(this.form.tableData) !=
                JSON.stringify(this.baseInfo.tableData) ||
              this.form.startDate != this.baseInfo.startDate ||
              this.form.endDate != this.baseInfo.endDate
            ) {
              this.$confirm(
                "保存后，所有指标的“关联父考核指标”和“目标值”都将同步被清空，确定修改吗",
                "提示",
                {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  iconClass: "iconfont-per icon-jingshi-qiangtishi1 icon-tishi",
                  closeOnClickModal: false,
                  closeOnPressEscape: false,
                  beforeClose(action, instance, done) {
                    if (action == "confirm") {
                      instance.$refs["confirm"].$el.onclick = function (e) {
                        e = e || window.event;
                        console.log(e.detail);
                        if (e.detail != 0) {
                          done();
                        }
                      };
                    } else {
                      done();
                    }
                  },
                }
              )
                .then(() => {
                  this.setBasic();
                })
                .catch(() => {
                  console.log("取消");
                });
            } else {
              this.setBasic();
            }
          } else {
            this.setBasic();
          }
        } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },
    async setBasic() {
      const { temporary, tableData, ...form } = this.form;
      const res = await setBasic(form);
      console.log(res);
      if (res.success) {
        this.$message({
          message: "保存成功",
          type: "success",
          duration: 1000,
        });
        sessionStorage.setItem("baseId", res.data.planId);
        this.$emit("commit", { done: true });
        this.getPlanDetail();
      } else {
        this.$message.error(res.msg);
      }
    },

    formatList() {
      console.log("_________", this.form.tableData);

      switch (this.form.type) {
        case 1:
          this.form.tableData.forEach((item) => {
            console.log(item);
            const arr = [];
            item.personList.forEach((el) => {
              const it = {
                employeeId: el.employeeId,
                subsidiaryId: el.subsidiaryId,
              };
              arr.push(it);
            });
            const obj = {
              subsidiaryId: item.id,
              relatedEmployeeList: arr,
            };
            this.form.subsidiaryList.push(obj);
          });

          break;

        case 2:
          this.form.tableData.forEach((item) => {
            const arr = [];
            item.personList.forEach((el) => {
              const it = {
                employeeId: el.employeeId,
                subsidiaryId: el.subsidiaryId,
              };
              arr.push(it);
            });
            const obj = {
              deptId: item.id,
              relatedEmployeeList: arr,
            };
            this.form.deptList.push(obj);
          });

          break;

        case 3:
          this.form.tableData.forEach((item) => {
            const obj = {
              employeeId: item.employeeId,
              subsidiaryId: item.subsidiaryId,
            };
            this.form.employeeList.push(obj);
          });
          break;
      }
    },
    handleOperaClick(btn, row) {
      console.log(btn, "调试:", row);
      this.currentId = row.id;
      this.select1 = this.selectStaffList1[this.currentId];
      console.log("this.form.tableData", this.form.tableData);
      console.log("this.select1", this.select1);
      this.showDialog1 = true;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";

.basic {
  width: 1200px;
  margin: 30px auto;
  padding-bottom: 80px;
}
.info-title {
  font-weight: 500;
  font-size: 16px;
  margin: 20px;
  color: #070f29;
  line-height: 18px;
  display: flex;
  align-items: center;
}
.info-title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 14px;
  background-color: $mainColor;
  border-radius: 1px;
  margin-right: 10px;
}

.basic-form {
  .el-form {
    margin-top: 30px;
    .el-input,
    .el-select {
      width: 300px;
    }
    /deep/.el-form-item__content {
      display: flex;
      align-items: center;
    }

    /deep/ .el-textarea__inner {
      padding: 5px 15px 30px;
    }
    /deep/ .el-textarea .el-input__count {
      width: 92%;
      height: 30px;
      bottom: 1px;
      text-align: right;
    }
    /deep/ .el-form-item__label {
      color: #888;
    }
    .form-name {
      /deep/.el-input__inner {
        padding: 0 49px 0 12px;
      }
    }
  }
}
</style>
<style>
.icon-tishi {
  position: absolute;
  top: 13px;
  font-size: 20px !important;
  color: #ff9b0e;
}
</style>
