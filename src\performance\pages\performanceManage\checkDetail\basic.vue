<template>
  <div class="basic">
    <span class="info-title">计划基础信息</span>
    <div class="basic-form" v-loading="loading">
      <el-form :model="form" ref="form" :rules="rules" label-width="170px">
        <el-row>
          <el-col :span="12">
            <el-form-item prop="name" label="考核计划名称">
              <p>{{ form.name }}</p>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划名称后缀">
              <!-- <span slot="label">
                <span> 计划名称后缀 </span>
                <el-tooltip
                  effect="dark"
                  content="若一次性制定多个考核周期的计划，可通过增加后缀来区分考核计划"
                  placement="top"
                >
                  <i class="iconfont-per icon-help" />
                </el-tooltip>
              </span> -->
              <div class="shield"></div>
              <el-checkbox v-model="form.suffix">考核周期</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="period" label="考核周期类型">
              <p>{{ dd.periodType[form.period] }}</p>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item prop="startDate" label="考核周期" v-if="type">
              <p>{{ date2Str(form.period, form.startDate, form.endDate) }}</p>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="type" label="考核类型">
              <p>{{ dd.checkType[form.type] }}</p>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="temporary" label="考核对象" v-if="form.type">
              <p>{{ form.temporary }}</p>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="关联人员"
              v-if="form.type == 1 || form.type == 2"
              prop="tableData"
            >
              <!-- <el-input
                v-model="form.name"
                placeholder="输入考核计划名称"
              ></el-input> -->
              <old-table
                style="width:300px;line-height:20px"
                :data="form.tableData"
                :headerData="headerData"
                :isShowOperation="isShowOperation"
                :operaOptions="operaOptions"
                @operaClick="handleOperaClick"
              ></old-table>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="description" label="考核计划说明">
              <p style="width: 300px">
                {{ form.description ? form.description : "--" }}
              </p>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import {
  getSubsidiaryList,
  setPlanAdd,
  updatePlanAdd,
  getPlanDetail
} from "performance/store/api.js";
import dd from "performance/utils/dataDictionary.js";
import store from "performance/store";
// import { data1, data } from "./components/data";
// console.log(data1.data);
import { date2Str } from "performance/utils/util.js";
export default {
  data() {
    var checkAssociation = (rule, value, callback) => {
      const status = value.some(item => !item.personList);
      if (status) {
        callback(new Error("请添加关联人员"));
      }
      callback();
    };

    var checkName = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入考核计划名称"));
      } else if (value.length > 49) {
        this.$refs.nameInput.blur();
        callback(new Error("考核计划名称不能多于50字"));
      }
      callback();
    };

    var checkDescription = (rule, value, callback) => {
      if (value.length > 199) {
        this.$refs.textarea.blur();
        callback(new Error("考核计划说明不能多于200字"));
      }
      callback();
    };

    return {
      loading: true,
      showDialog: false,
      showDialog1: false,
      type: null,
      dd: dd,
      date2Str: date2Str,
      data: [],
      data1: [],
      session: "", //考核对象
      person: "", //关联人员
      dateObj: {
        startDate: "",
        endDate: ""
      },
      form: {
        name: "", //考核计划名称
        period: "", //周期类型
        type: "", //考核类型
        description: null, //说明
        suffix: false, //考核计划名称后缀
        startDate: "", //考核开始日期
        endDate: "", //考核结束日期
        deptList: [], //部门考核对象列表
        employeeList: [], //个人考核对象列表
        subsidiaryList: [], //公司考核对象列表
        temporary: [],
        tableData: []
      },

      value: "",
      showTime2: false,
      showTime1a: false,
      year: new Date().getFullYear(),
      fullMonth: ["第一季度", "第二季度", "第三季度", "第四季度"],
      choseQuarter: "",
      headerData: [
        { title: "考核对象", label: "name" },
        { title: "关联人员", label: "person" }
      ],
      isShowOperation: false, //是否显示操作列
      operaOptions: {
        title: "操作", //名称
        // width: 100, //宽度
        // fixed: "right", // right - 固定在右侧
        buttonList: [
          //按钮列表
          { title: "选择人员" }
        ]
      },
      companyList: [
        // { id: "a", name: "A公司" },
        // { id: "b", name: "B公司" },
        // { id: "c", name: "C公司" }
      ], //考核对象公司列表
      value1: [],
      selectStaffList: [],
      selectStaffList1: {},
      currentId: "", //关联人员行id
      select1: [],
      count: 0,
      //
      rules: {
        name: [{ required: true, trigger: "change", validator: checkName }],
        period: [
          { required: true, message: "请选择核周期类型", trigger: "change" }
        ],

        type: [{ required: true, message: "考选择核类型", trigger: "change" }],

        startDate: [
          { required: true, message: "考选择核周期", trigger: "change" }
        ],
        temporary: [
          { required: true, message: "请选择考核对象", trigger: "change" }
        ],
        description: [{ trigger: "change", validator: checkDescription }],

        tableData: [
          { required: true, validator: checkAssociation, trigger: "change" }
        ]
      }
    };
  },

  mounted() {
    this.getSubsidiaryList();
    if (this.$route.query.planId || this.$parent.baseId) {
      // this.getPlanBaseInfo();
      this.getPlanDetail();
    } else {
      this.loading = false;
    }
  },
  methods: {
    setDate(val) {
      console.log(">>>>>", val);
      this.form.startDate = val.startDate;
      this.form.endDate = val.endDate;
    },

    async getPlanDetail() {
      const res = await getPlanDetail({
        planId: this.$route.query.planId || this.$parent.baseId
      });
      console.log("getPlanDetail", res);

      setTimeout(() => {
        this.loading = false;
      }, 300);

      if (res.success) {
        const data = res.data.basicInfo;
        for (var k in data) {
          for (var j in this.form) {
            if (k == j) {
              this.form[j] = data[k];
            }
          }
        }
        this.$emit("editName", {
          name: data.name,
          status: dd.affirmType[data.status],
          suffix: data.nameSuffix
        });

        store.commit("SET_BASEINFO", data);

        if (data.suffix) {
          var n = data.name.indexOf("-");
          var m = data.name.substring(0, n);
          this.form.name = m;
        }

        this.type = data.period;
        this.form.tableData = [];
        this.dateObj = {
          startDate: data.startDate,
          endDate: data.endDate
        };
        if (this.form.type == 2) {
          this.data = this.section;
        }
        if (this.form.type == 3) {
          this.data = this.person;
        }

        if (data.deptList.length) {
          const list = JSON.parse(JSON.stringify(data.deptList));
          this.form.temporary = list.map(it => it.deptName).join("、");
          list.forEach(item => {
            this.form.tableData.push({
              personList: item.relatedEmployeeList,
              person: item.relatedEmployeeList
                .map(it => it.employeeName)
                .join("、"),
              id: item.deptId,
              name: item.deptName
            });
            console.log(item);
          });

          this.form.tableData.forEach(el => {
            this.selectStaffList1[el.id] = el.personList.map(it => {
              it.id = it.employeeId;
              it.name = it.employeeName;
              return it;
            });
          });
          console.log("this.form.tableData>>>>>>>>", this.form.tableData);

          console.log("selectStaffList1", this.selectStaffList1);
          this.selectStaffList = this.form.tableData;
          this.form.deptList = [];
        }

        if (data.subsidiaryList.length) {
          const list = JSON.parse(JSON.stringify(data.subsidiaryList));
          this.form.temporary = list.map(it => it.subsidiaryId);
          const arr = [];
          this.companyList.forEach(item => {
            this.form.temporary.forEach(it => {
              if (item.id == it) {
                arr.push(item.name);
              }
            });
          });
          this.form.temporary = arr.join("，");

          list.forEach(item => {
            this.form.tableData.push({
              personList: item.relatedEmployeeList,
              person: item.relatedEmployeeList
                .map(it => it.employeeName)
                .join("、"),
              id: item.subsidiaryId,
              name: item.subsidiaryName
            });
            console.log(item);
          });

          this.form.tableData.forEach(el => {
            this.selectStaffList1[el.id] = el.personList.map(it => {
              it.id = it.employeeId;
              it.name = it.employeeName;
              return it;
            });
          });
          this.selectStaffList = this.form.tableData;
          this.form.subsidiaryList = [];
        }

        if (data.employeeList.length) {
          const list = JSON.parse(JSON.stringify(data.employeeList));
          this.form.temporary = list.map(it => it.employeeName).join("、");
          list.forEach(item => {
            this.form.tableData.push({
              id: item.employeeId,
              subsidiaryId: item.subsidiaryId,
              name: item.employeeName
            });
            console.log(item);
          });
          this.selectStaffList = this.form.tableData;
          this.form.employeeList = [];
        }

        this.baseInfo = JSON.parse(JSON.stringify(this.form));
        this.baseList = JSON.parse(JSON.stringify(this.form.tableData));
      } else {
        this.$$message.error(res.msg);
      }
    },

    //获取考核类型为公司列表
    async getSubsidiaryList() {
      const res = await getSubsidiaryList();
      if (res.success) {
        this.companyList = res.data || [];
      } else {
        this.$message.error(res.msg);
      }
    },

    handleOperaClick(btn, row) {
      console.log(btn, "调试:", row);
      this.currentId = row.id;
      // this.data1 = data.data;
      this.data1 = this.person;
      this.select1 = this.selectStaffList1[this.currentId];
      console.log("this.form.tableData", this.form.tableData);
      console.log("this.select1", this.select1);
      this.showDialog1 = true;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";

.basic {
  width: 1200px;
  margin: 30px auto;
}
.info-title {
  font-weight: 500;
  font-size: 16px;
  margin: 20px;
  color: #070f29;
  line-height: 18px;
  display: flex;
  align-items: center;
}
.info-title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 14px;
  background-color: $mainColor;
  border-radius: 1px;
  margin-right: 10px;
}

.basic-form {
  .el-form {
    margin-top: 30px;
    .el-input,
    .el-select {
      width: 300px;
    }
    /deep/.el-form-item__content {
      display: flex;
      align-items: center;
    }
    /deep/ .el-checkbox__input.is-checked .el-checkbox__inner {
      border-color: #ccc;
      background-color: #ccc;
    }
    .shield {
      width: 100px;
      height: 50px;
      position: absolute;
      z-index: 9;
    }
    p {
      color: #555;
      font-size: 14px;
    }
    /deep/ .el-form-item__label {
      color: #888;
    }
    /deep/ .el-checkbox__label {
      font-weight: 400;
    }
  }
}
</style>
