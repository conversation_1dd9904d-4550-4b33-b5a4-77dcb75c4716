<template>
  <div class="flow" v-loading="loading">
    <span class="info-title">考核确认流程</span>
    <p class="tip">
      <i class="iconfont-per icon-shujuyichang"></i>
      发起考核确认后，可安排指定人员对考核表进行修改确认
    </p>
    <el-form :model="form" label-width="110px">
      <el-form-item
        prop="name"
        label="确认节点"
        :rules="{
          required: true
        }"
      >
        <div class="sort-box">
          <el-row v-for="(item, index) in formData.confirmProcess" :key="index">
            <div class="sort-item">
              <div class="left">
                <div
                  :class="{
                    'no-setp': index == formData.confirmProcess.length - 1
                  }"
                  class="setp"
                ></div>
              </div>
              <div class="right tl">
                <h4>
                  确认人{{ index + 1 }}
                  <!-- <i
                    class="iconfont-per icon-bianji icon-blue"
                    @click="handleAddAffirm('confirmProcess', item, index)"
                  ></i>
                  <i
                    class="iconfont-per icon-shanchu icon-blue"
                    @click="delConfirmItem('confirmProcess', index)"
                  ></i> -->
                </h4>
                <div class="name">
                  <img
                    v-if="item.processorType != 3"
                    :src="avatar"
                    alt=""
                  />
                  <el-avatar
                    v-else
                    :class="colorList[parseInt(Math.random() * 4)]"
                    :key="index"
                    >{{ item.name.substr(-2) }}</el-avatar
                  >
                  <span style="margin-left:10px">{{ item.name }}</span>
                </div>
              </div>
            </div>
          </el-row>
        </div>
      </el-form-item>
    </el-form>

    <span class="info-title">考核评分流程</span>
    <p class="tip">
      <i class="iconfont-per icon-shujuyichang"></i>
      设置本次考核流程的评分人及其相应评分规则
    </p>
    <div class="sort-item-page">
      <div class="sort-item">
        <div class="person-info">
          <el-row style="width:100%">
            <el-col :span="3" class="item-icon tc"> 评分节点</el-col>
            <el-col :span="4" class="item-icon">
              评分人
            </el-col>
            <el-col :span="4">
              评分权重
              <el-tooltip
                effect="dark"
                content="指定指标评分人没有评分权重，不能设置"
                placement="top"
              >
                <i class="iconfont-per icon-help"></i>
              </el-tooltip>
            </el-col>
            <el-col :span="6" style="display: flex;">
              评分方式
            </el-col>
            <el-col :span="6">
              可见内容
            </el-col>
            <!-- <el-col :span="4" class="operation-btn">
              操作
            </el-col> -->
          </el-row>
        </div>

        <el-row
          v-for="(el, index) in formData.scoreProcess"
          :key="index"
          style="margin-bottom:10px;"
        >
          <div v-if="Array.isArray(el)">
            <div v-for="(it, i) in el" :key="i">
              <el-col :span="3" class="item-icon tc ">
                <!-- <div :class="{ 'none-step': index != 0 }" class="setp"></div> -->
                <div
                  class="setp"
                  :class="{
                    'no-setp': index == formData.scoreProcess.length - 1,
                    'no-setp1': i != 0
                  }"
                ></div>
              </el-col>
              <el-col :span="4" class="item-icon name">
                <img
                  v-if="it.processorType != 3"
                  :src="avatar"
                  alt=""
                />

                <el-avatar v-else :class="it.bgColor" :key="i">{{
                  it.name.substr(-2)
                }}</el-avatar>

                <el-tooltip
                  v-if="it.name.length > 4"
                  :content="it.name"
                  placement="right"
                  effect="light"
                >
                  <span style="margin-left:10px">{{
                    it.name.substr(0, 4) + "..."
                  }}</span>
                </el-tooltip>
                <span v-else style="margin-left:10px">{{ it.name }}</span>
              </el-col>
              <el-col :span="4">
                <!-- 权重 -->
                <div class="grid-content bg-purple">
                  {{ it.indicatorRange == 2 ? it.weight + "%" : "--" }}
                </div>
              </el-col>
              <!-- 评分方式-->
              <el-col :span="6" style="display: flex;">
                {{ indicatorRangeType[it.indicatorRange] }}
              </el-col>
              <el-col :span="6">
                <!-- 可见内容 -->
                <div class="grid-content bg-purple-light">
                  {{ visibleStatus[it.visibleType] }}
                </div>
              </el-col>

              <!-- <el-col :span="4" class="operation-btn"> </el-col> -->
            </div>
          </div>
          <div v-else>
            <el-col :span="3" class="item-icon tc ">
              <!-- <div :class="{ 'none-step': index != 0 }" class="setp"></div> -->
              <div
                :class="{
                  'no-setp': index == formData.scoreProcess.length - 1
                }"
                class="setp"
              ></div>
            </el-col>
            <el-col :span="4" class="item-icon name">
              <!-- 评分人 -->
              <!-- <el-avatar
                :class="colorList[parseInt(Math.random() * 4)]"
                :key="index"
                >{{ el.name.substring(0, 2) }}</el-avatar
              > -->

              <img
                v-if="el.processorType != 3"
                :src="avatar"
                alt=""
              />
              <el-avatar
                v-else
                :class="colorList[parseInt(Math.random() * 4)]"
                :key="index"
                >{{ el.name.substr(-2) }}</el-avatar
              >
                 <el-tooltip
                  v-if="el.name.length > 4"
                  :content="el.name"
                  placement="right"
                  effect="light"
                >
                  <span style="margin-left:10px">{{
                    el.name.substr(0, 4) + "..."
                  }}</span>
                </el-tooltip>
                <span v-else style="margin-left:10px">{{ el.name }}</span>
          
            </el-col>
            <el-col :span="4">
              <!-- 权重 -->
              <div class="grid-content bg-purple">
                {{ el.indicatorRange == 2 ? el.weight + "%" : "--" }}
              </div>
            </el-col>
            <!-- 评分方式-->
            <el-col :span="6" style="display: flex;">
              {{ indicatorRangeType[el.indicatorRange] }}
            </el-col>
            <el-col :span="6">
              <!-- 可见内容 -->
              <div class="grid-content bg-purple-light">
                {{ visibleStatus[el.visibleType] }}
              </div>
            </el-col>
          </div>
        </el-row>    
      </div>
    </div>
    <span class="info-title">结果审核流程</span>
    <p class="tip">
      <i class="iconfont-per icon-shujuyichang"></i>
      发放考核结果后，可安排指定人员对考核结果进行审核
    </p>
    <el-form :model="form" label-width="110px">
      <el-form-item
        prop="name"
        label="审核节点"
        :rules="{
          required: true
        }"
      >
        <div class="sort-box">
          <el-row v-for="(item, index) in formData.approveProcess" :key="index">
            <div class="sort-item">
              <div class="left">
                <div
                  :class="{
                    'no-setp': index == formData.approveProcess.length - 1
                  }"
                  class="setp"
                ></div>
          
              </div>
              <div class="right tl">
                <h4>
                  审核人{{ index + 1 }}
                </h4>
                <div class="name">
                  <img
                    v-if="item.processorType != 3"
                    :src="avatar"
                    alt=""
                  />
                  <el-avatar
                    v-else
                    :class="colorList[parseInt(Math.random() * 4)]"
                    :key="index"
                    >{{ item.name.substr(-2) }}</el-avatar
                  >
                  <span style="margin-left:10px">{{ item.name }}</span>
                </div>
              </div>
            </div>
          </el-row>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { setPlanProcess, getPlanDetail } from "performance/store/api.js";
import dd from "performance/utils/dataDictionary.js";
function sumCount(arr) {
  return arr.reduce(function(prev, curr, idx, arr) {
    return prev + curr;
  });
}

export default {
  props: {},

  data() {
    return {
      loading: true,
      tableData: [],
      count: 0,
      colorList: ["blue", "green", "orange", "purple"],
      type: null,
      avatar:
        window.env.server_env === "boc" || window.env.server_env === "cgb"
          ? require("../../../images/order-avatar.png")
          : require("../../../images/defaultAvatar.png"),
      form: {
        name: "" //考核计划名称
      },
      confirmIndex: null, //考核确认当前节点
      scoreIndex: null,
      scoreChlidIndex: null,
      editScoreData: {},
      scoreProcessorType: null,
      total: 0,
      scoreType: null, //评分节点类型
      editData: {},
      isReferrer: false, //是否有被考核人
      scoreProcess: [
        //临时评分流程列表
        [
          {
            nodeSort: 1,
            processorId: null,
            processorType: 2,
            superiorLevel: "",
            weight: 0,
            visibleType: 1,
            name: "被考核人"
          }
          // {
          //   nodeSort: 1,
          //   processorId: 2,
          //   processorType: "",
          //   superiorLevel: "",
          //   name: "明贺"
          // },
          // {
          //   nodeSort: 1,
          //   processorId: 4,
          //   processorType: "",
          //   superiorLevel: "",
          //   name: "JJ"
          // }
        ],
        [
          {
            nodeSort: 2,
            processorId: 3,
            processorType: 2,
            superiorLevel: 1,
            visibleType: 1,
            weight: 100,
            name: "直接上级"
          }
        ]
      ],
      visibleStatus: {
        2: "所有人的评分/评语",
        1: "仅自己的评分/评语"
      },
      levelType: dd.levelType,
      indicatorRangeType: {
        1: "对指定指标评分",
        2: "对所有考核指标评分"
      },
      status: false, //是否全部指定了评分人
      formData: {
        confirmProcess: [],
        scoreProcess: [],
        approveProcess: []
      },

      showAffirm: false,
      showScore: false
    };
  },
  computed: {
    ...mapState("salaryCalStore", {
      rouleId: "rouleId",
      sendBasicInfoForm: "basicInfoForm"
    })
  },
  watch: {
    "formData.scoreProcess": {
      immediate: true,
      handler: function(val) {
        if (val.length) {
          this.isReferrer = val.some(it => it.processorType == 1);
          console.log("this.referrer", this.isReferrer);
          this.total = sumCount(val.map(it => Number(it.weight)));
        }
        // val.forEach(item => {
        //   console.log("el>>", item.processorType);
        //   if (item.processorType == 1) {
        //     this.isReferrer = true;
        //   } else {
        //     this.isReferrer = false;
        //   }
        // });
      },
      deep: true
    },
    formData: {
      handler: function(val) {
        if (val) {
          if (JSON.stringify(val) == JSON.stringify(this.baseInfo)) {
            this.$emit("getStatus", true);
          } else {
            this.$emit("getStatus", false);
          }
        }
        this.count++;
      },
      deep: true
    }
    // count(val) {
    //   if (val > 1) {
    //     this.$emit("getStatus", false);
    //   } else {
    //     this.$emit("getStatus", true);
    //   }
    //   console.log("count", val);
    // }
  },

  created() {
    if (this.$route.query.planId || this.$parent.baseId) {
      this.getPlanDetail();
    }
  },
  methods: {
    async getPlanDetail() {
      const res = await getPlanDetail({
        planId: this.$route.query.planId || this.$parent.baseId
      });
      console.log("getPlanDetail", res);
      setTimeout(() => {
        this.loading = false;
      }, 300);

      if (res.success) {
        const planBaseInfo = res.data.basicInfo;
        const indicatorList = res.data.indicatorList;

        const obj = res.data.process;
        Object.keys(obj).forEach(key => {
          console.log(obj[key]); // foo
          obj[key].map(item => {
            item.bgColor = this.colorList[parseInt(Math.random() * 4)];
            switch (item.processorType) {
              case 1:
                item.name = "被考核人";
                break;
              case 2:
                item.name = this.levelType[item.superiorLevel];
                break;
              case 3:
                item.name = item.processorName;
                break;
            }
            return item;
          });
        });
        let arr = [];
        obj.scoreProcess.forEach((it, index) => {
          if (it.indicatorRange == 1) {
            console.log("index", index, it.name);
            arr.push(it);
          }
        });
        if (arr.length > 0) {
          obj.scoreProcess.push(arr);
        }
        obj.scoreProcess = obj.scoreProcess.filter(
          item => item.indicatorRange != 1
        );
        if (obj.scoreProcess.length > 1) {
          obj.scoreProcess = obj.scoreProcess.sort(function(a, b) {
            return (
              (Array.isArray(a) ? a[0].nodeSort : a.nodeSort) -
              (Array.isArray(b) ? b[0].nodeSort : b.nodeSort)
            );
          });
        }

        this.formData = obj;
        console.log("planBaseInfo.type", planBaseInfo.type);

        this.baseInfo = JSON.parse(JSON.stringify(this.formData));
      } else {
        this.$$message.error(res.msg);
      }
    },

    //新增节点
    handleAddAffirm(type, val, index) {
      if (val) {
        this.editData = val;
        this.confirmIndex = index;
      }
      this.type = type;
      this.$refs.refAffirm.openDialog();
    },
    //新增评分节点
    handleAddScoreNode(type, t) {
      console.log("type", type);
      this.scoreProcessorType = type;
      // this.scoreIndex = parent;
      this.scoreType = t;
      this.$refs.refScore.openDialog();
    },

    //编辑评分流程
    handleScoreEdit(el, index, type) {
      console.log(el, index);
      this.scoreChlidIndex = index;
      this.scoreType = type;
      this.editScoreData = el;
      this.$refs.refScore.openDialog();
    },

    getItem(val) {
      console.log("this.confirmIndex", this.confirmIndex);
      if (this.confirmIndex != null && this.confirmIndex >= 0) {
        this.formData[this.type].splice(this.confirmIndex, 1, val);
      } else {
        this.formData[this.type].push(val);
      }
      this.confirmIndex = null;
      this.type = null;
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.tc {
  text-align: center;
}
.tl {
  text-align: left;
}

.tr {
  text-align: right;
}
.flow {
  width: 1200px;
  margin: 30px auto;
}

.info-title {
  font-weight: 500;
  font-size: 16px;
  margin: 20px;
  color: #070f29;
  line-height: 18px;
  display: flex;
  align-items: center;
}
.info-title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 14px;
  background-color: $mainColor;
  border-radius: 1px;
  margin-right: 10px;
}
.tip {
  margin-left: 30px;
  margin-bottom: 20px;
  color: #6a6f7f;
  display: flex;
  align-items: center;
  font-size: 14px;
  .icon-shujuyichang {
    color: #9ea5bd;
    font-size: 16px;
    margin-right: 10px;
  }
}
.icon-help {
  color: #9ea5bd;
}

.setp {
  width: 40px;
}
.setp:before {
  position: absolute;
  left: 40%;
  display: block;
  content: "";
  top: 32px;
  z-index: 1;
  width: 1px;
  height: 100%;
  background-color: #e1e6eb;
  transform: translateX(-50%);
}

.setp:after {
  position: absolute;
  left: 40%;
  display: block;
  content: "";
  top: 50%;
  z-index: 2;
  width: 13px;
  height: 13px;
  background-color: #fff;
  border: 1px solid #4f71ff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.none-step.setp:after {
  display: none;
}
.none-step.setp:before {
  height: 120%;
}
img {
  width: 48px;
  height: 48px;
}

.sort-box {
  width: 600px;
  .sort-item {
    display: flex;
    .left {
      position: relative;
      height: 95px;
      .setp:before {
        position: absolute;
        left: 0%;
        top: 25px;
      }
      .setp:after {
        position: absolute;
        left: 0%;
        top: 22%;
      }
      .icon-paixu {
        color: #d1d5dc;
        position: relative;
        left: 13px;
      }
      .no-setp.setp:before {
        display: none;
      }
    }
    .right {
      h4 {
        color: #888;
      }
      .icon-blue {
        color: $mainColor;
      }

      .name {
        display: flex;
        align-items: center;
      }
    }
  }
  .add-item {
    .setp:before {
      display: none;
    }
  }
}

.sort-item-page {
  padding: 0 30px;
  box-sizing: border-box;
  overflow: auto;
  .draggable-container {
    .el-row:nth-of-type(even) {
      background: rgba(245, 246, 247, 0.3);
    }
  }
  .sort-item {
    padding-bottom: 10px;
    margin-bottom: 20px;
    border: 1px solid #f5f6f7;

    .no-setp1.setp:after {
      display: none;
    }
    // border-bottom: none;
    .no-setp.setp:before {
      display: none;
    }
  }
  .last-item {
    border-bottom: 1px solid #e8eaf3;
  }

  .name {
    display: flex;
    align-items: center;
  }
  .add-item_box {
    .add {
      width: 48px;
      height: 48px;
      padding: 0;
      border: none !important;
      &:hover {
        background-color: #fff;
      }
    }
  }

  .setp:before {
    position: absolute;
    left: 40%;
    display: block;
    content: "";
    top: 32px;
    z-index: 1;
    width: 1px;
    height: 120%;
    background-color: #e1e6eb;
    transform: translateX(-50%);
  }
  .add-all {
    .setp:before {
      display: none;
    }
  }

  .person-info {
    height: 40px;
    line-height: 40px;
    background: #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .title {
      display: inline-block;
      margin-left: 20px;
    }
  }

  .el-col {
    height: 50px;
    line-height: 50px;
  }
  .item-icon {
    padding-left: 20px;
    padding-right: 40px;
    position: relative;
  }

  .icon-paixu {
    color: #d1d5dc;
    position: relative;
    left: 20px;
  }

  .operation-btn {
    text-align: left;
    padding-right: 50px;
  }
  .el-icon-sort:before {
    color: #dddbdb;
  }

  .nameStyle {
    color: #4f71ff;
  }
  /deep/ .el-dropdown {
    color: #999;
    font-size: 18px;
  }
  .total {
    color: #ff9500;
    font-size: 14px;
    &.warning {
      color: #d6342a;
    }
  }
}

/deep/ .el-dropdown-menu__item {
  font-size: 12px !important;
}

/deep/ .el-avatar {
  width: 48px;
  height: 48px;
  line-height: 48px;
}

.blue {
  background: linear-gradient(122deg, #5486ff 0%, #4f71ff 100%);
}
.green {
  background: linear-gradient(134deg, #41ddb6 0%, #2bcda4 100%);
}
.orange {
  background: linear-gradient(135deg, #ffbc14 0%, #ff8300 100%);
}
.purple {
  background: linear-gradient(-44deg, #8b5feb 0%, #b095fe 100%);
}
</style>
