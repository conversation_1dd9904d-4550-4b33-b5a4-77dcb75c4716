<template>
  <div class="setting">
    <header class="setting-header setting-title">
      <div class="back">
        <span @click="goBack" class="back-style">
          返回
        </span>
        <span class="header-line">|</span>
        <el-tooltip
           v-if="baseInfo"
            placement="bottom"
            style="cursor:pointer"
          >
            <span slot="content">
               <span>
            {{ baseInfo.name }}
            <span v-if="baseInfo.suffix">-{{ baseInfo.suffix }}</span>
            <span class="status"> {{ baseInfo.status }}</span></span
          >
            </span>
            <span class="text">
            {{
              baseInfo.name.length > 10
                ? baseInfo.name.substr(0, 10) + "..."
                : baseInfo.name
            }}
            <span class="status"> {{ baseInfo.status }}</span></span>
          </el-tooltip>
        <span v-else>新增考核计划</span>
      </div>

      <el-tabs class="setting-tabs" v-model="planName">
        <template v-for="item in tabs">
          <el-tab-pane :key="item.name" :label="item.label" :name="item.name">
          </el-tab-pane>
        </template>
      </el-tabs>
    </header>
    <div class="content">
      <basic
        v-if="planName == 'BASIC'"
        ref="refBasic"
        @commit="commit"
        @editName="editName"
        @getStatus="getStatus"
      ></basic>
      <indicator
        v-if="planName == 'INDICATOR'"
        ref="refIndicator"
        @commit="commit"
        @getStatus="getStatus"
      ></indicator>
      <result
        v-if="planName == 'RESULT'"
        ref="refResult"
        @commit="commit"
        @getStatus="getStatus"
      >
      </result>
      <flow
        v-if="planName == 'FLOW'"
        ref="refFlow"
        @commit="commit"
        @getStatus="getStatus"
      ></flow>
    </div>
  </div>
</template>

<script>
import basic from "./basic.vue";
import indicator from "./indicator.vue";
import result from "./result";
import flow from "./flow";

export default {
  components: {
    basic,
    indicator,
    result,
    flow
  },
  data() {
    return {
      baseInfo: null,
      planName: "BASIC",
      loading: false,
      planId: null,
      baseId: null,
      done: false,
      planInfo: {},
      tabs: [
        { label: "基础设置", name: "BASIC" },
        { label: "考核指标设置", name: "INDICATOR" },
        { label: "流程设置", name: "FLOW" },
        { label: "结果设置", name: "RESULT" }
      ]
    };
  },
  watch: {
    planName(val) {
      console.log("watch", val);
      this.done = false;
    }
  },

  mounted() {
    this.planId = this.$route.query.planId;
  },
  methods: {
    commit(val) {
      if (val.id) {
        this.baseId = val.id;
      }
      this.done = val.done;
      console.log(this.done);
    },
    add() {
      this.$router.push("/performance/add");
    },

    goBack() {
      this.$router.go(-1);
    },
    getStatus(val) {
      this.done = val;
    },
    editName(val) {
      this.baseInfo = val;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.setting {
  box-sizing: border-box;
  position: relative;
}

.setting-header {
  margin: 0 20px;
  font-size: 16px;
  height: 61px;
  border-bottom: 1px solid #ededed;
  line-height: 61px;
  position: relative;
  .back {
    position: absolute;
    top: 10px;
  }
  .status {
    background: #f1f1f1;
    color: #6a6f7f;
    padding: 4px 12px;
    font-size: 14px;
    margin-left: 10px;
    border-radius: 14px;
  }
  .progress {
    background: #fff2e5;
    color: #ff9500;
  }
  .finish {
    background: #e6f8ea;
    color: #41bd5a;
  }
}

.setting-title {
  box-sizing: border-box;
  padding-top: 16px;
  height: 61px;
  line-height: 45px;
  display: flex;
  justify-content: space-between;
}

.setting-tabs {
  margin: 0 auto;
  /deep/.el-tabs__nav-wrap::after {
    display: none;
  }
}

.content {
  padding: 0 20px;
  height: calc(100vh - 160px);
  overflow: auto;
}
</style>

<style>
.el-tabs__item {
  font-size: 16px;
  padding: 0 20px;
  color: #555555;
}
.el-tabs__active-bar {
  height: 3px;
}
</style>
