<template>
  <div class="indicator" v-loading="loading">
    <div class="indicator-table" v-if="tableData.length">
      <old-table
        :data="tableData"
        :headerData="headerData"
        :isShowOperation="isShowOperation"
        :operaOptions="operaOptions"
        @operaClick="handleOperaClick"
      >
        <template slot="descriptionStr" slot-scope="scope">
          <el-tooltip
            placement="top"
            :disabled="scope.msg.row.descriptionStr.length < 50"
          >
            <div slot="content">
              {{ scope.msg.row.descriptionStr }}
            </div>
            <p class="text">{{ scope.msg.row.descriptionStr }}</p>
          </el-tooltip>
        </template>

        <template slot="scoreStandard" slot-scope="scope">
          <!-- <el-popover
            placement="top-start"
            trigger="hover"
            width="200"
            :disabled="scope.msg.row.scoreStandard.length < 50"
            :content="scope.msg.row.scoreStandard"
          >
            <p slot="reference" class="text">
              {{ scope.msg.row.scoreStandard }}
            </p>
          </el-popover> -->
          <el-tooltip
            placement="top"
            :disabled="scope.msg.row.scoreStandard.length < 50"
          >
            <div slot="content">
              {{ scope.msg.row.scoreStandard }}
            </div>
            <p class="text">{{ scope.msg.row.scoreStandard }}</p>
          </el-tooltip>
        </template>

        <template slot="maxScore-header">
          评分上限
          <el-tooltip effect="dark" placement="top">
            <div slot="content">加分项：加分上限<br />减分项：减分上限</div>
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template>
        
        <template slot="weightStr-header">
          考核指标权重
          <el-tooltip
            effect="dark"
            content="加分项、减分项没有考核指标权重，不能设置"
            placement="top"
          >
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template>
        <template slot="scoreType-header">
          评分方式
          <el-tooltip
            effect="dark"
            content="仅“定量考核指标”可设置"
            placement="top"
          >
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template>
        <!-- <template slot="targetList-header">
          目标值
          <el-tooltip
            effect="dark"
            content="仅“定量考核指标”可设置"
            placement="top"
          >
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template> -->

        <template slot="scoreType" slot-scope="scope">
          <div v-if="scope.msg.row.scoreType == 1">
            直接输入
          </div>
          <div v-if="scope.msg.row.scoreType == 2">
            <el-tooltip placement="top">
              <div slot="content">
                <p style="text-align:left">
                  <span>公式计算</span><br />
                  <span v-if="scope.msg.row.dataRuleType == 1"
                    >按实际完成值来算:</span
                  >
                  <span v-if="scope.msg.row.dataRuleType == 2"
                    >按目标达成率计算:</span
                  ><br />
                  <span
                    v-for="(dataRuleItem, index) in scope.msg.row.dataRuleList"
                    :key="index"
                  >
                    <span v-if="scope.msg.row.dataRuleType == 1"
                      >{{ index + 1 }}、{{
                        dataRuleItem.min + scope.msg.row.dataUnit
                      }}<span v-if="index !== 0">＜</span
                      ><span v-if="index == 0">≤</span>完成值≤{{
                        dataRuleItem.max + scope.msg.row.dataUnit
                      }}</span
                    ><span v-if="scope.msg.row.dataRuleType == 2"
                      >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                        v-if="index !== 0"
                        >＜</span
                      ><span v-if="index == 0">≤</span>目标达成率≤{{
                        dataRuleItem.max
                      }}%</span
                    >,得分：{{ dataRuleItem.score }}分;<br />
                  </span>
                </p>
              </div>
              <p class="text">
                <span>公式计算</span><br />
                <span v-if="scope.msg.row.dataRuleType == 1"
                  >按实际完成值来算:</span
                >
                <span v-if="scope.msg.row.dataRuleType == 2"
                  >按目标达成率计算:</span
                ><br />
                <span
                  v-for="(dataRuleItem, index) in scope.msg.row.dataRuleList"
                  :key="index"
                >
                  <span v-if="scope.msg.row.dataRuleType == 1"
                    >{{ index + 1 }}、{{
                      dataRuleItem.min + scope.msg.row.dataUnit
                    }}<span v-if="index !== 0">＜</span
                    ><span v-if="index == 0">≤</span>完成值≤{{
                      dataRuleItem.max + scope.msg.row.dataUnit
                    }}</span
                  ><span v-if="scope.msg.row.dataRuleType == 2"
                    >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                      v-if="index !== 0"
                      >＜</span
                    ><span v-if="index == 0">≤</span>目标达成率≤{{
                      dataRuleItem.max
                    }}%</span
                  >,得分：{{ dataRuleItem.score }}分;<br />
                </span>
              </p>
            </el-tooltip>
          </div>
        </template>

        <template slot="scorerName" slot-scope="scope">
          <p v-if="scope.msg.row.scoreType == 2" style="color:#ccc">
            系统评分
          </p>
          <p v-if="scope.msg.row.scoreType == 1">
            {{
              scope.msg.row.scorerName
                ? scope.msg.row.scorerName
                : scope.msg.row.scorerDataName || "--"
            }}
          </p>
        </template>
      </old-table>
    </div>
  </div>
</template>

<script>
import { getPlanDetail } from "performance/store/api.js";
import dd from "performance/utils/dataDictionary.js";

export default {
  data() {
    return {
      loading: true,
      planBaseInfo: {},

      editInfo: {},
      options: dd.indicatorType,
      levelType: dd.levelType,

      tableData: [],
      headerData: [
        { title: "考核指标名称", label: "name", align: "left" },
        { title: "考核指标类型", label: "typeStr", align: "left" },
        {
          title: "考核指标说明",
          label: "descriptionStr",
          slot: "descriptionStr",
          align: "left"
        },
        {
          title: "评价标准",
          label: "scoreStandard",
          slot: "scoreStandard",
          align: "left"
        },
        {
          title: "评分上限",
          label: "maxScore",
          slotHeader: "maxScore-header",
          align: "right",
          width: 120
        },
        {
          title: "考核指标权重",
          label: "weightStr",
          slotHeader: "weightStr-header",
          align: "right"
        },
        // {
        //   title: "目标值",
        //   label: "targetList",
        //   slotHeader: "targetList-header"
        // },
        {
          title: "评分方式",
          label: "scoreType",
          align: "left",
          slotHeader: "scoreType-header",
          slot: "scoreType"
        },
        {
          title: "考核指标评分人",
          label: "scorerName",
          slot: "scorerName",
          align: "left"
        }
      ],

      isShowOperation: false //是否显示操作列
    };
  },
  created() {
    console.log(">>>>>>>>>>>>>", this.$parent.baseId);
    if (this.$route.query.planId || this.$parent.baseId) {
      this.getPlanDetail();
      // this.getPlanIndicator();
    }
  },
  mounted() {},
  methods: {
    async getPlanDetail() {
      const res = await getPlanDetail({
        planId: this.$route.query.planId || this.$parent.baseId
      });
      setTimeout(() => {
        this.loading = false;
      }, 300);

      if (res.success) {
        this.planBaseInfo = res.data.basicInfo;
        this.tableData = this.handleList(res.data.indicatorList);
      } else {
        this.$$message.error(res.msg);
      }
    },

    handleList(arr) {
      if (arr.length == 0) return [];
      arr.map(item => {
        item.typeStr = this.options[item.type];
        item.weightStr =
          item.type == 1 || item.type == 2 ? item.weight + "%" : "--";
        item.descriptionStr = item.description || "--";
        if (item.scorerData.length > 0 && item.scorerName == "") {
          item.scorerData.map(it => {
            switch (it.processorType) {
              case 1:
                it.name = "被考核人";
                break;
              case 2:
                it.name = this.levelType[it.superiorLevel];
                break;
              case 3:
                it.name = it.processorName;
                break;
            }
            return it;
          });
          item.scorerDataName = item.scorerData.map(it => it.name).join("，");
        }
        return item;
      });

      return arr;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.indicator {
  min-width: 1280px !important;
  padding-bottom: 90px;

  .indicator-table {
    margin-top: 33px;
    position: relative;
  }

  .iconfont-per {
    color: $mainColor;
    font-size: 12px;
  }
  .icon-help {
    color: #909399;
    font-size: 13px;
  }
  .text {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
  }
}
</style>
