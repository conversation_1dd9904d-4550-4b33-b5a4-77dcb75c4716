<template>
  <div class="data-input">
    <el-date-picker
      style="width:300px"
      v-if="type == 1 || type == 4"
      :key="type"
      v-model="value"
      :type="dateType"
      :clearable="false"
      value-format="yyyy-MM-dd"
      :format="format"
      placeholder="请选择"
      @change="change"
      ref="datePicker"
    >
      >
    </el-date-picker>
    <el-date-picker
      style="width:300px"
      v-if="type == 5"
      :key="type"
      v-model="value"
      @change="change"
      type="daterange"
      :clearable="false"
      value-format="yyyy-MM-dd"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
    >
    </el-date-picker>
    <div v-if="type == 3 || type == 2" style="position:relative">
      <span @click="showDoubleMonth">
        <el-input
          v-model="value"
          :readonly="readonly"
          prefix-icon="el-icon-date"
          placeholder="请选择"
          @change="change"
          @blur="HandleBlur($event)"
        />
      </span>
      <div v-show="showTime1a" class="show1">
        <p>
          <button
            type="button"
            aria-label="前一年"
            class="btn el-picker-panel__icon-btn el-date-picker__prev-btn el-icon-d-arrow-left"
            @click="prev"
          />
          <span role="button" class="span-year">{{ year }}年</span>
          <button
            type="button"
            aria-label="后一年"
            class="btn el-picker-panel__icon-btn el-date-picker__next-btn el-icon-d-arrow-right"
            @click="next"
          />
        </p>
        <div>
          <!-- <span
            v-for="(item, index) in fullMonth"
            :key="index"
            class="selectMonth"
            :class="{ active: active == index }"
            @click="selectQuarter(item, index)"
            >{{ item }}</span
          > -->

          <span
            v-for="(val, key, index) in fullMonth"
            :key="index"
            class="selectMonth"
            :class="{ active: active == index }"
            @click="selectQuarter(val, key, index)"
            >{{ key }}</span
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from "dayjs";

const findKey = (o, v, compare = (a, b) => a == b) => {
  for (let k in o) {
    o[k] = JSON.stringify(o[k]);
  }
  return Object.keys(o).find(k => compare(o[k], v));
};

const timestampToTime = (timestamp, type) => {
  let date = new Date(timestamp);
  let Y = date.getFullYear();
  let M =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;
  let D = date.getDate() + 1 < 10 ? "0" + date.getDate() : date.getDate();
  var str = "";
  switch (type) {
    case "Y":
      str = Y;
      break;
    case "M-D":
      str = M + "-" + D;
      break;
    case "Y-M":
      str = Y + "-" + M;
      break;
    default:
      str = Y + "-" + M + "-" + D;
      break;
  }
  return str;
};

export default {
  props: {
    type: {
      type: Number,
      default: null
    },
    dateObj: {
      type: Object,
      default: {
        startDate: "",
        endDate: ""
      }
    }
  },
  data() {
    return {
      value: "",
      readonly: true,
      dateType: "year",
      format: "yyyy年",
      showTime2: false,
      showTime1a: false,
      year: new Date().getFullYear(),
      choseQuarter: "",
      active: null,
      fullMonth: {
        第一季度: { start: "01-01", end: "03-31" },
        第二季度: { start: "04-01", end: "06-30" },
        第三季度: { start: "07-01", end: "09-30" },
        第四季度: { start: "10-01", end: "12-31" }
      }
    };
  },

  watch: {
    type: {
      immediate: true,
      handler(val) {
        this.showTime1a = false;
        this.active = null;
        this.value = "";
        switch (val) {
          case 1:
            this.dateType = "year";
            this.format = "yyyy年";
            break;
          case 2:
            this.dateType = "year";
            this.halfYear();
            break;
          case 3:
            this.dateType = "year";
            this.quarterTime();
            break;
          case 4:
            this.dateType = "month";
            this.format = "yyyy年MM月";
            break;
          case 5:
            this.dateType = "daterange";
            break;
        }
        console.log(val, this.dateType);
      }
    },
    dateObj: {
      handler(val, oldVal) {
        console.log(val);
        if (val.startDate) {
          if (this.type == 1) {
            this.value = timestampToTime(val.startDate);
            console.log(this.value);
          } else if (this.type == 4) {
            this.value = timestampToTime(val.startDate, "Y-M");
          } else if (this.type == 5) {
            this.value = [
              timestampToTime(val.startDate),
              timestampToTime(val.endDate)
            ];
          } else {
            const obj = {
              start: timestampToTime(val.startDate, "M-D"),
              end: timestampToTime(val.endDate, "M-D")
            };
            const year = timestampToTime(val.startDate, "Y");
            const json = JSON.parse(JSON.stringify(this.fullMonth));
            this.value = year + "年" + findKey(json, JSON.stringify(obj));
          }
          console.log("this.value", this.value);
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    console.log(this.fullMonth);
    window.onscroll = function() {
      this.$refs.datePicker.blur();
    };
  },
  methods: {
    HandleBlur(e) {
      console.log("target>", e);
    },
    change(val) {
      console.log(val);
      // this.formatDate(1, val);
      this.$emit("setDate", this.formatDate(1, val));
    },

    // 点击季度按钮
    quarterTime() {
      this.choseQuarter = "";
      // this.fullMonth = ["第一季度", "第二季度", "第三季度", "第四季度"];

      this.fullMonth = {
        第一季度: { start: "01-01", end: "03-31" },
        第二季度: { start: "04-01", end: "06-30" },
        第三季度: { start: "07-01", end: "09-30" },
        第四季度: { start: "10-01", end: "12-31" }
      };
    },
    // 点击半年按钮
    halfYear() {
      this.choseQuarter = "";
      this.fullMonth = {
        上半年: { start: "01-01", end: "06-30" },
        下半年: { start: "07-01", end: "12-31" }
      };
    },
    // 点击input框
    showDoubleMonth() {
      this.showTime1a = true;
    },
    // 上一年
    prev() {
      this.year = this.year * 1 - 1;
    },
    // 下一年
    next() {
      this.year = this.year * 1 + 1;
    },
    // 点击选项事件
    selectQuarter(value, key, index) {
      console.log(value, key);
      this.active = index;
      this.choseQuarter = this.year + "年" + key;
      this.value = this.choseQuarter;

      console.log("selectQuarter", value);
      this.showTime1a = false;
      this.$emit("setDate", this.formatDate(2, value));
    },

    formatDate(type, date) {
      let obj;
      if (type == 1) {
        if (this.dateType == "year") {
          obj = {
            startDate: dayjs(date)
              .startOf("year")
              .format("YYYY-MM-DD"),
            endDate: dayjs(date)
              .endOf("year")
              .format("YYYY-MM-DD")
          };
        } else if (this.dateType == "month") {
          obj = {
            startDate: dayjs(date)
              .startOf("month")
              .format("YYYY-MM-DD"),
            endDate: dayjs(date)
              .endOf("month")
              .format("YYYY-MM-DD")
          };
        } else {
          obj = {
            startDate: date[0],
            endDate: date[1]
          };
        }
      } else {
        obj = {
          startDate: `${this.year}-${date.start}`,
          endDate: `${this.year}-${date.end}`
        };
      }
      console.log("obj>>>", obj);

      const newDate = {
        startDate: new Date(obj.startDate).getTime(),
        endDate: new Date(obj.endDate).getTime()
      };

      console.log(newDate);
      return newDate;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";
.show1 {
  width: 320px;
  margin-top: 10px;
  position: absolute;
  z-index: 2;
  height: auto;
  color: #606266;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background: #fff;
  padding: 5px;
  font-size: 12px;
  cursor: pointer;
  p:nth-child(1) {
    height: 50px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    margin: 0 12px;
  }
  .btn {
    margin-top: 1px;
    padding: 0;
  }
}

.show1 > div {
  width: 100%;
  height: auto;
}
.show1 > div span {
  width: 50%;
}
.selectMonth {
  display: inline-block;
  float: left;
  width: 78px;
  height: 40px;
  line-height: 40px;
  text-align: center;
}
.selectMonth:hover {
  color: $mainColor;
}
.active {
  color: $mainColor;
}
.span-year {
  width: 90%;
  margin: 0 auto;
  display: inline-block;
  text-align: center;
  line-height: 40px;
  font-size: 16px;
  font-weight: 500;
  color: #606266;
}
.el-input,
.el-select {
  width: 300px;
}
</style>
