<template>
  <div class="inspectionSetup">
    <div v-if="type == 1">
      <div class="result-title">
        <div class="left">
          <span class="circle"> </span>
          <span class="text">示例</span>
          <span>示例员工</span>
        </div>
        <div class="right">
          <div class="flex">
            <span style="color:#070f29">90</span>
            <span class="total">总分</span>
          </div>
          <div class="flex">
            <span style="color:#FF9500">S</span>
            <span class="total">绩效等级</span>
          </div>
        </div>
      </div>
      <table>
        <tr v-for="item in tableData" :key="item.name">
          <td style=" border-right: 1px solid #EBEEF5;">{{ item.name }}</td>
          <td>{{ item.score }}</td>
          <td style="text-align:right">{{ item.description }}</td>
        </tr>
      </table>
    </div>
    <div v-if="type == 2">
      <div class="flow-title">
        <p class="info-title">设置评分人查看考核表时，可见的评分和评语内容</p>
        <p class="content">
          所有人的评分/评语：可以看到所有人填写的评分、评语内容
        </p>
      </div>
      <table>
        <tr v-for="(item, index) in tableData1" :key="item.name">
          <td style=" border-right: 1px solid #EBEEF5; text-align:center">
            {{ item.name }}
          </td>
          <td
            style="text-align:center"
            :style="{ color:index == 1 ? '#ff9900' : ''}"
          >
            {{ item.description }}
          </td>
        </tr>
      </table>

      <div class="flow-title">
        <p class="content">
          仅自己的评分/评语：只能自己填写的评分、评语内容
        </p>
      </div>

      <table>
        <tr v-for="(item, index) in tableData2" :key="item.name">
          <td style=" border-right: 1px solid #EBEEF5; text-align:center">
            {{ item.name }}
          </td>
          <td style="text-align:center" :style="{ color:index == 1 ? '#ff9900' : ''}">
            {{ item.description }}
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  props: ["type"],
  data() {
    return {
      text: "",
      tableData: [
        {
          name: "评分人（我）",
          description: "评语：**",
          score: "评分：88"
        },
        {
          name: "评分人2",
          description: "评语：**",
          score: "评分：92"
        },
        {
          name: "评分人3",
          description: "评语：**",
          score: "评分：90"
        }
      ],
      tableData1: [
        {
          name: "评分人1（被考核人自评）",
          description: "考核评语1" + "\xa0\xa0\xa0" + "总评分：100"
        },
        {
          name: "评分人2（我）",
          description: "考核评语2" + "\xa0\xa0\xa0" + "总评分：90"
        },
        {
          name: "评分人3",
          description: "考核评语1" + "\xa0\xa0\xa0" + "总评分：80"
        }
      ],
      tableData2: [
        {
          name: "评分人1（被考核人自评）",
          description: "不可见"
        },
        {
          name: "评分人2（我）",
          description: "考核评语2" + "\xa0\xa0\xa0" + "总评分：90"
        },
        {
          name: "评分人3",
          description: "不可见"
        }
      ]
    };
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.flex {
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  margin-right: 10px;
}
.result-title {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  border: 1px solid #ededed;
  color: #888;
  .left {
    display: flex;
    align-items: center;
    padding: 10px;
    box-sizing: border-box;
    position: relative;

    .circle {
      width: 50px;
      height: 50px;
      text-align: center;
      line-height: 50px;
      border-radius: 50%;
      background: #EEF1FF;
      opacity: 0.5;
      margin-right: 10px;
    }
    .text {
      color: $mainColor;
      position: absolute;
      left: 21px;
    }
  }
  .right {
    display: flex;
    align-items: center;
  }
}
.info-title{
  font-weight: 500;
  font-size: 14px;
  color: #070F29;
}

.info-title::before {
  content: "";
  display: inline-block;
  position: relative;
  top: 2px;
  width: 3px;
  height: 14px;
  background-color: $mainColor;
  border-radius: 1px;
  margin-right: 10px;
}
.flow-title {
  text-align: left;
  .content {
    margin: 15px auto;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 10px;
  }
}


table {
  border: 1px solid#EBEEF5;
  border-bottom: none;
  color: #555555;
}
td {
  border-bottom: 1px solid#EBEEF5;
  padding: 10px;
}

.t-c81930,
.t-b6002a {
  .result-title{
    .circle {
     background: #FCF2F2;
  }

  }
  

}
</style>
