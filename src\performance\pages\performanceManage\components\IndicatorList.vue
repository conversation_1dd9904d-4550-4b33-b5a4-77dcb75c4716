<template>
  <div class="indicator">
   <div class="indicator-table">
      <old-table
        :data="tableData"
        :headerData="headerData"
        :isShowOperation="isShowOperation"
        :operaOptions="operaOptions"
        @operaClick="handleOperaClick"
      >
        <template slot="descriptionStr" slot-scope="scope">
          <el-tooltip
            placement="top"
            :disabled="scope.msg.row.descriptionStr.length < 50"
          >
            <div slot="content">
              {{ scope.msg.row.descriptionStr }}
            </div>
            <p class="text">{{ scope.msg.row.descriptionStr }}</p>
          </el-tooltip>
        </template>

        <template slot="scoreStandard" slot-scope="scope">
          <!-- <el-popover
            placement="top-start"
            trigger="hover"
            width="200"
            :disabled="scope.msg.row.scoreStandard.length < 50"
            :content="scope.msg.row.scoreStandard"
          >
            <p slot="reference" class="text">
              {{ scope.msg.row.scoreStandard }}
            </p>
          </el-popover> -->
          <el-tooltip
            placement="top"
            :disabled="scope.msg.row.scoreStandard.length < 50"
          >
            <div slot="content">
              {{ scope.msg.row.scoreStandard }}
            </div>
            <p class="text">{{ scope.msg.row.scoreStandard }}</p>
          </el-tooltip>
        </template>

        <template slot="maxScore-header">
          评分上限
          <el-tooltip effect="dark" placement="top">
            <div slot="content">加分项：加分上限<br />减分项：减分上限</div>
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template>
        
        <template slot="weightStr-header">
          考核指标权重
          <el-tooltip
            effect="dark"
            content="加分项、减分项没有考核指标权重，不能设置"
            placement="top"
          >
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template>
        <template slot="scoreType-header">
          评分方式
          <el-tooltip
            effect="dark"
            content="仅“定量考核指标”可设置"
            placement="top"
          >
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template>
        <!-- <template slot="targetList-header">
          目标值
          <el-tooltip
            effect="dark"
            content="仅“定量考核指标”可设置"
            placement="top"
          >
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template> -->

        <template slot="scoreType" slot-scope="scope">
          <div v-if="scope.msg.row.scoreType == 1">
            直接输入
          </div>
          <div v-if="scope.msg.row.scoreType == 2">
            <el-tooltip placement="top">
              <div slot="content">
                <p style="text-align:left">
                  <span>公式计算</span><br />
                  <span v-if="scope.msg.row.dataRuleType == 1"
                    >按实际完成值来算:</span
                  >
                  <span v-if="scope.msg.row.dataRuleType == 2"
                    >按目标达成率计算:</span
                  ><br />
                  <span
                    v-for="(dataRuleItem, index) in scope.msg.row.dataRuleList"
                    :key="index"
                  >
                    <span v-if="scope.msg.row.dataRuleType == 1"
                      >{{ index + 1 }}、{{
                        dataRuleItem.min + scope.msg.row.dataUnit
                      }}<span v-if="index !== 0">＜</span
                      ><span v-if="index == 0">≤</span>完成值≤{{
                        dataRuleItem.max + scope.msg.row.dataUnit
                      }}</span
                    ><span v-if="scope.msg.row.dataRuleType == 2"
                      >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                        v-if="index !== 0"
                        >＜</span
                      ><span v-if="index == 0">≤</span>目标达成率≤{{
                        dataRuleItem.max
                      }}%</span
                    >,得分：{{ dataRuleItem.score }}分;<br />
                  </span>
                </p>
              </div>
              <p class="text">
                <span>公式计算</span><br />
                <span v-if="scope.msg.row.dataRuleType == 1"
                  >按实际完成值来算:</span
                >
                <span v-if="scope.msg.row.dataRuleType == 2"
                  >按目标达成率计算:</span
                ><br />
                <span
                  v-for="(dataRuleItem, index) in scope.msg.row.dataRuleList"
                  :key="index"
                >
                  <span v-if="scope.msg.row.dataRuleType == 1"
                    >{{ index + 1 }}、{{
                      dataRuleItem.min + scope.msg.row.dataUnit
                    }}<span v-if="index !== 0">＜</span
                    ><span v-if="index == 0">≤</span>完成值≤{{
                      dataRuleItem.max + scope.msg.row.dataUnit
                    }}</span
                  ><span v-if="scope.msg.row.dataRuleType == 2"
                    >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                      v-if="index !== 0"
                      >＜</span
                    ><span v-if="index == 0">≤</span>目标达成率≤{{
                      dataRuleItem.max
                    }}%</span
                  >,得分：{{ dataRuleItem.score }}分;<br />
                </span>
              </p>
            </el-tooltip>
          </div>
        </template>

        <template slot="scorerName" slot-scope="scope">
          <!-- style="color:#ccc" -->
          <p v-if="scope.msg.row.scoreType == 2">
            系统评分
          </p>
          <p v-if="scope.msg.row.scoreType == 1">
            {{
              scope.msg.row.scorerName
                ? scope.msg.row.scorerName
                : scope.msg.row.scoreDataName || "--"
            }}
          </p>
        </template>
      </old-table>

      <div class="indicator-footer">
        <el-button type="text" @click="handleAdd(1)">
          <i class="iconfont-per icon-add1"></i>
          新增考核指标</el-button
        >
        <el-button class="text_btn" type="text" @click="handleSelect(null)">
          <i class="iconfont-per icon-ku1"></i>
          从考核指标库选择</el-button
        >
        <div class="total" :class="{ warning: total != 100 }">{{ total }}%</div>
      </div>
    </div>

    <my-add
      ref="addItem"
      :type="currentType"
      @getItem="getItem"
      :editInfo="editInfo"
      @clear="clear"
    ></my-add>
    <my-select
      ref="selectItem"
      @save="getSelect"
      :type="currentType"
      :selectedList="tableData"
    ></my-select>
  </div>
</template>

<script>
import MyAdd from "./pageComps/myAdd";
import MySelect from "./pageComps/mySelect";
import { sumCount, arrayUnique } from "performance/utils/util";
import dd from "performance/utils/dataDictionary.js";

export default {
  components: {
    MyAdd,
    MySelect
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    basicInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    scorerRequire: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: true,
      currentType: null,
      planBaseInfo: {},
      currnetIndex: null,
      editInfo: {},
      total: 0,
      options: dd.indicatorType,
      levelType: dd.levelType,

      formData: {
        addIndicatorList: [], //新增的指标列表
        removedIndicatorList: [], //删除的指标标识sign列表
        updatedIndicatorList: [] //编辑的指标列表
      },
      tableData: [],
      // tableHeight: document.body.clientHeight - 350 + "px",
      headerData: [
        { title: "考核指标名称", label: "name", align: "left" },
        { title: "考核指标类型", label: "typeStr", align: "left" },
        {
          title: "考核指标说明",
          label: "descriptionStr",
          slot: "descriptionStr",
          align: "left"
        },
        {
          title: "评价标准",
          label: "scoreStandard",
          slot: "scoreStandard",
          align: "left"
        },
        {
          title: "评分上限",
          label: "maxScore",
          slotHeader: "maxScore-header",
          align: "right"
        },
        {
          title: "考核指标权重",
          label: "weightStr",
          slotHeader: "weight-header",
          align: "right"
        },
        // {
        //   title: "目标值",
        //   label: "targetList",
        //   slotHeader: "targetList-header"
        // },
        {
          title: "评分方式",
          label: "scoreType",
          align: "left",
          slot: "scoreType"
        },
        {
          title: "考核指标评分人",
          label: "scorerName",
          slot: "scorerName",
          align: "left"
        }
      ],

      isShowOperation: true, //是否显示操作列
      operaOptions: {
        title: "操作", //名称
        align: "left",
        fixed: "right",
        width: 150, //宽度
        buttonList: [
          //按钮列表
          { title: "编辑" },
          {
            title: "删除",
            isShow: (row, btn) => {
              return row.scoreType == 1 ? true : false;
            }
          }
        ]
      },
      postList: [],
      visible: false
    };
  },
  watch: {
    list: {
      handler: function(list) {
        this.tableData = this.handleList(list);
      },
      immediate: true,
      deep: true
    },
    basicInfo: {
      handler: function(obj) {
        this.basic = obj;
        console.log("(this.basicInfo )", this.basic);
      },
      immediate: true,
      deep: true
    },
    scorerRequire(val) {
      console.log("scorerRequire>>", val);
    },

    tableData: {
      immediate: true,
      handler: function(val) {
        const list = val.filter(it => it.type == 1 || it.type == 2);
        this.total =
          list.length > 0 ? sumCount(list.map(it => Number(it.weight))) : 0;
      },
      deep: true
    }
  },
  created() {},
  mounted() {},
  methods: {
    handleList(arr) {
      if (arr.length == 0) return [];
      arr.map(item => {
        item.typeStr = this.options[item.type];
        item.weightStr =
          item.type == 1 || item.type == 2 ? item.weight + "%" : "--";
        item.descriptionStr = item.description || "--";
        // if (item.scoreData && item.scoreData.length > 0) {
        //   item.scoreDataName = item.scoreData
        //     .map(it => it.employeeName)
        //     .join("，");
        // } else {
        //   item.scoreDataName = "--";
        // }
        if (
          item.scorerData &&
          item.scorerData.length > 0 &&
          item.scorerName == ""
        ) {
          // item.scorerData.map(it => {
          //   switch (it.processorType) {
          //     case 1:
          //       it.name = "被考核人";
          //       break;
          //     case 2:
          //       it.name = this.levelType[it.superiorLevel];
          //       break;
          //     case 3:
          //       it.name = it.processorName;
          //       break;
          //   }
          //   return it;
          // });
          // item.scorerDataName = item.scorerData.map(it => it.name).join("，");
          item.scoreDataName = item.scorerData.length
            ? item.scorerData.map(it => it.processorName).join("，")
            : "--";
        }
        if (
          !item.scorerData ||
          (item.scorerData &&
            item.scorerData.length == 0 &&
            item.scorerName == "")
        ) {
          item.scoreDataName = "--";
        }

        return item;
      });

      return arr;
    },
    //新增指标
    handleAdd(val) {
      this.editInfo = {};
      this.currentType = Number(val);
      this.currnetIndex = null;
      this.$refs.addItem.openDialog();
    },
    handleSelect(val) {
      this.currentType = Number(val);
      this.$refs.selectItem.openDialog();
    },
    //获取新增/编辑考核指标
    getItem(val) {
      if (this.currnetIndex !== null) {
        this.tableData.splice(this.currnetIndex, 1, val);
        if (val.sign) {
          this.formData.updatedIndicatorList.push(val);
        }

        this.currnetIndex = null;
      } else {
        this.tableData.push(val);
      }
      this.tableData = this.handleList(this.tableData);
      this.editInfo = {};
    },
    getSelect(val) {
      console.log("valvalvalvalval", val);
      let list = val;
      list = list.map(item => {
        //dataRuleType: 1; //计算规则:1-按实际完成值计算;2-按目标达成率计算
        if (item.type == 1 && item.dataRuleType == 2) {
          item.targetList = [];
          this.$message.warning("请完善考核指标");
        }
        if (item.dataRule) {
          item.dataRuleList = JSON.parse(JSON.stringify(item.dataRule));
          delete item.dataRule;
        }
        return item;
      });
      this.tableData = this.tableData.concat(this.handleList(list));
      // this.tableData = this.handleList(this.tableData);
    },
    clear() {
      console.log("clear");
      this.editInfo = {};
      this.currnetIndex = null;
    },

    handleOperaClick(btn, row, { $index }) {
      console.log(btn, "调试:", row, $index);
      if (btn == "删除") {
        this.tableData.splice($index, 1);
        if (row.sign) {
          this.formData.removedIndicatorList.push(row.sign);
        }
      }
      if (btn == "编辑") {
        this.editInfo = row;
        this.currnetIndex = $index;
        this.$refs.addItem.openDialog();
      }
    },
    //是否有重复指标名
    isRepeat(arr) {
      var hash = {};
      for (var i in arr) {
        if (hash[arr[i]])
          //hash 哈希
          return true;
        hash[arr[i]] = true;
      }
      return false;
    },

    //保存
    checkFormData() {
      if (this.tableData.length == 0) {
        this.$message.error("至少设置一个考核指标");
        return { def_boo: false };
      }

      const allModified = this.tableData.every(
        it => it.type == 3 || it.type == 4
      );
      if (!allModified && this.total != 100) {
        this.$message.error("考核指标权重之和不是100%，请检查");
        return { def_boo: false };
      }

      const arr = this.tableData.map(it => it.name);
      if (this.isRepeat(arr)) {
        this.$message.error("同一考核计划下，考核指标名称唯一");
        return { def_boo: false };
      }

      for (let i = 0; i < this.tableData.length; i++) {
        const el = this.tableData[i];
        if (
          el.scoreType == 2 &&
          el.dataRuleType == 2 &&
          el.targetValue == 0
          // ((el.targetList.length > 0 &&
          //   !el.targetList.every(item => item.targetValue > 0)) ||
          //   el.targetList.length == 0)
        ) {
          this.$message.error(`请设置"${el.name}"的目标值`);
          return { def_boo: false };
        }
      }

      this.formData.addIndicatorList = [];
      this.tableData.forEach(item => {
        if (!item.sign) {
          this.formData.addIndicatorList.push(item);
        }
      });

      this.formData.updatedIndicatorList = arrayUnique(
        this.formData.updatedIndicatorList,
        "sign"
      );

      const data = JSON.parse(JSON.stringify(this.formData));
      console.log(this.formData);
      this.formData = {
        addIndicatorList: [], //新增的指标列表
        removedIndicatorList: [], //删除的指标标识sign列表
        updatedIndicatorList: [] //编辑的指标列表
      };

      return { def_boo: true, data };
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.indicator {
  min-width: 1200px !important;
  // padding-bottom: 90px;
  .tao-yong {
    display: flex;
    width: 100%;
    margin-top: 22px;
    flex-direction: row-reverse;
    .taoyong-name {
      cursor: pointer;
      font-weight: Medium;
      font-size: 14px;
      color: $mainColor;
      letter-spacing: 0;
      line-height: 16px;
    }
    .icon-Arrow21 {
      margin-top: 3px;
      margin-left: 10px;
    }
  }
  .empty {
    width: 840px;
    margin: 80px auto;
  }
  .indicator-tip {
    font-size: 14px;
    color: #888;
    text-align: center;
    margin-bottom: 40px;
  }
  .indicator-tabs {
    display: flex;
    justify-content: space-between;
  }
  .indicator-tabs_item {
    width: 160px;
    text-align: center;
    color: #555;
    .indicator-tabs_pic {
      width: 160px;
      height: 140px;
      margin-bottom: 25px;
      img {
        // width: 100%;
      }
    }
  }
  .indicator-table {
    // min-width: 1200px !important;
    // margin-top: 33px;
    position: relative;
  }
  .total {
    position: absolute;
    right: 31.5%;
    color: #ff9500;
    font-size: 14px;
    &.warning {
      color: #d6342a;
    }
  }
  .indicator-footer {
    display: flex;
    align-items: center;
    height: 50px;
    border: 1px solid #ebeef5;
    border-top: none;
    padding: 0 24px;
    font-size: 14px;
    .text_btn {
      margin-left: 30px;
    }
  }
  .iconfont-per {
    color: $mainColor;
    font-size: 12px;
  }
  .icon-help {
    color: #909399;
    font-size: 13px;
  }
  .text {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
  }
}
.post-item {
  width: 300px;
  text-align: left !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
</style>
