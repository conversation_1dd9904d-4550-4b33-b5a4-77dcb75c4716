<template>
  <div class="addPerson">
    <el-dialog
      :title="isUser ? '选择部门' : '选择人员'"
      :visible.sync="showDialog"
      @close="closeDialog"
      width="740px"
      :destroy-on-close="true"
    >
      <span class="dialogContent">
        <div class="left" v-loading="loading">
          <el-input
            v-model="keyAdmin"
            :placeholder="isUser ? '请输入部门名称' : '请输入员工姓名'"
            autocomplete="off"
            suffix-icon="iconiconfonticonfontsousuo1 iconfont"
          ></el-input>

          <div class="down-tree">
            <el-tree
              ref="treeNode"
              :class="{ isOnly: isOnly }"
              :data="data"
              show-checkbox
              node-key="id"
              :filter-node-method="filterNode"
              :default-expand-all="true"
              :default-expanded-keys="[0, 1, 2, 3]"
              :default-checked-keys="currLeftData"
              :check-strictly="isUser"
              :props="{
                label: 'name'
              }"
              @check="clickAdminCheck"
            >
              <span class="custom-tree-node" slot-scope="{ data }">
                <span
                  v-if="data.type == '1'"
                  class="icon iconfont-per icon-bumen"
                ></span>
                <span
                  v-if="data.type == '2'"
                  class="icon iconfont-per icon-yuangong1"
                ></span>
                <span :title="data.name" class="show-ellipsis">{{
                  data.name.substr(0, 10)
                }}</span>
                <!-- <span v-if="data.type == 1" style="color:#ccc"
                  >({{ data.children.length }})</span
                > -->
                <span v-if="data.mobile">{{ data.mobile }}</span>

                <span class="groups" v-if="data.type == 2">{{
                  data.subsidiaryName
                }}</span>
              </span>
            </el-tree>
          </div>
        </div>
        <i class="divider"></i>
        <ul class="right">
          <div class="statistics">
            <span class="num">
              <span>已选</span>
              <span style="margin-right:3px">{{ rightList.length }}</span
              ><span v-if="!isUser">人</span
              ><span v-if="isUser">部门</span></span
            >
            <span class="clear" @click="handleAllDelete">清空</span>
          </div>
          <div class="li-box">
            <li v-for="(item, index) in rightList" :key="item.id">
              <el-tooltip
                v-if="item.name.length > 10"
                placement="top-start"
              >
                <p slot="content">
                  {{ item.name}}
                  {{ item.mobile }}
                </p>
                <p>
                  {{ item.name.substr(0, 11) + "..." }}
                  {{ item.mobile }}
                </p>
              </el-tooltip>

              <p v-else>{{ item.name }} {{ item.mobile }}</p>

              <p class="subject">{{ item.subsidiaryName }}</p>

              <!-- <span>{{ item.name }}</span> -->

              <i
                class="el-icon-close"
                @click="handleDelete(item.id, index)"
              ></i>
            </li>
          </div>

          <p class="tip" v-if="!rightList.length && !isUser">
            请在左侧选择员工
          </p>
          <p class="tip" v-if="!rightList.length && isUser">请在左侧选择部门</p>
        </ul>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="confirmPerson">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { data1 } from "./data";
export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    select: {
      type: Array,
      default: () => []
    },
    selectIdList: {
      type: Array,
      default: () => []
    },
    isOnly: {
      type: Boolean,
      default: false
    },
    isDifferent: {
      type: Boolean,
      default: false
    },
    isUser: {
      type: Boolean,
      default: false
    },
    isMapDepartmentTree: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showDialog: true,
      loading: false,
      keyAdmin: "", //key
      currLeftData: [], //当前左侧已选
      rightList: [], //考右侧数据
      currRightData: [], //当前已选数据
      checked: true,
      data: [], //考勤人员
      selectOrg: []
    };
  },
  mounted() {
    this.loading = true;
    this.getPersonList();
  },
  watch: {
    keyAdmin(val) {
      this.$refs.treeNode.filter(val);
    },
    isUser(val) {
      console.log("isUser", val);
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    recursionNode(data) {
      for (let i in data) {
        if (this.isMapDepartmentTree) {
          if (!data[i].effective) {
            data[i].disabled = true;
          }
          this.recursionNode(data[i].children);
        } else {
          if (data[i].type == 2) {
            data[i].id = data[i].subsidiaryId + "-" + data[i].id;
          }
          if (data[i].type == 1) {
            if (this.isOnly) {
              data[i].disabled = true;
            }
            this.recursionNode(data[i].children);
          }
        }
      }
      return data;
    },
    getUserNode(data) {
      for (let i in data) {
        if (data[i].type == 2) {
          data[i].id = data[i].subsidiaryId + "-" + data[i].id;
          this.select.map(item => {
            if (item.id == data[i].id.split("-")[1]) {
              this.currLeftData.push(data[i]);
              let flag = this.rightList.some(function(listItem) {
                if (listItem.id.split("-")[1] == data[i].id.split("-")[1]) {
                  return listItem.id;
                }
              });
              if (!flag) {
                this.rightList.push(data[i]);
              }
            }
          });
        }
        if (data[i].type == 1) {
          if (this.isOnly) {
            data[i].disabled = true;
          }
          this.getUserNode(data[i].children);
        }
      }
      return data;
    },

    //获取人员
    getPersonList() {
      console.log("select>>", this.select);
      this.loading = false;
      if (this.isDifferent) {
        let arr = JSON.parse(JSON.stringify(this.select));
        arr.forEach(val => {
          if (val.subsidiaryId) {
            val.id = val.subsidiaryId + "-" + val.id;
          }
          this.currRightData.push(val);
          this.rightList.push(val);
          this.currLeftData.push(val.id);
        });

        this.loading = false;
        this.data = this.recursionNode(JSON.parse(JSON.stringify(this.list)));
        let arrAdmin = [];
        setTimeout(() => {
          this.currLeftData.forEach(key => {
            console.log("key>>>>>>", key, this.$refs.treeNode.getNode(key));
            arrAdmin.push(this.$refs.treeNode.getNode(key).data);
          });
          this.rightList = arrAdmin;
        }, 0);
      } else {
        this.data = this.getUserNode(JSON.parse(JSON.stringify(this.list)));
        this.$nextTick(item => {
          this.$refs.treeNode.setCheckedNodes(this.currLeftData); //选中已选中节点
        });
      }
      this.addCss();
    },

    addCss() {
      this.$nextTick(() => {
        var levelName = document.getElementsByClassName("groups");
        for (var i = 0; i < levelName.length; i++) {
          let parentNode = levelName[i].parentNode.parentNode;
          parentNode.classList.add("h50");
        }
        var disabled = document.getElementsByClassName(
          "el-checkbox is-disabled"
        );
        for (var j = 0; j < disabled.length; j++) {
          disabled[j].remove();
        }
      });
    },
    selectUser(list, selectId) {
      for (let i in list) {
        if (list[i].type == 2) {
          if (list[i].id.split("-")[1] == selectId.split("-")[1]) {
            let flag = this.currLeftData.some(function(listItem) {
              if (listItem.id == list[i].id) {
                return listItem.id;
              }
            });
            if (!flag) {
              this.currLeftData.push(list[i]);
            }
          }
        }
        if (list[i].children) {
          this.selectUser(list[i].children, selectId);
        }
      }
    },
    //选择人员
    clickAdminCheck(val) {
      console.log("selectVal>>", val);
      if (val.type && val.type == 2 && !val.effective) {
        this.$message.error("该人员账号异常，无法参与考核");
        this.$refs.treeNode.setChecked(val.id, false);
        return;
      }

      if (this.selectIdList && this.selectIdList.length > 0) {
        console.log(this.selectIdList);
        console.log(val);
        this.selectIdList.map(item => {
          if (item == val.id.split("-")[1]) {
            this.$message.error("该人员已存在，不能重复选择");
            this.$refs.treeNode.setChecked(val.id, false);
            return;
          }
        });
      }
      if (this.isDifferent) {
        if (this.isOnly) {
          this.$refs.treeNode.setCheckedKeys([]); //删除所有选中节点
          this.$refs.treeNode.setCheckedNodes([val]); //选中已选中节点
        }

        let checkNodes = this.$refs.treeNode.getCheckedNodes();
        console.log("checkNodes", checkNodes);
        if (checkNodes.length > 0) {
          for (let i = checkNodes.length - 1; i >= 0; i--) {
            if (checkNodes[i].type && checkNodes[i].type == 1) {
              checkNodes.splice(i, 1);
            }
          }
        }

        this.rightList = checkNodes;
      } else {
        this.checkUser(val);
      }
    },
    checkUser(val) {
      if (this.isOnly) {
        this.currLeftData = [];
        this.rightList = [];
      }
      let tree = this.$refs.treeNode.getCheckedNodes();
      console.log(tree);
      let targetIndex = tree.findIndex(obj => obj.id === val.id);
      console.log(targetIndex);
      if (targetIndex !== -1) {
        this.selectUser(this.data, val.id);
        console.log(this.currLeftData);
        this.$nextTick(() => {
          this.$refs.treeNode.setCheckedNodes(this.currLeftData); //选中已选中节点
        });
        let flag = this.rightList.some(function(listItem) {
          if (listItem.id == val.id) {
            return listItem.id;
          }
        });
        if (!flag) {
          this.rightList.push(val);
        }
      } else {
        for (let i = this.currLeftData.length - 1; i >= 0; i--) {
          if (this.currLeftData[i].id.split("-")[1] == val.id.split("-")[1]) {
            this.currLeftData.splice(i, 1);
          }
        }
        for (let i = this.rightList.length - 1; i >= 0; i--) {
          if (this.rightList[i].id.split("-")[1] == val.id.split("-")[1]) {
            this.rightList.splice(i, 1);
          }
        }
        console.log(this.currLeftData);
        this.$refs.treeNode.setCheckedNodes(this.currLeftData); //选中已选中节点
      }
    },
    //关闭弹窗清空记录
    closeDialog() {
      this.keyNeed = "";
      this.keyAdmin = "";
      this.rightList = [];
      this.currLeftData = [];
      this.$emit("close");
    },
    //全部清空
    handleAllDelete() {
      this.rightList = [];
      this.currLeftData = [];
      this.$refs.treeNode.setCheckedNodes(this.currLeftData);
    },
    //删除人员已选
    handleDelete(delId, index) {
      if (this.isDifferent) {
        this.rightList.splice(index, 1);
        this.$refs.treeNode.setChecked(delId);
      } else {
        this.rightList.splice(index, 1);
        for (let i = this.currLeftData.length - 1; i >= 0; i--) {
          if (this.currLeftData[i].id.split("-")[1] == delId.split("-")[1]) {
            this.currLeftData.splice(i, 1);
          }
        }
        this.$refs.treeNode.setCheckedNodes(this.currLeftData); //选中已选中节点
      }
    },
    //确定人员选择
    confirmPerson() {
      this.currLeftData = this.$refs.treeNode.getCheckedKeys(true);
      this.currRightData = this.rightList;
      this.rightList.map(item => {
        item.id = String(item.id);
        if (item.id.indexOf("-") != -1) {
          item.id = Number(item.id.split("-")[1]);
          console.log(item.id);
        } else {
          item.id = Number(item.id);
        }
      });
      console.log(" this.currRightData", this.currRightData);
      this.$emit("commit", this.rightList);
    },
    //取消人员选择
    cancelPerson() {}
  }
};
</script>

<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";

.dialogContent {
  .divider {
    width: 1px;
    height: 396px;
    background: #dddddd;
    position: absolute;
    left: 50%;
    top: 56px;
    // top: 16%;
  }
  .custom-tree-node {
    font-size: 14px;
    // padding-left: 20px;
  }
  .icon {
    display: inline-block;
    position: relative;
    top: 1px;
    width: 18px;
    height: 18px;
    color: #9ea3ba;
  }

  .groups {
    display: block;
    margin-left: 24px;
    color: rgba(0, 0, 0, 0.25);
  }

  .el-input {
    width: 330px;
  }

  .left {
    width: 340px;
    overflow-x: scroll;
    .show-ellipsis {
      width: 50px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .right {
    position: relative;
    font-size: 14px;
    float: right;

    .statistics {
      width: 330px;
      background: #fff;
      z-index: 99;
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      .num {
        color: #6a6f7f;
      }
      .orange {
        color: #ff9500;
      }
      .clear {
        color: $mainColor;
        cursor: pointer;
      }
    }
    .li-box {
      width: 330px;
      height: 330px;
      overflow-y: auto;
      overflow-x: hidden;
      li {
        position: relative;
        background: #d9eafc;
        padding: 9px 10px;
        box-sizing: border-box;
        margin-bottom: 10px;
        // width: 300px;
        height: 58px;
        background: #f4f4f4;
        border-radius: 4px;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #6a6f7f;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        // align-items: center;
        .subject {
          margin-top: 3px;
          color: rgba(0, 0, 0, 0.25);
        }
        .el-icon-close {
          position: absolute;
          font-size: 18px;
          right: 10px;
          color: #909399;
          cursor: pointer;
        }
      }
    }

    .tip {
      text-align: center;
      margin-top: 150px;
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
.addPerson {
  /deep/ .el-dialog__body {
    height: 355px;
    padding: 10px 20px 30px;
  }
  /deep/ .el-dialog {
    height: 516px;
    .el-tree {
      margin-top: 10px;
      height: 320px;
      overflow: auto;
    }
  }
  .down-tree /deep/ .el-tree-node.is-expanded > .el-tree-node__children {
    display: inline;
  }
}
/deep/ .dialog-footer {
  position: absolute;
  text-align: right;
  display: flex;
  flex-direction: row;
  bottom: 10px;
  right: 20px;
}
// /deep/ .el-tree-node {
//   overflow-x: auto;
// }

.isOnly {
  /deep/.el-checkbox__inner {
    border-radius: 50%;
  }
}
/deep/.el-dialog__footer {
  border-top: 1px solid #eaeaea;
}
/deep/.el-input__icon {
  margin-right: 10px;
}
// /deep/ .el-tree-node {
//   height: 100px;
// }
</style>
<style>
.h50 {
  height: 48px !important;
}
.h50 .el-checkbox__input {
  position: relative !important;
  top: -10px !important;
}
</style>
