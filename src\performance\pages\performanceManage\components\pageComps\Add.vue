<template>
  <div>
    <el-drawer
      :title="title"
      :visible.sync="openVisible"
      :before-close="handleClose"
      direction="rtl"
      size="650px"
      custom-class="demo-drawer"
      ref="drawer"
    >
      <div class="box">
        <p class="tip" v-if="typeName">
          <i class="iconfont-per icon-shujuyichang"></i>
          考核类型为“{{ typeName }}”，正在添加相关考核指标
        </p>
        <el-form
          :model="formData"
          :rules="rules"
          ref="form"
          label-width="140px"
        >
          <el-form-item class="mb5" label="考核指标名称" prop="name">
            <el-input
              class="form-name"
              v-model="formData.name"
              placeholder="请输入指标名称"
              maxlength="50"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="考核指标类型" class="mb5" prop="type">
            <span slot="label"
              >考核指标类型
              <el-popover placement="bottom" width="800" trigger="hover">
                <legeng :type="formData.type"></legeng>
                <i slot="reference" class="iconfont-per icon-tubiao1"></i>
              </el-popover>
            </span>
            <el-radio-group v-model="formData.type" @change="handleRadioType">
              <el-radio :label="1">定量考核指标</el-radio>
              <el-radio :label="2">定性考核指标</el-radio>
              <el-radio :label="3">加分项</el-radio>
              <el-radio :label="4">减分项</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            label="关联父考核指标"
            v-if="
              (formData.type == 1 || formData.type == 2) &&
                baseInfo &&
                (baseInfo.type == 2 || baseInfo.type == 3)
            "
            prop="parentId"
          >
            <span slot="label">
              关联父考核指标
              <el-tooltip
                style="diaplay:inline"
                effect="dark"
                content="设置考核指标之间的拆解关系。如:可以将销售部的毛利指标拆解到该部门每个员工身上（销售部毛利指标即关联父考核指标）"
                placement="top"
              >
                <span
                  class="iconfont-per icon-help"
                  style="font-size:16px;color:#9EA5BD;"
                ></span>
              </el-tooltip>
            </span>
            <div class="parent-box">
              <el-button @click="associate = true" v-if="!formData.parentId"
                >请选择</el-button
              >
              <el-button @click="associate = true" v-if="formData.parentId"
                >重新选择</el-button
              >
              <span
                style="margin-left:10px;"
                v-if="
                  formData.parentId &&
                    `${formData.parentName}(${formData.parentExamineeName})`
                      .length <= 10
                "
              >
                {{ `${formData.parentName}(${formData.parentExamineeName})` }}
              </span>
              <el-tooltip
                v-if="
                  formData.parentId &&
                    `${formData.parentName}(${formData.parentExamineeName})`
                      .length > 10
                "
                effect="dark"
                placement="bottom"
                :content="
                  `${formData.parentName}(${formData.parentExamineeName})`
                "
              >
                <span style="margin-left:10px;">
                  {{
                    `${formData.parentName}(${
                      formData.parentExamineeName
                    })`.slice(0, 10) + "..."
                  }}
                </span>
              </el-tooltip>
              <span
                class="iconfont-per icon-cha1"
                style="font-size:16px;color:#bbb;cursor:pointer;margin-left:10px;vatical-align:middle;"
                v-if="formData.parentId"
                @click="clearParentName"
              ></span>
            </div>
          </el-form-item>
          <el-form-item label="考核指标说明" prop="description">
            <el-input
              ref="refDescription"
              style="width:300px;"
              :autosize="{ minRows: 5, maxRows: 5 }"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请输入考核指标说明"
              v-model="formData.description"
            >
            </el-input>
          </el-form-item>
          <el-form-item prop="scoreStandard" label="评价标准">
            <el-input
              ref="refScoreStandard"
              style="width:300px"
              :autosize="{ minRows: 5, maxRows: 5 }"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请输入评价标准"
              v-model="formData.scoreStandard"
            >
            </el-input>
          </el-form-item>

          <el-form-item prop="maxScore" label="评分上限">
            <span slot="label">
              <span v-if="formData.type == 1 || formData.type == 2"
                >评分上限</span
              >
              <span v-if="formData.type == 3">加分上限</span>
              <span v-if="formData.type == 4">减分上限</span>
            </span>

            <el-input
              v-model="formData.maxScore"
              placeholder="请输入评分上限"
              oninput="value = value.toString().match(new RegExp('^\\d+(?:\\.\\d{0,2})?'))"
              @change="handleChange"
            >
              <template slot="append"
                >分</template
              >
            </el-input>
          </el-form-item>

          <el-form-item
            v-if="formData.type == 1 || formData.type == 2"
            prop="weight"
            label="考核指标权重"
          >
            <span slot="label"
              >考核指标权重
              <el-popover placement="bottom" width="500" trigger="hover">
                <legeng type="5"></legeng>
                <i class="iconfont-per icon-tubiao1" slot="reference"></i>
              </el-popover>
            </span>
            <el-input
              v-model="formData.weight"
              placeholder="请输入考核指标权重"
            >
              <template slot="append"
                >%</template
              >
            </el-input>
          </el-form-item>
          <div v-if="formData.type == 1">
            <el-form-item label="评分方式" class="mb5" prop="scoreType">
              <el-radio-group v-model="formData.scoreType">
                <el-radio :label="1">直接输入</el-radio>
                <el-radio :label="2">公式计算</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <el-form-item
            v-show="formData.type == 1 && formData.scoreType == 2"
            label="计算公式"
            prop="dataSource"
          >
            <el-input
              style="display:none"
              v-model="formData.dataSource"
            ></el-input>
            <old-table
              v-if="
                formData.dataRuleList != null &&
                  formData.dataRuleList.length > 0
              "
              style="width:300px;line-height:20px"
              class="table-main"
              :data="formData.dataRuleList"
              :headerData="headerData"
              :isShowOperation="isShowOperation"
            >
              <template slot="date-header">
                <div v-if="formData.dataRuleType == '1'">
                  {{ formData.dataSource }}
                </div>
                <div v-if="formData.dataRuleType == '2'">
                  目标达成率
                  <el-tooltip
                    content="目标达成率 = （实际完成值 ÷ 目标值） x 100%"
                    placement="top"
                  >
                    <i class="iconfont-per icon-help" />
                  </el-tooltip>
                </div>
              </template>
              <template slot="date" slot-scope="scope">
                <div v-if="scope.msg.row.max">
                  <div v-if="formData.dataRuleType == '1'">
                    {{ scope.msg.row.min + formData.dataUnit }}~{{
                      scope.msg.row.max + formData.dataUnit
                    }}
                  </div>
                  <div v-if="formData.dataRuleType == '2'">
                    {{ scope.msg.row.min }}%~{{ scope.msg.row.max }}%
                  </div>
                </div>
                <div v-if="!scope.msg.row.max">
                  {{ scope.msg.row.min + formData.dataUnit }}以上
                </div>
              </template>
              <template slot="score" slot-scope="scope">
                <div>{{ scope.msg.row.score }}分</div>
              </template>
            </old-table>
            <el-button type="text" @click="handleShow">设置</el-button>
          </el-form-item>
          <div
            v-if="
              baseInfo &&
                baseInfo.type &&
                formData.type == 1 &&
                formData.scoreType == 2 &&
                formData.dataRuleType == 2 &&
                formData.dataRuleList != null &&
                formData.dataRuleList.length > 0
            "
          >
            <el-form-item prop="tagVal" label="目标值" class="mb5">
              <el-input
                style="display:none"
                v-model="formData.tagVal"
              ></el-input>
              <el-button @click="showTarget = true"
                >{{
                  formData.targetList && formData.tagVal == "true"
                    ? "重新设置"
                    : "去设置"
                }}
              </el-button>
            </el-form-item>
          </div>

          <el-form-item
            label="指定评分人"
            v-if="formData.scoreType == 1"
            prop="scorerId"
          >
            <span slot="label">
              <span> 指定评分人 </span>
              <el-tooltip
                effect="dark"
                content="设置考核指标只由特定人员或指定角色的任意人员评分"
                placement="top"
              >
                <i class="iconfont-per icon-help" />
              </el-tooltip>
            </span>
            <div style="display:flex">
              <div
                class="iconfont-per icon-tianjiachengyuan"
                @click="showDialog = true"
              ></div>
              <span class="addName-bn" v-if="selectList.length > 0">
                <span class="addName-name">{{ selectList[0].name }}</span>
                <i class="iconfont-per icon-close1" @click="handleDelete"></i>
              </span>
            </div>
          </el-form-item>
          <div style="margin-bottom:20px"></div>
          <!-- <i class="line"></i> -->
          <!-- <el-form-item label="添加到考核指标库" prop="addToBank">
            <span slot="label">
              <span style="color:#070F29">
                添加到考核指标库
              </span>
            </span>
            <el-switch v-model="formData.addToBank"> </el-switch>
          </el-form-item> -->

          <!-- <el-form-item prop="bankId" label="考核指标分组">
            <el-cascader
              v-model="formData.bankId"
              placeholder="考核指标分组"
              :options="bankTreeData"
              :props="cascaderProps"
              clearable
              style="width:298px"
              :show-all-levels="false"
            ></el-cascader>
          </el-form-item> -->
        </el-form>

        <div class="drawer-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </div>
    </el-drawer>
    <AssociateParent
      :dialog="associate"
      :indicatorType="formData.type"
      :tabType="baseInfo && baseInfo.type ? baseInfo.type : ''"
      :planId="baseInfo && baseInfo.planId ? baseInfo.planId : ''"
      :parentId="formData && formData.parentId ? formData.parentId : 0"
      @close="associate = false"
      @save="handleSave"
    ></AssociateParent>
    <set-target
      v-if="showTarget"
      @close="showTarget = false"
      :formData="formData"
      :personList="personList"
      :type="baseInfo && baseInfo.type ? baseInfo.type : ''"
      @save="commitTarget"
    ></set-target>
    <user-select
      v-if="showDialog"
      :list="treeData"
      :isOnly="true"
      :select="selectList"
      :userList="userList"
      @close="showDialog = false"
      @commit="commitSelect"
    ></user-select>
    <indicators
      :addForm="formData"
      :depList="treeData"
      :userList="userList"
      v-if="showFormula"
      @close="showFormula = false"
      @save="commitFormula"
    ></indicators>
  </div>
</template>

<script>
import AssociateParent from "./AssociateParent";
import SetTarget from "./SetTarget";
import indicators from "performance/pages/IndicatorsLibrary/components/indicators";
import UserSelect from "performance/pages/IndicatorsLibrary/components/UserSelect";
import legeng from "performance/pages/IndicatorsLibrary/components/legeng.vue";
import dd from "performance/utils/dataDictionary";
import { getDepartmentTree, getUserList } from "performance/store/api.js";

const init = () => {
  return {
    name: "", //考核指标名称
    planIndicatorId: null, //考核指标id
    dataMarkerId: null,
    type: null, //指标类型:1-定量考核指标;2-定型考核指标;3-加分项;4-减分项
    description: "", //考核指标说明
    scoreStandard: null, //评分标准
    maxScore: null, //评分上限
    weight: null, //指标权重
    scoreType: 1, //评分方式:1-直接打分;2-公式计算得分
    scorerId: null, //指定评分人员id
    sign: null,
    scorerName: null,
    // addToBank: false, //是否添加到考核指标库
    // bankId: null, //分组id
    targetList: [], //目标值
    dataUnit: null, //单位
    dataSource: null, //数据来源
    dataRuleType: 1, //计算规则
    dataRuleList: [], //计算公式
    tagVal: "",
    parentId: null, //关联父级考核指标id
    parentName: null, //关联父级考核指标名称
    parentExamineeName: null //关联父级考核指标考核对象名称
  };
};

export default {
  components: {
    AssociateParent,
    SetTarget,
    UserSelect,
    indicators,
    legeng
  },
  props: {
    type: {
      type: Number,
      default: null
    },
    editInfo: {
      type: Object
    }
  },
  data() {
    var checkName = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入考核指标名称"));
      } else {
        const status = this.$parent.tableData.some(it => it.name == value);
        if (status && this.editInfo.name != value) {
          this.formData.name = "";
          callback(new Error("同一考核计划下，考核指标名称唯一"));
        }
        callback();
      }
    };
    var checkMaxScore = (rule, value, callback) => {
      console.log("val", value);
      if (value === "" || value === null) {
        callback(new Error("请输入评分上限"));
      } else {
        const status = !new RegExp('^[1-9]\\d{0,2}(\\.\\d{1,2})?$').test(value);
        console.log(value, status);
        if (status) {
          callback(new Error("评分上限为 1~999"));
        }
        callback();
      }
    };
    var checkWeight = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入考核指标权重"));
      } else if (!new RegExp('(^[0-9]\\d*$)').test(value)) {
        callback(new Error("请输入正整数"));
      } else {
        if (!new RegExp('^(?:[1-9]?\\d|100)$').test(value)) {
          callback(new Error("考核指标权重为0～100"));
        }
        callback();
      }
    };

    var checkDataRuleList = (rule, value, callback) => {
      if (this.formData.scoreType == 2 && !value) {
        callback(new Error("请设置计算公式"));
      }
      callback();
    };

    var checkdescription = (rule, value, callback) => {
      if (value && value.length > 300) {
        this.$refs.refDescription.blur();
        callback(new Error("考核指标说明不能多于300字"));
      }
      callback();
    };

    var checkTargetList = (rule, value, callback) => {
      console.log("checkTargetList>>", value);
      if (value == "false") {
        this.$nextTick(() => {
          let bottom = this.$el.querySelector(".el-drawer__body");
          bottom.scrollTop = bottom.scrollHeight;
        });
        callback(new Error("请设置目标值"));
      }
      callback();
    };

    return {
      parentTotalName: "", //关联父级选中后的回显
      openVisible: false,
      showDialog: false,
      showFormula: false,
      showTarget: false,
      title: "新增考核指标",
      baseInfo: JSON.parse(sessionStorage.getItem("baseInfo")),
      formData: init(),
      typeName: "",
      treeData: [],
      userList: [],
      personList: [], //目标值人员列表
      isShowOperation: false,
      headerData: [
        {
          title: "销售额",
          label: "date",
          align: "left",
          slot: "date",
          slotHeader: "date-header"
        },
        {
          title: "指标评分",
          label: "score",
          align: "left",
          slot: "score"
        }
      ],
      selectList: [], //选中人员，
      // bankTreeData: [], //考核指标分组树
      associate: false,

      formLabelWidth: "120px",
      rules: {
        name: [
          {
            required: true,
            trigger: "blur",
            validator: checkName
          }
        ],
        scoreStandard: [
          { required: true, trigger: "blur", message: "请输入评价标准" }
        ],
        description: [
          {
            trigger: "change",
            validator: checkdescription
          }
        ],

        maxScore: [
          { required: true, trigger: "blur", validator: checkMaxScore }
        ],
        weight: [{ required: true, trigger: "blur", validator: checkWeight }],
        dataSource: [
          {
            required: true,
            trigger: "change",
            validator: checkDataRuleList
          }
        ],
        tagVal: [
          {
            required: true,
            trigger: "change",
            validator: checkTargetList
          }
        ]
      }
    };
  },

  watch: {
    type(val) {
      this.formData.type = val;
    },
    "formData.targetList": {
      handler(val) {
        console.log(val, "=======");
        console.log(this.formData.tagVal);
        if (val && val.length > 0) {
          this.formData.tagVal = String(
            val.every(item => item.targetValue > 0)
          );
        } else {
          if (
            this.formData.type == 1 &&
            this.formData.scoreType == 2 &&
            this.formData.dataRuleType == 2 &&
            this.formData.dataRuleList !== null &&
            this.formData.dataRuleList.length > 0
          ) {
            this.formData.tagVal = "false";
          }
        }
        console.log("this.formData.tagVal", this.formData.tagVal);
      },
      deep: true
    },
    editInfo: {
      handler(val) {
        console.log("editInfo", val);
        if (val.name) {
          this.formData = JSON.parse(JSON.stringify(val));
          if (val.scorerId) {
            this.selectList.push({
              employeeId: val.scorerId,
              name: val.scorerName
            });
          }
          this.title = "编辑考核指标";
        } else {
          this.formData = init();

          this.formData.type = this.type;
          this.title = "新增考核指标";
        }

        console.log("#######", this.formData);
      },
      deep: true
    }
  },
  created() {
    if (this.baseInfo && this.baseInfo.type) {
      this.typeName = dd.checkType[this.baseInfo.type];
      switch (this.baseInfo.type) {
        case 1:
          this.personList = this.baseInfo.subsidiaryList;
          break;
        case 2:
          this.personList = this.baseInfo.deptList;
          break;
        case 3:
          this.personList = this.baseInfo.employeeList;
          break;
      }
    }
    this.getUserList();
    this.getDepartmentTree();
  },

  methods: {
    openDialog() {
      this.openVisible = true;
    },
    //设置计算公式
    handleShow() {
      if (this.formData.maxScore) {
        this.showFormula = true;
      } else {
        this.$message.error("请先设置评分上限");
      }
    },
    handleRadioType() {
      if (this.formData.type == 3 || this.formData.type == 4) {
        this.formData.weight = null;
        this.formData.scoreType = 1;
        this.formData.dataUnit = null; //单位
        this.formData.dataSource = null; //数据来源
        this.formData.dataRuleType = 1; //计算规则
        this.formData.dataRuleList = null; //计算公式
      }
      if (this.formData.type == 2) {
        this.formData.scoreType = 1;
        this.formData.dataUnit = null; //单位
        this.formData.dataSource = null; //数据来源
        this.formData.dataRuleType = 1; //计算规则
        this.formData.dataRuleList = null; //计算公式
      }
      this.formData.parentId = null; //关联父级考核指标id
      this.formData.parentName = null; //关联父级考核指标名称
      this.formData.parentExamineeName = null; //关联父级考核指标考核对象名称
    },
    oninput(value) {
      if (!isNaN(value)) {
        return null;
      }
      console.log(value);
      return value.toString().match(new RegExp('^\\d+(?:\\.\\d{0,2})?'));
    },

    handleChange(val) {
      this.formData.maxScore = val;
      this.formData.dataRuleList = [];
    },

    async getUserList() {
      const res = await getUserList();
      if (res.success) {
        this.userList = res.data;
        console.log(this.userList);
      } else {
        this.$message.error(res.msg);
      }
      console.log(res);
    },
    //获取部门树
    async getDepartmentTree() {
      const res = await getDepartmentTree();
      if (res.success) {
        this.treeData = res.data;
      } else {
        this.$message.error(res.msg);
      }
    },
    handleClose() {
      console.log("clear1");
      this.formData = init();
      this.$refs["form"].resetFields();
      this.selectList = [];
      this.openVisible = false;
      this.$emit("clear");
    },
    //获取选中对象
    commitSelect(val) {
      this.selectList = val;
      val.map(item => {
        this.formData.scorerId = item.employeeId;
        this.formData.scorerName = item.name;
      });
      this.showDialog = false;
    },
    //删除选中人员
    handleDelete() {
      this.selectList = [];
      this.formData.scorerName = null;
      this.formData.scorerId = null;
    },
    //获取计算公式
    commitFormula(val) {
      console.log("计算公式", val);
      this.formData = { ...this.formData, ...JSON.parse(JSON.stringify(val)) };
      this.showFormula = false;
    },
    //获取目标值
    commitTarget(val) {
      console.log("目标值", val);
      this.formData = JSON.parse(JSON.stringify(val));
      console.log(this.formData);
      this.showTarget = false;
    },
    //保存
    handleSubmit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          delete this.formData.tagVal;
          let from = JSON.parse(JSON.stringify(this.formData));
          this.formData = init();
          this.selectList = [];
          this.$emit("getItem", from);
          this.openVisible = false;
        } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });
    },

    // 关联父级保存
    handleSave(e) {
      // console.log("e",e)
      // this.parentTotalName=`${e[0].indicatorName}(${e[0].examineeName})`
      this.formData.parentId = e[0].indicatorId;
      this.formData.parentName = e[0].indicatorName;
      this.formData.parentExamineeName = e[0].examineeName;
      this.associate = false;
    },
    // 清楚关联父级指标
    clearParentName() {
      // this.parentTotalName=''
      this.formData.parentId = 0;
      this.formData.parentName = "";
      this.formData.parentExamineeName = "";
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";

.tip {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6a6f7f;
  margin-bottom: 20px;
  .icon-shujuyichang {
    color: #9ea5bd;
    font-size: 20px;
    margin-right: 10px;
  }
}
.el-form {
  .el-input,
  .el-select {
    width: 300px;
  }
}
.form-name {
  /deep/.el-input__inner {
    padding: 0 49px 0 12px;
  }
}
.icon-tianjiachengyuan {
  width: 50px;
  font-size: 30px;
  color: $mainColor;
}
.el-form-item {
  margin-bottom: 20px;
}
.addName-bn {
  margin-left: 10px;
  width: 100px;
  height: 32px;
  margin-top: 5px;
  background: #f1f1f1;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  box-sizing: border-box;

  .addName-name {
    width: 70px;
    display: inline-block;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 5px;
  }
  .icon-close1 {
    font-size: 12px;
    cursor: pointer;
    color: #6a6f7f;
  }
}

/deep/ .el-drawer {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  padding: 0 20px 80px;

  .el-drawer__header {
    color: #070f29;
    font-size: 16px;
    padding: 16px 0;
    margin-bottom: 20px;
    border-bottom: 1px solid #eaeaea;
    .el-drawer__close-btn {
      color: #6a6f7f;
    }
  }
}
.icon-help {
  color: #909399;
  font-size: 13px;
}
/deep/ .el-textarea__inner {
  padding: 5px 15px 30px;
}
/deep/ .el-textarea .el-input__count {
  width: 92%;
  height: 30px;
  bottom: 1px;
  text-align: right;
}
.mb5 {
  margin-bottom: 5px !important;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 610px;
  height: 80px;
  padding: 20px 0;
  background: #fff;
  border-top: 1px solid #eaeaea;
  box-sizing: border-box;
  text-align: right;
}
.icon-tubiao1 {
  color: #9ea5bd;
}
.parent-box {
  // width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
}
</style>
