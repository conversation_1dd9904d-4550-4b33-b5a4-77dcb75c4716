<template>
  <div>
    <el-drawer
      :title="title"
      :visible.sync="openVisible"
      @close="close"
      size="650px"
      direction="rtl"
      custom-class="demo-drawer"
      ref="drawer"
    >
      <el-form :model="formData" ref="form" :rules="rules" label-width="100px">
        <el-form-item prop="processorType" :label="title.substr(2, 2) + '人'">
          <el-radio-group
            v-model="formData.processorType"
            @change="handleChangeRadio"
          >
            <el-radio :label="1">被考核人</el-radio>
            <el-radio :label="2">上级</el-radio>
            <el-radio :label="3">指定人员</el-radio>
          </el-radio-group>
        </el-form-item>

        <div v-if="formData.processorType == 3">
          <el-form-item label="选择人员" prop="processorId">
            <el-input
              style="display:none"
              v-model="formData.processorId"
            ></el-input>
            <div class="flex-box">
              <div
                class="iconfont-per icon-tianjiachengyuan"
                @click="showDialog = true"
              ></div>
              <span class="addName-bn" v-if="selectList.length > 0">
                <span class="addName-name">{{ selectList[0].name }}</span>
                <i class="iconfont-per icon-close1" @click="handleDelete"></i>
              </span>
            </div>
          </el-form-item>
        </div>

        <div v-if="formData.processorType == 2">
          <el-form-item label="选择上级" prop="superiorLevel">
            <el-select v-model="formData.superiorLevel" placeholder="请选择">
              <el-option
                v-for="item in levelType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <!-- <el-form-item label="绩效校准" prop="addToBank">
          <span slot="label">
            绩效校准
            <el-tooltip
              effect="dark"
              content="开启后，可直接对绩效结果进行调整"
              placement="top"
            >
              <i class="iconfont-per icon-help" />
            </el-tooltip>
          </span>

          <el-switch v-model="formData.addToBank"> </el-switch>
        </el-form-item> -->
      </el-form>

      <div class="drawer-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="commit">确 定</el-button>
      </div>
    </el-drawer>
    <user-select
      v-if="showDialog"
      :list="treeData"
      :isOnly="true"
      :select="selectList"
      :userList="userList"
      @close="showDialog = false"
      @commit="commitSelect"
    ></user-select>
  </div>
</template>

<script>
import { getDepartmentTree, getUserList } from "performance/store/api.js";
import UserSelect from "../../../IndicatorsLibrary/components/UserSelect";
export default {
  components: {
    UserSelect
  },
  props: {
    form: {
      type: Object,
      default: () => {
        return {};
      }
    },
    title: {
      type: String,
      default: "新增确认节点"
    }
  },
  data() {
    return {
      openVisible: false,
      showDialog: false,
      selectList: [],
      userList: [],
      formData: {
        nodeSort: "",
        name: null,
        indicatorRange: 2,
        processorType: 3, //评分人类型
        processorId: null, //指定人员id
        superiorLevel: null //指定上级级别:1-直接上级;2-二级上级;3-三级...
      },
      levelType: [
        { label: "直接上级", value: 1 },
        { label: "隔级上级", value: 2 },
        { label: "三级上级", value: 3 },
        { label: "四级上级", value: 4 },
        { label: "五级上级", value: 5 },
        { label: "六级上级", value: 6 },
        { label: "七级上级", value: 7 }
      ],
      treeData: [],
      rules: {
        processorId: [
          {
            required: true,
            message: "请选择人员",
            trigger: "change"
          }
        ],
        superiorLevel: [
          {
            required: true,
            message: "请选择上级",
            trigger: "change"
          }
        ]
      }
    };
  },
  watch: {
    form(val) {
      console.log("val>>>>", val);
      if (val) {
        this.formData = JSON.parse(JSON.stringify(val));
        this.selectList.push({
          name: val.name,
          employeeId: val.processorId
        });
      } else {
        this.formData = {
          nodeSort: "",
          name: null,
          indicatorRange: 2,
          processorType: 3, //评分人类型
          processorId: null, //指定人员id
          superiorLevel: null //指定上级级别:1-直接上级;2-二级上级;3-三级...
        };
      }
      this.$refs.form.clearValidate();
    },
    title(val) {
      this.title = val;
    }
  },

  mounted() {
    this.getDepartmentTree();
    this.getUserList();
  },
  methods: {
    openDialog() {
      this.openVisible = true;
    },
    handleChangeRadio(val) {
      console.log(val);
      switch (val) {
        case 1:
          this.formData.superiorLevel = null;
          this.selectList = [];
          break;
        case 2:
          this.formData.superiorLevel = 1;
          break;
        case 3:
          this.selectList = [];
          this.formData.processorId = null;
          this.formData.superiorLevel = null;
          break;
      }
    },
    //获取选中对象
    commitSelect(val) {
      if (!val.length) return this.$message.error("请选择人员");
      this.selectList = val;
      val.forEach(item => {
        this.formData.processorId = item.employeeId;
        this.formData.name = item.name;
      });
      this.showDialog = false;
    },
    //删除选中人员
    handleDelete() {
      this.selectList = [];
      this.formData.processorId = null;
    },
    async getUserList() {
      const res = await getUserList();
      if (res.success) {
        this.userList = res.data;
        console.log(this.userList);
      } else {
        this.$message.error(res.msg);
      }
      console.log(res);
    },
    //获取员工树
    async getDepartmentTree() {
      const res = await getDepartmentTree();
      if (res.success) {
        this.treeData = res.data;
        console.log(this.treeData);
      } else {
        this.$message.error(res.msg);
      }
      console.log(res);
    },
    genID() {
      return Math.floor(Math.random() * (100 - 1)) + 1;
    },
    close() {
      this.formData = {
        nodeSort: "",
        name: null,
        processorType: 3, //评分人类型
        processorId: null, //指定人员id
        superiorLevel: null //指定上级级别:1-直接上级;2-二级上级;3-三级...
      };
      this.selectList = [];
      this.openVisible = false;
      this.$emit("clear");
    },

    commit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.formData.processorType == 2) {
            this.formData.name = this.levelType.filter(
              item => item.value == this.formData.superiorLevel
            )[0].label;
          } else if (this.formData.processorType == 1) {
            this.formData.name = "被考核人";
          }

          let from = JSON.parse(JSON.stringify(this.formData));
          this.formData = {
            nodeSort: "",
            name: null,
            processorType: 3, //评分人类型
            processorId: null, //指定人员id
            superiorLevel: null //指定上级级别:1-直接上级;2-二级上级;3-三级...
          };
          this.$refs.form.resetFields();

          this.selectList = [];

          this.$emit("getItem", from);
          this.openVisible = false;
        } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });

      // this.selectList = [];
      // this.openVisible = false;
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.el-input,
.el-select,
.el-cascader {
  width: 298px;
}
.tip {
  font-size: 14px;
  color: #6a6f7f;
  margin: 0 20px 20px;
}
.search {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
.flex-box {
  display: flex;
}
.radio {
  margin-bottom: 20px;
}

/deep/ .el-drawer {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  padding: 0 20px 80px;
  .el-drawer__header {
    color: #070f29;
    font-size: 16px;
    padding: 16px 0;
    margin-bottom: 20px;
    border-bottom: 1px solid #eaeaea;
    .el-drawer__close-btn {
      color: #6a6f7f;
    }
  }
  .el-radio-button__inner {
    height: 32px;
    line-height: 7px;
    font-size: 14px;
  }
}

.drawer-footer {
  position: absolute;
  text-align: right;
  bottom: 0;
  width: 610px;
  height: 80px;
  padding: 20px 0;
  background: #fff;
  border-top: 1px solid #eaeaea;
  box-sizing: border-box;
}

.addName-bn {
  margin-top: 5px;
  margin-left: 10px;
  width: 100px;
  height: 32px;
  background: #f1f1f1;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  box-sizing: border-box;

  .addName-name {
    width: 70px;
    display: inline-block;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 5px;
  }
  .icon-close1 {
    cursor: pointer;
    font-size: 12px;
    color: #6a6f7f;
  }
}
.icon-tianjiachengyuan {
  width: 50px;
  font-size: 30px;
  color: $mainColor;
}
</style>
