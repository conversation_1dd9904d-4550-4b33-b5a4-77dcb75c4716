<template>
  <el-dialog
    style="padding-top:0"
    title="关联父考核指标"
    :visible.sync="dialogFlag"
    @close="close"
    width="740px"
  >
    <el-tabs
      class="tabs"
      @tab-click="tabClick"
      v-model="activeTab"
      v-if="tabType != 3"
    >
      <el-tab-pane label="公司考核" name="first"> </el-tab-pane>
      <el-tab-pane label="部门考核" name="second"> </el-tab-pane>
    </el-tabs>
    <p class="tip">
      <i class="iconfont-per icon-shujuyichang" style="font-size:16px;"></i>
      仅支持选择发起考核确认后的指标
    </p>
    <el-input
      class="input"
      v-model="searchName"
      placeholder="请输入考核指标名称"
      autocomplete="off"
      @keyup.enter.native="searchItemChangeInput(searchName)"
      @input="searchItemChangeInput"
    >
      <!-- suffix-icon="el-icon-search" -->
      <i slot="suffix" class="iconfont-per icon-sousuo" style="font-size:16px"></i>
    </el-input>
      <!-- :class="tabType == 3 ? 'mt10' : ''" -->
    <!-- 表格 -->
    <el-table
      ref="multipleTable"
      :header-cell-style="{ background: '#f1f1f1', color: '#070F29' }"
      :data="tableData"
      border
      style="width: 100%"
      max-height="241"
      v-loading="loading"
    >
      <!-- @select="select"
      @selection-change="selectionChange" -->
      <!-- <el-table-column
        type="selection"
        align="center"
        width="55">
      </el-table-column> -->
      <el-table-column align="center" width="55">
        <template slot-scope="scope">
          <!-- <el-radio
            :label="scope.$index"
            v-model="templateRadio"
            @change="getTemplateRow(scope.$index,scope.row)"
          >&nbsp;</el-radio> -->
          <span
            class="iconfont-per icon-duigoutianchong- radio-en"
            style="font-size:16px;color:#4F71FF;cursor:pointer;"
            v-if="flag === true && activeIndex == scope.$index"
            @click="getTemplateRow(false, scope.$index, scope.row)"
          ></span>

          <!-- class="iconfont-per icon-kaohejinhangzhong"  -->
          <!-- <span
            class="iconfont-per icon-kaohejinhangzhong" 
            style="font-size:17px;cursor:pointer;"
            v-else
            @click="getTemplateRow(true,scope.$index,scope.row)"
          ></span> -->
          <span
            class="radio-un"
            v-else
            @click="getTemplateRow(true, scope.$index, scope.row)"
          ></span>
        </template>
      </el-table-column>

      <el-table-column
        prop="indicatorName"
        label="考核指标名称"
        minWidth="120"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="examineeName"
        label="考核对象"
        minWidth="120"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="planName"
        label="所属考核计划"
        minWidth="200"
        show-overflow-tooltip
      >
      </el-table-column>
    </el-table>
    <section class="temp-table-pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="pageOptions.pageSizes"
        :page-size="pageOptions.pageSize"
        layout="total, prev, pager, next,sizes,jumper"
        :total="pageOptions.total"
        background
      >
      </el-pagination>
    </section>

    <span slot="footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save" :disabled="!templateSelection"
        >保 存</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import { getParent } from "performance/store/api.js";
export default {
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    indicatorType: null,
    tabType: null,
    planId: null,
    parentId: null
  },
  data() {
    return {
      loading: false,
      dialogFlag: this.dialog,
      activeTab: "first",
      lastActiveTab: "first",
      searchName: null,
      templateRadio: null,
      // selectData:null,//用来接收选中的行数据，最后需要返回给父元素
      // selectedId:null,//用来解决数据回显的问题
      templateSelection: null, //当前行的数据
      activeIndex: null, //当前点击的index
      flag: false, //单选框状态
      pageOptions: {
        currPage: 1, //当前页码
        total: 10, //数据总数
        pageSize: 10, //每页显示条数
        pageSizes: [10, 20, 30, 40, 50, 100] //每页显示个数选择器选项设置
      },
      tableData: [],
      timer: null
      // headerData: [
      //   { title: "日期", label: "indicatorName" },
      //   { title: "姓名", label: "examineeName" }
      // ],
      // typeOptions: {
      //   width: 80,
      //   label: "序号",
      //   type: "selection" // type类型支持：selection - 多选， index - 序号
      // }
    };
  },
  watch: {
    dialog(val) {
      this.dialogFlag = val;
      if (val) {
        this.searchName = "";
        this.handleGetParent();
      }
    },
    indicatorType(val) {},
    parentId(val) {}
  },
  mounted() {
    // this.handleGetParent()
  },
  methods: {
    searchItemChangeInput(val) {
      // console.log("val: ",val)
      this.timer && clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.searchName = val.trim();
        this.pageOptions.currPage = 1;
        this.handleGetParent();
      }, 500);
    },
    save() {
      // console.log("row111",this.templateSelection)
      this.$emit("save", this.templateSelection);
    },
    close() {
      this.$emit("close");
    },
    tabClick(tab) {
      // console.log("tab:",tab)
      if (this.lastActiveTab == this.activeTab) {
        return false;
      }
      this.lastActiveTab = this.activeTab;
      this.pageOptions.currPage = 1;
      this.searchName = "";
      this.handleGetParent();
    },
    handleGetParent() {
      this.loading = true;
      let activeTab =
        this.tabType == 3 ? this.tabType : this.activeTab == "first" ? 1 : 2;
      this.activeIndex = null; //取消选中状态
      let params = {
        currentPage: this.pageOptions.currPage,
        pageSize: this.pageOptions.pageSize,
        // indicatorType:'1',
        indicatorType: this.indicatorType,
        // examineePlanId:
        planId: this.planId,
        tabType: activeTab,
        name: this.searchName
      };
      // console.log('params',params)
      getParent(params)
        .then(res => {
          this.loading = false;
          // console.log('关联父考核指标', res)
          if (res.success) {
            let { records, total } = res.data;
            this.tableData = records;
            this.pageOptions.total = total;
            // 重新选择回显
            // console.log("this.parentId",this.parentId)
            if (this.parentId) {
              // console.log("this.parentId",this.parentId)
              this.templateSelection = records.filter((ele, idx) => {
                if (ele.indicatorId == this.parentId) {
                  // console.log("idx",idx)
                  this.activeIndex = idx;
                  this.flag = true;
                }
                return ele.indicatorId == this.parentId;
              });
            } else {
              this.templateSelection = null;
              this.flag = false;
            }
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // select(selection, row) {
    //   // 清除 所有勾选项
    //   this.$refs.multipleTable.clearSelection()
    //   // 当表格数据都没有被勾选的时候 就返回
    //   // 主要用于将当前勾选的表格状态清除
    //   if(selection.length == 0) return this.selectData =null
    //   this.$refs.multipleTable.toggleRowSelection(row, true);
    //   this.selectData = row
    //   console.log("row:",this.selectData)
    // },
    // // 表格的选中 可以获得当前选中的数据
    // selectionChange(val) {
    //   // 将选中的数据存储起来
    //   // this.selectData = val
    // },
    getTemplateRow(flag, activeIndex, row) {
      this.activeIndex = activeIndex;
      this.flag = flag;
      if (flag) {
        this.templateSelection = [];
        this.templateSelection.push(row);
        // console.log("data",this.templateSelection);
      } else {
        this.templateSelection = null;
        // console.log("data",this.templateSelection);
      }
    },

    //分页size切换
    handleSizeChange(val) {
      this.loading = true;
      this.pageOptions.pageSize = val;
      this.handleGetParent();
    },
    //页码切换
    handleCurrentChange(val) {
      this.loading = true;
      this.pageOptions.currPage = val;
      this.handleGetParent();
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__header {
  margin: 0 16px;
  font-size: 16px;
  // font-weight: 600;
  color: #070f29;
  margin-left: 20px;
  margin-right: 20px;
}
/deep/ .el-dialog__body {
  padding: 20px;
  // padding: 10px 20px 20px;
}
/deep/ .el-table__header-wrapper th {
  font-weight: normal;
}
.tip {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6a6f7f;
  margin-bottom: 20px;
  .icon-shujuyichang {
    color: #9ea5bd;
    font-size: 20px;
    margin-right: 10px;
  }
}
.input {
  // margin-top: 20px;
  width: 260px;
  margin-bottom: 20px;
}
/deep/ .el-input__suffix {
  line-height: 40px;
}
.tabs {
  font-size: 16px;
  margin-bottom: 10px;
  /deep/ .el-tabs__nav-wrap::after {
    height: 0;
  }
  /deep/ .el-tabs__item {
    font-size: 16px;
  }
}
/deep/ .el-tabs {
  margin-top: -10px;
}
/deep/ .el-table__header-wrapper .el-checkbox__inner {
  display: none;
}
.radio-en {
  display: inline-block;
  width: 20px;
  height: 24px;
}
.radio-un {
  display: inline-block;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 1px solid #ccc;
  box-sizing: border-box;
  margin-top: 3px;
  cursor: pointer;
  // margin-left: -1px;
}

.temp-table-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.mt10 {
  margin-top: 10px;
}
/deep/.el-dialog__footer {
  border-top: 1px solid #EAEAEA;
  padding-left: 0;
  padding-right: 0;
  padding-top: 20px;
}
/deep/.el-table__cell {
  font-weight: 400;
}

</style>
