<template>
  <div>
    <el-drawer
      :title="title"
      :visible.sync="openVisible"
      @close="close"
      size="650px"
      direction="rtl"
      custom-class="demo-drawer"
      ref="drawer"
    >
      <p class="tip" v-if="formData.processorType == 1">
        <i class="iconfont-per icon-shujuyichang"></i>
        当执行人为多人时，所有人均需评价，最终取其平均分
      </p>
      <el-form :model="formData" ref="form" label-width="140px">
        <el-form-item prop="processorType" label="评分人">
          {{ typeObj[formData.processorType] }}
          <!-- <el-radio-group v-model="formData.processorType">
            <el-radio :label="1">被考核人</el-radio>
            <el-radio :label="2">上级</el-radio>
            <el-radio :label="3">指定人员</el-radio>
          </el-radio-group> -->
        </el-form-item>

        <el-form-item
          v-if="formData.processorType == 3"
          label="选择人员"
          prop="processorId"
          :rules="[
            {
              required: true,
              message: '请选择人员',
              trigger: 'change'
            }
          ]"
        >
          <el-input
            style="display:none"
            v-model="formData.processorId"
          ></el-input>
          <div class="flex-box">
            <i
              class="iconfont-per icon-tianjiachengyuan"
              @click="showDialog = true"
            ></i>

            <span class="addName-bn" v-if="selectList.length > 0">
              <span class="addName-name">{{ selectList[0].name }}</span>
              <i class="iconfont-per icon-close1" @click="handleDelete"></i>
            </span>
          </div>
        </el-form-item>
        <el-form-item
          v-if="formData.processorType == 2"
          label="选择上级"
          prop="superiorLevel"
          :rules="[
            {
              required: true,
              message: '请选择上级',
              trigger: 'change'
            }
          ]"
        >
          <el-select v-model="formData.superiorLevel" placeholder="请选择">
            <el-option
              v-for="item in levelType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          prop="weight"
          label="评分权重"
          :rules="[
            {
              required: true,
              validator: checkWeight,
              trigger: 'blur'
            }
          ]"
        >
          <el-input placeholder="请输入评分权重" v-model="formData.weight">
            <template slot="append"
              >%</template
            >
          </el-input>
        </el-form-item>

        <el-form-item
          prop="visibleType"
          label="可见内容"
          v-if="formData.processorType != 1"
        >
          <span slot="label"
            >可见内容
            <el-popover placement="left" width="360" trigger="hover">
              <Example type="2"></Example>
              <i class="iconfont-per icon-tubiao1" slot="reference"></i>
            </el-popover>
          </span>
          <el-radio-group v-model="formData.visibleType">
            <el-radio :label="2">所有人的评分/评语</el-radio>
            <el-radio :label="1">仅自己的评分/评语</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="commit">确 定</el-button>
      </div>
    </el-drawer>
    <user-select
      v-if="showDialog"
      :list="treeData"
      :isOnly="true"
      :select="selectList"
      :userList="userList"
      @close="showDialog = false"
      @commit="commitSelect"
    ></user-select>
  </div>
</template>

<script>
import { getDepartmentTree, getUserList } from "performance/store/api.js";
// import SelectStaff from "../SelectStaff";
import UserSelect from "../../../IndicatorsLibrary/components/UserSelect";

import Example from "../Example";

export default {
  components: {
    UserSelect,
    Example
  },
  props: {
    form: {
      type: Object,
      default: () => {
        return {};
      }
    },
    type: {
      type: Number,
      default: null
    },
    title: {
      type: String,
      default: "新增评分节点"
    }
  },
  data() {
    return {
      checkWeight: (rule, value, callback) => {
        if (value === "") {
          callback(new Error("请输入考核指标权重"));
        } else if (!new RegExp('(^[0-9]\\d*$)').test(value)) {
          callback(new Error("请输入正整数"));
        } else {
          if (!new RegExp('^(?:[1-9]?\\d|100)$').test(value)) {
            callback(new Error("考核指标权重为0～100"));
          }
          callback();
        }
      },
      openVisible: false,
      showDialog: false,
      selectList: [],
      userList: [],
      typeObj: {
        1: "被考核人",
        2: "上级",
        3: "指定人员"
      },

      formData: {
        nodeSort: "",
        name: null,
        weight: null, //评分权重
        indicatorRange: 2,
        processorType: 3, //评分人类型
        processorId: null, //指定人员id
        superiorLevel: null, //指定上级级别:1-直接上级;2-二级上级;3-三级...
        visibleType: 2 //可见内容
      },
      levelType: [
        { label: "直接上级", value: 1 },
        { label: "隔级上级", value: 2 },
        { label: "三级上级", value: 3 },
        { label: "四级上级", value: 4 },
        { label: "五级上级", value: 5 },
        { label: "六级上级", value: 6 },
        { label: "七级上级", value: 7 }
      ],
      treeData: []
    };
  },
  watch: {
    form(val) {
      console.log("val>>>>", val);
      if (val) {
        this.formData = JSON.parse(JSON.stringify(val));
        this.selectList.push({
          name: val.name,
          employeeId: val.processorId
        });
      } else {
        this.formData = {
          nodeSort: "",
          name: null,
          weight: null, //评分权重
          indicatorRange: 2,
          processorType: 3, //评分人类型
          processorId: null, //指定人员id
          superiorLevel: null, //指定上级级别:1-直接上级;2-二级上级;3-三级...
          visibleType: 2 //可见内容
        };
        this.selectList = [];
      }
    },
    type(val) {
      console.log("type", val);
      this.formData.processorType = val;
      if (val == 1) {
        this.formData.weight = 0;
      }
    }
  },

  mounted() {
    this.getDepartmentTree();
    this.getUserList();
  },

  methods: {
    openDialog() {
      this.openVisible = true;
    },
    async getUserList() {
      const res = await getUserList();
      if (res.success) {
        this.userList = res.data;
        console.log(this.userList);
      } else {
        this.$message.error(res.msg);
      }
      console.log(res);
    },

    //获取选中对象
    commitSelect(val) {
      if (!val.length) return this.$message.error("请选择人员");
      this.selectList = val;
      val.forEach(item => {
        this.formData.processorId = item.employeeId;
        this.formData.name = item.name;
      });
      this.showDialog = false;
    },
    //删除选中人员
    handleDelete() {
      this.selectList = [];
      this.formData.processorId = "";
    },
    //获取员工树
    async getDepartmentTree() {
      const res = await getDepartmentTree();
      if (res.success) {
        this.treeData = res.data;
        console.log(this.treeData);
      } else {
        this.$message.error(res.msg);
      }
      console.log(res);
    },
    close() {
      console.log("close");
      this.reset();
      this.openVisible = false;
      this.$emit("clear");
    },
    reset() {
      this.formData = {
        ...this.formData,
        nodeSort: "",
        name: null,
        indicatorRange: 2,
        weight: null, //评分权重
        processorId: null, //指定人员id
        superiorLevel: null, //指定上级级别:1-直接上级;2-二级上级;3-三级...
        visibleType: 2 //可见内容
      };
      this.$refs["form"].resetFields();
      this.selectList = [];
    },

    commit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.formData.processorType == 2) {
            this.formData.name = this.levelType.filter(
              item => item.value == this.formData.superiorLevel
            )[0].label;
          } else if (this.formData.processorType == 1) {
            this.formData.name = "被考核人";
            this.formData.visibleType = 1;
          }

          let from = JSON.parse(JSON.stringify(this.formData));
          this.reset();
          this.$emit("getItem", from);
          this.openVisible = false;
        } else {
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
        }
      });

      // this.selectList = [];
      // this.openVisible = false;
    }

    // cancelForm() {
    //   this.openVisible = false;
    // }
  }
};
</script>
<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.tip {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6a6f7f;
  margin: 0 20px 20px;
  .icon-shujuyichang {
    font-size: 20px;
    margin-right: 10px;
  }
}
.el-input,
.el-select,
.el-cascader {
  width: 298px;
}

.search {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
.radio {
  margin-bottom: 20px;
}

/deep/ .el-drawer {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  padding: 0 20px 80px;

  .el-drawer__header {
    color: #070f29;
    font-size: 16px;
    padding: 16px 0;
    margin-bottom: 20px;
    border-bottom: 1px solid #eaeaea;
    .el-drawer__close-btn {
      color: #6a6f7f;
    }
  }
  .el-radio-button__inner {
    height: 32px;
    line-height: 7px;
    font-size: 14px;
  }
}
.icon-tubiao1 {
  color: #9ea5bd;
}

.drawer-footer {
  position: absolute;
  text-align: right;
  bottom: 0;
  width: 610px;
  height: 80px;
  padding: 20px 0;
  background: #fff;
  border-top: 1px solid #eaeaea;
  box-sizing: border-box;
}
.flex-box {
  display: flex;
}

.addName-bn {
  margin-left: 10px;
  width: 100px;
  height: 32px;
  margin-top: 5px;
  background: #f1f1f1;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  box-sizing: border-box;

  .addName-name {
    width: 70px;
    display: inline-block;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 5px;
  }
  .icon-close1 {
    font-size: 12px;
    color: #6a6f7f;
    cursor: pointer;
  }
}
.icon-tianjiachengyuan {
  width: 50px;
  font-size: 30px;
  color: $mainColor;
}
</style>
