<template>
  <el-dialog
    title="设置目标值"
    :visible.sync="isShow"
    @close="close"
    width="740px"
  >
    <el-form label-width="90px">
      <el-form-item
        prop="name"
        label="考核对象"
        :rules="{
          required: true
        }"
      >
        <span>{{ objName }}</span>
      </el-form-item>
      <div class="source">
        <el-form-item
          prop="name"
          label="数据来源"
          :rules="{
            required: true
          }"
        >
          {{ form.dataSource }}
        </el-form-item>
        <el-form-item prop="name" label="单位">
          {{ form.dataUnit }}
        </el-form-item>
      </div>
    </el-form>

    <!-- <old-table :data="tableData" :headerData="headerData"></old-table> -->

    <el-table
      class="table"
      border
      :data="form.targetList"
      :header-cell-style="{
        background: '#F5F6F7',
        color: '#262935',
        fontSize: '14px'
      }"
    >
      <el-table-column label="考核对象">
        <template #default="scope">
          <!-- <el-input-number
            type="number"
            v-model="scope.row.date"
            :controls="false"
          ></el-input-number> -->

          <span class="unit">{{ scope.row.examineeName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="目标值">
        <template #default="scope">
          <el-input
            type="number"
            v-model="scope.row.targetValue"
            oninput="value=value.toString().match(new RegExp('^\\d+(?:\\.\\d{0,2})?'))"
            @blur="blurNum(scope.row.targetValue, scope.$index)"
          >
            <template slot="append">{{ form.dataUnit }}</template>
          </el-input>
        </template>
      </el-table-column>
    </el-table>

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="commit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    formData: {
      type: Object,
      default: () => {}
    },
    personList: {
      type: Array,
      default: () => []
    },
    type: {
      type: Number
    }
  },
  data() {
    return {
      isShow: true,
      form: {},
      objName: ""
    };
  },
  watch: {
    personList: {
      handler(val) {},
      deep: true
    }
  },
  created() {
    this.handleShow();
  },

  methods: {
    handleShow() {
      this.form = JSON.parse(JSON.stringify(this.formData));
      console.log(this.form);
      console.log(this.form.targetList);

      console.log("this.form.targetList.length", this.form.targetList.length);

      console.log("personList", this.personList, this.type);

      if (this.form.targetList.length == 0) {
        this.personList.forEach(item => {
          let name = "";
          switch (this.type) {
            case 1:
              name = item.subsidiaryName;
              break;
            case 2:
              name = item.deptName;
              break;
            case 3:
              name = item.employeeName;
              break;
          }
          const obj = {
            examineePlanId: item.examineePlanId,
            examineeName: name,
            targetValue: null
          };
          console.log(obj);
          this.form.targetList.push(obj);
        });
      }
      this.form.targetList.map(item => {
        if (item.targetValue == 0) {
          item.targetValue = null;
        }
        return item;
      });
      console.log("personList", this.form.targetList);

      this.objName = this.form.targetList.map(it => it.examineeName).join("，");
    },

    blurNum(val, index) {
      console.log(val);
      if (val) {
        if (val == 0) {
          this.$message.error("目标值不能为0");
          this.form.targetList[index].targetValue = null;
        }
        if(!new RegExp('^([1-9]\\d*|0)(\\.\\d{1,2})?$').test(val)) {
          this.$message.error("请输入合法数值");
          this.form.targetList[index].targetValue = null;
        }
        let boolean = !new RegExp('^[0-9]\\d{0,9}(\\.\\d{1,2})?$').test(val);
        if (boolean) {
          this.$message.error("目标值上限为1,000,000,000");
          this.form.targetList[index].targetValue = null;
        }
      }
    },
    commit() {
      for (let i = 0; i < this.form.targetList.length; i++) {
        console.log(
          " this.form.targetList[i]",
          this.form.targetList[i].targetValue
        );
        if (
          this.form.targetList[i].targetValue === null ||
          this.form.targetList[i].targetValue == ""
        )
          return this.$message.error("目标值必填");
      }
      this.$emit("save", this.form);
    },
    close() {
      this.$emit("close");
    }
  }
};
</script>
<style lang="scss" scoped>
/deep/.el-dialog__body {
  padding-top: 10px;
}
/deep/.el-input {
  width: 150px;
}
.el-form-item {
  margin-bottom: 5px;
  margin-right: 50px;
}
.source {
  display: flex;
}
</style>
