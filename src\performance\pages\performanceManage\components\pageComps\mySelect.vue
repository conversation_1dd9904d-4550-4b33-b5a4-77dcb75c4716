<template>
  <div>
    <el-drawer
      title="选择考核指标"
      :visible.sync="openVisible"
      :before-close="cancelForm"
      size="630px"
      direction="rtl"
      custom-class="demo-drawer"
      ref="drawer"
    >
      <p class="tip" v-if="typeName">
        <i class="iconfont-per icon-shujuyichang"></i>考核类型为“{{
          typeName
        }}”，正在添加相关考核指标
      </p>
      <div class="search">
        <!-- <el-select v-model="params.region" placeholder="请选择考核指标">
          <el-option label="区域一" value="shanghai"></el-option>
          <el-option label="区域二" value="beijing"></el-option>
        </el-select> -->

        <el-cascader
          v-model="params.bankId"
          placeholder="考核指标分组"
          :options="treeData"
          :props="cascaderProps"
          style="width:298px"
          :show-all-levels="false"
          @change="handleChangeCascader"
        ></el-cascader>

        <el-input
          v-model="params.name"
          placeholder="请输入指标名称"
          suffix-icon="iconiconfonticonfontsousuo1 iconfont"
          class="search-input"
          @keydown.enter.native="getList(1)"
        ></el-input>
      </div>

      <el-radio-group
        v-model="params.type"
        class="radio"
        @change="handleChangeRadio"
      >
        <el-radio-button
          v-for="item in radios"
          :key="item.value"
          :label="item.value"
          >{{ item.label }}</el-radio-button
        >
      </el-radio-group>

      <old-table
        ref="table"
        :data="tableData"
        :headerData="headerData"
        :height="tableHeight"
        :typeOptions="typeOptions"
        :rowKey="rowKey"
        @selectChange="handleSelectChange"
        :isShowPagination="isShowPagination"
        :pageOptions="pageOptions"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
      ></old-table>

      <div class="drawer-footer">
        <span>已选择{{ selectList.length }}个</span>
        <div>
          <el-button @click="openVisible = false">取 消</el-button>
          <el-button
            type="primary"
            :disabled="selectList.length < 1"
            @click="commit"
            >确 定</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getIndicatorList, getIndicatorTree } from "performance/store/api.js";
import store from "performance/store";
import dd from "performance/utils/dataDictionary";
export default {
  props: {
    type: {
      type: Number,
      default: null
    },
    selectedList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      openVisible: false,
      table: false,
      loading: false,
      treeData: [],
      cascaderProps: {
        children: "children",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false
      },
      selectList: [],
      tableData: [],
      isShowPagination: true,
      tableHeight: document.body.clientHeight - 390 + "px",
      pageOptions: {
        currPage: 1, //当前页码
        total: 10, //数据总数
        pageSize: 10, //每页显示条数
        pageSizes: [10, 20, 50, 100] //每页显示个数选择器选项设置
      },

      headerData: [
        { title: "考核指标名称", label: "name", align: "left" },
        { title: "所属考核指标分组", label: "bankName", align: "left" }
      ],
      radio3: "全部类型",
      radios: [
        { label: "全部", value: 0 },
        { label: "定量考核指标", value: 1 },
        { label: "定性考核指标", value: 2 },
        { label: "加分项", value: 3 },
        { label: "减分项", value: 4 }
      ],

      typeOptions: {
        width: 80,
        label: "序号",
        type: "selection", // type类型支持：selection - 多选， index - 序号
        selectable: (row, index) => {
          const status = this.selectedList.some(it => it.id == row.id);
          console.log(status);

          return !status;
        }
      },
      baseInfo: JSON.parse(sessionStorage.getItem("baseInfo")),
      params: {
        bankId: null, //考核指标分组id
        name: null, //考核指标名称
        currentPage: 1,
        type: null, //考核类型:1-公司;2-部门;3-个人
        pageSize: 10,
        scoreType: 1
      },
      formLabelWidth: "120px",
      timer: null,
      typeName: ""
    };
  },
  watch: {
    type(val) {
      // console.log("val", val);
      // this.params.type = val;
      // this.getList();
    },
    selectList: {
      immediate: true,
      handler: function(val) {
        console.log("selectList", val);
      },
      deep: true
    },
    sectedList: {
      immediate: true,
      handler: function(val) {},
      deep: true
    }
  },
  created() {
    this.typeName = dd.checkType[this.$parent.basic.type];
    this.params.type = this.$parent.basic.type;
    this.getCatalogTree();
  },

  mounted() {
    window.onresize = () => {
      return (() => {
        const clientHeight = document.body.clientHeight - 390 + "px";
        this.tableHeight = clientHeight;
      })();
    };
  },
  methods: {
    rowKey(row) {
      return row.id;
    },
    handleSelectChange(val) {
      this.selectList = val;
    },

    openDialog() {
      this.openVisible = true;
    },

    //获取分类树
    async getCatalogTree() {
      let res = await getIndicatorTree();
      if (res.success) {
        if (this.$parent.basic.type) {
          res.data[0].children.map(item => {
            if (item.type == this.$parent.basic.type) {
              console.log("treeitem", item);
              this.treeData.push(item);
              this.params.bankId = item.id;
            }
          });
        } else {
          this.treeData = res.data[0].children;
        }
      }
      this.treeData = this.setChildren(this.treeData);
      this.getList();
    },
    //无children设置null
    setChildren(arr) {
      arr.map(item => {
        if (item.children.length === 0) {
          item.children = null;
        } else {
          this.setChildren(item.children);
        }
      });
      return arr;
    },

    //查询考核指标列表
    async getList(curr) {
      this.params.currentPage = curr || this.params.currentPage;
      const res = await getIndicatorList(this.params);
      if (res.success) {
        this.tableData = res.data.records;
        this.pageOptions.total = res.data.total;
      } else {
        this.$message.error(res.msg);
      }
      console.log(res);
    },

    handleChangeRadio() {
      this.params.currentPage = 1;
      this.pageOptions.currPage = 1;
      this.getList();
    },
    commit() {
      this.$emit("save", JSON.parse(JSON.stringify(this.selectList)));
      this.selectList = [];
      this.$refs.table.clearSelection();
      this.openVisible = false;
    },

    //分页size切换
    handleSizeChange(val) {
      console.log("当前size", val);
      this.params.pageSize = val;
      this.pageOptions.pageSize = val;
      this.params.currentPage = 1;
      this.pageOptions.currPage = 1;
      this.getList(1);
    },
    //页码切换
    handleCurrentChange(val) {
      console.log("当前页码", val);
      this.params.currentPage = val;
      this.pageOptions.currPage = val;
      this.getList();
    },
    handleChangeCascader(val) {
      this.getList();
      console.log(val);
    },

    cancelForm() {
      this.selectList = [];
      this.$refs.table.clearSelection();
      this.openVisible = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.el-input,
.el-select,
.el-cascader {
  width: 298px;
  line-height: 40px;
}
.tip {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6a6f7f;
  margin-bottom: 20px;
  .icon-shujuyichang {
    color: #9ea5bd;
    font-size: 20px;
    margin-right: 10px;
  }
}
.search {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
.radio {
  margin-bottom: 20px;
}
.search-input {
  width: 280px;
  /deep/.el-input__inner {
    padding: 0 12px;
  }
  /deep/.el-input__suffix {
    right: 12px;
  }
}

/deep/ .el-drawer {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  padding: 0 20px 80px;

  .el-drawer__header {
    color: #070f29;
    font-size: 16px;
    padding: 16px 0;
    margin-bottom: 20px;
    border-bottom: 1px solid #eaeaea;
    .el-drawer__close-btn {
      color: #6a6f7f;
    }
  }
  .el-radio-button__inner {
    height: 32px;
    line-height: 7px;
    font-size: 14px;
  }
}

.drawer-footer {
  position: absolute;
  display: flex;
  justify-content: space-between;
  align-items: center;
  bottom: 0;
  width: 590px;
  height: 80px;
  padding: 20px 0;
  background: #fff;
  border-top: 1px solid #eaeaea;
  box-sizing: border-box;
}
</style>
