<template>
  <div class="flow" v-loading="loading">
    <span class="info-title">考核确认流程</span>
    <p class="tip">
      <i class="iconfont-per icon-shujuyichang"></i>
      发起考核确认后，可安排指定人员对考核表进行修改确认
    </p>
    <el-form :model="form" label-width="110px">
      <el-form-item
        prop="name"
        label="确认节点"
        :rules="{
          required: true
        }"
      >
        <div class="sort-box">
          <draggable
            animation="150"
            v-model="formData.confirmProcess"
            @change="changeDragger(formData.confirmProcess)"
            class="draggable-container"
          >
            <el-row
              v-for="(item, index) in formData.confirmProcess"
              :key="index"
            >
              <div class="sort-item">
                <div class="left">
                  <div class="setp"></div>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="拖动调整排序"
                    placement="top"
                  >
                    <i class="iconfont-per icon-paixu"></i>
                  </el-tooltip>
                </div>
                <div class="right tl">
                  <h4>
                    确认人{{ index + 1 }}
                    <i
                      class="iconfont-per icon-bianji icon-blue"
                      @click="handleAddAffirm('confirmProcess', item, index)"
                    ></i>
                    <i
                      class="iconfont-per icon-shanchu icon-blue"
                      @click="delConfirmItem('confirmProcess', index)"
                    ></i>
                  </h4>
                  <div class="name ">
                    <img
                      v-if="item.processorType != 3"
                      :src="avatar"
                      alt=""
                    />
                    <el-avatar v-else :class="item.bgColor" :key="index"
                      >{{ item.name.substr(-2) }}
                    </el-avatar>

                    <span style="margin-left:10px">{{ item.name }} </span>
                  </div>
                </div>
              </div>
            </el-row>
          </draggable>

          <div class="sort-item add-item">
            <div class="left">
              <div class="setp" v-if="formData.confirmProcess.length"></div>
            </div>
            <div class="right tl">
              <img
                src="../../images/add_person.svg"
                class="add"
                @click="handleAddAffirm('confirmProcess')"
              />
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <span class="info-title">考核评分流程</span>
    <p class="tip">
      <i class="iconfont-per icon-shujuyichang"></i>
      设置本次考核流程的评分人及其相应评分规则
    </p>
    <div class="sort-item-page">
      <div class="sort-item">
        <div class="person-info">
          <el-row style="width:100%">
            <el-col :span="3" class="item-icon tc">
              评分节点
            </el-col>
            <el-col :span="4" class="item-icon">
              评分人
            </el-col>
            <el-col :span="4">
              评分权重
              <el-tooltip
                effect="dark"
                content="指定指标评分人没有评分权重，不能设置"
                placement="top"
              >
                <i class="iconfont-per icon-help"></i>
              </el-tooltip>
            </el-col>
            <el-col :span="4" style="display: flex;">
              评分方式
            </el-col>
            <el-col :span="4">
              可见内容
            </el-col>
            <el-col :span="4" class="operation-btn">
              操作
            </el-col>
          </el-row>
        </div>

        <draggable
          animation="150"
          v-model="formData.scoreProcess"
          @change="changeDragger(formData.scoreProcess)"
          class="draggable-container"
        >
          <el-row
            v-for="(el, index) in formData.scoreProcess"
            :key="index"
            style="margin-bottom:10px;"
          >
            <div v-if="Array.isArray(el)">
              <div v-for="(it, i) in el" :key="i">
                <el-col :span="3" class="item-icon tc ">
                  <!-- <div :class="{ 'none-step': index != 0 }" class="setp"></div> -->
                  <div
                    class="setp"
                    :class="{
                      'no-setp1': i != 0,
                      'no-line': status
                    }"
                  ></div>
                  <el-tooltip
                    v-if="!status"
                    class="item drag"
                    effect="dark"
                    content="拖动调整排序"
                    placement="top"
                  >
                    <i v-if="i == 0" class="iconfont-per icon-paixu "></i>
                  </el-tooltip>
                </el-col>
                <el-col :span="4" class="item-icon name">
                  <img
                    v-if="it.processorType != 3"
                    :src="avatar"
                    alt=""
                  />

                  <el-avatar v-else :class="it.bgColor" :key="i">{{
                    it.name.substr(-2)
                  }}</el-avatar>

                  <el-tooltip
                    v-if="it.name.length > 4"
                    :content="it.name"
                    placement="right"
                    effect="light"
                  >
                    <span style="margin-left:10px">{{
                      it.name.substr(0, 4) + "..."
                    }}</span>
                  </el-tooltip>
                  <span v-else style="margin-left:10px">{{ it.name }}</span>
                </el-col>
                <el-col :span="4">
                  <!-- 权重 -->
                  <div class="grid-content bg-purple">
                    {{ it.indicatorRange == 2 ? it.weight + "%" : "--" }}
                  </div>
                </el-col>
                <!-- 评分方式-->
                <el-col :span="4" style="display: flex;">
                  {{ indicatorRangeType[it.indicatorRange] }}
                </el-col>
                <el-col :span="4">
                  <!-- 可见内容 -->
                  <div class="grid-content bg-purple-light">
                    {{ visibleStatus[it.visibleType] }}
                  </div>
                </el-col>

                <el-col :span="4" class="operation-btn"> </el-col>
              </div>
            </div>
            <div v-else>
              <el-col :span="3" class="item-icon tc ">
                <!-- <div :class="{ 'none-step': index != 0 }" class="setp"></div> -->
                <div
                  class="setp"
                  :class="{
                    'no-setp': status && index != 0,
                    'no-line': status
                  }"
                ></div>
                <el-tooltip
                  v-if="!status"
                  class="item drag"
                  effect="dark"
                  content="拖动调整排序"
                  placement="top"
                >
                  <i class="iconfont-per icon-paixu "></i>
                </el-tooltip>
              </el-col>
              <el-col :span="4" class="item-icon name">
                <!-- 评分人 -->
                <!-- <el-avatar
                :class="colorList[parseInt(Math.random() * 4)]"
                :key="index"
                >{{ el.name.substring(0, 2) }}</el-avatar
              > -->

                <img
                  v-if="el.processorType != 3"
                  :src="avatar"
                  alt=""
                />
                <el-avatar v-else :class="el.bgColor" :key="index">{{
                  el.name.substr(-2)
                }}</el-avatar>

                <el-tooltip
                  v-if="el.name.length > 4"
                  :content="el.name"
                  placement="right"
                  effect="light"
                >
                  <span style="margin-left:10px">{{
                    el.name.substr(0, 4) + "..."
                  }}</span>
                </el-tooltip>
                <span v-else style="margin-left:10px">{{ el.name }}</span>
              </el-col>
              <el-col :span="4">
                <!-- 权重 -->
                <div class="grid-content bg-purple">
                  {{ el.indicatorRange == 2 ? el.weight + "%" : "--" }}
                </div>
              </el-col>
              <!-- 评分方式-->
              <el-col :span="4" style="display: flex;">
                {{ indicatorRangeType[el.indicatorRange] }}
              </el-col>
              <el-col :span="4">
                <!-- 可见内容 -->
                <div class="grid-content bg-purple-light">
                  {{ visibleStatus[el.visibleType] }}
                </div>
              </el-col>

              <el-col :span="4" class="operation-btn">
                <el-button
                  v-if="el.indicatorRange == 2"
                  type="text"
                  @click="handleScoreEdit(el, index, 'edit')"
                  >编辑</el-button
                >
                <el-button
                  v-if="el.indicatorRange == 2"
                  type="text"
                  @click="handleScoreDel(index)"
                  >删除</el-button
                >
              </el-col>
            </div>
          </el-row>
          <!-- <div
              class="add-item"
              v-if="item.length > 0 && item.some(it => it.processorType != 2)"
            >
              <el-row style="margin-bottom:10px">
                <el-col :span="3" class="item-icon tc ">
                  <div :class="{ 'none-step': index != 0 }" class="setp"></div>
                </el-col>
                <el-col :span="4" class="item-icon name">
                  <el-popover
                    placement="right"
                    width="190"
                    trigger="click"
                    class="add-item_box"
                  >
                    <el-button
                      v-if="!isReferrer"
                      type="text"
                      @click="handleAddScoreNode(1, indexs, 'add')"
                      >添加被考核人自评</el-button
                    >
                    <el-button
                      type="text"
                      @click="handleAddScoreNode(3, indexs, 'add')"
                      >添加他人评分</el-button
                    >

                    <el-button class="add" slot="reference"> +</el-button>
                  </el-popover>
                </el-col>
                <el-col :span="12"> </el-col>
              </el-row>
            </div> -->
        </draggable>

        <div class="add-item" v-if="!status">
          <el-row style="width:100%">
            <el-col :span="3" class="item-icon add-all">
              <div class="setp"></div>
            </el-col>
            <el-col :span="4" class="item-icon">
              <el-popover
                placement="right"
                width="140"
                trigger="click"
                class="add-item_box"
              >
                <el-button
                  type="text"
                  @click="handleAddScoreNode(1)"
                  v-if="!isReferrer"
                  >添加被考核人自评</el-button
                >
                <br v-if="!isReferrer" />
                <el-button type="text" @click="handleAddScoreNode(3)"
                  >添加他人评分</el-button
                >
                <br />
                <el-button type="text" @click="handleAddScoreNode(2)"
                  >添加上级评分</el-button
                >
                <div class="add" slot="reference">
                  <img src="../../images/add_person.svg" />
                </div>
              </el-popover>
            </el-col>
            <el-col :span="4">
              <span class="total" :class="{ warning: total != 100 }">
                {{ total }}%</span
              >
            </el-col>
            <el-col :span="12"> </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <span class="info-title">结果审核流程</span>
    <p class="tip">
      <i class="iconfont-per icon-shujuyichang"></i>
      发放考核结果后，可安排指定人员对考核结果进行审核
    </p>
    <el-form :model="form" label-width="110px">
      <el-form-item
        prop="name"
        label="审核节点"
        :rules="{
          required: true
        }"
      >
        <div class="sort-box">
          <draggable
            animation="150"
            v-model="formData.approveProcess"
            @change="changeDragger(formData.approveProcess)"
            class="draggable-container"
          >
            <el-row
              v-for="(item, index) in formData.approveProcess"
              :key="index"
            >
              <div class="sort-item">
                <div class="left">
                  <div class="setp"></div>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="拖动调整排序"
                    placement="top"
                  >
                    <i class="iconfont-per icon-paixu"></i>
                  </el-tooltip>
                </div>
                <div class="right tl">
                  <h4>
                    审核人{{ index + 1 }}
                    <i
                      class="iconfont-per icon-bianji icon-blue"
                      @click="handleAddAffirm('approveProcess', item, index)"
                    ></i>
                    <i
                      class="iconfont-per icon-shanchu icon-blue"
                      @click="delConfirmItem('approveProcess', index)"
                    ></i>
                  </h4>
                  <div class="name">
                    <img
                      v-if="item.processorType != 3"
                      :src="avatar"
                      alt=""
                    />
                    <el-avatar v-else :class="item.bgColor" :key="index">{{
                      item.name.substr(-2)
                    }}</el-avatar>
                    <span style="margin-left:10px">{{ item.name }}</span>
                  </div>
                </div>
              </div>
            </el-row>
          </draggable>

          <div class="sort-item add-item">
            <div class="left">
              <div class="setp" v-if="formData.approveProcess.length"></div>
            </div>
            <div class="right tl">
              <div class="add"></div>
              <img
                src="../../images/add_person.svg"
                class="add"
                @click="handleAddAffirm('approveProcess')"
              />
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <add-affirm
      :title="title"
      @getItem="getItem"
      :form="editData"
      ref="refAffirm"
      @clear="clear"
    ></add-affirm>
    <score-node
      @getItem="getScoreItem"
      :type="scoreProcessorType"
      :form="editScoreData"
      ref="refScore"
      @clear="clearScore"
    ></score-node>
  </div>
</template>
<script>
import { mapState } from "vuex";
import draggable from "vuedraggable";
import { sumCount, arrayUnique } from "performance/utils/util";
import AddAffirm from "./components/pageComps/AddAffirm";
import ScoreNode from "./components/pageComps/ScoreNode";
import dd from "performance/utils/dataDictionary.js";
import {
  setPlanProcess,
  getPlanProcess,
  getPlanDetail
} from "performance/store/api.js";

export default {
  props: {},
  components: {
    draggable,
    AddAffirm,
    ScoreNode
  },

  data() {
    return {
      loading: true,
      tableData: [],
      count: 0,
      height: "",
      colorList: ["blue", "green", "orange", "purple"],
      type: null,
      title: "新增确认节点",
      avatar:
        window.env.server_env === "boc" || window.env.server_env === "cgb"
          ? require("../../images/order-avatar.png")
          : require("../../images/defaultAvatar.png"),
      form: {
        name: "" //考核计划名称
      },
      confirmIndex: null, //考核确认当前节点
      scoreIndex: null,
      scoreChlidIndex: null,
      editScoreData: {},
      scoreProcessorType: null,
      total: 0,
      scoreType: null, //评分节点类型
      editData: {},
      isReferrer: false, //是否有被考核人
      scoreProcess: [], //临时评分流程列表
      visibleStatus: {
        2: "所有人的评分/评语",
        1: "仅自己的评分/评语"
      },
      levelType: dd.levelType,
      indicatorRangeType: {
        1: "对指定指标评分",
        2: "对所有考核指标评分"
      },
      status: false, //是否全部指定了评分人
      formData: {
        confirmProcess: [],
        scoreProcess: [
          //评分节点
        ],
        approveProcess: []
      },
      showScore: false
    };
  },
  computed: {
    ...mapState("salaryCalStore", {
      rouleId: "rouleId",
      sendBasicInfoForm: "basicInfoForm"
    })
  },
  watch: {
    "formData.scoreProcess": {
      immediate: true,
      handler: function(val) {
        this.isReferrer = val.some(it => it.processorType == 1);
        if (val.length) {
          console.log("this.referrer", this.isReferrer);
          const list = val.filter(item => item.indicatorRange == 2);
          console.log("list", list);
          this.total =
            list.length > 0 ? sumCount(list.map(it => Number(it.weight))) : 0;
        }else{
          this.total = 0
        }
        // val.forEach(item => {
        //   console.log("el>>", item.processorType);
        //   if (item.processorType == 1) {
        //     this.isReferrer = true;
        //   } else {
        //     this.isReferrer = false;
        //   }
        // });
      },
      deep: true
    },
    formData: {
      handler: function(val) {
        if (val) {
          if (JSON.stringify(val) == JSON.stringify(this.baseInfo)) {
            this.$emit("getStatus", true);
          } else {
            this.$emit("getStatus", false);
          }
        }
        this.count++;
      },
      deep: true
    }
    // count(val) {
    //   if (val > 1) {
    //     this.$emit("getStatus", false);
    //   } else {
    //     this.$emit("getStatus", true);
    //   }
    //   console.log("count", val);
    // }
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        const clientHeight = document.body.clientHeight + "px";
        this.height = clientHeight;
      })();
    };
  },

  created() {
    if (this.$route.query.planId || this.$parent.baseId) {
      this.getPlanDetail();
    }
  },
  methods: {
    async getPlanDetail() {
      const res = await getPlanDetail({
        planId: this.$route.query.planId || this.$parent.baseId
      });
      console.log("getPlanDetail", res);
      setTimeout(() => {
        this.loading = false;
      }, 300);

      if (res.success) {
        const planBaseInfo = res.data.basicInfo;
        const indicatorList = res.data.indicatorList;

        //是否全部设置了指定评分人
        // scoreType

        // const list = indicatorList.filter(it => it.scoreType == 1);
        this.status =
          indicatorList.length > 0 &&
          indicatorList.every(item => item.scorerId !== 0);
        console.log("status>>", this.status);

        if (!this.status) {
          //没有全部指定评分人
          const obj = res.data.process;

          if (obj.scoreProcess.length > 0) {
            Object.keys(obj).forEach(key => {
              console.log(obj[key]); // foo
              obj[key].map((item, index) => {
                item.bgColor = this.colorList[parseInt(Math.random() * 4)];
                switch (item.processorType) {
                  case 1:
                    item.name = "被考核人";
                    break;
                  case 2:
                    item.name = this.levelType[item.superiorLevel];
                    break;
                  case 3:
                    item.name = item.processorName;
                    break;
                }
                return item;
              });
            });
            let arr = [];
            obj.scoreProcess.forEach((it, index) => {
              if (it.indicatorRange == 1) {
                console.log("index", index, it.name);
                arr.push(it);
              }
            });
            if (arr.length > 0) {
              obj.scoreProcess.push(arr);
            }
            obj.scoreProcess = obj.scoreProcess.filter(
              item => item.indicatorRange != 1
            );
            obj.scoreProcess = obj.scoreProcess.sort(function(a, b) {
              return (
                (Array.isArray(a) ? a[0].nodeSort : a.nodeSort) -
                (Array.isArray(b) ? b[0].nodeSort : b.nodeSort)
              );
            });

            this.formData = obj;
            console.log("const obj", this.formData);
          } else {
            //所有指标指未定评分人,默认节点
            if (planBaseInfo.type == 1) {
              this.formData.scoreProcess = [
                {
                  nodeSort: 1,
                  processorId: null,
                  processorType: 1,
                  indicatorRange: 2,
                  superiorLevel: null,
                  weight: 0,
                  visibleType: 1,
                  name: "被考核人"
                }
              ];
            } else {
              this.formData.scoreProcess = [
                {
                  nodeSort: 1,
                  processorId: null,
                  processorType: 1,
                  indicatorRange: 2,
                  superiorLevel: null,
                  weight: 0,
                  visibleType: 1,
                  name: "被考核人"
                },
                {
                  nodeSort: 2,
                  processorId: null,
                  processorType: 2,
                  indicatorRange: 2,
                  superiorLevel: 1,
                  visibleType: 2,
                  weight: 100,
                  name: "直接上级"
                }
              ];
            }
            if (indicatorList.some(item => item.scorerId !== 0)) {
              //个别指标指定了评分人,默认显示在最后节点

              const arr = indicatorList.some(item => item.scorerId !== 0);
              const listUnique = arrayUnique(indicatorList, "scorerId");

              console.log("listUnique>>", listUnique);
              let obj2 = [];
              listUnique.forEach((item, index) => {
                if (item.scorerId !== 0) {
                  const obj1 = {
                    bgColor: this.colorList[parseInt(Math.random() * 4)],
                    processorId: item.scorerId,
                    indicatorRange: 1,
                    nodeSort: index + 1,
                    processorType: 3,
                    weight: null,
                    visibleType: 1,
                    name: item.scorerName
                  };
                  obj2.push(obj1);

                  // this.formData.scoreProcess.push(obj1);
                }
              });
              // console.log("obj2", obj2);
              this.formData.scoreProcess.push(obj2);
              console.log(
                "this.formData.scoreProcess",
                this.formData.scoreProcess
              );
            }
          }
        } else {
          const obj = res.data.process;
          if (obj.scoreProcess.length > 0) {
            Object.keys(obj).forEach(key => {
              console.log(obj[key]); // foo
              obj[key].map(item => {
                item.bgColor = this.colorList[parseInt(Math.random() * 4)];
                switch (item.processorType) {
                  case 1:
                    item.name = "被考核人";
                    break;
                  case 2:
                    item.name = this.levelType[item.superiorLevel];
                    break;
                  case 3:
                    item.name = item.processorName;
                    break;
                }
                return item;
              });
            });
            let arr = [];
            obj.scoreProcess.forEach((it, index) => {
              if (it.indicatorRange == 1) {
                console.log("index", index, it.name);
                arr.push(it);
              }
            });
            if (arr.length > 0) {
              obj.scoreProcess.push(arr);
            }

            obj.scoreProcess = obj.scoreProcess.filter(
              item => item.indicatorRange != 1
            );
            obj.scoreProcess = obj.scoreProcess.sort(function(a, b) {
              return (
                (Array.isArray(a) ? a[0].nodeSort : a.nodeSort) -
                (Array.isArray(b) ? b[0].nodeSort : b.nodeSort)
              );
            });

            this.formData = obj;
          } else {
            const listUnique = arrayUnique(indicatorList, "scorerId");
            listUnique.forEach((item, index) => {
              if (item.scorerId !== 0) {
                const obj1 = {
                  bgColor: this.colorList[parseInt(Math.random() * 4)],
                  processorId: item.scorerId,
                  indicatorRange: 1,
                  nodeSort: 1,
                  processorType: 3,
                  weight: null,
                  visibleType: 1,
                  name: item.scorerName
                };
                this.formData.scoreProcess.push(obj1);
              }
            });
          }
        }
        // }

        this.baseInfo = JSON.parse(JSON.stringify(this.formData));
      } else {
        this.$$message.error(res.msg);
      }
    },

    //新增节点
    handleAddAffirm(type, val, index) {
      console.log(type, val, index);
      switch (type) {
        case "confirmProcess":
          this.title = "新增确认节点";
          break;
        case "approveProcess":
          this.title = "新增审核节点";
          break;
      }
      if (val) {
        switch (type) {
          case "confirmProcess":
            this.title = "编辑确认节点";
            break;
          case "approveProcess":
            this.title = "编辑审核节点";
            break;
        }
        this.editData = val;
      }
      this.confirmIndex = index != undefined ? index : null;
      this.type = type;
      this.$refs.refAffirm.openDialog();
    },
    clear() {
      console.log("clear");
      this.editData = null;
      this.type = null;
    },
    clearScore() {
      this.scoreChlidIndex = null;
      this.scoreType = null;
      this.editScoreData = null;
    },
    //新增评分节点
    handleAddScoreNode(type, t) {
      console.log("type", type);
      this.scoreProcessorType = type;
      // this.scoreIndex = parent;
      this.editScoreData = null;
      this.scoreType = t;
      this.$refs.refScore.openDialog();
    },

    //编辑评分流程
    handleScoreEdit(el, index, type) {
      console.log(el, index);
      this.scoreChlidIndex = index;
      this.scoreType = type;
      this.editScoreData = el;
      this.$refs.refScore.openDialog();
    },

    getItem(val) {
      console.log("this.confirmIndex", this.confirmIndex);
      val.bgColor = this.colorList[parseInt(Math.random() * 4)];
      console.log(val);
      if (this.confirmIndex !== null) {
        this.formData[this.type].splice(this.confirmIndex, 1, val);
      } else {
        this.formData[this.type].push(val);
      }
      this.confirmIndex = null;
      this.type = null;

      // if (this.formData.confirmProcess.length) {
      //   this.formData.confirmProcess.forEach((item, index) => {
      //     console.log(item.LocalId == val.LocalId);
      //     if (item.LocalId == val.LocalId) {
      //       console.log(1);
      //       this.formData.confirmProcess.splice(index, 1, val);
      //     } else {
      //       console.log(2);
      //       this.formData.confirmProcess.push(val);
      //     }
      //   });
      // } else {
      //   console.log(3);
      //   this.formData.confirmProcess.push(val);
      // }
    },
    //删除评分节点

    handleScoreDel(index) {
      this.$confirm("删除后不可恢复，确认要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        iconClass: "iconfont-per icon-jingshi-qiangtishi1 icon-tishi",
        closeOnClickModal: false,
        closeOnPressEscape: false,
        beforeClose(action, instance, done) {
          if (action == "confirm") {
            instance.$refs["confirm"].$el.onclick = function(e) {
              e = e || window.event;
              console.log(e.detail);
              if (e.detail != 0) {
                done();
              }
            };
          } else {
            done();
          }
        }
      }).then(() => {
        this.formData.scoreProcess.splice(index, 1);
      });
    },
    //赋值评分节点
    getScoreItem(val) {
      val.bgColor = this.colorList[parseInt(Math.random() * 4)];
      if (this.scoreType == "edit") {
        this.formData.scoreProcess[this.scoreChlidIndex] = val;
        this.formData.scoreProcess = [...this.formData.scoreProcess];
      } else if (this.scoreType == "add") {
        this.formData.scoreProcess.push(val);
      } else {
        this.formData.scoreProcess.push(val);
      }

      this.scoreChlidIndex = null;
      this.scoreIndex = null;
      this.scoreType = null;
      this.scoreProcessorType = null;
      // this.formData.approveProcess.push(val);
    },

    delConfirmItem(type, index) {
      this.formData[type].splice(index, 1);
    },

    //排序
    changeDragger(data) {
      // console.log(data);
      // let sortMap = {};
      // data.forEach((item, index) => {
      //   sortMap[item["id"]] = index;
      // });
    },

    async checkFormData() {

      if (this.formData.confirmProcess.length == 0)
        return this.$message.error("请至少选择一个确认人");
      if (this.formData.scoreProcess.length == 0)
        return this.$message.error("评分流程至少配置一个评分节点");
      console.log(!this.status, this.total != 100, this.total);
      if (!this.status && this.total != 100)
        return this.$message.error("评分节点权重之和不是100%，请检查");


     
      this.scoreProcess = []
      Reflect.ownKeys(this.formData).forEach(key => {
        if (this.formData[key].length > 0) {
          this.formData[key].map((it, index) => {
            delete it.bgColor;
            if (this.status) {
              it.nodeSort = index + 1;
              if (Array.isArray(it)) {
                it.forEach(el => {
                  el.nodeSort = 1;
                  el.weight = 100;
                  delete it.bgColor;
                  this.scoreProcess.push(el);
                });
              }
            }
            // if (it.indicatorRange == 1) {           //   curr = index;
            //   console.log("index", index);
            //   it.nodeSort = 1;
            //   it.weight = 100;
            // }
            else {
              console.log("it", it);
              it.nodeSort = index + 1;
              if (Array.isArray(it)) {
                it.forEach(el => {
                  el.nodeSort = it.nodeSort;
                  delete it.bgColor;
                  this.scoreProcess.push(el);
                  console.log("this.formData[key]", this.scoreProcess);
                });
              }
            }
            if (key == "scoreProcess") {
              this.scoreProcess.push(it);
            }
          });
          this.scoreProcess.forEach((item, ins) => {
            if (Array.isArray(item)) {
              this.scoreProcess.splice(ins, 1);
              console.log("ins", ins);
            }
          });
        }
      });

      const res = await setPlanProcess({
        ...this.formData,
        scoreProcess: this.scoreProcess,
        planId: Number(this.$route.query.planId || this.$parent.baseId)
      });
      if (res.success) {
        this.$message({
          message: "保存成功",
          type: "success",
          duration: 1000
        });
        this.$emit("commit", { done: true });
        this.getPlanDetail();
        this.scoreProcess = [];
      } else {
        this.$message.error(res.msg);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.tc {
  text-align: center;
}
.tl {
  text-align: left;
}

.tr {
  text-align: right;
}
.flow {
  width: 1200px;
  margin: 30px auto;
  // overflow-y: scroll;
}
.icon-help {
  color: #9ea5bd;
}
.info-title {
  font-weight: 500;
  font-size: 16px;
  margin: 20px;
  color: #070f29;
  line-height: 18px;
  display: flex;
  align-items: center;
}
.info-title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 14px;
  background-color: $mainColor;
  border-radius: 1px;
  margin-right: 10px;
}
.tip {
  margin-left: 30px;
  margin-bottom: 20px;
  color: #6a6f7f;
  display: flex;
  align-items: center;
  font-size: 14px;
  .icon-shujuyichang {
    color: #9ea5bd;
    font-size: 16px;
    margin-right: 10px;
  }
}

.setp {
  width: 40px;
}
.setp:before {
  position: absolute;
  left: 40%;
  display: block;
  content: "";
  top: 32px;
  z-index: 1;
  width: 1px;
  height: 100%;
  background-color: #e1e6eb;
  transform: translateX(-50%);
}

.setp:after {
  position: absolute;
  left: 40%;
  display: block;
  content: "";
  top: 50%;
  z-index: 2;
  width: 13px;
  height: 13px;
  background-color: #fff;
  border: 1px solid #4f71ff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.none-step.setp:after {
  display: none;
}
.none-step.setp:before {
  height: 120%;
}
img {
  width: 48px;
  height: 48px;
}

.sort-box {
  width: 600px;
  .sort-item {
    display: flex;
    .left {
      position: relative;
      height: 95px;
      .setp:before {
        position: absolute;
        left: 0%;
        top: 25px;
      }
      .setp:after {
        position: absolute;
        left: 0%;
        top: 22%;
      }
      .icon-paixu {
        color: #d1d5dc;
        position: relative;
        left: 13px;
      }
    }
    .right {
      h4 {
        color: #888;
      }
      .icon-blue {
        color: $mainColor;
      }

      .name {
        display: flex;
        align-items: center;
      }
    }
  }
  .add-item {
    margin-top: 8px;
    cursor: pointer;
    img {
      transition: all 0.2s;
      &:hover {
        transform: scale(1.1);
      }
    }

    .setp:before {
      display: none;
    }
  }
}

.sort-item-page {
  padding: 0 30px;
  box-sizing: border-box;
  overflow: auto;
  .draggable-container {
    .el-row:nth-of-type(even) {
      background: rgba(245, 246, 247, 0.3);
    }
  }
  .sort-item {
    padding-bottom: 10px;
    margin-bottom: 20px;
    border: 1px solid #f5f6f7;
    // border-bottom: none;
    .no-setp.setp {
      display: none;
    }
    .no-setp1.setp:after {
      display: none;
    }
    .no-line.setp:before {
      display: none;
    }
  }
  .last-item {
    border-bottom: 1px solid #e8eaf3;
  }

  .name {
    display: flex;
    align-items: center;
  }
  .add-item_box {
    .add {
      cursor: pointer;
      width: 48px;
      height: 48px;
      padding: 0;
      border: none !important;
      &:hover {
        background-color: #fff;
      }
      img {
        transition: all 0.2s;
        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }

  .setp:before {
    position: absolute;
    left: 40%;
    display: block;
    content: "";
    top: 32px;
    z-index: 1;
    width: 1px;
    height: 120%;
    background-color: #e1e6eb;
    transform: translateX(-50%);
  }
  .add-all {
    .setp:before {
      display: none;
    }
  }

  .person-info {
    height: 40px;
    line-height: 40px;
    background: #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .title {
      display: inline-block;
      margin-left: 20px;
    }
  }

  .el-col {
    height: 50px;
    line-height: 50px;
  }
  .item-icon {
    padding-left: 20px;
    padding-right: 40px;
    position: relative;
  }

  .icon-paixu {
    color: #d1d5dc;
    position: relative;
    left: 20px;
  }

  .operation-btn {
    text-align: left;
    padding-right: 50px;
  }
  .el-icon-sort:before {
    color: #dddbdb;
  }

  .nameStyle {
    color: #4f71ff;
  }
  /deep/ .el-dropdown {
    color: #999;
    font-size: 18px;
  }
  .total {
    color: #ff9500;
    font-size: 14px;
    &.warning {
      color: #d6342a;
    }
  }
}

/deep/ .el-dropdown-menu__item {
  font-size: 12px !important;
}

/deep/ .el-avatar {
  width: 48px;
  height: 48px;
  line-height: 48px;
}


.blue {
  background: linear-gradient(122deg, #5486ff 0%, #4f71ff 100%);
}
.green {
  background: linear-gradient(134deg, #41ddb6 0%, #2bcda4 100%);
}
.orange {
  background: linear-gradient(135deg, #ffbc14 0%, #ff8300 100%);
}
.purple {
  background: linear-gradient(-44deg, #8b5feb 0%, #b095fe 100%);
}
</style>

<style>
.icon-tishi {
  font-size: 20px !important;
  color: #ff9b0e;
}
</style>
