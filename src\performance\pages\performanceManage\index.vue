<template>
  <div v-if="unlock" class="performance">
    <header class="per-header">
      <el-row type="flex" class="row">
        <span>考核管理</span>
        <el-button
          v-if="havePrivilege('kpi.performance.plan.add')"
          type="primary"
          size="medium"
          @click="add"
        >
          新增考核计划
        </el-button>
      </el-row>
    </header>
    <div class="section">
      <el-tabs
        v-model="activeName"
        class="section-tabs"
        @tab-click="handleClickTab"
      >
        <template v-for="item in tabs">
          <el-tab-pane :key="item.name" :label="item.label" :name="item.name">
          </el-tab-pane>
        </template>
      </el-tabs>
      <div class="check-staff-menu">
        <div style="display: flex; align-items: center">
          <el-button
            type="default"
            class="filter-btn"
            @click="handleShowScreening"
          >
            筛选
          </el-button>
          <el-input
            v-model="params.name"
            class="search-input"
            placeholder="请输入计划名称"
            suffix-icon="iconiconfonticonfontsousuo1 iconfont"
            @change="handleChange"
          ></el-input>
          <el-radio-group
            v-model="params.status"
            class="radio-group"
            @change="handleChangeRadio"
          >
            <el-radio-button
              v-for="item in radios"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <old-table
        :key="activeName"
        ref="table"
        v-loading="loading"
        :data="tableData"
        :headerData="headerData"
        :isShowOperation="isShowOperation"
        :operaOptions="operaOptions"
        :isAllowClick="isAllowClick"
        :isShowPagination="isShowPagination"
        :pageOptions="pageOptions"
        @operaClick="handleOperaClick"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
        @columnClick="handleOneClick"
      >
        <template slot="description" slot-scope="scope">
          <el-tooltip
            placement="top"
            :disabled="scope.msg.row.description.length < 70"
          >
            <div slot="content">
              {{ scope.msg.row.description }}
            </div>
            <p class="text">{{ scope.msg.row.description }}</p>
          </el-tooltip>
        </template>
      </old-table>
    </div>
    <!--筛选-->
    <el-dialog
      title="筛选"
      :visible.sync="isShowScreening"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleCloseScreening"
      width="650px"
      class="screen-dialog"
    >
      <el-form ref="screenForm" :model="params" label-width="130px">
        <el-form-item label="考核确认状态">
          <el-select v-model="params.confirmStatus" placeholder="请选择">
            <el-option
              v-for="item in confirmList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="考核对象">
          <el-select
            v-if="params.type == 1"
            v-model="params.subsidiaryId"
            placeholder="请选择"
          >
            <el-option
              v-for="item in companyList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-input
            v-if="params.type == 2"
            v-model="temporary2"
            placeholder="请选择"
            suffix-icon="el-icon-plus"
            @focus="handleFocusDepart()"
          ></el-input>

          <el-input
            v-if="params.type == 3"
            v-model="temporary1"
            placeholder="请选择"
            suffix-icon="el-icon-plus"
            @focus="handleFocusPerson()"
          ></el-input>
        </el-form-item>

        <el-form-item v-if="params.type != 3" label="关联人员">
          <el-input
            v-model="temporary1"
            placeholder="请选择"
            suffix-icon="el-icon-plus"
            @focus="handleFocusPerson()"
          ></el-input>
        </el-form-item>

        <el-form-item label="考核周期类型">
          <el-select v-model="params.period" placeholder="请选择">
            <el-option
              v-for="item in periodType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考核周期">
          <el-date-picker
            v-model="params.startDate"
            type="date"
            placeholder="开始日期"
            value-format="timestamp"
            :picker-options="startDatePicker"
          >
          </el-date-picker>
          -
          <el-date-picker
            v-model="params.endDate"
            type="date"
            placeholder="结束日期"
            value-format="timestamp"
            :picker-options="endDatePicker"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleSearch"> 确定 </el-button>
      </div>
    </el-dialog>
    <select-staff
      v-if="showDialogDept"
      :list="section"
      :select="selectStaffList"
      :isUser="isUser"
      :isOnly="true"
      :isDifferent="true"
      @close="showDialogDept = false"
      @commit="commit"
    ></select-staff>
    <user-select
      v-if="showDialogPerson"
      :list="section"
      :isOnly="true"
      :select="selectStaffList"
      :userList="userList"
      @close="showDialogPerson = false"
      @commit="commit"
    ></user-select>

    <!--发起考核确认-->
    <el-dialog
      title="发起考核确认"
      :visible.sync="isShowLaunchAssessment"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="420px"
      :show-close="true"
    >
      <def-launch-assessment :data="launchAssessmentData" />
      <div slot="footer">
        <el-button @click="isShowLaunchAssessment = false">取消</el-button>
        <el-button type="primary" @click="handleLaunchAssessmentConfirm">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 发放考核结果-弹框 -->
    <dlg-start
      :centerDialogVisible.sync="centerDialogVisible"
      :checkObjiectNum="checkObjiectNum"
      :wrongNum="wrongNum"
      :startId="2"
      @clickStart="clickStart"
      @close="close"
    ></dlg-start>
    <!-- 确认完成-弹框 -->
    <dlg-done
      :centerDialogVisible2.sync="centerDialogVisible2"
      :wrongDoneNum="wrongDoneNum"
      :startId="2"
      @clickStart2="clickStart2"
      @close="close"
    ></dlg-done>
  </div>
</template>
<script>
import store from 'store'
import {
  getPlanList,
  getSubsidiaryList,
  getDepartmentTree,
  planRemove,
  getIntegrity,
  getPlanStartConfirmCheck,
  getPlanStartConfirm,
  getCheckResult,
  getUnCompletedCount,
  getSendResult,
  getComplete,
  getCheck,
  getUserList,
} from "performance/store/api.js";
import dd from "performance/utils/dataDictionary.js";
import { date2Str, havePrivilege } from "performance/utils/util.js";
import SelectStaff from "./components/SelectStaff";
import defLaunchAssessment from "../launchAssessment/components/LaunchAssessment";
import dlgStart from "../performanceStart/components/dlgStart";
import dlgDone from "../performanceStart/components/dlgDone";
import UserSelect from "performance/pages/IndicatorsLibrary/components/UserSelect";
import commons from "@/mixins/commons";
// import store from "performance/store";
// import { mapState } from "vuex";

const init = () => {
  return {
    type: "1", //考核类型:1-公司;2-部门;3-个人
    confirmStatus: null, //确认状态:1-待确认;2-确认中;3-已确认;
    status: null, //计划状态:1-未开始;2-进行中;3-已完成;
    currentPage: 1,
    startDate: null,
    endDate: null,
    subsidiaryId: null, //核子公司（用工主体）id
    pageSize: 10,
    deptId: null, //部门id
    employee: null, //个人
    relatedEmployee: null, //公司
  };
};

export default {
  components: {
    SelectStaff,
    UserSelect,
    defLaunchAssessment,
    dlgStart,
    dlgDone,
  },
  mixins: [commons],
  data() {
    return {
      loading: true,
      havePrivilege,
      centerDialogVisible: false, //发放考核结果-弹框flag
      centerDialogVisible2: false, //确认完成-弹框flag
      checkObjiectNum: "", //考核对象总数
      wrongNum: "", //异常流程
      wrongDoneNum: null, //未完成考核数量
      selectStaffList: [],
      isShowLaunchAssessment: false,
      launchAssessmentLoading: false,
      planId: null,
      launchAssessmentData: {},
      data1: [
        {
          title: "考核计划",
          label: "name",
          align: "left",
          width: 250,
          fixed: "left",
        },
        { title: "考核类型", label: "typeStr", align: "left", width: 120 },
        { title: "考核对象", label: "examineeList", align: "left", width: 120 },
        {
          title: "关联人员",
          label: "relationList",
          align: "left",
          minWidth: 120,
        },
        {
          title: "考核周期类型",
          label: "periodStr",
          align: "left",
          width: 120,
        },
        {
          title: "考核周期",
          label: "periodTimeStr",
          align: "left",
          minWidth: 150,
        },
        {
          title: "考核计划说明",
          slot: "description",
          label: "description",
          minWidth: 200,
          align: "left",
        },
        {
          title: "考核确认状态",
          label: "confirmStatusStr",
          align: "left",
          width: 120,
        },
        { title: "考核状态", label: "statusStr", align: "left", width: 120 },
      ],
      data2: [
        {
          title: "考核计划",
          label: "name",
          align: "left",
          width: 300,
          fixed: "left",
        },
        { title: "考核类型", label: "typeStr", width: 140 },
        { title: "考核对象", label: "examineeList", width: 150 },
        {
          title: "考核周期类型",
          label: "periodStr",
          align: "left",
          width: 120,
        },
        {
          title: "考核周期",
          label: "periodTimeStr",
          align: "left",
          minWidth: 150,
        },
        {
          title: "考核计划说明",
          slot: "description",
          label: "description",
          align: "left",
          minWidth: 200,
        },
        {
          title: "考核确认状态",
          label: "confirmStatusStr",
          align: "left",
          width: 120,
        },
        { title: "考核状态", label: "statusStr", align: "left", width: 120 },
      ],
      headerData: [],
      showDialogPerson: false,
      showDialogDept: false,
      isUser: false,
      // data: [],
      startDatePicker: null,
      endDatePicker: null,
      section: [],
      // person: [],
      userList: [],
      isShowOperation: true, //是否显示操作列
      isShowPagination: true,
      tableHeight: document.body.clientHeight - 350 + "px",
      pageOptions: {
        currPage: 1, //当前页码
        total: 10, //数据总数
        pageSize: 10, //每页显示条数
        pageSizes: [10, 20, 50, 100], //每页显示个数选择器选项设置
      },
      operaOptions: {
        title: "操作", //名称
        align: "left",
        width: 250,
        fixed: "right", // right - 固定在右侧
        buttonList: [
          {
            title: "发起考核确认",
            isShow: (row, btn) => {
              return this.haveConfirmCheck && row.confirmStatus == 1
                ? true
                : false;
            },
          },
          {
            title: "考核确认详情",
            isShow: (row, btn) => {
              return this.haveConfirmDetail &&
                row.confirmStatus != 1 &&
                row.status == 1
                ? true
                : false;
            },
          },
          {
            title: "启动考核",
            isShow: (row, btn) => {
              return this.haveScoreStart &&
                row.confirmStatus == 3 &&
                row.status == 1
                ? true
                : false;
            },
          },
          {
            title: "考核详情",
            isShow: (row, btn) => {
              return this.haveDetail && row.status != 1 ? true : false;
            },
          },

          {
            title: "发放考核结果",
            isShow: (row, btn) => {
              return row.status == 2 ? true : false;
              // return true ? true : false;
            },
          },

          {
            title: "确认完成",
            isShow: (row, btn) => {
              return row.status == 2 ? true : false;
              // return true ? true : false;
            },
          },
          {
            title: "删除",
            isShow: (row, btn) => {
              return this.haveDelete ? true : false;
            },
          },
        ],
      },
      infolist: [],
      radio1: "全部",
      isShowScreening: false, //批量更新编辑权限
      tabs: [
        { label: "公司考核", name: "company" },
        { label: "部门考核", name: "department" },
        { label: "个人考核", name: "person" },
      ],
      tabKeys: {
        company: 1,
        department: 2,
        person: 3,
      },
      activeName: "company",
      radios: [
        { label: "全部", value: null },
        { label: "未开始", value: 1 },
        { label: "进行中", value: 2 },
        { label: "已完成", value: 3 },
      ],
      confirmList: [
        { label: "待确认", value: 1 },
        { label: "确认中", value: 2 },
        { label: "已确认", value: 3 },
      ],
      companyList: [
        // { value: "a", label: "A公司" },
        // { value: "b", label: "B公司" },
        // { value: "c", label: "C公司" }
      ], //考核对象公司列表
      periodType: [
        { label: "年度", value: 1 },
        { label: "半年度", value: 2 },
        { label: "季度", value: 3 },
        { label: "月度", value: 4 },
      ],
      tableData: [],
      isAllowClick: true,
      columnClickOptions: {
        column: 0, // 允许点击的列 - 对应列标
      },
      params: init(),
      temporary1: "",
      temporary2: "",
    };
  },
  // computed: {
  //   ...mapState({
  //     serchForm: state => store.state.serchForm
  //   })
  // },
  beforeRouteEnter: (to, from, next) => {
    console.log(from.path);
    if (
      from.path !== "/performance/setting" &&
      from.path !== "/performance/checkSetting"
    ) {
      // store.commit("SET_SEARCHFORM", init());
      sessionStorage.setItem("set_searchform", JSON.stringify(init()));
    }
    next();
  },
  created() {
    this.onLoad().then((data)=> {
      //操作权限
      this.haveConfirmCheck = havePrivilege("kpi.performance.plan.confirmCheck");
      this.haveConfirmDetail = havePrivilege(
        "kpi.performance.plan.confirmDetail"
      );
      this.haveScoreStart = havePrivilege("kpi.performance.plan.scoreStart");
      this.haveDetail = havePrivilege("kpi.performance.plan.detail");
      this.haveDelete = havePrivilege("kpi.performance.plan.delete");
    });
    
    // const activeName = store.get("__performance_manage_default_active_name")
    // if(activeName){
    //   this.activeName = activeName
    // }
   
  },

  mounted() {
    this.onLoad().then((data)=> {
      this.params = JSON.parse(sessionStorage.getItem("set_searchform"));
      this.pageOptions.currPage = this.params.currentPage;
      this.pageOptions.pageSize = this.params.pageSize;
      window.onresize = () => {
        return (() => {
          const clientHeight = document.body.clientHeight - 350 + "px";
          this.tableHeight = clientHeight;
        })();
      };
      this.headerData = this.data1;
      this.getList();
      this.startDatePicker = this.beginDate();
      this.endDatePicker = this.processDate();
    });
  },

  methods: {
    //新增计划
    add() {
      // store.commit("SET_SEARCHFORM", this.params);
      sessionStorage.setItem("set_searchform", JSON.stringify(this.params));
      this.$router.push("/performance/setting");
    },
    //打开筛选弹窗
    handleShowScreening() {
      this.getSubsidiaryList();
      this.getUserList();
      this.getDepartmentTree();
      this.isShowScreening = true;
    },

    //考核管理列表
    async getList(curr) {
      this.loading = true;

      this.params = {
        ...this.params,
        currentPage: this.pageOptions.currPage,
        pageSize: this.pageOptions.pageSize,
      };
      // console.log("this.params", this.params);

      const res = await getPlanList(this.params);
      this.loading = false;
      if (res.success) {
        this.pageOptions.total = res.data.total;
        this.tableData = this.handleArr(res.data.records) || [];
      } else {
        this.$message.error(res.msg);
      }
    },

    handleClickTab(val) {
      if(val && val.name){
        store.set("__performance_manage_default_active_name",val.name)
      }
      this.params.type = this.tabKeys[val.name];
      this.pageOptions.currPage = 1;
      this.params.status = null;
      this.handleReset();
      if (val.name !== "person") {
        this.headerData = this.data1;
      } else {
        this.headerData = this.data2;
      }
      this.$nextTick(() => {
        this.$refs["table"].doLayout();
      });
    },

    async getIntegrity(id, type) {
      const res = await getIntegrity({ planId: id });
      if (res.success) {
        if (type == 1) {
          this.handleLaunchAssessment(id);
        } else {
          this.$router.push(`/performance/startCheck?planId=${id}`);
        }
      } else {
        this.$message.error(res.msg);
      }
    },

    //获取员工树
    async getUserList() {
      const res = await getUserList();
      if (res.success) {
        this.userList = res.data;
        console.log(this.userList);
      } else {
        this.$message.error(res.msg);
      }
      console.log(res);
    },

    //获取部门树
    async getDepartmentTree() {
      const res = await getDepartmentTree();
      if (res.success) {
        this.section = res.data;
      } else {
        this.$message.error(res.msg);
      }
      console.log(res);
    },

    //获取考核类型为公司列表
    async getSubsidiaryList() {
      const res = await getSubsidiaryList();
      if (res.success) {
        this.companyList = res.data || [];
      } else {
        this.$message.error(res.msg);
      }
    },

    handleArr(arr) {
      arr.map((item) => {
        item.name = item.nameSuffix
          ? item.name + "-" + item.nameSuffix
          : item.name;
        item.typeStr = dd.checkType[item.type];
        item.periodStr = dd.periodType[item.period];
        item.confirmStatusStr = dd.confirmStatus[item.confirmStatus];
        item.statusStr = dd.affirmType[item.status];
        item.description = item.description || "--";

        if (item.examineeList && item.examineeList.length > 2) {
          const list = `${item.examineeList.slice(0, 2).join("，")}，等${
            item.examineeList.length
          }个`;
          item.examineeList = list;
        } else {
          item.examineeList = item.examineeList
            ? item.examineeList.join("，")
            : "--";
        }

        if (item.relationList && item.relationList.length > 3) {
          const list = `${item.relationList.slice(0, 3).join("，")}，等${
            item.relationList.length
          }人`;
          item.relationList = list;
        } else {
          item.relationList = item.relationList
            ? item.relationList.join("，")
            : "--";
        }
        item.periodTimeStr = date2Str(
          item.period,
          item.startDate,
          item.endDate
        );
        return item;
      });
      return arr;
    },

    handleCloseScreening() {
      this.isShowScreening = false;
    },

    handleFocusPerson() {
      // this.data = this.person;
      this.currentSelect = "person";
      this.selectStaffList = this.selectPerson;
      this.isUser = false;
      this.showDialogPerson = true;
    },
    handleFocusDepart() {
      // this.data = this.section;
      this.currentSelect = "department";
      this.selectStaffList = this.selectDepartment;
      this.isUser = true;
      this.showDialogDept = true;
    },

    commit(list) {
      console.log(list);
      console.log(this.params.type);
      if (list.length == 0)
        return this.$message.error(
          `请选择${
            this.currentSelect == "department" ? "考核对象" : "关联人员"
          }`
        );
      switch (this.params.type) {
        case "1":
          this.params.relatedEmployee = {
            employeeId: list[0].employeeId,
            subsidiaryId: list[0].subsidiaryId,
          };
          this.temporary1 = list[0].name;
          this.selectPerson = list;
          console.log(this.temporary1);
          break;
        case "2":
          if (this.currentSelect == "person") {
            this.params.relatedEmployee = {
              employeeId: list[0].employeeId,
              subsidiaryId: list[0].subsidiaryId,
            };
            this.temporary1 = list[0].name;
            this.selectPerson = list;
          }
          if (this.currentSelect == "department") {
            this.params.deptId = list[0].id;
            this.temporary2 = list[0].name;
            this.selectDepartment = list;
          }
          break;
        case "3":
          this.params.employee = {
            employeeId: list[0].employeeId,
            subsidiaryId: list[0].subsidiaryId,
          };
          this.temporary1 = list[0].name;
          this.selectPerson = list;
          break;
      }

      this.showDialogPerson = false;
      this.showDialogDept = false;
    },

    //第一列点击
    handleOneClick(row) {
      if (!havePrivilege("kpi.performance.plan.add")) return false;
      // store.commit("SET_SEARCHFORM", this.params);
      sessionStorage.setItem("set_searchform", JSON.stringify(this.params));
      if (row.confirmStatus == 1) {
        this.$router.push({
          path: `/performance/setting?planId=${row.id}`,
        });
      } else {
        this.$router.push(`/performance/checkSetting?planId=${row.id}`);
      }
    },

    handleReset() {
      const reset = {
        confirmStatus: null, //计划状态:1-未开始;2-进行中;3-已完成;
        startDate: null,
        period: null,
        endDate: null,
        subsidiaryId: null, //核子公司（用工主体）id
        deptId: null, //部门id
        employee: null, //个人
        relatedEmployee: null, //公司
      };
      this.params = { ...this.params, ...reset };
      this.temporary1 = "";
      this.temporary2 = "";
      this.selectPerson = [];
      this.selectDepartment = [];
      this.isShowScreening = false;
      this.getList();
    },

    handleSearch() {
      this.getList();
      this.isShowScreening = false;
    },

    handleChange() {
      this.pageOptions.currPage = 1;
      this.getList();
    },

    handleChangeRadio() {
      this.pageOptions.currPage = 1;
      this.getList();
    },

    //发起确认
    handleLaunchAssessment() {
      this.launchAssessmentLoading = true;
      let obj = {
        planId: this.planId,
      };
      getPlanStartConfirmCheck(obj)
        .then((res) => {
          const { errorNum, total } = res.data;
          this.launchAssessmentData = { errorNum, total };
          this.launchAssessmentLoading = false;
          this.isShowLaunchAssessment = true;
        })
        .catch((err) => {
          this.launchAssessmentLoading = false;
        });
    },
    //发起确认-确定
    handleLaunchAssessmentConfirm() {
      let obj = {
        planId: this.planId, //考核计划id
      };
      getPlanStartConfirm(obj)
        .then((res) => {
          this.isShowLaunchAssessment = false;
          this.$router.push({
            path: "/launch-assessment/detail",
            query: { planId: this.planId },
          });
        })
        .catch((err) => {
          this.isShowLaunchAssessment = false;
        });
    },

    handleOperaClick(btn, row, { $index }) {
      console.log(btn, "调试:", row, $index);
      this.planId = row.id;
      if (btn == "删除") {
        this.$confirm(
          "确认要删除吗？确认后该计划的相关数据将同步被删除，请谨慎操作",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            iconClass: "iconfont-per icon-jingshi-qiangtishi1 icon-tishi",
            closeOnClickModal: false,
            closeOnPressEscape: false,
            beforeClose(action, instance, done) {
              if (action == "confirm") {
                instance.$refs["confirm"].$el.onclick = function (e) {
                  e = e || window.event;
                  console.log(e.detail);
                  if (e.detail != 0) {
                    done();
                  }
                };
              } else {
                done();
              }
            },
          }
        ).then(async () => {
          const res = await planRemove({ id: row.id });
          if (res.success) {
            this.getList();
          } else {
            this.$message.error(res.msg);
          }
        });
      }
      if (btn == "发起考核确认") {
        this.getIntegrity(row.id, 1);
      }

      if (btn == "考核确认详情") {
        this.$router.push(`/launch-assessment/detail?planId=${row.id}`);
      }

      if (btn == "启动考核") {
        getCheck({ planId: row.id }).then((res) => {
          if (res.success) {
            const { errorNum, total } = res.data;
            this.checkObjiectNum = total;
            this.wrongNum = errorNum;
            if (total == 0) {
              this.$message.error("请至少选择一个考核对象，才能启动考核计划");
              return false;
            }
            this.getIntegrity(row.id, 2);
            // this.centerDialogVisible=true
          } else {
            this.$message.error(res.msg);
          }
        });
      }
      if (btn == "考核详情") {
        this.$router.push(`/performance/checkDetail?planId=${row.id}`);
      }

      if (btn == "发放考核结果") {
        getCheckResult({ planId: this.planId }).then((res) => {
          // console.log('点击发放考核结果', res)
          if (res.success) {
            const { errorNum, total } = res.data;
            if (total == 0) {
              this.$message("暂无需发放考核结果的考核对象");
              return;
            }
            this.checkObjiectNum = total;
            this.wrongNum = errorNum;
            this.centerDialogVisible = true;
          } else {
            this.$message.error(res.msg);
          }
        });
      }
      if (btn == "确认完成") {
        getUnCompletedCount({ planId: this.planId }).then((res) => {
          // console.log('点击确认完成', res)
          if (res.success) {
            const { count } = res.data;
            if (count == 0) {
              // this.$message.success("操作成功");
              this.clickStart2(false, 2);
            } else {
              this.wrongDoneNum = count;
              this.centerDialogVisible2 = true;
            }
          } else {
            this.$message.error(res.msg);
          }
        });
      }
    },

    // 发放考核结果-确定
    clickStart(e, id) {
      // console.log("确定id",id)
      id == 2 &&
        getSendResult({
          planId: this.planId,
        }).then((res) => {
          // console.log('发放考核结果-确定', res)
          if (res.success) {
            this.centerDialogVisible = e;
            this.$message.success(
              `已成功发放${this.checkObjiectNum}个考核对象的考核结果`
            );
            // 重新请求
            this.getList();
          } else {
            this.$message.error(res.msg);
          }
        });
    },
    // 确认完成-确定
    clickStart2(e, id) {
      // console.log("确定id",id)
      id == 2 &&
        getComplete({
          planId: this.planId,
        }).then((res) => {
          // console.log('发放考核结果-确定', res)
          if (res.success) {
            this.centerDialogVisible2 = e;
            this.$message.success("操作成功");
            // 重新请求
            this.getList();
          } else {
            this.$message.error(res.msg);
          }
        });
    },
    // 发放考核结果、确认完成-关闭弹窗
    close(e) {
      this.centerDialogVisible = e;
      this.centerDialogVisible2 = e;
    },

    //分页size切换
    handleSizeChange(val) {
      console.log("当前size", val);
      // this.params.pageSize = val;
      this.pageOptions.pageSize = val;
      // this.params.currentPage = 1;
      this.pageOptions.currPage = 1;
      this.getList();
    },
    //页码切换
    handleCurrentChange(val) {
      console.log("当前页码", val);
      // this.params.currentPage = val;
      this.pageOptions.currPage = val;
      this.getList();
    },

    beginDate() {
      const self = this;
      return {
        disabledDate(time) {
          if (self.params.endDate) {
            return new Date(self.params.endDate).getTime() < time.getTime();
          }
        },
      };
    },
    processDate() {
      const self = this;
      return {
        disabledDate(time) {
          if (self.params.startDate) {
            return new Date(self.params.startDate).getTime() > time.getTime();
          }
        },
      };
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.performance {
  padding: 0 20px;
  box-sizing: border-box;
  /*height: calc(100vh - 80px);*/
  .per-header {
    font-size: 16px;
    height: 61px;
    border-bottom: 1px solid #eaeaea;
    line-height: 61px;
    .row {
      justify-content: space-between;
      align-items: center;
    }
  }
  .section {
    padding-top: 10px;
    .section-tabs {
      /deep/.el-tabs__nav-wrap::after {
        display: none;
      }
    }
  }
  .radio-group {
    margin-left: 16px;
  }
  .el-input,
  .el-select {
    width: 200px;
  }
}
.text {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
}
/deep/ .el-tabs__header {
  margin: 0;
}
.search-input {
  width: 280px;
  /deep/.el-input__inner {
    padding: 0 32px 0 12px;
  }
  /deep/.el-input__suffix {
    right: 12px;
  }
}
/deep/.el-button--small {
  font-size: 14px;
}
</style>
<style>
.el-tabs__item {
  font-size: 16px;
  padding: 0 15px;
  color: #555555;
}
.el-tabs__active-bar {
  width: 65px;
  height: 3px;
}
.icon-tishi {
  position: absolute;
  top: 13px;
  font-size: 20px !important;
  color: #ff9b0e;
}
</style>
