<template>
  <div class="indicator" v-loading="loading">
    <div class="indicator-table" v-if="tableData.length">
      <old-table
        :data="tableData"
        :headerData="headerData"
        :isShowOperation="isShowOperation"
        :operaOptions="operaOptions"
        @operaClick="handleOperaClick"
      >
        <template slot="descriptionStr" slot-scope="scope">
          <el-tooltip
            placement="top"
            :disabled="scope.msg.row.descriptionStr.length < 50"
          >
            <div slot="content">
              {{ scope.msg.row.descriptionStr }}
            </div>
            <p class="text">{{ scope.msg.row.descriptionStr }}</p>
          </el-tooltip>
        </template>

        <template slot="scoreStandard" slot-scope="scope">
          <!-- <el-popover
            placement="top-start"
            trigger="hover"
            width="200"
            :disabled="scope.msg.row.scoreStandard.length < 50"
            :content="scope.msg.row.scoreStandard"
          >
            <p slot="reference" class="text">
              {{ scope.msg.row.scoreStandard }}
            </p>
          </el-popover> -->
          <el-tooltip
            placement="top"
            :disabled="scope.msg.row.scoreStandard.length < 50"
          >
            <div slot="content">
              {{ scope.msg.row.scoreStandard }}
            </div>
            <p class="text">{{ scope.msg.row.scoreStandard }}</p>
          </el-tooltip>
        </template>

        <template slot="maxScore-header">
          评分上限
          <el-tooltip effect="dark" placement="top">
            <div slot="content">加分项：加分上限<br />减分项：减分上限</div>
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template>
        
        <template slot="weightStr-header">
          考核指标权重
          <el-tooltip
            effect="dark"
            content="加分项、减分项没有考核指标权重，不能设置"
            placement="top"
          >
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template>
        <template slot="scoreType-header">
          评分方式
          <el-tooltip
            effect="dark"
            content="仅“定量考核指标”可设置"
            placement="top"
          >
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template>
        <!-- <template slot="targetList-header">
          目标值
          <el-tooltip
            effect="dark"
            content="仅“定量考核指标”可设置"
            placement="top"
          >
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template> -->

        <template slot="scoreType" slot-scope="scope">
          <div v-if="scope.msg.row.scoreType == 1">
            直接输入
          </div>
          <div v-if="scope.msg.row.scoreType == 2">
            <el-tooltip placement="top">
              <div slot="content">
                <p style="text-align:left">
                  <span>公式计算</span><br />
                  <span v-if="scope.msg.row.dataRuleType == 1"
                    >按实际完成值来算:</span
                  >
                  <span v-if="scope.msg.row.dataRuleType == 2"
                    >按目标达成率计算:</span
                  ><br />
                  <span
                    v-for="(dataRuleItem, index) in scope.msg.row.dataRuleList"
                    :key="index"
                  >
                    <span v-if="scope.msg.row.dataRuleType == 1"
                      >{{ index + 1 }}、{{
                        dataRuleItem.min + scope.msg.row.dataUnit
                      }}<span v-if="index !== 0">＜</span
                      ><span v-if="index == 0">≤</span>完成值≤{{
                        dataRuleItem.max + scope.msg.row.dataUnit
                      }}</span
                    ><span v-if="scope.msg.row.dataRuleType == 2"
                      >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                        v-if="index !== 0"
                        >＜</span
                      ><span v-if="index == 0">≤</span>目标达成率≤{{
                        dataRuleItem.max
                      }}%</span
                    >,得分：{{ dataRuleItem.score }}分;<br />
                  </span>
                </p>
              </div>
              <p class="text">
                <span>公式计算</span><br />
                <span v-if="scope.msg.row.dataRuleType == 1"
                  >按实际完成值来算:</span
                >
                <span v-if="scope.msg.row.dataRuleType == 2"
                  >按目标达成率计算:</span
                ><br />
                <span
                  v-for="(dataRuleItem, index) in scope.msg.row.dataRuleList"
                  :key="index"
                >
                  <span v-if="scope.msg.row.dataRuleType == 1"
                    >{{ index + 1 }}、{{
                      dataRuleItem.min + scope.msg.row.dataUnit
                    }}<span v-if="index !== 0">＜</span
                    ><span v-if="index == 0">≤</span>完成值≤{{
                      dataRuleItem.max + scope.msg.row.dataUnit
                    }}</span
                  ><span v-if="scope.msg.row.dataRuleType == 2"
                    >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                      v-if="index !== 0"
                      >＜</span
                    ><span v-if="index == 0">≤</span>目标达成率≤{{
                      dataRuleItem.max
                    }}%</span
                  >,得分：{{ dataRuleItem.score }}分;<br />
                </span>
              </p>
            </el-tooltip>
          </div>
        </template>

        <template slot="scorerName" slot-scope="scope">
          <p v-if="scope.msg.row.scoreType == 2" style="color:#ccc">
            系统评分
          </p>
          <p v-if="scope.msg.row.scoreType == 1">
            {{
              scope.msg.row.scorerName
                ? scope.msg.row.scorerName
                : scope.msg.row.scorerDataName || "--"
            }}
          </p>
        </template>
      </old-table>

      <div class="indicator-footer">
        <el-button type="text" @click="handleAdd(1)">
          <i class="iconfont-per icon-add1"></i>
          新增考核指标</el-button
        >
        <el-button class="text_btn" type="text" @click="handleSelect(null)">
          <i class="iconfont-per icon-ku1"></i>
          从考核指标库选择</el-button
        >
        <div class="total" :class="{ warning: total != 100 }">{{ total }}%</div>
      </div>
    </div>
    <div v-else>
      <div class="tao-yong">
        <el-popover
          :width="postList.length > 0 ? 300 : 200"
          trigger="click"
          @show="rotate = true"
          @hide="rotate = false"
          v-model="visible"
        >
          <div v-if="postList.length > 0">
            <div
              class="post-item"
              v-for="item in postList"
              :key="item.id"
              :title="item.name"
              @click="handlePost(item.id)"
            >
              {{ item.name }}
            </div>
          </div>
          <div v-else>
            暂无数据
          </div>
          <div slot="reference" class="taoyong-name">套用岗位模板 <div class="iconfont-per icon-Arrow21" :class="{rotate:rotate}"></div></div>
        </el-popover>
      </div>
      <div class="empty">
        <p class="indicator-tip">
          请根据需要考核的指标内容，点击添加对应的考核指标类型
        </p>
        <div class="indicator-tabs">
          <div
            class="indicator-tabs_item"
            v-for="(val, key) in options"
            :key="key"
          >
            <div class="indicator-tabs_pic " @click="handleAdd(key)">
              <img :src="val.url" alt="" />
            </div>
            <el-tooltip
              effect="dark"
              content="从考核指标库选择"
              placement="top"
            >
              <i class="iconfont-per icon-ku1" @click="handleSelect(key)"></i>
            </el-tooltip>
            <span style="margin-left:10px">{{ val.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <Add
      ref="addItem"
      :type="currentType"
      @getItem="getItem"
      :editInfo="editInfo"
      @clear="clear"
    ></Add>
    <Select
      ref="selectItem"
      @save="getSelect"
      :type="currentType"
      :selectedList="tableData"
      @clear="clearSelect"
    ></Select>
  </div>
</template>

<script>
import Add from "./components/pageComps/Add";
import Select from "./components/pageComps/Select";
import { sumCount, arrayUnique } from "performance/utils/util";
import dd from "performance/utils/dataDictionary.js";
import {
  setPlanIndicator,
  getPlanDetail,
  gePositionList,
  gePositionDetail
} from "performance/store/api.js";

export default {
  components: {
    Add,
    Select
  },
  data() {
    return {
      loading: true,
      currentType: null,
      planBaseInfo: {},
      currnetIndex: null,
      editInfo: {},
      total: 0,
      rotate:false,
      options: {
        1: { name: "定量考核指标", url: require("../../images/dingliang.png") },
        2: { name: "定性考核指标", url: require("../../images/dingxing.png") },
        3: { name: "加分项", url: require("../../images/jiafenxiang.png") },
        4: { name: "减分项", url: require("../../images/jianfenxiang.png") }
      },
      levelType: dd.levelType,

      formData: {
        addIndicatorList: [], //新增的指标列表
        removedIndicatorList: [], //删除的指标标识sign列表
        updatedIndicatorList: [] //编辑的指标列表
      },
      tableData: [],
      // tableHeight: document.body.clientHeight - 350 + "px",
      headerData: [
        {
          title: "考核指标名称",
          label: "name",
          align: "left",
          width: 180,
          fixed: "left"
        },
        { title: "考核指标类型", label: "typeStr", align: "left" },
        {
          title: "考核指标说明",
          label: "descriptionStr",
          slot: "descriptionStr",
          align: "left"
        },
        {
          title: "评价标准",
          label: "scoreStandard",
          slot: "scoreStandard",
          align: "left"
        },
        {
          title: "评分上限",
          label: "maxScore",
          slotHeader: "maxScore-header",
          align: "right",
          width: 120
        },
        {
          title: "考核指标权重",
          label: "weightStr",
          slotHeader: "weightStr-header",
          align: "right"
        },
        // {
        //   title: "目标值",
        //   label: "targetList",
        //   slotHeader: "targetList-header"
        // },
        {
          title: "评分方式",
          label: "scoreType",
          align: "left",
          slotHeader: "scoreType-header",
          slot: "scoreType"
        },
        {
          title: "考核指标评分人",
          label: "scorerName",
          slot: "scorerName",
          align: "left"
        }
      ],

      isShowOperation: true, //是否显示操作列
      operaOptions: {
        title: "操作", //名称
        align: "left",
        fixed: "right",
        width: 120, //宽度
        buttonList: [
          //按钮列表
          { title: "编辑" },
          { title: "删除" }
        ]
      },
      postList: [],
      visible: false
    };
  },
  watch: {
    tableData: {
      immediate: true,
      handler: function(val) {
        console.log(this.total, "this.total ");
        if (val.length) {
          const list = val.filter(it => it.type == 1 || it.type == 2);
          console.log(list);
          this.total =
            list.length > 0 ? sumCount(list.map(it => Number(it.weight))) : 0;
          console.log("this.total", this.total);
        }
        if (JSON.stringify(val) == JSON.stringify(this.baseList)) {
          this.$emit("getStatus", true);
        } else {
          this.$emit("getStatus", false);
        }
      },
      deep: true
    }
  },
  created() {
    if (this.$route.query.planId || this.$parent.baseId) {
      this.getPlanDetail();
      // this.getPlanIndicator();
    }
    this.gePositionList();
  },
  mounted() {},
  methods: {
    async getPlanDetail() {
      const res = await getPlanDetail({
        planId: this.$route.query.planId || this.$parent.baseId
      });
      console.log("getPlanDetail", res);
      setTimeout(() => {
        this.loading = false;
      }, 300);

      if (res.success) {
        this.planBaseInfo = res.data.basicInfo;
        this.tableData = this.handleList(res.data.indicatorList);
        this.baseList = JSON.parse(JSON.stringify(this.tableData));
      } else {
        this.$$message.error(res.msg);
      }
    },

    handleList(arr) {
      if (arr.length == 0) return [];
      arr.map(item => {
        item.typeStr = this.options[item.type].name;
        item.weightStr =
          item.type == 1 || item.type == 2 ? item.weight + "%" : "--";
        item.descriptionStr = item.description || "--";
        if (
          item.scorerData &&
          item.scorerData.length > 0 &&
          item.scorerName == ""
        ) {
          item.scorerData.map(it => {
            switch (it.processorType) {
              case 1:
                it.name = "被考核人";
                break;
              case 2:
                it.name = this.levelType[it.superiorLevel];
                break;
              case 3:
                it.name = it.processorName;
                break;
            }
            return it;
          });
          item.scorerDataName = item.scorerData.map(it => it.name).join("，");
        }

        return item;
      });

      return arr;
    },
    //新增指标
    handleAdd(val) {
      this.editInfo = {};
      this.currentType = Number(val);
      this.currnetIndex = null;
      this.$refs.addItem.openDialog();
    },
    handleSelect(val) {
      this.currentType = Number(val);
      this.$refs.selectItem.openDialog();
    },
    //获取新增/编辑考核指标
    getItem(val) {
      if (this.currnetIndex !== null) {
        this.tableData.splice(this.currnetIndex, 1, val);
        if (val.sign) {
          this.formData.updatedIndicatorList.push(val);
        }

        this.currnetIndex = null;
      } else {
        this.tableData.push(val);
      }
      this.tableData = this.handleList(this.tableData);
      this.editInfo = {};
    },
    getSelect(val) {
      let list = val;
      list = list.map(item => {
        //dataRuleType: 1; //计算规则:1-按实际完成值计算;2-按目标达成率计算
        if (item.type == 1 && item.dataRuleType == 2) {
          item.targetList = [];
          this.$message.warning("请完善考核指标");
        }
        if (item.dataRule) {
          item.dataRuleList = JSON.parse(JSON.stringify(item.dataRule));
          delete item.dataRule;
        }
        return item;
      });
      this.tableData = this.tableData.concat(this.handleList(list));
      // this.tableData = this.handleList(this.tableData);
    },
    clear() {
      console.log("clear");
      this.editInfo = {};
      this.currnetIndex = null;
    },
    clearSelect() {
      this.currentType = 5;
    },

    handleOperaClick(btn, row, { $index }) {
      console.log(btn, "调试:", row, $index);
      if (btn == "删除") {
        // this.$confirm("确定删除该指标？", "提示", {
        //   confirmButtonText: "确定",
        //   cancelButtonText: "取消",
        //   iconClass: "iconfont-per icon-jingshi-qiangtishi1 icon-tishi",
        //   closeOnClickModal: false,
        //   closeOnPressEscape: false,
        //   beforeClose(action, instance, done) {
        //     if (action == "confirm") {
        //       instance.$refs["confirm"].$el.onclick = function(e) {
        //         e = e || window.event;
        //         console.log(e.detail);
        //         if (e.detail != 0) {
        //           done();
        //         }
        //       };
        //     } else {
        //       done();
        //     }
        //   }
        // }).then(() => {

        // });
        this.tableData.splice($index, 1);
        if (row.sign) {
          this.formData.removedIndicatorList.push(row.sign);
        }
      }
      if (btn == "编辑") {
        this.editInfo = row;
        this.currnetIndex = $index;
        this.$refs.addItem.openDialog();
      }
    },
    //是否有重复指标名
    isRepeat(arr) {
      var hash = {};
      for (var i in arr) {
        if (hash[arr[i]])
          //hash 哈希
          return true;
        hash[arr[i]] = true;
      }
      return false;
    },

    //保存
    async checkFormData() {
      if (this.tableData.length == 0)
        return this.$message.error("至少设置一个考核指标");

      const allModified = this.tableData.every(
        it => it.type == 3 || it.type == 4
      );
      if (!allModified && this.total != 100)
        return this.$message.error("考核指标权重之和不是100%，请检查");

      const arr = this.tableData.map(it => it.name);
      if (this.isRepeat(arr))
        return this.$message.error("同一考核计划下，考核指标名称唯一");

      // if (this.tableData.every(item => item.scoreType == 2)) {
      //   return this.$message.error(
      //     "当前版本暂不支持评分方式全部为公式计算的指标"
      //   );
      // }
      for (let i = 0; i < this.tableData.length; i++) {
        const el = this.tableData[i];
        // console.log(
        //   "el.targetList>>>>",
        //   el.scoreType,
        //   el.dataRuleType,
        //   el.targetList,
        //   el.targetList.length == 0
        // );

        if (
          el.scoreType == 2 &&
          el.dataRuleType == 2 &&
          ((el.targetList.length > 0 &&
            !el.targetList.every(item => item.targetValue > 0)) ||
            el.targetList.length == 0)
        ) {
          return this.$message.error(`请设置"${el.name}"的目标值`);
        }
      }

      this.formData.addIndicatorList = [];

      console.log(this.formData);

      this.tableData.forEach(item => {
        if (!item.sign) {
          this.formData.addIndicatorList.push(item);
        }
      });

      this.formData.updatedIndicatorList = arrayUnique(
        this.formData.updatedIndicatorList,
        "sign"
      );

      const res = await setPlanIndicator({
        ...this.formData,
        planId: Number(this.$route.query.planId || this.$parent.baseId)
      });
      if (res.success) {
        this.$emit("commit", { done: true });
        this.$message({
          message: "保存成功",
          type: "success",
          duration: 1000
        });
        this.getPlanDetail();
        this.formData = {
          addIndicatorList: [], //新增的指标列表
          removedIndicatorList: [], //删除的指标标识sign列表
          updatedIndicatorList: [] //编辑的指标列表
        };
      } else {
        this.$message.error(res.msg);
      }
    },
    async gePositionList() {
      let res = await gePositionList({ name: "" });
      this.postList = res.data.records;
    },
    async handlePost(item) {
     
      const res = await gePositionDetail({
        id: item
      });
      if (res.success) {
        this.tableData = this.handleList(
          res.data.indicatorList.map(it => {
            if (it.type == 1 && it.dataRuleType == 2) {
              it.targetList = [];
            }
            return it;
          })
        );
        this.$message.success("套用成功");
        this.visible = false;
      } else {
        this.$message.error(res.msg);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.indicator {
  // min-width: 1200px !important;
  padding-bottom: 90px;
  overflow: scroll;
  .tao-yong {
    display: flex;
    width: 100%;
    margin-top: 22px;
    flex-direction: row-reverse;
    .taoyong-name {
      cursor: pointer;
      font-weight: Medium;
      display: flex;
      font-size: 14px;
      color: $mainColor;
      letter-spacing: 0;
      line-height: 16px;
      .icon-Arrow21 {
        margin-top: 1px;
        margin-left: 8px;
        font-size: 10px;
        transform: scale(0.7);  
       }
      .rotate { 
        margin-top: -1px;
        transform: scale(0.7) rotate(180deg);
      } 
    }
    
  }
  .empty {
    width: 840px;
    margin: 80px auto;
  }

  .indicator-tip {
    font-size: 14px;
    color: #888;
    text-align: center;
    margin-bottom: 40px;
  }
  .indicator-tabs {
    display: flex;
    justify-content: space-between;
  }
  .indicator-tabs_item {
    width: 160px;
    text-align: center;
    color: #555;

    .indicator-tabs_pic {
      width: 160px;
      height: 140px;
      margin-bottom: 25px;
      cursor: pointer;
      transition: all 0.5s;
      &:hover {
        transform: scale(1.2);
      }
    }
  }

  .indicator-table {
    min-width: 1280px !important;
    margin-top: 33px;
    position: relative;
  }
  .total {
    position: absolute;
    right: 32%;
    color: #ff9500;
    font-size: 14px;
    &.warning {
      color: #d6342a;
    }
  }
  .indicator-footer {
    display: flex;
    align-items: center;
    height: 50px;
    border: 1px solid #ebeef5;
    border-top: none;
    padding: 0 24px;
    font-size: 14px;
    .text_btn {
      margin-left: 30px;
    }
  }
  .iconfont-per {
    color: $mainColor;
    font-size: 12px;
  }
  .icon-help {
    color: #909399;
    font-size: 13px;
  }
  .text {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
  }
}
/deep/.el-button--primary {
  font-size: 12px;
}
/deep/.el-button--small{
  font-size: 14px;
}
.post-item {
  width: 300px;
  text-align: left !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  margin-bottom: 5px;
  &:hover {
    color: $mainColor;
  }
}
.cell {
  padding: 0 20px !important;
}
</style>
<style>
.icon-tishi {
  font-size: 20px !important;
  color: #ff9b0e;
}
</style>
