<template>
  <div class="resultSetting" v-loading="loading">
    <span class="info-title">考核结果被考核人可见内容</span>
    <p class="tip">
      <i class="iconfont-per icon-shujuyichang"></i>
      被考核人确认考核结果时，可以看到考核表的以下内容
      <el-popover placement="bottom" width="400" trigger="hover">
        <Example type="1"></Example>
        <i class="iconfont-per icon-tubiao1" slot="reference"></i>
      </el-popover>
    </p>

    <el-form :model="formData" label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="评分内容">
            <el-radio-group v-model="formData.visibleType">
              <el-radio :label="2">所有评分/评语</el-radio>
              <el-radio :label="3">自己+上级的评分/评语</el-radio>
              <el-radio :label="1">仅自己的评分/评语</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { setPlanResult, getPlanDetail } from "performance/store/api.js";
import Example from "./components/Example";
export default {
  components: {
    Example
  },
  data() {
    return {
      loading: true,
      formData: {
        visibleType: 3
      },
      count: 0
    };
  },
  watch: {
    formData: {
      handler: function(val) {
        if (val) {
          if (JSON.stringify(val) == JSON.stringify(this.baseInfo)) {
            this.$emit("getStatus", true);
          } else {
            this.$emit("getStatus", false);
          }
        }
      },
      deep: true
    }
  },
  created() {
    if (this.$route.query.planId || this.$parent.baseId) {
      this.getPlanDetail();
    }
    this.$emit("getStatus", true);
  },
  methods: {
    async getPlanDetail() {
      const res = await getPlanDetail({
        planId: this.$route.query.planId || this.$parent.baseId
      });
      setTimeout(() => {
        this.loading = false;
      }, 300);
      if (res.success) {
        this.formData.visibleType = res.data.resultSetting.visibleType;
        this.baseInfo = JSON.parse(JSON.stringify(this.formData));
      } else {
        this.$$message.error(res.msg);
      }
    },

    async checkFormData() {
      const res = await setPlanResult({
        ...this.formData,
        planId: Number(this.$route.query.planId || this.$parent.baseId)
      });
      if (res.success) {
        this.$emit("commit", { done: true });
        this.$message({
          message: "保存成功",
          type: "success",
          duration: 1000
        });

        this.getPlanDetail();
      } else {
        this.$message.error(res.msg);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.resultSetting {
  width: 1100px;
  margin: 30px auto;
}
.info-title {
  font-weight: 500;
  font-size: 16px;
  margin: 20px;
  color: #070f29;
  line-height: 18px;
  display: flex;
  align-items: center;
}
.info-title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 14px;
  background-color: $mainColor;
  border-radius: 1px;
  margin-right: 10px;
}

.tip {
  margin-left: 30px;
  margin-bottom: 30px;
  color: #6a6f7f;
  display: flex;
  align-items: center;
  font-size: 14px;
  .icon-shujuyichang {
    color: #9ea5bd;
    font-size: 20px;
    margin-right: 10px;
  }
  .icon-tubiao1 {
    color: #9ea5bd;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
