<template>
  <div class="flow">
    <span class="info-title">考核确认流程</span>
    <p class="tip">
      <i class="iconfont-per icon-jingshi-ruotishi1"></i>
      发起考核确认后，可安排指定人员对考核表进行修改确认
    </p>
    <el-form :model="form" label-width="110px">
      <el-form-item
        prop="name"
        label="确认节点"
        :rules="{
          required: true
        }"
      >
        <div class="sort-box">
          <draggable
            animation="150"
            v-model="formData.confirmProcess"
            @change="changeDragger(formData.confirmProcess)"
            class="draggable-container"
          >
            <el-row
              v-for="(item, index) in formData.confirmProcess"
              :key="index"
            >
              <div class="sort-item">
                <div class="left">
                  <div class="setp"></div>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="拖动调整排序"
                    placement="top"
                  >
                    <i class="iconfont-per icon-paixu"></i>
                  </el-tooltip>
                </div>
                <div class="right tl">
                  <h4>
                    确认人{{ index + 1 }}
                    <i
                      class="iconfont-per icon-bianji icon-blue"
                      @click="handleAddAffirm(item, index)"
                    ></i>
                    <i
                      class="iconfont-per icon-shanchu icon-blue"
                      @click="delConfirmItem(index)"
                    ></i>
                  </h4>
                  <div class="name ">
                    <el-avatar
                      :class="colorList[parseInt(Math.random() * 4)]"
                      :key="index"
                      >{{ item.name.substring(0, 2) }}</el-avatar
                    >

                    <span style="margin-left:10px">{{ item.name }}</span>
                  </div>
                </div>
              </div>
            </el-row>
          </draggable>

          <div class="sort-item add-item">
            <div class="left">
              <div class="setp"></div>
            </div>
            <div class="right tl">
              <div class="add" @click="handleAddAffirm()"></div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <span class="info-title">考核评分流程</span>
    <p class="tip">
      <i class="iconfont-per icon-jingshi-ruotishi1"></i>
      设置本次考核流程的评分人及其相应评分规则
    </p>
    <div class="sort-item-page">
      <div class="sort-item">
        <div class="person-info">
          <el-row style="width:100%">
            <el-col :span="3" class="item-icon tc"> 评分节点</el-col>
            <el-col :span="4" class="item-icon">
              评分人
            </el-col>
            <el-col :span="4">
              评分权重
              <i class="iconfont-per icon-help"></i>
            </el-col>
            <el-col :span="4" style="display: flex;">
              评分方式
            </el-col>
            <el-col :span="4">
              可见内容
            </el-col>
            <el-col :span="4" class="operation-btn">
              操作
            </el-col>
          </el-row>
        </div>

        <draggable
          animation="150"
          v-model="temporaryList"
          @change="changeDragger(temporaryList)"
          class="draggable-container"
        >
          <div v-for="(item, indexs) in temporaryList">
            <el-row
              v-for="(el, index) in item"
              :key="index"
              style="margin-bottom:10px;"
            >
              <el-col :span="3" class="item-icon tc ">
                <div :class="{ 'none-step': index != 0 }" class="setp"></div>
                <el-tooltip
                  v-if="index == 0"
                  class="item drag"
                  effect="dark"
                  content="拖动调整排序"
                  placement="top"
                >
                  <i class="iconfont-per icon-paixu "></i>
                </el-tooltip>
              </el-col>
              <el-col :span="4" class="item-icon name">
                <!-- 评分人 -->
                <el-avatar
                  :class="colorList[parseInt(Math.random() * 4)]"
                  :key="index"
                  >{{ el.name.substring(0, 2) }}</el-avatar
                >
                <span style="margin-left:10px">{{ el.name }}</span>
              </el-col>
              <el-col :span="4">
                <!-- 权重 -->
                <div class="grid-content bg-purple">{{ el.weight }} %</div>
              </el-col>
              <!-- 评分方式-->
              <el-col :span="4" style="display: flex;">
                对所有考核指标评分
              </el-col>
              <el-col :span="4">
                <!-- 可见内容 -->
                <div class="grid-content bg-purple-light">
                  {{ visibleStatus[el.visibleType] }}
                </div>
              </el-col>

              <el-col :span="4" class="operation-btn">
                <el-button
                  type="text"
                  @click="handleScoreEdit(el, index, indexs, 'edit')"
                  >编辑</el-button
                >
                <el-button type="text" @click="handleScoreDel(index, indexs)"
                  >删除</el-button
                >
              </el-col>
            </el-row>
            <div
              class="add-item"
              v-if="item.length > 0 && item.some(it => it.processorType != 2)"
            >
              <el-row style="margin-bottom:10px">
                <el-col :span="3" class="item-icon tc ">
                  <div :class="{ 'none-step': index != 0 }" class="setp"></div>
                </el-col>
                <el-col :span="4" class="item-icon name">
                  <el-popover
                    placement="right"
                    width="190"
                    trigger="click"
                    class="add-item_box"
                  >
                    <el-button
                      v-if="!isReferrer"
                      type="text"
                      @click="handleAddScoreNode(1, indexs, 'add')"
                      >添加被考核人自评</el-button
                    >
                    <el-button
                      type="text"
                      @click="handleAddScoreNode(3, indexs, 'add')"
                      >添加他人评分</el-button
                    >

                    <el-button class="add" slot="reference"> +</el-button>
                  </el-popover>
                </el-col>
                <el-col :span="12"> </el-col>
              </el-row>
            </div>
          </div>
        </draggable>

        <div class="add-item">
          <el-row style="width:100%">
            <el-col :span="3" class="item-icon add-all">
              <div class="setp"></div>
            </el-col>
            <el-col :span="4" class="item-icon">
              <el-popover
                placement="right"
                width="190"
                trigger="click"
                class="add-item_box"
              >
                <el-button
                  type="text"
                  @click="handleAddScoreNode(1)"
                  v-if="!isReferrer"
                  >添加被考核人自评</el-button
                >
                <el-button type="text" @click="handleAddScoreNode(3)"
                  >添加他人评分</el-button
                >
                <el-button type="text" @click="handleAddScoreNode(2)"
                  >添加上级评分</el-button
                >

                <el-button class="add" slot="reference">+</el-button>
              </el-popover>
            </el-col>
            <el-col :span="4">
              <span style="color:#FF9500"> 100%</span>
            </el-col>
            <el-col :span="12"> </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <span class="info-title">结果审核流程</span>
    <p class="tip">
      <i class="iconfont-per icon-jingshi-ruotishi1"></i>
      发放考核结果后，可安排指定人员对考核结果进行审核
    </p>
    <el-form :model="form" label-width="110px">
      <el-form-item
        prop="name"
        label="确认节点"
        :rules="{
          required: true
        }"
      >
        <div class="sort-box">
          <draggable
            animation="150"
            v-model="formData.confirmProcess"
            @change="changeDragger(formData.confirmProcess)"
            class="draggable-container"
          >
            <el-row
              v-for="(item, index) in formData.confirmProcess"
              :key="index"
            >
              <div class="sort-item">
                <div class="left">
                  <div class="setp"></div>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="拖动调整排序"
                    placement="top"
                  >
                    <i class="iconfont-per icon-paixu"></i>
                  </el-tooltip>
                </div>
                <div class="right tl">
                  <h4>
                    确认人{{ index + 1 }}
                    <i class="iconfont-per icon-bianji icon-blue"></i>
                    <i class="iconfont-per icon-shanchu icon-blue"></i>
                  </h4>
                  <div class="name">
                    <el-avatar
                      :class="colorList[parseInt(Math.random() * 4)]"
                      :key="index"
                      >{{ item.name.substring(0, 2) }}</el-avatar
                    >

                    <span style="margin-left:10px">{{ item.name }}</span>
                  </div>
                </div>
              </div>
            </el-row>
          </draggable>

          <div class="sort-item add-item">
            <div class="left">
              <div class="setp"></div>
            </div>
            <div class="right tl">
              <div class="add"></div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <add-affirm
      :showAffirm="showAffirm"
      @close="showAffirm = false"
      @getItem="getItem"
      :form="editData"
      ref="refAffirm"
    ></add-affirm>
    <score-node
      :showAffirm="showScore"
      @close="showAffirm = false"
      @getItem="getScoreItem"
      :type="scoreProcessorType"
      :form="editScoreData"
      ref="refScore"
    ></score-node>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { apiSalaryItemInfo } from "../../../modules/salaryCal/store/api";
import draggable from "vuedraggable";

import AddAffirm from "./components/pageComps/AddAffirm";
import ScoreNode from "./components/pageComps/ScoreNode";

function countTotal(arr, keyName) {
  let $total = 0;
  $total = arr.reduce(function(total, currentValue, currentIndex, arr) {
    return currentValue[keyName] ? total + currentValue[keyName] : total;
  }, 0);
  return $total;
}

export default {
  props: {},
  components: {
    draggable,
    AddAffirm,
    ScoreNode
  },

  data() {
    return {
      tableData: [],
      colorList: ["blue", "green", "orange", "purple"],
      form: {
        name: "" //考核计划名称
      },
      confirmIndex: null, //考核确认当前节点
      scoreIndex: null,
      scoreChlidIndex: null,
      editScoreData: {},
      scoreProcessorType: null,
      scoreType: null, //评分节点类型
      editData: {},
      isReferrer: true, //是否有被考核人
      temporaryList: [
        //临时评分流程列表
        [
          {
            nodeSort: 1,
            processorId: null,
            processorType: 1,
            superiorLevel: "",
            weight: 0,
            visibleType: 1,
            name: "被考核人"
          }
          // {
          //   nodeSort: 1,
          //   processorId: 2,
          //   processorType: "",
          //   superiorLevel: "",
          //   name: "明贺"
          // },
          // {
          //   nodeSort: 1,
          //   processorId: 4,
          //   processorType: "",
          //   superiorLevel: "",
          //   name: "JJ"
          // }
        ],
        [
          {
            nodeSort: 2,
            processorId: 3,
            processorType: 2,
            superiorLevel: 1,
            visibleType: 1,
            weight: 100,
            name: "直接上级"
          }
        ]
      ],
      visibleStatus: {
        1: "仅自己评分评语",
        2: "所有人的评分评语"
      },
      formData: {
        confirmProcess: [
          // {
          //   nodeSort: 1,
          //   processorId: 1,
          //   processorType: "",
          //   superiorLevel: "",
          //   name: "陈江"
          // }
          // {
          //   nodeSort: 2,
          //   processorId: 2,
          //   processorType: "",
          //   superiorLevel: "",
          //   name: "许江涛"
          // }
        ], //确认节点
        scoreProcess: [
          {
            nodeSort: 1,
            processorId: 1,
            processorType: "",
            superiorLevel: "",
            name: "陈江"
          },
          {
            nodeSort: 2,
            processorId: 2,
            processorType: "",
            superiorLevel: "",
            name: "许江涛"
          }
        ], //评分节点
        approveProcess: [
          {
            nodeSort: 1,
            processorId: 1,
            processorType: "",
            superiorLevel: "",
            name: "陈江"
          },
          {
            nodeSort: 2,
            processorId: 2,
            processorType: "",
            superiorLevel: "",
            name: "许江涛"
          }
        ] //审核节点
      },

      loading: false,
      showAffirm: false,
      showScore: false
    };
  },
  computed: {
    ...mapState("salaryCalStore", {
      rouleId: "rouleId",
      sendBasicInfoForm: "basicInfoForm"
    })
  },
  watch: {
    temporaryList: {
      handler: function(val) {
        val.forEach(item => {
          item.forEach(el => {
            console.log("el>>", el.processorType);
            if (el.processorType == 1) {
              this.isReferrer = true;
            } else {
              this.isReferrer = false;
            }
          });
        });
        console.log("this.referrer", this.isReferrer);
      },
      deep: true
    }
  },

  created() {
    this.ruleId = this.rouleId;
    this.ruleId = 1739;
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      this.tableData = [];

      apiSalaryItemInfo(this.ruleId)
        .then(res => {
          let tableData = res.data;
          this.tableData = tableData.filter(it => it);
          this.tableData = this.tableData.slice(0, 1);
          this.loading = false;
        })
        .catch(res => {});
    },

    //新增节点
    handleAddAffirm(val, index) {
      if (val) {
        this.editData = val;
        this.confirmIndex = index;
      }
      this.$refs.refAffirm.openDialog();
    },
    //新增评分节点
    handleAddScoreNode(type, parent, t) {
      console.log("type", type);
      this.scoreProcessorType = type;
      this.scoreIndex = parent;
      this.scoreType = t;
      this.$refs.refScore.openDialog();
    },

    //编辑评分流程
    handleScoreEdit(el, index, indexs, type) {
      console.log(el, index, indexs);
      this.scoreChlidIndex = index;
      this.scoreIndex = indexs;
      this.scoreType = type;
      this.editScoreData = el;
      this.$refs.refScore.openDialog();
    },

    getItem(val) {
      if (this.confirmIndex) {
        this.formData.confirmProcess.splice(this.confirmIndex, 1, val);
      } else {
        this.formData.confirmProcess.push(val);
      }
      this.confirmIndex = null;

      // if (this.formData.confirmProcess.length) {
      //   this.formData.confirmProcess.forEach((item, index) => {
      //     console.log(item.LocalId == val.LocalId);
      //     if (item.LocalId == val.LocalId) {
      //       console.log(1);
      //       this.formData.confirmProcess.splice(index, 1, val);
      //     } else {
      //       console.log(2);
      //       this.formData.confirmProcess.push(val);
      //     }
      //   });
      // } else {
      //   console.log(3);
      //   this.formData.confirmProcess.push(val);
      // }
    },
    //删除评分节点

    handleScoreDel(index, indexs) {
      this.$confirm("删除后不可恢复，确认要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        closeOnPressEscape: false
      }).then(() => {
        this.temporaryList[indexs].splice(index, 1);
      });
    },
    //赋值评分节点
    getScoreItem(val) {
      console.log(val);
      if (this.scoreType == "edit") {
        this.temporaryList[this.scoreIndex][this.scoreChlidIndex] = val;
        this.temporaryList = [...this.temporaryList];
        console.log(this.temporaryList);
      } else if (this.scoreType == "add") {
        this.temporaryList[this.scoreIndex].push(val);
        console.log(">>>>>", val);
      } else {
        this.temporaryList.push([val]);
      }

      this.scoreChlidIndex = null;
      this.scoreIndex = null;
      this.scoreType = null;

      // this.formData.approveProcess.push(val);
    },

    delConfirmItem(index) {
      this.formData.confirmProcess.splice(index, 1);
      this.$message.success("删除成功");
    },
    //删除工资表项
    deleteItem(id) {
      this.$confirm(
        "您确定删除工资项目，如果是，请点击“确定”，如果否，请点击“取消”?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        }
      )
        .then(() => {
          console.log("del");
        })
        .catch(() => {});
    },
    //工资表排序
    changeDragger(data) {
      // console.log(data);
      // let sortMap = {};
      // data.forEach((item, index) => {
      //   sortMap[item["id"]] = index;
      // });
    },
    //修改工资表项状态
    changeStatus(row) {},
    //显示新增薪资项
    salaryItemDetailShow(type, data) {
      data.salaryRuleId = this.ruleId;
      data.group = type;
      this.$store.commit("salaryCalStore/SET_SALARYCONFIGITEM", data);
      this.$router.push("/salaryCal/salaryItemDetail");
    }
  }
};
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";
.tc {
  text-align: center;
}
.tl {
  text-align: left;
}

.tr {
  text-align: right;
}
.flow {
  width: 1200px;
  margin: 30px auto;
  padding-bottom: 90px;
}

.info-title {
  font-weight: 500;
  font-size: 16px;
  margin: 20px;
  color: #070f29;
  line-height: 18px;
  display: flex;
  align-items: center;
}
.info-title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 14px;
  background-color: $mainColor;
  border-radius: 1px;
  margin-right: 10px;
}
.tip {
  margin-left: 30px;
  margin-bottom: 20px;
  color: #6a6f7f;
  display: flex;
  align-items: center;
  font-size: 14px;
  .icon-jingshi-ruotishi1 {
    font-size: 20px;
    margin-right: 10px;
  }
}

.setp {
  width: 40px;
}
.setp:before {
  position: absolute;
  left: 40%;
  display: block;
  content: "";
  top: 32px;
  z-index: 1;
  width: 1px;
  height: 100%;
  background-color: #e1e6eb;
  transform: translateX(-50%);
}

.setp:after {
  position: absolute;
  left: 40%;
  display: block;
  content: "";
  top: 50%;
  z-index: 2;
  width: 13px;
  height: 13px;
  background-color: #fff;
  border: 1px solid $mainColor;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.none-step.setp:after {
  display: none;
}
.none-step.setp:before {
  height: 120%;
}

.sort-box {
  width: 200px;
  .sort-item {
    display: flex;
    .left {
      position: relative;
      height: 95px;
      .setp:before {
        position: absolute;
        left: 0%;
        top: 25px;
      }
      .setp:after {
        position: absolute;
        left: 0%;
        top: 22%;
      }
      .icon-paixu {
        color: #d1d5dc;
        position: relative;
        left: 13px;
      }
    }
    .right {
      h4 {
        color: #888;
      }
      .icon-blue {
        color: $mainColor;
      }

      .name {
        display: flex;
        align-items: center;
      }
    }
  }
  .add-item {
    .setp:before {
      display: none;
    }
    .add {
      width: 44px;
      height: 44px;
      border: 2px solid #9ea5bd;
      border-radius: 50%;
    }
  }
}

.sort-item-page {
  padding: 0 30px;
  box-sizing: border-box;
  overflow: auto;
  .draggable-container {
    .el-row:nth-of-type(even) {
      background: rgba(245, 246, 247, 0.3);
    }
  }
  .sort-item {
    padding-bottom: 10px;
    margin-bottom: 20px;
    border: 1px solid #f5f6f7;
    // border-bottom: none;
  }
  .last-item {
    border-bottom: 1px solid #e8eaf3;
  }

  .name {
    display: flex;
    align-items: center;
  }
  .add-item_box {
    .add {
      width: 44px;
      height: 44px;
      border: 2px solid #9ea5bd;
      border-radius: 50%;
      text-align: center;
    }
  }

  .setp:before {
    position: absolute;
    left: 40%;
    display: block;
    content: "";
    top: 32px;
    z-index: 1;
    width: 1px;
    height: 120%;
    background-color: #e1e6eb;
    transform: translateX(-50%);
  }
  .add-all {
    .setp:before {
      display: none;
    }
  }

  .person-info {
    height: 40px;
    line-height: 40px;
    background: #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .title {
      display: inline-block;
      margin-left: 20px;
    }
    .person-info-fun {
      // float: right;
      margin-right: 20px;
      color: #4f71ff;
      cursor: pointer;
      .el-icon-document {
        color: #4f71ff;
        font-size: 16px;
        display: inline-block;
        margin-right: 10px;
      }
      .iconshouqi,
      .iconzhankai {
        color: #c0c4cc;
        font-size: 12px;
      }
    }
  }

  .el-col {
    height: 50px;
    line-height: 50px;
  }
  .item-icon {
    padding-left: 20px;
    padding-right: 40px;
    position: relative;
  }

  .icon-paixu {
    color: #d1d5dc;
    position: relative;
    left: 20px;
  }

  .operation-btn {
    text-align: left;
    padding-right: 50px;
  }
  .el-icon-sort:before {
    color: #dddbdb;
  }

  .nameStyle {
    color: #4f71ff;
  }
  /deep/ .el-dropdown {
    color: #999;
    font-size: 18px;
  }
}

/deep/ .el-dropdown-menu__item {
  font-size: 12px !important;
}

/deep/ .el-avatar {
  width: 48px;
  height: 48px;
  line-height: 48px;
}

.blue {
  background: linear-gradient(122deg, #5486ff 0%, #4f71ff 100%);
}
.green {
  background: linear-gradient(134deg, #41ddb6 0%, #2bcda4 100%);
}
.orange {
  background: linear-gradient(135deg, #ffbc14 0%, #ff8300 100%);
}
.purple {
  background: linear-gradient(-44deg, #8b5feb 0%, #b095fe 100%);
}
</style>
