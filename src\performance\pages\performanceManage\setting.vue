<template>
  <div class="setting">
    <header class="setting-header setting-title">
      <div class="back">
        <span @click="goBack" class="back-style"> 返回 </span>
        <span class="header-line">|</span>
        <el-tooltip v-if="baseInfo" placement="bottom" style="cursor: pointer">
          <span slot="content">
            <span>
              {{ baseInfo.name }}
              <span v-if="baseInfo.suffix">-{{ baseInfo.suffix }}</span>
              <span class="status"> {{ baseInfo.status }}</span></span
            >
          </span>
          <span class="text">
            {{
              baseInfo.name.length > 10
                ? baseInfo.name.substr(0, 10) + "..."
                : baseInfo.name
            }}
            <span class="status"> {{ baseInfo.status }}</span></span
          >
        </el-tooltip>
        <span v-else>新增考核计划</span>
      </div>

      <el-tabs
        class="setting-tabs"
        v-model="planName"
        :before-leave="beforeLeave"
        @tab-click="handleTabs"
      >
        <template v-for="item in tabs">
          <el-tab-pane :key="item.name" :label="item.label" :name="item.name">
          </el-tab-pane>
        </template>
      </el-tabs>
    </header>
    <div class="content">
      <basic
        v-if="planName == 'BASIC'"
        ref="refBasic"
        @commit="commit"
        @editName="editName"
        @getStatus="getStatus"
      ></basic>
      <indicator
        v-if="planName == 'INDICATOR'"
        ref="refIndicator"
        @commit="commit"
        @getStatus="getStatus"
      ></indicator>
      <result
        v-if="planName == 'RESULT'"
        ref="refResult"
        @commit="commit"
        @getStatus="getStatus"
      >
      </result>
      <flow
        v-if="planName == 'FLOW'"
        ref="refFlow"
        @commit="commit"
        @getStatus="getStatus"
      ></flow>
    </div>

    <div class="footer">
      <div class="footer-box">
        <el-button type="primary" @click="btn">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import basic from "./basic.vue";
import indicator from "./indicator.vue";
import result from "./result";
import flow from "./flow";

export default {
  components: {
    basic,
    indicator,
    result,
    flow,
  },
  data() {
    return {
      baseInfo: null,
      planName: "BASIC",
      loading: false,
      planId: null,
      baseId: null,
      done: false,
      planBaseInfo: {},
      tabs: [
        { label: "基础设置", name: "BASIC" },
        { label: "考核指标设置", name: "INDICATOR" },
        { label: "流程设置", name: "FLOW" },
        { label: "结果设置", name: "RESULT" },
      ],
    };
  },

  created() {
    this.planName = sessionStorage.getItem("planName") || "BASIC";
    this.baseId = sessionStorage.getItem("baseId") || "";
  },

  mounted() {
    this.planId = this.$route.query.planId;
  },
  beforeDestroy() {
    sessionStorage.removeItem("planName");
    sessionStorage.removeItem("baseId");
    sessionStorage.removeItem("baseInfo");
  },
  methods: {
    commit(val) {
      console.log("val>>", val);
      this.baseId = sessionStorage.getItem("baseId") || "";
      this.done = val.done;
      console.log(this.done);
    },
    add() {
      this.$router.push("/performance/add");
    },
    handleTabs(val) {
      sessionStorage.setItem("planName", this.planName);
    },

    goBack() {
      if (!this.done) {
        this.$confirm(" 已修改内容将丢失，是否先保存？", "提示", {
          cancelButtonText: "不保存",
          confirmButtonText: "保存",
          showClose: false,
          type: "warning",
          closeOnClickModal: false,
          closeOnPressEscape: false,
        })
          .then((_) => {
            this.btn();
          })
          .catch((_) => {
            this.$router.go(-1);
          });
      } else {
        this.$router.go(-1);
      }
    },
    getStatus(val) {
      this.done = val;
    },
    beforeLeave(acticeName, oldName) {
      console.log(
        "oldName",
        oldName,
        oldName == "BASIC" && !this.$route.query.planId && !this.baseId
      );
      if (oldName == "BASIC" && !this.$route.query.planId && !this.baseId) {
        if (this.done) {
          return true;
        } else {
          this.$message.error("请先保存当前页内容");
          return false;
        }
      } else {
        if (this.done) {
          return true;
        }

        return this.$confirm("已修改内容将丢失，是否先保存？", "提示", {
          distinguishCancelAndClose: true,
          cancelButtonText: "保存",
          confirmButtonText: "不保存",
          closeOnClickModal: false,
          closeOnPressEscape: false,
          cancelButtonClass: "el-button--primary confirm-cancel",
          confirmButtonClass: "confirm-save",
          iconClass: "iconfont-per icon-jingshi-qiangtishi1 icon-tishi",
        })
          .then(() => {
            console.log("不保存");
          })
          .catch((action) => {
            action == "cancel" ? this.btn() : null;
            throw new Error("取消成功！");
          });
      }
    },

    editName(val) {
      if (this.$route.query.planId) {
        this.baseInfo = val;
      }
      console.log("this.baseInfo>>>>>>>>>>>", this.baseInfo);
    },
    btn() {
      console.log("btn");
      const { refBasic, refFlow, refResult, refIndicator } = this.$refs;
      if (refBasic) {
        this.$refs.refBasic.checkFormData();
      }
      if (refResult) {
        refResult.checkFormData();
      }
      if (refIndicator) {
        refIndicator.checkFormData();
      }
      if (refFlow) {
        refFlow.checkFormData();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.setting {
  box-sizing: border-box;
  position: relative;
}

.setting-header {
  margin: 0 20px;
  font-size: 16px;
  height: 61px;
  border-bottom: 1px solid #ededed;
  line-height: 61px;
  position: relative;
  .back {
    position: absolute;
    top: 10px;
  }
  .status {
    background: #f1f1f1;
    color: #6a6f7f;
    padding: 4px 12px;
    font-size: 14px;
    margin-left: 10px;
    border-radius: 14px;
  }
}

.setting-title {
  box-sizing: border-box;
  padding-top: 16px;
  height: 61px;
  line-height: 45px;
  display: flex;
  justify-content: space-between;
}

.setting-tabs {
  margin: 0 auto;
  /deep/.el-tabs__nav-wrap::after {
    display: none;
  }
}

.content {
  padding: 0 20px;
  height: calc(100vh - 160px);
  overflow: auto;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  background: #fff;
  text-align: center;
  z-index: 9;
  &-box {
    margin: 0 20px;
    padding: 20px 0;
    border-top: 1px solid #e5e5e5;
  }
}

// .setting-btn {
//   background: #fff;
//   position: fixed;
//   bottom: 0;
//   width: calc(100% - 270px);
//   height: 96px;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   border-top: 1px solid #eaeaea;
//   z-index: 99;
// }
</style>

<style>
.confirm-save {
  font-size: 12px;
  background: #ffffff !important;
  border: 1px solid #dcdfe6;
  border-color: #dcdfe6 !important;
  color: #606266 !important;
}
.confirm-save:hover {
  background: #ffffff;
  border: 1px solid #dcdfe6;
  border-color: #dcdfe6;
  color: #606266;
  opacity: 0.8;
}

.confirm-cancel {
  float: right;
  margin-left: 10px;
}
.el-tabs__item {
  font-size: 16px;
  padding: 0 20px;
  color: #555555;
}

.el-tabs__active-bar {
  height: 3px;
}

.icon-tishi {
  font-size: 20px !important;
  color: #ff9b0e;
}
</style>
