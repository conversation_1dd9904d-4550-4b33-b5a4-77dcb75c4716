<template>
  <div class="inspectionSetup">
    <header class="per-header">
      <el-row type="flex">
        <el-col :span="12">
          <span class="back-style" @click="$router.go(-1)">返回</span>
          <span class="header-line">|</span>
          <span>个人绩效档案</span>
        </el-col>
      </el-row>
    </header>
    <div class="main">
      <def-card
        :cardPhoto="cardDataLineOne.name"
        :lineOne="cardDataLineOne"
        :lineTwo="cardDataLineTwo"
        :lineThree="cardDataLineThree"
        :isShowSteps="cardDataLineShowSteps"
      />
      <div class="main-ckass">
        <def-title class="tabs-title" text="考核记录结果" />
        <el-tabs v-model="activeName" class="section-tabs" @tab-click="handleClickTab">
          <template v-for="item in tabs">
            <el-tab-pane :key="item.label" :label="item.label" :name="item.name" :lazy="true">
              <!-- <tabs-for-wait v-if="item.name == activeName" :type="activeName"></tabs-for-wait> -->
              <old-table
                class="table-main"
                :maxHeight="tableHeight"
                :data="questionData"
                :headerData="headerData"
                :isShowOperation="isShowOperation"
                :operaOptions="operaOptions"
                @operaClick="handleOperaClick"
              >
                <template slot="planName" slot-scope="scope">
                  <el-popover
                    placement="top-start"
                    trigger="hover"
                    width="200"
                    :disabled="scope.msg.row.planName.length < 70"
                    :content="scope.msg.row.planName"
                  >
                    <p slot="reference" class="text" @click="handlePlan(scope.msg.row)">
                      {{ scope.msg.row.planName }}
                    </p>
                  </el-popover>
                </template>
              </old-table>
            </el-tab-pane>
          </template>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import { defCard, defTitle } from "../personalPerformance/components";
import { getarchivesDetail,getemployeeList } from "../../store/api";
import { date2Str } from "performance/utils/util.js";
const indicatorType = {
  1: "年度",
  2: "半年度",
  3: "季度",
  4: "月度",
  5:'自定义'
};
const bankList = {
  1: "公司考核",
  2: "部门考核",
  3: "个人考核"
};
const statusList={
  1: "未开始",
  2: "进行中",
  3: "已完成"
}
export default {
  components: {
    defCard,
    defTitle
  },
  data() {
    return {
      tabs: [
        { label: "个人考核", name: "grkh" },
        { label: "部门考核", name: "bmkh" },
        { label: "公司考核", name: "gskh" },
      ],
      activeName: "grkh",
      tableHeight: document.body.clientHeight - 320 + "px",
      cardDataLineOne: {
        name: "狂徒张三",
        phone: "***********",
        tag: ""
      },
      cardDataLineTwo: {
        label: "用工主体名称",
        value: "研发产品部"
      },
      cardDataLineThree: {
        label: "部门名称",
        value: "测试岗"
      },
      cardDataLineShowSteps: false,
      headerData: [
        { title: "考核对象", label: "examineeName", align: "left",fixed: "left",},
        { title: "所属考核计划", label: "planName", align: "left",slot: "planName",},
        {
          title: "考核类型",
          label: "type",
          align: "left",
          formatter: (row, column, cellVal) => {
            //formatter - 自定义单元格内容
            for (let ke in bankList) {
              if (ke == cellVal) {
                return bankList[ke];
              }
            }
          }
        },
        {
          title: "考核周期",
          label: "periodTimeStr",
          align: "left",
        },
        {
          title: "当前阶段",
          label: "status",
          align: "left",
          formatter: (row, column, cellVal) => {
            //formatter - 自定义单元格内容
            for (let ke in statusList) {
              if (ke == cellVal) {
                return statusList[ke];
              }
            }
          }
        },
        {
          title: "总评分",
          label: "score",
          align: "right"
        },
        {
          title: "绩效等级",
          label: "scoreLevel",
          align: "left"
        }
      ],
      isShowOperation: true, //是否显示操作列
      operaOptions: {
        title: "操作", //名称
        width: 100, //宽度
        fixed: "right", // right - 固定在右侧
        buttonList: [
          //按钮列表
          { title: "详情" } // type - 是否为 否定含义：表格里所有否定含义的操作都用红色，比如“删除”、“停用”、“撤销”、“拒绝”等等
        ]
      },
      questionData: [],
      total: 10,
      type:3,
    };
  },
  created(){
    this.getarchivesDetail()
    this.getemployeeList()
  },
  methods: {
    getarchivesDetail() {
      getarchivesDetail({id:this.$route.query.id}).then(res => {
        if (res.success) {
          this.cardDataLineOne={
            name: res.data.name,
            phone: res.data.mobile,
            tag: ""
          }
          this.cardDataLineTwo.value=res.data.subsidiaryName
          this.cardDataLineThree.value=res.data.deptName
        }
      });
    },
    getemployeeList(){
      getemployeeList({id:this.$route.query.id,type:this.type}).then(res => {
        if (res.success) {
          console.log(res.data);
          this.questionData=res.data.records
          this.questionData.map(item => {
            item.score = item.score || "--";
            item.scoreLevel = item.scoreLevel||"--";
            item.periodTimeStr = date2Str(
              item.period,
              item.startDate,
              item.endDate
            );
          });
          this.$forceUpdate()
        }
      });
    },
    handleClickTab(tab, event) {
      this.page = 1
      const { name } = tab
      if(name == "gskh"){
        this.type = 1
      }
      if(name == "bmkh"){
        this.type = 2
      }
      if(name == "grkh"){
        this.type = 3
      }
      this.getemployeeList()
    },
    handlePlan(value){
      console.log(value);
      this.$router.push({
        path: `/performance/checkSetting?planId=${value.planId}`
      })
    },
    handleOperaClick(btn, row) {
      console.log(row)
      if (btn == "详情") {
        const { examineePlanId,planId } = row
        this.$router.push({path: "/my-performance/comp-detail",query: { examineePlanId,planId }});
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.main {
  height: calc(100vh - 230px);
  overflow-y: auto;
  overflow-x: hidden;
  margin: 10px 0;
}
.inspectionSetup{
  padding: 0 20px;
}
.per-header {
  font-size: 16px;
  height: 61px;
  border-bottom: 1px solid #eaeaea;
  line-height: 61px;
  .row {
    justify-content: space-between;
    align-items: center;
  }
}
.main-title{
  margin-left: 10px;
}
.main-ckass {
  margin-top: 20px;
}
.table-main {
  margin-top: 20px;
}
.tabs-title{
  margin: 10px 0;
}
.text {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  color:$mainColor;
  cursor: pointer;
}
/deep/.el-tabs__header {
    padding: 0;
    position: relative;
    margin: 0 !important;
}
.section-tabs {
  /deep/.el-tabs__nav-wrap::after {
    display: none;
  }
}
/deep/.el-button--small{
  font-size: 14px;
}
</style>
