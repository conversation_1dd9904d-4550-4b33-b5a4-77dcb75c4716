<template>
  <div class="inspectionSetup def_per_height">
    <header class="per-header">
      <el-row type="flex">
        <el-col :span="12">
          <span style="fontsize: 16px">个人绩效档案</span>
        </el-col>
      </el-row>
    </header>
    <div class="main">
      <div class="search-main">
        <el-button
          type="default"
          class="filter-btn"
          @click="handleShowScreening"
          >筛选</el-button
        >
        <el-input
          v-model="searchForm.employeeName"
          @keydown.enter.native="fetchData(1)"
          placeholder="请输入员工姓名"
          suffix-icon="iconiconfonticonfontsousuo1 iconfont"
          class="search-input"
        ></el-input>
        <el-input
          v-model="deptName"
          placeholder="请选择部门"
          class="search-input"
          @focus="handleFocusDepart()"
          suffix-icon="el-icon-plus"
          @clear="handleDelete"
          clearable
        ></el-input>
      </div>
      <div class="questionTable">
        <old-table
          :data="questionData"
          :headerData="headerData"
          :isShowOperation="isShowOperation"
          :operaOptions="operaOptions"
          :isShowPagination="true"
          @operaClick="handleOperaClick"
          :pageOptions="pageOptions"
          @sizeChange="handleSizeChange"
          @currentChange="handleCurrentChange"
        >
          <template slot="employeeName" slot-scope="scope">
            <el-tooltip placement="top">
              <p slot="content">
                {{ scope.msg.row.employeeName }}
              </p>
              <p class="text1" @click="handleName(scope.msg.row)">
                {{ scope.msg.row.employeeName }}
              </p>
            </el-tooltip>
          </template>
          <template slot="planName" slot-scope="scope">
            <el-tooltip placement="top">
              <p slot="content">
                {{ scope.msg.row.planName }}
              </p>
              <p class="text" @click="handlePlan(scope.msg.row)">
                {{ scope.msg.row.planName }}
              </p>
            </el-tooltip>
          </template>
        </old-table>
      </div>
    </div>
    <el-dialog
      title="筛选"
      :visible.sync="isShowScreening"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleCloseScreening"
      width="500px"
      class="screen-dialog"
    >
      <el-form ref="screenForm" :model="searchForm" label-width="130px">
        <el-form-item label="公司名称">
          <el-select
            v-model="searchForm.subsidiaryId"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in subsidiaryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="员工状态">
          <el-select
            v-model="searchForm.employeeStatus"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in periodType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleSearch"> 确定 </el-button>
      </div>
    </el-dialog>
    <select-staff
      v-if="showDialog"
      :list="departmentList"
      :select="selectList"
      :isUser="true"
      :isOnly="true"
      :isDifferent="true"
      @close="showDialog = false"
      @commit="commit"
    ></select-staff>
  </div>
</template>

<script>
import {
  getarchivesList,
  getDepartmentTree,
  getSubsidiaryList,
} from "../../store/api";
import SelectStaff from "performance/pages/performanceManage/components/SelectStaff";
import { havePrivilege } from "performance/utils/util.js";
import { mapState } from "vuex";
import store from "../../store/index";
export default {
  components: {
    SelectStaff,
  },
  computed: {
    ...mapState({
      searchFormRecord: (state) => store.state.searchFormRecord,
    }),
  },
  beforeRouteEnter: (to, from, next) => {
    if (
      from.path !== "/performance/checkSetting" &&
      from.path !== "/performance/profileDetail"
    ) {
      store.commit("SET_SEARCHFORMRECORD", {
        deptId: "",
        currentPage: 1,
        employeeName: "",
        pageSize: 10,
        type: null,
        employeeStatus: 1,
        positionId: "",
        subsidiaryId: "",
      });
    }
    next();
  },
  data() {
    return {
      havePrivilege,
      isShowTooltip: true,
      tableHeight: document.body.offsetHeight - 300 + "px",
      searchForm: {
        deptId: "",
        currentPage: 1,
        employeeName: "",
        pageSize: 10,
        type: null,
        employeeStatus: 1,
        positionId: "",
        subsidiaryId: "",
      },
      deptName: "",
      periodType: [
        { label: "在职", value: 1 },
        { label: "离职", value: 2 },
      ],
      headerData: [
        {
          title: "姓名",
          label: "employeeName",
          align: "left",
          width: "100px",
          slot: "employeeName",
          fixed: "left",
        },
        {
          title: "公司名称",
          label: "subsidiaryName",
          align: "left",
          minWidth: "120px",
        },
        {
          title: "部门",
          label: "deptName",
          align: "left",
        },
        {
          title: "最近考核计划",
          label: "planName",
          align: "left",
          slot: "planName",
          minWidth: "120px",
        },
        {
          title: "最近总评分",
          label: "score",
          align: "right",
          minWidth: "120px",
        },
        {
          title: "最近绩效等级",
          label: "scoreLevel",
          align: "left",
          minWidth: "120px",
        },
        {
          title: "考核次数",
          label: "planNum",
          align: "right",
          minWidth: "120px",
        },
      ],
      isShowOperation: true, //是否显示操作列
      operaOptions: {
        title: "操作", //名称
        width: 100, //宽度
        fixed: "right", // right - 固定在右侧
        buttonList: [
          //按钮列表
          {
            title: "详情",
            isShow: (row, btn, scope) => {
              return havePrivilege("kpi.performance.archives.detail")
                ? true
                : false;
            },
          }, // type - 是否为 否定含义：表格里所有否定含义的操作都用红色，比如“删除”、“停用”、“撤销”、“拒绝”等等
        ],
      },
      pageOptions: {
        currPage: 1, //当前页码
        total: 10, //数据总数
        pageSize: 10, //每页显示条数
        pageSizes: [10, 20, 50, 100], //每页显示个数选择器选项设置
      },
      questionData: [],
      total: 10,
      departmentList: [],
      subsidiaryList: [],
      selectList: [],
      showDialog: false,
      isShowScreening: false,
    };
  },
  created() {
    if (this.searchFormRecord) {
      this.searchForm = JSON.parse(JSON.stringify(this.searchFormRecord));
      this.pageOptions.pageSize = this.searchForm.pageSize;
      this.pageOptions.currPage = this.searchForm.currentPage;
      delete this.searchForm.disName;
      if (this.searchFormRecord.disName) {
        this.deptName = JSON.parse(
          JSON.stringify(this.searchFormRecord.disName)
        );
      }
      this.fetchData();
    }
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        const clientHeight = document.body.clientHeight - 350 + "px";
        this.tableHeight = clientHeight;
      })();
    };
  },
  methods: {
    handleCloseScreening() {
      this.isShowScreening = false;
    },
    handleReset() {
      const reset = {
        deptId: this.searchForm.deptId,
        currentPage: 1,
        employeeName: this.searchForm.employeeName,
        pageSize: this.searchForm.pageSize,
        type: null,
        employeeStatus: 1,
        positionId: "",
        subsidiaryId: null,
      };
      this.searchForm = { ...reset };
      this.isShowScreening = false;
      this.fetchData(1);
    },
    handleSearch() {
      this.fetchData(1);
      this.isShowScreening = false;
    },
    //打开筛选弹窗
    handleShowScreening() {
      this.getSubsidiaryList();
      this.isShowScreening = true;
    },
    //获取考核类型为公司列表
    async getSubsidiaryList() {
      const res = await getSubsidiaryList();
      if (res.success) {
        this.subsidiaryList = res.data || [];
      } else {
        this.$message.error(res.msg);
      }
    },
    commit(list) {
      if (list && list.length > 0) {
        this.searchForm.deptId = list[0].id;
        this.deptName = list[0].name;
      } else {
        this.searchForm.deptId = null;
        this.deptName = null;
      }
      this.selectList = list;
      this.fetchData(1);
      // this.temporary2 = list[0].name;
      this.showDialog = false;
    },
    async handleFocusDepart() {
      const res = await getDepartmentTree();
      if (res.success) {
        // this.section = res.data;
        // this.data = this.section;
        this.departmentList = res.data;
      } else {
        this.$message.error(res.msg);
      }
      this.showDialog = true;
    },
    //列表搜索
    fetchData(curr) {
      console.log(this.searchFormRecord);
      this.searchForm.currentPage = curr || this.searchForm.currentPage;
      getarchivesList(this.searchForm).then((res) => {
        if (res.success) {
          this.pageOptions.total = res.data.total;
          this.questionData = res.data.records;
          this.questionData.map((item) => {
            item.score = item.score || "--";
            item.scoreLevel = item.scoreLevel || "--";
          });
        }
      });
    },
    handleDelete() {
      this.searchForm.deptId = "";
      this.selectList = [];
      this.fetchData(1);
    },
    //操作-点击事件
    handleOperaClick(btn, row) {
      if (btn == "详情") {
        this.searchForm.disName = this.deptName;
        store.commit("SET_SEARCHFORMRECORD", this.searchForm);
        this.$router.push({
          path: "/performance/profileDetail",
          query: { id: row.id },
        });
      }
    },
    handleSizeChange(val) {
      this.searchForm.pageSize = val;
      this.searchForm.currentPage = 1;
      this.pageOptions.pageSize = val;
      this.pageOptions.currPage = 1;
      this.fetchData(1);
    },
    handleCurrentChange(val) {
      this.searchForm.currentPage = val;
      this.pageOptions.currPage = val;
      this.fetchData();
    },
    handlePlan(value) {
      console.log(value);
      if (havePrivilege("kpi.performance.archives.last")) {
        this.searchForm.disName = this.deptName;
        store.commit("SET_SEARCHFORMRECORD", this.searchForm);
        this.$router.push({
          path: `/performance/checkSetting?planId=${value.planId}`,
        });
      }
    },
    handleName(value) {
      if (havePrivilege("kpi.performance.archives.detail")) {
        this.searchForm.disName = this.deptName;
        store.commit("SET_SEARCHFORMRECORD", this.searchForm);
        this.$router.push({
          path: "/performance/profileDetail",
          query: { id: value.id },
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.inspectionSetup {
  padding: 0 20px;
}
.per-header {
  font-size: 16px;
  height: 61px;
  border-bottom: 1px solid #eaeaea;
  line-height: 61px;
  .row {
    justify-content: space-between;
    align-items: center;
  }
}
.main {
  margin: 10px 0;
  padding: 15px 0;
}
.search-main {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.filter-btn {
  margin-right: 20px;
}
.text {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: $mainColor;
  cursor: pointer;
}
.text1 {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: $mainColor;
  cursor: pointer;
}
.search-input {
  width: 260px;
  margin-right: 20px;
  /deep/.el-input__inner {
    padding: 0 32px 0 12px;
  }
  /deep/.el-input__suffix {
    right: 12px;
  }
}

.main-title {
  margin-left: 10px;
}
/deep/.el-button--small {
  font-size: 14px;
}
</style>
