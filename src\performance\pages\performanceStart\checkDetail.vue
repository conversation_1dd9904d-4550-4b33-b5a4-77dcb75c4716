<template>
  <div class="wrapper-jx">
    <div class="back">
      <!-- 头部 -->
      <def-header 
        :headerText="def_HeaderData.headerText" 
        :isBack="true"
        :isShowTag="true"
        :headerTag="def_HeaderData.headerTag"
        :source="source"
      />
    </div>
    <!-- tab -->
    <el-tabs v-model="activeName" @tab-click="tabClick" stretch ref="tabs">
      <el-tab-pane label="考核对象" name="first">
        <!-- 内容区 -->
        <div class="content">
          <!-- 查询栏 -->
          <div class="search-box">
            <search-header
            :searchId="2" 
            :identityFlag="identityFlag"
            :options="options"
            :employeeName="employeeName"
            :deptId="deptId"
            :valueBm="valueBm"
            :subsidiaryId="subsidiaryId"
            :processStatus="processStatus"
            @statusChange="statusChange"
            @searchItemChange="searchItemChange"
            ></search-header>
          </div>
          <!-- 公司、部门的table -->
          <div class="table-list">
            <common-table 
              :loading="loading"
              :tableId='tableId'
              :identityFlag="identityFlag"
              :status="status"
              :screenHeight="screenHeight"
              :tableData="tableData"
              :headerData="identityFlag==3?headerData2:headerData"
              :pageOptions="pageOptions"
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              @requestOneMore="requestOneMore"
              :tableRef="ref1"
            ></common-table>
              <!-- :operaOptions="operaOptions" -->
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="实际完成数据" name="second">
        <!-- 内容区 -->
        <div class="content">
          <!-- 查询栏 -->
        <div class="search-box">
          <search-header
          :searchId="3" 
          :options="options"
          :identityFlag="identityFlag"
          :employeeName="employeeName"
          :deptId="deptId"
          :valueBm="valueBm"
          :num="num"
          :subsidiaryId="subsidiaryId"
          @searchItemChange="searchItemChange"
          ></search-header>
        </div>
          <!-- table -->
          <div class="temp-table">
            <!-- v-loading="loading" -->
            <el-table
              :data="tableData"
              border
              v-loading="loading"
              :height="screenHeight"
              :header-cell-style="{ background: '#f1f1f1' }"
              ref="table2"
            >
              <!-- 序号 -->
              <!-- <el-table-column label="序号" type="index" width="73" align="left" fixed="left">
              </el-table-column> -->
              <!-- 考核对象 -->
              <el-table-column prop="examineeName" label="考核对象" min-width="200" fixed="left" show-overflow-tooltip>
                <!-- 考核对象 -->
                <template slot="examineeName" slot-scope="scope">
                  <span>
                    <!-- {{scope.row.examineeName || '--'}}  -->
                    <span v-if="identityFlag==3" :class="[scope.row.employeeStatus!=1?'red':'']">
                      <!-- {{scope.row.examineeName||'--'}} -->
                      {{scope.row.employeeStatus==6?'':scope.row.examineeName||'--'}}
                      <span v-if="scope.row.employeeStatus!=1">{{ryztStatus2[scope.row.employeeStatus]}}</span>
                      <!-- {{scope.msg.row.employeeStatus==6?'':scope.msg.row.examineeName||'--'}} -->
                      <!-- <span v-if="scope.msg.row.employeeStatus!=1">{{ryztStatus2[scope.msg.row.employeeStatus]}}</span> -->
                    </span>
                    <span v-if="identityFlag!=3">
                      {{scope.row.examineeName||'--'}}
                    </span>
                    <!-- hover弹出提示 -->
                    <!-- <el-popover
                      placement="top"
                      width="200"
                      trigger="hover"
                      v-if="handleTips(scope.row)"
                      :content="tips">
                      <i class="el-icon-warning-outline" style="color:red;" slot="reference"></i>
                    </el-popover> -->
                  </span> 
                </template>
              </el-table-column>
              <!-- 关联人员 -->
              <el-table-column prop="relations" label="关联人员"  min-width="200" v-if="identityFlag!=3" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span  v-for="(item1,index1) in scope.row.relations" :key="index1">
                    <span :class="[item1.status!=1?'red':'']">
                      <!-- {{item1.name||"--"}} -->
                      {{item1.status==6?'':item1.name||'--'}}
                      <span v-if="item1.status!=1">{{ryztStatus2[item1.status]}}</span>
                    </span>
                    <span v-if="index1<scope.row.relations.length-1">、</span>
                  </span> 
                  <span v-if="scope.row.relations.length===0">--</span>
                </template>
              </el-table-column>
              <!-- 公司名称 -->
              <el-table-column prop="subsidiaryName" label="公司名称"  min-width="200" v-if="identityFlag==3" show-overflow-tooltip>
                <template slot="subsidiaryName" slot-scope="scope" v-if="identityFlag==3">
                  {{scope.row.subsidiaryName || '--'}}
                </template>
              </el-table-column>
              <!-- 部门 -->
              <el-table-column prop="deptName" label="部门"  min-width="200" v-if="identityFlag==3" show-overflow-tooltip>
                <template slot="deptName" slot-scope="scope" v-if="identityFlag==3">
                  {{scope.row.deptName || '--'}}
                </template>
              </el-table-column>
              <!-- 多级表头 -->
              <template v-if="handleIf(tableData,'1')">
                <el-table-column  v-for="(item2, index2) in tableData[0].indicatorCompletion" :key="index2+'-indicatorCompletion'" align="center" :label="item2.name||'--'">
                    <el-table-column  align="right" :label="'目标值'" min-width="150" v-if="handleIf(tableData[0].indicatorCompletion,'2',index2)" show-overflow-tooltip>
                      <template slot-scope="scope" v-if="scope.row.indicatorCompletion[index2]">
                          {{ handleValue(scope.row.indicatorCompletion[index2],scope.row.indicatorCompletion[index2].targetValue)||'--'}}
                      </template>
                    </el-table-column>
                    <el-table-column  align="right" :label="'实际完成值'" min-width="200" v-if="handleIf(tableData[0].indicatorCompletion,'2',index2)" show-overflow-tooltip>
                      <!-- v-if="handleIf(tableData[0].indicatorCompletion,'3')" -->
                      <template slot-scope="scope">
                        <span v-if="handleIf(scope,'3',index2)" class="completeValue-input">
                          <!-- <el-input v-model="scope.row.indicatorCompletion[index2].completeValue" placeholder="请输入" onInput="value=value.replace(/^(0+)|[^\d]+/g,'')" /> -->
                          <el-input v-model="scope.row.indicatorCompletion[index2].completeValue" placeholder="请输入" onInput="value=value.replace(new RegExp('[^\\d^\\.]+','g'),'').replace('.','$#$').replace(new RegExp('(\\.|\\。)','g'),'').replace('$#$','.');value=value.replace(new RegExp('0*(\\d+)'),'$1');if(value.includes('.')){value = value.replace(new RegExp('^(\\-)*(\\d+)\\.(\\d\\d).*$'),'$1$2.$3');}else{value=value.length>10?value.slice(0,10):value}">
                            <template slot="append" v-if="scope.row.indicatorCompletion[index2].dataUnit.length<=3">{{scope.row.indicatorCompletion[index2].dataUnit||''}}</template>
                            <template v-else slot="append">
                              <el-tooltip effect="dark" placement="top" :content="scope.row.indicatorCompletion[index2].dataUnit">
                                <span>
                                  {{scope.row.indicatorCompletion[index2].dataUnit.slice(0,3)+'...'}}
                                </span>
                              </el-tooltip>
                            </template>
                          </el-input>
                          <!-- <span class="data-unit">{{scope.row.indicatorCompletion[index2].dataUnit||''}}</span> -->
                        </span>
                        <span v-if="handleIf(scope,'4',index2)">
                          {{ handleValue(scope.row.indicatorCompletion[index2],scope.row.indicatorCompletion[index2].completeValue)}}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column  align="right" label="考核指标评分" min-width="150" v-if="handleIf(tableData[0].indicatorCompletion,'2',index2)" show-overflow-tooltip>
                      <template slot-scope="scope">
                        <span v-if="handleIf(scope,'5',index2)">
                          <!-- {{scope.row.indicatorCompletion[index2].score}} -->
                          {{handleInput(scope.row.indicatorCompletion[index2],scope.row.indicatorCompletion[index2].completeValue)}}
                        </span>
                        <span v-if="handleIf(scope,'6',index2)">
                          {{ handleValue2(scope.row.indicatorCompletion[index2],scope.row.indicatorCompletion[index2].score,scope) }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column  align="left" label="待处理人" min-width="130" v-if="showDM"  show-overflow-tooltip>
                      <template slot-scope="scope">
                        <!-- <el-tooltip effect="dark" class="tipsTxt" v-if="scope.row.indicatorCompletion[index2].dataMarker&&scope.row.indicatorCompletion[index2].dataMarker.length>0" :content="scope.row.indicatorCompletion[index2].dataMarker.name" placement="top"> -->
                          <span v-if="handleIf(scope.row.indicatorCompletion[index2].dataMarker,'7')">
                            <!-- <span v-if="item2.dataMarker && ((Object.keys(item2.dataMarker).length)>0)">
                              <span :class="[item2.dataMarker.status=='DISABLE'?'red':'']">
                                {{item2.dataMarker.status=='DISABLE'?(item2.dataMarker.name+"（已删除）"):item2.dataMarker.name}} 
                              </span>
                            </span> -->
                            <span :class="[scope.row.indicatorCompletion[index2].dataMarker.status!=1?'red':'']">
                              {{scope.row.indicatorCompletion[index2].dataMarker.employeeStatus==6?'':(scope.row.indicatorCompletion[index2].dataMarker.name||"--")}}
                              <span v-if="scope.row.indicatorCompletion[index2].dataMarker.status!=1">{{ryztStatus2[scope.row.indicatorCompletion[index2].dataMarker.status]}}</span>
                            </span>
                            <!-- <span v-if="!item2.dataMarker.name">--</span> -->
                          </span>
                        <!-- </el-tooltip> -->
                        <!-- <div v-if="!handleIf(scope.row.indicatorCompletion.dataMarker,'7')">--</div> -->
                      </template>
                    </el-table-column>
                </el-table-column>
              </template>
              <!-- 操作 -->
              <el-table-column label="操作" width="150" fixed="right" v-if="showBtn">
                <template slot-scope="scope" v-if="scope.row.scoreStatus!=3">
                  <!-- <old-button tag="span" type="text" 
                    v-if="showBG(scope)" 
                    @click="handleOperaClick3('变更',scope.row,scope.$index)"
                  >变更</old-button> -->
                  <old-button tag="span" type="text" 
                    v-if="activeIndex == scope.$index && changeButton" @click="handleOperaClick3('取消',scope.row,scope.$index)"
                  >取消</old-button>
                  <old-button tag="span" type="text" 
                    v-if="activeIndex == scope.$index && changeButton" @click="handleOperaClick3('确定',scope.row,scope.$index)"
                  >确定</old-button>
                  <old-button tag="span" type="text" 
                    @click="handleOperaClick3('代录入',scope.row,scope.$index)" v-if="showDLR(scope)"
                  >代录入</old-button>
                </template>
              </el-table-column>
            </el-table>

            <section class="temp-table-pagination">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :page-sizes="pageOptions.pageSizes"
                :page-size="pageOptions.pageSize"
                layout="total, prev, pager, next,sizes,jumper"
                :total="pageOptions.total"
                background
              >
              </el-pagination>
            </section>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="考核结果" name="third">
        <!-- 内容区 -->
        <div class="content">
          <!-- 等级选择 -->
          <div class="status-num">
            <div class="outside-box">
              <div :class="['status-num-item-all',isActive==='ALL'?'active':'']" @click="handleStatusNum('ALL')">
                全部<span>{{allLevelNum}}</span>个
              </div>
            </div>
            <div :class="['outside-box outside-box-item',isActive==(item.levelName)?'active':'']" @click="handleStatusNum(item.levelName)" v-for="item in levelArr" :key="item.levelName">
              <div class="status-num-item">
                <div class="status-num-item-top" v-if="item.levelName.length<=3">
                  等级<span>{{item.levelName}}</span>
                </div>
                <el-tooltip v-else effect="dark" placement="top" :content="item.levelName">
                  <div class="status-num-item-top">
                    等级<span>{{item.levelName.slice(0,3)+'...'}}</span>
                  </div>
                </el-tooltip>
                
                <div class="status-num-item-bottom">
                  {{item.levelNum}}个
                </div>
              </div>
            </div>
          </div>
          <!-- 查询栏 -->
          <div class="search-box">
            <search-header 
            :searchId="4" 
            :identityFlag="identityFlag" 
            :options="options"
            :employeeName="employeeName"
            :deptId="deptId"
            :valueBm="valueBm"
            :subsidiaryId="subsidiaryId"
            :processStatus2="processStatus2"
            :status="status"
            @statusChange2="statusChange2"
            @searchItemChange="searchItemChange"
            @start="start"
            @start2="start2"
            :showFF="showFF"
            ></search-header>
          </div>
          <!-- 考核结果的table -->
          <div class="table-list">
            <common-table 
              :loading="loading"
              :tableId='tableId2'
              :identityFlag="identityFlag"
              :status="status"
              :screenHeight="screenHeight3"
              :tableData="tableData"
              :headerData="identityFlag==3?headerData4:headerData3"
              :pageOptions="pageOptions"
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              @requestOneMore="requestOneMore"
              :tableRef="ref3"
            ></common-table>
              <!-- :operaOptions="operaOptions3" -->
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <!-- 发放考核结果-弹框 -->
    <dlg-start :centerDialogVisible.sync="centerDialogVisible" :checkObjiectNum="checkObjiectNum" :wrongNum="wrongNum" 
    :startId="2" @clickStart="clickStart" @close="close"></dlg-start>
    <!-- 确认完成-弹框 -->
    <dlg-done :centerDialogVisible2.sync="centerDialogVisible2"  :wrongDoneNum="wrongDoneNum" 
    :startId="2" @clickStart2="clickStart2" @close="close"></dlg-done>
    <!-- 代录入-确定-弹框 -->
    <dlg-dlr :removeDialogVisible.sync="removeDialogVisible" @clickEnsure="clickEnsure" @closeRemove="closeRemove"></dlg-dlr>
  </div>
</template>

<script>
import { getPlanDetailList,getSendResult,getCheckResult,getPlanBaseInfo,getSubsidiaryList,getLevel,getUnCompletedCount,getComplete,getReplaceCompleteValue,getChangeCompleteValue,getCompleteValueNum } from 'performance/store/api.js'
import { khjhStatus,ryztStatus2 } from 'performance/utils/enum.js'
import defHeader  from './components/Header.vue'
import dlgStart from "./components/dlgStart";
import dlgDone from "./components/dlgDone";
import dlgDlr from "./components/dlgDlr";
import searchHeader from "./components/searchHeader";
import commonTable from "./components/commonTable";
import Footer from '../../../components/basic/Footer.vue';
export default {
  name:"performance-checkdetail",
  components:{
    dlgStart,
    searchHeader,
    defHeader,
    commonTable,
    dlgDone,
    dlgDlr,
    Footer
    // SelectStaff
  },
  data() {
    return {
      removeDialogVisible:false,
      tableId:2,
      tableId2:3,
      ref1:"ref1",
      ref3:"ref3",
      def_HeaderData:{},//top信息
      loading:true,//表格加载
      planName:'考核计划名称',//考核计划名称
      planId:null,//考核计划id
      // examineePlanId:7,//考核对象id
      confirmStatus:1,//确认状态--1:未开始,2:进行中,3:已完成
      identityFlag:null,//公司考核、部门考核还是个人考核
      employeeName:null,//员工姓名
      deptId:null,//部门id
      valueBm:null,
      subsidiaryId:null,//公司id
      screenHeight: document.body.clientHeight - 294,//屏幕高度-其他高度=表格高度
      screenHeight2: document.body.clientHeight - 334,//屏幕高度-其他高度=表格高度
      screenHeight3: document.body.clientHeight - 416,//屏幕高度-其他高度=表格高度
      ryztStatus2,
      tips:'',//关联人员异常，考核对象后提示

      centerDialogVisible:false,//发放考核结果-弹框flag
      centerDialogVisible2:false,//确认完成-弹框flag
      checkObjiectNum:'',//考核对象总数
      wrongNum:'',//异常流程
      wrongDoneNum:null,//未完成考核数量

      activeName: 'third',//tab
      tabPosition:"ALL",//状态选择
      tabPosition2:"ALL",//状态选择

      isActive:"ALL",//考核结果-筛选等级
      levelArr:[],
      allLevelNum:0,
      processStatus:"ALL",//评分状态/审核状态
      processStatus2:"ALL",//审核状态
      status:null,//考核计划状态
      num:null,//待录入实际完成值个数

      showSecond:1,

      activeIndex:-1,//当前操作行index
      changeButton:false,//点击变更/代录入---变为保存和取消
      btn:"",//变更还是代录入
      arr3:[],//代录入的数据
      // searchObj:{},//搜索数据---searchId:"1",//区分右侧按钮; // options:[],//公司选项;identityFlag:null,//公司考核、部门考核还是个人考核
      options: [],

      // 公司、部门的table
      tableData: [],
      headerData: [
        { title: "考核对象", label: "examineeName", slot:"examineeName",align:"left",minWidth:"120px", showTooltip: true,fixed:"left" },
        { title: "关联人员", label: "relations", slot:"relations",align:"left",minWidth:"120px", showTooltip: true},
        { title: "数据来源指定人", label: "dataMarkers", slot:"dataMarkers",align:"left",minWidth:"150px", showTooltip: true },
        { title: "评分人",minWidth:"200px", label: "scorerList", slot:"scorerList",align:"left", showTooltip: true },
        { title: "考核评分状态", label: "scoreStatus",slot:"scoreStatus", align:"left",minWidth:"130px", showTooltip: true },
        { title: "待处理人", label: "pendingOperator",slot:"pendingOperator",align:"left",minWidth:"120px", showTooltip: true},
        { title: "总评分", label: "totalScore",slot:"totalScore", align:"left",minWidth:"120px", showTooltip: true },
        { title: "绩效等级", label: "scoreLevel",slot:"scoreLevel", align:"left",minWidth:"120px", showTooltip: true },
      ],
      pageOptions: {
        currPage: 1, //当前页码
        total: 10, //数据总数
        pageSize: 10, //每页显示条数
        pageSizes: [10, 20, 30, 40, 50, 100] //每页显示个数选择器选项设置
      },
      
      // operaOptions: {
      //     title: "操作", //名称
      //     width: 200, //宽度
      //     fixed: 'right',  // right - 固定在右侧
      //     align:"left",
      //     buttonList: [    //按钮列表
      //         { title: "详情" },
      //         { title: "修改流程",
      //           isShow: (row, btn) => {  //控制 - 显隐，返回Boolean
      //             return row.scoreStatus == 2 ? true : false
      //           } },
      //         { title: "移除" }
      //     ]
      // },

      // 个人考核的table
      headerData2: [
        { title: "考核对象", label: "examineeName", slot:"examineeName",align:"left",minWidth:"120px", showTooltip: true,fixed:"left" },
        { title: "公司名称", label: "subsidiaryName", align:"left",minWidth:"120px", showTooltip: true},
        { title: "部门", label: "deptName", align:"left",minWidth:"80px", showTooltip: true},
        { title: "数据来源指定人", label: "dataMarkers", slot:"dataMarkers",align:"left",minWidth:"150px", showTooltip: true },
        { title: "评分人", label: "scorerList",minWidth:"200px", slot:"scorerList",align:"left", showTooltip: true },
        { title: "考核评分状态", label: "scoreStatus",slot:"scoreStatus", align:"left",minWidth:"130px", showTooltip: true },
        { title: "待处理人", label: "pendingOperator",slot:"pendingOperator",align:"left",minWidth:"120px", showTooltip: true},
        { title: "总评分", label: "totalScore",slot:"totalScore", align:"left",minWidth:"120px", showTooltip: true },
        { title: "绩效等级", label: "scoreLevel",slot:"scoreLevel", align:"left",minWidth:"120px", showTooltip: true }
      ],

      // tabs---实际完成数据的table
      editRowJson:'',//编辑行一整行row数据，取消时用到
      // 考核结果的table-公司、部门
      headerData3: [
        { title: "考核对象", label: "examineeName", slot:"examineeName",align:"left",minWidth:"120px",showTooltip: true,fixed:"left" },
        { title: "关联人员", label: "relations", slot:"relations",align:"left",minWidth:"120px",showTooltip: true},
        { title: "审核人", label: "auditorList",minWidth:"200px", slot:"auditorList",align:"left",showTooltip: true },
        { title: "考核结果审核状态", label: "approveStatus",slot:"approveStatus", align:"left",minWidth:"160px",showTooltip: true },
        { title: "待处理人", label: "pendingOperator",slot:"pendingOperator",align:"left",minWidth:"120px",showTooltip: true},
        { title: "总评分", label: "totalScore",slot:"totalScore", align:"left",minWidth:"120px",showTooltip: true },
        { title: "绩效等级", label: "scoreLevel",slot:"scoreLevel", align:"left",minWidth:"120px",showTooltip: true },
      ],
      // 考核结果的table-个人
      headerData4: [
        { title: "考核对象", label: "examineeName", slot:"examineeName",align:"left",minWidth:"120px",showTooltip: true,fixed:"left" },
        { title: "公司名称", label: "subsidiaryName", align:"left",minWidth:"100px",showTooltip: true},
        { title: "部门", label: "deptName", align:"left",minWidth:"80px",showTooltip: true},
        { title: "审核人", label: "auditorList",minWidth:"200px", slot:"auditorList",align:"left",showTooltip: true },
        { title: "考核结果审核状态", label: "approveStatus",slot:"approveStatus", align:"left",minWidth:"150px",showTooltip: true },
        { title: "待处理人", label: "pendingOperator",slot:"pendingOperator",align:"left",minWidth:"100px",showTooltip: true},
        { title: "总评分", label: "totalScore",slot:"totalScore", align:"left",minWidth:"120px",showTooltip: true },
        { title: "绩效等级", label: "scoreLevel",slot:"scoreLevel", align:"left",minWidth:"100px",showTooltip: true },
      ],
      // operaOptions3: {
      //     title: "操作", //名称
      //     width: 250, //宽度
      //     // fixed: 'right',  // right - 固定在右侧
      //     align:"left",
      //     buttonList: [    //按钮列表
      //         { title: "详情" },
      //         { title: "修改流程" },
      //         { title: "调整考核结果" },
      //         { title: "发放考核结果" },
      //         { title: "查看调整记录" },
      //         { title: "移除" },
      //     ]
      // },
      showBtn:false,//是否隐藏操作列
      showDM:true,//是否显示待处理人
      source:'',//从哪个页面跳转过来的
      showFF:false,//判断发放考核结果按钮是否展示
    }
  },
  // created(){
  // },
  mounted(){
    this.handleInit();
    // resize
    this.handleTableResize()
  },
  methods:{
    // 代录入-确定
    clickEnsure(e){
      this.changeButton=false
      this.activeIndex=-1
      if(this.arr3.length>0){
        if(this.btn==="代录入"){
          this.handleGetReplaceCompleteValue(this.arr3)
        }
      }else{
        // 没有变化，恢复
        row.indicatorCompletion=editRow.indicatorCompletion
      }
      this.removeDialogVisible=e
    },
    // 代录入-关闭弹窗
    closeRemove(e){
      this.removeDialogVisible=e
    },
    // 是否显示tab2
    ifShowSecond(tableData){
      return (tableData.length>0&&tableData[0]&&tableData[0].indicatorCompletion&&tableData[0].indicatorCompletion.length>0)?true:false
    },
    // 判断是变更还是代录入
    handleChangeOrDLR(scope){
      let indicatorCompletion=scope.row.indicatorCompletion
      let arr=[]
      if(indicatorCompletion.length>0){
        arr=indicatorCompletion.filter((ele)=>ele.completeValueStatus==1)
      }
      return arr.length>0?false:true
      // let completeValueStatus= indicatorCompletion.length>0?indicatorCompletion[0].completeValueStatus:null
      // return completeValueStatus==2?true:false
    },
    isShowBtn(){
      // console.log(111)
      if(this.tableData&&this.tableData.length>0){
        this.tableData.map(ele1=>{
          let indicatorCompletion=ele1.indicatorCompletion
          if(indicatorCompletion && indicatorCompletion.length>0){
            let arr=indicatorCompletion.filter(ele2=>ele2.completeValueStatus==1)
            // console.log(arr)
            if(arr.length>0){
              this.showBtn=true 
            }
          }
        })
      }
    },
    handleValue(item,value){
      if(value){
        return value+item.dataUnit
      }else if(value===0){
        return 0+item.dataUnit
      }else if(value===null){
        return '--'
      }else{
        return '--'
      }
    },
    handleValue2(item,value,scope){
      if(value){
        return value+'分'
      }else if(value===0){
        if(item.completeValueStatus==1){
          return '--'
        }else{
          return 0+'分'
        }
      }else{
        return '--'
      }
    },
    async handleInit(){
      this.changeButton=false
      this.activeIndex=-1
      const { planId,source } = this.$route.query
      this.planId = planId
      this.source = source
      this.handleGetSubsidiaryList();//子公司(用工主体)
      this.handleGetPlanBaseInfo();//考核基本信息
      // await this.handleGetPlanDetailList2(2);//考核计划详情列表
      await this.handleGetPlanDetailList(2);//考核计划详情列表
      this.activeName='first'
      this.handleTableResize()
    },
    handleTableResize(){
      window.onresize=()=>{
        return ( ()=>{
          this.screenHeight=document.body.clientHeight - 294 + 'px'
          this.screenHeight3= document.body.clientHeight - 416 + 'px'//屏幕高度-其他高度=表格高度
        }
        )()
      }
    },
    //考核基本信息
    async handleGetPlanBaseInfo(){
      let obj = { planId:this.planId }
      const { data } = await getPlanBaseInfo(obj)
      const { name,type,status } = data
      this.def_HeaderData = {
        headerText:`${name}考核详情`,
        // headerText:`${name}`,
        headerTag:`${khjhStatus[status]}`
        // headerTag:`待确认`
      }
      this.identityFlag = type;
      this.status=status
      // this.identityFlag = 1;
      
    },
    async handleGetSubsidiaryList(){
      const { data } = await getSubsidiaryList()
      this.options=data
    },
    async handleGetLevel(){
      const { data } = await getLevel({planId:this.planId})
      this.levelArr = data || []
      let allLevelNum=0
      if(this.levelArr && this.levelArr.length>0){
        this.levelArr.forEach((ele)=>{
          allLevelNum+=Number(ele.levelNum)
        })
      }
      this.allLevelNum=allLevelNum
    },
    handleTipsItem(item){
      if(item && item.length>0){
        let errArr= item.filter(ele => ele.status!=1);
        // console.log("errArr:",errArr)
        if(errArr.length>0) {
          this.tips='考核评分流程存在异常，请调整'
          return true
        } 
      }
    },
    handleTipsItem2(item){
      if(item && item.length>0){
        let errArr= item.filter(ele => ele.status!=1);
        console.log("errArr:",errArr)
        if(errArr.length>0) {
          this.tips='关联人员存在异常，请调整'
          return true
        } 
      }
    },
    handleTipsItem3(item){
      if(item && Object.keys(item).length>0){
        if(item.employeeStatus!=1) {
          this.tips='考核对象存在异常，请调整'
          return true
        }else{
          return false
        }
      }
    },
    handleTipsItem4(item){
      if(item && item.length>0){
        let errArr=[]
        item.forEach((ele1)=>{
          if(ele1.dataMarker && ele1.dataMarker.length>0){
            errArr= ele1.dataMarker.filter(ele => ele.status!=1);
            if(errArr.length>0) {
              this.tips='待处理人存在异常，请调整'
              return true
            } 
          }
        })
      }
    },
    // tips提示
    handleTips(row){
      console.log("row",row)
      if(row){
        if(this.identityFlag!=3){
          let flag1=this.handleTipsItem2(row.relations)
          if(flag1===true){
            return true
          }else{
            let flag2=this.handleTipsItem4(row.indicatorCompletion)
            if(flag2){
              return true
            }
          }
        }else{
          let flag1=this.handleTipsItem3(row)//考核对象是否异常
          if(flag1){
            return true
          }else{
            let flag2=this.handleTipsItem4(row.indicatorCompletion)
            if(flag2){
              return true
            }
          }
        }
      }else{
        return false
      }
    },
    //分页size切换
    handleSizeChange(val) {
      this.loading=true
      this.pageOptions.pageSize=val
      this.requestOneMore()
    },
    //页码切换
    handleCurrentChange(val) {
      this.loading=true
      this.pageOptions.currPage=val
      this.requestOneMore()
    },

    // 点击发放考核结果
    start(){
      getCheckResult({planId:this.planId}).then(res=>{
        // console.log('点击发放考核结果', res)
        if(res.success){
          const { errorNum,total } = res.data
          if(total==0){
            this.$message("暂无需发放考核结果的考核对象")
            return
          }
          this.checkObjiectNum=total
          this.wrongNum=errorNum
          this.centerDialogVisible=true
        }else{
          this.$message.error(res.msg)
        }
      })
    },
    // 点击确认完成
    start2(){
      getUnCompletedCount({planId:this.planId}).then(res=>{
        // console.log('点击确认完成', res)
        if(res.success){
          const { count } = res.data
          if(count==0){
            this.clickStart2(false,2)
            // this.$message.success("操作成功")
          }else{
            this.wrongDoneNum=count
            this.centerDialogVisible2=true
          }
        }else{
          this.$message.error(res.msg)
        }
      })
    },
    // 评分状态切换
    statusChange(label){
      this.processStatus=label
      this.pageOptions.currPage=1
      this.handleGetPlanDetailList(2)
    },
    // 审核状态切换
    statusChange2(label){
      this.processStatus2=label
      this.pageOptions.currPage=1
      this.handleGetPlanDetailList(3)
    },
    // 发放考核结果-确定
    clickStart(e,id){
      id==2 && getSendResult({
        planId:this.planId,
      }).then(res=>{
        // console.log('发放考核结果-确定', res)
        if(res.success){
          this.centerDialogVisible=e
          this.$message.success(`已成功发放${this.checkObjiectNum}个考核对象的考核结果`)
          this.pageOptions.currPage=1
          this.requestOneMore()
        }else{
          this.$message.error(res.msg)
        }
      })
    },
    // 确认完成-确定
    clickStart2(e,id){
      id==2 && getComplete({
        planId:this.planId,
      }).then(res=>{
        // console.log('发放考核结果-确定', res)
        if(res.success){
          this.centerDialogVisible2=e
          this.$message.success("操作成功")
          this.pageOptions.currPage=1
          this.handleGetPlanBaseInfo();//考核基本信息
          this.requestOneMore()
        }else{
          this.$message.error(res.msg)
        }
      })
    },
    // 启动考核计划-关闭弹窗
    close(e){
      this.centerDialogVisible=e
      this.centerDialogVisible2=e
    },
    // 修改后再请求一次
    requestOneMore(){
      this.loading=true
      if(this.activeName=="third"){
        this.handleGetPlanDetailList(3);//考核计划详情列表
      }else if(this.activeName=="second"){
        this.handleGetCompleteValueNum()
        this.handleGetPlanDetailList(2);//考核计划详情列表
      }else{
        this.handleGetPlanDetailList(2);//考核计划详情列表
      }
    },
    // 搜索栏公司、部门、人员变动
    searchItemChange(subsidiaryId,deptId,employeeName,valueBm){
      this.pageOptions.currPage=1
      this.subsidiaryId=subsidiaryId
      this.deptId=deptId
      this.valueBm=valueBm
      this.employeeName=employeeName
      if(this.activeName==="third"){
        this.handleGetPlanDetailList(3)
      }else {
        this.handleGetPlanDetailList(2)
      }
    },
    // 请求列表数据
    handleGetPlanDetailList(listType){
      this.loading=true
      let processStatus=null
      let scoreLevel=null
      if(listType==2){
        processStatus=this.processStatus=="ALL"?null:Number(this.processStatus)
      }else if(listType==3){
        processStatus=this.processStatus2=="ALL"?null:Number(this.processStatus2)
        scoreLevel=this.isActive=="ALL"?null:this.isActive
      }
      let parmas={
        currentPage:this.pageOptions.currPage,
        pageSize:this.pageOptions.pageSize,
        deptId:this.deptId,
        employeeName:this.employeeName,
        subsidiaryId:this.subsidiaryId,
        listType,
        planId:this.planId,
        scoreLevel,
        processStatus
        // time:this.time
      }
      getPlanDetailList(parmas).then(res=>{
        this.loading=false
        if(res.success){
          this.pageOptions.total=res.data.total
          // Object.assign(this.pageOptions,{
          //   total:res.data.total
          // })
          const { records } = res.data
          // console.log("111",JSON.stringify(records))
          
          this.tableData = records
          // if(listType==2 && this.ifShowSecond(records)===false){
          //   this.hideTabs()
          // }
          if(listType==3){
            // 判断发放考核结果按钮是否展示
            this.isShowFF()
          }
          if(this.showSecond===1&&listType==2 && this.ifShowSecond(records)===false){
            this.hideTabs()//tab是否显示实际完成数据
            // this.loading=true
            this.activeName="third"
            setTimeout(()=>{
              this.activeName="first"
              // this.loading=false
            },0)
            // resolve()
          }
          ++this.showSecond
          if(listType==2 && this.activeName=="second"&& this.ifShowSecond(records)===true){
            this.isShowBtn()
            // this.hideTabs()
            this.$nextTick(()=>{
              this.$refs.table2.doLayout()
            })
          }
        }else{
          this.$message.error(res.msg)
        }
      })
    },
    isShowFF(){
      // 判断发放考核结果按钮是否展示
      if(this.tableData.length>0){
        let arr=this.tableData.filter((ele)=>ele.approveStatus==1)
        this.showFF=arr.length>0?true:false
        console.log("this.showFF",this.showFF)
        console.log("arr",arr)
      }else{
        this.showFF=false
      }
      console.log("this.showFF",this.showFF)
    },
    hideTabs(){
      //tab是否显示实际完成数据
      this.$nextTick(()=>{
        this.$refs.tabs.$children[0].$refs.tabs[1].style.display='none'
      })
    },
    // tab切换
    tabClick(tab, event) {
      // tab切换，重置基本数据
      this.processStatus="ALL"
      this.processStatus2="ALL"
      this.isActive="ALL"
      // this.id=null
      this.subsidiaryId=null
      this.deptId=null
      this.valueBm=null
      this.employeeName=null
      this.pageOptions.currPage= 1
      this.pageOptions.pageSize= 10
      this.handleTableResize()  
      if(tab.name==="first"){
        // this.$refs.table1.doLayout()
        this.handleGetPlanDetailList(2)
      }else if(tab.name==="second"){
        // this.$refs.table2.doLayout()
        this.handleGetCompleteValueNum()
        this.handleGetPlanDetailList(2)
      }else if(tab.name==="third"){
        // this.$refs.table3.doLayout()
        this.handleGetLevel()//获取等级列表
        this.handleGetPlanDetailList(3)
      }
    },
    // 获取待录入实际完成值个数
    handleGetCompleteValueNum(){
      getCompleteValueNum({
        planId:this.planId,
      }).then(res=>{
        // console.log('发放考核结果-确定', res)
        if(res.success){
          const { num } = res.data
          this.num=num
          // this.centerDialogVisible2=e
          // this.$message.success(res.msg)
        }else{
          this.$message.error(res.msg)
        }
      })
    },
    showBG(scope){
      return this.activeIndex != scope.$index&&this.tableData.length>0&&this.tableData[0].indicatorCompletion&&this.tableData[0].indicatorCompletion.length>0 &&this.handleChangeOrDLR(scope) ?true:false
    },
    showDLR(scope){
      return this.activeIndex != scope.$index&&this.tableData.length>0&&this.tableData[0].indicatorCompletion&&this.tableData[0].indicatorCompletion.length>0 &&!this.handleChangeOrDLR(scope) ?true:false
    },
    
    //操作-点击事件(变更、待录入)
    handleOperaClick3(btn, row, activeIndex) {
      if(btn==="代录入"){
        if(this.changeButton===true){
          this.$message("请先保存正在编辑的行")
        }else{
          this.editRowJson = JSON.stringify(row);
          this.changeButton=true
          this.activeIndex=activeIndex
          this.btn="代录入"
        }
      }
      // if(btn==="变更"){
      //   if(this.changeButton===true){
      //     this.$message("请先保存正在编辑的行")
      //   }else{
      //     this.editRowJson = JSON.stringify(row);
      //     this.changeButton=true
      //     this.activeIndex=activeIndex
      //     this.btn="变更"
      //   }
      // }
      if(btn==="确定"){
        this.clickQD(row)
        
      }
      if(btn==="取消"){
        let editRow = JSON.parse(this.editRowJson);
        row.indicatorCompletion=editRow.indicatorCompletion
        this.changeButton=false
        this.activeIndex=-1
      }
    },
    clickQD(row){
      let editRow = JSON.parse(this.editRowJson);
      let arr1=row.indicatorCompletion
      let arr2=editRow.indicatorCompletion
      let arr3=[]
      let examineePlanId=row.examineePlanId
      let flag=false
      arr1.forEach(ele => {
        let completeValue=ele.completeValue
        // completeValue=String(completeValue).replace(/\D/g, '')//过滤掉非数字
        if(!(completeValue || completeValue===0)){
          flag=true
        }
        completeValue =Number(Number(completeValue).toFixed(2));
        if(isNaN(completeValue)){
          completeValue = null;
        }
        arr2.forEach(ele2 => {
          if(ele.examineeIndicatorId===ele2.examineeIndicatorId){
            if(completeValue!==ele2.completeValue){
              arr3.push({
                examineePlanId,
                examineeIndicatorId:ele.examineeIndicatorId,
                completeValue,
              })
            }
          }
        });
      });
      if(flag){
        this.$message.error("当前行还有实际完成值未录入")
        return false
      }

      this.arr3=arr3
      this.removeDialogVisible=true
      
      
    },
    // cp(val){
    //   // 实时输入
    //   console.log(val)
    //   val? val=val.replace(/[^\d^\.]+/g,'').replace('.','$#$').replace(/(\.|\。)/g,'').replace('$#$','.'):''
    //   val=val.replace(/0*(\d+)/,'$1')
    //   if(val.includes('.')){
    //     val=Number(val)
    //     val=val.toFixed(2)
    //   }else{
    //     val=val.length>10?val.slice(0,10):val
    //   }
    //   console.log(val)
    // },
    // 实时根据公式得到结果
    handleInput(item,completeValue){
      // console.log("completeValue",completeValue)
      // if(completeValue===null){
      //   return ''
      // }
      if(completeValue===null||completeValue===''){
        return ''
      }
      if(item.dataRuleType==2){
        if(Number(item.targetValue)==0){
          this.$message.error("目标值不能为空或0")
          return ''
        }
        // completeValue=completeValue.replace(/0*(\d+)/,'$1')
        // console.log("completeValue",completeValue)
        completeValue=Number(completeValue)/Number(item.targetValue)
      }else{
        // completeValue=completeValue.replace(/0*(\d+)/,'$1')
        completeValue=Number(completeValue)
      }
      let score=null
      let rule=null
      if(item.dataRule){
        rule=JSON.parse(item.dataRule)
      }else{
        this.$message.error("没有相应的计算公式")
        completeValue=null
        return false
      }
      var max = Number(rule[0].max);
      var min = Number(rule[0].min);
      function findMax( i ){
        if( i == rule.length ) return max;
        if( max < Number(rule[i].max) ) max = Number(rule[i].max);
        findMax(i+1);
      }
      findMax(1);
      function findMin( i ){
        if( i == rule.length ) return min;
        if( min > Number(rule[i].min) ) min = Number(rule[i].min);
        findMin(i+1);
      }
      findMin(1);
      // console.log("max",max);
      // console.log("min",min);
      if(completeValue>max){
        // score= (rule.filter((e)=>e.max==max))[0].score
        // 评分上限
        score= item.maxScore
      }else if(completeValue<=min){
        score= (rule.filter((e)=>e.min==min))[0].score
      }else{
        let arr= rule.filter((ele)=>{
          return (completeValue>Number(ele.min) && completeValue<=Number(ele.max))
        })
        // console.log("arr",arr)
        if(arr.length===1){
          score=arr[0].score
          // return score
        }
      }
      return score+'分'
    },
    // 请求变更接口
    handleGetChangeCompleteValue(arr){
      getChangeCompleteValue({
        completeValues:arr,
        updateType:2
      }).then(res=>{
        this.requestOneMore()
        if(res.success){
          // this.$message.success(res.msg)
        }else{
          // this.$message.error(res.msg)
          // this.requestOneMore()
        }
      }).catch((err)=>{
        this.requestOneMore()
      })
    },
    // 请求代录入接口
    handleGetReplaceCompleteValue(arr){
      getReplaceCompleteValue({
        completeValues:arr,
        updateType:2
      }).then(res=>{
        this.requestOneMore()
        if(res.success){
          // this.$message.success(res.msg)
        }else{
          // this.$message.error(res.msg)
        }
      }).catch((err)=>{
        this.requestOneMore()
      })
    },
    // 考核结果-考核等级筛选
    handleStatusNum(id){
      this.isActive=id
      this.pageOptions.currPage=1
      this.handleGetPlanDetailList(3)
      // if(id=='ALL'){
      //   this.id=null
      //   this.handleGetPlanDetailList(3)
      // }else{
      //   this.id=id
      //   this.handleGetPlanDetailList(3)
      // }
    },
    handleIf(content,id,index2){
      if(id==='1'){
        if(content && content.length>0 && content[0].indicatorCompletion && content[0].indicatorCompletion.length>0){
          return true
        }else{
          return false
        }
      }else if(id==='2'){
        if(content && content[index2]){
          return true
        }else{
          return false
        }
      }else if(id==='3'){
        if(this.changeButton && this.activeIndex==content.$index &&content.row&&content.row.indicatorCompletion&& content.row.indicatorCompletion[index2] &&content.row.indicatorCompletion[index2].completeValueStatus==1){
          return true
        }else{
          return false
        }
      }else if(id==='4'){
        if(((!this.changeButton || this.activeIndex!==content.$index) &&(content.row&&content.row.indicatorCompletion&& content.row.indicatorCompletion[index2]))||content.row.indicatorCompletion[index2].completeValueStatus==2){
          return true
        }else{
          return false
        }
      }else if(id==='5'){
        if((this.changeButton && this.activeIndex==content.$index &&content.row&&content.row.indicatorCompletion&& content.row.indicatorCompletion[index2]&&content.row.indicatorCompletion[index2].completeValueStatus==1)){
          return true
        }else{
          return false
        }
      }else if(id==='6'){
        if(((!this.changeButton || this.activeIndex!==content.$index) &&(content.row&&content.row.indicatorCompletion&& content.row.indicatorCompletion[index2]))||content.row.indicatorCompletion[index2].completeValueStatus==2){
          return true
        }else{
          return false
        }
      }else if(id==='7'){
        if(content && content.name){
          this.showDM=true
          return true
        }else{
          this.showDM=false
          return false
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";
.wrapper-jx {
  min-width: 1000px;
  /deep/.el-table th, .el-table td {
    padding: 12px 12px;
  }
  /deep/.el-table .el-table__body-wrapper .cell {
    padding: 0 22px;
  }
  /deep/.el-table .el-table__fixed-right .el-table__fixed-body-wrapper .cell {
    padding: 0 22px;
  }
  // /deep/.el-table .el-table__fixed-left .el-table__fixed-body-wrapper .cell {
  //   padding: 0 22px;
  // }
  

  /deep/.def_header header {
    height: 59px!important;
  }
  /deep/.el-tabs__item {
    font-size: 16px!important;
  }
  /deep/ .el-button--small {
    font-size: 14px;
  }
  font-size: 14px;
  height: 100%;
  background: #fff;
  position: relative;
  // /deep/.el-table th, .el-table td {
  //   padding: 12px 12px!important;
  // }
  .back {
    width: calc(100% - 40px);
    // width: calc(100% - 40px);
    position: absolute;
    top: 0;
    left: 20px;
    height: 60px;
    z-index: 1;
  }
  /deep/.el-tabs {
    height: calc(100% -10px);
  }
  // box-sizing: border-box;
  /deep/.el-tabs__header {
    width: 350px;
    height: 50px;
    margin-left: calc((100% - 350px)/2);
    // margin-right: auto;
    // margin-left: 800px;
    margin-top: 10px;
    margin-bottom: 0;
  }
  /deep/.el-tabs__nav-scroll {
    width: 350px;
    // margin-left: auto;
    // margin-right: auto;
  }
  /deep/.el-tabs__nav-wrap {
    width: 350px;
    // margin-left: auto;
    // margin-right: auto;
  }
  /deep/.el-tabs__item {
    height: 50px;
    line-height: 50px;
  }
  /deep/.el-tabs__nav-wrap::after {
    // display: none;
    height: 0 !important;
  }
  
  .content {
    // border-radius: 8px;
    // position: relative;
    // height: calc(100% - 16px);
    height: 100%;
    // box-shadow: 0 8px 16px -14px rgba(52,61,160,0.16);
    padding: 0 20px;
    .status-num {
      width: 100%;
      height: 100px;
      font-size: 16px;
      // background-color: aqua;
      border: 1px solid rgba(0,0,0,0.15);
      border-radius: 4px;
      margin-top: 20px;
      display: flex;
      // justify-content: center;
      // align-items: center;
      .outside-box {
        flex: 0.7;
        height: 100%;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        cursor: pointer;
      }
      .outside-box-item {
        flex: 1;
      }
      .status-num-item {
        width: 100%;
        height: 50%;
        // flex: 1;
        // display: flex;
        // justify-content: center;
        // align-items: center;
        border-right: 1px solid rgba(0,0,0,0.15);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .status-num-item-top {
          span {
            font-size: 18px;
            font-weight: 600;
          }
          // margin-top: -10px;
        }
        .status-num-item-bottom {
          margin-top: 10px;
        }
      }
      .outside-box-item:last-child .status-num-item {
        border-right: none;
      }
      .status-num-item-all {
        // flex: 0.7;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-right: 1px solid rgba(0,0,0,0.15);
        span {
          font-size: 18px;
          font-weight: 600;
          padding: 0 5px;
        }
      }
      .outside-box:hover {
        background: #F7F8FA;
      }
    }
    
    .table-list,.table-list2 {
      font-size: 14px;
      // .old-table__pagination {
      //   position: absolute!important;
      //   right: 0!important;
      //   bottom: 0px!important;
      // }
      // .el-table--border {
      //   border: none!important;;
      // }
      // .el-table::before {
      //   height: 0;
      // }
      .scorer {
        margin-left: -10px;
        margin-right: -10px;
        // padding-left: 12px;
        // padding-right: 12px;
        // padding-bottom: 12px;
        // padding-top: 12px;
        padding: 24px;
        border-bottom: 1px solid #EBEEF5;
      }
      .scorer:first-child {
        padding-top: 12px;
      }
      .scorer:last-child {
        border-bottom: none;
        padding-bottom: 12px;
      }
    }
  }
  .search-box {
    height: 40px;
    // display: flex;
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .dialog-content {
    // margin-top: 10px;
    .dialog-content1 {
      font-size: 14px;
      color: #888;
    }
    .dialog-content2,.dialog-content3 {
      padding-left: 18px;
      margin-top: 10px;
      span {
        color:#E6A23C;
      }
    }
    .dialog-content3 {
      .wrongNum {
        margin-left: 28px;
      }
    }
    .dialog-content4 {
      margin-top: 6px;
      color: #888;
      margin-left: 120px;
    }
  }
  // 自定义表格
  .temp-table {
    /deep/.old-button__text {
      color: $mainColor;
      font-size: 14px;
    }
    /deep/ .el-table__row td:first-child .cell {
      padding-left: 22px;
    }
    position: relative;
    .el-link.el-link--primary {
      margin: 0 10px;
    }
    .el-link.el-link--danger {
      margin: 0 10px;
    }
    .temp-table-pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
    /deep/.el-table th > .cell{
      font-weight: normal;
      color: rgb(51, 51, 51);
    }
    /deep/.el-table td{
      border-color: #e8eaf3 !important;
      color: #333 !important;
    }
    /deep/.el-table th{
      border-color: #e8eaf3 !important;
      color: #333 !important;
    }
    /deep/.old-button--text {
      padding-left: 0;
    }
    /deep/.el-pagination__total {
      font-size: 14px;
      margin-right: 5px;
      padding-top: 1px;
    }

    .def_addRow-line{
      border-bottom: 1px solid #EBEEF5;
      // padding:12px 0;
    }
    .def_addRow-paddingTop{
      padding-top:12px;
    }
    .def_addRow-paddingBottom{
      padding-bottom:12px;
    }
    .completeValue-input {
      display: flex;
      align-items: center;
      .data-unit {
        margin-left: 4px;
      }
    }  
    /deep/.el-input {
      min-width: 80px;
    }
  }
  .red {
    color: #D6342A;
    // color: $mainColor;
    
  }
  .active {
    color: $mainColor;
  }
  .tipsTxt{
    text-overflow:ellipsis;/*设置隐藏部分为省略号*/
    overflow: hidden;/*设置隐藏*/
    display: -webkit-box;
    -webkit-line-clamp: 2;/*设置显示行数，此处为2行，可设置其他数字*/
    -webkit-box-orient: vertical;
  }
}
</style>

