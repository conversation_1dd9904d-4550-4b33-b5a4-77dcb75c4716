<template>
  <div class="performance_detail def_per_height" v-if="!loading" v-loading="loading">
    <def-header 
      :headerText="def_HeaderData.headerText" 
      :isBack="true"
      :isShowTag="true"
      :headerTag="def_HeaderData.headerTag"
    >
      <section slot="btnArea">
        <el-button class="change-btn" type="primary" v-if="basicInfo.scoreStatus===2" @click="changePro">修改流程</el-button>
      </section>
    </def-header>

    <!-- <div class="score-tips" v-if="showTips===true">
      <i class="iconfont-per icon-jingshi-qiangtishi1 text-color" style="font-size:23px"></i>
      <span>
        考核评分流程存在异常，请及时调整
      </span>
      <i class="iconfont-per icon-close1 text-color2" style="font-size:23px" @click="closeTips"></i>
    </div> -->

    <section class="def_per_section def_per_section-top">
      <def-card 
        :isShowPhoto="handleIsshowCardPhoto()"
        :cardPhoto="def_CardData.cardPhoto"
        :lineOne="def_CardData.cardDataLineOne"
        :lineTwo="def_CardData.cardDataLineTwo"
        :lineThree="def_CardData.cardDataLineThree"
        :lineFour="def_CardData.cardDataLineFour"
        :isShowRate="basicInfo.scoreStatus==3?true:false"
        :rate="def_CardData.cardRate"
        :isShowStep="true"
        :steps="def_CardData.cardSteps"
      />
      
    </section>

    <section class="def_per_section def_per_section-top" v-if="basicInfo.scoreStatus ==3 && handleSourceSection('绩效结果')">
      <def-title text="绩效结果" />
      <detail-jxjg 
        :score="def_CardData.cardRate.score"
        :grade="def_CardData.cardRate.grade"
      />
    </section>
    
    <section
      class="def_per_section def_per_section-top"
      v-if="basicInfo.scoreStatus!=3 && handleSourceSection('考核指标评分明细')"
    >
      <def-title text="考核指标评分明细" />
      <section class="detail-table">
        <def-etable
          :tableHeader="tableHeader_khzbpf"
          :tableData="def_TableData"
          @formatter="handleFormatter"
          @btnColumn="handleBtnColumn"
          @search="handleSearch"
          :total="total"
          :isShowIndex="true"
          :def_height="tableHeight"
          :isHidePage="true"
        />
      </section>
    </section>

    <section
      class="def_per_section def_per_section-top"
      v-if="basicInfo.scoreStatus==3 && handleSourceSection('考核指标评分明细')"
    >
      <def-title text="考核指标评分明细" />
      <section class="detail-table">
        <def-etable
          :tableHeader="tableHeader_khzbpf2"
          :tableData="def_TableData"
          @formatter="handleFormatter"
          @btnColumn="handleBtnColumn"
          @search="handleSearch"
          :total="total"
          :isShowIndex="true"
          :def_height="tableHeight"
          :isHidePage="true"
        />
      </section>
    </section>


    <section
      class="def_per_section def_per_section-top lc"
      v-if="def_CommentData.length !== 0 && handleSourceSection('总评')"
    >
      <def-title text="总评" />
      <detailZp 
        :list="def_CommentData"
      />
    </section>

    <section
      class="def_per_section def_per_section-top lc"
      v-if="handleSourceSection('考核评分流程')"
    >
      <def-title text="考核评分流程" />
      <detail-lc 
        :list="def_ScoreProcessData"
      />
    </section>

    
    <section
      class="def_per_section def_per_section-top lc"
      v-if="basicInfo.approveStatus!=1&&handleSourceSection('结果审核流程')"
    >
      <def-title text="结果审核流程" />
      <detail-lc 
        :list="def_ApproveProcessData"
      />
    </section>
    <!-- 修改流程-右侧抽屉 -->
    <!-- :loading="loading" -->
    <drawer-change :changeDialogVisible.sync="changeDialogVisible" 
    :tableId="tableId" :changeObj="changeObj"  @clickChangeEnsure="clickChangeEnsure" @closeChange="closeChange"></drawer-change>
  </div>
</template>

<script>

import { defCard,defNode,defTitle,defTable,defPhoto,defEtable,defNewNode } from 'performance/pages/personalPerformance/components'
import defHeader from './components/Header.vue'
import { getMyPlanDetail,getPlanDetail,getExamineePlanDetail } from 'performance/store/api.js'
import { khjhStatus,khlxType,khqrztStatus,khzqPeriodType,jdzxztNodetype,jdtType,zblxType,pffsScoreType,khqrjdztStatus,pfType,handleScoreStatus,khpfjdztStatus,ryztStatus,otherStatus } from 'performance/utils/enum.js'
import { date2Str } from "performance/utils/util.js";
import drawerChange from "./components/drawerChange";
import detailJxjg from "performance/pages/personalPerformance/components/compDetail/DetailJxjg"
import detailZp from "performance/pages/personalPerformance/components/compDetail/DetailZp"
import detailLc from "performance/pages/personalPerformance/components/compDetail/DetailLc"
export default {
  name:"performance_checkPlanDetail",
  components:{
    defHeader,
    defCard,
    defNode,
    defTitle,
    defTable,
    defPhoto,
    defEtable,
    defNewNode,
    drawerChange,
    detailJxjg,
    detailZp,
    detailLc
  },
  data(){
    return {
      loading:true,
      type:null,
      /*
        个人考核详情
      */ 
      basicInfo:{},//考核对象基本信息
      indicatorList:[],//考核指标信息
      process:{},//考核对象的流程数据
      resultSetting:{},//考核结果设置
      scoreLevel:"",//总分Level
      totalScore:null,//总分

      

      tableHeight:'auto',
      // tableHeight:document.body.clientHeight - 500 +'px',
      tableHeader:[
        { label: "考核指标名称", prop: "khzbmc" },
        { label: "考核指标说明", prop: "khzbsm" },
        { label: "评价标准", prop: "pjbz" },
        { label: "评分上限", prop: "pfsx" },
        { label: "考核指标权重", prop: "khzbqz" },
        { label: "目标值", prop: "mbz" },
        { label: "实际完成值", prop: "sjwcz" },
        {
          label: "绩效评分",
          prop: "jxpf",
          children: [
            { label: "评分人", prop: "pfr",type:"addRow" },
            { label: "考核指标评分", prop: "khzbpf",type:"addRow"},
            { label: "考核指标评语", prop: "khzbpy",type:"addRow",isTip:true},
          ]
        },
        { label: "考核指标总分", prop: "khzbzf" },
      ],
      total:null,
      limit:10,
      start:0,
      page:1,
      tableData:[],

      /**
       * 考核对象确认详情
      */
      tableHeader_khdxqrxq:[
        { label: "考核指标名称", prop: "khzbmc" },
        { label: "考核指标类型", prop: "khzblx" },
        { label: "考核指标说明", prop: "khzbsm" },
        { label: "评价标准", prop: "pjbz" },
        { label: "评分上限", prop: "pfsx" },
        { label: "考核指标权重", prop: "khzbqz" },
        { label: "目标值", prop: "mbz" },
        { label: "评分方式", prop: "pffs" },
        { label: "考核指标评分人", prop: "khzbpfr" },
      ],

      khqrjdztStatus:khqrjdztStatus,//考核确认节点状态


      def_HeaderData:{},
      def_CardData:{},
      def_TableData:[],
      def_CommentData:[],
      def_ScoreProcessData:[],
      def_ApproveProcessData:[],
      // def_ConfirmProcessData:[],
      // def_ChangeProcessData:[],
      def_StartInfo: {},

      /**
       * khqrxq:"考核确认详情"
      */
      source:'khqrxq',
      sourceShowSection:{
        // khqrxq:['考核指标明细','考核指标修正记录','考核确认流程']
        khqrxq:['考核指标评分明细','总评','考核评分流程','绩效结果']
      },



      /**
       * 考核指标评分明细
      */
      tableHeader_khzbpf:[],
      tableHeader_khzbpf2:[],
      pfType,
      changeDialogVisible:false,//修改流程-弹框flag
      changeObj:{},
      tableId:2,
      handleScoreStatus,
      tableHeaderBase: [
        { label: "考核指标名称", prop: "khzbmc",width:'150px' },
        { label: "考核指标类型", prop: "khzblx",width:'150px' },
        { label: "考核指标说明", prop: "khzbsm",isTip:true },
        { label: "评价标准", prop: "pjbz",width:'150px',isTip:true },
        { label: "评分上限", prop: "pfsx",width:'150px',align:"right" },
        { label: "考核指标权重", prop: "khzbqz",width:'150px',align:"right" },
        { label: "目标值", prop: "mbz",width:'150px',align:"right" },
        { label: "评分方式", prop: "pffs",width:'150px',align:"left",isTip:true },
        { label: "实际完成值", prop: "sjwcz",width:'150px',align:"right" },
        {
          label: "绩效评分",
          prop: "jxpf",
          isFixed:'right',
          width:'480px',
          align:"center",
          children: [
            { label: "评分人", prop: "pfr", type: "addRow",isFixed:'right',width:'140px',isTip:true },
            {
              label: "考核指标评分",
              prop: "khzbpf",
              type: "addRow",
              isEdit: true,
              realProp: "score",
              fatherProp: "scoreData",
              isEditRowKey: "isCanEditScore",
              isShowTag: true,
              editType:"number",
              numberType:"zs",
              tagProp: "maxScore",
              isFixed:'right',
              width:'140px',
              align:"right"
            },
            {
              label: "考核指标评语",
              prop: "khzbpy",
              type: "addRow",
              isEdit: true,
              realProp: "comment",
              fatherProp: "scoreData",
              isEditRowKey: "isCanEditComment",
              editType:"textarea",
              maxlength:200,
              isTip:true,
              isFixed:'right',
              width:'200px'
            }
          ]
        },
        { label: "考核指标总分", prop: "khzbzf" ,isFixed:'right',width:'150px',align:"right"},
        { label: "考核指标评分人", prop: "khzbpfr",width:'150px' }
      ],
      globalState: {
        currentStage: '', //当前阶段
        // state: null //1:未开始,2:进行中,3:已完成
      },
      def_approveStatus:{
        1:"处理中",
        2:"等待处理",
        3:"已处理",
      },
      def_scoreStatus:{
        1:"处理中",
        2:"等待处理",
        3:"已处理",
      },
      // def_confirmStatus:{
      //   1:"待确认",
      //   2:"确认中",
      //   3:"确认完成"
      // },
      //1:处理中,2:等待处理,3:已处理
      //进度条
      def_approveStatusSteps:{
        1:"审核中",
        2:"待审核",
        3:"审核完成"
      },
      def_scoreStatusSteps:{
        1:"评分中",
        2:"待评分",
        3:"评分完成"
      },
      def_confirmStatusSteps:{
        1:"确认中",
        2:"待确认",
        3:"确认完成"
      },

      def_processorType:{
        1:'被考核者',
        2:'上级',
        3:'指定人员'
      },
      showTips:false,
      khpfjdztStatus,
      otherStatus,
      // loading:false,
      def_DataMarkersData:{},
    }
  },
  mounted(){
    this.handleInit()
    // this.handleGetExamineePlanDetail()
  },
  methods:{
    handleInit() {
      this.hanldeFilterTableHeader();
      this.handleGetExamineePlanDetail();
    },
    handleSourceSection(name){
      // return this.sourceShowSection[this.source].includes(name)
      return true
    },
    async handleGetExamineePlanDetail(){
      const { examineePlanId,planId } = this.$route.query
      let obj = {
        planId,examineePlanId
      }
      if(true){
        const { data } = await getExamineePlanDetail(obj)
        const { basicInfo, indicatorList,process,resultSetting,scoreLevel,totalScore } = data
        this.basicInfo = basicInfo;//考核对象基本信息
        this.indicatorList = indicatorList;//考核指标信息
        this.process = process;//考核对象的流程数据
        this.resultSetting = resultSetting;//考核结果设置
        this.scoreLevel = scoreLevel;//总分Level
        this.totalScore = totalScore;//总分
        this.def_StartInfo = this.process.startInfo; //考核对象各个阶段启动数据
        this.def_ScoreProcessData = this.process.scoreProcess; //评分流程
        this.def_DataMarkersData = this.process.dataMarkers; //录入实际完成值流程
        this.def_TableData = this.indicatorList; //考核指标信息
        this.def_CommentData = this.process.commentProcess; //总评数据
        this.def_ApproveProcessData = this.process.approveProcess; //审核流程

        this.type = this.basicInfo.type;//考核类型 1:公司考核,2:部门考核,3:个人考核
        this.handleAddDataMarkers(); //拼接录入实际完成值流程
      }
      this.handleKhdxBase()

      this.loading = false
    },
    handleAddDataMarkers(){
      if (Object.keys(this.def_DataMarkersData).length !== 0) {
          let arr = [],
              boo = false;
          this.def_DataMarkersData.map(v=>{
            arr.push({
              processorName: v["dataMarkerName"],
              processorText: this.handleLrName(v),
              status: v["operationTime"] ? 3 : 1,
              time: v["operationTime"] ? this.$dayjs(v["operationTime"]).format("YYYY-MM-DD HH:mm") : ""
            })
            if(!boo){
              boo = v["operationTime"] ? true : false
            }
          })
          this.def_ScoreProcessData.unshift({
            det_nodeProcessors: true,
            nodeType: boo ? 3 : 1,
            nodeProcessors: arr
          });
      }
    },
    handleLrName(v){
      let text = ""
      if(v["operationTime"]){
        if(v["replaceUserName"]){
          text = `已录入实际完成值（${v["replaceUserName"]}代录入）`
        }else{
          text = `已录入实际完成值`
        }
      }else{
        text = "未录入实际完成值"
      }
      return text
    },
    handleArrBaseData(list,key){
      // console.log(list,key)
      let arr = []
      list.map(v=>{
        arr.push(v[key])
      })
      // console.log(arr)
      return arr
    },
    /**
     * 考核对象确认详情
    */
   //考核对象确认详情
    handleKhdxBase(){
      let isGr = this.type === 3 ? true : false
      this.def_HeaderData = {
        headerText:this.handleHeaderName(),
        headerTag:`${khjhStatus[this.basicInfo.status]}`,
      }
      this.def_CardData = {
        cardPhoto:this.basicInfo.employeeName,
        cardDataLineOne:{
          name: this.handleHeaderName2(),
          phone:this.basicInfo.mobile,
          tag:khlxType[this.basicInfo.type],
        },
        cardDataLineTwo:{
          label:isGr ? "" : "考核关联人员:",
          value:isGr ? `${this.basicInfo.subsidiaryName} ${this.basicInfo.deptName}` : this.handleExamineeRelations(this.basicInfo.examineeRelations)
        },
        // cardDataLineThree: {
        //   label: "考核周期:",
        //   // value: khzqPeriodType[this.basicInfo.period]
        //   value: date2Str(
        //     this.basicInfo.period,
        //     this.basicInfo.startDate,
        //     this.basicInfo.endDate
        //   )
        // },
        cardDataLineThree:{
          label:"考核计划名称:",
          value: this.basicInfo.name
        },
        cardDataLineFour:{
          label:"考核周期:",
          value: date2Str(this.basicInfo.period,this.basicInfo.startDate,this.basicInfo.endDate)
        },
        cardRate:{
          score:this.totalScore,
          grade:this.scoreLevel||"无"
        },
        cardSteps:this.process.statusProcess.map(v=>{return{
          id:v.nodeSort,
          state:v.nodeType,
          text:this.handleStepsNodeName(v.nodes,v.nodeType),
          tips: this.handleStepsNodeTips(v.nodes, v.nodeType),
        }})
      }
      // this.def_TableData = this.indicatorList;//考核指标信息
      // this.def_CommentData = this.process.commentProcess;//总评数据
      // this.def_ScoreProcessData = this.process.scoreProcess;//评分流程
      // this.def_ApproveProcessData = this.process.approveProcess;//审核流程
      // this.def_ConfirmProcessData = this.process.confirmProcess;//确认流程
      // this.def_ChangeProcessData = JSON.parse(JSON.stringify(this.process.changeProcess))
      // this.def_ChangeProcessData.map(v=>{
      //   v.def_isshow = false
      // });//考核指标修正记录
      this.handleAddStartInfo(); //拼接考核确认流程
    },
    //拼接考核确认流程
    handleAddStartInfo() {
      if (Object.keys(this.def_StartInfo).length !== 0) {
        // if (this.def_StartInfo["confirmStarterId"] !== 0) {
        //   this.def_ConfirmProcess.unshift({
        //     det_nodeProcessors: true,
        //     nodeProcessors: [
        //       {
        //         processorName: this.def_StartInfo["confirmStarterName"],
        //         processorText: "发起了考核确认",
        //         status: 3
        //       }
        //     ]
        //   });
        // }
        if (this.def_StartInfo["scoreStarterId"] !== 0) {
          this.def_ScoreProcessData.unshift({
            det_nodeProcessors: true,
            nodeType: 3,
            nodeProcessors: [
              {
                processorName: this.def_StartInfo["scoreStarterName"],
                processorText: "启动了考核",
                status: 3,
                time: this.$dayjs(this.def_StartInfo["scoreStartTime"]).format(
                  "YYYY-MM-DD HH:mm"
                )
              }
            ]
          });
        }
        if (this.def_StartInfo["approveStarterId"] !== 0) {
          this.def_ApproveProcessData.unshift({
            det_nodeProcessors: true,
            nodeType: 3,
            nodeProcessors: [
              {
                processorName: this.def_StartInfo["approveStarterName"],
                processorText: "发放考核结果",
                status: 3,
                time: this.$dayjs(
                  this.def_StartInfo["approveStartTime"]
                ).format("YYYY-MM-DD HH:mm")
              }
            ]
          });
        }
      }
    },
    handleHeaderName(){
      const { subsidiaryName,deptName,employeeName } = this.basicInfo
      switch(this.type){
        case 1 :
          return `${subsidiaryName}考核详情`
        case 2 :
          return `${deptName}考核详情`
        case 3 :
          return `${employeeName}考核详情`
      }
    },
    handleHeaderName2(){
      const { subsidiaryName,deptName,employeeName } = this.basicInfo
      switch(this.type){
        case 1 :
          return `${subsidiaryName}`
        case 2 :
          return `${deptName}`
        case 3 :
          return `${employeeName}`
      }
    },
    //card 进度条文案
    handleStepsNodeName(items, nodeType) {
      let objColor = {
        1: "#4F71FF",
        2: "#BBBBBB",
        3: "#555555"
      };
      let arr = [],
          baseObj={};
      items.map(v => {
        const { key, name, type } = v;
        if(Reflect.ownKeys(baseObj).includes(key)){
          baseObj[key].push(v)
        }else{
          baseObj[key] = [v]
        }
      });
      for (const i in baseObj) {
        let def_key = `<span style="color:${objColor[nodeType]}">${i} </sapn>`;
        let def_addTextArr = [];
        baseObj[i].map(v=>{
          const { key, name, type } = v;
          let def_name = (i && name) ? `<span style="color:${objColor[nodeType]}">：${name} </sapn>` : `<span style="color:${objColor[nodeType]}">${name} </sapn>`;
          let def_type = "";
          if (type === 1) {
            def_type = `<span style="color:#FF9500">${this.handleStepsNodeState(type)}</sapn>`;
          }
          def_addTextArr.push(def_name + def_type)
        })
        let def_text = def_key + def_addTextArr.join(`<span style="color:${objColor[nodeType]}">、</sapn>`)
        arr.push(def_text)
      }
      return arr.join(`<span style="color:${objColor[nodeType]}">，</sapn>`);
    },
    handleStepsNodeTips(items, nodeType){
      let arr = [],
          baseObj={};
      items.map(v => {
        const { key, name, type } = v;
        if(Reflect.ownKeys(baseObj).includes(key)){
          baseObj[key].push(v)
        }else{
          baseObj[key] = [v]
        }
      });
      for (const i in baseObj) {
        let def_key = `${i} `;
        let def_addText = "";
        let def_addTextArr = [];
        baseObj[i].map(v=>{
          const { key, name, type } = v;
          let def_name = (i && name) ? `：${name} `: `${name}`;
          let def_type = "";
          if (type === 1) {
            def_type = `${this.handleStepsNodeState(type)}`;
          }
          def_addTextArr.push(def_name + def_type)
        })
        let def_text = def_key + def_addTextArr.join(`、`)
        arr.push(def_text)
      }
      return arr.join(`、`);
    },
    
    //进度条状态
    handleStepsNodeState(type){
      // const { currentStage } = this.globalState
      let currentStage=this.basicInfo.approveStatus==1?"def_scoreStatus":"def_approveStatus"
      
      // console.log("currentStage",currentStage)
      switch(currentStage){
        case 'def_approveStatus' :
          return this.def_approveStatusSteps[type]
          // return this.def_confirmStatusSteps[type]
        case 'def_scoreStatus' :
          return this.def_scoreStatusSteps[type]
          // return this.def_confirmStatusSteps[type]
        // case 'def_confirmStatus' :
        //   return this.def_confirmStatusSteps[type]
      }
    },
    // handleFormatterKhdx({prop,data,btnItem},callback){
    //   switch(prop){
    //     case "khzbmc":
    //       callback(data["name"])
    //       break;
    //     case "khzblx":
    //       callback(zblxType[data["type"]])
    //       break;
    //     case "khzbsm":
    //       callback(data["description"])
    //       break;
    //     case "pjbz":
    //       callback(data["scoreStandard"])
    //       break;
    //     case "pfsx":
    //       callback(data["maxScore"])
    //       break;
    //     case "khzbqz":
    //       callback(data["weight"]==null ? '--' : data["weight"]+'%');
    //       break;
    //     case "mbz":
    //       callback(data["targetValue"])
    //       break;
    //     case "pffs":
    //       callback(pffsScoreType[data["scoreType"]])
    //       break;
    //     case "khzbpfr":
    //       // callback(this.handleArrBaseDataKhqr(data["scoreData"],"employeeName"))
    //       if(data["scoreType"]===2){
    //         callback('系统评分');
    //       }else{
    //         callback(this.handleArrBaseData(data["scoreData"], "employeeName"));
    //       }
    //       break;
    //   }
    // },
    handleBtnColumnKhdx(val,type){
      // console.log(val,type)
      switch(type){

      }
    },
    handleSearchKhdx({limit,start,page}){

    },
    handleArrBaseDataKhqr(list,key){
      let arr = []
      list.map(v=>{
        arr.push(v[key])
      })
      return arr.join('，')
    },
    //是否展示卡片头像
    handleIsshowCardPhoto(){
      /**
       * 个人考核展示头像
       * 1:公司考核,2:部门考核,3:个人考核
      */
      return this.type === 3 ? true : false
    },
    handleExamineeRelations(list){
      let arr = [];
      list.map(v=>{
        arr.push(v.employeeName)
      })
      return arr.join('，')
    },
    handleXzjlTime(time){
      return this.$dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    handleIsShowHide(item,index){
      this.$set(this.def_ChangeProcessData,index,{
        ...this.def_ChangeProcessData[index],
        def_isshow:!item.def_isshow
      })
    },


    // handleInit(){
    //   this.hanldeFilterTableHeader()
    // },
    //确定表头信息
    hanldeFilterTableHeader(val) {
      this.tableHeader_khzbpf = this.tableHeaderBase.filter(v => {
        return [
          "khzbmc",
          "khzblx",
          "khzbsm",
          "pjbz",
          "pfsx",
          "khzbqz",
          "mbz",
          "sjwcz",
          "jxpf"
        ].includes(v.prop);
      });
      this.tableHeader_khzbpf2 = this.tableHeaderBase.filter(v => {
        return [
          "khzbmc",
          "khzblx",
          "khzbsm",
          "pjbz",
          "pfsx",
          "khzbqz",
          "mbz",
          "sjwcz",
          "jxpf",
          "khzbzf"
        ].includes(v.prop);
      });
    },
    handleFormatter({ prop, data, btnItem }, callback) {
      switch (prop) {
        case "khzbmc":
          callback(data["name"]? data["name"]:'--' );
          break;
        case "khzblx":
          callback(zblxType[data["type"]]?zblxType[data["type"]]:'--');
          break;
        case "khzbsm":
          callback(data["description"] ? data["description"]:'--' );
          break;
        case "pjbz":
          callback(data["scoreStandard"] ? data["scoreStandard"] : '--');
          break;
        case "pfsx":
          callback(data["maxScore"]==null ? '--' : data["maxScore"]+'分');
          break;
        case "khzbqz":
          callback(data["weight"]==null ? '--' : data["weight"]+'%');
          break;
        case "mbz":
          callback(data["targetValue"] == null ? '--' : data["targetValue"]+data['dataUnit']);
          break;
        case "sjwcz":
          callback(data["realValue"] == null ? '--' : data["realValue"]+data['dataUnit']);
          break;
        case "pfr":
          // if(data["scoreType"]===2){
          //   callback('系统评分');
          // }else{
            callback(this.handleColumnsArrBaseData(data["scoreData"], "pfr",data["scoreType"]));
          // }
          break;
        case "khzbpf":
          callback(this.handleColumnsArrBaseData(data["scoreData"], "khzbpf"));
          break;
        case "khzbpy":
          callback(this.handleColumnsArrBaseData(data["scoreData"], "khzbpy"));
          break;
        case "khzbzf":
          callback(data["totalScore"]+'分');
          break;
        case "pffs":
          callback(pffsScoreType[data["scoreType"]]);
          break;
        case "khzbpfr":
          callback(this.handleArrBaseData(data["scoreData"], "employeeName"));
          break;
      }
    },
    handleColumnsArrBaseData(list,key,type){
      let arr = [];
      // if(type==2){
      //   arr.push("系统评分")
      //   return arr
      // }
      if(key=='pfr'){
        list.map(v=>{
          if([2,3,4,5,6].includes(v.employeeStatus)){
            arr.push(`
              <span style="color:red">${v["employeeTitle"]}:${v["employeeName"]}(${ryztStatus[v.employeeStatus]})</span>
            `)
          }else{
            arr.push(`${v["employeeName"]}`)
          }
        })
      }
      if(key=='khzbpf'){
        list.map(v=>{
          if(v.score===null){
            arr.push('--')
          }else{
            arr.push(`${v["score"]}分`)
          }
        })
      }
      if(key=='khzbpy'){
        list.map(v=>{
          if(!v.comment){
            arr.push('--')
          }else{
            arr.push(`${v["comment"]}`)
          }
        })
      }
      return arr
    },
    handleBtnColumn(val,type){
      // console.log(val,type)
      switch(type){

      }
    },
    handleSearch({limit,start,page}){

    },
    //指标评分明细关联总评数据 - 权重计算评分
    // handleIndicatorListToCommentData(employeeId,weight,score,examineeIndicatorId){
    //   // console.log(employeeId,weight,score,examineeIndicatorId)
    //   const val = score * weight / 100
    //   this.def_IndicatorListDataSum[examineeIndicatorId] = val
    //   this.handleIndicatorListDataSum(employeeId)
      
    // },
    //计算总评评分
    // handleIndicatorListDataSum(employeeId){
    //   // console.log(Object.values(this.def_IndicatorListDataSum))
    //   let val = math.add(Object.values(this.def_IndicatorListDataSum))
    //   // console.log(val)
    //   this.def_CommentData.map(v=>{
    //     if(v["employeeId"] == employeeId && v["isCanEdit"] == true){
    //       if( !!Number(val) ){
    //         v.totalScore = val * v["weight"] / 100
    //       }else{
    //         v.totalScore = 0
    //       }
    //     }
    //   })
    // },
    // 修改流程
    changePro(){
      let planId=this.basicInfo.planId
      let examineePlanId=this.basicInfo.examineePlanId
      let identityFlag=this.basicInfo.type
      // console.log("identityFlag",identityFlag)
      this.changeObj={
        planId,
        examineePlanId,
        examineeName:this.handleHeaderName2(),
        identityFlag
      }
      // console.log("changeObj",this.changeObj)
      this.changeDialogVisible=true//暂时
    },
    // 修改流程-确定
    clickChangeEnsure(e){
      this.changeDialogVisible=e
      // this.$emit("requestOneMore")
      this.loading=true
      this.handleGetExamineePlanDetail()
    },
    // 修改流程-关闭弹窗
    closeChange(e){
      this.changeDialogVisible=e
    },
    // 关闭提示
    closeTips(){
      this.showTips=false
    }
  },
  watch:{
    process: {
      handler (val) {
        // console.log(val)
      },
      deep: true
    },
    // def_ConfirmProcessData: {
    //   handler (val) {
    //     console.log(val)
    //   },
    //   deep: true
    // },
  }
}
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";
@media screen and (min-width: 1300px) {
  .def_per_section {
    padding:0 100px;
  }
}
.performance_detail{
  .def_header {
    padding: 0 20px;
  }
  .score-tips {
    width: calc(100% - 42px);
    height: 30px;
    margin-left: 20px;
    margin-top: 20px;
    // padding-right: 5px;
    background: #ff950018 ;
    color: #070F29;
    border: 1px solid #FF9500;
    border-radius: 6px;
    display: flex;
    align-items: center;
    .text-color{
      color:#FF9500;
      margin-right: 8px;
      margin-left: 8px;
    }
    .text-color2{
      color:#bbb;
      // margin-right: 8px;
      font-size: 14px!important;
      margin-left: auto;
      margin-right: 10px;
      cursor: pointer;
    }
  }
  .khzbxzjl{
    .khzbxzjl-node{
      margin-top: 20px;
      .node-header{
        font-size: 14px;
        color: #888888;
        letter-spacing: 0;
        line-height: 14px;
      }
      .slot-header{
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 48px;
        .slot-header-name{
          margin-left:16px;
          font-size: 16px;
          color: #070F29;
          letter-spacing: 0;
          line-height: 14px;
        }
        .slot-header-title{
          margin-left:10px;
          font-size: 16px;
          color: #070F29;
          letter-spacing: 0;
          line-height: 14px;
        }
      }
    }
    
  }
  .khqrlc{
    .khqr-node{
      margin-top: 20px;
      .node-right{
        .node-right-photo{
          margin:10px 0;
        }
        .def_icon{
          position:absolute;
          bottom: 0px;
          right: -10px;
          width:20px;
          height:20px;
          border-radius: 50%;
          border: 1px solid #fff;
          background: #fff;
        }
        .right-header{
          display: flex;
          flex-direction: row;
          align-items: center;
          height: 48px;
          .right-header-name{
            margin-left:16px;
            font-size: 16px;
            color: #070F29;
            letter-spacing: 0;
            line-height: 14px;
          }
          .right-header-text{
            margin-left:20px;
            font-size: 12px;
            color: #6A6F7F;
            letter-spacing: 0;
            line-height: 14px;
          }
        }
      }
      .slot-header{
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 48px;
        .slot-header-name{
          margin-left:16px;
          font-size: 16px;
          color: #070F29;
          letter-spacing: 0;
          line-height: 14px;
        }
        .slot-header-text{
          margin-left:20px;
          font-size: 12px;
          color: #6A6F7F;
          letter-spacing: 0;
          line-height: 14px;
        }
      }
    }
    
  }
  .lc{
    .lc-node{
      margin-top: 20px;
      .node-right{
        .node-right-photo{
          margin:10px 0;
        }
        .def_icon{
          position:absolute;
          bottom: 0px;
          right: -10px;
          width:20px;
          height:20px;
          border-radius: 50%;
          border: 1px solid #fff;
          background: #fff;
        }
        .right-header{
          display: flex;
          flex-direction: row;
          align-items: center;
          height: 48px;
          .right-header-name{
            margin-left:16px;
            font-size: 16px;
            color: #070F29;
            letter-spacing: 0;
            line-height: 14px;
          }
          .right-header-text{
            margin-left:20px;
            font-size: 12px;
            color: #6A6F7F;
            letter-spacing: 0;
            line-height: 14px;
          }
        }
      }
      .slot-header{
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 48px;
        .slot-header-name{
          margin-left:16px;
          font-size: 16px;
          color: #070F29;
          letter-spacing: 0;
          line-height: 14px;
        }
        .slot-header-tag{
          margin-left:10px;
          border-radius: 14px;
          padding:6px 12px;
          background: #F1F1F1;

          font-size: 14px;
          color: #6A6F7F;
          letter-spacing: 0;
          line-height: 14px;
        }
        .slot-header-text{
          margin-left:20px;
          font-size: 12px;
          color: #6A6F7F;
          letter-spacing: 0;
          line-height: 14px;
        }
      }
      .zp-bt {
        display: block;
        .py {
          width: 500px;
        }
      }
    }
    
  }
  .jxjg {
    margin-top: 20px;
    margin-left: 13px;
    .jxjg-title {
      font-size: 14px;
      color: #888888;
      letter-spacing: 0;
      text-align: right;
      line-height: 14px;
    }
    .jxjg-score {
      margin-left: 10px;
      font-size: 16px;
      color: #070f29;
      letter-spacing: 0;
      line-height: 14px;
    }
    .jxjg-grade {
      margin-left: 10px;
      font-size: 16px;
      color: #ff9500;
      letter-spacing: 0;
      line-height: 14px;
    }
  }
  .detail-table{
    margin-top:20px;
  }
  .detail-evaluate{
    display: flex;
    align-items: center;
    margin-top:20px;
  }
  .evaluate-score{
    margin-left:16px;
    .score-one{
      font-size: 14px;
      color: #888888;
      letter-spacing: 0;
      text-align: right;
      line-height: 14px;
    }
    .score-two,.score-three{
      margin-left: 10px;
      font-size: 14px;
      color: #555555;
      letter-spacing: 0;
      line-height: 14px;
    }
  }
  .evaluate-comment {
    display: flex;
    flex-direction: row;
    margin-top: 20px;
    .comment-one {
      min-width:42px;
      margin-left: 4px;
      .one-required {
        font-size: 14px;
        color: #ff6051;
        letter-spacing: 0;
        line-height: 14px;
      }
      .one-text {
        margin-left: 6px;
        font-size: 14px;
        color: #888888;
        letter-spacing: 0;
        text-align: right;
        line-height: 14px;
      }
    }
    .comment-two {
      margin-left: 14px;
      flex-grow: 1;
    }
  }
  .evaluate-node{
    margin-top:20px;
    .node-right{
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .slot-header{
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 48px;
      .slot-header-name{
        margin-left:16px;
        font-size: 16px;
        color: #070F29;
        letter-spacing: 0;
        line-height: 14px;
      }
      .slot-header-text{
        margin-left:20px;
        font-size: 12px;
        color: #6A6F7F;
        letter-spacing: 0;
        line-height: 14px;
      }
    }
    .node-right{
      .node-right-photo{
        margin:10px 0;
      }
      .def_icon{
        position:absolute;
        bottom: 0px;
        right: -10px;
        width:20px;
        height:20px;
        border-radius: 50%;
        border: 1px solid #fff;
        background: #fff;
      }
      .right-header{
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 48px;
        .right-header-name{
          margin-left:16px;
          font-size: 16px;
          color: #070F29;
          letter-spacing: 0;
          line-height: 14px;
        }
        .right-header-text{
          margin-left:20px;
          font-size: 12px;
          color: #6A6F7F;
          letter-spacing: 0;
          line-height: 14px;
        }
      }
    }
    .evaluate-name{
      font-size: 16px;
      color: #070F29;
      letter-spacing: 0;
      line-height: 14px;
      margin-left:16px;
    }
    .evaluate-tag{
      margin-left:10px;
      padding:6px 12px;
      background: #F1F1F1;
      border-radius: 14px;

      font-size: 14px;
      color: #6A6F7F;
      letter-spacing: 0;
      line-height: 14px;
    }
    .evaluate-proportion{
      font-size: 14px;
      color: #6A6F7F;
      letter-spacing: 0;
      line-height: 14px;
      margin-left:10px;
    }
  }
  
  .detail-btn{
    display: flex;
    justify-content: center;
    padding: 0 0 24px 0;
  }
  .change-btn {
    width: 98px;
    height: 40px;
    background: $mainColor;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: auto;
    font-size: 14px;
    color: #fff;
  }
}
</style>