<template>
  <div class="def_header">
    <header class="header">
      <template v-if="isBack">
        <el-row
          type="flex"
          style="justify-content: space-between; align-items: center"
        >
          <el-col :span="12" style="flex: 1">
            <span
              @click="
                source == 'fromStart'
                  ? $router.push('/performance/manage')
                  : $router.go(-1)
              "
              class="back-style header-back"
              >{{ headerBackText }}</span
            >
            <span class="header-line">|</span>
            <span class="header-title" v-if="headerText.length <= 10">{{
              headerText
            }}</span>
            <el-tooltip
              v-else
              effect="dark"
              placement="bottom"
              :content="headerText"
            >
              <span class="header-title">{{
                headerText.slice(0, 10) + "..."
              }}</span>
            </el-tooltip>
            <span
              class="header-tag no-start"
              v-if="isShowTag && headerTag == '未开始'"
              >{{ headerTag }}</span
            >
            <span
              class="header-tag"
              v-if="isShowTag && headerTag == '进行中'"
              >{{ headerTag }}</span
            >
            <span
              class="header-tag done"
              v-if="isShowTag && headerTag == '已完成'"
              >{{ headerTag }}</span
            >
            <!-- <slot name="leftArea" /> -->
          </el-col>
          <div><slot name="btnArea" /></div>
        </el-row>
      </template>
      <template v-else>
        <el-row type="flex" style="justify-content: space-between">
          <span>{{ headerText }}</span>
          <div><slot name="rightArea" /></div>
        </el-row>
      </template>
    </header>
  </div>
</template>

<script>
/**
 * headerText：标题文案
 * headerBackText：返回文案 默认"返回"
 * isBack:是否开启返回功能
 * headerTag：tag状态
 */
export default {
  name: "def_header",
  props: {
    headerText: {
      type: String,
      default: "",
    },
    headerBackText: {
      type: String,
      default: "返回",
    },
    isShowTag: {
      type: Boolean,
      default: false,
    },
    headerTag: {
      type: String,
      default: "",
    },
    isBack: {
      type: Boolean,
      default: false,
    },
    source: {
      type: String,
      default: "",
    },
  },
  watch: {
    source(val) {},
  },
};
</script>

<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";
.def_header {
  // padding: 0 20px;
  // position:sticky;
  // top:0;
  z-index: 1000;
  background: #fff;
  .header {
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
    overflow: hidden;
    .header-back {
      font-size: 16px;
      color: $mainColor;
      letter-spacing: 0;
      line-height: 16px;
      cursor: pointer;
    }
    .header-title {
      font-size: 16px;
      color: #070f29;
      letter-spacing: 0;
      line-height: 16px;
    }
    .header-line {
      font-size: 16px;
      color: #bbbbbb;
    }
    .header-tag {
      margin-left: 10px;
      padding: 4px 12px;
      background: #fff2e5;
      border-radius: 14px;

      font-size: 14px;
      color: #ff9500;
      letter-spacing: 0;
      line-height: 12px;
    }
  }
  .no-start {
    background: #f1f1f1 !important;
    color: #6a6f7f !important;
  }
  .done {
    background: #e6f8ea !important;
    color: #41bd5a !important;
  }
}
</style>
