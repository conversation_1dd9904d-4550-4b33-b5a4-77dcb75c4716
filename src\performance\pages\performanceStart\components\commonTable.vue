<template>
  <div class="el-dialog-wrapper">
    <old-table
      :data="tableData"
      :headerData="headerData"
      :loading="loadingFlag"
      :isShowPagination="isShowPagination"
      :pageOptions="pageOptions"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
      :isShowOperation="isShowOperation"
      :operaOptions="operaOptions"
      @operaClick="handleOperaClick"
      :height="comScreenHeight"
      :ref="tableRef"
    >
      <!-- :isShowTooltip="isShowTooltip" -->
      <!-- :typeOptions="typeOptions" -->
      <!-- 考核对象 -->
      <template slot="examineeName" slot-scope="scope">
        <span>
          <!-- {{scope.msg.row.examineeName || '--'}}  -->
          <span
            v-if="identityFlag == 3"
            :class="[scope.msg.row.employeeStatus != 1 ? 'red' : '']"
          >
            <!-- {{scope.msg.row.examineeName}} -->
            {{
              scope.msg.row.employeeStatus == 6
                ? ""
                : scope.msg.row.examineeName || "--"
            }}
            <span v-if="scope.msg.row.employeeStatus != 1">{{
              ryztStatus2[scope.msg.row.employeeStatus]
            }}</span>
          </span>
          <span v-if="identityFlag != 3">
            {{ scope.msg.row.examineeName || "--" }}
          </span>

          <!-- hover弹出提示 -->
          <!-- <el-popover
            placement="top"
            width="200"
            trigger="hover"
            v-if="handleTips(scope.msg.row)"
            :content="tips">
            <i class="el-icon-warning-outline" style="color:red;" slot="reference"></i>
          </el-popover> -->
        </span>
      </template>
      <!-- 关联人员 -->
      <template slot="relations" slot-scope="scope">
        <span v-for="(item, index) in scope.msg.row.relations" :key="index">
          <span :class="[item.status != 1 ? 'red' : '']">
            {{ item.status == 6 ? "" : item.name || "--" }}
            <span v-if="item.status != 1">{{ ryztStatus2[item.status] }}</span>
          </span>
          <!-- <span v-if="!item.name">--</span> -->
          <span v-if="index < scope.msg.row.relations.length - 1">、</span>
        </span>
        <span v-if="scope.msg.row.relations.length === 0">--</span>
      </template>
      <!-- 数据来源指定人 -->
      <template slot="dataMarkers" slot-scope="scope">
        <span v-for="(item, index) in scope.msg.row.dataMarkers" :key="index">
          <span v-if="item.name" :class="[item.status != 1 ? 'red' : '']">
            {{ item.status == 6 ? "" : item.name || "--" }}
            <span v-if="item.status != 1">{{ ryztStatus2[item.status] }}</span>
          </span>
          <span v-if="!item.name">--</span>
          <span v-if="index < scope.msg.row.dataMarkers.length - 1">、</span>
        </span>
        <span v-if="scope.msg.row.dataMarkers.length === 0">--</span>
      </template>
      <!-- 评分人 -->
      <!-- <template slot="scorerList" slot-scope="scope">
        <div>
          <div class="scorer" v-for="item in (scope.msg.row.scorerList)" :key="item.nodeSort">
            <span :class="handleScorerTitle(item)?'red':''" v-if="item.operators && item.operators.length>0 && item.operators[0].title">
              {{(item.operators[0].title || '--')+':'}}
            </span>
            <span v-if="item.operators && item.operators.length>0">
              <span v-for="(it,idx) in item.operators" :key="idx">
                <span :class="[it.status!=1?'red':'']">
                  {{(it.status==6?'':it.name||'--')}}
                  <span v-if="it.status!=1">{{ryztStatus2[it.status]}}</span>
                </span>
                <span v-if="idx<item.operators.length-1">、</span>
              </span>
            </span>
            <span v-else>--</span>
          </div>
          <div v-if="(scope.msg.row.scorerList).length===0">--</div>
        </div>
      </template> -->
      <template slot="scorerList" slot-scope="scope">
        <div>
          <div
            class="scorer"
            v-for="(item, index) in scope.msg.row.scorerList"
            :key="index"
          >
            <span v-if="item.operators && item.operators.length > 0">
              <span v-for="(it, idx) in handleList(item.operators)" :key="idx">
                <span :class="handleScorerTitle(it) ? 'red' : ''">
                  {{ (it.title || "--") + ":" }}
                </span>
                <span v-for="(i, v) in it.status" :key="v">
                  <span :class="[i != 1 ? 'red' : '']">
                    {{ i == 6 ? "" : it.name[v] || "--" }}
                    <span v-if="i != 1">{{ ryztStatus2[i] }}</span>
                  </span>
                  <span v-if="v < it.status.length - 1">、</span>
                </span>
                <br v-if="idx < handleList(item.operators).length - 1" />
              </span>
            </span>
            <span v-else>--</span>
          </div>
          <div v-if="scope.msg.row.scorerList.length === 0">--</div>
        </div>
      </template>
      <!-- 审核人 -->
      <template slot="auditorList" slot-scope="scope">
        <div>
          <div
            class="scorer"
            v-for="(item, index) in scope.msg.row.auditorList"
            :key="index"
          >
            <span v-if="item.operators && item.operators.length > 0">
              <span v-for="(it, idx) in handleList(item.operators)" :key="idx">
                <span :class="handleScorerTitle(it) ? 'red' : ''">
                  {{ (it.title || "--") + ":" }}
                </span>
                <span v-for="(i, v) in it.status" :key="v">
                  <span :class="[i != 1 ? 'red' : '']">
                    {{ i == 6 ? "" : it.name[idx] || "--" }}
                    <span v-if="i != 1">{{ ryztStatus2[i] }}</span>
                  </span>
                  <span v-if="v < it.status.length - 1">、</span>
                </span>
                <br v-if="idx < handleList(item.operators).length - 1" />
              </span>
            </span>
            <span v-else>--</span>
          </div>
          <div v-if="scope.msg.row.auditorList.length === 0">--</div>
        </div>
      </template>
      <!-- 待处理人 -->
      <template slot="pendingOperator" slot-scope="scope">
        <span
          v-for="(item, index) in scope.msg.row.pendingOperator"
          :key="index"
        >
          <span :class="[item.status != 1 ? 'red' : '']">
            {{ item.status == 6 ? "" : item.name || "--" }}
            <span v-if="item.status != 1">{{ ryztStatus2[item.status] }}</span>
          </span>
          <!-- <span v-if="!item.name">--</span> -->
          <span v-if="index < scope.msg.row.pendingOperator.length - 1"
            >、</span
          >
        </span>
        <span v-if="scope.msg.row.pendingOperator.length === 0">--</span>
      </template>

      <!-- 公司名称 -->
      <template slot="subsidiaryName" slot-scope="scope">
        {{ scope.msg.row.subsidiaryName || "--" }}
      </template>
      <!-- 部门 -->
      <template slot="deptName" slot-scope="scope">
        {{ scope.msg.row.deptName || "--" }}
      </template>
      <!-- 考核评分状态 -->
      <template slot="scoreStatus" slot-scope="scope">
        {{ handleScoreStatus[scope.msg.row.scoreStatus] || "--" }}
      </template>
      <!-- 总评分 -->
      <template slot="totalScore" slot-scope="scope">
        {{
          scope.msg.row.totalScore == null
            ? "--"
            : scope.msg.row.totalScore + "分"
        }}
      </template>
      <!-- 绩效等级 -->
      <template slot="scoreLevel" slot-scope="scope">
        {{ scope.msg.row.scoreLevel || "--" }}
      </template>
      <!-- 考核结果审核状态 -->
      <template slot="approveStatus" slot-scope="scope">
        {{ handleApproveStatus[scope.msg.row.approveStatus] || "--" }}
      </template>
    </old-table>
    <!-- 移除-弹框 -->
    <dlg-remove
      :removeDialogVisible.sync="removeDialogVisible"
      @clickEnsure="clickEnsure"
      @closeRemove="closeRemove"
      :examineeName="examineeName"
    ></dlg-remove>
    <!-- 修改流程-右侧抽屉 -->
    <drawer-change
      :loading="loading"
      :changeDialogVisible.sync="changeDialogVisible"
      :tableId="tableId"
      :changeObj="changeObj"
      @clickChangeEnsure="clickChangeEnsure"
      @closeChange="closeChange"
    ></drawer-change>
  </div>
</template>

<script>
import { getRemove, getSendOneResult } from "performance/store/api.js";
import {
  ryztStatus2,
  handleScoreStatus,
  handleApproveStatus,
  ryztStatus,
} from "performance/utils/enum.js";
import dlgRemove from "./dlgRemove";
import drawerChange from "./drawerChange";
export default {
  name: "common-table",
  components: {
    dlgRemove,
    drawerChange,
  },
  props: {
    tableId: null, //区分不同的表格
    loading: false,
    identityFlag: null,
    headerData: {
      type: Array,
      default: [],
    },
    screenHeight: "",
    tableData: {
      type: Array,
      default: [],
    },
    pageOptions: {},
    // operaOptions:{},
    tableRef: {
      type: String,
      default: "ref1",
    },
    status: "",
    // searchObj:{}//搜索数据---searchId:"1",//区分右侧按钮; // options:[],//公司选项;identityFlag:1/2/3,//公司考核、部门考核还是个人考核
  },
  data() {
    return {
      loadingFlag: this.loading,
      comScreenHeight: this.screenHeight,
      // searchId: this.searchObj.searchId,
      isShowPagination: true,
      // typeOptions: {
      //   width: 73,
      //   label: "序号",
      //   align:"left",
      //   type: "index" // type类型支持：selection - 多选， index - 序号
      // },
      isShowOperation: true, //是否显示操作列
      // isShowTooltip:true,//文字浮框展示
      tips: "", //关联人员异常，考核对象后提示

      ryztStatus,
      ryztStatus2,
      handleScoreStatus,
      handleApproveStatus,

      planId: null, //考核计划id
      examineePlanId: null, //考核对象id
      examineeName: "", //考核对象名称
      removeDialogVisible: false, //移除-弹框flag
      changeDialogVisible: false, //修改流程-弹框flag
      changeObj: {}, //修改流程数据
      operaOptions: {
        title: "操作", //名称
        width: 200, //宽度
        fixed: "right", // right - 固定在右侧
        align: "left",
        buttonList: [
          //按钮列表
          {
            title: "详情",
            isShow: (row, btn) => {
              //控制 - 显隐，返回Boolean
              return this.tableId == 1 ? false : true;
            },
          },
          {
            title: "修改流程",
            isShow: (row, btn) => {
              //控制 - 显隐，返回Boolean
              return this.status == 3
                ? false
                : this.tableId == 1
                ? true
                : this.tableId == 2 && row.scoreStatus == 2
                ? true
                : this.tableId == 3 && row.approveStatus != 3
                ? true
                : false;
            },
          },
          {
            title: "移除",
            isShow: (row, btn) => {
              //控制 - 显隐，返回Boolean
              return this.status == 3
                ? false
                : this.tableId == 1
                ? true
                : this.tableId == 2 && row.scoreStatus == 2
                ? true
                : this.tableId == 3 && row.approveStatus != 3
                ? true
                : false;
            },
          },
          {
            title: "发放考核结果",
            isShow: (row, btn) => {
              //控制 - 显隐，返回Boolean
              return this.status == 3
                ? false
                : this.tableId == 1 || this.tableId == 2
                ? false
                : this.tableId == 3 && row.approveStatus == 1
                ? true
                : false;
            },
          },
        ],
      },
    };
  },
  watch: {
    loading(val) {
      // console.log("val",val)
      if (!val) {
        this.$nextTick(() => {
          this.$refs[this.tableRef].doLayout();
        });
      }
      this.loadingFlag = val;
    },
    screenHeight(val) {
      this.comScreenHeight = val;
      this.$nextTick(() => {
        this.$refs[this.tableRef].doLayout();
      });
    },
    tableData(val) {
      this.$nextTick(() => {
        this.$refs[this.tableRef].doLayout();
      });
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs[this.tableRef].doLayout();
    });
  },
  methods: {
    // handleShow(id){
    //   let identityFlag=this.identityFlag
    //   let tableId=this.tableId
    //   // console.log("identityFlag",identityFlag)
    //   // console.log("tableId",tableId)
    //   if(id===1){
    //     // 关联人员
    //     return (identityFlag!= 3) ? true :false
    //   }else if(id===2){
    //     // 数据来源指定人
    //     return ((tableId==1 && identityFlag!= 3) || tableId==2) ? true :false
    //   }else if(id===3){
    //     // 评分人
    //     return (tableId != 3) ? true :false
    //   }else if(id===4){
    //     // 审核人
    //     return (tableId != 2) ? true :false
    //   }else if(id===5){
    //     // 公司/部门
    //     return (identityFlag == 3) ? true :false
    //   }else if(id===6){
    //     // 待处理人/总评分/绩效等级
    //     return (tableId != 1) ? true :false
    //   }else if(id===7){
    //     // 考核评分状态
    //     return (tableId == 2) ? true :false
    //   }else if(id===8){
    //     // 考核结果审核状态
    //     return (tableId == 3) ? true :false
    //   }
    // },

    // 过滤评分人及审核人list
    handleList(arr) {
      let hash = {};
      let i = 0;
      let newArr = [];
      arr.length > 0 &&
        arr.forEach((item) => {
          let title = item.title;
          if (hash[title]) {
            newArr[hash[title] - 1].status.push(item.status);
            newArr[hash[title] - 1].name.push(item.name);
          } else {
            hash[title] =
              ++i &&
              newArr.push({
                status: [item.status],
                name: [item.name],
                title: title,
              });
          }
        });
      // console.log("newArr:",newArr)
      return newArr;
    },
    handleTipsItem(item) {
      if (item && item.length > 0) {
        let errArr = item.filter((ele) => ele.status != 1);
        // console.log("errArr:",errArr)
        if (errArr.length > 0) {
          this.tips = "考核评分流程存在异常，请调整";
          return true;
        }
      }
    },
    handleTipsItem2(item) {
      if (item && item.length > 0) {
        let errArr = item.filter((ele) => ele.status != 1);
        // console.log("errArr:",errArr)
        if (errArr.length > 0) {
          this.tips = "关联人员存在异常，请调整";
          return true;
        } else {
          return false;
        }
      }
    },
    handleTipsItem3(item) {
      if (item && Object.keys(item).length > 0) {
        if (item.employeeStatus != 1) {
          this.tips = "考核对象存在异常，请调整";
          return true;
        } else {
          return false;
        }
      }
    },
    // tips提示
    handleTips(row1) {
      // console.log("row",row)
      let row = row1;
      if (row) {
        if (this.identityFlag != 3) {
          let flag1 = this.handleTipsItem2(row.relations);
          if (flag1 === true) {
            return true;
          } else {
            let flag2 = this.handleTipsItem(row.dataMarkers);
            let flag3 = this.handleTipsItem(row.auditorList);
            let flag4 = this.handleTipsItem(row.scorerList);
            if (flag2 || flag3 || flag4) {
              return true;
            } else {
              return false;
            }
          }
        } else {
          let flag1 = this.handleTipsItem3(row); //考核对象是否异常
          if (flag1) {
            return true;
          } else {
            let flag2 = this.handleTipsItem(row.dataMarkers);
            let flag3 = this.handleTipsItem(row.auditorList);
            let flag4 = this.handleTipsItem(row.scorerList);
            if (flag2 || flag3 || flag4) {
              return true;
            } else {
              return false;
            }
          }
        }
      } else {
        return false;
      }
    },
    // 评分人及审核人title是否标红(内部全部人员标红title才会标红)
    handleScorerTitle(item) {
      if (item && item.status && item.status.length > 0) {
        let checkArr = item.status.filter((ele) => ele != 1);
        return checkArr.length > 0 ? true : false;
      } else {
        return false;
      }
    },
    // table1
    //分页size切换
    handleSizeChange(val) {
      this.loadingFlag = true;
      this.pageOptions.pageSize = val;
      this.$emit("handleSizeChange", this.pageOptions.pageSize);
      // this.handleGetPlanDetailList()
    },
    //页码切换
    handleCurrentChange(val) {
      this.loadingFlag = true;
      this.pageOptions.currPage = val;
      this.$emit("handleCurrentChange", this.pageOptions.currPage);
      // this.handleGetPlanDetailList()
      // console.log('当前页码', val)
    },
    //操作-点击事件
    handleOperaClick(btn, row, scope) {
      // console.log(btn, '调试:', row,scope)
      if (btn === "移除") {
        this.examineePlanId = row.examineePlanId;
        this.planId = row.planId;
        // let identityFlag=this.identityFlag
        // this.examineeName=identityFlag==1?row.subsidiaryName:identityFlag==2?row.deptName:row.examineeName,
        this.examineeName = row.examineeName;
        this.removeDialogVisible = true;
      }
      if (btn === "详情") {
        this.examineePlanId = row.examineePlanId;
        this.planId = row.planId;
        // this.examineeName=row.examineeName
        // this.removeDialogVisible=true
        this.$router.push({
          path: "/performance/checkPlanDetail",
          query: {
            planId: this.planId,
            examineePlanId: this.examineePlanId,
            // tableId:this.tableId
          },
        });
      }
      if (btn === "修改流程") {
        this.planId = row.planId;
        this.examineePlanId = row.examineePlanId;
        let identityFlag = this.identityFlag;
        this.changeObj = {
          planId: row.planId,
          examineePlanId: row.examineePlanId,
          // examineeName:identityFlag==1?row.subsidiaryName:identityFlag==2?row.deptName:row.examineeName,
          examineeName: row.examineeName,
          identityFlag,
        };
        this.changeDialogVisible = true; //暂时
      }
      if (btn === "发放考核结果") {
        this.loadingFlag = true;
        this.examineePlanId = row.examineePlanId;
        getSendOneResult({ examineePlanId: this.examineePlanId })
          .then((res) => {
            this.loadingFlag = false;
            // console.log('点击发放单个考核结果', res)
            if (res.success) {
              // this.$message(res.msg)
              this.$emit("requestOneMore");
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((err) => {
            this.loadingFlag = false;
          });
      }
    },
    // 移除-确定
    clickEnsure(e, planId) {
      getRemove({
        planId: this.planId,
        examineePlanId: this.examineePlanId,
      }).then((res) => {
        if (res.success) {
          this.centerDialogVisible = e;
          this.$message.success(res.msg);
          // this.handleGetPlanDetailList()
          this.$emit("requestOneMore");
        } else {
          this.$message.error(res.msg);
        }
      });
      this.removeDialogVisible = e;
    },
    // 移除-关闭弹窗
    closeRemove(e) {
      this.removeDialogVisible = e;
    },
    // 修改流程-确定
    clickChangeEnsure(e) {
      this.changeDialogVisible = e;
      this.$emit("requestOneMore");
    },
    // 修改流程-关闭弹窗
    closeChange(e) {
      this.changeDialogVisible = e;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";
.el-dialog-wrapper {
  // /deep/.el-table th, .el-table td {
  //   padding: 12px 12px;
  // }
  // /deep/.el-table .el-table__body-wrapper .cell {
  //   padding: 0 22px;
  // }
  // /deep/.el-table .el-table__fixed-right .el-table__fixed-body-wrapper .cell {
  //   padding: 0 19px;
  // }
  /deep/ .el-table th.el-table__cell > .cell {
    padding: 0 10px !important;
    font-size: 14px;
  }
  font-size: 14px;
  .scorer {
    margin-left: -22px;
    margin-right: -22px;
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
  }
  .scorer:first-child {
    padding-top: 12px;
  }
  .scorer:last-child {
    border-bottom: none;
    padding-bottom: 12px;
  }
  .red {
    color: #d6342a;
    // color: $mainColor;
  }
}
</style>
