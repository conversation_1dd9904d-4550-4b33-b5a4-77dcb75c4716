<template>
  <div class="el-dialog-wrapper">
    <!-- 弹窗 -->
    <el-dialog
      width="420px"
      :visible.sync="isVisible"
      title="确认完成"
      @close="close"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="dialog-content" v-if="startId==2">
        <div class="dialog-content1">
          <!-- <i class="el-icon-warning" style="color:#E6A23C;margin-right:0;"></i> -->
          <!-- <span class="icon iconfont">&#xe666;</span> -->
          <i class="iconfont-per icon-jingshi-qiangtishi1 text-color" style="font-size:16px"></i>
          <span class="wrong-desc">
            当前考核计划还有<span class="wrong-num">{{wrongDoneNum}}</span>个考核对象尚未完成考核结果审核。确认完成后，这部分考核对象将无法进行考核结果审核
          </span>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="close" class="btn">取消</el-button>
        <!-- <el-button @click="handleStart" type="primary" class="btn btn2" v-if="startId==1">确认启动</el-button> -->
        <el-button @click="handleStart" type="primary" class="btn btn2" v-if="startId==2">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "dlg-start",
  props: {
    centerDialogVisible2:{
      type: Boolean,
      default:false
    },
    wrongDoneNum:null,
    checkObjiectNum:null,
    startId:null
  },
  data() {
    return {
      isVisible:this.centerDialogVisible2,
    }
  },
  watch:{
    centerDialogVisible2(val){
      this.isVisible=val
    }
  },
  methods: {
    handleStart(){
      this.isVisible=false
      this.$emit("clickStart2",false,this.startId)
    },
    close(){
      this.isVisible=false
      this.$emit("close",false)
    }
  },
};
</script>

<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";
.el-dialog-wrapper {
  /deep/.el-dialog {
    margin-top: 35vh!important;
  }
  // /deep/.el-dialog__title {
  //   font-weight: 600;
  // }
  .dialog-content {
    height: 60px;
    // padding-left: 48px;
    .dialog-content1 {
      height: 23px;
      display: flex;
      // align-items: center;
      font-size: 14px;
      color: #888;
      .text-color{
        margin-top: 4px;
        color:#FF9500;
      }
      .wrong-desc {
        margin-left: 6px;
        line-height: 24px;
        .wrong-num {
          color:#FF9B0E;
        }
      }
      // span {
      //   width: 23px;
      //   height: 23px;
      //   background: #FF9B0E;
      //   border-radius: 50%;
      //   margin-right: 10px;
      // }
    }
    
  }
  /deep/.el-dialog__footer {
    border-top: 0;
    padding-left: 0;
    padding-right: 0;
  }
  .btn {
    color: #555;
    width: 70px;
    height: 40px;
  }
  .btn2 {
    width: 70px;
    color: #fff;
    background: $mainColor;
  }
}
</style>