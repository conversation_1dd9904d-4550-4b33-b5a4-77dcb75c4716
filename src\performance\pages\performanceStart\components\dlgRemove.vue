<template>
  <div class="el-dialog-wrapper">
    <!-- 弹窗 -->
    <el-dialog
      width="420px"
      :visible.sync="isVisible"
      title="移除考核对象"
      @close="close"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="dialog-content">
          <!-- <i class="el-icon-warning" style="color:#E6A23C;margin-right:0;"></i> -->
          <!-- <span class="icon iconfont">&#xe666;</span> -->
          <i class="iconfont-per icon-jingshi-qiangtishi1 text-color" style="font-size:16px"></i>
          <span>
            确定将考核对象：{{examineeName}} 移除本次考核吗？<br> 
            <!-- 移除后，你可以从"无需考核对象”将其重新加入考核 -->
          </span>
      </div>
      <div slot="footer">
        <el-button @click="close" class="btn">取消</el-button>
        <el-button @click="handleStart" type="primary" class="btn btn2">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "dlg-romove",
  props: {
    removeDialogVisible:{
      type: Boolean,
      default:false
    },
    planId:null,
    examineeName:null
  },
  data() {
    return {
      isVisible:this.removeDialogVisible,
    }
  },
  watch:{
    removeDialogVisible(val){
      this.isVisible=val
    }
  },
  // created(){
  //   this.keyupEnter()
  // },
  methods: {
    // keyupEnter(){
    //   let that=this
    //   // console.log('that.removeDialogVisible',that.removeDialogVisible)
    //   document.onkeydown = (e) =>{
    //     console.log("e",e)
    //     let body = document.getElementsByTagName('body')[0]
    //     if (e.key === "Enter" && e.target === body && that.removeDialogVisible) {
    //       console.log('enter')
    //       // console.log('that.removeDialogVisible',that.removeDialogVisible)
    //       // that.removeDialogVisible=false
    //       // that.$emit("closeRemove",false)
    //       // that.close()
    //       that.$emit("clickEnsure",false,this.planId)
    //     }
    //     if (e.key === "Control" && e.target === body && that.removeDialogVisible) {
    //       console.log('Control')
    //       that.$emit("closeRemove",false)
    //     }
    //   }
    // },
    handleStart(){
      this.isVisible=false
      this.$emit("clickEnsure",false,this.planId)
    },
    close(){
      this.isVisible=false
      this.$emit("closeRemove",false)
    },
  },
};
</script>


<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";
.el-dialog-wrapper {
  /deep/.el-dialog {
    margin-top: 37vh!important;
  }
  // /deep/.el-dialog__title {
  //   font-weight: 600;
  // }
  .dialog-content {
    height: 50px;
    // padding-left: 16px;
    display: flex;
    font-size: 14px;
    color: #888888;
    letter-spacing: 0;
    line-height: 22px;
    .warning {
      width: 22px;
      height: 22px;
      background: #FF9B0E;
      border-radius: 50%;
      margin-right: 10px;
    }
    .text-color{
      color:#FF9500;
      margin-right: 8px;
    }
  }
  /deep/.el-dialog__footer {
    border-top: 0;
    padding-left: 0;
    padding-right: 0;
  }
  .btn {
    color: #555;
    width: 70px;
    height: 40px;
  }
  .btn2 {
    color: #fff;
    background: $mainColor;
  }
}
</style>