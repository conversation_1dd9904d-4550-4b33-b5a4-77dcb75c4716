<template>
  <div class="el-dialog-wrapper">
    <!-- 弹窗 -->
    <el-dialog
      :width="startId==1?'452px':'480px'"
      :visible.sync="isVisible"
      :title="startId==1?'启动考核计划':startId==2?'发放考核结果':'提示'"
      @close="close"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="dialog-content" v-if="startId==1">
        <div class="dialog-content1">
          <!-- <i class="el-icon-warning" style="color:#E6A23C;margin-right:0;"></i> -->
          <!-- <span class="icon iconfont">&#xe666;</span> -->
          <i class="iconfont-per icon-jingshi-qiangtishi1 text-color" style="font-size:16px"></i>
          <span>
            计划启动后，考核表将分发给待处理人员依次处理
          </span>
        </div>
        <div class="dialog-content2">
          考核对象总数
          <span class="dialog-content3-2">{{checkObjiectNum}}</span>
        </div>
        <div class="dialog-content3">
          <span class="dialog-content3-1">异常流程</span>
          <span class="dialog-content3-2">{{wrongNum}}</span>
          <span class="tips">您可以启动考核计划后，再对异常考核对象单独调整</span>
        </div>
      </div>
      <div class="dialog-content" v-if="startId==2">
        <div class="dialog-content1">
          <!-- <i class="el-icon-warning" style="color:#E6A23C;margin-right:0;"></i> -->
          <!-- <span class="icon iconfont">&#xe666;</span> -->
          <i class="iconfont-per icon-jingshi-qiangtishi1 text-color" style="font-size:16px"></i>
          <span>
            发放考核结果后，考核结果将分发给考核结果审核人依次处理
          </span>
        </div>
        <div class="dialog-content2">
          发放考核结果总数
          <span class="dialog-content3-2">{{checkObjiectNum}}</span>
        </div>
        <div class="dialog-content3 pl">
          <span class="dialog-content3-1">异常流程</span>
          <span class="dialog-content3-2">{{wrongNum}}</span>
          <span class="tips">您可以发放考核结果后，再对异常考核对象单独调整</span>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="close" class="btn">取消</el-button>
        <el-button @click="handleStart" type="primary" class="btn btn2" v-if="startId==1">确认启动</el-button>
        <el-button @click="handleStart" type="primary" class="btn btn2" v-if="startId==2">确认发放</el-button>
        <!-- <el-button @click="handleStart" type="primary" class="btn btn2" v-if="startId==3">确定</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "dlg-start",
  props: {
    centerDialogVisible:{
      type: Boolean,
      default:false
    },
    wrongNum:null,
    wrongDoneNum:null,
    checkObjiectNum:null,
    startId:null
  },
  data() {
    return {
      isVisible:this.centerDialogVisible,
    }
  },
  watch:{
    centerDialogVisible(val){
      this.isVisible=val
    }
  },
  methods: {
    handleStart(){
      this.isVisible=false
      this.$emit("clickStart",false,this.startId)
    },
    close(){
      this.isVisible=false
      this.$emit("close",false)
    }
  },
};
</script>

<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";
.el-dialog-wrapper {
  /deep/.el-dialog {
    margin-top: 30vh!important;
  }
  // /deep/.el-dialog__title {
  //   font-weight: 600;
  // }
  .dialog-content {
    // height: 140px;
    // padding-left: 48px;
    .dialog-content1 {
      // height: 23px;
      display: flex;
      // align-items: center;
      font-size: 14px;
      color: #888;
      .text-color{
        margin-top: 2px;
        color:#FF9500;
      }
      span {
        margin-left: 6px;
      }
      // span {
      //   width: 23px;
      //   height: 23px;
      //   background: #FF9B0E;
      //   border-radius: 50%;
      //   margin-right: 10px;
      // }
    }
    .dialog-content2,.dialog-content3 {
      color: #555;
      margin-top: 24px;
      // font-weight: 600;
      .dialog-content3-2 {
        margin-top: 1px;
        color:#FF9B0E;
        margin-left: 10px;
        // font-weight: 500;
      }
      .tips {
        margin-top: 2px;
        color: #888;
        font-size: 12px;
        margin-left: 18px;
      }
    }
    .dialog-content3 {
      padding-left: 28px;
      display: flex;
      .dialog-content3-1 {
        width: 60px;
      }
    }
    .pl {
      padding-left: 57px!important;
    }
    
  }
  /deep/.el-dialog__footer {
    border-top: 0;
    padding-left: 0;
    padding-right: 0;
  }
  .btn {
    color: #555;
    width: 70px;
    height: 40px;
  }
  .btn2 {
    width: 98px;
    color: #fff;
    background: $mainColor;
  }
}
</style>