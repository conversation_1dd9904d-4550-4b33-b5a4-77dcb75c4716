<template>
  <div class="el-dialog-wrapper">
    <!-- 右侧抽屉 -->
    <el-drawer
      title="修改流程"
      :visible.sync="drawer"
      direction="rtl"
      :before-close="close"
      :wrapperClosable="false"
      v-loading="loadingFlag"
    >
      <div class="dialog-content">
        <!-- 考核对象：售后部 -->
        <div class="dialog-content1">
          <div class="dialog-content-title mt20">
            <div class="dialog-content-title-bg"></div>
            <span>考核对象：{{ this.changeObj.examineeName || "--" }}</span>
          </div>
          <!-- 提示 -->
          <div
            class="dialog-content1-tips"
            v-if="showTips && handleShow(examineeRelations, 3)"
          >
            <!-- <i class="el-icon-warning" style="margin-right:2px;color:#888;" slot="reference"></i> -->
            <i
              class="iconfont-per icon-shujuyichang tip-color"
              style="font-size: 16px"
            ></i>
            <span> 关联人员删除后，将同步变更考核计划中的关联人员。 </span>
          </div>
          <!-- 关联人员 -->
          <div
            :class="['dialog-content-man', showTips ? 'mt10' : 'mt20']"
            v-if="handleShow(examineeRelations, 3)"
          >
            <div
              :class="['dialog-content-man-item', index == 0 ? '' : 'mt20']"
              v-for="(item, index) in examineeRelations"
              :key="index"
            >
              <!-- 姓名头像 -->
              <def-photo
                v-if="item.employeeName"
                class="node-right-photo"
                :isRandomColor="true"
                :name="item.employeeName"
                boxSize="48px"
                textSize="14px"
              />
              <!-- 默认头像 -->
              <div class="avatar" v-else>
                <img src="../../../images/defaultAvatar.png" alt="" />
              </div>
              <div
                v-if="item.employeeName"
                :class="['related-name', handleShow(item, 1) ? 'red' : '']"
              >
                <span>
                  {{ item.employeeName }}
                  <span v-if="item.status != 1">{{
                    ryztStatus2[item.status]
                  }}</span>
                </span>
              </div>
              <old-button
                tag="span"
                v-if="examineeRelations.length > 1"
                type="text"
                class="dialog-content-man-item-del"
                @click="deleteItem('1', index)"
                >删除</old-button
              >
            </div>
          </div>
        </div>
        <!-- 录入实际完成值 -->
        <div
          class="dialog-content2"
          v-if="tableId != 3 && dataMarkers.length > 0"
        >
          <div class="dialog-content-title">
            <div class="dialog-content-title-bg"></div>
            <span>录入实际完成值</span>
          </div>
          <!-- 数据来源指定人 -->
          <div class="dialog-content-man" v-if="dataMarkers.length > 0">
            <div
              :class="['dialog-content-man-item', index == 0 ? '' : 'mt20']"
              v-for="(item, index) in dataMarkers"
              :key="index"
            >
              <!-- 姓名头像 -->
              <def-photo
                v-if="item.dataMarkerName"
                class="node-right-photo"
                :isRandomColor="true"
                :name="item.dataMarkerName"
                boxSize="48px"
                textSize="14px"
              />
              <!-- 默认头像 -->
              <div class="avatar" v-else>
                <img src="../../../images/defaultAvatar.png" alt="" />
              </div>
              <div v-if="item.dataMarkerName" class="related-name">
                <span :class="[handleShow(item, 4) ? 'red' : '']">
                  <span
                    v-if="item.dataMarkerName"
                    :class="[item.dataMarkerStatus != 1 ? 'red' : '']"
                  >
                    {{ item.dataMarkerName }}
                    <span v-if="item.dataMarkerStatus != 1">{{
                      ryztStatus2[item.dataMarkerStatus]
                    }}</span>
                  </span>
                </span>
                <span
                  class="indicator-box"
                  v-if="item.indicators && item.indicators.length > 0"
                >
                  <span class="kuohao">（</span>
                  <span
                    class="indicator"
                    v-for="(it, idx) in item.indicators"
                    :key="idx"
                  >
                    {{ it.name || "--" }}
                    <span v-if="idx < item.indicators.length - 1">、</span>
                  </span>
                  <span class="kuohao">）</span>
                </span>
              </div>
              <old-button
                tag="span"
                type="text"
                class="dialog-content-man-item-change"
                v-if="showDataMakersButton(item)"
                @click="handleEditRy('3', { item, index })"
                >修改数据来源指定人</old-button
              >
            </div>
          </div>
        </div>
        <!-- 同步所有考核对象 -->
        <!-- <div class="dialog-content3">
          <span>同步所有考核对象</span>
          <el-popover
            placement="top"
            width="200"
            trigger="hover"
            :content="tips"
            class="popover">
            <i class="el-icon-warning-outline"  slot="reference"></i>
          </el-popover>
          <el-switch
            v-model="switchValue"
          >
          </el-switch>
        </div> -->
        <!-- 考核评分流程 -->
        <div
          class="dialog-content4"
          v-if="tableId != 3 && scoreProcess && scoreProcess.length > 0"
        >
          <div class="dialog-content-title mb37">
            <div class="dialog-content-title-bg"></div>
            <span>考核评分流程</span>
          </div>
          <!-- 各个节点 -->
          <div class="grade" v-for="(item, index) in scoreProcess" :key="index">
            <div class="grade-left">
              <div class="grade-circle"></div>
              <div
                :class="[
                  'grade-line',
                  index == scoreProcess.length - 1 ? 'boder-right-none' : '',
                ]"
              ></div>
            </div>
            <div class="grade-detail">
              <div class="grade-detail-item">
                <div class="dialog-content-grade-title">
                  {{ handleGradeTitle(item, "1") }}
                </div>
                <div
                  class="dialog-content-grade-base"
                  v-for="(it, idx) in item.nodeProcessors"
                  :key="idx"
                >
                  <!-- 姓名头像 -->
                  <def-photo
                    class="node-right-photo"
                    :isRandomColor="true"
                    :name="it.processorName"
                    boxSize="48px"
                    textSize="14px"
                  />
                  <!-- 默认头像 -->
                  <!-- <div class="avatar" v-else>
                    <img src="../../../images/defaultAvatar.png" alt="">
                  </div> -->
                  <div
                    v-if="it.processorName"
                    :class="['related-name', handleShow(it, 2) ? 'red' : '']"
                  >
                    <span>
                      {{ it.processorName }}
                      <span v-if="it.processorStatus != 1">{{
                        ryztStatus2[it.processorStatus]
                      }}</span>
                    </span>
                  </div>
                  <div class="grade-btn" v-if="it.status != 3">
                    <old-button
                      tag="span"
                      type="text"
                      class="dialog-content-man-item-change"
                      v-if="it.processorType != 1"
                      @click="
                        handleEditRy(
                          '1',
                          { item, index },
                          it,
                          idx,
                          item.nodeProcessors
                        )
                      "
                      >修改评分人</old-button
                    >
                    <old-button
                      tag="span"
                      type="text"
                      class="dialog-content-man-item-del"
                      v-if="checkScore(item, index, it)"
                      @click="deleteItem('2', index, idx)"
                      >删除</old-button
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 结果审核流程 -->
        <div
          class="dialog-content5"
          v-if="
            (tableId == 1 || tableId == 3) &&
            approveProcess &&
            approveProcess.length > 0
          "
        >
          <div class="dialog-content-title mb37">
            <div class="dialog-content-title-bg"></div>
            <span>结果审核流程</span>
          </div>
          <!-- 各个节点 -->
          <div
            class="grade"
            v-for="(item, index) in approveProcess"
            :key="index"
          >
            <div class="grade-left">
              <div class="grade-circle"></div>
              <div
                :class="[
                  'grade-line',
                  index == approveProcess.length - 1 ? 'boder-right-none' : '',
                ]"
              ></div>
            </div>
            <div class="grade-detail">
              <div class="grade-detail-item">
                <div class="dialog-content-grade-title">
                  {{ handleGradeTitle(item, "2") }}
                </div>
                <div
                  class="dialog-content-grade-base"
                  v-for="(it, idx) in item.nodeProcessors"
                  :key="idx"
                >
                  <!-- 姓名头像 -->
                  <def-photo
                    class="node-right-photo"
                    v-if="it.processorName"
                    :isRandomColor="true"
                    :name="it.processorName"
                    boxSize="48px"
                    textSize="14px"
                  />
                  <!-- 默认头像 -->
                  <div class="avatar" v-else>
                    <img src="../../../images/defaultAvatar.png" alt="" />
                  </div>
                  <div
                    v-if="it.processorName"
                    :class="['related-name', handleShow(it, 2) ? 'red' : '']"
                  >
                    <span>
                      {{ it.processorName }}
                      <span v-if="it.processorStatus != 1">{{
                        ryztStatus2[it.processorStatus]
                      }}</span>
                    </span>
                  </div>
                  <div class="grade-btn" v-if="it.status != 3">
                    <old-button
                      tag="span"
                      type="text"
                      class="dialog-content-man-item-change"
                      v-if="it.processorType != 1"
                      @click="
                        handleEditRy(
                          '2',
                          { item, index },
                          it,
                          idx,
                          item.nodeProcessors
                        )
                      "
                      >修改审核人</old-button
                    >
                    <old-button
                      tag="span"
                      type="text"
                      class="dialog-content-man-item-del"
                      v-if="approveFlag"
                      @click="deleteItem('3', index, idx)"
                      >删除</old-button
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <footer class="footer">
        <el-button @click="close">取消</el-button>
        <el-button @click="handleStart" type="primary">确定</el-button>
      </footer>
    </el-drawer>
    <user-select
      v-if="showDialog"
      :userList="employeeTree"
      :list="departmentTree"
      @close="handleRyClose"
      @commit="handleRyData"
      :select="def_select"
      :isOnly="true"
      :selectIdList="selectIdList"
    ></user-select>
  </div>
</template>

<script>
import Footer from "../../../../components/basic/Footer.vue";
import { defPhoto } from "../../personalPerformance/components";
import { ryztStatus2 } from "performance/utils/enum.js";
import UserSelect from "performance/pages/IndicatorsLibrary/components/UserSelect.vue";
import {
  getProcess,
  postProcess,
  getEmployeeTree,
  getUserList,
  getDepartmentTree,
} from "performance/store/api.js";
export default {
  components: {
    Footer,
    UserSelect,
    defPhoto,
  },
  name: "dlg-change",
  props: {
    changeDialogVisible: {
      type: Boolean,
      default: false,
    },
    changeObj: {
      type: Object,
      default: () => {},
    },
    tableId: null,
    loading: null,
  },
  data() {
    return {
      showDialog: false,
      employeeTree: [],
      departmentTree: [],
      def_select: [], //当前选择人员
      edit_node: {}, //正在修改的节点信息
      edit_employee: {}, //正在修改的人员信息
      id: "", //1-评分流程，2-结果审核流程，3-数据来源指定人
      ryztStatus2,

      loadingFlag: true,
      drawer: this.changeDialogVisible,
      examineeName: "", //考核对象名称
      showTips: false, //是否显示提示
      // scoreFlag:true,//评分流程是否显示提示删除按钮
      approveFlag: true, //结果审核流程是否显示提示删除按钮
      tips: "开启后，若在当前页修改数据来源指定人，将同步变更所有考核对象的数据来源指定人", //hover提示
      switchValue: true, //switch开关
      // changeData:{},
      examineeRelations: [],
      scoreProcess: [],
      approveProcess: [],
      dataMarkers: [], //未处理过的数据来源指定人
      // dataMarkers2:[],//处理过的数据来源指定人
      // submitDataMarkers:[],//提交的数据来源指定人
      // params:{},
      selectIdList: [], //修改人员的时候，不允许选择当前列表的人
    };
  },
  watch: {
    changeDialogVisible(val) {
      // console.log("val",val)
      this.drawer = val;
      if (val === true) {
        this.init();
      }
    },
    loading(val) {
      this.loadingFlag = val;
    },
  },
  computed: {
    requestExamineeRelations: {
      //保存流程 关联人员参数
      get() {
        return this.handleSubmitItem1(this.examineeRelations);
      },
      set(val) {},
    },
    requestApproveProcessor: {
      //保存流程 审核流程参数
      get() {
        return this.handleSubmitItem2(this.approveProcess);
      },
      set(val) {},
    },
    requestScoreProcessors: {
      //保存流程 评分流程参数
      get() {
        return this.handleSubmitItem2(this.scoreProcess);
      },
      set(val) {},
    },
    requestMarkProcessors: {
      //保存流程 数据来源指定人流程参数
      get() {
        return this.handleSubmitList(this.dataMarkers);
      },
      set(val) {},
    },
  },
  // created(){
  //   // this.init()
  // },
  methods: {
    handleShow(item, id) {
      if (id == 1) {
        return item.status != 1 && this.tableId != 3 ? true : false;
      } else if (id == 2) {
        return item.processorStatus != 1 ? true : false;
      } else if (id == 3) {
        return this.changeObj.identityFlag != 3 &&
          this.tableId != 3 &&
          item &&
          item.length > 0
          ? true
          : false;
      } else if (id == 4) {
        return item.dataMarkerStatus != 1 && this.tableId != 3 ? true : false;
      }
    },
    handleSubmitItem1(process) {
      let arr = [];
      if (process.length > 0) {
        process.map((v) => {
          arr.push({
            employeeId: v.employeeId,
            taskId: v.taskId,
          });
        });
      }
      return arr;
    },
    handleSubmitItem2(process) {
      let arr = [],
        baseArr = [];
      // if(process.length>0){
      process.map((v) => {
        baseArr = [...baseArr, ...v.nodeProcessors];
      });
      baseArr.map((v) => {
        arr.push({
          employeeId: v.processorId,
          taskId: v.taskId,
        });
      });
      // }

      return arr;
    },

    //修改人员
    async handleEditRy(id, { item, index }, it, idx, process) {
      this.id = id;

      await getDepartmentTree().then((res1) => {
        this.departmentTree = res1.data;
        getUserList()
          .then((res2) => {
            console.log(res1, res2);
            this.employeeTree = res2.data;
            let selectIdList = [];
            // console.log("id",id)
            if (id == 3) {
              this.def_select = [{ employeeId: item.dataMarkerId }];
              if (item.dataMarkerId) {
                selectIdList.push(item.dataMarkerId);
              }
            } else {
              // 评分人/审核人
              this.def_select = [{ employeeId: it["processorId"] }];
              process.map((ele) => {
                if (ele.processorId) {
                  selectIdList.push(ele.processorId);
                }
              });
            }
            console.log("selectIdList:", selectIdList);
            this.selectIdList = selectIdList;

            this.showDialog = true;
            this.edit_node = {
              index,
              item,
            };
            if (id != "3") {
              this.edit_employee = {
                index: idx,
                item: it,
              };
            }
            // console.log(">>>>>",this.edit_node,this.edit_employee)
          })
          .catch((err) => {});
      });
    },
    //选择人员回调
    handleRyData(val) {
      console.log("选择人员回调>>>", val[0]);
      if (val.length == 1) {
        const { employeeId, name, effective } = val[0];
        if (!effective) {
          this.$message("该人员账号异常，无法参与考核");
          return;
        }
        let nodeIndex = this.edit_node["index"];
        let employeeIndex = this.edit_employee["index"];
        // console.log("nodeProcessors:",nodeProcessors)
        if (this.id == "1") {
          let nodeProcessors = this.scoreProcess[nodeIndex]["nodeProcessors"];
          let def_nodeProcessors = nodeProcessors.map((v, i) => {
            if (i == employeeIndex) {
              v.processorId = employeeId;
              v.processorName = name;
              v.processorStatus = 1;
            }
            return v;
          });
          // console.log('def_nodeProcessors',def_nodeProcessors)
          this.$set(this.scoreProcess, nodeIndex, {
            ...this.scoreProcess[nodeIndex],
            nodeProcessors: def_nodeProcessors,
          });
        } else if (this.id == "2") {
          let nodeProcessors2 =
            this.approveProcess[nodeIndex]["nodeProcessors"];
          let def_nodeProcessors = nodeProcessors2.map((v, i) => {
            if (i == employeeIndex) {
              v.processorId = employeeId;
              v.processorName = name;
              v.processorStatus = 1;
            }
            return v;
          });
          this.$set(this.approveProcess, nodeIndex, {
            ...this.approveProcess[nodeIndex],
            nodeProcessors: def_nodeProcessors,
          });
        } else if (this.id == "3") {
          let nodeProcessors3 = this.dataMarkers;
          let def_nodeProcessors = nodeProcessors3.map((v, i) => {
            if (i == nodeIndex) {
              v.dataMarkerId = employeeId;
              v.dataMarkerName = name;
              v.dataMarkerStatus = 1;
            }
            return v;
          });
          // console.log('def_nodeProcessors',def_nodeProcessors)
          this.dataMarkers = def_nodeProcessors;
          // this.$set(this.dataMarkers2, nodeIndex, def_nodeProcessors
          // );
        }
      }
      this.showDialog = false;
    },
    //选择人员取消操作
    handleRyClose() {
      this.showDialog = false;
      this.edit_node = {};
      this.edit_employee = {};
    },
    //获取员工树
    // async handleGetEmployeeTree() {
    //   const res = await getEmployeeTree();
    //   if (res.success) {
    //     this.employeeTree = res.data;
    //   } else {
    //     this.$message.error(res.msg);
    //   }
    // },
    init() {
      this.handleGetProcess();
      // this.handleGetEmployeeTree()
    },
    handleGetProcess() {
      // 发请求
      getProcess({
        planId: this.changeObj.planId,
        examineePlanId: this.changeObj.examineePlanId,
      })
        .then((res) => {
          // console.log("修改流程res: ",res)
          this.loadingFlag = false;
          if (res.success) {
            if (Object.keys(res.data).length > 0) {
              // this.changeData=res.data
              const {
                examineeRelations,
                dataMarkers,
                scoreProcess,
                approveProcess,
              } = res.data;
              this.examineeRelations = examineeRelations;
              this.dataMarkers = dataMarkers;
              this.scoreProcess = scoreProcess;
              this.approveProcess = approveProcess;
              // 关联人员提示是否显示
              this.checkTips();
              this.checkApprove(approveProcess);
              this.checkApprove2(approveProcess);
              this.handleList(dataMarkers);
            } else {
              // data里没有数据
            }
          } else {
            // this.changeData=[]
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          // this.changeData=[]
        });
    },
    // 关联人员提示是否显示
    checkTips() {
      // if(this.examineeRelations.length==0){
      //   this.showTips=false
      // }else{
      //   // let checkArr=this.examineeRelations.filter((ele)=>{
      //   //     return (ele.status=="2" || ele.status=="3")
      //   // })
      //   // let checkArr=this.examineeRelations.filter((ele)=>ele.status!=1)
      //   // this.showTips=checkArr.length>0?true:false
      //   this.showTips=checkArr.length>0?true:false
      // }
      this.showTips = this.examineeRelations.length > 1 ? true : false;
    },
    // 检查评分流程删除按钮是否显示
    checkScore(item, index, it) {
      if (item.nodeProcessors && item.nodeProcessors.length > 0) {
        if (item.nodeProcessors[0].indicatorRange == 1) {
          return false;
        }
        // if(it.status==3){
        //   return false
        // }
        if (item.nodeProcessors[0].weight && item.nodeProcessors.length == 1) {
          return false;
        } else {
          return true;
        }
      }
    },
    // 检查审核流程是否只剩一个
    checkApprove(approveProcess) {
      let sum = 0;
      for (let i = 0; i < approveProcess.length; i++) {
        let newLength = approveProcess[i].nodeProcessors.length;
        if (newLength > 0) {
          sum += newLength;
        }
      }
      // console.log("sum",sum)
      if (sum == 1) {
        this.approveFlag = false;
      } else {
        this.approveFlag = true;
      }
    },
    // 检查审核流程节点是否有length，没有者则删除父级
    checkApprove2(approveProcess) {
      for (let i = 0; i < approveProcess.length; i++) {
        let newLength = approveProcess[i].nodeProcessors.length;
        if (newLength == 0) {
          approveProcess.splice(i, 1);
        }
      }
    },
    // 过滤数据来源指定人
    // handleList(arr){
    //   let hash = {};
    //   let i = 0;
    //   let newArr = [];
    //   arr && arr.forEach((item)=>{
    //     let employeeId = item.employeeId;
    //     if(hash[employeeId]){
    //       newArr[hash[employeeId] - 1].indicatorId.push(item.indicatorId)
    //       newArr[hash[employeeId] - 1].taskId.push(item.taskId)
    //       newArr[hash[employeeId] - 1].indicatorName.push(item.indicatorName)
    //     }else{
    //       hash[employeeId] = ++i && newArr.push({
    //         indicatorId: [item.indicatorId],
    //         taskId: [item.taskId],
    //         indicatorName: [item.indicatorName],
    //         employeeId: employeeId,
    //         employeeName: item.employeeName,
    //         status: item.status
    //       })
    //     }
    //   });
    //   // console.info("newArr",newArr)
    //   this.dataMarkers2=newArr
    // },

    // 已录入完所有指标的数据来源指定人不允许修改
    showDataMakersButton(item) {
      let indicators = item.indicators;
      let arr = indicators.filter((ele) => ele.completeValueStatus == 1);
      return arr.length > 0 ? true : false;
    },
    // 处理提交的数据来源指定人
    handleSubmitList(arr) {
      let newArr = [];
      arr &&
        arr.forEach((ele) => {
          for (let i = 0; i < ele.indicators.length; i++) {
            newArr.push({
              employeeId: ele.dataMarkerId,
              taskId: ele.indicators[i].examineeIndicatorId,
            });
          }
        });
      // console.log("submitDataMarkers",newArr)
      return newArr;
      // this.submitDataMarkers=newArr
    },
    num2cn(section) {
      var chnNumChar = [
        "零",
        "一",
        "二",
        "三",
        "四",
        "五",
        "六",
        "七",
        "八",
        "九",
      ];
      var chnUnitSection = ["", "万", "亿", "万亿", "亿亿"];
      var chnUnitChar = ["", "十", "百", "千"];
      var strIns = "",
        chnStr = "";
      var unitPos = 0;
      var zero = true;
      while (section > 0) {
        var v = section % 10;
        if (v == 0) {
          if (!zero) {
            zero = true;
            chnStr = chnNumChar[v] + chnStr;
          }
        } else {
          zero = false;
          strIns = chnNumChar[v];
          strIns += chnUnitChar[unitPos];
          chnStr = strIns + chnStr;
        }
        unitPos++;
        section = Math.floor(section / 10);
      }
      // console.log("chnStr",chnStr)
      return chnStr;
    },
    // 处理考核评分及结果审核流程的title
    handleGradeTitle(item, id) {
      if (item) {
        let processorType = item.nodeProcessors[0].processorType;
        let superiorLevel = item.nodeProcessors[0].superiorLevel;
        let weight = item.nodeProcessors[0].weight || "0";
        let indicatorRange = item.nodeProcessors[0].indicatorRange;
        if (indicatorRange == 1 && id == "1") {
          return `指定指标人评分`;
        }
        if (processorType == "1" && id == "1") {
          return `被考核人自评（权重${weight}%）`;
        } else if (processorType == "1" && id == "2") {
          return "被考核者";
        } else if (processorType == "2") {
          if (superiorLevel) {
            if (superiorLevel == 1) {
              if (id == "1") {
                return `直接上级（权重${weight}%）`;
              } else if (id == "2") {
                return "直接上级";
              }
            } else if (superiorLevel == 2) {
              if (id == "1") {
                return `隔级上级（权重${weight}%）`;
              } else if (id == "2") {
                return "隔级上级";
              }
            } else {
              let num = this.num2cn(superiorLevel);
              if (id == "1") {
                return `${num}级上级（权重${weight}%）`;
              } else if (id == "2") {
                return `${num}级上级`;
              }
            }
          }
        } else if (processorType == "3") {
          if (id == "1") {
            return `他人评分（权重${weight}%）`;
          } else if (id == "2") {
            return "指定人员";
          }
        }
      }
    },
    // 删除
    deleteItem(id, index, idx) {
      // 删除关联人员
      if (id == "1") {
        this.examineeRelations.splice(index, 1);
        this.checkTips(); // 关联人员提示是否显示
      } else if (id == "2") {
        this.scoreProcess[index].nodeProcessors.splice(idx, 1);
        if (this.scoreProcess[index].nodeProcessors.length == 0) {
          this.scoreProcess.splice(index, 1);
        }
      } else if (id == "3") {
        this.approveProcess[index].nodeProcessors.splice(idx, 1);
        this.checkApprove(this.approveProcess); // 检查审核流程是否只剩一个
        this.checkApprove2(this.approveProcess);
      }
    },
    // 对比看看变动的流程
    handleSubmit() {
      // this.handleSubmitList(this.dataMarkers2)//

      let params = {
        relationList: this.requestExamineeRelations, //关联人员
        approveProcessor: this.requestApproveProcessor, //审核流程
        scoreProcessors: this.requestScoreProcessors, //评分流程
        dataMarkProcessors: this.requestMarkProcessors, //数据来源指定人
        examineePlanId: this.changeObj.examineePlanId,
      };
      // this.params=params
      // console.log("params",params)
      return params;
    },
    handleStart() {
      this.loadingFlag = true;
      let params = this.handleSubmit();
      // 发请求
      postProcess(params)
        .then((res) => {
          this.loadingFlag = false;
          this.drawer = false;
          this.$emit("clickChangeEnsure", false);
          if (res.success) {
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          this.drawer = false;
          this.$emit("clickChangeEnsure", false);
        });
    },
    close() {
      // this.$confirm('确认关闭？')
      // .then(_ => {
      // this.drawer=false
      // this.changeData=[]
      // this.init()
      this.$emit("closeChange", false);
      // })
      // .catch(_ => {});
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";
.el-dialog-wrapper {
  color: #070f29;
  /deep/.el-loading-mask {
    width: 654px;
    left: calc(100% - 654px);
  }
  /deep/.el-drawer {
    width: 654px !important;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }
  /deep/.el-drawer__header {
    font-size: 16px;
    font-weight: 600;
    color: #070f29;
    padding: 16px 0;
    margin-bottom: 0;
    border-bottom: 1px solid #eaeaea;
    margin-left: 20px;
    margin-right: 20px;
  }
  /deep/.old-button--text {
    color: $mainColor;
    padding: 0;
  }
  .dialog-content {
    padding-left: 20px;
    padding-right: 20px;
    // padding-bottom: 80px;
    height: calc(100% - 80px);
    overflow: auto;
    font-size: 14px;
    .dialog-content-title {
      height: 16px;
      display: flex;
      align-items: center;
      margin-top: 30px;
      font-size: 16px;
      color: #070f29;
      font-weight: 600;
      .dialog-content-title-bg {
        display: inline-block;
        width: 4px;
        border-radius: 2px;
        height: 100%;
        background: $mainColor;
        margin-right: 10px;
        margin-top: 1px;
      }
    }
    .dialog-content-man {
      margin-top: 20px;
      display: flex;
      flex-direction: column;
      margin-left: 10px;
      .dialog-content-man-item {
        display: flex;
        align-items: center;
        // margin-bottom: 10px;
        .related-name {
          margin-left: 10px;
        }
        .dialog-content-man-item-change,
        .dialog-content-man-item-del {
          margin-left: auto;
        }
      }
    }
    .mt0 {
      margin-top: 0;
    }
    .mt10 {
      margin-top: 10px;
    }
    .mt20 {
      margin-top: 20px;
    }
    .mb37 {
      margin-bottom: 37px;
    }
    .grade {
      margin-top: 2px;
      display: flex;
      .grade-left {
        width: 17px;
        padding-top: 2px;
        display: flex;
        flex-direction: column;
        .grade-circle {
          width: 17px;
          height: 17px;
          border-radius: 50%;
          background: url(../../../images/circle.png) no-repeat;
          background-size: 100% 100%;
        }
        .grade-line {
          width: 7px;
          height: 100%;
          height: calc(100% - 20px);
          margin-top: 4px;
          border-right: 1px solid #eaeaea;
        }
      }
      .grade-detail {
        margin-left: 20px;
        padding-bottom: 15px;
        .grade-detail-item {
          margin-top: 20px;
          .dialog-content-grade-title {
            color: #888;
            margin-bottom: 10px;
          }
          .dialog-content-grade-base {
            width: 560px;
            display: flex;
            align-items: center;
            // padding-right: 20px;
            margin-bottom: 20px;
            .related-name {
              margin-left: 10px;
            }
            .grade-btn {
              margin-left: auto;
              .dialog-content-man-item-del {
                padding-left: 20px;
              }
            }
          }
          .dialog-content-grade-base:last-child {
            margin-bottom: 0px !important;
          }
        }
        .grade-detail-item:first-child {
          margin-top: 0;
        }
      }
    }
    .grade:first-child {
      margin-top: 0;
    }
    .boder-right-none {
      border-right: 1px solid transparent !important;
    }
    .dialog-content1 {
      .dialog-content1-tips {
        margin-left: 10px;
        margin-top: 20px;
        display: flex;
        align-items: center;
        color: #6a6f7f;
        .tip-color {
          color: #9ea5bd;
        }
        span {
          margin-left: 8px;
          // width: 23px;
          // height: 23px;
          // background: #FF9B0E;
          // border-radius: 50%;
          // margin-right: 10px;
        }
      }
    }
    .dialog-content2 {
      // .dialog-content-man-item-change {
      //   width: 200px;
      // }
      .related-name {
        width: 400px;
        display: flex;
        align-items: center;
        color: #070f29;
      }
      .dialog-content-man-item-change {
        width: 120px;
      }
      .indicator-box {
        // width: 350px;
        margin-left: 10px;
        text-indent: -1em;
        .indicator,
        .kuohao {
          color: #888;
        }
      }
    }
    .dialog-content3 {
      display: flex;
      align-items: center;
      margin-top: 15px;
      // margin-bottom: 10px;
      .popover {
        margin: 0 5px;
      }
      // /deep/.el-switch__core {
      //   width: 30px!important;
      //   height: 15px!important;
      // }
      // /deep/.is-checked {
      //   width: 30px!important;
      // }
      // /deep/.el-switch__core::after {
      //   width: 12px;
      //   height: 12px;
      // }
      // /deep/.el-switch.is-checked .el-switch__core::after {
      //   margin-left: -12px;
      // }
    }
    .avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .red {
    color: #d6342a;
    // color: $mainColor;
  }

  .footer {
    width: 614px;
    height: 80px;
    // margin-left: 20px;
    position: fixed;
    bottom: 0;
    right: 20px;
    padding: 20px 0;
    background: #fff;
    border-top: 1px solid #eaeaea;
    text-align: right;
  }
}
</style>
