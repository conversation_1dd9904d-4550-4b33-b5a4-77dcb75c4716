<template>
  <div class="el-dialog-wrapper">
    <div class="search">
      <el-select v-if="handleShow('1')" 
      @change="searchItemChange"
      v-model="subsidiaryId11" placeholder="请选择公司" clearable>
        <el-option
          v-for="item in options"
          :key="item.id"
          :label="item.name"
          :value="item.id">
        </el-option>
      </el-select>
      <el-input v-if="handleShow('3')" 
        placeholder="请输入员工姓名/手机号"
        style="width:250px;margin-right:10px;" 
        v-model="employeeName11" 
        @keyup.enter.native="searchItemChange"
        @input="searchItemChangeInput"
        >
        <!-- @input="searchItemChange" -->
        <!-- suffix-icon="el-icon-search"  -->
          <i slot="suffix" class="el-input__icon el-icon-search" ></i>
      </el-input>
      <!-- <el-select v-if="identityFlag==2||identityFlag==3" 
        class="chooseDep" v-model="deptId11" 
        placeholder="请选择部门"
        @input="searchItemChange"
      >
        <el-option
          v-for="item in options2"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select> -->
      <!-- <def-department v-model="deptId11" placeholder="请选择部门"  
          v-if="identityFlag==2||identityFlag==3"
          style="width:250px;" 
          @input="searchItemChange"
        /> -->
      <el-input
          v-if="identityFlag === 2||identityFlag === 3"
          v-model="valueBm11"
          placeholder="请选择部门"
          style="width:250px;"
          @focus="handleFocusDepart()"
          clearable
          @clear="inputClear"
        >
          <i slot="suffix" class="el-input__icon el-icon-plus" v-if="!valueBm11"></i>
        </el-input>
      <!-- 评分等级筛选 -->
      <div class="check-status" v-if="searchId==2">
        <el-radio-group v-model="processStatus11" size="medium" @change="statusChange">
          <el-radio-button label="ALL">全部</el-radio-button>
          <el-radio-button label="2">评分中</el-radio-button>
          <el-radio-button label="3">已评分</el-radio-button>
        </el-radio-group>
      </div>
      <!-- 待录入实际完成值 -->
      <div class="waitNum-label" v-if="searchId==3">待录入实际完成值<span class="waitNum-num">{{num}}</span>个</div>
      <!-- 考核结果 -->
      <!-- 审核等级筛选 -->
      <div class="check-status" v-if="searchId==4">
        <el-radio-group v-model="processStatus22" size="medium" @change="statusChange2">
          <el-radio-button label="ALL">全部</el-radio-button>
          <el-radio-button label="1">待审核</el-radio-button>
          <el-radio-button label="2">审核中</el-radio-button>
          <el-radio-button label="3">已审核</el-radio-button>
        </el-radio-group>
      </div>
      <div class="waitNum" v-if="searchId==4 && status!=3">
        <old-button type="plain" class="start2" @click="handleStart2">确认完成</old-button>
        <el-button type="primary" class="start1" @click="handleStart" v-if="showFF2">发放考核结果</el-button>
      </div>
      <el-button type="primary" class="start" @click="handleStart" v-if="searchId==1">确认启动</el-button>
    </div>
    <select-staff
      v-if="showDialog"
      :list="data"
      :isUser="isUser"
      :isOnly="true"
      :isDifferent="true"
      @close="showDialog = false"
      @commit="commit"
      :select="def_select"
    ></select-staff>
  </div>
</template>

<script>
import { defDepartment } from 'performance/pages/personalPerformance/components'
import { getDepartmentTree } from 'performance/store/api.js'
import SelectStaff from "performance/pages/performanceManage/components/SelectStaff";
export default {
  name: "startCheck-search",
  components: {
    defDepartment,
    SelectStaff
  },
  props: {
    searchId:null,
    identityFlag:null,
    options:{
      type:Array,
      default:()=>[]
    },
    employeeName:{
      type: String,
      default: ""
    },
    deptId:{
      type: Number,
      default: null
    },
    valueBm:null,
    subsidiaryId:{
      type: Number,
      default: null
    },
    processStatus:{
      type: String,
      default: "ALL"
    },
    processStatus2:{
      type: String,
      default: "ALL"
    },
    status:null,//考核计划状态
    num:null,//待录入实际完成值个数
    showFF:{
      type: Boolean,
      default: false
    }
    // identityFlag:null,
    // searchObj:{
    //   type:Object,
    //   default:()=>{}
    // }//搜索数据---searchId:"1",//区分右侧按钮; // options:[],//公司选项;identityFlag:1/2/3,//公司考核、部门考核还是个人考核
  },
  data() {
    return {
      def_select:[],
      // copySearchId:this.searchId,
      subsidiaryId11:this.subsidiaryId,
      deptId11:this.deptId,
      valueBm11:this.valueBm,
      employeeName11:this.employeeName,
      // identity: this.identityFlag,//
      // options: this.options,
      options2: [],
      options3: [],
      processStatus11:this.processStatus,
      processStatus22:this.processStatus2,
      timer:null,

      data: [],
      // section: [],
      isUser: true,
      showDialog:false,
      showFF2:this.showFF
    }
  },
  watch:{
    searchId(val){
      // this.copySearchId=val
    },
    identityFlag(val){
      // this.identity=val
    },
    employeeName(val){
      this.employeeName11=val
    },
    deptId(val){
      this.deptId11=val
    },
    valueBm(val){
      this.valueBm11=val
    },
    subsidiaryId(val){
      this.subsidiaryId11=val
    },
    processStatus(val){
      this.processStatus11=val
    },
    processStatus2(val){
      this.processStatus22=val
    },
    status(val){},
    showFF(val){
      this.showFF2=val
    }
  },
  methods: {
    commit(list) {
      if(list && list.length>0){
        this.valueBm11 = list[0].name;
        this.deptId11=list[0].id
        this.def_select = list
        // console.log("valueBm11",this.valueBm11)
        // console.log("deptId11",this.deptId11)
      }else{
        this.valueBm11=null
        this.deptId11=null
        this.def_select = []
      }
      // this.handleInit()
      // this.temporary2 = list[0].name;
      this.showDialog = false;
      this.searchItemChange()
    },
    //获取部门树
    async getDepartmentTree() {
      const res = await getDepartmentTree();
      if (res.success) {
        // this.section = res.data;
        // this.data = this.section;
        this.data = res.data;
      } else {
        this.$message.error(res.msg);
      }
      // console.log(res);
    },
    async handleFocusDepart() {
      await this.getDepartmentTree();
      this.showDialog = true;
    },
    inputClear(){
      this.valueBm11=''
      this.deptId11=null
      this.def_select = []
      this.searchItemChange()
    },


    handleShow(id){
      return this.identityFlag==id?true:false
    },
    handleStart(){
      this.$emit("start",false)
    },
    handleStart2(){
      this.$emit("start2",false)
    },
    statusChange(label){
      this.$emit("statusChange",label)
    },
    statusChange2(label){
      this.$emit("statusChange2",label)
    },
    searchItemChangeInput(val){
      // console.log("val: ",val)
      this.timer&&clearTimeout(this.timer)
      this.timer=setTimeout(()=>{
        val=val.trim()
        // console.log("fffff")
        this.searchItemChange(val)
      },500)
    },
    searchItemChange(val){
      this.$emit("searchItemChange",this.subsidiaryId11,this.deptId11,this.employeeName11,this.valueBm11)
    }
  },
};
</script>

<style lang="scss" scoped>
@import "../../../../assets/scss/helpers.scss";
.el-dialog-wrapper {
  .search {
    height: 40px;
    display: flex;
    align-items: center;
    
    font-size: 16px;
    .el-select {
      width: 260px;
      height: 100%;
      border: none;
      outline: none;
      border-radius: 2px;
      .el-input--suffix {
        border: 1px solid #EAEAEA;
      }
    }
    .el-input {
      width: 260px;
      height: 40px;
    }
    .chooseDep {
      margin-left: 20px;
    }
    .start {
      width: 98px;
      height: 40px;
      background: $mainColor;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: auto;
      font-size: 14px;
      color: #fff;
    }
    .check-status {
      margin-left: 10px;
    }
    .waitNum-label {
      font-size: 14px;
      margin-left: auto;
      .waitNum-num {
        color: #FF9B0E;
      }
    }
    .waitNum {
      margin-left: auto;
      display: flex;
      align-items: center;
      .waitNum-label {
        // margin-left: auto;
        font-size: 14px;
        margin-right: 20px;
        .waitNum-num {
          color:#E6A23C;
        }
      }
      .start1,.start2 {
        // width: 98px;
        // display: block;
        // height: 40px;
        border-radius: 4px;
        // display: flex;
        // justify-content: center;
        // align-items: center;
        font-size: 14px;
      }
      .start1 {
        background: $mainColor;
        color: #fff;
      }
      .start2 {
        border: 1px solid #ccc;
        margin-right: 10px;
      }
    }
  }
}
</style>