<template>
  <div class="wrapper">
    <!-- 内容区 -->
    <div class="content">
      <!-- 头部 -->
      <def-header 
        :headerText="def_HeaderData.headerText" 
        :isBack="true"
        :isShowTag="true"
        :headerTag="def_HeaderData.headerTag"
      />
      <!-- 查询栏 -->
      <div class="search-box">
        <search-header 
        :searchId="1" 
        :identityFlag="identityFlag"
        :options="options"
        :employeeName="employeeName"
        :deptId="deptId"
        :valueBm="valueBm"
        :subsidiaryId="subsidiaryId"
        @searchItemChange="searchItemChange"
        @start="start"></search-header>
      </div>
      <!-- 表格 -->
      <div class="table-list">
        <common-table 
          :loading="loading"
          :tableId='tableId'
          :identityFlag="identityFlag"
          :screenHeight="screenHeight"
          :tableData="tableData"
          :headerData="identityFlag==3?headerData2:headerData"
          :pageOptions="pageOptions"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @requestOneMore="requestOneMore"
        ></common-table>
          <!-- :operaOptions="operaOptions" -->
      </div>
    </div>
    <!-- 启动考核计划-弹框 -->
    <dlg-start :centerDialogVisible.sync="centerDialogVisible" :checkObjiectNum="checkObjiectNum" :wrongNum="wrongNum" 
    :startId="1" @clickStart="clickStart" @close="close"></dlg-start>
  </div>
</template>

<script>
import { getPlanDetailList,getStart,getCheck,getPlanBaseInfo,getSubsidiaryList } from 'performance/store/api.js'
import { khjhStatus } from 'performance/utils/enum.js'
import defHeader  from './components/Header.vue'
import dlgStart from "./components/dlgStart";
import searchHeader from "./components/searchHeader";
import commonTable from "./components/commonTable";
// import SelectStaff from "../performanceManage/components/SelectStaff";
export default {
  name:"performance-startCheck",
  components:{
    dlgStart,
    searchHeader,
    defHeader,
    commonTable
    // SelectStaff
  },
  data() {
    return {
      tableId:1,
      def_HeaderData:{},//top信息
      loading:true,//表格加载
      planName:'考核计划名称',//考核计划名称
      planId:null,//考核计划id
      planStatus:null,//考核计划状态
      // examineePlanId:7,//考核对象id
      confirmStatus:1,//确认状态--1:未开始,2:进行中,3:已完成
      identityFlag:null,//公司考核、部门考核还是个人考核
      employeeName:null,//员工姓名
      deptId:null,//部门id
      valueBm:null,
      subsidiaryId:null,//公司id
      screenHeight: document.body.clientHeight - 294,//屏幕高度-其他高度=表格高度
      // screenHeight2: document.body.clientHeight - 332,//屏幕高度-其他高度=表格高度
      
      centerDialogVisible:false,//启动考核计划-弹框flag
      options:[],
      // searchObj:{},//搜索数据---searchId:"1",//区分右侧按钮; // options:[],//公司选项;identityFlag:null,//公司考核、部门考核还是个人考核
      // searchId:1,
      checkObjiectNum:'',//考核对象总数
      wrongNum:'',//异常流程
      
      // 公司、部门的table
      tableData: [],
      headerData: [
        { title: "考核对象", label: "examineeName", slot:"examineeName",align:"left",minWidth:"120px", showTooltip: true,fixed:"left" },
        { title: "关联人员", label: "relations", slot:"relations",align:"left",minWidth:"100px", showTooltip: true},
        { title: "数据来源指定人", label: "dataMarkers", slot:"dataMarkers",align:"left",minWidth:"150px", showTooltip: true },
        { title: "评分人", label: "scorerList", slot:"scorerList",align:"left",minWidth:"200px", showTooltip: true },
        { title: "审核人", label: "auditorList",slot:"auditorList",align:"left",minWidth:"200px", showTooltip: true},
      ],
      pageOptions: {
        currPage: 1, //当前页码
        total: 10, //数据总数
        pageSize: 10, //每页显示条数
        pageSizes: [10, 20, 30, 40, 50, 100] //每页显示个数选择器选项设置
      },
      // operaOptions: {
      //     title: "操作", //名称
      //     width: 200, //宽度
      //     fixed: 'right',  // right - 固定在右侧
      //     align:"left",
      //     buttonList: [    //按钮列表
      //         { title: "修改流程" },
      //         { title: "移除" }
      //     ]
      // },
      // 个人的table
      headerData2: [
        { title: "考核对象", label: "examineeName", slot:"examineeName",align:"left",minWidth:"120px", showTooltip: true,fixed:"left" },
        { title: "公司名称", label: "subsidiaryName", slot:"subsidiaryName", align:"left",minWidth:"100px", showTooltip: true},
        { title: "部门", label: "deptName", slot:"deptName", align:"left",minWidth:"80px", showTooltip: true },
        { title: "评分人", label: "scorerList", slot:"scorerList",align:"left",minWidth:"200px", showTooltip: true },
        { title: "审核人", label: "auditorList",slot:"auditorList",align:"left",minWidth:"200px", showTooltip: true},
      ],
    }
  },
  // created(){
  // },
  mounted(){
    // console.log("document.body.clientHeight:",document.body.clientHeight)
    // resize
    this.handleInit();
    this.handleTableResize()
  },
  methods:{
    // 搜索栏公司、部门、人员变动
    searchItemChange(subsidiaryId,deptId,employeeName,valueBm){
      // console.log("接收到的employeeName",employeeName)
      // console.log("接收到的deptId",deptId)
      // console.log("接收到的subsidiaryId",subsidiaryId)
      this.pageOptions.currPage=1
      this.subsidiaryId=subsidiaryId
      this.deptId=deptId
      this.valueBm=valueBm
      this.employeeName=employeeName
      this.handleGetPlanDetailList()
    },
    // 修改后再请求一次
    requestOneMore(){
      this.handleGetPlanDetailList();//考核计划详情列表
    },
    handleInit(){
      const { planId } = this.$route.query
      this.planId = planId
      this.handleGetSubsidiaryList();//子公司(用工主体)
      this.handleGetPlanBaseInfo();//考核基本信息
      this.handleGetPlanDetailList();//考核计划详情列表
    },
    handleTableResize(){
      window.onresize=()=>{
        return ( ()=>{
          this.screenHeight=document.body.clientHeight - 294 + 'px'
          // this.screenHeight2=document.body.clientHeight - 332
        }
        )()
      }
    },
    //考核基本信息
    async handleGetPlanBaseInfo(){
      let obj = { planId:this.planId }
      const { data } = await getPlanBaseInfo(obj)
      const { name,type,status} = data
      this.def_HeaderData = {
        headerText:`${name}启动考核`,
        // headerText:`${name}`,
        headerTag:`${khjhStatus[status]}`
        // headerTag:"未开始"
        // headerTag:`待确认`
      }
      this.identityFlag = type;
      this.planStatus=status
      // this.identityFlag = 1;
      
    },
    async handleGetSubsidiaryList(){
      const { data } = await getSubsidiaryList()
      // console.log('data',data)
      this.options = data
      // this.searchObj={
      //   identityFlag:this.identityFlag,
      //   options:data
      // }
    },
    
    //分页size切换
    handleSizeChange(val) {
      this.loading=true
      this.pageOptions.pageSize=val
      this.handleGetPlanDetailList()
    },
    //页码切换
    handleCurrentChange(val) {
      this.loading=true
      this.pageOptions.currPage=val
      this.handleGetPlanDetailList()
        // console.log('当前页码', val)
    },

    // 点击启动考核
    start(){
      // 解决点击启动考核死循环刷新的bug
      if(this.planStatus!=1){
        this.$router.push({
          path:"/performance/checkDetail",
          query:{
            planId:this.planId,
            source:'fromStart'
          }
        })
      }else{
        getCheck({planId:this.planId}).then(res=>{
          if(res.success){
            const { errorNum,total } = res.data
            this.checkObjiectNum=total
            this.wrongNum=errorNum
            if(total==0){
              this.$message.error("请至少选择一个考核对象，才能启动考核计划")
              return false
            }
            this.centerDialogVisible=true
          }else{
            this.$message.error(res.msg)
          }
        })
      }
    },
    // 启动考核计划-确定
    clickStart(e,id){
      // console.log("确定id",id)
      id==1 && getStart({
        planId:this.planId,
      }).then(res=>{
        if(res.success){
          this.centerDialogVisible=e
          // this.$message.success("启动成功")
          this.$router.push({
            path:"/performance/checkDetail",
            query:{
              planId:this.planId,
              source:'fromStart'
            }
          })
        }else{
          // this.$message.error(res.msg)
        }
      })
    },
    // 启动考核计划-关闭弹窗
    close(e){
      this.centerDialogVisible=e
    },
    
    // 请求列表数据
    handleGetPlanDetailList(){
      this.loading=true
      let params={
        currentPage:this.pageOptions.currPage,
        pageSize:this.pageOptions.pageSize,
        deptId:this.deptId,
        employeeName:this.employeeName,
        subsidiaryId:this.subsidiaryId,
        listType:2,
        planId:this.planId,
        // scoreLevel:this.scoreLevel,
        // time:this.time
      }
      // console.log("params",params)
      getPlanDetailList(params).then(res=>{
        // console.log('res',res)
        this.loading=false
        if(res.success){
          // this.pageOptions.total=res.data.total
          Object.assign(this.pageOptions,{
            total:res.data.total
          })
          const { records } = res.data
          this.tableData = records
        }else{
          this.$message.error(res.msg)
        }
      })
    },
    
  }
};
</script>

<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";
.wrapper {
  height: 100%;
  background: #fff;
  /deep/ .el-button--small {
    font-size: 14px;
  }
  .content {
    height: 100%;
    padding: 0 20px;
    .search-box {
      height: 40px;
      margin-top: 20px;
      margin-bottom: 20px;
    }
  }
}
</style>

