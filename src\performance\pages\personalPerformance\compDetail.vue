<template>
  <div
    class="comp_detail def_per_height"
    v-if="!loading"
    v-loading="loading"
  >
    <def-header
      :headerText="def_HeaderData.headerText"
      :isBack="true"
      :isShowTag="true"
      :headerTag="def_HeaderData.headerTag"
    />

    <section 
      class="def_per_section def_per_section-top"
    >
      <def-card
        :isShowPhoto="handleIsshowCardPhoto()"
        :cardPhoto="def_CardData.cardPhoto"
        :lineOne="def_CardData.cardDataLineOne"
        :lineTwo="def_CardData.cardDataLineTwo"
        :lineThree="def_CardData.cardDataLineThree"
        :lineFour="def_CardData.cardDataLineFour"
        :isShowRate="handleIsshowRate()"
        :rate="def_CardData.cardRate"
        :isShowStep="true"
        :steps="def_CardData.cardSteps"
      >
        <section slot="btnArea">
          <old-button>修改流程</old-button>
        </section>
      </def-card>
    </section>
    <section
      class="def_per_section def_per_section-top"
      v-if="handleSourceSection('绩效结果')||stageStatus===6"
    >
      <def-title text="绩效结果" />
      <detail-jxjg 
        :score="def_CardData.cardRate.score"
        :grade="def_CardData.cardRate.grade"
      />
    </section>
    <section
      class="def_per_section def_per_section-top"
      v-if="handleSourceSection('考核指标评分明细')"
    >
      <def-title text="考核指标评分明细" />
      <section class="detail-table def_fixed_height">
        <def-etable
          ref="def_etable"
          :tableHeader="tableHeader_khzbpf"
          :tableData="def_TableData"
          @formatter="handleFormatter"
          @btnColumn="handleBtnColumn"
          @search="handleSearch"
          :total="total"
          :isShowIndex="true"
          :def_height="tableHeight"
          :isHidePage="true"
        />
      </section>
    </section>

    <section
      class="def_per_section def_per_section-top"
      v-if="handleSourceSection('考核指标明细')"
    >
      <def-title text="考核指标明细" />
      <section class="detail-table def_fixed_height" 
        v-if="def_TableData.length !== 0"
      >
        <def-etable
          ref="def_etable"
          :tableHeader="tableHeader_khzb"
          :tableData="def_TableData"
          @formatter="handleFormatter"
          @btnColumn="handleBtnColumn"
          @search="handleSearch"
          :total="total"
          :isShowIndex="true"
          :def_height="tableHeight"
          :isHidePage="true"
        />
      </section>
    </section>

    <section
      class="def_per_section def_per_section-top lc"
      v-if="def_CommentData.length !== 0&&handleSourceSection('总评')"
    >
      <def-title text="总评" />
      <detailZp 
        :list="def_CommentData"
      />
    </section>

    <section
      class="def_per_section def_per_section-top"
      v-if="handleSourceSection('考核指标修正记录')"
    >
      <def-title text="考核指标修正记录" />
    </section>

    <section
      class="def_per_section def_per_section-top lc"
      v-if="handleSourceSection('考核评分流程')"
    >
      <def-title text="考核评分流程" />
      <detail-lc 
        :list="def_ScoreProcessData"
      />
    </section>

    <section
      class="def_per_section def_per_section-top lc"
      v-if="handleSourceSection('结果审核流程')"
    >
      <def-title text="结果审核流程" />
      <detail-lc 
        :list="def_ApproveProcessData"
      />
    </section>

    <section
      class="def_per_section def_per_section-top lc"
      v-if="handleSourceSection('考核确认流程')"
    >
      <def-title text="考核确认流程" />
      <detail-lc 
        :list="def_ConfirmProcessData"
      />
    </section>

    <!-- <section style="height:115px"></section> -->
    
  </div>
</template>

<script>
import {
  defHeader,
  defCard,
  defNode,
  defTitle,
  defTable,
  defPhoto,
  defEtable,
  defNewNode
} from "./components";
import {
  getMyPlanDetail,
  getPlanDetail,
  getExamineePlanDetail,
  getMyPlanTodoConfirm,
  getMyPlanTodoSetScore,
  getMyPlanTodoApprove
} from "performance/store/api.js";
import {
  pfType,
  khpfjdztStatus,
  khqrStatus,
  khlxType,
  khqrztStatus,
  khzqPeriodType,
  jdzxztNodetype,
  jdtType,
  zblxType,
  pffsScoreType,
  khqrjdztStatus,
  stageStatus,
  otherStatus
} from "performance/utils/enum.js";
import indicatorList from "performance/pages/performanceManage/components/IndicatorList";
import { math } from "performance/utils/math.js";
import { date2Str } from "performance/utils/util.js";

import detailJxjg from "performance/pages/personalPerformance/components/compDetail/DetailJxjg"
import detailZp from "performance/pages/personalPerformance/components/compDetail/DetailZp"
import detailLc from "performance/pages/personalPerformance/components/compDetail/DetailLc"

export default {
  name: "comp_detail",
  components: {
    defHeader,
    defCard,
    defNode,
    defTitle,
    defTable,
    defPhoto,
    defEtable,
    defNewNode,
    indicatorList,
    detailJxjg,
    detailZp,
    detailLc
  },
  data() {
    return {
      isShowAlert:false,
      $dayjs: this.$dayjs,
      examineePlanId: null,
      planId:null,
      loading: true,
      type: null,
      /*
        详情
      */

      basicInfo: {}, //考核对象基本信息
      indicatorList: [], //考核指标信息
      process: {}, //考核对象的流程数据
      resultSetting: {}, //考核结果设置
      scoreLevel: "", //总分Level
      totalScore: null, //总分
      processorType: null, //1:被考核者,2:上级,3:指定人员
      adminType: {}, //当前登录人对当前计划的状态 1:处理中,2:等待处理,3:已处理

      stageStatus:null,//当前登录人所看到的状态
      basicInfoStageStatus:null,//当前考核对象所在状态

      // tableHeight:document.body.clientHeight - 500 +'px',
      tableHeight: "auto",
      total: null,
      limit: 10,
      start: 0,
      page: 1,

      tableHeaderBase: [
        { label: "考核指标名称", prop: "khzbmc",width:'150px' },
        { label: "考核指标类型", prop: "khzblx",width:'150px' },
        { label: "考核指标说明", prop: "khzbsm" ,isTip:true },
        { label: "评价标准", prop: "pjbz",width:'150px' ,isTip:true },
        { label: "评分上限", prop: "pfsx",width:'150px',align:"right" },
        { label: "考核指标权重", prop: "khzbqz",width:'150px',align:"right" },
        { label: "目标值", prop: "mbz",width:'150px',align:"right" },
        { label: "评分方式", prop: "pffs",width:'150px' ,isTip:true },
        { label: "实际完成值", prop: "sjwcz",width:'150px',align:"right" },
        {
          label: "绩效评分",
          prop: "jxpf",
          isFixed:'right',
          width:'550px',
          align:"center",
          children: [
            { label: "评分人", prop: "pfr", type: "addRow",isFixed:'right',width:'150px',isTip:true  },
            {
              label: "考核指标评分",
              prop: "khzbpf",
              type: "addRow",
              isEdit: true,
              realProp: "score",
              fatherProp: "scoreData",
              isEditRowKey: "isCanEditScore",
              isShowTag: true,
              editType:"number",
              numberType:"zs",
              tagProp: "maxScore",
              isFixed:'right',
              width:'200px',
              align:"right"
            },
            {
              label: "考核指标评语",
              prop: "khzbpy",
              type: "addRow",
              isEdit: true,
              realProp: "comment",
              fatherProp: "scoreData",
              isEditRowKey: "isCanEditComment",
              editType:"textarea",
              maxlength:200,
              isTip:true,
              isFixed:'right',
              width:'200px'
            }
          ]
        },
        { label: "考核指标总分", prop: "khzbzf" ,isFixed:'right',width:'150px',align:"right"},
        { label: "考核指标评分人", prop: "khzbpfr",width:'150px' }
      ],

      /**
       * 考核指标明细
       */
      tableHeader_khzb: [],
      /**
       * 考核指标评分明细
       */
      tableHeader_khzbpf: [],

      // khqrjdztStatus: khqrjdztStatus, //考核确认节点状态
      // khpfjdztStatus: khpfjdztStatus, //考核评分节点状态
      otherStatus: otherStatus, 
      pfType: pfType, //评分类型

      def_HeaderData: {},
      def_CardData: {},
      def_TableData: [],
      def_CommentData: [],
      def_ScoreProcessData: [],
      def_ConfirmProcessData: [],
      def_ChangeProcessData: [],
      def_ApproveProcessData: [],
      def_StartInfo: {},
      def_DataMarkersData:{},

      /**
       * xq_pf:考核详情-评分
       * xq_sh:考核详情-审核
       * xq_qr:考核详情-确认
       */
      source: "xq_all",
      sourceShowSection: {
        xq_pf: ["考核指标评分明细", "总评", "考核评分流程"],
        xq_sh: ["绩效结果", "考核指标评分明细", "总评", "结果审核流程"],
        xq_qr: ["考核指标明细", "考核确认流程"],
        xq_all: ["考核指标评分明细","考核评分流程","绩效结果", "总评", "结果审核流程","考核指标明细", "考核确认流程"]
      },

      def_submitIndicatorListData: {}, //评分详情 - 考核指标评分明细提交数据汇总
      def_submitCommentProcessData: "", //评分详情 - 总评提交数据汇总
      def_IndicatorListDataSum: {} //当前评分人所有指标评分汇总
    };
  },
  mounted() {
    this.handleInit();
  },
  methods: {
    handleInit() {
      this.handleGetMyPlanDetail();
    },
    handleAutoHeight(){
      this.$nextTick(() => {
        let doms = document.querySelectorAll('.def_fixed_height')
        doms.forEach(v=>{
          console.log(v.clientHeight)
          if(v.clientHeight > 500){
            this.tableHeight = '500px'
          }
        })
        // this.$refs.def_etable.$refs.Etable.doLayout();
      })
    },
    handleSourceSection(name) {
      return this.sourceShowSection[this.source].includes(name);
    },
    //是否显示评分等级
    handleIsshowRate() {
      if([6,7,8,9].includes(this.stageStatus)){
        return true
      }else{
        return false
      }
    },
    async handleGetMyPlanDetail() {
      const { examineePlanId,planId } = this.$route.query;
      this.examineePlanId = Number(examineePlanId);
      this.planId = Number(planId);
      let obj = {
        examineePlanId: this.examineePlanId,
        planId:this.planId
      };
      if (true) {
        // const { data } = await getMyPlanDetail(obj);
        const { data } = await getExamineePlanDetail(obj);
        const { basicInfo,indicatorList,process,resultSetting,scoreLevel,totalScore } = data;
        const { confirmStatus, scoreStatus, approveStatus } = data;
        this.basicInfo = basicInfo; //考核对象基本信息
        this.indicatorList = indicatorList; //考核指标信息
        this.process = process; //考核对象的流程数据
        this.resultSetting = resultSetting; //考核结果设置
        this.scoreLevel = scoreLevel; //总分Level
        this.totalScore = totalScore; //总分
        this.type = this.basicInfo.type; //考核类型 1:公司考核,2:部门考核,3:个人考核

        this.adminType = { confirmStatus, scoreStatus, approveStatus };

        this.stageStatus = this.basicInfo.stageStatus;//当前详情状态
        this.basicInfoStageStatus = this.basicInfo.stageStatus;//当前考核对象所在状态

        if([1,2,3].includes(this.stageStatus)){
          this.source = "xq_qr";
        }
        if([4,5,6].includes(this.stageStatus)){
          this.source = "xq_pf";
        }
        if([7,8,9].includes(this.stageStatus)){
          this.source = "xq_sh";
        }
      }
      this.handleBaseData();
      this.hanldeFilterTableHeader();
      this.$nextTick(() => {
        this.handleAutoHeight();//通过渲染高度 控制table固定高度
      })
      

      this.loading = false;
    },
    //确定表头信息
    hanldeFilterTableHeader(val) {
      this.tableHeader_khzbpf = this.tableHeaderBase.filter(v => {
        if([6,7,8,9].includes(this.stageStatus)){
          return ["khzbmc","khzblx","khzbsm","pjbz","pfsx","khzbqz","mbz","sjwcz","jxpf","khzbzf"].includes(v.prop);
        }else{
          return ["khzbmc","khzblx","khzbsm","pjbz","pfsx","khzbqz","mbz","sjwcz","jxpf"].includes(v.prop);
        }
      });
      this.tableHeader_khzb = this.tableHeaderBase.filter(v => {
        if([6,7,8,9].includes(this.stageStatus)){
          return ["khzbmc","khzblx","khzbsm","pjbz","pfsx","khzbqz","mbz","pffs","khzbpfr","khzbzf"].includes(v.prop);
        }else{
          return ["khzbmc","khzblx","khzbsm","pjbz","pfsx","khzbqz","mbz","pffs","khzbpfr"].includes(v.prop);
        }
      });
    },
    handleArrBaseData(list, key) {
      if(list===null||list.length===0){
        return '--'
      }
      let arr = [];
      list.map(v => {
        arr.push(v[key]);
      });
      return arr.join("，");
    },
    /**
     * 详情
     */
    //构造渲染数据
    handleBaseData() {
      let isGr = this.type === 3 ? true : false;
      if (this.source == "xq_pf") {
        this.def_HeaderData = {
          headerText: this.handleHeaderName(),
          headerTag: this.handleHeaderTag()
        };
        this.def_CardData = {
          cardPhoto: this.basicInfo.employeeName,
          cardDataLineOne: {
            name:this.handleCardName(),
            phone: this.basicInfo.mobile,
            tag: khlxType[this.basicInfo.type]
          },
          cardDataLineTwo: {
            label: isGr ? "" : "考核关联人员:",
            value: isGr
              ? `${this.basicInfo.subsidiaryName} ${this.basicInfo.deptName}`
              : this.handleExamineeRelations(this.basicInfo.examineeRelations)
          },
          cardDataLineThree: {
            label: "考核计划名称:",
            value: this.basicInfo.name
          },
          cardDataLineFour: {
            label: "考核周期:",
            value: date2Str(
              this.basicInfo.period,
              this.basicInfo.startDate,
              this.basicInfo.endDate
            )
          },
          cardRate: {
            score: this.totalScore,
            grade: this.scoreLevel || "无"
          },
          cardSteps: this.process.statusProcess.map(v => {
            return {
              id: v.key,
              state: v.nodeType,
              text: this.handleStepsNodeName(v.nodes, v.nodeType),
              tips: this.handleStepsNodeTips(v.nodes, v.nodeType),
            };
          })
        };
      }
      if (this.source == "xq_sh") {
        this.def_HeaderData = {
          headerText: this.handleHeaderName(),
          // headerTag:`${khqrStatus[this.basicInfo.confirmStatus]}`
          headerTag: this.handleHeaderTag()
        };
        this.def_CardData = {
          cardPhoto: this.basicInfo.employeeName,
          cardDataLineOne: {
            name:this.handleCardName(),
            phone: this.basicInfo.mobile,
            tag: khlxType[this.basicInfo.type]
          },
          cardDataLineTwo: {
            label: isGr ? "" : "考核关联人员:",
            value: isGr
              ? `${this.basicInfo.subsidiaryName} ${this.basicInfo.deptName}`
              : this.handleExamineeRelations(this.basicInfo.examineeRelations)
          },
          cardDataLineThree: {
            label: "考核计划名称:",
            value: this.basicInfo.name
          },
          cardDataLineFour: {
            label: "考核周期:",
            value: date2Str(
              this.basicInfo.period,
              this.basicInfo.startDate,
              this.basicInfo.endDate
            )
          },
          cardRate: {
            score: this.totalScore,
            grade: this.scoreLevel || "无"
          },
          cardSteps: this.process.statusProcess.map(v => {
            return {
              id: v.key,
              state: v.nodeType,
              text: this.handleStepsNodeName(v.nodes, v.nodeType),
              tips: this.handleStepsNodeTips(v.nodes, v.nodeType),
            };
          })
        };
      }
      if (this.source == "xq_qr") {
        this.def_HeaderData = {
          headerText: this.handleHeaderName(),
          headerTag: this.handleHeaderTag()
        };
        this.def_CardData = {
          cardPhoto: this.basicInfo.employeeName,
          cardDataLineOne: {
            name:this.handleCardName(),
            phone: this.basicInfo.mobile,
            tag: khlxType[this.basicInfo.type]
          },
          cardDataLineTwo: {
            label: isGr ? "" : "考核关联人员:",
            value: isGr
              ? `${this.basicInfo.subsidiaryName} ${this.basicInfo.deptName}`
              : this.handleExamineeRelations(this.basicInfo.examineeRelations)
          },
          cardDataLineThree: {
            label: "考核计划名称:",
            value: this.basicInfo.name
          },
          cardDataLineFour: {
            label: "考核周期:",
            value: date2Str(
              this.basicInfo.period,
              this.basicInfo.startDate,
              this.basicInfo.endDate
            )
          },
          cardRate: {
            score: this.totalScore,
            grade: this.scoreLevel || "无"
          },
          cardSteps: this.process.statusProcess.map(v => {
            return {
              id: v.key,
              state: v.nodeType,
              text: this.handleStepsNodeName(v.nodes, v.nodeType),
              tips: this.handleStepsNodeTips(v.nodes, v.nodeType),
            };
          })
        };
      }

      if (this.source == "xq_all"){
        this.def_HeaderData = {
          headerText: this.handleHeaderName(),
          // headerTag:`${khqrStatus[this.basicInfo.confirmStatus]}`
          headerTag: this.handleHeaderTag()
        };
        this.def_CardData = {
          cardPhoto: this.basicInfo.employeeName,
          cardDataLineOne: {
            name:this.handleCardName(),
            phone: this.basicInfo.mobile,
            tag: khlxType[this.basicInfo.type]
          },
          cardDataLineTwo: {
            label: isGr ? "" : "考核关联人员:",
            value: isGr
              ? `${this.basicInfo.subsidiaryName} ${this.basicInfo.deptName}`
              : this.handleExamineeRelations(this.basicInfo.examineeRelations)
          },
          cardDataLineThree: {
            label: "考核计划名称:",
            value: this.basicInfo.name
          },
          cardDataLineFour: {
            label: "考核周期:",
            value: date2Str(
              this.basicInfo.period,
              this.basicInfo.startDate,
              this.basicInfo.endDate
            )
          },
          cardRate: {
            score: this.totalScore,
            grade: this.scoreLevel || "无"
          },
          cardSteps: this.process.statusProcess.map(v => {
            return {
              id: v.key,
              state: v.nodeType,
              text: this.handleStepsNodeName(v.nodes, v.nodeType),
              tips: this.handleStepsNodeTips(v.nodes, v.nodeType),
            };
          })
        };
      }

      this.def_StartInfo = this.process.startInfo; //考核对象各个阶段启动数据
      this.def_TableData = this.indicatorList; //考核指标信息
      this.def_CommentData = this.process.commentProcess; //总评数据
      this.def_ScoreProcessData = this.process.scoreProcess; //评分流程
      this.def_ConfirmProcessData = this.process.confirmProcess; //确认流程
      this.def_ApproveProcessData = this.process.approveProcess; //审核流程
      this.def_DataMarkersData = this.process.dataMarkers; //审核流程
      
      this.handleAddDataMarkers(); //拼接录入实际完成值流程
      this.handleAddStartInfo(); //拼接考核确认流程
      
    },
    //拼接考核确认流程
    handleAddStartInfo() {
      if (Object.keys(this.def_StartInfo).length !== 0) {
        if (this.def_StartInfo["confirmStarterId"] !== 0) {
          this.def_ConfirmProcessData.unshift({
            det_nodeProcessors: true,
            nodeType: 3,
            nodeProcessors: [
              {
                processorName: this.def_StartInfo["confirmStarterName"],
                processorText: "发起了考核确认",
                status: 3,
                time: this.$dayjs(
                  this.def_StartInfo["confirmStartTime"]
                ).format("YYYY-MM-DD HH:mm")
              }
            ]
          });
        }
        if (this.def_StartInfo["scoreStarterId"] !== 0) {
          this.def_ScoreProcessData.unshift({
            det_nodeProcessors: true,
            nodeType: 3,
            nodeProcessors: [
              {
                processorName: this.def_StartInfo["scoreStarterName"],
                processorText: "启动了考核",
                status: 3,
                time: this.$dayjs(this.def_StartInfo["scoreStartTime"]).format(
                  "YYYY-MM-DD HH:mm"
                )
              }
            ]
          });
        }
        if (this.def_StartInfo["approveStarterId"] !== 0) {
          this.def_ApproveProcessData.unshift({
            det_nodeProcessors: true,
            nodeType: 3,
            nodeProcessors: [
              {
                processorName: this.def_StartInfo["approveStarterName"],
                processorText: "发放考核结果",
                status: 3,
                time: this.$dayjs(
                  this.def_StartInfo["approveStartTime"]
                ).format("YYYY-MM-DD HH:mm")
              }
            ]
          });
        }
      }
    },
    handleAddDataMarkers(){
      if (Object.keys(this.def_DataMarkersData).length !== 0) {
          let arr = [],
              boo = false;
          this.def_DataMarkersData.map(v=>{
            arr.push({
              processorName: v["dataMarkerName"],
              processorText: this.handleLrName(v),
              status: v["operationTime"] ? 3 : 1,
              time: v["operationTime"] ? this.$dayjs(v["operationTime"]).format("YYYY-MM-DD HH:mm") : ""
            })
            if(!boo){
              boo = v["operationTime"] ? true : false
            }
          })
          this.def_ScoreProcessData.unshift({
            det_nodeProcessors: true,
            nodeType: boo ? 3 : 1,
            nodeProcessors: arr
          });
      }
    },
    handleLrName(v){
      let text = ""
      if(v["operationTime"]){
        if(v["replaceUserName"]){
          text = `已录入实际完成值（${v["replaceUserName"]}代录入）`
        }else{
          text = `已录入实际完成值`
        }
      }else{
        text = "未录入实际完成值"
      }
      return text
    },
    handleHeaderName() {
      const {
        subsidiaryName,
        deptName,
        employeeName,
      } = this.basicInfo;
      switch (this.type) {
        case 1:
          return `${subsidiaryName}考核详情`;
        case 2:
          return `${deptName}考核详情`;
        case 3:
          return `${employeeName}考核详情`;
      }
    },
    handleCardName(){
      const { subsidiaryName,deptName,employeeName } = this.basicInfo
      switch(this.type){
        case 1 :
          return `${subsidiaryName}`
        case 2 :
          return `${deptName}`
        case 3 :
          return `${employeeName}`
      }
    },
    handleHeaderTag() {
      return stageStatus[this.basicInfoStageStatus]
    },
    //card 进度条文案
    handleStepsNodeName(items, nodeType) {
      // let val = "";
      let objColor = {
        1: "#4F71FF",
        2: "#BBBBBB",
        3: "#555555"
      };
      let arr = [],
          baseObj={};
      items.map(v => {
        const { key, name, type } = v;
        if(Reflect.ownKeys(baseObj).includes(key)){
          baseObj[key].push(v)
        }else{
          baseObj[key] = [v]
        }
      });
      for (const i in baseObj) {
        let def_key = i ? 
          `<span style="color:${objColor[nodeType]}">${i}： </sapn>` :
          `<span style="color:${objColor[nodeType]}">${i} </sapn>`;
        let def_addTextArr = [];
        baseObj[i].map(v=>{
          const { key, name, type } = v;
          let def_name = `<span style="color:${objColor[nodeType]}">${name} </sapn>`;
          let def_type = "";
          if (type === 1) {
            def_type = `<span style="color:#FF9500">${this.handleStepsNodeState(type)}</sapn>`;
          }
          def_addTextArr.push(def_name + def_type)
        })
        let def_text = def_key + def_addTextArr.join(`<span style="color:${objColor[nodeType]}">、</sapn>`)
        arr.push(def_text)
      }
      return arr.join(`<span style="color:${objColor[nodeType]}">，</sapn>`);
    },
    handleStepsNodeTips(items, nodeType){
      let arr = [],
          baseObj={};
      items.map(v => {
        const { key, name, type } = v;
        if(Reflect.ownKeys(baseObj).includes(key)){
          baseObj[key].push(v)
        }else{
          baseObj[key] = [v]
        }
      });
      for (const i in baseObj) {
        let def_key = i ? 
          `${i}： ` :
          `${i} `;
        let def_addText = "";
        let def_addTextArr = [];
        baseObj[i].map(v=>{
          const { key, name, type } = v;
          let def_name = `${name} `;
          let def_type = "";
          if (type === 1) {
            def_type = `${this.handleStepsNodeState(type)}`;
          }
          def_addTextArr.push(def_name + def_type)
        })
        let def_text = def_key + def_addTextArr.join(`、`)
        arr.push(def_text)
      }
      return arr.join(`、`);
    },
    //进度条状态
    handleStepsNodeState(type) {
      return otherStatus[type]
    },
    handleFormatter({ prop, data, btnItem }, callback) {
      switch (prop) {
        case "khzbmc":
          callback(data["name"]==null||data["name"]==="" ? '--':data["name"]);
          break;
        case "khzblx":
          callback(zblxType[data["type"]]||'--');
          break;
        case "khzbsm":
          callback(data["description"]==null||data["description"]===""?'--':data["description"]);
          break;
        case "pjbz":
          callback(data["scoreStandard"]==null||data["scoreStandard"]===""?'--':data["scoreStandard"]);
          break;
        case "pfsx":
          callback(data["maxScore"]==null||data["maxScore"]===""?'--':data["maxScore"]+'分');
          break;
        case "khzbqz":
          // callback(data["weight"]==null||data["weight"]==0 ? '--' : data["weight"]);
          callback(data["weight"]==null||data["weight"]==="" ? '--' : data["weight"]+'%');
          break;
        case "mbz":
          callback(data["targetValue"] == null ? '--' : data["targetValue"]+data["dataUnit"]);
          break;
        case "sjwcz":
          callback(data["realValue"] == null ? '--' : data["realValue"]+data["dataUnit"]);
          break;
        case "pfr":
          callback(this.handleColumnsArrBaseData(data["scoreData"], "pfr"));
          break;
        case "khzbpf":
          callback(this.handleColumnsArrBaseData(data["scoreData"], "khzbpf"));
          break;
        case "khzbpy":
          callback(this.handleColumnsArrBaseData(data["scoreData"], "khzbpy"));
          break;
        case "khzbzf":
          callback(data["score"]==null||data["score"]==="" ? '--':data["score"]);
          break;
        case "pffs":
          callback(pffsScoreType[data["scoreType"]]||'--');
          break;
        case "khzbpfr":
          // if(data["scoreType"]===2){
          //   callback(`<span style="color:#BBBBBB">系统评分</span>`);
          // }else{
          //   callback(this.handleArrBaseData(data["scoreData"], "employeeName"));
          // }
          callback(this.handleArrBaseData(data["scoreData"], "employeeName"));
          break;
      }
    },
    handleColumnsArrBaseData(list, key) {
      // console.log(list)
      if( list == null ){
        return '--'
      }
      let obj = {
        pfr: "employeeName",
        khzbpf: "score",
        khzbpy: "comment"
      };
      let arr = [];
      list.map(v => {
        if(v[obj[key]]===null||v[obj[key]]===""){
          arr.push('--')
        }else{
          if(key=="khzbpf"){
            arr.push(`${v[obj[key]]}分`);
          }else{
            arr.push(v[obj[key]]);
          }
        }
      });
      return arr;
    },
    handleBtnColumn(val, type) {
      console.log(val, type);
      switch (type) {
      }
    },
    handleSearch({ limit, start, page }) {},
    //是否展示卡片头像
    handleIsshowCardPhoto() {
      /**
       * 个人考核展示头像
       * 1:公司考核,2:部门考核,3:个人考核
       */
      return this.type === 3 ? true : false;
    },
    handleExamineeRelations(list) {
      let arr = [];
      list.map(v => {
        arr.push(v.employeeName);
      });
      return arr.join("，");
    },
  },
  watch: {}
};
</script>

<style lang="scss" scoped>
@media screen and (min-width: 1300px) {
  .def_per_section {
    padding:0 100px;
  }
}
.comp_detail {
  /deep/.el-alert--info.is-light{
    font-size: 14px;
    color: #555555;
    background-color: #F4F7FF;
    border: 1px solid #A4B5FF;
  }
  .detail-table {
    margin-top: 20px;
  }
}
</style>
