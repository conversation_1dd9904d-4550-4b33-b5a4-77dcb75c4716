<template>
  <div class="def_card">
    <section class="card-top">
      <section class="card-top-left">
        <def-photo v-if="isShowPhoto" :name="cardPhoto" boxSize="60px" textSize="17.5px" />
        <section class="top-right">
          <section class="top-right-line1">
            <span class="top-right-name" v-if="lineOne.name">{{lineOne.name}}</span>
            <span class="top-right-phone" v-if="lineOne.phone">{{lineOne.phone}}</span>
            <span class="top-right-tag" v-if="lineOne.tag">{{lineOne.tag}}</span>
          </section>
          <section class="top-right-line2 def_auto"><span class="line2-title" v-if="lineTwo.label">{{lineTwo.label}} </span><span class="line2-text" v-if="lineTwo.value">{{lineTwo.value}}</span></section>
          <section class="top-right-line3 def_auto"><span class="line3-title" v-if="lineThree.label">{{lineThree.label}} </span><span class="line3-text" v-if="lineThree.value">{{lineThree.value}}</span></section>
          <section class="top-right-line4 def_auto"><span class="line4-title" v-if="lineFour.label">{{lineFour.label}} </span><span class="line4-text" v-if="lineFour.value">{{lineFour.value}}</span></section>
        </section>
      </section>
      <section class="card-top-right" v-if="isShowRate">
        <section class="top-right-left"><span class="left-score">{{rate.score}}分</span><span class="left-text">总评分</span></section>
        <section class="top-right-right">
          <span class="right-grade" v-if="rate.grade.length<=3">{{rate.grade}}</span>
          <el-tooltip v-else effect="dark" placement="top" :content="rate.grade">
            <div class="status-num-item-top">
              <span class="right-grade">{{rate.grade.slice(0,3)+'...'}}</span>
            </div>
          </el-tooltip>
          <span class="right-text">绩效等级</span>
        </section>
      </section>
    </section>
    <el-divider v-if="isShowStep"></el-divider>
    <section class="card-bottom" v-if="isShowStep">
      <template v-for="(item,index) in steps">
        <def-steps 
          :key="item.id"
          :showLine="index==steps.length-1 ? false : true"
          :stepText="item.text"
          :stepState="item.state"
          :stepTip="item.tips"
          style="padding-bottom: 5px;"
          ></def-steps>
      </template>
    </section>
  </div>
</template>

<script>
import defSteps from './Steps'
import defPhoto from './Photo'

export default {
  name:"def_card",
  components:{
    defSteps,
    defPhoto
  },
  props:{
    isShowPhoto: {
      type: Boolean,
      default: true
    },
    cardPhoto: {
      type: String,
      default: '张三'
    },
    lineOne:{
      type: Object,
      default: ()=>{
        return {
          name:"",
          phone:"",
          tag:"",
        }
      }
    },
    lineTwo:{
      type: Object,
      default: ()=>{
        return {
          label:"",
          value:""
        }
      }
    },
    lineThree:{
      type: Object,
      default: ()=>{
        return {
          label:"",
          value:""
        }
      }
    },
    lineFour:{
      type: Object,
      default: ()=>{
        return {
          label:"",
          value:""
        }
      }
    },
    rate:{
      type: Object,
      default: ()=>{
        return {
          score:98,
          grade:"A"
        }
      }
    },
    isShowRate:{
      type:Boolean,
      default:false
    },
    isShowStep:{
      type:Boolean,
      default:false
    },
    steps:{
      type:Array,
      default:()=>[]
    }
  },
  data(){
    return {

    }
  }
}
</script>

<style lang="scss" scoped>
.def_card{
  display: flex;
  flex-direction: column;
  // margin:10px;
  padding:30px;
  min-width:500px;
  // -webkit-box-shadow: 0 2px 12px 0 #ebeef5;
  // box-shadow: 0 2px 12px 0 #ebeef5;

  background: #FFFFFF;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  .card-top{
    display:flex;
    flex-direction:row;
    justify-content: space-between;
    .card-top-left{
      display:flex;
      flex-direction:row;
      width: 100%;
      .top-left{
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-image: linear-gradient(122deg, #5486FF 0%, #4F71FF 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        span{
          font-size: 17.5px;
          color: #FFFFFF;
        }
      }
      .top-right{
        width: 100%;
        padding-left:10px;
        .top-right-line1{
          display: flex;
          align-items: center;
          .top-right-name{
            font-size: 20px;
            color: #070F29;
            letter-spacing: 0;
            line-height: 20px;
          }
          .top-right-phone{
            margin-left:10px;
            font-size: 16px;
            color: #070F29;
            letter-spacing: 0;
            line-height: 14px;
          }
          .top-right-tag{
            margin-left:10px;
            padding:6px 12px;
            background: #E2E7FF;
            border-radius: 14px;

            font-size: 14px;
            color: #4F71FF;
            letter-spacing: 0;
            line-height: 14px;
          }
        }
        .top-right-line2{
          margin-top:15px;
          line-height: 32px;
          .line2-title{
            font-size: 16px;
            color: #888888;
            letter-spacing: 0;
            line-height: 32px;
          }
          .line2-text{
            font-size: 16px;
            color: #555555;
            letter-spacing: 0;
          }
        }
        .top-right-line3{
          .line3-title{
            font-size: 16px;
            color: #888888;
            letter-spacing: 0;
            line-height: 32px;
          }
          .line3-text{
            font-size: 16px;
            color: #555555;
            letter-spacing: 0;
          }
        }
        .top-right-line4{
          .line4-title{
            font-size: 16px;
            color: #888888;
            letter-spacing: 0;
            line-height: 32px;
          }
          .line4-text{
            font-size: 16px;
            color: #555555;
            letter-spacing: 0;
          }
        }
      }
    }
    .card-top-right{
      width: 30%;
      display:flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      .top-right-left{
        margin-right:32px;
        display:flex;
        flex-direction:column;
        justify-content: center;
        align-items: center;
        .left-score{
          font-size: 24px;
          color: #070F29;
          letter-spacing: 0;
          line-height: 24px;
        }
        .left-text{
          margin-top:14px;
          font-size: 16px;
          color: #BBBBBB;
          letter-spacing: 0;
          line-height: 16px;
        }
      }
      .top-right-right{
        display:flex;
        flex-direction: row;
        align-items: center;
        flex-direction:column;
        justify-content: center;
        .right-grade{
          font-size: 24px;
          color: #FF9500;
          letter-spacing: 0;
          text-align: center;
          line-height: 24px;
        }
        .right-text{
          margin-top:14px;
          font-size: 16px;
          color: #BBBBBB;
          letter-spacing: 0;
          line-height: 16px;
        }
      }
    }
  }
  .card-bottom{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }
}
</style>