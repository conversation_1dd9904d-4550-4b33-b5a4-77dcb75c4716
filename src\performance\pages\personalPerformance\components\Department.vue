<template>
  <div class="comp-department">
    <el-popover
      placement="bottom-start"
      width="500"
      trigger="click"
      v-model="popoveShow"
      :disabled="readonly"
      ref="treePopper"
      >
      <!-- <el-input :readonly="false" v-model="valueName" :placeholder="placeholder" slot="reference" ref="input" :disabled="readonly" clearable @clear="handleClear"></el-input>
      <el-input :readonly="false" v-model="value" placeholder="请输入" ref="input" :disabled="readonly" clearable></el-input> -->
      <el-input :readonly="false" v-model="value" :placeholder="placeholder" slot="reference" ref="input" :disabled="readonly" clearable @focus="handleFocus" @clear="handleClear"
        suffix-icon="el-icon-plus"
      ></el-input>
      <!-- <el-input :readonly="false" v-model="value" placeholder="请输入" ref="input" :disabled="readonly" clearable></el-input> -->
      <el-tree
        ref="departTree"
        :data="departmentList"
        node-key="id"
        :props="defaultProps"
        :default-expanded-keys="defaultExpandedKey"
        @node-click="handleNodeClick"
        :filter-node-method="handleFilterNode"
      ></el-tree>
    </el-popover>
  </div>
</template>

<script>
import { getDepartmentTreeConfirm } from 'performance/store/api.js'
export default {
  name: 'comp-department',
  components: {},
  props:{
    placeholder:{
      type:String,
      default:"请输入"
    }
  },
  data() {
    return {
      popoveShow:false,
      readonly:false,
      value:'',
      valueName:"",
      departmentList:[],
      defaultProps: {
        // children: 'children',
        label: 'name'
      },
      defaultExpandedKey:[]
    };
  },
  mounted() {
    this.handleGetSubsidiaryList()
  },
  methods: {
    handleNodeClick(data) {
      // console.log(data)
      const { id,name } = data
      // this.valueName = name
      this.value = name
      this.$emit("input", id);
      this.$refs.treePopper.doClose();
    },
    handleClear(){
      // console.log(123)
      this.$emit("input", null);
      this.$refs.treePopper.doClose();
      this.handleGetSubsidiaryList()
    },
    handleFocus(){
      this.departmentList =[]
      this.handleGetSubsidiaryList()
    },
    //筛选部门
    handleFilterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    async handleGetSubsidiaryList(){
      const { data } = await getDepartmentTreeConfirm()
      // console.log(data)
      this.departmentList = data;
      this.departmentList.forEach((item) => {
        this.defaultExpandedKey.push(item.id);
      });
    },
  },
  watch:{
    value(val) {
      this.$refs.departTree.filter(val);
    },
  }
}
</script>
<style lang='scss' scoped>

</style>