<template>
  <div class="temp-table">
    <el-table
      class="def-scrollbar"
      ref="Etable"
      :data="tableData"
      border
      stripe
      :height="def_height"
      :header-cell-style="{ background: '#f1f1f1' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="isShowSelection"
        type="selection"
        width="55"
        align="center"
      >
      </el-table-column>
      <!-- <el-table-column v-if="isShowIndex" label="序号" type="index" width="50" :fixed="true" align="center">
      </el-table-column> -->
      <template v-for="(item, index) in tableHeader">
        <template v-if="!item.btn">
          <el-table-column
            min-width="200"
            :prop="item.prop"
            :label="item.label"
            :formatter="handleFormatter"
            :key="item.prop"
            :fixed="index == 0 || item.isFixed"
            :align="item.align || 'left'"
            :width="item.width"
          >
            <template slot-scope="scope">
              <section>
                <template v-if="item.type == 'addRow'">
                  <template
                    v-for="(itemRow, indexRow) in handleFormatter(scope.row, {
                      property: item.prop,
                    })"
                  >
                    <section
                      class="def_addRow"
                      :key="indexRow"
                      :class="
                        handleDefAddrow({
                          index: indexRow,
                          length: handleFormatter(scope.row, {
                            property: item.prop,
                          }).length,
                        })
                      "
                    >
                      <!-- <span v-html="itemRow"></span> -->
                      <div
                        v-if="!item.isEdit"
                        :style="{
                          justifyContent:
                            item.align == 'right' ? 'flex-end' : 'flex-start',
                        }"
                      >
                        <template v-if="!item.isTip">
                          <span v-html="itemRow" class="def_auto1"></span>
                        </template>
                        <template v-else>
                          <el-tooltip
                            popper-class="def_tips"
                            class="item"
                            effect="dark"
                            placement="top-start"
                          >
                            <span slot="content">
                              <span v-html="itemRow" class="def_auto2"></span>
                            </span>
                            <span v-html="itemRow" class="def_auto2"></span>
                          </el-tooltip>
                          <!-- <span v-html="itemRow"></span> -->
                        </template>
                      </div>
                      <el-input
                        v-else
                        v-model="scope.row[item.prop]"
                        placeholder="请输入"
                      ></el-input>
                    </section>
                  </template>
                </template>
                <template v-else>
                  <div
                    v-if="!item.isEdit"
                    :style="{
                      justifyContent:
                        item.align == 'right' ? 'flex-end' : 'flex-start',
                    }"
                  >
                    <template v-if="!item.isTip">
                      <span
                        v-html="
                          handleFormatter(scope.row, { property: item.prop })
                        "
                        class="def_auto1"
                      ></span>
                    </template>
                    <template v-else>
                      <el-tooltip
                        popper-class="def_tips"
                        class="item"
                        effect="dark"
                        placement="top-start"
                      >
                        <span slot="content">
                          <span
                            v-html="
                              handleFormatter(scope.row, {
                                property: item.prop,
                              })
                            "
                            class="def_auto1"
                          ></span>
                        </span>
                        <span
                          v-html="
                            handleFormatter(scope.row, { property: item.prop })
                          "
                          class="def_auto1"
                        ></span>
                      </el-tooltip>
                      <!-- <span v-html="handleFormatter(scope.row, { property: item.prop })"></span> -->
                    </template>
                  </div>
                  <el-input
                    class="def_height"
                    v-else
                    v-model="scope.row[item.prop]"
                    placeholder="请输入"
                  ></el-input>
                </template>
              </section>
            </template>
            <!-- 多行表头 -->
            <template v-if="item.children">
              <template v-for="(itemChild, indexChild) in item.children">
                <el-table-column
                  min-width="200"
                  :prop="itemChild.prop"
                  :label="itemChild.label"
                  :formatter="handleFormatter"
                  :key="itemChild.prop"
                  :align="itemChild.align || 'left'"
                  :fixed="itemChild.isFixed"
                  :width="itemChild.width"
                >
                  <template slot-scope="scopeChild">
                    <section>
                      <template v-if="itemChild.type == 'addRow'">
                        <template
                          v-for="(itemRow, indexRow) in handleFormatter(
                            scopeChild.row,
                            { property: itemChild.prop }
                          )"
                        >
                          <section
                            class="def_addRow"
                            :key="indexRow"
                            :class="
                              handleDefAddrow({
                                index: indexRow,
                                length: handleFormatter(scopeChild.row, {
                                  property: itemChild.prop,
                                }).length,
                              })
                            "
                          >
                            <!--isEditRowKey：对某列可编辑字段  可二次控制其当前行状态状态 -->
                            <div
                              v-if="!itemChild.isEdit"
                              class="def_height"
                              :style="{
                                justifyContent:
                                  itemChild.align == 'right'
                                    ? 'flex-end'
                                    : 'flex-start',
                              }"
                            >
                              <!-- <span v-html="itemRow" class="def_auto3"></span> -->
                              <el-tooltip
                                popper-class="def_tips"
                                v-if="itemChild.isTip"
                                class="item"
                                effect="dark"
                                placement="top-start"
                              >
                                <span slot="content">
                                  <span
                                    v-html="itemRow"
                                    class="def_auto1"
                                  ></span>
                                </span>
                                <span v-html="itemRow" class="def_auto3"></span>
                              </el-tooltip>
                              <span
                                v-else
                                v-html="itemRow"
                                class="def_auto3"
                              ></span>
                            </div>
                            <div
                              v-else-if="
                                itemChild.isEdit &&
                                scopeChild.row[itemChild.fatherProp][indexRow][
                                  itemChild.isEditRowKey
                                ] !== true
                              "
                              class="def_height"
                              :style="{
                                justifyContent:
                                  itemChild.align == 'right'
                                    ? 'flex-end'
                                    : 'flex-start',
                              }"
                            >
                              <el-tooltip
                                popper-class="def_tips"
                                v-if="itemChild.isTip"
                                class="item"
                                effect="dark"
                                placement="top-start"
                              >
                                <span slot="content">
                                  <span
                                    v-html="itemRow"
                                    class="def_auto1"
                                  ></span>
                                </span>
                                <span v-html="itemRow" class="def_auto3"></span>
                              </el-tooltip>
                              <span
                                v-else
                                v-html="itemRow"
                                class="def_auto3"
                              ></span>
                            </div>
                            <div v-else class="def_height">
                              <section class="def_tag">
                                <!-- oninput="value=value.replace(/[^0-9]/g,'')" -->
                                <template v-if="itemChild.numberType == '2'">
                                  <el-input
                                    :type="itemChild.editType || 'text'"
                                    :maxlength="itemChild.maxlength"
                                    show-word-limit
                                    v-model="
                                      scopeChild.row[itemChild.fatherProp][
                                        indexRow
                                      ][itemChild.realProp]
                                    "
                                    placeholder="请输入"
                                    @mousewheel.native.prevent
                                    @DOMMouseScroll.native.prevent
                                    oninput="this.value = this.value.match(new RegExp('^\\d+(\\.\\d{0,2})?$','g'))||Number(this.value)==0 ? this.value : Number(this.value).toFixed(2);"
                                    @input="
                                      handleTagInput(
                                        $event,
                                        itemChild.isShowTag,
                                        scopeChild.row[itemChild.tagProp]
                                      )
                                    "
                                  ></el-input>
                                </template>
                                <template v-else>
                                  <el-input
                                    :type="itemChild.editType || 'text'"
                                    :maxlength="itemChild.maxlength"
                                    show-word-limit
                                    v-model="
                                      scopeChild.row[itemChild.fatherProp][
                                        indexRow
                                      ][itemChild.realProp]
                                    "
                                    placeholder="请输入"
                                    @mousewheel.native.prevent
                                    @DOMMouseScroll.native.prevent
                                    @input="
                                      handleTagInput(
                                        $event,
                                        itemChild.isShowTag,
                                        scopeChild.row[itemChild.tagProp]
                                      )
                                    "
                                  ></el-input>
                                </template>
                                <span
                                  class="def_input_tag"
                                  v-if="itemChild.isShowTag"
                                  >上限{{
                                    scopeChild.row[itemChild.tagProp]
                                  }}分</span
                                >
                              </section>
                            </div>
                          </section>
                        </template>
                      </template>
                      <template v-else>
                        <div
                          v-if="!itemChild.isEdit"
                          class=""
                          :style="{
                            justifyContent:
                              itemChild.align == 'right'
                                ? 'flex-end'
                                : 'flex-start',
                          }"
                        >
                          <span
                            v-html="
                              handleFormatter(scopeChild.row, {
                                property: itemChild.prop,
                              })
                            "
                          ></span>
                        </div>
                        <div
                          v-else-if="
                            itemChild.isEdit &&
                            scopeChild.row[itemChild.isEditRowKey] !== true
                          "
                          class="def_height"
                          :style="{
                            justifyContent:
                              itemChild.align == 'right'
                                ? 'flex-end'
                                : 'flex-start',
                          }"
                        >
                          <span
                            v-html="
                              handleFormatter(scopeChild.row, {
                                property: itemChild.prop,
                              })
                            "
                          ></span>
                        </div>
                        <div
                          v-else-if="
                            itemChild.isEdit &&
                            scopeChild.row[itemChild.isEditRowKey] == true &&
                            scopeChild.row[itemChild.actionIsEdit] !== true
                          "
                          class="def_height"
                          :style="{
                            justifyContent:
                              itemChild.align == 'right'
                                ? 'flex-end'
                                : 'flex-start',
                          }"
                        >
                          <span
                            v-html="
                              handleFormatter(scopeChild.row, {
                                property: itemChild.prop,
                              })
                            "
                          ></span>
                        </div>
                        <template v-else>
                          <!-- oninput="value=value.replace(/[^0-9]/g,'')" -->
                          <el-input
                            :type="itemChild.editType || 'text'"
                            class="def_height"
                            v-if="itemChild.numberType == '2'"
                            v-model="scopeChild.row[itemChild.realProp]"
                            placeholder="请输入"
                            @mousewheel.native.prevent
                            @DOMMouseScroll.native.prevent
                            oninput="this.value = this.value.match(new RegExp('^\\d+(\\.\\d{0,2})?$','g'))||Number(this.value)==0 ? this.value : Number(this.value).toFixed(2);"
                            @input="handleInput($event)"
                          ></el-input>
                          <el-input
                            :type="itemChild.editType || 'text'"
                            class="def_height"
                            v-else
                            v-model="scopeChild.row[itemChild.realProp]"
                            placeholder="请输入"
                            @mousewheel.native.prevent
                            @DOMMouseScroll.native.prevent
                            @input="handleInput($event)"
                          ></el-input>
                        </template>
                      </template>
                    </section>
                  </template>
                </el-table-column>
              </template>
            </template>
          </el-table-column>
        </template>
        <template v-else>
          <el-table-column
            min-width="100"
            :width="item.width"
            :prop="item.prop"
            :label="item.label"
            :formatter="handleFormatter"
            :key="item.prop"
            align="left"
            fixed="right"
            class="def_border"
          >
            <template slot-scope="scope">
              <template
                v-for="(btnItem, btnIndex) in filterData(
                  scope.row,
                  scope.column,
                  item.btn
                ).slice(0, 2)"
              >
                <!-- 自定义按钮 -->
                <template v-if="btnItem.type == 'def_btn'">
                  <el-link
                    v-show="
                      handleFormatter(scope.row, scope.column, btnItem.prop)
                    "
                    :key="btnItem.prop"
                    :type="btnItem.typeName || 'primary'"
                    :underline="false"
                    @click="handleBtnColumn(scope.row, btnItem.fun, scope)"
                  >
                    {{ btnItem.label }}
                  </el-link>
                </template>
                <template v-else>
                  <el-link
                    :key="btnItem.prop"
                    :type="btnItem.typeName || 'primary'"
                    :underline="false"
                    @click="handleBtnColumn(scope.row, btnItem.fun, scope)"
                  >
                    {{ handleFormatter(scope.row, scope.column) }}
                  </el-link>
                </template>
              </template>
              <!-- 三个按钮-->
              <template
                v-if="filterData(scope.row, scope.column, item.btn).length == 3"
              >
                <template
                  v-for="(btnItemT, btnIndexT) in filterData(
                    scope.row,
                    scope.column,
                    item.btn
                  ).slice(2)"
                >
                  <el-link
                    v-show="
                      handleFormatter(scope.row, scope.column, btnItemT.prop)
                    "
                    :key="btnItemT.prop"
                    :type="btnItemT.typeName || 'primary'"
                    :underline="false"
                    @click="handleBtnColumn(scope.row, btnItemT.fun, scope)"
                  >
                    {{ btnItemT.label }}
                  </el-link>
                </template>
              </template>
              <!-- 超出三个按钮以更多形式展示-->
              <template
                v-if="filterData(scope.row, scope.column, item.btn).length > 3"
              >
                <el-dropdown
                  trigger="click"
                  class="more-operation"
                  placement="bottom"
                  style="margin-left: 10px"
                >
                  <el-link type="primary" :underline="false">更多</el-link>
                  <el-dropdown-menu slot="dropdown">
                    <template
                      v-for="(btnItemGd, btnIndexGd) in filterData(
                        scope.row,
                        scope.column,
                        item.btn
                      ).slice(2)"
                    >
                      <el-dropdown-item
                        @click.native="
                          handleBtnColumn(scope.row, btnItemGd.fun, scope)
                        "
                        :key="btnItemGd.prop"
                      >
                        {{ btnItemGd.label }}
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </template>
          </el-table-column>
        </template>
      </template>
    </el-table>
    <section class="temp-table-pagination" v-if="!isHidePage">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 50]"
        :page-size="10"
        layout="total,sizes,prev,pager,next,jumper"
        :total="total"
        background
      >
      </el-pagination>
    </section>
  </div>
</template>

<script>
/**
 * search：分页查询
 * formatter：格式化
 * btnColumn：按钮操作
 */
export default {
  name: "temp-table",
  props: {
    tableHeader: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    total: {
      type: Number,
      default: null,
    },
    isShowIndex: {
      type: Boolean,
      default: false,
    },
    def_height: {
      type: String,
      default: "",
    },
    isHidePage: {
      type: Boolean,
      default: false,
    },
    isShowSelection: {
      // 是否展示复选框
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      page: 1,
      limit: 10,
      start: 0,
    };
  },
  methods: {
    //分页-条数
    handleSizeChange(val) {
      this.limit = val;
      this.start = this.limit * (this.page - 1);
      this.$emit("search", {
        limit: this.limit,
        start: this.start,
        page: this.page,
      });
    },
    //分页-开始位置
    handleCurrentChange(val) {
      this.page = val;
      this.start = this.limit * (val - 1);
      this.$emit("search", {
        limit: this.limit,
        start: this.start,
        page: this.page,
      });
    },
    //格式化数据
    handleFormatter(row, column, btnItem) {
      let value = null;
      this.$emit(
        "formatter",
        { prop: column["property"], data: row, btnItem: btnItem },
        (val) => {
          value = val;
        }
      );
      return value;
    },
    //操作按钮
    handleBtnColumn(val, type, scope) {
      this.$emit("btnColumn", val, type, scope);
    },
    handleSelectionChange(val) {
      this.$emit("selectionChange", val);
    },
    filterData(row, column, val) {
      return val.filter((item) => {
        return this.handleFormatter(row, column, item.prop);
      });
    },
    handleDefAddrow({ index, length }) {
      // console.log(index,length)
      let arr = [];
      if (length > 1) {
        index !== length - 1 &&
          arr.push("def_addRow-line", "def_addRow-paddingBottom");
        index !== 0 && arr.push("def_addRow-paddingTop");
      }
      return arr;
    },
    handleInput(val) {
      this.$emit("inputChange", val);
    },
    handleTagInput(val, isTag, tagVal) {
      this.$emit("inputCheck", { val, isTag, tagVal });
    },
  },
};
</script>

<style lang="scss" scoped>
.temp-table {
  // height: 100%;
  // display: flex;
  // flex-direction: column;
  // justify-content: space-between;
  .def_border {
    border: 1px solid #ebeef5 !important;
  }
  .el-link.el-link--primary {
    // margin: 0 10px;
    margin-right: 10px;
  }
  .el-link.el-link--danger {
    margin: 0 10px;
  }
  .temp-table-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding-bottom: 30px;
  }
  /deep/.el-table th > .cell {
    font-weight: normal;
    color: rgb(51, 51, 51);
  }
  /deep/.el-table td {
    border-color: #e8eaf3 !important;
    color: #333 !important;
  }
  /deep/.el-table th {
    border-color: #e8eaf3 !important;
    color: #333 !important;
  }

  .def_addRow {
    margin: 0 -10px;
    padding: 0 10px;
  }
  /deep/.el-table__header tr th:nth-last-child(1) {
    border-left: 1px solid #e8eaf3;
  }
  /deep/.el-table__header tr th:nth-last-child(2) {
    border-right: none;
  }
  /deep/.is-group tr th:nth-last-child(1) {
    border-left: 1px solid #e8eaf3;
  }
  /deep/.is-group tr th:nth-last-child(2) {
    border-right: none;
  }
  /deep/.el-table__row td:nth-last-child(1) {
    border-left: 1px solid #e8eaf3;
  }
  /deep/.el-table__row td:nth-last-child(2) {
    border-right: none;
  }
  .def_addRow-line {
    border-bottom: 1px solid #ebeef5;
    // padding:12px 0;
  }
  .def_addRow-paddingTop {
    padding-top: 12px;
  }
  .def_addRow-paddingBottom {
    padding-bottom: 12px;
  }

  .def_height {
    height: 62px;
    display: flex;
    align-items: center;
    // justify-content: center;
    // overflow: hidden;
  }
  .def_auto1 {
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;

    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    line-clamp: 5;
    -webkit-box-orient: vertical;
    // cursor: pointer;
  }
  .def_auto3 {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  .def_tag {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    height: 100%;
  }
  .def_input_tag {
    color: #bbbbbb;
    font-size: 12px;
  }
}
</style>
