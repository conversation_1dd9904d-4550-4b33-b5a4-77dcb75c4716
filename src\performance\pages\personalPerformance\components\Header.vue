<template>
  <div class="def_header">
    <header class="header">
      <template v-if="isBack">
        <el-row type="flex" style="justify-content: space-between">
          <el-col :span="12" style="flex: 1">
            <span @click="$router.go(-1)" class="back-style header-back">{{
              headerBackText
            }}</span>
            <span class="header-line">|</span>
            <!-- <span class="header-title"></span> -->
            <el-tooltip
              popper-class="def_tips"
              class="item"
              effect="dark"
              placement="top-start"
            >
              <span slot="content">
                <span v-html="headerText" class="def_auto2"></span>
              </span>
              <span class="header-title">{{
                headerText.length > 10
                  ? headerText.substr(0, 10) + "..."
                  : headerText
              }}</span>
            </el-tooltip>
            <span class="header-tag" v-if="isShowTag">{{ headerTag }}</span>
            <!-- <slot name="leftArea" /> -->
          </el-col>
          <div><slot name="btnArea" /></div>
        </el-row>
      </template>
      <template v-else>
        <el-row type="flex" style="justify-content: space-between">
          <span>{{ headerText }}</span>
          <div><slot name="rightArea" /></div>
        </el-row>
      </template>
    </header>
  </div>
</template>

<script>
/**
 * headerText：标题文案
 * headerBackText：返回文案 默认"返回"
 * isBack:是否开启返回功能
 * headerTag：tag状态
 */
export default {
  name: "def_header",
  props: {
    headerText: {
      type: String,
      default: "",
    },
    headerBackText: {
      type: String,
      default: "返回",
    },
    isShowTag: {
      type: Boolean,
      default: false,
    },
    headerTag: {
      type: String,
      default: "",
    },
    isBack: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
.def_header {
  padding: 0 20px;
  // position: sticky;
  // top: 0;
  z-index: 9;
  background: #fff;
  .header {
    font-size: 17px;
    height: 61px;
    border-bottom: 1px solid #ededed;
    line-height: 61px;
    overflow: hidden;
    .header-back {
      font-size: 16px;
      color: var(--color-primary);
      letter-spacing: 0;
      line-height: 16px;
    }
    .header-title {
      font-size: 16px;
      color: #070f29;
      letter-spacing: 0;
      line-height: 16px;
    }
    .header-line {
      font-size: 16px;
      color: #bbbbbb;
    }
    .header-tag {
      margin-left: 10px;
      padding: 4px 12px;
      background: #fff2e5;
      border-radius: 14px;

      font-size: 14px;
      color: #ff9500;
      letter-spacing: 0;
      line-height: 12px;
    }
  }
}
</style>
