<template>
  <div class="def_card" id="def_myCard">
    <section class="card-transition def_per_alignItems" @click="handleChange">
      <section class="transition-left">
        最近一次考核
      </section>
      <section class="transition-right">
        <i class="icon-font iconfont-per"  
          :class="isShow?'icon-jixiao_shouqi':'icon-jixiao_zhankai'"
          style="color:#9EA5BD;font-size:15px;"
        >
        </i>
      </section>
    </section>
    
    <section>
      <el-collapse-transition>
        <div v-if="isShow">
          <el-divider></el-divider>
          <section class="card-top">
            <section class="card-top-left">
              <def-photo v-if="isShowPhoto" :name="cardPhoto" boxSize="60px" textSize="17.5px" />
              <section class="top-right">
                <section class="top-right-line1">
                  <span class="top-right-name" v-show="lineOne.name">{{lineOne.name}}</span>
                  <span class="top-right-phone" v-show="lineOne.phone">{{lineOne.phone}}</span>
                  <span class="top-right-tag" v-show="lineOne.tag">{{lineOne.tag}}</span>
                  <span class="top-right-tag-my" v-show="lineOne.tag">{{lineOne.myTag}}</span>
                </section>
                <section class="top-right-line2"><span class="line2-title" v-if="lineTwo.label">{{lineTwo.label}} </span><span class="line2-text" v-if="lineTwo.value">{{lineTwo.value}}</span></section>
                <section class="top-right-line3"><span class="line3-title" v-if="lineThree.label">{{lineThree.label}} </span><span class="line3-text" v-if="lineThree.value">{{lineThree.value}}</span></section>
              </section>
            </section>
            <section class="card-top-right" v-if="isShowRate">
              <section class="top-right-left"><span class="left-score">{{rate.score}}分</span><span class="left-text">总评分</span></section>
              <section class="top-right-right"><span class="right-grade">{{rate.grade}}</span><span class="right-text">绩效等级</span></section>
            </section>
          </section>
          <section class="card-bottom" v-if="isShowStep">
            <template v-for="(item,index) in steps">
              <def-my-steps 
                :key="item.id"
                :showLine="index==steps.length-1 ? false : true"
                :stepText="item.text"
                :stepState="item.state"
                ></def-my-steps>
            </template>
          </section>
        </div>
      </el-collapse-transition>
    </section>
  </div>
</template>

<script>
import defMySteps from './MySteps'
import defPhoto from './Photo'
export default {
  name:"def_card",
  components:{
    defMySteps,
    defPhoto
  },
  props:{
    isShowPhoto: {
      type: Boolean,
      default: true
    },
    cardPhoto: {
      type: String,
      default: '张三'
    },
    lineOne:{
      type: Object,
      default: ()=>{
        return {
          name:"",
          phone:"",
          tag:"",
        }
      }
    },
    lineTwo:{
      type: Object,
      default: ()=>{
        return {
          label:"",
          value:""
        }
      }
    },
    lineThree:{
      type: Object,
      default: ()=>{
        return {
          label:"",
          value:""
        }
      }
    },
    rate:{
      type: Object,
      default: ()=>{
        return {
          score:98,
          grade:"A"
        }
      }
    },
    isShowRate:{
      type:Boolean,
      default:false
    },
    isShowStep:{
      type:Boolean,
      default:false
    },
    steps:{
      type:Array,
      default:()=>[]
    }
  },
  data(){
    return {
      isShow:false
    }
  },
  mounted(){
    this.isShow = true
  },
  methods:{
    handleChange(){
      this.isShow = !this.isShow
    }
  },
  watch:{
    isShow: {
      handler (val) {
        this.$nextTick(()=>{
          // let heightdocument.getElementById('def_myCard') ? document.getElementById('def_myCard').clientHeight : 0
          // this.$emit('cardChange',val)
        })
      },
      deep: true
    },
  }
}
</script>

<style lang="scss" scoped>
.def_card{
  display: flex;
  flex-direction: column;
  padding:0px 20px 5px 30px;
  min-width:500px;

  background: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #EAEAEA;
  /deep/.el-divider--horizontal{
    margin: 0 0 20px 0;
  }
  .card-transition{
    // height: 34px;
    padding:16px 0px 16px 0px;
    justify-content: space-between;
    cursor:pointer;
    .transition-left{
      font-weight: Medium;
      font-size: 16px;
      color: #070F29;
      letter-spacing: 0;
      line-height: 16px;
    }
    .transition-right{
      // cursor:pointer;
    }
  }
  .card-top{
    display:flex;
    flex-direction:row;
    justify-content: space-between;
    .card-top-left{
      display:flex;
      flex-direction:row;
      .top-left{
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-image: linear-gradient(122deg, #5486FF 0%, #4F71FF 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        span{
          font-size: 17.5px;
          color: #FFFFFF;
        }
      }
      .top-right{
        padding-left:10px;
        .top-right-line1{
          display: flex;
          align-items: center;
          .top-right-name{
            font-size: 20px;
            color: #070F29;
            letter-spacing: 0;
            line-height: 20px;
          }
          .top-right-phone{
            margin-left:10px;
            font-size: 14px;
            color: #070F29;
            letter-spacing: 0;
            line-height: 14px;
          }
          .top-right-tag{
            margin-left:10px;
            padding:6px 12px;
            background: #E2E7FF;
            border-radius: 14px;

            font-size: 14px;
            color: #4F71FF;
            letter-spacing: 0;
            line-height: 14px;
          }
          .top-right-tag-my{
            margin-left:10px;
            padding:6px 12px;
            background: #FFF2E5;
            border-radius: 14px;

            font-size: 14px;
            color: #FF9500;
            letter-spacing: 0;
            line-height: 14px;
          }
        }
        .top-right-line2{
          margin-top:15px;
          .line2-title{
            font-size: 16px;
            color: #888888;
            letter-spacing: 0;
          }
          .line2-text{
            font-size: 16px;
            color: #555555;
            letter-spacing: 0;
          }
        }
        .top-right-line3{
          .line3-title{
            font-size: 16px;
            color: #888888;
            letter-spacing: 0;
            line-height: 32px;
          }
          .line3-text{
            font-size: 16px;
            color: #555555;
            letter-spacing: 0;
          }
        }
      }
    }
    .card-top-right{
      display:flex;
      flex-direction: row;
      align-items: center;
      .top-right-left{
        margin-right:32px;
        display:flex;
        flex-direction:column;
        justify-content: center;
        .left-score{
          font-size: 24px;
          color: #070F29;
          letter-spacing: 0;
          line-height: 24px;
        }
        .left-text{
          margin-top:14px;
          font-size: 16px;
          color: #BBBBBB;
          letter-spacing: 0;
          line-height: 16px;
        }
      }
      .top-right-right{
        display:flex;
        flex-direction: row;
        align-items: center;
        flex-direction:column;
        justify-content: center;
        .right-grade{
          font-size: 24px;
          color: #FF9500;
          letter-spacing: 0;
          text-align: center;
          line-height: 24px;
        }
        .right-text{
          margin-top:14px;
          font-size: 16px;
          color: #BBBBBB;
          letter-spacing: 0;
          line-height: 16px;
        }
      }
    }
  }
  .card-bottom{
    margin-top:20px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }
}
</style>