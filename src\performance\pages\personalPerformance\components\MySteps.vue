<template>
  <div class="def_steps">
    <section class="steps-icon">
      <template>
      </template>
    </section>
    <section class="steps-text" :style="{backgroundColor:stepState==1?'#E2E7FF':'#F0F3F8'}">
      <span v-html="stepText" :style="{color:stepState==1?'#4F71FF':'#555555'}"></span>
    </section>
    <div class="steps-line">
      <div v-if="showLine" class="def_icon iconfont-per icon-jixiao-jiantou"></div>
    </div>
  </div>
</template>

<script>

export default {
  name:"def_mySteps",
  props:{
    stepText:{
      type:String,
      default:""
    },
    stepState:{
      type:Number,
      default:null
    },
    showLine:{
      type: Boolean,
      default: false
    }
  },
  data(){
    return {
      
    }
  }
}
</script>

<style lang="scss" scoped>
.def_steps{
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom:10px;
  .steps-icon{
    .icon-font{
      font-size: 20px;
      // color: #4F71FF;
    }
    .stept-state{
      width:17px;
      height:17px;
      border:1px solid #4F71FF;
      border-radius:50%;
    }
  }
  .steps-text{
    padding:10px 20px;
    background: #F0F3F8;
    // margin-left:10px;
    border-radius: 20px;
    span{
      font-size: 16px;
      color: #4F71FF;
      letter-spacing: 0;
      line-height: 16px;
    }
  }
  .steps-line{
    margin: 0 30px;
    .line{
      width:50px;
      height: 2px;
      background-color: #4F71FF;
    }
    .def_icon{
      font-size:25px;
      color: #EAEAEA;
    }
  }
}
</style>