<template>
  <div class="def_node">

    <section class="node-header def_per_alignItems">
      <section v-if="showNode" style="margin-right:20px">
        <div><i class="iconfont-per" :class="[nodeIcon?'icon-kaoheyiwancheng':'icon-kaohejinhangzhong']" style="font-size:16px" :style="{color:nodeColor}"></i></div>
      </section>
      <section class="def_per_alignItems">
        <def-photo :name="name" boxSize="48px" :isRandomColor="true" textSize="14px" v-if="showDefPhoto">
          <svg class="def_icon icon" aria-hidden="true" v-if="showIcon">
            <use :xlink:href="icon"></use>
          </svg>
        </def-photo>
        <slot name="node-header-text" />
        <section>

        </section>
      </section>
    </section>
    <section class="node-content">
      <section class="node-line def_per_justifyContent" :style="{margin:margin}" v-if="showNode">
        <div class="def_line" v-if="showLine" :style="{backgroundColor:lineColor}"></div>
      </section>
      <section style="margin-left:20px;flex:1">
        <slot name="node-content" />
      </section>
    </section>
  </div>
</template>

<script>
import defPhoto from './Photo'
export default {
  name:"def_node",
  props:{
    name: {
      type: String,
      default: ''
    },
    showLine:{
      type: Boolean,
      default: true
    },
    showIcon:{
      type: Boolean,
      default: false
    },
    showNode:{
      type: Boolean,
      default: false
    },
    showDefPhoto:{
      type: Boolean,
      default: true
    },
    icon:{
      type: String,
      default: ""
    },
    margin:{//流程线偏移量
      type: String,
      default: "0px 0px"
    },
    lineColor:{
      type: String,
      default: "#4F71FF",//#EAEAEA
    },
    nodeColor:{
      type: String,
      default: "#4F71FF",//#EAEAEA
    },
    nodeIcon:{
      type: Boolean,
      default: false,
    }
  },
  components:{
    defPhoto
  },
}
</script>

<style lang="scss" scoped>
.def_node{
  display: flex;
  flex-direction: column;
  .node-header{
    .def_icon{
      position:absolute;
      bottom: 0px;
      right: -10px;
      width:20px;
      height:20px;
      border-radius: 50%;
      border: 1px solid #fff;
      background: #fff;
    }
  }
  .node-content{
    display: flex;
    flex-direction: row;
    .node-line{
      width:16px;
      min-height: 40px;
      // margin: -12px 0;
      .def_line{
        width:1px;
        height: 100%;
        background-color: #4F71FF;
      }
    }
  }
}
</style>