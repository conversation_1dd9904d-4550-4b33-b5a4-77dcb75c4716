<template>
  <div class="def_photo" :style="{'min-width':boxSize,'height':boxSize,'backgroundImage':photoImg,'backgroundSize':boxSize}">
    <span :style="{'font-size':textSize}">
      {{name.substring(name.length-2)}}
    </span>
    <slot />
  </div>
</template>

<script>
/**
 * name:名称
 * boxSize：盒子大小
 * textSize：文字大小
*/
import defPhoto from 'performance/images/defaultAvatar.png'
import orderPhoto from 'performance/images/order-avatar.png'
export default {
  name:"def_photo",
  props:{
    name:{
      type:String,
      default:""
    },
    boxSize:{
      type:String,
      default:"60px"
    },
    textSize:{
      type:String,
      default:"17.5px"
    },
    isRandomColor:{
      type:Boolean,
      default:false
    }
  },
  data(){
    return {
      photoImg:"",
      baseImg:{
        1:"linear-gradient(135deg, #FFBC14 0%, #FF8300 100%)",
        2:"linear-gradient(122deg, #5486FF 0%, #4F71FF 100%)",
        3:"linear-gradient(134deg, #41DDB6 0%, #2BCDA4 100%)",
        4:"linear-gradient(-44deg, #8B5FEB 0%, #B095FE 100%)"
      },
      img:window.env.server_env === "boc" || window.env.server_env === "cgb"  ? orderPhoto : defPhoto
    }
  },
  mounted(){
    this.handleRandom(1,4)
  },
  methods:{
    handleRandom(min,max){
      if(this.isRandomColor){
        let val = Math.floor(Math.random()*(max-min+1)+min);
        this.photoImg = this.baseImg[val]||"linear-gradient(122deg, #5486FF 0%, #4F71FF 100%)"
        if(!this.name){
          this.photoImg = `url(${this.img})`
        }
        // console.log(this.photoImg)
      }else{
        this.photoImg = "linear-gradient(122deg, #5486FF 0%, #4F71FF 100%)"
        if(!this.name){
          this.photoImg = `url(${this.img})`
        }
      }
      // return this.photoImg
    }
  }
}
</script>

<style lang="scss" scoped>
.def_photo{
  // width: 60px;
  // height: 60px;
  border-radius: 50%;
  // background-image: ;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  span{
    // font-size: 17.5px;
    color: #FFFFFF;
  }
}
</style>