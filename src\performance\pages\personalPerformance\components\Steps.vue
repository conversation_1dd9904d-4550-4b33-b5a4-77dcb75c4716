<template>
  <div class="def_steps">
    <section class="steps-icon">
      <template>
        <i class="icon-font iconfont-per"  
        :class="stepState==3?'icon-kaoheyiwancheng':'icon-kaohejinhangzhong'"
        :style="{color:stepState==2?'#CCCCCC':'#4F71FF'}">
        </i>
      </template>
      <!-- <template v-else>
        <p class="stept-state"></p>
      </template> --> 
    </section>
    <section class="steps-text" :style="{color:handleColor(stepState)}">
      <span v-html="stepText" :title="stepTip"></span>
    </section>
    <div class="steps-line">
      <div class="line" v-if="showLine" :style="{backgroundColor:stepState==3?'#4F71FF':'#EAEAEA'}"></div>
    </div>
  </div>
</template>

<script>
/**
 * stepState:
 * 1：处理中#4F71FF
 * 2：等待处理#BBBBBB
 * 3：已处理#4F71FF
*/
export default {
  name:"def_steps",
  props:{
    stepText:{
      type:String,
      default:""
    },
    stepTip:{
      type:String,
      default:""
    },
    stepState:{
      type:Number,
      default:null
    },
    showLine:{
      type: Boolean,
      default: false
    }
  },
  data(){
    return {
      
    }
  },
  methods:{
    handleColor(type){
      console.log(type)
      if(type=="3"){//已完成
        return '#555555'
      }else if(type == "2"){//未开始
        return '#BBBBBB'
      }else{//正在处理
        return '#4F71FF'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.def_steps{
  display: flex;
  flex-direction: row;
  align-items: center;
  .steps-icon{
    .icon-font{
      font-size: 20px;
      // color: #4F71FF;
    }
    .stept-state{
      width:17px;
      height:17px;
      border:1px solid #4F71FF;
      border-radius:50%;
    }
  }
  .steps-text{
    margin-left:10px;
    font-size: 16px;
    color: #555555;
    letter-spacing: 0;
    line-height: 16px;

    max-width: 400px;    /*根据自己项目进行定义宽度*/
    overflow: hidden;     /*设置超出的部分进行影藏*/
    text-overflow: ellipsis;     /*设置超出部分使用省略号*/
    white-space:nowrap ;    /*设置为单行*/
  }
  .steps-line{
    margin: 0 10px;
    .line{
      width:50px;
      height: 2px;
      // background-color: #4F71FF;
    }
  }
}
</style>