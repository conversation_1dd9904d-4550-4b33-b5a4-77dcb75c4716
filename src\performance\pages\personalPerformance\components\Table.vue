<template>
<!-- :height="tableHeight" -->
  <div class="def_table">
    <old-table
      :height="tableHeight"
      :data="tableData" 
      :headerData="headerData" 
      :isShowOperation="isShowOperation"
      :isShowPagination="isShowPagination"
      :operaOptions="operaOptions"
      :pageOptions="pageOptions"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
      @operaClick="handleOperaClick"
      :arraySpanMethod="handleMergeRow"
    >
      <template v-for="(item,index) in tableSlots" slot-scope="row" :slot="item.slot">
        <section :key="item.slot" v-if="item.type=='addRow'">
          <template v-for="(itemRow,indexRow) in row.msg[item.slot]">
            <section class="def_addRow" :key="itemRow"
              :class="handleDefAddrow({index:indexRow,length:row.msg[item.slot].length})"
            >{{itemRow}}
            </section>
          </template>
        </section>
        <section :key="item.slot" v-else>{{row.msg[item.slot]}}</section>
      </template>
    </old-table>
  </div>
  
</template>

<script>
export default {
  props:{
    headerData: {
      type: Array,
      default: ()=>[]
    },
    operaOptions: {
      type: Object,
      default:()=>{}
    },
    tableData: {
      type: Array,
      default: ()=>[]
    },
    tableSlots: {
      type: Array,
      default: ()=>[]
    },
    isShowOperation: {
      type: Boolean,
      default: false
    },
    isShowPagination: {
      type: Boolean,
      default: false
    },
    pageOptions: {
      type: Object,
      default:()=>{}
    },
    tableHeight: {
      type: String,
      default:"null"
    },
  },
  data() {
    return {
    };
  },
  /**
   * output:
   * def_search
   * def_operate
  */
  methods:{
    //分页size切换
    handleSizeChange(val) {
      console.log('当前size', val)
      this.$emit("def_search", {
        pageSize:val
      });
    },
    //页码切换
    handleCurrentChange(val) {
      console.log('当前页码', val)
      this.$emit("def_search", {
        currPage:val
      });
    },
    handleOperaClick(btn,row){
      this.$emit("def_operate", {btn,row});
    },
    handleMergeRow({ row, column, rowIndex, columnIndex }){
      let value = null;
      this.$emit("def_addSpan", { row, column, rowIndex, columnIndex },val => {
        value = val;
      });
      if(value) return value
    },
    handleDefAddrow({index,length}){
      console.log(index,length)
      let arr = []
      if(length>1){
        index !== length-1 && arr.push('def_addRow-line','def_addRow-paddingBottom');
        index !== 0 && arr.push('def_addRow-paddingTop');
      }
      return arr
    }
  }
};
</script>

<style lang="scss" scoped>
.def_table{
  .def_addRow{
    margin: 0 -10px;
  }
  .def_addRow-line{
    border-bottom: 1px solid #EBEEF5;
    // padding:12px 0;
  }
  .def_addRow-paddingTop{
    padding-top:12px;
  }
  .def_addRow-paddingBottom{
    padding-bottom:12px;
  }
}

</style>