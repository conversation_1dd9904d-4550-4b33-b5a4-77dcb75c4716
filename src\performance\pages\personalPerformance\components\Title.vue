<template>
  <div class="def_title">
    <section class="title-content">
      <span class="title-line"></span>
      <span class="title-text">{{text}}</span>
    </section>
    <section>
      <slot />
    </section>
  </div>
</template>

<script>
export default {
  name:"def_title",
  props:{
    text:{
      type:String,
      default:""
    }
  }
}
</script>

<style lang="scss" scoped>
.def_title{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title-content{
    display: flex;
    align-items: center;
    .title-line{
      z-index:99;
      width: 3px;
      height: 14px;
      background: var(--color-primary);
      border-radius: 1px;
    }
    .title-text{
      z-index:99;
      margin-left:10px;
      font-size: 16px;
      color: #070F29;
      letter-spacing: 0;
      line-height: 16px;
    }
  }
}
</style>