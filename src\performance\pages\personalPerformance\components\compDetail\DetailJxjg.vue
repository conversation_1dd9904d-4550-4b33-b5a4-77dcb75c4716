<template>
  <div class="detail-jxjg">
    <section class="jxjg def_per_TopBottom">
      <section>
        <span class="jxjg-title">总分</span>
        <span class="jxjg-score">{{ score }} 分</span>
      </section>
      <section style="margin-top:10px;">
        <span class="jxjg-title">评级</span>
        <span class="jxjg-grade">{{ grade }}</span>
      </section>
    </section>
  </div>
</template>

<script>
/**
 * 绩效结果
*/
export default {
  name: '',
  components: {},
  props:{
    score: {
      type: Number,
      default: 0
    },
    grade: {
      type: String,
      default: ''
    },
  },
  data() {
    return {

    };
  },
  mounted() {},
  methods: {},
}
</script>
<style lang='scss' scoped>
.detail-jxjg{
  .jxjg {
    margin-top: 10px;
    margin-left: 13px;
    .jxjg-title {
      font-size: 14px;
      color: #888888;
      letter-spacing: 0;
      text-align: right;
      line-height: 14px;
    }
    .jxjg-score {
      margin-left: 10px;
      font-size: 16px;
      color: #070f29;
      letter-spacing: 0;
      line-height: 14px;
    }
    .jxjg-grade {
      margin-left: 10px;
      font-size: 16px;
      color: #ff9500;
      letter-spacing: 0;
      line-height: 14px;
    }
  }
}
</style>