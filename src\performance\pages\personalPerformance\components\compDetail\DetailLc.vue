<template>
  <div class="detail-lc">
    <section>
      <template v-for="(item, index) in list">
        <def-new-node
          :key="item.nodeSort"
          style="width:100%"
          :showLine="index == list.length - 1 ? false : true"
          :showIcon="true"
          :showNode="true"
          margin="-12px 0"
          nodeMargin="16px 0"
          :nodeIcon="item.nodeType === 3"
          :lineColor="item.nodeType === 3 ? '#4F71FF' : '#EAEAEA'"
          :nodeColor="item.nodeType === 3 || item.nodeType === 1 ? '#4F71FF' : '#CCCCCC'"
        >
          <section class="node-right def_per_TopBottom" slot="node-content">
            <template v-for="(nodeItem, nodeIndex) in item.nodeProcessors">
              <section class="def_per_leftRight" style="margin-bottom:20px">
                <def-photo
                  class=""
                  :name="nodeItem.processorName"
                  boxSize="48px"
                  textSize="14px"
                  :isRandomColor="true"
                >
                  <!-- <svg class="def_icon icon" aria-hidden="true" v-if="true">
                    <use :xlink:href="nodeItem.status !== 3 ? '#icon-shenpizhong' : '#icon-yiwancheng'"></use>
                  </svg> -->
                  <i class="def_icon iconfont-per"  
                    :class="nodeItem.status !== 3 ? 'icon-dengdaishenhe' : 'icon-duigoutianchong-'"
                    :style="{color:nodeItem.status !== 3 ?'#FF9B0E':'#41BD5A'}"
                  >
                  </i>
                  
                </def-photo>
                <section class="slot-header">
                  <span class="slot-header-name">{{nodeItem.processorName || nodeItem.processorTitle}}</span>
                  <span class="slot-header-text">{{item.det_nodeProcessors ? nodeItem.processorText : otherStatus[nodeItem["status"]]}}</span>
                  <span class="slot-header-text" style="margin-right:20px;">{{item.det_nodeProcessors ? `${nodeItem.time}`&&`(${nodeItem.time})`: nodeItem["operateTime"]&&`(${$dayjs(nodeItem["operateTime"]).format('YYYY-MM-DD HH:mm')})`}}
                  </span>
                </section>
              </section>
            </template>
          </section>
        </def-new-node>
      </template>
    </section>
  </div>
</template>

<script>
/**
 * 流程
*/
import { otherStatus } from "performance/utils/enum.js";
import { defPhoto,defNewNode } from "performance/pages/personalPerformance/components";

export default {
  name: 'detail-lc',
  components: {
    defPhoto,
    defNewNode
  },
  props:{
    list: {
      type: Array,
      default: ()=>[]
    },
  },
  data() {
    return {
      otherStatus: otherStatus, 
    };
  },
  mounted() {},
  methods: {},
}
</script>
<style lang='scss' scoped>
.detail-lc{
  margin-top: 20px;
  .node-right {
    .node-right-photo {
      margin: 10px 0;
    }
    .def_icon {
      position: absolute;
      bottom: 0px;
      right: -5px;
      // width: 20px;
      // height: 20px;
      border-radius: 50%;
      border: 2px solid #fff;
      background: #fff;
    }
    .right-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 48px;
      .right-header-name {
        margin-left: 16px;
        font-size: 16px;
        color: #070f29;
        letter-spacing: 0;
        line-height: 14px;
      }
      .right-header-text {
        margin-left: 20px;
        font-size: 12px;
        color: #6a6f7f;
        letter-spacing: 0;
        line-height: 14px;
      }
    }
  }
  .slot-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 48px;
    .slot-header-name {
      margin-left: 16px;
      font-size: 16px;
      color: #070f29;
      letter-spacing: 0;
      line-height: 16px;
    }
    .slot-header-tag {
      margin-left: 10px;
      border-radius: 14px;
      padding: 6px 12px;
      background: #f1f1f1;

      font-size: 14px;
      color: #6a6f7f;
      letter-spacing: 0;
      line-height: 14px;
    }
    .slot-header-text {
      margin-left: 20px;
      font-size: 12px;
      color: #6a6f7f;
      letter-spacing: 0;
      line-height: 14px;
    }
  }
}
</style>