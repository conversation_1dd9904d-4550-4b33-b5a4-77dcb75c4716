<template>
  <div class="detail-xglj">
    <section class="khzbxzjl-node">
      <template v-for="(item,index) in dataList">
        <def-new-node :key="index" style="width:100%" 
          :showLine="index==dataList.length-1 ? false : true" 
          :showIcon="false"
          :showNode="true"
          margin="0"
          nodeMargin="5px 0"
          :nodeIcon="false"
        >
          <section class="node-right def_per_TopBottom" slot="node-content">
            <section class="node-header">{{handleXzjlTime(item.operateTime)}}</section>
            <section class="def_per_TopBottom" style="margin:10px 0">
              <section class="def_per_leftRight">
                <def-photo class="" :name="item.operatorName" boxSize="48px" textSize="14px" :isRandomColor="true" />
                <section class="slot-header">
                  <span class="slot-header-name">{{item.operatorName}}</span>
                  <span class="slot-header-title">{{item.title}}</span>
                  <el-link style="margin-left:5px" type="primary" :underline="false" @click="handleIsShowHide(item,index)">{{item.def_isshow ? '收起':'展开'}}</el-link>
                </section>
              </section>
              <section>
                <el-collapse-transition>
                  <div v-show="item.def_isshow" v-html="item.content" style="margin-left: 120px;"></div>
                </el-collapse-transition>
              </section>
            </section>
          </section>
        </def-new-node>
      </template>
    </section>
  </div>
</template>

<script>
import { defPhoto,defNewNode } from "performance/pages/personalPerformance/components";

export default {
  name: 'detail-xglj',
  components: {
    defPhoto,
    defNewNode
  },
  props:{
    list: {
      type: Array,
      default: ()=>[]
    },
  },
  data() {
    return {
      dataList:this.list
    };
  },
  mounted() {
    
  },
  methods: {
    handleIsShowHide(item,index){
      this.$set(this.dataList,index,{
        ...this.dataList[index],
        def_isshow:!item.def_isshow
      })
    },
    handleXzjlTime(time){
      return this.$dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
  },
}
</script>
<style lang='scss' scoped>
.detail-xglj{
  .khzbxzjl-node{
    margin-top: 20px;
    .node-header{
      font-size: 14px;
      color: #888888;
      letter-spacing: 0;
      line-height: 14px;
    }
    .slot-header{
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 48px;
      .slot-header-name{
        margin-left:16px;
        font-size: 16px;
        color: #070F29;
        letter-spacing: 0;
        line-height: 14px;
      }
      .slot-header-title{
        margin-left:10px;
        font-size: 16px;
        color: #070F29;
        letter-spacing: 0;
        line-height: 14px;
      }
    }
  }
}
</style>