<template>
  <div class="detail-zp">
    <template v-for="(item, index) in list">
      <def-new-node
        :key="item.nodeSort"
        style="width:100%;margin-bottom:14px;"
        :showLine="false"
        :showIcon="false"
        :showNode="false"
        margin="-12px 0"
        nodeMargin="16px 0"
        :nodeIcon="index == list.length - 1 ? false : true"
      >
        <section class="node-right def_per_TopBottom" slot="node-content">
          <section class="def_per_leftRight" style="margin-bottom:5px">
            <def-photo
              class=""
              :name="item.employeeName"
              boxSize="48px"
              textSize="14px"
              :isRandomColor="true"
            >
            </def-photo>
            <section class="slot-header">
              <span class="slot-header-name">{{ item.employeeName }}</span>
              <span class="slot-header-tag">{{ pfType[item.type] }}</span>
              <span
                class="slot-header-text"
                v-if="item.weight !== null"
                >权重{{ item.weight }}%</span
              >
            </section>
          </section>
          <section style="margin-left:48px">
            <section class="evaluate-score" v-if="item.totalScore!==null">
              <span class="score-one">评分</span>
              <span class="score-two">{{ item.totalScore==null? '--': item.totalScore}}  分</span>
            </section>
            <section class="evaluate-comment">
              <section class="comment-one">
                <span
                  class="one-required"
                  style="width:2px;display:inline-block;"
                ></span>
                <span class="one-text">评语</span>
              </section>
              <section class="comment-two">
                <template v-if="item.isCanEdit == true">
                  <el-input
                    type="textarea"
                    :rows="4"
                    placeholder="请输入评语"
                    v-model="item.comment"
                    :disabled="item.isCanEdit !== true"
                    maxlength="600"
                    show-word-limit
                  >
                  </el-input>
                </template>
                <template v-else>
                  <p style="color:#555555" v-model="item.comment">{{item.comment||'--'}}</p>
                </template>
              </section>
            </section>
          </section>
        </section>
      </def-new-node>
    </template>
  </div>
</template>

<script>
/**
 * 总评
*/
import { pfType } from "performance/utils/enum.js";
import { defPhoto,defNewNode } from "performance/pages/personalPerformance/components";
export default {
  name: 'detail-zp',
  components: {
    defPhoto,
    defNewNode
  },
  props:{
    list: {
      type: Array,
      default: ()=>[]
    },
  },
  data() {
    return {
      pfType: pfType, //评分类型
    };
  },
  mounted() {},
  methods: {},
}
</script>
<style lang='scss' scoped>
.detail-zp {
  margin-top: 20px;
  .node-right {
    .node-right-photo {
      margin: 10px 0;
    }
    .def_icon {
      position: absolute;
      bottom: 0px;
      right: -10px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 1px solid #fff;
      background: #fff;
    }
    .right-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 48px;
      .right-header-name {
        margin-left: 16px;
        font-size: 16px;
        color: #070f29;
        letter-spacing: 0;
        line-height: 14px;
      }
      .right-header-text {
        margin-left: 20px;
        font-size: 12px;
        color: #6a6f7f;
        letter-spacing: 0;
        line-height: 14px;
      }
    }
  }
  .slot-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 48px;
    .slot-header-name {
      margin-left: 16px;
      font-size: 16px;
      color: #070f29;
      letter-spacing: 0;
      line-height: 14px;
    }
    .slot-header-tag {
      margin-left: 10px;
      border-radius: 14px;
      padding: 6px 12px;
      background: #f1f1f1;

      font-size: 14px;
      color: #6a6f7f;
      letter-spacing: 0;
      line-height: 14px;
    }
    .slot-header-text {
      margin-left: 20px;
      font-size: 12px;
      color: #6a6f7f;
      letter-spacing: 0;
      line-height: 14px;
    }
  }
  .evaluate-score {
    margin-left: 16px;
    margin-bottom: 10px;
    .score-one {
      font-size: 14px;
      color: #888888;
      letter-spacing: 0;
      text-align: right;
      line-height: 14px;
    }
    .score-two,
    .score-three {
      margin-left: 10px;
      font-size: 14px;
      color: #555555;
      font-weight: Medium;
      letter-spacing: 0;
      line-height: 14px;
    }
  }
  .evaluate-comment {
    display: flex;
    flex-direction: row;
    // align-items: baseline;
    .comment-one {
      min-width:42px;
      margin-left: 4px;
      .one-required {
        font-size: 14px;
        color: #ff6051;
        letter-spacing: 0;
        line-height: 14px;
      }
      .one-text {
        margin-left: 6px;
        font-size: 14px;
        color: #888888;
        letter-spacing: 0;
        text-align: right;
        line-height: 14px;
      }
    }
    .comment-two {
      margin-left: 10px;
      flex-grow: 1;
    }
  }
}
</style>