<template>
  <div class="finish-state def_per_height">
    <def-header 
      :headerText="def_HeaderData.headerText" 
      :isBack="true"
    />
    <section class="def_per_section def_per_section-top table-header">
      <section>
        
      </section>
      <section>
        <p v-html="totalTip"></p>
        <!-- <el-button type="primary" @click="handleBtnClick">导入实际完成值</el-button> -->
      </section>
    </section>
    <section class="def_per_section def_per_section-top">
      <def-etable 
        v-if="tableHeader.length!==0"
        :tableHeader="tableHeader" 
        :tableData="tableData" 
        @formatter="handleFormatter" 
        @btnColumn="handleBtnColumn"
        @search="handleSearch"
        :total="total"
        :isShowIndex="true"
        :def_height="tableHeight"
        :isHidePage="true"
        @inputChange="handleInputChange"
      />
      <!-- @inputChange="handleInputChange" -->
    </section>
    <!-- <section class="def_per_section table-btn">
      <el-button>取消</el-button>
      <el-button>暂存</el-button>
      <el-button type="primary">确认并提交</el-button>
    </section> -->
  </div>
</template>

<script>
import { defHeader,defCard,defNode,defTitle,defTable,defPhoto,defEtable } from '../index'
import { getMyPlanTodoCompleteValueList,getMyPlanTodoSetCompleteValue,getPlanBaseInfo } from 'performance/store/api.js'

export default {
  name: 'finish-state',
  components: {
    defHeader,
    defTable,
    defEtable
  },
  data() {
    return {
      tableHeight:document.body.clientHeight - 200 +'px',
      tableHeader:[],
      base_tableHeader:[
        { prop: "khdx", label: "考核对象"},
        { prop: "glry", label: "关联人员" },
      ],
      btn_tableHeader:[{ prop: "def_cz", label: "操作" ,width:"200px",btn:[
        {prop:"def_lr",label:"录入",type:"def_btn",fun:"handleInput"},
        {prop:"def_qx",label:"取消",type:"def_btn",fun:"handleCancel"},
        {prop:"def_tj",label:"提交",type:"def_btn",fun:"handleSubmit"},
      ]}],
      def_tableHeader:[],
      def_HeaderData:{
        headerText:""
      },
      tableData:[],
      baseTableData:[],
      total:0,
      limit:10,
      start:0,
      page:1,

      editRow:{},
      type:null,
      totalTip:""
    };
  },
  mounted() {
    this.handleInit()
    this.handleTableResize()
  },
  methods: {
    handleInit(){
      this.handleGetPlanBaseInfo()
      this.handleGetMyPlanTodoCompleteValueList()
    },
    handleTableResize(){
      window.onresize = () => {
        return (() => {
          this.tableHeight = document.body.clientHeight - 200+'px';
        })();
      };
    },
    async handleGetMyPlanTodoCompleteValueList(){
      const { planId,type } = this.$route.query
      this.type = type
      let obj = {
        // currentPage:this.page,
        // pageSize:this.limit,
        planId:planId,
        type:type,
      }
      const { data } = await getMyPlanTodoCompleteValueList(obj)
      const { records,total } = data
      this.total = total
      
      this.def_tableHeader = this.handleDefTableHeader(records)
      this.tableData = this.handleTableData(records)
      this.baseTableData = JSON.parse(JSON.stringify(this.tableData))
      this.tableHeader = this.handleBaseTableHeader()

      this.totalTip = this.handleTotalTip()
    },
    handleTotalTip(){
      if(this.type == 1){
        return `<p>待录入实际完成值<span style="color:#FF9500;">${this.total}</span>个</p>`
      }
      if(this.type==2){
        return `<p>已录入实际完成值<span style="color:#FF9500;">${this.total}</span>个</p>`
      }
    },
    //考核基本信息
    async handleGetPlanBaseInfo(){
      const { planId } = this.$route.query
      let obj = { planId }
      const { data } = await getPlanBaseInfo(obj)
      const { name,type,status } = data
      this.def_HeaderData = {
        headerText:`${name}实际完成数据`,
      }
    },
    //构建渲染数据
    handleTableData(data){
      if(Object.prototype.toString.call(data) == '[object Array]'){
        let arr = [];
        data.map(v=>{
          let zbObj = {}
          let def_submit = []
          if(Object.prototype.toString.call(v["indicatorCompletion"]) == '[object Array]'&&v["indicatorCompletion"].length!==0){
            v["indicatorCompletion"].map((zbV,zbI)=>{
              zbObj["examineePlanId"] = v["examineePlanId"];
              zbObj[`examineeIndicatorId_${zbI}`] = zbV["examineeIndicatorId"];
              // zbObj[`targetValue_${zbI}`] = zbV["targetValue"];
              zbObj[`targetValue_${zbI}`] = zbV["targetValue"] == null ? '--' : `${zbV["targetValue"]}${ zbV["dataUnit"]}`;//目标值
              zbObj[`completeValue_${zbI}`] = zbV["completeValue"] == null ? '--' : `${zbV["completeValue"]}${ zbV["dataUnit"]}`;//实际完成值
              zbObj[`score_${zbI}`] = zbV["score"] == null ? '--' : `${zbV["score"]}分`;//考核指标评分
              zbObj[`maxScore_${zbI}`] = zbV["maxScore"]
              zbObj[`dataRule_${zbI}`] = zbV["dataRule"]
              zbObj[`dataRuleType_${zbI}`] = zbV["dataRuleType"]
              
              zbObj["isEditRow"] = false

              zbObj[`actionIsEdit_${zbI}`] = zbV["status"] == 2 ? false : true,//根据当前行数据判断是否可编辑
              // zbObj["isEditRow"] = zbV["status"] == 2 ? false : true,
              def_submit.push(
                {
                  completeValue:`completeValue_${zbI}`,
                  examineeIndicatorId:`examineeIndicatorId_${zbI}`,
                  examineePlanId:"examineePlanId"
                }
              )
            })
            zbObj["def_submit"] = def_submit
          }
          let obj = {
            khdx:v.examineeName,
            glry:v.relationName,
            ...zbObj
          }
          arr.push(obj)
        })
        return arr
      }
    },
    // 设置 动态表头
    handleDefTableHeader(list){
      let _this = this
      let arr = [];
      if(Object.prototype.toString.call(list) == '[object Array]' && list.length!==0){
        if(Object.prototype.toString.call(list[0]["indicatorCompletion"]) == '[object Array]'){
          list[0]["indicatorCompletion"].map((v,i)=>{
            let obj = {
              prop:`${v.name}`,
              label:v.name,
              align:"center",
              children:[
                {
                  prop:`targetValue_${i}`,
                  label:`目标值`,
                  align:"right"
                },
                {
                  prop:`completeValue_${i}`,
                  label:`实际完成值`,
                  // isEdit:v.status == 2 ? false : true,
                  isEdit: true,
                  realProp:`completeValue_${i}`,
                  isEditRowKey:"isEditRow",//是否可编辑当前行该字段
                  editType:"number",
                  numberType:"2",
                  relaField:`score_${i}`,//关联字段
                  ruleData:`dataRule_${i}`,
                  maxScore:`maxScore_${i}`,//评分上限
                  dataRuleType:`dataRuleType_${i}`,//计算规则,可用值:1:按实际完成值计算,2:按目标达成率计算
                  targetValue:`targetValue_${i}`,//目标值
                  actionIsEdit:`actionIsEdit_${i}`,//目标值
                  align:"right"
                }
              ]
            };
            console.log(_this.type)
            if(Number(_this.type) !== 1){
              obj.children.push({
                prop:`score_${i}`,
                label:"考核指标评分",
                align:"right"
              })
            }
            arr.push(obj)
          })
        }
      }
      return arr
    },
    handleBtnShow(row,btn){
      return Reflect.ownKeys(this.tableBtnShow).includes(row.khdx)&&!!this.tableBtnShow[row.khdx]
      // return this.tableBtnShow.includes(row.khdx)
      // this.tableBtnShow.includes(row.khdx)
    },
    handleBtnClick(){

    },
    handleDefSearch(val){
      console.log(val)
    },
    //格式化table数据
    handleFormatter({prop,data,btnItem},callback){
      // console.log(data)
      if(prop=="def_cz"){
        let boo = false
        switch(btnItem){
          case "def_lr":
            boo = data["isEditRow"] == false
            callback(boo)
            break;
          case "def_qx":
            boo = data["isEditRow"] !== false
            callback(boo)
            break;
          case "def_tj":
            boo = data["isEditRow"] !== false
            callback(boo)
            break;
        }
      }else{
        switch(prop){
          case "khdx":
            callback(data["khdx"])
            break;
          case "glry":
            callback(this.handleGlryData(data["glry"]))
            break;
          default:
            callback(data[`${prop}`])
        }
      }
    },
    //关联人员
    handleGlryData(list){
      if(Object.prototype.toString.call(list) == '[object Array]'){
        if(list.length==0){
          return '--'
        }else{
          return list.join('，')
        }
      }else{
        return '--'
      }
    },
    handleBtnColumn(val,type,{ $index }){
      // console.log(val,type,scope)
      switch(type){
        case 'handleInput':
          this.handleInput(val,$index)
          break;
        case 'handleCancel':
          this.handleCancel(val,$index)
          break;
        case 'handleSubmit':
          this.handleSubmit(val)
          break;
      }
    },
    //录入
    handleInput(val,index){
      if(this.tableData.find(v=>{return v["isEditRow"] == true})){
        this.$message('不允许编辑多行数据');
        return
      }
      if(this.tableData[index]["examineePlanId"] == val["examineePlanId"]){
        this.$set(this.tableData,index,{
          ...this.tableData[index],
          isEditRow:true
        })
      }else{
        let findIndex = this.tableData.findIndex(v=>{return v["examineePlanId"] == val["examineePlanId"]})
        this.$set(this.tableData,findIndex,{
          ...this.tableData[findIndex],
          isEditRow:true
        })
      }
    },
    //取消
    handleCancel(val,index){
      this.tableData = JSON.parse(JSON.stringify(this.baseTableData))
    },
    //提交
    handleSubmit(val){
      const { def_submit } = val
      let arr = []
      def_submit.map(v=>{
        let objData = {}
        Object.keys(v).map(vK=>{
          objData[vK] = val[v[vK]]
        })
        arr.push(objData)
      })
      // console.log(arr)
      if(!arr.every(v=>{return v["completeValue"] !== "--" && v["completeValue"] !== null && v["completeValue"] !== ""})){
        this.$message({
          message: `当前行还有实际完成值未录入`,
          type: 'warning'
        });
        return false;
      }
      if(!arr.every(v=>{return Number(v["completeValue"]) < 9999999999.99})){
        this.$message({
          message: `当前行实际完成值不能超过10位数字`,
          type: 'warning'
        });
        return false;
      }
      if(!arr.every(v=>{return Number(v["completeValue"]) > 0})){
        this.$message({
          message: `当前行实际完成值不允许为负数`,
          type: 'warning'
        });
        return false;
      }
      let obj = {
        completeValues:arr.filter(v=>v["completeValue"] !== "--" && v["examineeIndicatorId"] !== null),
        updateType:2
      }
      // console.log(obj)
      getMyPlanTodoSetCompleteValue(obj).then(res=>{
        if(res.success){
          this.$message({message: '修改成功',type: 'success'});
          this.handleInit()
        }else{
          this.$message.error(res.msg);
        }
      })
    },
    handleSearch({limit,start,page}){
      this.page = page
      this.limit = limit
      this.start = start
      this.handleGetMyPlanTodoCompleteValueList();
    },
    handleBaseTableHeader(){
      if(Number(this.type) !== 1){
        return [...this.base_tableHeader,...this.def_tableHeader]
      }else{
        return [...this.base_tableHeader,...this.def_tableHeader,...this.btn_tableHeader]
      }
      
    },
    handleInputChange(val){
      console.log(val)
      if(Number(val)>9999999999.99){
        this.$message({
          message: `实际完成值不能超过10位数字`,
          type: 'warning'
        });
        return false
      }
      if(0>Number(val)){
        this.$message({
          message: `实际完成值不允许为负数`,
          type: 'warning'
        });
        return false;
      }
    }
    //计算评分
    // handleInputChange(row,itemChild){
    //   const { isEditRow,realProp,relaField,ruleData,maxScore,dataRuleType,targetValue } = itemChild
    //   this.tableData.map(v=>{
    //     if(v["isEditRow"]){
    //       // console.log(JSON.parse(v[ruleData]))
    //       let val = v[realProp]
    //       let rule = JSON.parse(v[ruleData])
    //       // console.log(val)
    //       if(val == ""){
    //         v[relaField] = '--'
    //       }else{
    //         console.log(v[dataRuleType])
    //         if(v[dataRuleType]===1){
    //           if(rule.length!==0&&rule[rule.length-1].max<val){
    //             v[relaField] = v[maxScore]
    //           }else{
    //             rule.forEach((vR,vI)=>{
    //               if(vI==1){//第一个区间 
    //                 if(vR.min<=val&&vR.max>=val){
    //                   // console.log(relaField,vR.score)
    //                   v[relaField] = vR.score
    //                   return
    //                 }
    //               }else{
    //                 if(vR.min<val&&vR.max>=val){
    //                   console.log(relaField,vR.score)
    //                   v[relaField] = vR.score
    //                   return
    //                 }
    //               }
    //             })
    //           }
    //         }
    //         if(v[dataRuleType]===2){
    //           let mathVal = val/v[targetValue]
    //           v[relaField] = mathVal.toFixed(2)
    //         }
    //       }
    //     }
    //   })
    // }
  },
  watch: {
    // tableData: {
    //   handler(val) {
    //     this.handleComputePf();
    //   },
    //   deep: true
    // },
  }
}
</script>
<style lang='scss' scoped>
.finish-state{
  .table-header{
    display: flex;
    justify-content: space-between;
  }
  .table-btn{
    display: flex;
    justify-content: center;
    padding: 24px 0 24px 0;
  }
}
</style>