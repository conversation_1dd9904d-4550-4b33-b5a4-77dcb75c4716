<template>
  <div class="tabs-wait">
    <section class="table-header">
      <section>
        <section class="tabs-wait-search">
          <el-radio-group
            v-model="planType"
            @change="handleGroupChange"
            style="display: flex; margin-right: 10px"
          >
            <el-radio-button :label="null">全部</el-radio-button>
            <el-radio-button :label="1">公司考核</el-radio-button>
            <el-radio-button :label="2">部门考核</el-radio-button>
            <el-radio-button :label="3">个人考核</el-radio-button>
          </el-radio-group>
          <el-select
            v-model="valueGs"
            clearable
            placeholder="请选择公司"
            style="width: 250px"
            v-if="planType === 1"
            @change="handleInit"
          >
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-input
            v-model="valueYg"
            placeholder="请输入员工姓名/手机号"
            style="width: 250px"
            v-if="planType === 3"
            @keyup.enter.native="handleInit"
            suffix-icon="el-icon-search"
          ></el-input>
          <!-- <def-department v-model="valueBm" placeholder="请选择部门" style="width:250px;" 
            v-if="planType === 2||type === 3" 
            @input="handleInit"
          /> -->
          <el-input
            v-if="planType === 2 || type === 3"
            v-model="valueBmName"
            placeholder="请选择部门"
            style="width: 250px"
            @focus="handleFocusDepart()"
            @clear="handleClearDepart"
            clearable
          >
            <i
              slot="suffix"
              v-if="!valueBmName"
              class="el-input__icon el-icon-plus"
            ></i>
          </el-input>
        </section>
      </section>
      <section
        style="display: flex"
        v-if="state !== 'done'"
        class="tabs-wait-btn"
      >
        <template v-for="(item, index) in btnList">
          <el-button
            v-show="item.name == btnIsshowObj[type]"
            :key="item.name"
            @click="handleBtnClick(item.name)"
            >{{ item.label }}</el-button
          >
        </template>
      </section>
    </section>
    <section class="def_per_section-top">
      <def-etable
        ref="def_etable"
        :tableHeader="tableHeader"
        :tableData="tableData"
        @formatter="handleFormatter"
        @btnColumn="handleBtnColumn"
        @search="handleSearch"
        :total="total"
        :isShowIndex="true"
        :isHidePage="false"
      />
    </section>
    <select-staff
      v-if="showDialog"
      :list="data"
      :isUser="isUser"
      :isOnly="true"
      :isDifferent="true"
      @close="showDialog = false"
      @commit="handleCommit"
      :select="def_select"
    ></select-staff>
  </div>
</template>

<script>
import {
  defHeader,
  defCard,
  defNode,
  defTitle,
  defTable,
  defPhoto,
  defEtable,
  defDepartment,
} from "../index";
import {
  getMyPlanTodoApproveList,
  getMyPlanTodoConfirmList,
  getMyPlanTodoScoreList,
  getMyPlanTodoInputList,
  getSubsidiaryList,
  getDepartmentTree,
} from "performance/store/api.js";
import { khlxType, khzqPeriodType } from "performance/utils/enum.js";
import { date2Str, havePrivilege } from "performance/utils/util.js";
import SelectStaff from "performance/pages/performanceManage/components/SelectStaff";
export default {
  name: "tabs-wait",
  components: {
    defTable,
    defEtable,
    defDepartment,
    SelectStaff,
  },
  props: {
    type: {
      type: String,
      default: "",
    },
    state: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      data: [],
      // section: [],
      isUser: true,
      showDialog: false,
      def_select: [],
      /**
       * 筛选条件相关
       */
      options: [],
      valueGs: null,
      valueYg: "",
      valueBm: null,
      valueBmName: null,
      planType: null,

      listType: null, //待我确认/评分/审核/录入类型：1-待我确认/评分/审核/录入；2-我已确认/评分/审核/录入；

      tableHeight:
        this.state == "done"
          ? document.body.clientHeight - 305 + "px"
          : document.body.clientHeight - 343 + "px",
      tableHeader: [
        { label: "考核对象", prop: "khdx" },
        { label: "关联人员", prop: "glry" },
        { label: "考核类型", prop: "khlx" },
        { label: "被考核人所在公司", prop: "szgs" },
        { label: "考核计划", prop: "khjh" },
        { label: "考核周期", prop: "khzq" },
        { label: "当前阶段", prop: "dqjd" },
        {
          prop: "def_cz",
          label: "操作",
          width: "100px",
          btn: [
            {
              prop: "def_xq",
              label: this.state == "done" ? "详情" : "去处理",
              type: "def_btn",
              fun: "handleDetail",
            },
          ],
        },
      ],
      tableHeaderLr: [
        { label: "考核计划", prop: "khjh" },
        { label: "考核类型", prop: "khlx" },
        { label: "考核对象", prop: "khdxlr" },
        { label: "关联人员", prop: "glry" },
        { label: "考核周期", prop: "khzq" },
        {
          prop: "def_cz",
          label: "操作",
          width: "100px",
          btn: [
            {
              prop: "def_xq",
              label: this.state == "done" ? "详情" : "去处理",
              type: "def_btn",
              fun: "handleDetail",
            },
          ],
        },
      ],

      tableHeaderBase: [
        { label: "考核对象", prop: "khdx" },
        { label: "关联人员", prop: "glry" },
        { label: "考核类型", prop: "khlx" },
        { label: "被考核人所在公司", prop: "szgs" },
        { label: "考核计划", prop: "khjh" },
        { label: "考核周期", prop: "khzq" },
        { label: "当前阶段", prop: "dqjd" },
        {
          prop: "def_cz",
          label: "操作",
          width: "100px",
          btn: [
            {
              prop: "def_xq",
              label: this.state == "done" ? "详情" : "去处理",
              type: "def_btn",
              fun: "handleDetail",
            },
          ],
        },
      ],

      tableHeaderLrBase: [
        { label: "考核计划", prop: "khjh" },
        { label: "考核类型", prop: "khlx" },
        { label: "考核对象", prop: "khdxlr" },
        { label: "关联人员", prop: "glry" },
        { label: "考核周期", prop: "khzq" },
        {
          prop: "def_cz",
          label: "操作",
          width: "100px",
          btn: [
            {
              prop: "def_xq",
              label: this.state == "done" ? "详情" : "去处理",
              type: "def_btn",
              fun: "handleDetail",
            },
          ],
        },
      ],

      tableData: [],
      total: null,
      limit: 10,
      start: 0,
      page: 1,

      btnIsshowObj: {
        dwqr: "wyqr",
        dwpf: "wypf",
        dwsh: "wysh",
        dwlr: "wylr",
      },
      btnList: [
        { label: "我已确认", name: "wyqr" },
        { label: "我已评分", name: "wypf" },
        { label: "我已审核", name: "wysh" },
        { label: "我已录入", name: "wylr" },
      ],
    };
  },
  created() {
    this.handleHeader();
  },
  // activated(){
  //   console.log('activated')
  //   this.handleInit()
  //   this.handleTableResize()
  // },
  mounted() {
    this.handleInit();
    this.handleTableResize();
  },
  methods: {
    handleCommit(list) {
      if (list && list.length > 0) {
        this.valueBm = list[0].id;
        this.valueBmName = list[0].name;
        this.def_select = list;
      } else {
        this.valueBm = null;
        this.valueBmName = null;
        this.def_select = [];
      }
      this.handleInit();
      // this.temporary2 = list[0].name;
      this.showDialog = false;
    },
    //获取部门树
    async getDepartmentTree() {
      const res = await getDepartmentTree();
      if (res.success) {
        // this.section = res.data;
        // this.data = this.section;
        this.data = res.data;
      } else {
        this.$message.error(res.msg);
      }
      // console.log(res);
    },
    async handleFocusDepart() {
      await this.getDepartmentTree();
      this.showDialog = true;
    },
    handleClearDepart() {
      this.valueBm = null;
      this.valueBmName = null;
      this.def_select = [];
      this.handleInit();
    },
    handleHeader() {
      if (this.type == "dwlr") {
        this.tableHeader = this.tableHeaderLr;
      }
    },
    handleInit() {
      this.tableData = [];
      this.listType = this.state == "done" ? 2 : 1;
      this.handleGetSubsidiaryList();
      switch (this.type) {
        case "dwqr":
          this.handleGetMyPlanTodoConfirmList();
          break;
        case "dwpf":
          this.handleGetMyPlanTodoScoreList();
          break;
        case "dwsh":
          this.handleGetMyPlanTodoApproveList();
          break;
        case "dwlr":
          this.handleGetMyPlanTodoInputList();
          break;
      }
    },
    handleGroupChange() {
      this.hanldeFilterTableHeader();
      this.handleClearSearch();
      this.handleInit();
    },
    hanldeFilterTableHeader() {
      console.log(this.planType);
      if (this.planType === 3) {
        this.tableHeader = this.tableHeaderBase.filter((v) => {
          return v.prop !== "glry";
        });
        this.tableHeaderLr = this.tableHeaderLrBase.filter((v) => {
          return v.prop !== "glry";
        });
      } else {
        this.tableHeader = this.tableHeaderBase;
        this.tableHeaderLr = this.tableHeaderLrBase;
      }
      this.handleHeader();
      this.$nextTick(() => {
        this.$refs.def_etable.$refs.Etable.doLayout();
      });
    },
    handleClearSearch() {
      this.page = 1;
      this.valueGs = null;
      this.valueYg = "";
      this.valueBm = null;
      this.valueBmName = "";
    },
    handleTableResize() {
      let height = this.state == "done" ? 300 : 360;
      window.onresize = () => {
        return (() => {
          this.tableHeight = document.body.clientHeight - height + "px";
        })();
      };
    },
    async handleGetSubsidiaryList() {
      const { data } = await getSubsidiaryList();
      this.options = data;
    },
    // 待我考核-审核列表
    async handleGetMyPlanTodoApproveList() {
      let obj = {
        // confirmToType:1,//确认类型：1-给自己确认；2-给他人确认
        currentPage: this.page, //当前页
        deptId: this.valueBm, //部门id
        employeeKeyword: this.valueYg, //人员姓名
        pageSize: this.limit, //每页大小
        planType: this.planType, //考核类型：1-公司考核；2-部门考核；3-个人考核
        subsidiaryId: this.valueGs, //用工主体id
        type: this.listType, //待我确认/评分/审核/录入类型：1-待我确认/评分/审核/录入；2-我已确认/评分/审核/录入；
      };
      const { data } = await getMyPlanTodoApproveList(obj);
      this.tableData = data.records;
      this.total = data.total;
    },
    // 待我考核-确认列表
    async handleGetMyPlanTodoConfirmList() {
      let obj = {
        confirmToType: 2, //确认类型：1-给自己确认；2-给他人确认
        currentPage: this.page, //当前页
        deptId: this.valueBm, //部门id
        employeeKeyword: this.valueYg, //人员姓名
        pageSize: this.limit, //每页大小
        planType: this.planType, //考核类型：1-公司考核；2-部门考核；3-个人考核
        subsidiaryId: this.valueGs, //用工主体id
        type: this.listType, //待我确认/评分/审核/录入类型：1-待我确认/评分/审核/录入；2-我已确认/评分/审核/录入；
      };
      const { data } = await getMyPlanTodoConfirmList(obj);
      this.tableData = data.records;
      this.total = data.total;
    },
    // 待我考核-评分列表
    async handleGetMyPlanTodoScoreList() {
      let obj = {
        // confirmToType:1,//确认类型：1-给自己确认；2-给他人确认
        currentPage: this.page, //当前页
        deptId: this.valueBm, //部门id
        employeeKeyword: this.valueYg, //人员姓名
        pageSize: this.limit, //每页大小
        planType: this.planType, //考核类型：1-公司考核；2-部门考核；3-个人考核
        subsidiaryId: this.valueGs, //用工主体id
        type: this.listType, //待我确认/评分/审核/录入类型：1-待我确认/评分/审核/录入；2-我已确认/评分/审核/录入；
      };
      const { data } = await getMyPlanTodoScoreList(obj);
      this.tableData = data.records;
      this.total = data.total;
    },
    // 待我考核-录入实际完成值列表
    async handleGetMyPlanTodoInputList() {
      let obj = {
        // confirmToType:1,//确认类型：1-给自己确认；2-给他人确认
        currentPage: this.page, //当前页
        deptId: this.valueBm, //部门id
        employeeKeyword: this.valueYg, //人员姓名
        pageSize: this.limit, //每页大小
        planType: this.planType, //考核类型：1-公司考核；2-部门考核；3-个人考核
        subsidiaryId: this.valueGs, //用工主体id
        type: this.listType, //待我确认/评分/审核/录入类型：1-待我确认/评分/审核/录入；2-我已确认/评分/审核/录入；
      };
      const { data } = await getMyPlanTodoInputList(obj);
      this.tableData = data.records;
      this.total = data.total;
    },
    handleFormatter({ prop, data, btnItem }, callback) {
      let _this = this;
      if (prop == "def_cz") {
        switch (btnItem) {
          case "def_xq":
            // callback(true)
            callback(havePrivilege("kpi.performance.myTodo.detail"));
            break;
        }
      } else {
        switch (prop) {
          case "khdx":
            callback(data["examineeName"]);
            break;
          case "khdxlr":
            callback(this.handleArrData(data["examineePlanName"]));
            break;
          case "glry":
            callback(this.handleArrData(data["relationName"]));
            break;
          case "khlx":
            callback(khlxType[data["type"]]);
            break;
          case "szgs":
            callback(this.handleArrData(data["subsidiaryName"]));
            break;
          case "khjh":
            callback(data["planName"]);
            break;
          case "khzq":
            callback(
              date2Str(
                data["period"],
                data["planStartDate"],
                data["planEndDate"]
              )
            );
            break;
          case "dqjd":
            callback(data["currentStage"]);
            break;
        }
      }
    },
    handleArrData(list) {
      if (Object.prototype.toString.call(list) == "[object Array]") {
        if (list.length == 0) {
          return "--";
        } else {
          return list.join("，");
        }
      } else {
        return "--";
      }
    },
    handleBtnColumn(val, type) {
      console.log(val, type);
      switch (type) {
        case "handleDetail":
          if (this.type == "dwlr") {
            this.$router.push({
              path: "/wait-myper/finish-State",
              query: { planId: val["planId"], type: this.listType },
            });
          }
          if (["dwqr", "dwpf", "dwsh"].includes(this.type)) {
            this.$router.push({
              path: "/my-performance/detail",
              query: { examineePlanId: val["examineePlanId"] },
            });
          }
          break;
      }
    },
    handleSearch({ limit, start, page }) {
      this.page = page;
      this.limit = limit;
      this.start = start;
      this.handleInit();
    },
    handleBtnClick(name) {
      this.$router.push({
        path: "/wait-myper/done-list",
        query: { type: this.type, state: "done" },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.tabs-wait {
  .table-header {
    display: flex;
    justify-content: space-between;
  }
  .tabs-wait-search {
    display: flex;
  }
  .tabs-wait-btn {
    /deep/.el-button {
      color: var(--color-primary);
      border: 1px solid var(--color-primary);
    }
  }
}
</style>
