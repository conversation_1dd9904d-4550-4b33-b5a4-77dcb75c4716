<template>
  <div class="tabs-waitItem def_per_height">
    <def-header 
      :headerText="headerText" 
      :isBack="true"
    />
    <section class="def_per_section def_per_section-top">
      <tabs-for-wait v-if="isShowTemp" :type="type" :state="state"></tabs-for-wait>
    </section>
  </div>
</template>

<script>
import TabsForWait from "./TabsForWait";
import { defHeader,defCard,defNode,defTitle,defTable,defPhoto,defEtable } from '../index'
import { getMyPlanTodoApproveList,getMyPlanTodoConfirmList,getMyPlanTodoScoreList,getMyPlanTodoInputList } from 'performance/store/api.js'
export default {
  name: 'tabs-wait',
  components: {
    defHeader,
    defTable,
    defEtable,
    TabsForWait
  },
  props:{
    activeName:{
      type:String,
      default:""
    }
  },
  data() {
    return {
      type:"",
      state:"",
      isShowTemp:false,
      titleList:{
        dwqr:"我已确认",
        dwpf:"我已评分",
        dwsh:"我已审核",
        dwlr:"我已录入",
      },
      headerText:""
    };
  },
  mounted() {
    this.handleInit()
  },
  methods: {
    handleInit(){
      const { type,state } = this.$route.query
      console.log(type,state)
      this.type = type;
      this.state = state
      this.isShowTemp = true
      this.headerText = `${this.titleList[this.type]}的考核`
    },
  },
}
</script>
<style lang='scss' scoped>
.tabs-waitItem{
  display: flex;
  flex-direction: column;
  .table-header{
    display: flex;
    justify-content: space-between;
  }
}

</style>