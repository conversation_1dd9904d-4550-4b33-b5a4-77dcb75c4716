<template>
  <div class="wait-list def_per_height">
    <def-header headerText="查看待确认考核表" :isBack="true" />
    <section class="def_per_section def_per_section-top">
      <def-etable
        :tableHeader="tableHeader"
        :tableData="tableData"
        @formatter="handleFormatter"
        @btnColumn="handleBtnColumn"
        @search="handleSearch"
        :total="total"
        :isShowIndex="true"
        :isHidePage="false"
      />
    </section>
  </div>
</template>

<script>
import { defHeader, defTable, defEtable } from "../index";
import { getMyPlanTodoConfirmList } from "performance/store/api.js";
import { khlxType, khzqPeriodType } from "performance/utils/enum.js";
import { date2Str } from "performance/utils/util.js";
export default {
  name: "wait-list",
  components: {
    defHeader,
    defTable,
    defEtable,
  },
  data() {
    return {
      tableHeight: document.body.clientHeight - 245 + "px",
      tableHeader: [
        { label: "考核对象", prop: "khdx" },
        { label: "关联人员", prop: "glry" },
        { label: "考核类型", prop: "khlx" },
        { label: "我所在公司", prop: "wszgs" },
        { label: "考核计划", prop: "khjh" },
        { label: "考核周期", prop: "khzq" },
        { label: "当前阶段", prop: "dqjd" },
        // { label: "绩效结果", prop: "jxjg" },
        {
          prop: "def_cz",
          label: "操作",
          width: "100px",
          btn: [
            {
              prop: "def_xq",
              label: "详情",
              type: "def_btn",
              fun: "handleDetail",
            },
          ],
        },
      ],
      tableData: [],
      total: null,
      limit: 10,
      start: 0,
      page: 1,
    };
  },
  mounted() {
    this.handleInit();
    this.handleTableResize();
  },
  methods: {
    handleInit() {
      this.handleGetMyPlanTodoConfirmList();
    },
    handleTableResize() {
      window.onresize = () => {
        return (() => {
          this.tableHeight = document.body.clientHeight - 250 + "px";
        })();
      };
    },
    handleFormatter({ prop, data, btnItem }, callback) {
      if (prop == "def_cz") {
        switch (btnItem) {
          case "def_xq":
            callback(true);
            break;
        }
      } else {
        switch (prop) {
          case "khdx":
            callback(data["examineeName"]);
            break;
          case "glry":
            callback(this.handleArrData(data["relationName"]));
            break;
          case "khlx":
            callback(khlxType[data["type"]]);
            break;
          case "wszgs":
            callback(this.handleArrData(data["subsidiaryName"]));
            break;
          case "khjh":
            callback(data["planName"]);
            break;
          case "khzq":
            // callback(khzqPeriodType[data["period"]])
            callback(
              date2Str(
                data["period"],
                data["planStartDate"],
                data["planEndDate"]
              )
            );
            break;
          case "dqjd":
            callback(data["currentStage"]);
            break;
        }
      }
    },
    handleArrData(list) {
      if (Object.prototype.toString.call(list) == "[object Array]") {
        if (list.length == 0) {
          return "--";
        } else {
          return list.join("，");
        }
      } else {
        return "--";
      }
    },
    handleBtnColumn(val, type) {
      console.log(val);
      switch (type) {
        case "handleDetail":
          this.$router.push({
            path: "/my-performance/detail",
            query: { examineePlanId: val["examineePlanId"] },
          });
          break;
      }
    },
    handleSearch({ limit, start, page }) {
      this.page = page;
      this.limit = limit;
      this.start = start;
      this.handleInit();
    },
    // 待我考核-确认列表
    async handleGetMyPlanTodoConfirmList() {
      let obj = {
        confirmToType: 1, //确认类型：1-给自己确认；2-给他人确认
        currentPage: this.page, //当前页
        // deptId:this.valueBm,//部门id
        // employeeKeyword:this.valueYg,//人员姓名
        pageSize: this.limit, //每页大小
        // planType:this.planType,//考核类型：1-公司考核；2-部门考核；3-个人考核
        // subsidiaryId:this.valueGs,//用工主体id
        type: 1, //待我确认/评分/审核/录入类型：1-待我确认/评分/审核/录入；2-我已确认/评分/审核/录入；
      };
      const { data } = await getMyPlanTodoConfirmList(obj);
      this.tableData = data.records;
      this.total = data.total;
    },
  },
};
</script>
<style lang="scss" scoped>
.wait-list {
}
</style>
