<template>
  <div
    class="performance_detail def_per_height"
    v-if="!loading"
    v-loading="loading"
  >
    <def-header
      :headerText="def_HeaderData.headerText"
      :isBack="true"
      :isShowTag="true"
      :headerTag="def_HeaderData.headerTag"
    />
    <section class="def_per_section def_per_section-top" v-if="isShowAlert">
      <el-alert title="">
        <i
          class="iconfont-per icon-Tips1"
          style="font-size: 16px; color: #4f71ff"
        ></i>
        <span
          style="
            margin-left: 10px;
            font-weight: Medium;
            font-size: 14px;
            color: #555555;
          "
          >您的评分被考核人不可见，请放心评分~</span
        >
      </el-alert>
    </section>
    <section class="def_per_section def_per_section-top">
      <def-card
        :isShowPhoto="handleIsshowCardPhoto()"
        :cardPhoto="def_CardData.cardPhoto"
        :lineOne="def_CardData.cardDataLineOne"
        :lineTwo="def_CardData.cardDataLineTwo"
        :lineThree="def_CardData.cardDataLineThree"
        :lineFour="def_CardData.cardDataLineFour"
        :isShowRate="handleIsshowRate()"
        :rate="def_CardData.cardRate"
        :isShowStep="true"
        :steps="def_CardData.cardSteps"
      >
        <section slot="btnArea">
          <old-button>修改流程</old-button>
        </section>
      </def-card>
    </section>
    <section
      class="def_per_section def_per_section-top"
      v-if="handleSourceSection('绩效结果')"
    >
      <def-title text="绩效结果" />
      <detail-jxjg
        :score="def_CardData.cardRate.score"
        :grade="def_CardData.cardRate.grade"
      />
    </section>
    <section
      class="def_per_section def_per_section-top"
      v-if="handleSourceSection('考核指标评分明细')"
    >
      <def-title text="考核指标评分明细" />
      <section class="detail-table def_fixed_height">
        <def-etable
          ref="def_etable"
          :tableHeader="tableHeader_khzbpf"
          :tableData="def_TableData"
          @formatter="handleFormatter"
          @btnColumn="handleBtnColumn"
          @search="handleSearch"
          :total="total"
          :isShowIndex="true"
          :def_height="tableHeight"
          :isHidePage="true"
          @inputCheck="handleCheckTag"
        />
      </section>
    </section>

    <section
      class="def_per_section def_per_section-top"
      v-if="handleSourceSection('考核指标明细')"
    >
      <def-title text="考核指标明细" />
      <section class="detail-table" v-if="handleIsshowKhzbmx('非自己确认')">
        <section style="overflow: auto">
          <indicator-list
            ref="refIndicator"
            :list="def_TableData"
            :basicInfo="basicInfo"
            @commit="handleZbmxCommit"
            :scorerRequire="scorerRequire"
          />
        </section>
      </section>
      <section
        class="detail-table def_fixed_height"
        v-if="def_TableData.length !== 0 && handleIsshowKhzbmx('自己确认')"
      >
        <def-etable
          ref="def_etable"
          :tableHeader="tableHeader_khzb"
          :tableData="def_TableData"
          @formatter="handleFormatter"
          @btnColumn="handleBtnColumn"
          @search="handleSearch"
          :total="total"
          :isShowIndex="true"
          :def_height="tableHeight"
          :isHidePage="true"
        />
      </section>
    </section>

    <section
      class="def_per_section def_per_section-top lc"
      v-if="def_CommentData.length !== 0 && handleSourceSection('总评')"
    >
      <def-title text="总评" />
      <detailZp :list="def_CommentData" />
    </section>

    <section
      class="def_per_section def_per_section-top"
      v-if="
        handleSourceSection('考核指标修正记录') &&
        def_ChangeProcessData.length !== 0
      "
    >
      <def-title text="考核指标修正记录" />
      <detail-xgjl :list="def_ChangeProcessData" />
    </section>

    <section
      class="def_per_section def_per_section-top lc"
      v-if="handleSourceSection('考核评分流程')"
    >
      <def-title text="考核评分流程" />
      <detail-lc :list="def_ScoreProcessData" />
    </section>

    <section
      class="def_per_section def_per_section-top lc"
      v-if="handleSourceSection('结果审核流程')"
    >
      <def-title text="结果审核流程" />
      <detail-lc :list="def_ApproveProcessData" />
    </section>

    <section
      class="def_per_section def_per_section-top lc"
      v-if="handleSourceSection('考核确认流程')"
    >
      <def-title text="考核确认流程" />
      <detail-lc :list="def_ConfirmProcessData" />
    </section>

    <!-- <section style="height:115px"></section> -->

    <section class="detail-btn def_per_section">
      <el-divider></el-divider>
      <section class="detail-btn-items">
        <template v-for="(item, index) in btnList">
          <el-button :type="item.type" @click="handleBtnList(item.fun)">{{
            item.label
          }}</el-button>
        </template>
      </section>
    </section>
  </div>
</template>

<script>
import {
  defHeader,
  defCard,
  defNode,
  defTitle,
  defTable,
  defPhoto,
  defEtable,
  defNewNode,
} from "./components";
import {
  getMyPlanDetail,
  getPlanDetail,
  getExamineePlanDetail,
  getMyPlanTodoConfirm,
  getMyPlanTodoSetScore,
  getMyPlanTodoApprove,
} from "performance/store/api.js";
import {
  pfType,
  khpfjdztStatus,
  khqrStatus,
  khlxType,
  khqrztStatus,
  khzqPeriodType,
  jdzxztNodetype,
  jdtType,
  zblxType,
  pffsScoreType,
  khqrjdztStatus,
  stageStatus,
  otherStatus,
} from "performance/utils/enum.js";
import indicatorList from "performance/pages/performanceManage/components/IndicatorList";
import { math } from "performance/utils/math.js";
import { date2Str } from "performance/utils/util.js";
import detailJxjg from "performance/pages/personalPerformance/components/compDetail/DetailJxjg";
import detailZp from "performance/pages/personalPerformance/components/compDetail/DetailZp";
import detailLc from "performance/pages/personalPerformance/components/compDetail/DetailLc";
import detailXgjl from "performance/pages/personalPerformance/components/compDetail/DetailXgjl";
import * as AT from "@/store/actionTypes";
export default {
  name: "performance_detail",
  components: {
    defHeader,
    defCard,
    defNode,
    defTitle,
    defTable,
    defPhoto,
    defEtable,
    defNewNode,
    indicatorList,
    detailJxjg,
    detailZp,
    detailLc,
    detailXgjl,
  },
  data() {
    return {
      isShowAlert: false,
      $dayjs: this.$dayjs,
      examineePlanId: null,
      loading: true,
      type: null,
      /*
        详情
      */

      basicInfo: {}, //考核对象基本信息
      indicatorList: [], //考核指标信息
      process: {}, //考核对象的流程数据
      resultSetting: {}, //考核结果设置
      scoreLevel: "", //总分Level
      totalScore: null, //总分
      processorType: null, //1:被考核者,2:上级,3:指定人员
      adminType: {}, //当前登录人对当前计划的状态 1:处理中,2:等待处理,3:已处理
      scorerRequire: false,

      stageStatus: null, //当前登录人所看到的状态
      basicInfoStageStatus: null, //当前考核对象所在状态

      // tableHeight:document.body.clientHeight - 500 +'px',
      tableHeight: "auto",
      total: null,
      limit: 10,
      start: 0,
      page: 1,

      tableHeaderBase: [
        { label: "考核指标名称", prop: "khzbmc", width: "150px" },
        { label: "考核指标类型", prop: "khzblx", width: "150px" },
        { label: "考核指标说明", prop: "khzbsm", isTip: true },
        { label: "评价标准", prop: "pjbz", width: "150px", isTip: true },
        { label: "评分上限", prop: "pfsx", width: "100px", align: "right" },
        {
          label: "考核指标权重",
          prop: "khzbqz",
          width: "100px",
          align: "right",
        },
        { label: "目标值", prop: "mbz", width: "100px", align: "right" },
        {
          label: "评分方式",
          prop: "pffs",
          width: "150px",
          align: "left",
          isTip: true,
        },
        { label: "实际完成值", prop: "sjwcz", width: "100px", align: "right" },
        {
          label: "绩效评分",
          prop: "jxpf",
          isFixed: "right",
          width: "480px",
          align: "center",
          children: [
            {
              label: "评分人",
              prop: "pfr",
              type: "addRow",
              isFixed: "right",
              width: "80px",
              isTip: true,
            },
            {
              label: "考核指标评分",
              prop: "khzbpf",
              type: "addRow",
              isEdit: true,
              realProp: "score",
              fatherProp: "scoreData",
              isEditRowKey: "isCanEditScore",
              isShowTag: true,
              editType: "number",
              numberType: "2",
              tagProp: "maxScore",
              isFixed: "right",
              width: "200px",
              align: "right",
            },
            {
              label: "考核指标评语",
              prop: "khzbpy",
              type: "addRow",
              isEdit: true,
              realProp: "comment",
              fatherProp: "scoreData",
              isEditRowKey: "isCanEditComment",
              editType: "textarea",
              maxlength: 200,
              isTip: true,
              isFixed: "right",
              width: "200px",
            },
          ],
        },
        {
          label: "考核指标总分",
          prop: "khzbzf",
          isFixed: "right",
          width: "150px",
          align: "right",
        },
        { label: "考核指标评分人", prop: "khzbpfr", width: "150px" },
      ],

      btnList: [
        { id: 1, label: "取消", name: "qx", type: "", fun: "handleCancel" },
        {
          id: 2,
          label: "确认并提交",
          name: "qrbtj-other",
          type: "primary",
          fun: "handleConfirmSubmitOther",
        }, //他人确认
        {
          id: 3,
          label: "确认并提交",
          name: "qrbtj-me",
          type: "primary",
          fun: "handleConfirmSubmitMe",
        }, //自己确认
        {
          id: 4,
          label: "提交考核",
          name: "tjkh-pf",
          type: "primary",
          fun: "handleSetScore",
        },
        {
          id: 4,
          label: "确认并提交",
          name: "tjkh-sh",
          type: "primary",
          fun: "handleApprove",
        },
      ],
      /**
       * 考核指标明细
       */
      tableHeader_khzb: [],
      /**
       * 考核指标评分明细
       */
      tableHeader_khzbpf: [],

      // khqrjdztStatus: khqrjdztStatus, //考核确认节点状态
      // khpfjdztStatus: khpfjdztStatus, //考核评分节点状态
      otherStatus: otherStatus,
      pfType: pfType, //评分类型

      def_HeaderData: {},
      def_CardData: {},
      def_TableData: [],
      def_CommentData: [],
      def_ScoreProcessData: [],
      def_ConfirmProcessData: [],
      def_ChangeProcessData: [],
      def_ApproveProcessData: [],
      def_StartInfo: {},
      def_DataMarkersData: {},

      /**
       * xq_pf:考核详情-评分
       * xq_sh:考核详情-审核
       * xq_qr:考核详情-确认
       */
      source: "",
      sourceShowSection: {
        xq_pf: ["考核指标评分明细", "总评", "考核评分流程"],
        xq_sh: ["绩效结果", "考核指标评分明细", "总评", "结果审核流程"],
        xq_qr: ["考核指标明细", "考核确认流程", "考核指标修正记录"],
      },

      // globalState: {
      //   currentStage: "", //当前阶段
      //   state: null //1:未开始,2:进行中,3:已完成
      // },
      //1:等待处理,2:处理中,3:已处理
      //titlt status
      def_approveStatus: {
        1: "待审核",
        2: "审核中",
        3: "审核完成",
      },
      def_scoreStatus: {
        1: "待评分",
        2: "评分中",
        3: "评分完成",
      },
      def_confirmStatus: {
        1: "待确认",
        2: "确认中",
        3: "确认完成",
      },
      //1:处理中,2:等待处理,3:已处理
      //进度条
      def_approveStatusSteps: {
        1: "审核中",
        2: "待审核",
        3: "审核完成",
      },
      def_scoreStatusSteps: {
        1: "评分中",
        2: "待评分",
        3: "评分完成",
      },
      def_confirmStatusSteps: {
        1: "确认中",
        2: "待确认",
        3: "确认完成",
      },

      def_processorType: {
        1: "被考核者",
        2: "上级",
        3: "指定人员",
      },

      def_submitIndicatorListData: {}, //评分详情 - 考核指标评分明细提交数据汇总
      def_submitCommentProcessData: "", //评分详情 - 总评提交数据汇总
      def_IndicatorListDataSum: {}, //当前评分人所有指标评分汇总
    };
  },
  mounted() {
    this.handleInit();
  },
  methods: {
    handleInit() {
      this.handleGetMyPlanDetail();
    },
    handleAutoHeight() {
      this.$nextTick(() => {
        let doms = document.querySelectorAll(".def_fixed_height");
        doms.forEach((v) => {
          console.log(v.clientHeight);
          if (v.clientHeight > 500) {
            this.tableHeight = "500px";
          }
        });
        // this.$refs.def_etable.$refs.Etable.doLayout();
      });
    },
    handleSourceSection(name) {
      return this.sourceShowSection[this.source].includes(name);
    },
    //是否显示评分等级
    handleIsshowRate() {
      if ([7, 8, 9].includes(this.stageStatus)) {
        return true;
      } else {
        return false;
      }
    },
    async handleGetMyPlanDetail() {
      const { examineePlanId } = this.$route.query;
      this.examineePlanId = Number(examineePlanId);
      let obj = {
        examineePlanId: this.examineePlanId,
      };
      if (true) {
        const { data } = await getMyPlanDetail(obj);
        const {
          basicInfo,
          indicatorList,
          process,
          resultSetting,
          scoreLevel,
          totalScore,
          processorType,
          stageStatus,
          scorerRequire,
        } = data;
        const { confirmStatus, scoreStatus, approveStatus } = data;
        this.basicInfo = basicInfo; //考核对象基本信息
        this.indicatorList = indicatorList; //考核指标信息
        this.process = process; //考核对象的流程数据
        this.resultSetting = resultSetting; //考核结果设置
        this.scoreLevel = scoreLevel; //总分Level
        this.totalScore = totalScore; //总分
        this.processorType = processorType; //1:被考核者,2:上级,3:指定人员
        this.type = this.basicInfo.type; //考核类型 1:公司考核,2:部门考核,3:个人考核
        this.scorerRequire = scorerRequire; //是否需要评分人

        this.adminType = { confirmStatus, scoreStatus, approveStatus };

        this.stageStatus = stageStatus; //当前登录人所看到的状态
        this.basicInfoStageStatus = this.basicInfo.stageStatus; //当前考核对象所在状态

        if ([1, 2, 3].includes(this.stageStatus)) {
          this.source = "xq_qr";
        }
        if ([4, 5, 6].includes(this.stageStatus)) {
          this.source = "xq_pf";
        }
        if ([7, 8, 9].includes(this.stageStatus)) {
          this.source = "xq_sh";
        }
      }
      // this.handleGlobalState(); //确定全局状态 当前详情类型
      this.handleAdminGlobalState(); //确定当前登录人全局状态
      this.handleNowState();
      this.handleBaseData();
      this.handleShowBtnList();
      this.handleShowAlert();
      this.hanldeFilterTableHeader();
      this.handleAutoHeight(); //通过渲染高度 控制table固定高度

      this.loading = false;
    },
    //汇总提交修改评分数据
    handleInputIndicatorList() {
      let arr = [];
      this.def_TableData.map((v) => {
        v.scoreData &&
          v.scoreData.map((vItem) => {
            if (vItem.isCanEditComment || vItem.isCanEditScore) {
              arr.push({
                comment: vItem.isCanEditComment ? vItem["comment"] : undefined, //指标评语
                examineeIndicatorId: v["examineeIndicatorId"], //计划指标id
                score: vItem.isCanEditScore ? vItem["score"] : undefined, //指标分数
                maxScore: v["maxScore"], //上限
                isCanEditScore: vItem.isCanEditScore,
              });
              let def_weight = v["weight"] == null ? 0 : v["weight"];
              // this.handleIndicatorListToCommentData(vItem["employeeId"],def_weight,vItem["score"],v["examineeIndicatorId"]);
            }
          });
      });
      this.def_submitIndicatorListData = arr;
    },
    //汇总提交修改总评数据
    handleInputCommentData() {
      let obj = this.def_CommentData.find((v) => {
        return v.isCanEdit == true;
      });
      if (obj !== undefined && Object.keys(obj).length !== 0) {
        this.def_submitCommentProcessData = obj.comment;
      } else {
        this.def_submitCommentProcessData = "";
      }
    },
    //指标评分明细关联总评数据 - 权重计算评分
    handleIndicatorListToCommentData(
      employeeId,
      weight,
      score,
      examineeIndicatorId
    ) {
      // console.log(employeeId,weight,score,examineeIndicatorId)
      const val = (score * weight) / 100;
      this.def_IndicatorListDataSum[examineeIndicatorId] = val;
      this.handleIndicatorListDataSum(employeeId);
    },
    //计算总评评分
    handleIndicatorListDataSum(employeeId) {
      // console.log(Object.values(this.def_IndicatorListDataSum))
      let val = math.add(Object.values(this.def_IndicatorListDataSum));
      // console.log(val)
      this.def_CommentData.map((v) => {
        if (v["employeeId"] == employeeId && v["isCanEdit"] == true) {
          if (!!Number(val)) {
            v.totalScore = (val * v["weight"]) / 100;
          } else {
            v.totalScore = 0;
          }
        }
      });
    },
    handleShowAlert() {
      const { visibleType } = this.resultSetting; //考核结果设置
      if (this.stageStatus == 5) {
        if (visibleType === 3) {
          //自己+上级的评分评语
          if ([3].includes(this.processorType)) {
            //processorType1:被考核者,2:上级,3:指定人员
            this.isShowAlert = true;
          }
        }
        if (visibleType === 1) {
          //1:仅自己评分评语
          if ([2, 3].includes(this.processorType)) {
            this.isShowAlert = true;
          }
        }
      }
    },
    handleShowBtnList() {
      this.btnList = this.btnList.filter((v) => {
        switch (v.name) {
          case "qx":
            return (
              (this.stageStatus === 2 && [2, 3].includes(this.processorType)) ||
              (this.stageStatus === 2 && [1].includes(this.processorType)) ||
              this.stageStatus === 5 ||
              this.stageStatus === 8
            );
          case "qrbtj-other":
            /**
             * 显示条件：
             * 1：确认阶段
             * 2：待确认 状态
             * 3：指定人/上级确认
             */
            return (
              this.stageStatus === 2 && [2, 3].includes(this.processorType)
            );
          case "qrbtj-me":
            /**
             * 显示条件：
             * 1：确认阶段
             * 2：待确认 状态
             * 3：自己确认
             */

            return this.stageStatus === 2 && [1].includes(this.processorType);
          case "tjkh-pf":
            /**
             * 显示条件：
             * 1：评分阶段
             * 2：待评分 状态
             */
            return this.stageStatus === 5;
          case "tjkh-sh":
            /**
             * 显示条件：
             * 1：审核阶段
             * 2：待审核 状态
             */
            return this.stageStatus === 8;
        }
      });
    },
    //确定表头信息
    hanldeFilterTableHeader(val) {
      this.tableHeader_khzbpf = this.tableHeaderBase.filter((v) => {
        if ([7, 8, 9].includes(this.stageStatus)) {
          return [
            "khzbmc",
            "khzblx",
            "khzbsm",
            "pjbz",
            "pfsx",
            "khzbqz",
            "mbz",
            "sjwcz",
            "jxpf",
            "khzbzf",
          ].includes(v.prop);
        } else {
          return [
            "khzbmc",
            "khzblx",
            "khzbsm",
            "pjbz",
            "pfsx",
            "khzbqz",
            "mbz",
            "sjwcz",
            "jxpf",
          ].includes(v.prop);
        }
      });
      this.tableHeader_khzb = this.tableHeaderBase.filter((v) => {
        if ([7, 8, 9].includes(this.stageStatus)) {
          return [
            "khzbmc",
            "khzblx",
            "khzbsm",
            "pjbz",
            "pfsx",
            "khzbqz",
            "mbz",
            "pffs",
            "khzbpfr",
            "khzbzf",
          ].includes(v.prop);
        } else {
          return [
            "khzbmc",
            "khzblx",
            "khzbsm",
            "pjbz",
            "pfsx",
            "khzbqz",
            "mbz",
            "pffs",
            "khzbpfr",
          ].includes(v.prop);
        }
      });
    },
    handleArrBaseData(list, key) {
      if (list === null || list.length === 0) {
        return "--";
      }
      let arr = [];
      list.map((v) => {
        arr.push(v[key]);
      });
      return arr.join("，");
    },
    /**
     * 详情
     */
    //构造渲染数据
    handleBaseData() {
      let isGr = this.type === 3 ? true : false;
      if (this.source == "xq_pf") {
        this.def_HeaderData = {
          headerText: this.handleHeaderName(),
          headerTag: this.handleHeaderTag(),
        };
        this.def_CardData = {
          cardPhoto: this.basicInfo.employeeName,
          cardDataLineOne: {
            name: this.handleCardName(),
            // phone: this.basicInfo.mobile ? this.basicInfo.mobile.substr(0, 3) + "****" + this.basicInfo.mobile.substr(7) : this.basicInfo.mobile,
            phone: this.basicInfo.mobile,
            tag: khlxType[this.basicInfo.type],
          },
          cardDataLineTwo: {
            label: isGr ? "" : "考核关联人员:",
            value: isGr
              ? `${this.basicInfo.subsidiaryName} ${this.basicInfo.deptName}`
              : this.handleExamineeRelations(this.basicInfo.examineeRelations),
          },
          cardDataLineThree: {
            label: "考核计划名称:",
            value: this.basicInfo.name,
          },
          cardDataLineFour: {
            label: "考核周期:",
            value: date2Str(
              this.basicInfo.period,
              this.basicInfo.startDate,
              this.basicInfo.endDate
            ),
          },
          cardRate: {
            score: this.totalScore,
            grade: this.scoreLevel || "无",
          },
          cardSteps: this.process.statusProcess.map((v) => {
            return {
              id: v.key,
              state: v.nodeType,
              text: this.handleStepsNodeName(v.nodes, v.nodeType),
              tips: this.handleStepsNodeTips(v.nodes, v.nodeType),
            };
          }),
        };
      }
      if (this.source == "xq_sh") {
        this.def_HeaderData = {
          headerText: this.handleHeaderName(),
          // headerTag:`${khqrStatus[this.basicInfo.confirmStatus]}`
          headerTag: this.handleHeaderTag(),
        };
        this.def_CardData = {
          cardPhoto: this.basicInfo.employeeName,
          cardDataLineOne: {
            name: this.handleCardName(),
            // phone: this.basicInfo.mobile ? this.basicInfo.mobile.substr(0, 3) + "****" + this.basicInfo.mobile.substr(7) : this.basicInfo.mobile,
            phone: this.basicInfo.mobile,
            tag: khlxType[this.basicInfo.type],
          },
          cardDataLineTwo: {
            label: isGr ? "" : "考核关联人员:",
            value: isGr
              ? `${this.basicInfo.subsidiaryName} ${this.basicInfo.deptName}`
              : this.handleExamineeRelations(this.basicInfo.examineeRelations),
          },
          cardDataLineThree: {
            label: "考核计划名称:",
            value: this.basicInfo.name,
          },
          cardDataLineFour: {
            label: "考核周期:",
            value: date2Str(
              this.basicInfo.period,
              this.basicInfo.startDate,
              this.basicInfo.endDate
            ),
          },
          cardRate: {
            score: this.totalScore,
            grade: this.scoreLevel || "无",
          },
          cardSteps: this.process.statusProcess.map((v) => {
            return {
              id: v.key,
              state: v.nodeType,
              text: this.handleStepsNodeName(v.nodes, v.nodeType),
              tips: this.handleStepsNodeTips(v.nodes, v.nodeType),
            };
          }),
        };
      }
      if (this.source == "xq_qr") {
        this.def_HeaderData = {
          headerText: this.handleHeaderName(),
          headerTag: this.handleHeaderTag(),
        };
        this.def_CardData = {
          cardPhoto: this.basicInfo.employeeName,
          cardDataLineOne: {
            name: this.handleCardName(),
            // phone: this.basicInfo.mobile ? this.basicInfo.mobile.substr(0, 3) + "****" + this.basicInfo.mobile.substr(7) : this.basicInfo.mobile,
            phone: this.basicInfo.mobile,
            tag: khlxType[this.basicInfo.type],
          },
          cardDataLineTwo: {
            label: isGr ? "" : "考核关联人员:",
            value: isGr
              ? `${this.basicInfo.subsidiaryName} ${this.basicInfo.deptName}`
              : this.handleExamineeRelations(this.basicInfo.examineeRelations),
          },
          cardDataLineThree: {
            label: "考核计划名称:",
            value: this.basicInfo.name,
          },
          cardDataLineFour: {
            label: "考核周期:",
            value: date2Str(
              this.basicInfo.period,
              this.basicInfo.startDate,
              this.basicInfo.endDate
            ),
          },
          cardRate: {
            score: this.totalScore,
            grade: this.scoreLevel || "无",
          },
          cardSteps: this.process.statusProcess.map((v) => {
            return {
              id: v.key,
              state: v.nodeType,
              text: this.handleStepsNodeName(v.nodes, v.nodeType),
              tips: this.handleStepsNodeTips(v.nodes, v.nodeType),
            };
          }),
        };
      }

      this.def_StartInfo = this.process.startInfo; //考核对象各个阶段启动数据
      this.def_TableData = this.indicatorList; //考核指标信息
      this.def_CommentData = JSON.parse(
        JSON.stringify(this.process.commentProcess)
      ); //总评数据
      this.def_CommentData.forEach((v, i) => {
        //可编剧列 置顶
        let item = this.process.commentProcess[i];
        if (v.isCanEdit === true) {
          this.def_CommentData.splice(i, 1);
          this.def_CommentData.unshift(item);
          return;
        }
      }); //总评数据
      this.def_ScoreProcessData = this.process.scoreProcess; //评分流程
      this.def_ConfirmProcessData = this.process.confirmProcess; //确认流程
      this.def_ApproveProcessData = this.process.approveProcess; //审核流程
      this.def_DataMarkersData = this.process.dataMarkers; //审核流程
      this.def_ChangeProcessData = JSON.parse(
        JSON.stringify(this.process.changeProcess)
      );
      this.def_ChangeProcessData.map((v) => {
        v.def_isshow = false;
      }); //考核指标修正记录

      this.handleAddDataMarkers(); //拼接录入实际完成值流程
      this.handleAddStartInfo(); //拼接考核确认流程
    },
    //拼接考核确认流程
    handleAddStartInfo() {
      if (Object.keys(this.def_StartInfo).length !== 0) {
        if (this.def_StartInfo["confirmStarterId"] !== 0) {
          this.def_ConfirmProcessData.unshift({
            det_nodeProcessors: true,
            nodeType: 3,
            nodeProcessors: [
              {
                processorName: this.def_StartInfo["confirmStarterName"],
                processorText: "发起了考核确认",
                status: 3,
                time: this.$dayjs(
                  this.def_StartInfo["confirmStartTime"]
                ).format("YYYY-MM-DD HH:mm"),
              },
            ],
          });
        }
        if (this.def_StartInfo["scoreStarterId"] !== 0) {
          this.def_ScoreProcessData.unshift({
            det_nodeProcessors: true,
            nodeType: 3,
            nodeProcessors: [
              {
                processorName: this.def_StartInfo["scoreStarterName"],
                processorText: "启动了考核",
                status: 3,
                time: this.$dayjs(this.def_StartInfo["scoreStartTime"]).format(
                  "YYYY-MM-DD HH:mm"
                ),
              },
            ],
          });
        }
        if (this.def_StartInfo["approveStarterId"] !== 0) {
          this.def_ApproveProcessData.unshift({
            det_nodeProcessors: true,
            nodeType: 3,
            nodeProcessors: [
              {
                processorName: this.def_StartInfo["approveStarterName"],
                processorText: "发放考核结果",
                status: 3,
                time: this.$dayjs(
                  this.def_StartInfo["approveStartTime"]
                ).format("YYYY-MM-DD HH:mm"),
              },
            ],
          });
        }
      }
    },
    handleAddDataMarkers() {
      if (Object.keys(this.def_DataMarkersData).length !== 0) {
        let arr = [],
          boo = false;
        this.def_DataMarkersData.map((v) => {
          arr.push({
            processorName: v["dataMarkerName"],
            processorText: this.handleLrName(v),
            status: v["operationTime"] ? 3 : 1,
            time: v["operationTime"]
              ? this.$dayjs(v["operationTime"]).format("YYYY-MM-DD HH:mm")
              : "",
          });
          if (!boo) {
            boo = v["operationTime"] ? true : false;
          }
        });
        this.def_ScoreProcessData.unshift({
          det_nodeProcessors: true,
          nodeType: boo ? 3 : 1,
          nodeProcessors: arr,
        });
      }
    },
    handleLrName(v) {
      let text = "";
      if (v["operationTime"]) {
        if (v["replaceUserName"]) {
          text = `已录入实际完成值（${v["replaceUserName"]}代录入）`;
        } else {
          text = `已录入实际完成值`;
        }
      } else {
        text = "未录入实际完成值";
      }
      return text;
    },
    handleHeaderName() {
      const { subsidiaryName, deptName, employeeName } = this.basicInfo;
      switch (this.type) {
        case 1:
          return `${subsidiaryName}考核详情`;
        case 2:
          return `${deptName}考核详情`;
        case 3:
          return `${employeeName}考核详情`;
      }
    },
    handleCardName() {
      const { subsidiaryName, deptName, employeeName } = this.basicInfo;
      switch (this.type) {
        case 1:
          return `${subsidiaryName}`;
        case 2:
          return `${deptName}`;
        case 3:
          return `${employeeName}`;
      }
    },
    handleHeaderTag() {
      return stageStatus[this.basicInfoStageStatus];
      // const { currentStage, state } = this.globalState;
      // switch (currentStage) {
      //   case "def_approveStatus":
      //     return this.def_approveStatus[state];
      //   case "def_scoreStatus":
      //     return this.def_scoreStatus[state];
      //   case "def_confirmStatus":
      //     return this.def_confirmStatus[state];
      // }
    },
    //确定当前登录人全局状态
    handleAdminGlobalState() {},
    //汇总全局状态
    handleGlobalState() {
      const { approveStatus, scoreStatus, confirmStatus } = this.basicInfo;
      let obj = {
        1: "公司考核",
        2: "部门考核",
        3: "个人考核",
      };
      console.log(obj[this.type]);
      console.log(
        `审核状态${approveStatus},评分状态${scoreStatus},确认状态${confirmStatus}`
      );
      if (approveStatus == 2 || approveStatus == 3) {
        this.globalState = {
          currentStage: "def_approveStatus", //当前阶段
          state: approveStatus,
        };
        this.source = "xq_sh";
      } else if (approveStatus == 1) {
        this.handleScoreStatus();
      } else {
        console.log("暂无审核状态");
      }
    },
    handleNowState() {
      const { confirmStatus, scoreStatus, approveStatus } = this.basicInfo;
      console.log(this.def_processorType[this.processorType]);
    },
    //校验评分状态
    handleScoreStatus() {
      const { approveStatus, scoreStatus, confirmStatus } = this.basicInfo;
      if (scoreStatus == 2 || scoreStatus == 3) {
        this.globalState = {
          currentStage: "def_scoreStatus", //当前阶段
          state: scoreStatus,
        };
        this.source = "xq_pf";
      } else if (scoreStatus == 1) {
        this.handleConfirmStatus();
      } else {
        console.log("暂无评分状态");
      }
    },
    //校验确认状态
    handleConfirmStatus() {
      const { approveStatus, scoreStatus, confirmStatus } = this.basicInfo;
      if (confirmStatus == 1 || confirmStatus == 2 || confirmStatus == 3) {
        this.globalState = {
          currentStage: "def_confirmStatus", //当前阶段
          state: confirmStatus,
        };
        this.source = "xq_qr";
      } else {
        console.log("暂无确认状态");
      }
    },
    //card 进度条文案
    handleStepsNodeName(items, nodeType) {
      let objColor = {
        1: "#4F71FF",
        2: "#BBBBBB",
        3: "#555555",
      };
      let arr = [],
        baseObj = {};
      items.map((v) => {
        const { key, name, type } = v;
        if (Reflect.ownKeys(baseObj).includes(key)) {
          baseObj[key].push(v);
        } else {
          baseObj[key] = [v];
        }
      });
      for (const i in baseObj) {
        let def_key = `<span style="color:${objColor[nodeType]}">${i} </sapn>`;
        let def_addTextArr = [];
        baseObj[i].map((v) => {
          const { key, name, type } = v;
          let def_name =
            i && name
              ? `<span style="color:${objColor[nodeType]}">：${name} </sapn>`
              : `<span style="color:${objColor[nodeType]}">${name} </sapn>`;
          let def_type = "";
          if (type === 1) {
            def_type = `<span style="color:#FF9500">${this.handleStepsNodeState(
              type
            )}</sapn>`;
          }
          def_addTextArr.push(def_name + def_type);
        });
        let def_text =
          def_key +
          def_addTextArr.join(
            `<span style="color:${objColor[nodeType]}">、</sapn>`
          );
        arr.push(def_text);
      }
      return arr.join(`<span style="color:${objColor[nodeType]}">，</sapn>`);
    },
    handleStepsNodeTips(items, nodeType) {
      let arr = [],
        baseObj = {};
      items.map((v) => {
        const { key, name, type } = v;
        if (Reflect.ownKeys(baseObj).includes(key)) {
          baseObj[key].push(v);
        } else {
          baseObj[key] = [v];
        }
      });
      for (const i in baseObj) {
        let def_key = `${i} `;
        let def_addText = "";
        let def_addTextArr = [];
        baseObj[i].map((v) => {
          const { key, name, type } = v;
          let def_name = i && name ? `：${name} ` : `${name}`;
          let def_type = "";
          if (type === 1) {
            def_type = `${this.handleStepsNodeState(type)}`;
          }
          def_addTextArr.push(def_name + def_type);
        });
        let def_text = def_key + def_addTextArr.join(`、`);
        arr.push(def_text);
      }
      return arr.join(`、`);
    },
    //进度条状态
    handleStepsNodeState(type) {
      return otherStatus[type];
      // const { currentStage, state } = this.globalState;
      // // console.log(type)
      // switch (currentStage) {
      //   case "def_approveStatus":
      //     return this.def_approveStatusSteps[type];
      //   case "def_scoreStatus":
      //     return this.def_scoreStatusSteps[type];
      //   case "def_confirmStatus":
      //     return this.def_confirmStatusSteps[type];
      // }
    },
    handleFormatter({ prop, data, btnItem }, callback) {
      switch (prop) {
        case "khzbmc":
          callback(
            data["name"] === null || data["name"] === "" ? "--" : data["name"]
          );
          break;
        case "khzblx":
          callback(zblxType[data["type"]] || "--");
          break;
        case "khzbsm":
          callback(
            data["description"] == null || data["description"] === ""
              ? "--"
              : data["description"]
          );
          break;
        case "pjbz":
          callback(
            data["scoreStandard"] == null || data["scoreStandard"] === ""
              ? "--"
              : data["scoreStandard"]
          );
          break;
        case "pfsx":
          callback(
            data["maxScore"] == null || data["maxScore"] === ""
              ? "--"
              : data["maxScore"] + "分"
          );
          break;
        case "khzbqz":
          // callback(data["weight"]==null||data["weight"]==0 ? '--' : data["weight"]);
          callback(
            data["weight"] == null || data["weight"] === ""
              ? "--"
              : data["weight"] + "%"
          );
          break;
        case "mbz":
          callback(
            data["targetValue"] == null
              ? "--"
              : data["targetValue"] + data["dataUnit"]
          );
          break;
        case "sjwcz":
          callback(
            data["realValue"] == null
              ? "--"
              : data["realValue"] + data["dataUnit"]
          );
          break;
        case "pfr":
          callback(this.handleColumnsArrBaseData(data["scoreData"], "pfr"));
          break;
        case "khzbpf":
          callback(this.handleColumnsArrBaseData(data["scoreData"], "khzbpf"));
          break;
        case "khzbpy":
          callback(this.handleColumnsArrBaseData(data["scoreData"], "khzbpy"));
          break;
        case "khzbzf":
          callback(
            data["score"] == null || data["score"] === ""
              ? "--"
              : `${data["score"]}分`
          );
          break;
        case "pffs":
          // callback(pffsScoreType[data["scoreType"]]||'--');
          callback(this.handleTableDataPffs(data));
          break;
        case "khzbpfr":
          // if(data["scoreType"]===2){
          //   callback(`<span style="color:#BBBBBB">系统评分</span>`);
          // }else{
          //   callback(this.handleArrBaseData(data["scoreData"], "employeeName"));
          // }
          callback(this.handleArrBaseData(data["scoreData"], "employeeName"));
          break;
      }
    },
    handleTableDataPffs(data) {
      let text = "";
      if (data.scoreType == 1) {
        return "直接输入";
      }
      if (data.scoreType == 2) {
        text = "公式计算<br>";
        if (data.dataRuleType == 1) {
          text += `按实际完成值来算:<br>`;
        }
        if (data.dataRuleType == 2) {
          text += `按目标达成率计算:<br>`;
        }
        data.dataRuleList.map((v, i) => {
          if (data.dataRuleType == 1) {
            text += `${i + 1}、${v.min}${data.dataUnit}`;
            if (i !== 0) {
              text += `<`;
            }
            if (i == 0) {
              text += `≤`;
            }
            text += `完成值≤${v.max}${data.dataUnit}`;
          }
          if (data.dataRuleType == 2) {
            text += `${i + 1}、${v.min}%`;
            if (i !== 0) {
              text += `<`;
            }
            if (i == 0) {
              text += `≤`;
            }
            text += `目标达成率≤${v.max}%,得分：${v.score}分；`;
          }
          text += `<br>`;
        });
      }
      return text;
    },
    handleColumnsArrBaseData(list, key) {
      // console.log(list)
      if (list == null) {
        return "--";
      }
      let obj = {
        pfr: "employeeName",
        khzbpf: "score",
        khzbpy: "comment",
      };
      let arr = [];
      list.map((v) => {
        if (v[obj[key]] === null || v[obj[key]] === "") {
          arr.push("--");
        } else {
          if (key == "khzbpf") {
            arr.push(`${v[obj[key]]}分`);
          } else {
            arr.push(v[obj[key]]);
          }
        }
      });
      // console.log(arr)
      return arr;
    },
    handleBtnColumn(val, type) {
      console.log(val, type);
      switch (type) {
      }
    },
    handleSearch({ limit, start, page }) {},
    //是否展示卡片头像
    handleIsshowCardPhoto() {
      /**
       * 个人考核展示头像
       * 1:公司考核,2:部门考核,3:个人考核
       */
      return this.type === 3 ? true : false;
    },
    handleExamineeRelations(list) {
      let arr = [];
      list.map((v) => {
        arr.push(v.employeeName);
      });
      return arr.join("，");
    },

    handleBtnList(fun) {
      // console.log(fun);
      switch (fun) {
        case "handleCancel":
          this.$router.go(-1);
          break;
        case "handleConfirmSubmitOther":
          console.log("他人确认");
          this.handleGetMyPlanTodoConfirm();
          break;
        case "handleConfirmSubmitMe":
          console.log("自己确认");
          this.handleGetMyPlanTodoConfirm();
          break;
        case "handleSetScore":
          this.handleGetMyPlanTodoSetScore();
          break;
        case "handleApprove":
          this.handleGetMyPlanTodoApprove();
          break;
      }
    },
    //两种 考核指标明细 显示条件
    handleIsshowKhzbmx(type) {
      /**
       * 显示条件：
       * 1：确认阶段
       * 2：待确认 状态
       * 3：自己确认
       */
      /**
       * 显示条件：
       * 1：确认阶段
       * 2：待确认 状态
       * 3：指定人/上级确认
       */
      if (type == "自己确认") {
        if (this.stageStatus == 2) {
          return [1].includes(this.processorType);
        }
        if (this.stageStatus == 3) {
          return true;
        }
      }
      if (type == "非自己确认") {
        return this.stageStatus == 2 && [2, 3].includes(this.processorType);
      }
    },
    handleGetMyPlanTodoConfirm() {
      const { refIndicator } = this.$refs;
      let obj = {};
      if (refIndicator) {
        const { def_boo, data } = refIndicator.checkFormData();
        if (def_boo) {
          obj = {
            examineePlanId: this.examineePlanId, //考核对象id
            ...data,
          };
        } else {
          return;
        }
      } else {
        obj = {
          examineePlanId: this.examineePlanId, //考核对象id
        };
      }
      console.log(obj);
      getMyPlanTodoConfirm(obj)
        .then((res) => {
          if (res.success) {
            this.$message({ message: " 提交成功", type: "success" });
            this.$router.go(-1);
            // this.handleInit()
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //评分数据校验
    handleTagData() {
      for (let i = 0; i < this.def_submitIndicatorListData.length; i++) {
        let v = this.def_submitIndicatorListData[i];
        if (
          v.isCanEditScore &&
          (v.score == null || v.score == undefined || v.score == "")
        ) {
          this.$message({
            message: `存在未评分的指标，请输入评分`,
            type: "warning",
          });
          return false;
        }
        // if(v.isCanEditScore && v.score.includes('.')){
        //   this.$message({
        //     message: `评分请输入整数`,
        //     type: 'warning'
        //   });
        //   return false;
        // }
        if (v.isCanEditScore && 0 > Number(v.score)) {
          this.$message({
            message: `评分不允许为负数`,
            type: "warning",
          });
          return false;
        }
        if (v.isCanEditScore && v.maxScore < Number(v.score)) {
          this.$message({
            message: `已超过评分上限${v.maxScore}，请重新输入`,
            type: "warning",
          });
          return false;
        }
      }
      return true;
    },
    handleCheckTag({ val, isTag, tagVal }) {
      console.log(val, isTag, tagVal);
      if (isTag && Number(val) > Number(tagVal)) {
        this.$message({
          message: `已超过评分上限${tagVal}，请重新输入`,
          type: "warning",
        });
        return false;
      }
      // if(val.includes('.')){
      //   this.$message({
      //     message: `评分请输入整数`,
      //     type: 'warning'
      //   });
      // }
      if (0 > Number(val)) {
        this.$message({
          message: `评分不允许为负数`,
          type: "warning",
        });
        return false;
      }
    },
    handleGetMyPlanTodoSetScore() {
      let boo = this.handleTagData();
      let list = JSON.parse(JSON.stringify(this.def_submitIndicatorListData));
      if (boo) {
        list.map((v) => {
          delete v.maxScore;
          delete v.isCanEditScore;
        });
      } else {
        return;
      }
      let obj = {
        examineePlanId: this.examineePlanId,
        generalComment: this.def_submitCommentProcessData, //总评评语
        indicatorScoreParam: list,
        updateType: 2, //保存方式：1-暂存；2-提交考核
      };
      console.log(obj);
      getMyPlanTodoSetScore(obj)
        .then((res) => {
          if (res.success) {
            this.$message({ message: "提交成功", type: "success" });
            this.$router.go(-1);
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleGetMyPlanTodoApprove() {
      let obj = {
        examineePlanId: this.examineePlanId,
      };
      getMyPlanTodoApprove(obj)
        .then((res) => {
          if (res.success) {
            this.$message({ message: "提交成功", type: "success" });
            this.$router.go(-1);
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleZbmxCommit(val) {
      console.log(val);
    },
  },
  watch: {
    def_TableData: {
      handler(val) {
        if ([4, 5, 6].includes(this.stageStatus)) {
          this.handleInputIndicatorList();
        }
      },
      deep: true,
    },
    def_CommentData: {
      handler(val) {
        if ([4, 5, 6].includes(this.stageStatus)) {
          this.handleInputCommentData();
        }
      },
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.o-app-frame_layout_content_wrapper {
  overflow-y: scroll;
}
@media screen and (min-width: 1300px) {
  .def_per_section {
    padding: 0 100px;
  }
}
.performance_detail {
  /deep/.el-alert--info.is-light {
    font-size: 14px;
    color: #555555;
    background-color: #f4f7ff;
    border: 1px solid #a4b5ff;
  }
  .jxjg {
    margin-top: 20px;
    margin-left: 13px;
    .jxjg-title {
      font-size: 14px;
      color: #888888;
      letter-spacing: 0;
      text-align: right;
      line-height: 14px;
    }
    .jxjg-score {
      margin-left: 10px;
      font-size: 16px;
      color: #070f29;
      letter-spacing: 0;
      line-height: 14px;
    }
    .jxjg-grade {
      margin-left: 10px;
      font-size: 16px;
      color: #ff9500;
      letter-spacing: 0;
      line-height: 14px;
    }
  }
  .lc {
    .lc-node {
      margin-top: 20px;
      .node-right {
        .node-right-photo {
          margin: 10px 0;
        }
        .def_icon {
          position: absolute;
          bottom: 0px;
          right: -10px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          border: 1px solid #fff;
          background: #fff;
        }
        .right-header {
          display: flex;
          flex-direction: row;
          align-items: center;
          height: 48px;
          .right-header-name {
            margin-left: 16px;
            font-size: 16px;
            color: #070f29;
            letter-spacing: 0;
            line-height: 14px;
          }
          .right-header-text {
            margin-left: 20px;
            font-size: 12px;
            color: #6a6f7f;
            letter-spacing: 0;
            line-height: 14px;
          }
        }
      }
      .slot-header {
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 48px;
        .slot-header-name {
          margin-left: 16px;
          font-size: 16px;
          color: #070f29;
          letter-spacing: 0;
          line-height: 14px;
        }
        .slot-header-tag {
          margin-left: 10px;
          border-radius: 14px;
          padding: 6px 12px;
          background: #f1f1f1;

          font-size: 14px;
          color: #6a6f7f;
          letter-spacing: 0;
          line-height: 14px;
        }
        .slot-header-text {
          margin-left: 20px;
          font-size: 12px;
          color: #6a6f7f;
          letter-spacing: 0;
          line-height: 14px;
        }
      }
    }
  }
  .detail-table {
    margin-top: 20px;
  }
  .detail-evaluate {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .evaluate-score {
    margin-left: 16px;
    margin-bottom: 20px;
    .score-one {
      font-size: 14px;
      color: #888888;
      letter-spacing: 0;
      text-align: right;
      line-height: 14px;
    }
    .score-two,
    .score-three {
      margin-left: 10px;
      font-size: 14px;
      color: #555555;
      letter-spacing: 0;
      line-height: 14px;
    }
  }
  .evaluate-comment {
    display: flex;
    flex-direction: row;

    .comment-one {
      min-width: 42px;
      margin-left: 4px;
      .one-required {
        font-size: 14px;
        color: #ff6051;
        letter-spacing: 0;
        line-height: 14px;
      }
      .one-text {
        margin-left: 6px;
        font-size: 14px;
        color: #888888;
        letter-spacing: 0;
        text-align: right;
        line-height: 14px;
      }
    }
    .comment-two {
      margin-left: 10px;
      flex-grow: 1;
    }
  }
  .detail-btn {
    position: sticky;
    bottom: 0;
    z-index: 1000;
    background: #fff;
    .detail-btn-items {
      display: flex;
      justify-content: center;
      padding: 0 0 24px 0;
    }
  }
}
</style>
