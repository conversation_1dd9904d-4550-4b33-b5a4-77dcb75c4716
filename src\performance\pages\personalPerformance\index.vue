<template>
  <div class="my-performance def_per_height" v-loading="loading">
    <def-Header headerText="我的绩效" />
    <section
      id="def_cardOfMine"
      class="def_per_section def_per_section-top"
      v-if="isShowCard"
      ref="def_cardOfMine"
    >
      <def-my-card
        :isShowPhoto="def_CardData.isShowPhoto"
        :cardPhoto="def_CardData.cardPhoto"
        :lineOne="def_CardData.cardDataLineOne"
        :lineTwo="def_CardData.cardDataLineTwo"
        :lineThree="def_CardData.cardDataLineThree"
        :isShowRate="false"
        :rate="def_CardData.cardRate"
        :isShowStep="true"
        :steps="def_CardData.cardSteps"
        @cardChange="handleCardChange"
      />
    </section>
    <section class="def_per_section def_per_section-top">
      <def-title text="我的全部考核">
        <section class="def_per_leftRight def_per_alignItems">
          <section style="font-size: 14px; color: #555555; z-index: 9">
            待考核确认表<span
              style="font-size: 14px; color: #ff9b0e; z-index: 9"
            >
              {{ count }} </span
            >个
          </section>
          <el-button
            style="margin-left: 20px; z-index: 9"
            type="primary"
            @click="handleWaitList"
            >查看待确认考核表</el-button
          >
        </section>
      </def-title>

      <section>
        <el-tabs
          class="section-tabs"
          v-model="activeName"
          @tab-click="handleClickTab"
        >
          <template v-for="(item, index) in tabs">
            <el-tab-pane
              :key="item.label"
              :label="item.label"
              :name="item.name"
              :lazy="true"
            >
              <def-etable
                ref="def_etable"
                v-loading="loadingTable"
                :tableHeader="tableHeader"
                :tableData="tableData"
                @formatter="handleFormatter"
                @btnColumn="handleBtnColumn"
                @search="handleSearch"
                :total="total"
                :isShowIndex="true"
                :isHidePage="false"
              />
            </el-tab-pane>
          </template>
        </el-tabs>
      </section>
    </section>
  </div>
</template>

<script>
import {
  defHeader,
  defCard,
  defTable,
  defNode,
  defTitle,
  defEtable,
  defMyCard,
} from "./components";
import {
  getMyPlanList,
  getMyPlanLast,
  getMyPlanTodoConfirmCount,
} from "performance/store/api.js";
import {
  khlxType,
  khzqPeriodType,
  jdtType,
  khjhStatus,
} from "performance/utils/enum.js";
import { date2Str, havePrivilege } from "performance/utils/util.js";
import { mapState } from "vuex";
import store from "performance/store";
export default {
  name: "my-performance",
  components: {
    defHeader,
    defCard,
    defTable,
    defNode,
    defTitle,
    defEtable,
    defMyCard,
  },
  data() {
    return {
      loading: true,
      loadingTable: false,
      tabs: [
        { label: "个人考核", name: "grkh" },
        { label: "部门考核", name: "bmkh" },
        { label: "公司考核", name: "gskh" },
      ],
      activeName: "grkh",
      tableHeight: "530px",
      tableHeaderBase: [
        { label: "考核对象", prop: "khdx" },
        { label: "关联人员", prop: "glry" },
        { label: "考核类型", prop: "khlx" },
        { label: "我所在公司", prop: "wszgs" },
        { label: "考核计划", prop: "khjh" },
        { label: "考核周期", prop: "khzq" },
        { label: "当前阶段", prop: "dqjd" },
        { label: "待处理人", prop: "dclr" },
        { label: "绩效结果", prop: "jxjg" },
        {
          prop: "def_cz",
          label: "操作",
          width: "100px",
          btn: [
            {
              prop: "def_xq",
              label: "详情",
              type: "def_btn",
              fun: "handleDetail",
            },
          ],
        },
      ],
      tableHeader: [
        { label: "考核对象", prop: "khdx" },
        // { label: "关联人员", prop: "glry" },
        { label: "考核类型", prop: "khlx" },
        { label: "我所在公司", prop: "wszgs" },
        { label: "考核计划", prop: "khjh" },
        { label: "考核周期", prop: "khzq" },
        { label: "当前阶段", prop: "dqjd" },
        { label: "待处理人", prop: "dclr" },
        { label: "绩效结果", prop: "jxjg" },
        {
          prop: "def_cz",
          label: "操作",
          width: "100px",
          btn: [
            {
              prop: "def_xq",
              label: "详情",
              type: "def_btn",
              fun: "handleDetail",
            },
          ],
        },
      ],
      tableData: [],
      total: null,
      limit: 10,
      start: 0,
      page: 1,

      basicInfo: {},
      employeeName: "",
      process: {},
      type: 3,
      lastType: null,

      def_CardData: {},
      isShowCard: false,
      count: 0, //我的待确认考核表数量
      lastCardHeight: 0,
    };
  },
  // activated(){
  //   this.handleInit()
  // },
  computed: {
    ...mapState({
      searchFormMine: (state) => store.state.searchFormMine,
    }),
  },
  beforeRouteEnter: (to, from, next) => {
    if (
      !["/my-performance/detail", "/my-performance/waitConfirmList"].includes(
        from.path
      )
    ) {
      store.commit("SET_SEARCHFORMMINE", {
        currentPage: 1,
        pageSize: 10,
        type: 3,

        activeName: "grkh",
      });
    }
    next();
  },
  created() {
    const { currentPage, pageSize, type, activeName } = this.searchFormMine;
    this.page = currentPage;
    this.limit = pageSize;
    this.type = type;
    this.activeName = activeName;
  },
  mounted() {
    this.handleInit();
  },
  methods: {
    handleInit() {
      this.handleGetMyPlanLast();
      this.handleGetMyPlanTodoConfirmCount(); //我的待确认考核表数量
      this.handleGetMyPlanList();
    },
    handleTableResize() {
      window.onresize = () => {
        return (() => {
          this.tableHeight =
            document.body.clientHeight - this.lastCardHeight - 350 + "px";
        })();
      };
    },
    handleCardChange(isShow, height) {
      let _this = this;
      console.log(isShow, height);
      this.$nextTick(() => {});
    },
    async handleGetMyPlanList() {
      let obj = {
        currentPage: this.page,
        pageSize: this.limit,
        type: this.type,
      };
      const { data } = await getMyPlanList(obj);
      this.tableData = data.records;
      this.total = data.total;

      this.loading = false;
      this.loadingTable = false;
    },
    async handleGetMyPlanTodoConfirmCount() {
      const { data } = await getMyPlanTodoConfirmCount();
      this.count = data.count;
    },
    async handleGetMyPlanLast() {
      const { data } = await getMyPlanLast();
      const { basicInfo, process, employeeName } = data;
      this.basicInfo = basicInfo;
      this.process = process;
      this.employeeName = employeeName;
      if (!basicInfo) {
        this.isShowCard = false;
      } else {
        this.handleBaseData();
      }
    },
    handleClickTab(tab, event) {
      this.loadingTable = true;
      this.page = 1;
      const { name } = tab;
      if (name == "gskh") {
        this.type = 1;
      }
      if (name == "bmkh") {
        this.type = 2;
      }
      if (name == "grkh") {
        this.type = 3;
      }
    },
    handleBaseData() {
      let isGr = this.basicInfo.type == 3 ? true : false;
      this.def_CardData = {
        isShowPhoto: true,
        cardPhoto: this.employeeName,
        cardDataLineOne: {
          // name:this.handleCardName(),
          name: this.basicInfo.name, //所有类型都返回考核计划名称
          // phone:this.basicInfo.mobile ? this.basicInfo.mobile.substr(0, 3) + '****' + this.basicInfo.mobile.substr(7) : this.basicInfo.mobile,
          tag: khlxType[this.basicInfo.type],
          myTag: khjhStatus[this.basicInfo.status],
        },
        cardDataLineTwo: {
          label: "",
        },
        cardDataLineThree: {
          label: "考核周期:",
          value: date2Str(
            this.basicInfo.period,
            this.basicInfo.startDate,
            this.basicInfo.endDate
          ),
        },
        cardRate: {
          score: this.totalScore,
          grade: this.scoreLevel || "无",
        },
        cardSteps: this.process.statusProcess.map((v) => {
          return {
            id: v.key,
            state: v.nodeType,
            text: this.handleStepsNodeName(v.nodes, v.nodeType),
          };
        }),
      };
      this.lastType = this.basicInfo.type;
      this.isShowCard = true;
      this.$nextTick(() => {});
    },
    handleCardName() {
      const { subsidiaryName, deptName, employeeName } = this.basicInfo;
      switch (this.type) {
        case 1:
          return `${subsidiaryName}`;
        case 2:
          return `${deptName}`;
        case 3:
          return `${employeeName}`;
      }
    },
    handleStepsNodeName(items, nodeType) {
      let val = "";
      let arr = [];
      items.map((v) => {
        const { key, name, type } = v;
        if (!arr.includes(name)) {
          arr.push(name);
        }
      });
      return val + arr.join("，");
    },
    handleFormatter({ prop, data, btnItem }, callback) {
      let _this = this;
      if (prop == "def_cz") {
        switch (btnItem) {
          case "def_xq":
            callback(havePrivilege("kpi.performance.myPlan.detail"));
            break;
        }
      } else {
        switch (prop) {
          case "khdx":
            callback(data["examineeName"]);
            break;
          case "glry":
            callback(this.handleGlryData(data["relationName"]));
            break;
          case "khlx":
            callback(khlxType[data["type"]]);
            break;
          case "wszgs":
            callback(data["subsidiaryName"]);
            break;
          case "khjh":
            callback(data["planName"]);
            break;
          case "khzq":
            callback(
              date2Str(data["period"], data["startDate"], data["endDate"])
            );
            break;
          case "dqjd":
            callback(data["currentStage"]);
            break;
          case "dclr":
            callback(data["handleName"] || "--");
            break;
          case "jxjg":
            callback(this.handleJxjg(data));
            break;
        }
      }
    },
    handleBtnColumn(val, type) {
      switch (type) {
        case "handleDetail":
          this.handleSetVuex();
          this.$router.push({
            path: "/my-performance/detail",
            query: { examineePlanId: val.examineePlanId },
          });
          break;
      }
    },
    handleSearch({ limit, start, page }) {
      this.page = page;
      this.limit = limit;
      this.start = start;
      this.handleGetMyPlanList();
    },
    handleWaitList() {
      this.handleSetVuex();
      this.$router.push({ path: "/my-performance/waitConfirmList", query: {} });
    },
    handleSetVuex() {
      let obj = {
        currentPage: this.page,
        pageSize: this.limit,
        type: this.type,

        activeName: this.activeName,
      };
      store.commit("SET_SEARCHFORMMINE", obj);
    },
    //关联人员
    handleGlryData(list) {
      if (Object.prototype.toString.call(list) == "[object Array]") {
        return list.join("，");
      } else {
        return list;
      }
    },
    //确定表头信息
    hanldeFilterTableHeader(val) {
      this.tableHeader = this.tableHeaderBase.filter((v) => {
        if ([1, 2].includes(val)) {
          return [
            "khdx",
            "glry",
            "khlx",
            "wszgs",
            "khjh",
            "khzq",
            "dqjd",
            "jxjg",
            "dclr",
            "def_cz",
          ].includes(v.prop);
        }
        if ([3].includes(val)) {
          return [
            "khdx",
            "khlx",
            "wszgs",
            "khjh",
            "khzq",
            "dqjd",
            "jxjg",
            "dclr",
            "def_cz",
          ].includes(v.prop);
        }
      });
      this.$nextTick(() => {
        this.$refs.def_etable.$refs.Etable.doLayout();
      });
    },
    handleJxjg(data) {
      const { score, scoreLevel } = data;
      if (score == null) {
        return "--";
      } else {
        return `总评分：${score}分<br>绩效等级：${scoreLevel}`;
      }
    },
  },
  watch: {
    type: {
      handler(val) {
        this.handleGetMyPlanList();
        this.hanldeFilterTableHeader(val);
      },
      deep: true,
    },
    lastCardHeight: {
      handler(val) {
        // this.tableHeight = document.body.clientHeight - val - 350 +'px';
      },
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.my-performance {
  .section-tabs {
    /deep/.el-tabs__nav-wrap::after {
      display: none;
    }
  }
}
</style>
