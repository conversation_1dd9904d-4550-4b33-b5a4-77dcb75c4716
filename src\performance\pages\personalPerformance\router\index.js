import index from '../index';
import detail from '../detail';
import compDetail from '../compDetail';
import waitMyPer from '../waitMyPer';
import waitConfirmList from '../components/pagesComp/WaitConfirmList';
import finishState from '../components/pagesComp/FinishState';
import tabsForWaitItem from '../components/pagesComp/TabsForWaitItem';

export default [
  {
    path: '/my-performance', //我的考核
    component: index,
    meta: {
      businessCode: 'kpi.performance.myPlan',
      icon: 'iconjixiaokaohe',
      // keepAlive:true,//缓存
    },
  },
  {
    path: '/wait-myper', //待我考核
    component: waitMyPer,
    meta: {
      businessCode: 'kpi.performance.myTodo',
      icon: 'iconjixiaokaohe',
      // keepAlive:true,//缓存
    },
  },
  {
    path: '/wait-myper/done-list', //已完成list
    component: tabsForWaitItem,
  },
  {
    path: '/wait-myper/finish-State', //考核计划实际完成数据页
    component: finishState,
  },
  {
    path: '/my-performance/detail', //详情
    meta: {
      menu: false
    },
    component: detail,
  },
  {
    path: '/my-performance/comp-detail', //个人绩效详情
    component: compDetail,
  },
  {
    path: '/my-performance/waitConfirmList', //待确认考核表
    component: waitConfirmList,
  },
];
