<template>
  <div class="wait-myper def_per_height">
    <def-header headerText="待我考核" />
    <section class="def_per_section" style="margin-top: 5px">
      <el-tabs
        class="section-tabs"
        v-model="activeName"
        @tab-click="handleClickTab"
      >
        <template v-for="(item, index) in tabs">
          <el-tab-pane
            :key="item.label"
            :label="handleTabName(item.label, item.limitId)"
            :name="item.name"
            :lazy="true"
          >
            <tabs-for-wait
              v-if="item.name == activeName"
              :type="activeName"
            ></tabs-for-wait>
          </el-tab-pane>
        </template>
      </el-tabs>
    </section>
  </div>
</template>

<script>
import {
  defHeader,
  defCard,
  defNode,
  defTitle,
  defTable,
  defPhoto,
} from "./components";
import TabsForWait from "./components/pagesComp/TabsForWait";
import { getMyPlanTodoStatistics } from "performance/store/api.js";
import { khlxType } from "performance/utils/enum.js";
import { mapState } from "vuex";
import store from "performance/store";
export default {
  name: "wait-myper",
  components: {
    defHeader,
    TabsForWait,
  },
  data() {
    return {
      tabs: [
        { label: "待我确认", name: "dwqr", limitId: "needConfirmNum" },
        { label: "待我评分", name: "dwpf", limitId: "needScoreNum" },
        { label: "待我审核", name: "dwsh", limitId: "needApproveNum" },
        {
          label: "待我录入实际完成值",
          name: "dwlr",
          limitId: "needEntryDataNum",
        },
      ],
      activeName: "dwqr",
      limit: {
        needConfirmNum: 0,
        needScoreNum: 0,
        needApproveNum: 0,
        needEntryDataNum: 0,
      },
    };
  },
  // activated(){
  //   this.handleInit()
  // },
  computed: {
    ...mapState({
      searchFormWait: (state) => store.state.searchFormWait,
    }),
  },
  beforeRouteEnter: (to, from, next) => {
    if (
      ![
        "/my-performance/detail",
        "/wait-myper/done-list",
        "/wait-myper/finish-State",
      ].includes(from.path)
    ) {
      store.commit("SET_SEARCHFORMWAIT", {
        activeName: "dwqr",
      });
    }
    next();
  },
  created() {
    const { activeName } = this.searchFormWait;
    this.activeName = activeName;
  },
  mounted() {
    this.handleInit();
  },
  methods: {
    handleInit() {
      this.handleGetMyPlanTodoStatistics();
    },
    handleTabName(name, id) {
      return `${name} ${this.limit[id]}`;
    },
    async handleGetMyPlanTodoStatistics() {
      const { data } = await getMyPlanTodoStatistics();
      this.limit = data;
    },
    handleClickTab(tab, event) {
      this.handleSetVuex();
      this.handleInit();
    },
    handleSetVuex() {
      let obj = {
        activeName: this.activeName,
      };
      store.commit("SET_SEARCHFORMWAIT", obj);
    },
  },
};
</script>
<style lang="scss" scoped>
.wait-myper {
  .section-tabs {
    /deep/.el-tabs__nav-wrap::after {
      display: none;
    }
  }
}
</style>
