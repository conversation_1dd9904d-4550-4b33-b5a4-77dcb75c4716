<template>
  <div class="indicator">
    <header class="per-header">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>{{
            $route.query.name ? $route.query.name : "新增岗位模板"
          }}</span>
        </el-col>
      </el-row>
    </header>
    <div class="main">
      <el-form
        :model="formData"
        ref="formData"
        label-width="110px"
        class="add-class"
        :rules="rules"
      >
        <el-form-item prop="name" label="岗位模板名称">
          <el-input
            v-model.trim="formData.name"
            ref="input"
            maxlength="15"
            show-word-limit
            style="width:260px"
            placeholder="不超过15字"
            @input="handleInput(formData.name, 'input', 15)"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="main-ckass">
        <def-title text="考核指标明细" />
        <div class="indicator-table">
          <old-table
            :data="tableData"
            :maxHeight="tableHeight"
            :headerData="headerData"
            :isShowOperation="isShowOperation"
            :operaOptions="operaOptions"
            @operaClick="handleOperaClick"
          >
            <template slot="descriptionStr" slot-scope="scope">
              <el-tooltip
                placement="top"
                :disabled="scope.msg.row.descriptionStr.length < 50"
              >
                <p slot="content" >
                  {{ scope.msg.row.descriptionStr }}
                </p>
                <p class="text">
                  {{ scope.msg.row.descriptionStr }}
                </p>
              </el-tooltip>
            </template>

            <template slot="scoreStandard" slot-scope="scope">
              <el-tooltip
                placement="top"
                :disabled="scope.msg.row.scoreStandard.length < 50"
              >
                <p slot="content">
                  {{ scope.msg.row.scoreStandard }}
                </p>
                <p class="text">
                  {{ scope.msg.row.scoreStandard }}
                </p>
              </el-tooltip>
            </template>
            <template slot="maxScore-header">
              评分上限
              <el-tooltip
                effect="dark"
                content="加分项：加分上限；减分项：减分上限"
                placement="top"
              >
                <i class="iconfont-per icon-help" />
              </el-tooltip>
            </template>
            <template slot="weight-header">
              考核指标权重
              <el-tooltip
                effect="dark"
                content="加分项、减分项没有考核指标权重，不能设置"
                placement="top"
              >
                <i class="iconfont-per icon-help" />
              </el-tooltip>
            </template>
            <!-- <template slot="targetList-header">
          目标值
          <el-tooltip
            effect="dark"
            content="仅“定量考核指标”可设置"
            placement="top"
          >
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template> -->
            <template slot="scoreType" slot-scope="scope">
              <div v-if="scope.msg.row.scoreType == 1">
                直接输入
              </div>
              <div v-if="scope.msg.row.scoreType == 2">
                <el-tooltip placement="top">
                  <p style="text-align:left" class="text">
                    <span>公式计算</span><br />
                    <span v-if="scope.msg.row.dataRuleType == 1"
                      >按实际完成值来算:</span
                    >
                    <span v-if="scope.msg.row.dataRuleType == 2"
                      >按目标达成率计算:</span
                    ><br />
                    <span
                      v-for="(dataRuleItem, index) in scope.msg.row
                        .dataRuleList"
                      :key="index"
                    >
                      <span v-if="scope.msg.row.dataRuleType == 1"
                        >{{ index + 1 }}、{{
                          dataRuleItem.min + scope.msg.row.dataUnit
                        }}<span v-if="index !== 0">＜</span
                        ><span v-if="index == 0">≤</span>完成值≤{{
                          dataRuleItem.max + scope.msg.row.dataUnit
                        }}</span
                      ><span v-if="scope.msg.row.dataRuleType == 2"
                        >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                          v-if="index !== 0"
                          >＜</span
                        ><span v-if="index == 0">≤</span>目标达成率≤{{
                          dataRuleItem.max
                        }}%</span
                      >,得分：{{ dataRuleItem.score }}分;<br />
                    </span>
                  </p>

                  <p slot="content">
                    <span>公式计算</span><br />
                    <span v-if="scope.msg.row.dataRuleType == 1"
                      >按实际完成值来算:</span
                    >
                    <span v-if="scope.msg.row.dataRuleType == 2"
                      >按目标达成率计算:</span
                    ><br />
                    <span
                      v-for="(dataRuleItem, index) in scope.msg.row
                        .dataRuleList"
                      :key="index"
                    >
                      <span v-if="scope.msg.row.dataRuleType == 1"
                        >{{ index + 1 }}、{{
                          dataRuleItem.min + scope.msg.row.dataUnit
                        }}<span v-if="index !== 0">＜</span
                        ><span v-if="index == 0">≤</span>完成值≤{{
                          dataRuleItem.max + scope.msg.row.dataUnit
                        }}</span
                      ><span v-if="scope.msg.row.dataRuleType == 2"
                        >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                          v-if="index !== 0"
                          >＜</span
                        ><span v-if="index == 0">≤</span>目标达成率≤{{
                          dataRuleItem.max
                        }}%</span
                      >,得分：{{ dataRuleItem.score }}分;<br />
                    </span>
                  </p>
                </el-tooltip>
              </div>
            </template>

            <template slot="scorerName" slot-scope="scope">
              <p v-if="scope.msg.row.scoreType == 2" style="color:#ccc">
                系统评分
              </p>
              <p v-if="scope.msg.row.scoreType == 1">
                {{
                  scope.msg.row.scorerName
                    ? scope.msg.row.scorerName
                    : scope.msg.row.scorerDataName || "--"
                }}
              </p>
            </template>
          </old-table>

          <div class="indicator-footer">
            <el-button type="text" @click="handleAdd(1)">
              <i class="iconfont-per icon-add1"></i>
              新增考核指标</el-button
            >
            <el-button class="text_btn" type="text" @click="handleSelect(null)">
              <i class="iconfont-per icon-ku1"></i>
              从考核指标库选择</el-button
            >
            <div
              class="total"
              :class="{ warning: total != 100 }"
              v-if="total !== ''"
            >
              {{ total }}%
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mian-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="checkFormData('formData')"
        >保存</el-button
      >
    </div>
    <el-dialog :visible.sync="dialogVisible1" width="522px" :show-close="false">
      <div slot="title">
        <div class="z-header">
          <span class="title">删除指标 </span>
          <i class="iconfont-per icon-close1" @click="closeDelete"></i>
        </div>
      </div>
      <div>
        <i class="iconfont-per icon-jingshi-qiangtishi1 delete-icon"></i>
        <span style="color:#555555"
          >确定删除该指标？</span
        >
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDelete">取 消</el-button>
        <el-button type="primary" @click="handleDeleteNode(deleteId,deleteIndex)"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <Add
      ref="addItem"
      :type="currentType"
      @getItem="getItem"
      :editInfo="editInfo"
      @clear="clear"
    ></Add>
    <Select
      ref="selectItem"
      @save="getSelect"
      :type="currentType"
      :selectedList="tableData"
    ></Select>
  </div>
</template>

<script>
import { defCard, defTitle } from "../personalPerformance/components";
import Add from "../performanceManage/components/pageComps/Add.vue";
import Select from "../performanceManage/components/pageComps/Select";
import { sumCount, arrayUnique } from "performance/utils/util";
import dd from "performance/utils/dataDictionary.js";
import {
  gePositionDetail,
  gePositionCheckName,
  gePositionUpdate,
  gePositionAdd
} from "performance/store/api.js";

export default {
  components: {
    Add,
    Select,
    defCard,
    defTitle
  },
  data() {
    return {
      dialogVisible1:false,
      deleteId:'',
      deleteIndex:'',
      currentType: null,
      planBaseInfo: {},
      currnetIndex: null,
      editInfo: {},
      total: "",
      name: "",
      levelType: dd.levelType,
      options: {
        1: { name: "定量考核指标" },
        2: { name: "定性考核指标" },
        3: { name: "加分项" },
        4: { name: "减分项" }
      },
      formData: {
        name: "",
        addIndicatorList: [], //新增的指标列表
        removedIndicatorList: [], //删除的指标标识sign列表
        updatedIndicatorList: [] //编辑的指标列表
      },
      tableData: [],
      tableHeight: document.body.clientHeight - 400 + "px",
      headerData: [
        { title: "考核指标名称", label: "name", align: "left",fixed: "left",minWidth: "120px",},
        { title: "考核指标类型", label: "typeStr", align: "left",minWidth: "120px", },
        {
          title: "考核指标说明",
          label: "descriptionStr",
          slot: "descriptionStr",
          align: "left",
          minWidth: "120px",
        },
        {
          title: "评价标准",
          label: "scoreStandard",
          slot: "scoreStandard",
          align: "left",
          minWidth: "120px",
        },
        {
          title: "评分上限",
          label: "maxScore",
          slotHeader: "maxScore-header",
          align: "right",
          minWidth: "120px",
        },
        {
          title: "考核指标权重",
          label: "weightStr",
          slotHeader: "weight-header",
          align: "right",
          minWidth: "120px",
        },
        // {
        //   title: "目标值",
        //   label: "targetList",
        //   slotHeader: "targetList-header"
        // },
        {
          title: "评分方式",
          label: "scoreType",
          align: "left",
          slot: "scoreType",
          minWidth: "120px",
        },
        {
          title: "考核指标评分人",
          label: "scorerName",
          slot: "scorerName",
          align: "left",
          minWidth: "140px",
        }
      ],

      isShowOperation: true, //是否显示操作列
      operaOptions: {
        title: "操作", //名称
        align: "left",
        fixed: "right",
        width: 120, //宽度
        buttonList: [
          //按钮列表
          { title: "编辑" },
          { title: "删除" }
        ]
      },
      rules: {
        name: {
          required: true,
          validator: (rule, value, callback) => {
            if (!value) {
              callback("请输入岗位模板名称");
            } else {
              if (value !== this.name) {
                gePositionCheckName({ name: value }).then(res => {
                  console.log(res.data);
                  if (res.data.nameExist) {
                    callback("岗位模板名称唯一");
                  } else {
                    callback();
                  }
                });
              } else {
                callback();
              }
            }
          },
          trigger: ["blur"]
        }
      }
    };
  },
  watch: {
    tableData: {
      immediate: true,
      handler: function(val) {
        console.log(this.total, "this.total ");
        if (val.length) {
          const list = val.filter(it => it.type == 1 || it.type == 2);
          console.log(list);
          this.total =
            list.length > 0 ? sumCount(list.map(it => Number(it.weight))) : 0;
          console.log("this.total", this.total);
        }
      },
      deep: true
    }
  },
  created() {
    if (this.$route.query.id) {
      this.getPlanDetail();
    }
  },
  mounted() {},
  methods: {
    async getPlanDetail() {
      const res = await gePositionDetail({
        id: this.$route.query.id
      });
      console.log("getPlanDetail", res);
      setTimeout(() => {
        this.loading = false;
      }, 300);

      if (res.success) {
        this.formData.id = res.data.id;
        this.formData.name = res.data.name;
        this.tableData = this.handleList(res.data.indicatorList);
        this.name = JSON.parse(JSON.stringify(res.data.name));
      } else {
        this.$message.error(res.msg);
      }
    },

    handleList(arr) {
      if (arr.length == 0) return [];
      arr.map(item => {
        item.typeStr = this.options[item.type].name;
        item.weightStr =
          item.type == 1 || item.type == 2 ? item.weight + "%" : "--";
        item.descriptionStr = item.description || "--";
        if (
          item.scorerData &&
          item.scorerData.length > 0 &&
          item.scorerName == ""
        ) {
          item.scorerData.map(it => {
            switch (it.processorType) {
              case 1:
                it.name = "被考核人";
                break;
              case 2:
                it.name = this.levelType[it.superiorLevel];
                break;
              case 3:
                it.name = it.processorName;
                break;
            }
            return it;
          });
          item.scorerDataName = item.scorerData.map(it => it.name).join("，");
        }

        return item;
      });

      return arr;
    },
    //新增指标
    handleAdd(val) {
      this.editInfo = {};
      this.currentType = Number(val);
      this.currnetIndex = null;
      this.$refs.addItem.openDialog();
    },
    handleSelect(val) {
      this.currentType = Number(val);
      this.$refs.selectItem.openDialog();
    },
    //获取新增/编辑考核指标
    getItem(val) {
      if (this.currnetIndex !== null) {
        this.tableData.splice(this.currnetIndex, 1, val);
        if (val.id) {
          this.formData.updatedIndicatorList.push(val);
        }

        this.currnetIndex = null;
      } else {
        this.tableData.push(val);
      }
      this.tableData = this.handleList(this.tableData);
      this.editInfo = {};
    },
    getSelect(val) {
      console.log("valvalvalvalval", val);
      const list = val;
      list.forEach(item => {
        //dataRuleType: 1; //计算规则:1-按实际完成值计算;2-按目标达成率计算
        // if (item.type == 1 && item.dataRuleType == 2) {
        //   this.$message.warning("请完善考核指标");
        // }
        if (item.dataRule) {
          item.dataRuleList = JSON.parse(JSON.stringify(item.dataRule));
          delete item.dataRule;
        }
        delete item.id;
        this.tableData.push(item);
      });
      console.log(this.tableData);
      this.tableData = this.handleList(this.tableData);
    },
    clear() {
      console.log("clear");
      this.editInfo = {};
      this.currnetIndex = null;
    },
    closeDelete() {
      this.dialogVisible1 = false;
      this.deleteId = "";
      this.deleteIndex=''
    },
    handleDeleteNode(data,index) {
      console.log(data);
      this.tableData.splice(index, 1);
      if (data) {
        this.formData.removedIndicatorList.push(data);
      }
      this.closeDelete();
    },
    handleOperaClick(btn, row, { $index }) {
      console.log(btn, "调试:", row, $index);
      if (btn == "删除") {
        this.tableData.splice($index, 1);
        if (row.sign) {
          this.formData.removedIndicatorList.push(row.sign);
        }
      }
      if (btn == "编辑") {
        this.editInfo = row;
        this.currnetIndex = $index;
        this.$refs.addItem.openDialog();
      }
    },
    handleClose() {
      this.formData = {
        name: "",
        addIndicatorList: [], //新增的指标列表
        removedIndicatorList: [], //删除的指标标识sign列表
        updatedIndicatorList: [] //编辑的指标列表
      };
      this.$router.go(-1);
    },
    //是否有重复指标名
    isRepeat(arr) {
      var hash = {};
      for (var i in arr) {
        if (hash[arr[i]])
          //hash 哈希
          return true;
        hash[arr[i]] = true;
      }
      return false;
    },
    handleInput(data, ref, num) {
      if (data.length > num) {
        this.formData.name = data.substr(0, num - 1);
        this.$message.error("岗位模板名称" + "最大不超过" + num + "个字符");
        this.$refs[ref].blur();
      }
    },

    //保存
    async checkFormData(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          if (this.tableData.length == 0)
            return this.$message.error("至少设置一个考核指标");

          const allModified = this.tableData.every(
            it => it.type == 3 || it.type == 4
          );
          if (!allModified && this.total != 100)
            return this.$message.error("考核指标权重之和不是100%，请检查");

          const arr = this.tableData.map(it => it.name);
          if (this.isRepeat(arr))
            return this.$message.error("同一考核计划下，考核指标名称唯一");

          // if (this.tableData.every(item => item.scoreType == 2)) {
          //   return this.$message.error(
          //     "当前版本暂不支持评分方式全部为公式计算的指标"
          //   );
          // }
          // for (let i = 0; i < this.tableData.length; i++) {
          //   const el = this.tableData[i];

          //   if (
          //     el.scoreType == 2 &&
          //     el.dataRuleType == 2 &&
          //     el.targetList.length > 0 &&
          //     !el.targetList.every(item => item.targetValue > 0)
          //   ) {
          //     return this.$message.error(`请设置"${el.name}"的目标值`);
          //   }
          // }

          this.formData.addIndicatorList = [];

          console.log(this.formData);

          this.tableData.forEach(item => {
            if (!item.id) {
              this.formData.addIndicatorList.push(item);
            } else {
              this.formData.updatedIndicatorList.push(item);
            }
          });
          if (this.$route.query.id) {
            const res = await gePositionUpdate({
              ...this.formData,
              id: Number(this.$route.query.id)
            });
            if (res.success) {
              this.$message({
                message: "保存成功",
                type: "success",
                duration: 1000
              });
              this.formData = {
                name: "",
                addIndicatorList: [], //新增的指标列表
                removedIndicatorList: [], //删除的指标标识sign列表
                updatedIndicatorList: [] //编辑的指标列表
              };
              this.$router.go(-1);
            } else {
              this.$message.error(res.msg);
            }
          } else {
            delete this.formData.updatedIndicatorList;
            delete this.formData.removedIndicatorList;
            const res = await gePositionAdd({
              ...this.formData
            });
            if (res.success) {
              this.$message({
                message: "保存成功",
                type: "success",
                duration: 1000
              });
              this.formData = {
                name: "",
                addIndicatorList: [], //新增的指标列表
                removedIndicatorList: [], //删除的指标标识sign列表
                updatedIndicatorList: [] //编辑的指标列表
              };
              this.$router.go(-1);
            } else {
              this.$message.error(res.msg);
            }
          }
        } else {
          console.log("error submit!!");
           this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
          
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.indicator {
  // min-width: 1200px !important;
  padding-bottom: 90px;
  .empty {
    width: 840px;
    margin: 80px auto;
  }
  .indicator-tip {
    font-size: 14px;
    color: #888;
    text-align: center;
    margin-bottom: 40px;
  }
  .indicator-tabs {
    display: flex;
    justify-content: space-between;
  }
  .indicator-tabs_item {
    width: 160px;
    text-align: center;
    color: #555;
    .indicator-tabs_pic {
      width: 160px;
      height: 140px;
      margin-bottom: 25px;
      img {
        // width: 100%;
      }
    }
  }
  .indicator-table {
    margin-top: 33px;
    position: relative;
  }
  .total {
    position: absolute;
    right: 33.5%;
    color: #ff9500;
    font-size: 14px;
    &.warning {
      color: #d6342a;
    }
  }
  .indicator-footer {
    display: flex;
    align-items: center;
    height: 50px;
    border: 1px solid #ebeef5;
    border-top: none;
    padding: 0 24px;
    font-size: 14px;
    .text_btn {
      margin-left: 30px;
    }
  }
  .iconfont-per {
    color: $mainColor;
    font-size: 12px;
  }
  .icon-help {
    color: #909399;
    font-size: 13px;
  }
  .text {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
  }
  .z-header {
    display: flex;
    justify-content: space-between;
    .title {
      font-weight: Medium;
      font-size: 16px;
      color: #070f29;
    }
  }
  .delete-icon {
    color: $lineBorderPointer !important;
    font-size: 16px;
  }
  .icon-close1{
    font-size: 16px;
    color:#9EA5BD ;
  }
}
.main {
  padding: 20px 20px;
}
.main-title{
  margin-left: 10px;
}
.mian-footer {
  position: fixed;
  bottom: 0;
  width: calc(100% - 223px);
  height: 30px;
  padding: 20px 0 20px 0px;
  border-top: 1px solid #e5e5e5;
  background: #fff;
  text-align: center;
  z-index: 99;
  .btn {
    margin: 0 auto;
  }
}
.per-header {
  font-size: 16px;
  height: 61px;
  margin: 0 20px;
  border-bottom: 1px solid #eaeaea;
  line-height: 61px;
  .row {
    justify-content: space-between;
    align-items: center;
  }
}
/deep/.el-button--small{
  font-size: 14px;
}
</style>