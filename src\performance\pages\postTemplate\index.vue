<template>
  <div class="inspectionSetup">
    <header class="header">
      <el-row type="flex">
        <el-col :span="12">
          <span>岗位模板库</span>
        </el-col>
      </el-row>
    </header>
    <div class="main">
      <div class="search-main">
        <el-input
          v-model="searchForm.name"
          placeholder="请输入岗位模板名称"
          prefix-icon="iconiconfonticonfontsousuo1 iconfont"
          class="search-input"
          @keydown.enter.native="fetchData()"
        ></el-input>
      </div>
      <div class="questionTable">
        <div class="card-list">
          <div class="post-card" v-for="item in postList" :key="item.id">
            <div class="card-main" @click="handeDetail(item)">
              <div class="card-name" :title="item.name">{{ item.name }}</div>
              <div class="btn-list">
                <el-button type="primary" @click.stop="handleUpdate(item)" v-if="havePrivilege('kpi.performance.positionBank.change')">变更</el-button>
                <el-button @click.stop="handleDelete(item)" v-if="havePrivilege('kpi.performance.positionBank.delete')">删除</el-button>
              </div>
            </div>
          </div>
          <div class="add-card" @click="handleOperaClick" v-if="havePrivilege('kpi.performance.positionBank.add')">
              <div class="add-icon"><i class="iconfont-per icon-add1"></i></div>
              <div class="card-name">新增岗位模板</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { gePositionList,gePositionRemove } from "../../store/api";
import {havePrivilege} from "performance/utils/util.js";
import { mapState } from "vuex";
import store from "../../store/index";
export default {
  computed: {
    ...mapState({
      searchFormTemplate: state => store.state.searchFormTemplate
    })
  },
  beforeRouteEnter: (to, from, next) => {
    if (from.path !== "/postTemplate/showDetail"&&from.path !== "/postTemplate/detail") {
      store.commit("SET_SEARCHFORMTEMPLATE", {
        name: "",
      });
    }
    next();
  },
  data() {
    return {
      havePrivilege,
      searchForm: {
        name: "",
      },
      postList: [],
      isDelete:false,
    };
  },
  created(){
    if(this.searchFormTemplate){
      console.log(this.searchFormTemplate);
      this.searchForm = JSON.parse(JSON.stringify(this.searchFormTemplate));
      this.fetchData()
    }
  },
  methods: {
    //列表搜索
    async fetchData() {
      let res = await gePositionList(this.searchForm);
      this.total = res.data.total;
      this.postList = res.data.records;
    },
    handeDetail(item){
      store.commit("SET_SEARCHFORMTEMPLATE", this.searchForm);
      this.$router.push({
        path: "/postTemplate/showDetail",
        query: { id: item.id,name:item.name },
      });
    },
    handleUpdate(item){
      store.commit("SET_SEARCHFORMTEMPLATE", this.searchForm);
      this.$router.push({
        path: "/postTemplate/detail",
        query: { id: item.id,name:item.name },
      });
    },
    handleOperaClick() {
      store.commit("SET_SEARCHFORMTEMPLATE", this.searchForm);
      this.$router.push({
        path: "/postTemplate/detail"
        //   query: { id: row.id },
      });
    },
    handleDelete(item){
      this.$confirm("确定删除吗，删除后不可恢复",'删除岗位模板', {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type:'warning',
        closeOnClickModal: false,
        closeOnPressEscape: false
      }).then(async res => {
        let resRemove = await gePositionRemove({
          id: item.id
        });
        if (resRemove.success) {
          this.$message.success("删除成功");
          this.fetchData();
        } else {
          this.$message.error(resRemove.msg);
        }
      });
    },
  }
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.inspectionSetup{
  padding: 0 22px;
  .header {
    font-size: 16px;
    height: 61px;
    border-bottom: 1px solid #eaeaea;
    line-height: 61px;
    .row {
      justify-content: space-between;
      align-items: center;
    }
  }
}
.main {
  margin-top: 20px;
}
.search-main {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.card-list {
  display: flex;
  flex-wrap: wrap;
}
.questionTable{
  height: calc(100vh - 230px);
  overflow-y: auto;
  overflow-x: hidden;
}
.post-card {
  width: 23%;
  border-radius: 8px;
  padding-right: 20px;
  padding-bottom: 40px;
  cursor: pointer;
  .card-main{
    background: #F7F8FA;
    height: 200px;
    cursor: pointer;
    transition: all 0.5s;
    &:hover {
      transform: scale(1.1);
    }
      .card-name {
        width: 60%;
        padding-top: 30px;
        margin: 0 auto;
        text-align: center;
        font-weight: 600;
        font-size: 18px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .btn-list {
        height: 40px;
        display: flex;
        justify-content: center;
        margin-top: 70px;
      }
  }
}
.add-card {
  width: 23%;
  height: 198px;
  border: 1px dashed #CCCCCC;
  margin-right: 20px;
  margin-bottom: 40px;
  border-radius: 8px;
  cursor: pointer;
  &:hover {
    border: 1px dashed $mainColor;
  }
  .add-icon {
    width: 40px;
    height: 40px;
    margin: 0 auto;
    margin-top: 60px;
    .icon-add1 {
      font-size: 24px;
      color: $mainColor;
    }
  }
  .card-name {
    width: 60%;
    margin: 0 auto;
    text-align: center;
    font-size: 14px;
    color: $mainColor;
  }
}
.add-cards:hover{
    border: 1px dashed $mainColor;
  }
.dlo-header {
  display: flex;
  justify-content: space-between;
  .title {
    font-weight: Medium;
    font-size: 16px;
    color: #070f29;
  }
}
</style>
<style>
.el-message-box__title{
  font-weight: Medium;
  font-size: 16px !important;
  color: #070f29 !important;
}
</style>
