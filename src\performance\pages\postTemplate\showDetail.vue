<template>
  <div class="inspectionSetup">
    <header class="per-header">
      <el-row type="flex">
        <el-col :span="12">
          <span @click="$router.go(-1)" class="back-style">返回</span>
          <span class="header-line">|</span>
          <span>{{ $route.query.name }}</span>
        </el-col>
      </el-row>
    </header>
    <div class="main">
      <span> <i class="iconfont-per icon-shujuyichang"></i> 可套用模板快速创建考核计划</span>
      <div class="main-ckass">
        <def-title text="考核指标明细" />
        <div class="indicator-table" v-if="tableData.length">
          <old-table
            :data="tableData"
            :headerData="headerData"
            :isShowOperation="isShowOperation"
          >
            <template slot="descriptionStr" slot-scope="scope">
              <el-tooltip
                placement="top"
                :disabled="scope.msg.row.descriptionStr.length < 50"
              >
                <p slot="content" >
                  {{ scope.msg.row.descriptionStr }}
                </p>
                <p class="text">
                  {{ scope.msg.row.descriptionStr }}
                </p>
              </el-tooltip>
            </template>

            <template slot="scoreStandard" slot-scope="scope">
              <el-tooltip
                placement="top"
                :disabled="scope.msg.row.scoreStandard.length < 50"
              >
                <p slot="content">
                  {{ scope.msg.row.scoreStandard }}
                </p>
                <p class="text">
                  {{ scope.msg.row.scoreStandard }}
                </p>
              </el-tooltip>
            </template>

            <template slot="maxScore-header">
              评分上限
              <el-tooltip
                effect="dark"
                content="加分项：加分上限；减分项：减分上限"
                placement="top"
              >
                <i class="iconfont-per icon-help" />
              </el-tooltip>
            </template>
            <template slot="weight-header">
              考核指标权重
              <el-tooltip
                effect="dark"
                content="加分项、减分项没有考核指标权重，不能设置"
                placement="top"
              >
                <i class="iconfont-per icon-help" />
              </el-tooltip>
            </template>
            <!-- <template slot="targetList-header">
          目标值
          <el-tooltip
            effect="dark"
            content="仅“定量考核指标”可设置"
            placement="top"
          >
            <i class="iconfont-per icon-help" />
          </el-tooltip>
        </template> -->
            <template slot="scoreType" slot-scope="scope">
              <div v-if="scope.msg.row.scoreType == 1">
                直接输入
              </div>
              <div v-if="scope.msg.row.scoreType == 2">
                <el-tooltip placement="top">
                  <p style="text-align:left" class="text">
                    <span>公式计算</span><br />
                    <span v-if="scope.msg.row.dataRuleType == 1"
                      >按实际完成值来算:</span
                    >
                    <span v-if="scope.msg.row.dataRuleType == 2"
                      >按目标达成率计算:</span
                    ><br />
                    <span
                      v-for="(dataRuleItem, index) in scope.msg.row
                        .dataRuleList"
                      :key="index"
                    >
                      <span v-if="scope.msg.row.dataRuleType == 1"
                        >{{ index + 1 }}、{{
                          dataRuleItem.min + scope.msg.row.dataUnit
                        }}<span v-if="index !== 0">＜</span
                        ><span v-if="index == 0">≤</span>完成值≤{{
                          dataRuleItem.max + scope.msg.row.dataUnit
                        }}</span
                      ><span v-if="scope.msg.row.dataRuleType == 2"
                        >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                          v-if="index !== 0"
                          >＜</span
                        ><span v-if="index == 0">≤</span>目标达成率≤{{
                          dataRuleItem.max
                        }}%</span
                      >,得分：{{ dataRuleItem.score }}分;<br />
                    </span>
                  </p>

                  <p slot="content">
                    <span>公式计算</span><br />
                    <span v-if="scope.msg.row.dataRuleType == 1"
                      >按实际完成值来算:</span
                    >
                    <span v-if="scope.msg.row.dataRuleType == 2"
                      >按目标达成率计算:</span
                    ><br />
                    <span
                      v-for="(dataRuleItem, index) in scope.msg.row
                        .dataRuleList"
                      :key="index"
                    >
                      <span v-if="scope.msg.row.dataRuleType == 1"
                        >{{ index + 1 }}、{{
                          dataRuleItem.min + scope.msg.row.dataUnit
                        }}<span v-if="index !== 0">＜</span
                        ><span v-if="index == 0">≤</span>完成值≤{{
                          dataRuleItem.max + scope.msg.row.dataUnit
                        }}</span
                      ><span v-if="scope.msg.row.dataRuleType == 2"
                        >{{ index + 1 }}、{{ dataRuleItem.min }}%<span
                          v-if="index !== 0"
                          >＜</span
                        ><span v-if="index == 0">≤</span>目标达成率≤{{
                          dataRuleItem.max
                        }}%</span
                      >,得分：{{ dataRuleItem.score }}分;<br />
                    </span>
                  </p>
                </el-tooltip>
              </div>
            </template>

            <template slot="scorerName" slot-scope="scope">
              <p v-if="scope.msg.row.scoreType == 2" style="color:#ccc">
                系统评分
              </p>
              <p v-if="scope.msg.row.scoreType == 1">
                {{
                  scope.msg.row.scorerName
                    ? scope.msg.row.scorerName
                    : scope.msg.row.scorerDataName || "--"
                }}
              </p>
            </template>
          </old-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { defCard, defTitle } from "../personalPerformance/components";
import dd from "performance/utils/dataDictionary.js";
import { gePositionDetail } from "performance/store/api.js";
export default {
  components: {
    defCard,
    defTitle
  },
  data() {
    return {
      planBaseInfo: {},

      editInfo: {},
      options: dd.indicatorType,
      levelType: dd.levelType,

      tableData: [],
      headerData: [
        { title: "考核指标名称", label: "name", align: "left",fixed: "left",minWidth: "120px",},
        { title: "考核指标类型", label: "typeStr", align: "left",minWidth: "120px", },
        {
          title: "考核指标说明",
          label: "descriptionStr",
          slot: "descriptionStr",
          align: "left",
          minWidth: "120px",
        },
        {
          title: "评价标准",
          label: "scoreStandard",
          slot: "scoreStandard",
          align: "left",
          minWidth: "120px",
        },
        {
          title: "评分上限",
          label: "maxScore",
          slotHeader: "maxScore-header",
          align: "right",
          minWidth: "120px",
        },
        {
          title: "考核指标权重",
          label: "weightStr",
          slotHeader: "weight-header",
          align: "right",
          minWidth: "120px",
        },
        // {
        //   title: "目标值",
        //   label: "targetList",
        //   slotHeader: "targetList-header"
        // },
        {
          title: "评分方式",
          label: "scoreType",
          align: "left",
          slot: "scoreType",
          minWidth: "120px",
        },
        {
          title: "考核指标评分人",
          label: "scorerName",
          slot: "scorerName",
          align: "left",
          minWidth: "120px",
        }
      ],

      isShowOperation: false //是否显示操作列
    };
  },
  created() {
    this.getPlanDetail();
  },
  methods: {
    async getPlanDetail() {
      const res = await gePositionDetail({
        id: this.$route.query.id
      });
      setTimeout(() => {
        this.loading = false;
      }, 300);

      if (res.success) {
        this.tableData = this.handleList(res.data.indicatorList);
      } else {
        this.$$message.error(res.msg);
      }
    },

    handleList(arr) {
      if (arr.length == 0) return [];
      arr.map(item => {
        item.typeStr = this.options[item.type];
        item.weightStr =
          item.type == 1 || item.type == 2 ? item.weight + "%" : "--";
        item.descriptionStr = item.description || "--";
        if (item.scorerData.length > 0 && item.scorerName == "") {
          item.scorerData.map(it => {
            switch (it.processorType) {
              case 1:
                it.name = "被考核人";
                break;
              case 2:
                it.name = this.levelType[it.superiorLevel];
                break;
              case 3:
                it.name = it.processorName;
                break;
            }
            return it;
          });
          item.scorerDataName = item.scorerData.map(it => it.name).join("，");
        }
        return item;
      });

      return arr;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.main {
  margin: 10px 20px;
}
.per-header {
  font-size: 16px;
  height: 61px;
  margin: 0 20px;
  border-bottom: 1px solid #eaeaea;
  line-height: 61px;
  .row {
    justify-content: space-between;
    align-items: center;
  }
}
.main-title{
  margin-left: 10px;
}
.main-ckass {
  margin-top: 20px;
}
.table-main {
  margin-top: 20px;
}
.indicator-table {
  margin-top: 33px;
  position: relative;
}

.iconfont-per {
  color: #9EA5BD;
  font-size: 12px;
}
.icon-help {
  color: #909399;
  font-size: 13px;
}
.text {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
}
</style>