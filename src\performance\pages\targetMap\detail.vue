<template>
  <el-dialog
    :title="title"
    :visible.sync="isShow"
    @close="close"
    width="1050px"
  >
    <div class="content">
      <div class="first">
        <div v-for="(item, index) in person" :key="index" class="first-item">
          <img class="item-left" :src="item.img" />
          <div class="item-right">
            <p v-if="item.key !== 'relations'" class="black">
              {{ form[item.key] }}
            </p>

            <el-popover
              v-else
              placement="top-start"
              trigger="hover"
              width="200"
              :disabled="!allPerson"
              :content="allPerson"
            >
              <p slot="reference" class="black" style="margin-bottom:5px">
                {{ form[item.key] }}
              </p>
            </el-popover>

            <p class="gray">{{ item.label }}</p>
          </div>
        </div>
      </div>

      <span class="info-title">考核指标信息</span>
      <div class="second">
        <div
          v-for="(item, index) in second"
          :key="index"
          class="second-item"
          :class="{ no: (index + 1) % 4 == 0 }"
        >
          <div class="cont">
            <p class="gray">{{ item.label }}</p>
            <p class="black">{{ form[item.key] }}</p>
          </div>
        </div>
      </div>
      <div style="margin-bottom:25px">
        <p class="gray">考核指标说明</p>
        <p class="black">{{ form["indicatorDesc"] }}</p>
      </div>
      <div>
        <p class="gray">评价标准</p>
        <p class="black">
          {{ form["scoreStandard"] }}
        </p>
      </div>
      <span class="info-title">考核计划信息</span>
      <div class="second">
        <div
          v-for="(item, index) in third"
          :key="index"
          class="second-item"
          :class="{ no: (index + 1) % 4 == 0 }"
        >
          <div class="cont">
            <p class="gray">{{ item.label }}</p>
            <p class="black">{{ form[item.key] }}</p>
          </div>
        </div>
      </div>
      <div>
        <p class="gray">考核计划说明</p>
        <p class="black">{{ form["planDesc"] }}</p>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import dd from "performance/utils/dataDictionary.js";
import { date2Str } from "performance/utils/util.js";
const personInit = () => {
  return [
    {
      label: "考核对象",
      img: require("../../images/khdx.png"),
      key: "examineeName"
    },
    {
      label: "关联人员",
      img: require("../../images/glry.png"),
      key: "relations"
    },
    {
      label: "考核状态",
      img: require("../../images/khzt.png"),
      key: "stageStatus"
    },
    {
      label: "目标达成率",
      img: require("../../images/mbdcl.png"),
      key: "targetRate"
    },
    {
      label: "考核指标总分",
      img: require("../../images/zf.png"),
      key: "totalScore"
    }
  ];
};
const secondInit = () => {
  return [
    {
      label: "考核指标名称",
      key: "indicatorName"
    },
    {
      label: "考核指标类型",
      key: "indicatorType"
    },
    {
      label: "关联父考核指标",
      key: "parentIndicatorName"
    },
    {
      label: "评分上限",
      key: "maxScore"
    },
    {
      label: "考核指标权重",
      key: "indicatorWeight"
    },
    {
      label: "评分方式",
      key: "scoreType"
    },
    {
      label: "目标值",
      key: "targetValue"
    },
    {
      label: "实际完成值",
      key: "completeValue"
    }
  ];
};

export default {
  props: {
    detail: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      isShow: false,
      form: {},
      title: "",
      person: personInit(),
      second: secondInit(),
      allPerson: "", //全部关联人员
      third: [
        {
          label: "考核计划名称",
          key: "planName"
        },
        {
          label: "考核周期",
          key: "planPeriod"
        },
        {
          label: "考核类型",
          key: "planType"
        }
      ]
    };
  },
  watch: {
    detail: {
      handler(obj) {
        this.person = personInit();
        this.second = secondInit();
        console.log("val", obj);
        this.title = `${obj.indicatorName}${
          obj.targetValueFlag
            ? "(" + obj.targetValue + obj.targetValueUnit + ")"
            : ""
        } `;

        if (obj.scoreType == 1) {
          this.second.splice(
            this.second.findIndex(v => v.key === "completeValue"),
            1
          );
        }
        if (!obj.targetValueFlag) {
          this.second.splice(
            this.second.findIndex(v => v.key === "targetValue"),
            1
          );
          this.person.splice(
            this.person.findIndex(v => v.key === "targetRate"),
            1
          );
        }
        if (obj.planType == 3) {
          this.person.splice(
            this.person.findIndex(v => v.key === "relations"),
            1
          );
        }

        for (let [key, value] of Object.entries(obj)) {
          switch (key) {
            case "indicatorType":
              value = dd.indicatorType[value];
              break;
            case "planPeriod":
              value = date2Str(obj.planPeriod, obj.startDate, obj.endDate);
              break;
            case "planType":
              value = dd.checkType[value];
              break;
            case "stageStatus":
              value = dd.checkFinishType[value];
              break;
            case "scoreType":
              value = { 1: "直接输入", 2: "公式计算" }[value];
              break;
            case "maxScore":
              value = value + "分";
              break;
            case "indicatorWeight":
              value = value + "%";
              break;
            case "targetRate":
              value = value ? value + "%" : null;
              break;
            case "totalScore":
              value = value ? value + "分" : null;
              break;
            case "completeValue":
              value = value ? value + obj.targetValueUnit : null;
              break;
            case "targetValue":
              value = value ? value + obj.targetValueUnit : null;
              break;
            case "relations": {
              if (value && value.length > 1) {
                this.allPerson = value.join("，");
                const arr = value.map(i => {
                  const n = i.length > 3 ? i.substr(0, 3) + "..." : i;
                  return n;
                });
                value = arr.slice(0, 2).join("，");
              } else {
                this.allPerson = null;
                value = value ? value.join("，") : null;
              }

              break;
            }
          }
          if (value === null || value === "") {
            value = "--";
          }
          this.form[key] = value;
        }
      },
      deep: true
    }
  },
  methods: {
    openDialog() {
      this.isShow = true;
    },
    close() {
      this.isShow = false;
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";

.content {
  padding: 10px 14px;
  font-size: 14px;
  .first {
    width: 100%;
    display: flex;
    .first-item {
      display: flex;
      width: 200px;
      margin-right: 50px;
      &:last-child {
        margin-right: 0;
      }
      .item-left {
        width: 40px;
        height: 40px;
        margin-right: 10px;
        margin-top: 3px;
      }
    }
  }
  .second {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .second-item {
      width: 260px;
      margin-right: 35px;
      margin-bottom: 25px;
    }
    .no {
      width: 70px;
      margin-right: 0;
    }
  }
  .info-title {
    font-weight: 500;
    font-size: 16px;
    margin: 30px 0 20px;
    color: #070f29;
    line-height: 18px;
    display: flex;
    align-items: center;
  }
  .info-title::before {
    content: "";
    display: inline-block;
    width: 3px;
    height: 14px;
    background-color: $mainColor;
    border-radius: 1px;
    margin-right: 10px;
  }
  p:first-child {
    margin-bottom: 5px;
  }
  .black {
    color: #070f29;
  }
  .gray {
    color: #888;
  }
}
</style>
