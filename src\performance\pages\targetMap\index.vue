<template>
  <div class="target-map" v-if="unlock">
    <header class="header">
      <el-row type="flex" class="row">
        <span>目标地图</span>
      </el-row>
    </header>
    <div class="target-map_section">
      <el-tabs
        class="section-tabs"
        v-model="params.tabType"
        @tab-click="handleClickTab"
      >
        <el-tab-pane
          v-if="havePrivilege('kpi.performance.targetMap.viewSubsidiary')"
          label="按公司查看"
          name="1"
        ></el-tab-pane>
        <el-tab-pane
          v-if="havePrivilege('kpi.performance.targetMap.viewDepartment')"
          label="按部门查看"
          name="2"
        ></el-tab-pane>
        <el-tab-pane label="按个人查看" name="3"></el-tab-pane>
      </el-tabs>
    </div>
    <div v-loading="loading">
      <div class="target-map_select">
        <el-select
          v-if="params.tabType == 1"
          v-model="sId"
          placeholder="请选择公司"
          :popper-append-to-body="false"
        >
          <el-option
            v-for="item in companyList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <el-input
          v-if="params.tabType == 2 || params.tabType == 3"
          v-model="temporary"
          :placeholder="placeholder"
          @focus="handleFocus()"
          suffix-icon="el-icon-plus"
          :validate-event="false"
        ></el-input>

        <el-select
          v-model="params.period"
          placeholder="请选择"
          @change="handleChange"
          :popper-append-to-body="false"
        >
          <el-option
            v-for="item in periodType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>

        <el-input
          v-model="params.startDate"
          v-show="false"
          :validate-event="false"
        ></el-input>
        <date-picker
          :type="params.period"
          :dateObj="dateObj"
          @setDate="setDate"
        ></date-picker>
        <el-button class="btn" @click="handleQuery">查询</el-button>
      </div>
      <div v-if="noData" class="no-data">
        <img :src="imgBg" alt="" />
        <p>暂无数据</p>
      </div>
      <vue-okr-tree
        v-else
        :data="treeData"
        direction="horizontal"
        show-collapsable
        default-expand-all
        showNodeNum
        :render-content="renderContent"
        @node-click="handleNodeClick"
      ></vue-okr-tree>
    </div>

    <!-- <el-input
                v-if="form.type == 2 || form.type == 3"
                v-model="form.temporary"
                placeholder="请选择考核对象"
                @focus="handleFocus()"
                suffix-icon="el-icon-plus"
                :validate-event="false"
              ></el-input> -->

    <!-- <tree
      v-if="treeData.children.length > 0"
      :chartData="treeData"
      @getDetail="getDetail"
    /> -->
    <!-- :node-btn-content="renderBtnContent" -->

    <select-staff
      v-if="showDialogDept"
      :list="selectData"
      :isUser="isUser"
      :isOnly="true"
      :isDifferent="true"
      :isMapDepartmentTree="isMapDepartmentTree"
      :select="selectList"
      @close="showDialogDept = false"
      @commit="commitSelect"
    ></select-staff>

    <user-select
      v-if="showDialogPerson"
      :list="selectData"
      :isOnly="true"
      :select="selectList"
      :userList="employeeList"
      @close="showDialogPerson = false"
      @commit="commitSelect"
    ></user-select>

    <detail ref="detail" :detail="detail"></detail>
  </div>
</template>
<script>
import tree from "./tree";
import DatePicker from "../performanceManage/components/DatePicker";
import SelectStaff from "../performanceManage/components/SelectStaff";
import UserSelect from "performance/pages/IndicatorsLibrary/components/UserSelect";
// import { VueOkrTree } from "vue-okr-tree";
// import "vue-okr-tree/dist/vue-okr-tree.css";
import detail from "./detail";
import commons from "@/mixins/commons";

function getQuarterByMonth() {
  var today = new Date(); //获取当前时间
  var year = today.getFullYear();
  var month = today.getMonth() + 1;
  var newDate;
  console.log(year, month);
  if (month >= 1 && month <= 3) {
    newDate = {
      startDate: new Date(`${year}-01-01`).getTime(),
      endDate: new Date(`${year}-03-31`).getTime(),
    };
  } else if (month >= 4 && month <= 6) {
    newDate = {
      startDate: new Date(`${year}-04-01`).getTime(),
      endDate: new Date(`${year}-06-30`).getTime(),
    };
  } else if (month >= 7 && month <= 9) {
    newDate = {
      startDate: new Date(`${year}-07-01`).getTime(),
      endDate: new Date(`${year}-09-30`).getTime(),
    };
  } else {
    newDate = {
      startDate: new Date(`${year}-10-01`).getTime(),
      endDate: new Date(`${year}-12-31`).getTime(),
    };
  }
  return newDate;
}
import {
  getMapDepartment,
  getMapEmployee,
  getMapSubsidiary,
  checkMapDepartment,
  checkMapPersonal,
  checkMapSubsidiary,
  getMapDetail,
} from "performance/store/api.js";
import { date2Str, havePrivilege } from "performance/utils/util.js";

export default {
  components: {
    DatePicker,
    SelectStaff,
    detail,
    // VueOkrTree,
    UserSelect,
  },
  mixins: [commons],
  data() {
    return {
      havePrivilege,
      loading: true,
      noDepartment: false, //是否有浏览部门树权限
      companyList: [],
      dateObj: {},
      showDialogDept: false,
      showDialogPerson: false,
      isUser: false,
      detail: null,
      temporary: "",
      temporary1: "", //部门名称
      temporary2: "", //员工名称
      subject: "", //用工主体名称
      isMapDepartmentTree: false,
      selectList: [],
      selectData: [], //树组件数组
      employeeList: [], //人员数组
      selectPersonList: [],
      selectDepartmentList: [],
      noData: false,
      sId: null,
      imgBg:
        window.env.server_env === "boc" || window.env.server_env === "cgb"
          ? require("../../images/no-data-red.png")
          : require("../../images/no-data.png"),
      placeholder: "",
      periodType: [
        { label: "年度", value: 1 },
        { label: "半年度", value: 2 },
        { label: "季度", value: 3 },
        { label: "月度", value: 4 },
        { label: "自定义", value: 5 },
      ],
      params: {
        period: 3,
        orgId: null,
        endDate: null,
        startDate: null,
        tabType: "1",
      },
      treeData: [],
      baseBg: {
        0: "linear-gradient(122deg, #5486FF 0%, #4F71FF 100%)",
        1: "linear-gradient(135deg, #FFBC14 0%, #FF8300 100%)",
        2: "linear-gradient(134deg, #41DDB6 0%, #2BCDA4 100%)",
        3: "linear-gradient(-44deg, #8B5FEB 0%, #B095FE 100%)",
        4: "linear-gradient(129deg, #FF7372 0%, #FF4E51 100%)",
      },
    };
  },

  created() {
    this.onLoad().then((data) => {
      this.getQuarterByMonth();
    });
  },

  mounted() {
    // this.$refs.datePicker.blur();
    window.onscroll = function () {
      var scrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;
      console.log("滚动距离" + scrollTop);
    };
    this.onLoad().then((data) => {
      if (havePrivilege("kpi.performance.targetMap.viewSubsidiary")) {
        this.params.tabType = "1";
        this.getMapSubsidiary();
      } else if (havePrivilege("kpi.performance.targetMap.viewDepartment")) {
        this.isUser = true;
        this.params.tabType = "2";
        this.getMapDepartment();
      } else {
        this.isUser = false;
        this.params.tabType = "3";
        this.getMapEmployee();
      }
      console.log("=====权限=====");
      console.log(
        "公司",
        havePrivilege("kpi.performance.targetMap.viewSubsidiary")
      );
      console.log(
        "部门",
        havePrivilege("kpi.performance.targetMap.viewDepartment")
      );
      console.log(
        "个人",
        havePrivilege("kpi.performance.targetMap.viewEmployee")
      );
    });
  },
  methods: {
    renderContent(h, node) {
      var color = "";
      if (node.data.targetRate < 100) {
        color = "#D6342A";
      } else if (node.data.targetRate == 100) {
        color = "#4F71FF";
      } else {
        color = "#41BD5A";
      }

      let images = null;
      const status = node.data.stageStatus;
      if (status <= 3) {
        images = require("../../images/wks.png");
      } else if (status > 3 && status <= 8) {
        images = require("../../images/pfz.png");
      } else {
        images = require("../../images/ypf.png");
      }

      let date = date2Str(
        node.data.planPeriod,
        node.data.startDate,
        node.data.endDate
      );

      var m;
      if (node.data.planPeriod != 1) {
        var n = date.indexOf("年");
        m = date.substring(n + 1);
      } else {
        m = date;
      }
      console.log(m);

      const o = { 1: "公司", 3: "个人" };

      return node.data.tip ? (
        <div class="con-root">{node.data.label} </div>
      ) : (
        <div class="con-box">
          <div class="con-top">
            <div>
              {/* //是否使用目标值 targetValueFlag*/}
              {node.data.indicatorName.length > 10
                ? node.data.indicatorName.substr(0, 8) + "..."
                : node.data.indicatorName}
              {node.data.targetValueFlag
                ? node.data.targetValue + node.data.targetValueUnit
                : ""}
            </div>

            <div class="con-top_right">
              <span
                class="con-ratio"
                style={{
                  color,
                }}
              >
                {node.data.targetValueFlag && node.data.targetRate
                  ? node.data.targetRate + "%"
                  : ""}
              </span>
              <span class="con-score">
                {node.data.indicatorScore
                  ? node.data.indicatorScore + "分"
                  : ""}
              </span>
              <span>
                <img class="con-img" src={images} />
              </span>
            </div>
          </div>
          <div class="con-middle">
            {node.data.relations.slice(0, 2).map((x) => (
              <div class="item">
                <div
                  class="circle"
                  style={{
                    background: this.baseBg[parseInt(Math.random() * 5)],
                  }}
                >
                  {x.substr(-2)}
                </div>
                <p>
                  {node.data.relations.length > 1 && x.length > 4
                    ? x.substr(0, 4) + "..."
                    : x.length > 10
                    ? x.substr(0, 10) + "..."
                    : x}
                </p>
              </div>
            ))}
            <span class="omit">
              {node.data.relations.length > 2 ? "..." : ""}
            </span>
          </div>
          <div class="con-bottom">
            <span>
              {node.data.planType == 2
                ? node.data.examineeName
                : o[node.data.planType]}
            </span>
            <span>{m.length > 10 ? m.substr(0, 10) + "..." : m}</span>
          </div>
          <div class="diy-con-content">{node.data.content}</div>
        </div>
      );
    },
    handleNodeClick(data) {
      console.log(data);
      if (data.indicatorId) {
        this.getDetail(data.indicatorId);
      }
    },
    handleClickTab() {
      this.loading = true;
      this.params.orgId = null;
      this.temporary = "";
      this.params.period = 3;
      this.getQuarterByMonth();
      this.treeData = [];
      this.selectList = [];
      console.log(this.params.tabType);
      switch (this.params.tabType) {
        case "1":
          this.getMapSubsidiary();
          break;
        case "2":
          this.isUser = true;
          this.isMapDepartmentTree = true;
          this.placeholder = "请选择部门";
          this.getMapDepartment();
          break;
        case "3":
          this.isUser = false;
          this.isMapDepartmentTree = false;
          this.placeholder = "请选择人员";
          this.getMapEmployee();
          break;
      }
    },
    // handleChangeOrg() {
    //   this.handleCheckTree();
    // },
    //获取当前季度时间戳
    getQuarterByMonth() {
      this.dateObj = getQuarterByMonth();
      this.params.startDate = this.dateObj.startDate;
      this.params.endDate = this.dateObj.endDate;
    },
    //查看详情
    async getDetail(id) {
      const res = await getMapDetail({ indicatorId: id });
      console.log(res);
      if (res.success) {
        this.detail = res.data;
        this.$refs.detail.openDialog();
      } else {
        this.$message.error(res.msg);
      }
    },
    //考核周期类型选择
    handleChange(val) {
      this.params.startDate = "";
      // this.treeData = [];
      // this.noData = true;
    },

    handleQuery() {
      if (this.params.tabType == "2" && this.noDepartment)
        return this.$message.error("您未担任任一部门负责人，暂无法使用此功能");
      if (this.params.tabType == "3" && this.noDepartment)
        return this.$message.error("您还未加入花名册，暂无法使用此功能");

      if (!this.params.startDate) return this.$message.error("请选择考核周期");
      this.handleCheckTree();
    },

    setDate(val) {
      this.params.startDate = val.startDate;
      this.params.endDate = val.endDate;
    },
    handleFocus() {
      if (this.params.tabType == "2" && this.noDepartment)
        return this.$message.error("您未担任任一部门负责人，暂无法使用此功能");
      if (this.params.tabType == "3" && this.noDepartment)
        return this.$message.error("您还未加入花名册，暂无法使用此功能");

      switch (this.params.tabType) {
        case "2":
          this.showDialogDept = true;
          break;
        case "3":
          this.showDialogPerson = true;
          break;
      }
    },

    //获取选中对象
    commitSelect(val) {
      if (val.length == 0)
        return this.$message.error(
          `请选择${this.params.tabType == "2" ? "部门" : "人员"}`
        );
      if (this.params.tabType == "2") {
        this.selectDepartmentList = val;
        this.temporary1 = val[0].name;
        this.dId = val[0].id;
        this.temporary = this.temporary1;
        this.selectList = this.selectDepartmentList;
        this.showDialogDept = false;
      } else {
        this.selectPersonList = val;
        this.pId = val[0].employeeId;
        this.temporary2 = val[0].employeeName;
        this.subject = `(${val[0].subsidiaryName})`;
        this.temporary = this.temporary2;
        this.selectList = this.selectPersonList;
        this.showDialogPerson = false;
      }
      this.treeData = [];
      this.noData = true;
    },

    //用工主体列表
    async getMapSubsidiary() {
      const res = await getMapSubsidiary();
      if (res.success) {
        this.companyList = res.data.list || [];
        this.sId = this.sId ? this.sId : res.data.current.id;
        // this.params.orgId = res.data.current.id;
        this.handleCheckTree();
      } else {
        this.$message.error(res.msg);
      }
    },

    //部门树
    async getMapDepartment() {
      const res = await getMapDepartment();
      console.log(res);
      this.selectData = res.data.tree;
      if (res.data.current) {
        if (this.selectDepartmentList.length == 0) {
          this.selectDepartmentList.push(res.data.current);
          this.dId = res.data.current.id;
          this.temporary1 = res.data.current.name;
        }
        this.noDepartment = false;
        this.selectList = this.selectDepartmentList;
        this.temporary = this.temporary1;
        this.handleCheckTree();
      } else {
        this.noDepartment = true;
        this.noData = true;
        this.loading = false;
      }
    },

    //员工树
    async getMapEmployee() {
      const res = await getMapEmployee();
      // this.selectData = res.data.tree;
      this.selectData = res.data.departmentTree;
      this.employeeList = res.data.employeeList;

      if (res.data.current) {
        if (this.selectPersonList.length == 0) {
          this.selectPersonList.push(res.data.current);
          this.pId = res.data.current.employeeId;
          this.temporary2 = res.data.current.employeeName;
          this.subject = `(${res.data.current.subsidiaryName})`;
        }
        this.noDepartment = false;
        this.selectList = this.selectPersonList;
        this.temporary = this.temporary2;

        this.handleCheckTree();
      } else {
        this.noDepartment = true;
        this.noData = true;
        this.loading = false;
      }
    },

    async handleCheckTree() {
      var _req;
      switch (this.params.tabType) {
        case "1":
          this.params.orgId = this.sId;
          _req = checkMapSubsidiary;
          break;
        case "2":
          this.params.orgId = this.dId;
          _req = checkMapDepartment;
          break;
        case "3":
          this.params.orgId = this.pId;
          _req = checkMapPersonal;
          break;
      }

      if (!this.params.startDate) {
        this.loading = false;
        return false;
      }

      const res = await _req(this.params);
      this.loading = false;
      if (res.success) {
        this.treeData = [];
        if (res.data.length > 0) {
          this.handleData(res.data);
          this.noData = false;
        } else {
          this.noData = true;
        }
      } else {
        this.$message.error(res.msg);
      }
    },
    handleData(arr) {
      const name =
        this.params.tabType == 1
          ? this.companyList.filter((it) => it.id == this.params.orgId)[0].name
          : this.params.tabType == "3"
          ? `${this.temporary2}${this.subject}`
          : this.temporary1;
      console.log(name);

      const obj = {
        label: name,
        tip: "root",
        children: arr,
      };

      this.treeData.push(obj);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~assets/scss/helpers.scss";
.target-map {
  padding: 0 32px;
  box-sizing: border-box;
  /*height: calc(100vh - 80px);*/
  overflow: scroll;
  .header {
    font-size: 16px;
    height: 61px;
    border-bottom: 1px solid #eaeaea;
    line-height: 61px;
    .row {
      justify-content: space-between;
      align-items: center;
    }
  }
  .target-map_section {
    padding-top: 10px;

    .section-tabs {
      /deep/.el-tabs__nav-wrap::after {
        display: none;
      }
    }
  }
  .target-map_select {
    display: flex;
    margin-top: 20px;
    margin-bottom: 20px;
    .btn {
      width: 70px;
      height: 40px;
      margin-left: 10px;
      color: $mainColor;
      border: 1px solid $mainColor;
    }
  }
  .el-input,
  .el-select {
    width: 260px;
    margin-right: 10px;
  }
  .no-data {
    margin: 150px auto 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    img {
      width: 382px;
      height: 218px;
      margin-bottom: 40px;
    }
    p {
      font-size: 16px;
      color: #555555;
    }
  }
}

.text {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
}
/deep/ .el-tabs__header {
  margin: 0;
}
.search-input {
  width: 280px;
  /deep/.el-input__inner {
    padding: 0 32px 0 12px;
  }
  /deep/.el-input__suffix {
    right: 12px;
  }
}
</style>

<style>
.tagTitle {
  position: fixed;
  width: 100%;
  z-index: 9999;
}
.horizontal .org-chart-node-label .org-chart-node-label-inner {
  padding: 0;
  box-shadow: none;
}

/* .horizontal .org-chart-node:not(.is-left-child-node):after {
  border-left: 1px solid #e8eaf3;
}
.horizontal .is-left-child-node:after,
.horizontal .org-chart-node:not(.is-left-child-node):after {
  border-left: 1px solid #e8eaf3;
} */

.con-root {
  min-width: 80px;
  white-space: nowrap;
  height: 40px;
  background: #f1f1f1;
  border-radius: 20px;
  line-height: 40px;
  font-size: 14px;
  padding: 0 10px;
}
.con-box {
  padding: 16px 14px;
  width: 270px;
  height: 96px;
  border: 1px solid #cccccc;
  border-radius: 10px;
}

.horizontal .org-chart-node-label .org-chart-node-label-inner:hover {
  box-shadow: 0 0px 0px #ccc;
}

.con-box:hover {
  border: 1px solid var(--color-primary);
}

.con-top {
  font-size: 14px;
  color: #070f29;
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
.con-top_right {
  height: 18px;
  display: flex;
  align-items: center;
}
.con-ratio {
  display: inline-block;
  margin-right: 5px;
}
.con-score {
  display: inline-block;
  margin-right: 8px;
  color: #555;
}
.con-img {
  position: relative;
  top: 1px;
  width: 18px;
  height: 18px;
}
.con-middle {
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: #070f29;
}
.con-middle .item {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.con-middle .item .circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 36px;
  margin-right: 6px;
  font-size: 12px;
}
.con-middle .omit {
  position: relative;
  top: -4px;
  color: #878b94;
}

.con-bottom {
  text-align: right;
}
.con-bottom span {
  display: inline-block;
  color: #6a6f7f;
  height: 14px;
  line-height: 14px;
  border-radius: 14px;
  font-size: 14px;
  padding: 4px 12px;
  background: #f1f1f1;
  margin-left: 10px;
}
</style>

<style>
/*使用 ::before 和 ::after 绘制连接器*/
.horizontal .org-chart-node:not(.is-left-child-node):before,
.horizontal .org-chart-node:not(.is-left-child-node)::after {
  content: "";
  position: absolute;
  border-left: 1px solid #e8eaf3;
  top: 0;
  left: 0;
  width: 20px;
  height: 50%;
}
.horizontal .is-left-child-node:before,
.horizontal .is-left-child-node::after {
  content: "";
  position: absolute;
  border-right: 1px solid #e8eaf3;
  top: 0;
  right: 0;
  width: 20px;
  height: 50%;
}
.horizontal .org-chart-node:not(.is-left-child-node):after {
  top: 50%;
  border-top: 1px solid #e8eaf3;
}
.horizontal .is-left-child-node:after {
  top: 50%;
  border-top: 1px solid #e8eaf3;
}

/*从单个子节点的顶部移除空格*/
.horizontal.one-branch > .org-chart-node {
  padding-left: 0;
}

/*从第一个子节点移除左连接器，从最后一个子节点移除右连接器*/
.horizontal .org-chart-node:first-child::before,
.horizontal .org-chart-node:last-child::after {
  border: 0 none;
}
/*将垂直连接器添加回最后的节点*/
.horizontal
  .org-chart-node:not(.is-left-child-node):not(.is-not-child):last-child::before {
  border-bottom: 1px solid #e8eaf3;
  border-radius: 0 0px 0 5px;
}
.horizontal .is-left-child-node:last-child::before {
  border-bottom: 1px solid #e8eaf3;
  border-radius: 0 0px 5px 0px;
}

.horizontal .org-chart-node:only-child::before {
  border-radius: 0 0px 0 0px !important;
  border-color: #e8eaf3;
}

.horizontal .is-left-child-node.is-leaf::before {
  content: "";
  display: block;
}
.horizontal .is-left-child-node .org-chart-node-label::after {
  display: none;
}

/*从父节点添加向下的连接器了*/
.horizontal .org-chart-node-children::before,
.horizontal .org-chart-node-left-children::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  border-top: 1px solid #e8eaf3;
  width: 12px;
  height: 10px;
}
.horizontal .org-chart-node-children::before {
  width: 20px;
}
.horizontal .org-chart-node-left-children::before {
  left: calc(100% - 11px);
}
.horizontal > .only-both-tree-node > .org-chart-node-left-children::before {
  display: none;
}

.horizontal .org-chart-node-label {
  position: relative;
  display: inline-block;
}

.horizontal .org-chart-node-label .org-chart-node-btn {
  border: 1px solid #687087;
  border-radius: 50%;
  cursor: pointer;
}

.horizontal .org-chart-node-label .org-chart-node-btn::before,
.horizontal .org-chart-node-label .org-chart-node-left-btn::before {
  top: 50%;
  left: 4px;
  right: 3px;
  border-top: 1px solid #687087;
  height: 0;
  transform: translateY(-50%);
}

.horizontal .org-chart-node-label .org-chart-node-left-btn {
  border: 1px solid #e8eaf3;
}

.horizontal .org-chart-node.collapsed .org-chart-node-label::after,
.horizontal .is-left-child-node.collapsed .org-chart-node-label::before {
  content: "";
  border-bottom: 1px solid #e8eaf3;
}

.org-chart-node-children .org-chart-node-btn-text {
  color: #687087;
  z-index: 2;
}
</style>
