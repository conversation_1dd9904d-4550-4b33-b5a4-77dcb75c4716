<template>
  <!-- -->
  <div :style="{ width: '1200px', height: '700px' }" />
</template>

<script>
// import * as echarts from "echarts";
// require("echarts/theme/macarons");
// import { debounce } from "@/utils";

function debounce(fn, delay) {
  let timer;
  return function() {
    let th = this;
    let args = arguments;
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(function() {
      timer = null;
      fn.apply(th, args);
    }, delay);
  };
}

export default {
  props: {
    width: {
      type: String,
      default: "1200px"
    },
    height: {
      type: String,
      default: "100%"
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      }
    }
  },
  mounted() {
    this.initChart();
    //是否需要自适应-加了防抖函数
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize();
      }
    }, 100);

    window.onresize = () => {
      return (() => {
        this.clientWidth = document.body.clientWidth + "px";
        console.log(this.clientWidth);
      })();
    };

    window.addEventListener("resize", this.__resizeHandler);
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    window.removeEventListener("resize", this.__resizeHandler);
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons", { renderer: "svg" });
      this.setOptions(this.chartData);
      const nodes = this.chart._chartsViews[0]._data._graphicEls;
      let allNode = 0;
      for (let index = 0; index < nodes.length; index++) {
        const node = nodes[index];
        if (node === undefined) {
          continue;
        }
        allNode++;
      }
 
      this.chart.resize();

      this.chart.on("click", params => {
        console.log("params=====", params.data.indicatorId);
        this.$emit("getDetail", params.data.indicatorId);
      }); //节点点击事件
    },
    setOptions(data) {
      this.chart.setOption({
        //提供数据视图、还原、下载的工具
        // toolbox: {
        //   show: true,
        //   feature: {
        //     mark: { show: true },
        //     dataView: { show: true, readOnly: false },
        //     restore: { show: true },
        //     saveAsImage: { show: true }
        //   }
        // },
        series: [
          {
            name: "tree",
            type: "tree",
            orient: "LR", //竖向或水平   TB代表竖向  LR代表水平
            // top: "10%",
            edgeShape: "polyline",
            initialTreeDepth: 2, //树图初始展开的层级（深度）
            expandAndCollapse: true, //点击节点时不收起子节点，default: true
            // symbolSize: [135, 65],
            itemStyle: {
              color: "transparent",
              borderWidth: 0
            },
            lineStyle: {
              color: "#D5D5D5",
              width: 1,
              curveness: 1
            },
            data: [data]
          }
        ]
      });
    },
    sidebarResizeHandler(e) {
      console.log(e);
      if (e.propertyName === "width") {
        this.__resizeHandler();
      }
    }
  }
};
</script>
