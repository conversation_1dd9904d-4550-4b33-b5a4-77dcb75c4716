import personalPerformance from '../pages/personalPerformance/router';
import launchAssessment from '../pages/launchAssessment/router';
const manageList = () => import('../pages/performanceManage/index.vue');
const setting = () => import('../pages/performanceManage/setting.vue');
const checkSettingDetail = () =>
  import('../pages/performanceManage/checkDetail/index.vue');

const inspectionSetup = () => import('../pages/inspectionSetup/index.vue');
const libraryList = () => import('../pages/IndicatorsLibrary/list.vue');
const libraryAdd = () => import('../pages/IndicatorsLibrary/add.vue');
const profileList = () => import('../pages/performanceProfile/list.vue');
const profileDetail = () => import('../pages/performanceProfile/detail.vue');
const startCheck = () => import('../pages/performanceStart/startCheck.vue');
const checkDetail = () => import('../pages/performanceStart/checkDetail.vue');
const checkPlanDetail = () =>
  import('../pages/performanceStart/checkPlanDetail.vue');
// const changeProgress = () => import('../pages/performanceStart/components/changeProgress.vue');
import postTemplate from '../pages/postTemplate/index.vue'
import postTemplateDetail from '../pages/postTemplate/detail.vue'
import postTemplateshowDetail from '../pages/postTemplate/showDetail.vue'
import mapIndex from '../pages/targetMap/index';

export default [
  {
    path: '/performance/manage',
    component: manageList,
    meta: {
      businessCode: 'kpi.performance.plan',
      icon: 'iconjixiaokaohe'
    }
  },
  {
    path: '/targetMap/index',
    component: mapIndex,
    meta: {
      businessCode: 'kpi.performance.targetMap',
      icon: 'iconjixiaokaohe'
    }
  },
  {
    path: '/performance/setting',
    component: setting,
    // meta: {
    //   businessCode: 'salary.performance.setting',
    //   icon: 'icondaka'
    // }
  },
  {
    path: '/performance/checkSetting',
    meta: {
      menu: false
    },
    component: checkSettingDetail
  },
  {
    path: '/performance/startCheck',
    component: startCheck,
    // meta: {
    //   businessCode: 'salary.performance.startCheck',
    //   icon: 'icondaka'
    // }
  },
  {
    path: '/performance/inspectionSetup',
    component: inspectionSetup,
    meta: {
      businessCode: 'kpi.performance.planSetting'
    }
  },
  {
    path: '/performance/checkDetail',
    meta: {
      menu: false
    },
    component: checkDetail,
    // meta: {
    //   businessCode: 'salary.performance.checkDetail',
    //   icon: 'icondaka'
    // }
  },
  {
    path: '/performance/checkPlanDetail',
    meta: {
      menu: false
    },
    component: checkPlanDetail,
    // meta: {
    //   businessCode: 'salary.performance.checkPlanDetail',
    //   icon: 'icondaka'
    // }
  },
  {
    path: '/performance/libraryList',
    component: libraryList,
    meta: {
      businessCode: 'kpi.performance.indicatorBank',
      icon: 'iconjixiaokaohe'
    }
  },
  {
    path: '/performance/libraryAdd',
    component: libraryAdd,
    // meta: {
    //   businessCode: 'salary.performance.libraryAdd',
    //   icon: 'icondaka'
    // }
  },
  {
    path: '/performance/profileList',
    component: profileList,
    meta: {
      businessCode: 'kpi.performance.archives',
      icon: 'iconjixiaokaohe'
    }
  },
  {
    path: '/performance/profileDetail',
    component: profileDetail,
    // meta: {
    //   businessCode: 'salary.performance.profileDetail',
    //   icon: 'icondaka'
    // }
  },
  {
    path: '/postTemplate/index',
    component: postTemplate,
    meta: {
      businessCode: 'kpi.performance.positionBank',
      icon: 'iconjixiaokaohe'
    }
  },
  {
    path: '/postTemplate/detail',
    meta: {
      menu: false
    },
    component: postTemplateDetail,
    // meta: {
    //   businessCode: 'salary.performance.postTemplateDetail',
    //   icon: 'icondaka'
    // }
  },
  {
    path: '/postTemplate/showDetail',
    component: postTemplateshowDetail,
    // meta: {
    //   businessCode: 'salary.performance.postTemplateshowDetail',
    //   icon: 'icondaka'
    // }
  },
  // {
  //   path: '/performance/changeProgress',
  //   component: changeProgress,
  //   meta: {
  //     businessCode: 'salary.performance.changeProgress',
  //     icon: 'icondaka'
  //   }
  // },
  ...personalPerformance,
  ...launchAssessment
];
