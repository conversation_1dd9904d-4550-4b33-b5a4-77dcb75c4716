import { get, post } from 'performance/utils/fetch.js';

/**
 * 个人绩效
 * 发起考核确认
 */

// 我的全部考核
const getMyPlanList = param => post('/api/kpi/my/plan/list', param);
// 我的绩效详情
const getMyPlanDetail = param => get('/api/kpi/my/plan/detail', param);
// 考核计划详情
const getPlanDetail = param => get('/api/kpi/plan/detail', param);
// 考核计划详情列表
const getPlanDetailList = param => post('/api/kpi/plan/detailList', param);
// 待我考核-审核列表
const getMyPlanTodoApproveList = param =>
  post('/api/kpi/my/plan/todo/approveList', param);
// 待我考核-确认列表
const getMyPlanTodoConfirmList = param =>
  post('/api/kpi/my/plan/todo/confirmList', param);
// 待我考核-评分列表
const getMyPlanTodoScoreList = param =>
  post('/api/kpi/my/plan/todo/scoreList', param);
// 待我考核-录入实际完成值列表
const getMyPlanTodoInputList = param =>
  post('/api/kpi/my/plan/todo/inputList', param);
// 考核确认-部门树
const getDepartmentTreeConfirm = param =>
  get('/api/kpi/department/tree', param);
// 考核确认-发起考核确认-检查数据
const getPlanStartConfirmCheck = param =>
  get('/api/kpi/plan/startConfirm/check', param);
// 考核确认-发起考核确认
const getPlanStartConfirm = param => get('/api/kpi/plan/startConfirm', param);
// 修改流程-获取流程数据
const getExamineePlanProcess = param =>
  get('/api/kpi/examinee/plan/process', param);
// 修改流程-修改流程数据
const apiExamineePlanProcess = param =>
  post('/api/kpi/examinee/plan/process', param);
// 考核确认-移除考核对象
const getExamineePlanRemove = param =>
  get('/api/kpi/examinee/plan/remove', param);
// 我的绩效-最近一次详情
const getMyPlanLast = param => get('/api/kpi/my/plan/last', param);
// 考核对象详情列表中的详情 - 考核对象详情
const getExamineePlanDetail = param =>
  get('/api/kpi/examinee/plan/detail', param);
// 待我考核 - 待办统计值
const getMyPlanTodoStatistics = param =>
  get('/api/kpi/my/plan/todo/statistics', param);
// 待我考核 - 我的待确认考核表数量
const getMyPlanTodoConfirmCount = param =>
  get('/api/kpi/my/plan/todo/confirmCount', param);
// 待我考核 - 确认审核
const getMyPlanTodoApprove = param =>
  get('/api/kpi/my/plan/todo/approve', param);
// 待我考核 - 确认考核
const getMyPlanTodoConfirm = param =>
  post('/api/kpi/my/plan/todo/confirm', param);
// 待我考核 - 评分
const getMyPlanTodoSetScore = param =>
  post('/api/kpi/my/plan/todo/setScore', param);
// 待我考核 - 待我录入完成值指标列表
const getMyPlanTodoCompleteValueList = param =>
  post('/api/kpi/my/plan/todo/completeValueList', param);
// 待我考核 - 更新实际完成值
const getMyPlanTodoSetCompleteValue = param =>
  post('/api/kpi/my/plan/todo/setCompleteValue', param);

//************ 考核管理 (杨)*****************
//查询考核考核管理（计划）列表
const getPlanList = param => post('/api/kpi/plan/list', param);

//子公司（用工主体）
const getSubsidiaryList = param => get('/api/kpi/subsidiary/list', param);

//新增考核计划 (基础设置)
const setPlanAdd = param => post('/api/kpi/plan/add', param);

//新增考核计划 (基础设置)
const setBasic = param => post('/api/kpi/plan/setBasic', param);

//编辑考核计划 (基础设置)
const updatePlanAdd = param => post('/api/kpi/plan/update', param);

//考核计划 (指标设置)
const setPlanIndicator = param => post('/api/kpi/plan/setIndicator', param);

//考核计划 （流程设置）
const setPlanProcess = param => post('/api/kpi/plan/setProcess', param);

//考核计划 (结果设置)
const setPlanResult = param => post('/api/kpi/plan/setResult', param);

//查询考核指标列表
const getIndicatorList = param => post('/api/kpi/indicator/list', param);

//获取考核指标库分组树

const getIndicatorTree = param => get('/api/kpi/indicator/bank/tree', param);

//新增考核指标分组
const addIndicatorTree = param =>
  post('/api/kpi/indicator/bank/add', param).then(res => res);

//删除考核指标分组
const removeIndicatorTree = param =>
  get('/api/kpi/indicator/bank/remove', param).then(res => res);
//删除考核指标分组
const updateIndicatorTree = param =>
  post('/api/kpi/indicator/bank/update', param).then(res => res);

//获取员工树
const getEmployeeTree = param =>
  get('/api/kpi/employee/tree', param).then(res => res);

//获取部门树
const getDepartmentTree = param =>
  get('/api/kpi/department/tree', param).then(res => res);

//新增指标
const IndicatorAdd = param =>
  post('/api/kpi/indicator/add', param).then(res => res);

//考核基本信息 (wx）
const getPlanBaseInfo = param =>
  get('/api/kpi/plan/baseInfo', param).then(res => res);

//考核指标 (wx)

const getPlanIndicator = param =>
  get('/api/kpi/plan/indicator', param).then(res => res);

//流程数据 (wx)
const getPlanProcess = param =>
  get('/api/kpi/plan/process', param).then(res => res);

//结果设置 (wx)
const getPlanResult = param =>
  get('/api/kpi/plan/resultSetting', param).then(res => res);
//删除指标
const IndicatorRemove = param =>
  get('/api/kpi/indicator/remove', param).then(res => res);
//************ 启动考核 *****************
//启动考核
const getStart = param => get('/api/kpi/plan/start', param);
//启动考核-检查数据
const getCheck = param => get('/api/kpi/plan/start/check', param);
//移除考核对象
const getRemove = param => get('/api/kpi/examinee/plan/remove', param);

//修改流程 - 获取流程数据
const getProcess = param => get('/api/kpi/examinee/plan/process', param);
//修改流程 - 提交流程数据
const postProcess = param => post('/api/kpi/examinee/plan/process', param);

//获取评分等级列表
const getLevel = param => get('/api/kpi/plan/level', param);
const getCompleteValueNum = param =>
  get('/api/kpi/plan/completeValueNum', param);

//变更实际完成值
const getChangeCompleteValue = param =>
  post('/api/kpi/examinee/plan/changeCompleteValue', param);
//代录入实际完成值
const getReplaceCompleteValue = param =>
  post('/api/kpi/examinee/plan/replaceCompleteValue', param);
//考核对象详情列表中的详情 - 考核对象详情
const getCheckdetail = param => get('/api/kpi/examinee/plan/detail', param);
//发放考核结果
const getSendResult = param => get('/api/kpi/plan/sendResult', param);
//检测考核结果
const getCheckResult = param => get('/api/kpi/plan/checkResult', param);
//未完成考核数量
const getUnCompletedCount = param =>
  get('/api/kpi/plan/getUnCompletedCount', param);
//确认完成
const getComplete = param => get('/api/kpi/plan/complete', param);

//发放单个考核对象结果
const getSendOneResult = param =>
  get('/api/kpi/examinee/plan/sendResult', param);
//查询指标详情
const getIndicatorDetail = param => get('/api/kpi/indicator/detail', param);
//编辑指标详情
const updateIndicatorDetail = param => post('/api/kpi/indicator/update', param);

//删除考核计划
const planRemove = param => get('/api/kpi/plan/remove', param);

//获取考核设置
const getLevelSetting = param => get('/api/kpi/plan/setting/getLevel', param);

//更新考核设置
const updateLevelSetting = param =>
  post('/api/kpi/plan/setting/updateLevel', param);

//指标查重
const indicatorCheckName = param => get('/api/kpi/indicator/checkName', param);
const getIntegrity = param => get('/api/kpi/plan/integrity', param);

// 关联父考核指标 - 获取父指标列表
const getParent = param => post('/api/kpi/examinee/indicator/relation/list', param);
/**
 *  ================================= 目标地图相关接口 (wx) =================================
 */

//筛选条件获取部门树
const getMapDepartment = param =>
  get('/api/kpi/target/map/condition/department', param);

//筛选条件获取员工树
const getMapEmployee = param =>
  get('/api/kpi/target/map/condition/employee', param);

//筛选条件获取用工主体列表
const getMapSubsidiary = param =>
  get('/api/kpi/target/map/condition/subsidiary', param);

//按部门查看
const checkMapDepartment = param =>
  post('/api/kpi/target/map/department', param);

//按个人查看
const checkMapPersonal = param => post('/api/kpi/target/map/personal', param);

//按公司查看
const checkMapSubsidiary = param =>
  post('/api/kpi/target/map/subsidiary', param);

//获取指标详情数据
const getMapDetail = param => get('/api/kpi/target/map/detail', param);

//获取个人绩效档案列表
const getarchivesList = param => post('/api/kpi/plan/archives/list', param);

//获取个人绩效档案详情
const getarchivesDetail = param => get('/api/kpi/employee/detail', param);

//获取个人绩效档案列表
const getemployeeList = param => post('/api/kpi/plan/employee/list', param);

//获取岗位模板库列表
const gePositionList = param => post('/api/kpi/position/indicator/bank/list', param);

//添加岗位模板库
const gePositionAdd = param => post('/api/kpi/position/indicator/bank/add', param);

//更新·岗位模板库
const gePositionUpdate = param => post('/api/kpi/position/indicator/bank/update', param);

//岗位模板库名称查重
const gePositionCheckName = param => get('/api/kpi/position/indicator/bank/checkName', param);

//获取岗位模板详情
const gePositionDetail = param => get('/api/kpi/position/indicator/bank/detail', param);

//删除岗位模板
const gePositionRemove = param => get('/api/kpi/position/indicator/bank/remove', param);

//获取人员列表
const getUserList = param => get('/api/kpi/employee/list', param);

export {
  getMyPlanList,
  getMyPlanDetail,
  getPlanList,
  getPlanDetail,
  getSubsidiaryList,
  setPlanAdd,
  setPlanIndicator,
  getIndicatorList,
  getIndicatorTree,
  getPlanDetailList,
  getMyPlanTodoApproveList,
  getMyPlanTodoConfirmList,
  getMyPlanTodoScoreList,
  getMyPlanTodoInputList,
  getDepartmentTreeConfirm,
  getPlanStartConfirmCheck,
  getPlanStartConfirm,
  getExamineePlanProcess,
  apiExamineePlanProcess,
  getExamineePlanRemove,
  getPlanBaseInfo,
  addIndicatorTree,
  removeIndicatorTree,
  updateIndicatorTree,
  getEmployeeTree,
  IndicatorAdd,
  setPlanResult,
  getDepartmentTree,
  setPlanProcess,
  getPlanIndicator,
  getPlanProcess,
  getPlanResult,
  IndicatorRemove,
  getStart,
  getCheck,
  getRemove,
  getProcess,
  postProcess,
  getChangeCompleteValue,
  getReplaceCompleteValue,
  getCheckdetail,
  getSendResult,
  getCheckResult,
  getSendOneResult,
  getMyPlanLast,
  getExamineePlanDetail,
  getIndicatorDetail,
  updateIndicatorDetail,
  updatePlanAdd,
  planRemove,
  getLevelSetting,
  updateLevelSetting,
  getIntegrity,
  getMyPlanTodoStatistics,
  getMyPlanTodoConfirmCount,
  getLevel,
  getUnCompletedCount,
  getComplete,
  getMyPlanTodoApprove,
  getMyPlanTodoConfirm,
  getMyPlanTodoSetScore,
  getMyPlanTodoCompleteValueList,
  getMyPlanTodoSetCompleteValue,
  indicatorCheckName,
  getCompleteValueNum,
  setBasic,
  getParent,
  getMapDepartment,
  getMapEmployee,
  getMapSubsidiary,
  checkMapDepartment,
  checkMapPersonal,
  checkMapSubsidiary,
  getMapDetail,
  getarchivesList,
  getarchivesDetail,
  getemployeeList,
  gePositionList,
  gePositionAdd,
  gePositionUpdate,
  gePositionCheckName,
  gePositionDetail,
  gePositionRemove,
  getUserList
};
