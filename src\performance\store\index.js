import Vue from 'vue';
import Vuex from 'vuex';
import mutations from './mutations';

Vue.use(Vuex);
export default new Vuex.Store({
  namespaced: true,
  state: {
    librarySearchForm: {}, //将列表页当前查询条件缓存，用于详情页
    baseInfo: {}, //基础设置
    serchForm: {}, //考核管理列表查询缓存
    searchFormMine: {}, // 我的绩效 查询条件缓存 
    searchFormWait: {}, // 待我考核 查询条件缓存 
    searchFormRecord:{},//个人绩效档案查询条件
    searchFormTemplate:{},//岗位模板库查询条件
  },
  mutations
});
