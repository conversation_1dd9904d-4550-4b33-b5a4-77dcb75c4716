import * as PA from './actionTypes';

export default {
  [PA.SET_LIBRARYSEARCHFORM](state, librarySearchForm) {
    state.librarySearchForm = librarySearchForm;
  },
  [PA.SET_BASEINFO](state, payload) {
    state.baseInfo = payload;
  },
  [PA.SET_SEARCHFORM](state, payload) {
    state.serchForm = payload;
  },
  [PA.SET_SEARCHFORMMINE](state, payload) {
    state.searchFormMine = payload;
  },
  [PA.SET_SEARCHFORMWAIT](state, payload) {
    state.searchFormWait = payload;
  },
  [PA.SET_SEARCHFORMRECORD](state, payload) {
    state.searchFormRecord = payload;
  },
  [PA.SET_SEARCHFORMTEMPLATE](state, payload) {
    state.searchFormTemplate = payload;
  }
};
