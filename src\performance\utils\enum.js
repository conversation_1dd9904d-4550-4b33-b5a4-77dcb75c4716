//考核周期
export const khzqPeriodType = {
  1:"年度",
  2:"半年度",
  3:"季度",
  4:"月度",
  5:"自定义"
}
//考核类型
export const khlxType = {
  1:"公司考核",
  2:"部门考核",
  3:"个人考核",
}
//人员状态
export const ryztStatus = {
  1:"在职",
  2:"离职",
  3:"已删除",
  4:"已调岗",
  5:"平台账户异常",
  6:"无"
}
//人员状态2
export const ryztStatus2 = {
  1:"(在职)",
  2:"(已离职)",
  3:"(已删除)",
  4:"(已调岗)",
  5:"(平台账户异常)",
  // 5:"(用户不存在平台ID)",
  6:"(无)"
}
// 考核评分状态
export const handleScoreStatus = {
  1:"待评分",
  2:"评分中",
  3:"已评分",
}
// 考核结果审核状态
export const handleApproveStatus = {
  1:"待审核",
  2:"审核中",
  3:"已审核",
}
//考核计划列表类型
export const khjhlbType = {
  1:"考核确认列表",
  2:"启动考核列表",
  3:"审核列表",
}
//流程节点类型描述
export const processorType = {
  1:"被考核者",
  2:"直接上级",
  3:"指定人员",
}
//考核确认状态
export const khqrztStatus = {
  1:"未开始",
  2:"进行中",
  3:"已完成",
}
//节点执行状态
export const jdzxztNodetype = {
  1:"待处理",
  2:"已处理",
}
//指标类型
export const zblxType = {
  1:"定量考核指标",
  2:"定性考核指标",
  3:"加分项",
  4:"减分项",
}
//评分方式
export const pffsScoreType = {
  1:"直接输入",
  2:"公式计算得分",
}
//确认流程节点状态
export const khqrjdztStatus = {
  1:"处理中",
  2:"等待处理",
  3:"已处理",
}
//评分流程节点状态
export const khpfjdztStatus = {
  1:"处理中",
  2:"等待处理",
  3:"已处理",
}
//考核计划
export const khjhStatus = {
  1:"未开始",
  2:"进行中",
  3:"已完成",
}
//进度条状态
export const jdtType = {
  1:"处理中",
  2:"等待处理",
  3:"已处理",
}
//考核确认状态
export const khqrStatus = {
  1: "未开始", 
  2: "确认中" ,
  3: "已确认"
}
//评分类型
export const pfType = {
  1: "自评", 
  2: "上级评分",
  3: "他人评分"
}
export const stageStatus = {
  1:"待确认",
  2:"确认中",
  3:"已确认",
  4:"待评分",
  5:"评分中",
  6:"已评分",
  7:"待审核",
  8:"审核中",
  9:"已审核"
}
export const otherStatus = {
  1:"处理中",
  2:"等待处理",
  3:"已处理",
}


