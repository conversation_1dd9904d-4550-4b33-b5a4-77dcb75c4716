import axios from 'axios';
import { Message } from 'element-ui';
//导航统一获取token
import {getToken} from "@olading/olading-business-ui";

var defaultHeader = {
  Accept: 'application/json',
  'Content-Type': 'application/json'
};

// const env = process.env.NODE_ENV == "development" ? '/jx/' : ''
const instance = axios.create({
  timeout: 180000,
  headers: defaultHeader,
  withCredentials: true,
  baseURL: window.env.apiPath
});

// 请求拦截
instance.interceptors.request.use(
  config => {
    config.headers['Authorization'] = getToken();
    config.headers['AgentClientDomain'] = location.hostname;
    return config;
  },
  err => {
    return Promise.reject(err);
  }
);
// 响应拦截
instance.interceptors.response.use(
  response => {
    if (response.config.responseType === 'blob') {
    } else {
      if (!response.data.success) {
        Message.error(response.data.msg || '接口错误');
        return Promise.reject(response.data);
      }
    }
    return response;
  },
  error => {
    if (error) {
      console.error(error);
    }
    return Promise.reject(error);
  }
);

function request(url, params, headers = {}, method, blob) {
  return new Promise((resolve, reject) => {
    let data = {};
    if (method == 'get') data = { params: params };
    if (method == 'post') data = { data: params };
    instance({
      url,
      method,
      headers,
      ...data,
      ...blob
    })
      .then(res => {
        resolve(res.data);
      })
      .catch(err => {
        reject(err);
      });
  });
}
function get(url, params, headers) {
  return request(url, params, headers, 'get');
}
function post(url, params, headers) {
  return request(url, params, headers, 'post');
}
function down(url, params, headers = {}) {
  return request(url, params, headers, 'post', { responseType: 'blob' });
}
export { get, post, down };
