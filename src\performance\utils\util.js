const dateObj = {
  '01-01-03-31': '第一季度',
  '04-01-06-30': '第二季度',
  '07-01-09-30': '第三季度',
  '10-01-12-31': '第四季度',
  '01-01-06-30': '上半年',
  '07-01-12-31': '下半年'
};

const tt2Time = (timestamp, type) => {
  let date = new Date(timestamp);
  let Y = date.getFullYear();
  let M =
    date.getMonth() + 1 < 10
      ? '0' + (date.getMonth() + 1)
      : date.getMonth() + 1;
  let D = date.getDate() + 1 < 10 ? '0' + date.getDate() : date.getDate();
  var str = '';
  switch (type) {
    case 'Y':
      str = Y;
      break;
    case 'M':
      str = M;
      break;
    case 'M-D':
      str = M + '-' + D;
      break;
    default:
      str = Y + '-' + M + '-' + D;
      break;
  }
  return str;
};

/**
 * 时间戳转周期
 * @param {Number} type 周期类型
 * @param {String} start 开始时间
 * @param {String} end   结束时间
 * @returns
 */

export function date2Str(type, start, end) {
  var str = '';
  switch (type) {
    case 1:
      str = `${tt2Time(start, 'Y')}年`;
      break;
    case 4:
      str = `${tt2Time(start, 'Y')}年${tt2Time(start, 'M')}月`;
      break;
    case 5:
      str = tt2Time(start) + '至' + tt2Time(end);
      break;
    default: {
      str = `${tt2Time(start, 'Y')}年${
        dateObj[tt2Time(start, 'M-D') + '-' + tt2Time(end, 'M-D')]
      }`;

      break;
    }
  }
  return str;
}

/**
 *
 * @param {Array} arr 需计算数组
 * @returns
 */

export function sumCount(arr) {
  return arr.reduce(function(prev, curr, idx, arr) {
    return prev + curr;
  });
}

/**
 *
 * @param {Array} arr
 * @param {String} name 去重标识
 * @returns
 */

export function arrayUnique(arr, name) {
  let map = new Map();
  for (let item of arr) {
    if (!map.has(item[name])) {
      map.set(item[name], item);
    }
  }
  return [...map.values()];
}

export function havePrivilege(key) {
  const privilegeVoList =
  JSON.parse(sessionStorage.getItem('xst/vuex')).privilegeVoList || [];
  return privilegeVoList.includes(key);
}
