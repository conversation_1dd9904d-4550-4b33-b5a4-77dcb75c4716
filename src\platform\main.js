import 'kit/assets/themes/element.ui.css'
import 'kit/assets/iconfont/iconfont.css'
import { setTheme } from 'kit/assets/themes'
import App from './app.vue'
import Vue from 'vue'
import ElementUI from 'element-ui'
import VueRouter from 'vue-router'
import routes from './routes'

Vue.use(ElementUI)
Vue.use(VueRouter)

const theme = window.env.theme || 'default'
setTheme(theme)

const router = new VueRouter({
  mode: 'history',
  base: '/operate-labor/platform/',
  routes
})

router.beforeEach((to, from, next) => {
  document.title = to.meta.title
  next()
})

const vStore = null //为了避免类型检查
new Vue({
  el: '#app',
  router,
  vStore,
  render: h => h(App)
}).$mount()
