<template>
  <div class="menus">
    <div
      style="
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eee;
        padding-left: 12px;
        height: 60px;
      "
    >
      <div style="flex: 1; margin-left: 5px">{{ navigation.title }}</div>
    </div>

    <el-menu
      :default-active="activeMenu"
      style="border-right: none"
      :router="true"
    >
      <template v-for="(item, index) in navigation.children">
        <el-submenu
          v-if="item.children && item.children.length"
          :key="index"
          :index="item.path"
        >
          <template slot="title">
            <span>{{ item.title }}</span>
          </template>
          <el-menu-item
            v-for="(subItem, subIndex) in item.children"
            :key="subIndex"
            :index="subItem.path"
            :route="subItem.path"
          >
            {{ subItem.title }}
          </el-menu-item>
        </el-submenu>
        <el-menu-item v-else :key="index" :index="item.path" :route="item.path">
          {{ item.title }}
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script>
export default {
  props: {
    navigation: Object
  },
  data() {
    return {
      currentUser: {
        name: ''
      },
      activeMenu: this.$route.path
    }
  },
  watch: {
    '$route.path': function (newPath) {
      this.activeMenu = newPath
    }
  },
  created() {
    this.fetchUserDetail()
  },
  methods: {
    async fetchUserDetail() {
      const userInfoString = sessionStorage.getItem('userInfo')

      if (userInfoString) {
        const userInfo = JSON.parse(userInfoString)
        const body = { kxyUserId: userInfo.kxyUserId }

        try {
          const response = await fetch('/api/boss/detailLoggedInUser', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: 'KXY a-b-c-d'
            },
            body: JSON.stringify(body)
          })
          if (!response.ok) throw new Error('Network response was not ok.')
          const result = await response.json()

          this.currentUser = result.user
        } catch (error) {
          console.error('获取详细用户信息时出错:', error)
        }
      }
    }
  }
}
</script>

<style></style>
