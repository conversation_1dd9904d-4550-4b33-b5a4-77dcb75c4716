<template>
  <div class="menus">
    <div
      style="
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eee;
        padding-left: 12px;
        height: 60px;
      "
    >
      <div style="flex: 1; margin-left: 5px">{{ navigation.title }}</div>
    </div>

    <el-menu
      :default-active="defaultActive"
      style="border-right: none"
      :router="true"
    >
      <el-menu-item
        index="/roles"
        route="/roles"
        v-for="(item, index) in navigation.children"
        :key="index"
      >
        {{ item.title }}
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script>
export default {
  props: {
    navigation: Object
  },
  data() {
    return {
      currentUser: {
        name: ''
      },
      defaultActive: '/roles'
    }
  },
  created() {
    const index = this.$route.path.indexOf('/', 1)
    this.defaultActive = this.$route.path
    if (index !== -1) {
      this.defaultActive = this.$route.path.substring(0, index)
    }

    this.fetchUserDetail()
  },
  methods: {
    async fetchUserDetail() {
      const userInfoString = sessionStorage.getItem('userInfo')

      if (userInfoString) {
        const userInfo = JSON.parse(userInfoString)
        const body = { kxyUserId: userInfo.kxyUserId }

        try {
          const response = await fetch('/api/boss/detailLoggedInUser', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: 'KXY a-b-c-d'
            },
            body: JSON.stringify(body)
          })
          if (!response.ok) throw new Error('Network response was not ok.')
          const result = await response.json()

          this.currentUser = result.user
        } catch (error) {
          console.error('获取详细用户信息时出错:', error)
        }
      }
    }
  }
}
</script>

<style></style>
