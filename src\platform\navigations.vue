<template>
  <div
    class="navigations"
    style="
      display: flex;
      height: 64px;
      overflow: hidden;
      border-bottom: 1px solid #eee;
      border-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    "
  >
    <div
      class="item"
      style="
        display: flex;
        align-items: center;
        gap: 5px;
        flex: 0 0 150px;
        padding-left: 14px;
      "
    >
      <img :src="operateLaborLogo" style="height: 36px" />
      <span style="font-size: 18px; font-weight: 500">灵工系统</span>
    </div>
    <div
      class="item"
      :class="{ active: activeIndex === index }"
      v-for="(item, index) in navigations"
      :key="index"
      @click="
        () => {
          activeIndex = index
          $emit('change', item)
        }
      "
    >
      {{ item.title }}
    </div>
    <div
      class="user-area"
      style="
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-right: 24px;
      "
    >
      <el-dropdown
        @command="handleCommand"
        trigger="click"
        style="cursor: pointer"
      >
        <div class="user-avatar">
          <el-avatar :size="32" icon="el-icon-user-solid"></el-avatar>
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="accountSettings">
            <span>账户管理</span>
            <i
              class="el-icon-arrow-right"
              style="margin-left: 8px; color: #909399"
            ></i>
          </el-dropdown-item>
          <el-dropdown-item divided command="logout">
            <span>退出登录</span>
            <i
              class="el-icon-arrow-right"
              style="margin-left: 8px; color: #909399"
            ></i>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { removeToken } from 'kit/helpers/token'
import operateLaborLogo from './operateLaborLogo.png'
export default {
  props: {
    navigations: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeIndex: 0,
      operateLaborLogo
    }
  },
  methods: {
    handleCommand(command) {
      if (command === 'accountSettings') {
        this.$router.push('/accountSettings')
      } else if (command === 'logout') {
        removeToken()
        this.$router.push('/login')
      }
    }
  }
}
</script>

<style scoped>
.item {
  flex: 0 0 100px;
  line-height: 62px;
  padding: 0 10px;
  cursor: pointer;
  text-align: center;
}
.item.active {
  border-bottom: 2px solid var(--o-primary-color);
}
</style>
