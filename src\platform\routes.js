import Index from 'kit/pages/operateLabor/platform/index.vue'
import Login from 'kit/pages/operateLabor/platform/login.vue'
import LoginWithCaptcha from 'kit/pages/operateLabor/platform/loginWithCaptcha.vue'
import Register from 'kit/pages/operateLabor/platform/register.vue'
import FindPassword from 'kit/pages/operateLabor/platform/findPassword.vue'
import Roles from 'kit/pages/operateLabor/platform/roles.vue'
import RolesNew from 'kit/pages/operateLabor/platform/rolesNew.vue'
import SupplierCustomers from 'kit/pages/operateLabor/platform/supplierCustomers.vue'
import SupplierCustomersNew from 'kit/pages/operateLabor/platform/supplierCustomersNew.vue'
import SupplierCustomersEdit from 'kit/pages/operateLabor/platform/supplierCustomersEdit.vue'
import SupplierCustomerDetail from 'kit/pages/operateLabor/platform/supplierCustomerDetail.vue'
import ServiceContracts from 'kit/pages/operateLabor/platform/serviceContracts.vue'
import Corporations from 'kit/pages/operateLabor/platform/corporations.vue'
import CorporationsNew from 'kit/pages/operateLabor/platform/corporationsNew.vue'
import ServiceContractsNew from 'kit/pages/operateLabor/platform/serviceContractsNew.vue'
import ServiceContractsEdit from 'kit/pages/operateLabor/platform/serviceContractsEdit.vue'
import ServiceContractDetail from 'kit/pages/operateLabor/platform/serviceContractDetail.vue'
import SupplierUsers from 'kit/pages/operateLabor/platform/supplierUsers.vue'
import SupplierUsersNew from 'kit/pages/operateLabor/platform/supplierUsersNew.vue'
import AccountSettings from 'kit/pages/operateLabor/platform/accountSettings.vue'
import Todo from 'kit/pages/operateLabor/platform/todo.vue'
import SupplierSettings from 'kit/pages/operateLabor/platform/supplierSettings.vue'
import ContractTemplates from 'kit/pages/operateLabor/platform/contractTemplates.vue'
import ContractTemplatesNewStep1 from 'kit/pages/operateLabor/platform/contractTemplatesNew/step1.vue'

const routes = [
  {
    path: '/',
    component: Index,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/login',
    component: Login,
    meta: {
      title: '登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/accountSettings',
    component: AccountSettings,
    meta: {
      title: '账户管理'
    }
  },
  {
    path: '/loginWithCaptcha',
    component: LoginWithCaptcha,
    meta: {
      title: '验证码登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/register',
    component: Register,
    meta: {
      title: '注册',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/findPassword',
    component: FindPassword,
    meta: {
      title: '找回密码',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/roles',
    component: Roles,
    meta: {
      title: '角色管理'
    }
  },
  {
    path: '/roles/new',
    component: RolesNew,
    meta: {
      title: '新建角色'
    }
  },
  {
    path: '/roles/:id/edit',
    component: RolesNew,
    meta: {
      title: '编辑角色'
    }
  },
  {
    path: '/supplierCustomers',
    component: SupplierCustomers,
    meta: {
      title: '客户管理'
    }
  },
  {
    path: '/supplierCustomers/new',
    component: SupplierCustomersNew,
    meta: {
      title: '新增客户'
    }
  },
  {
    path: '/supplierCustomers/:id/edit',
    component: SupplierCustomersEdit,
    meta: {
      title: '编辑客户'
    }
  },
  {
    path: '/supplierCustomers/:id',
    component: SupplierCustomerDetail,
    meta: {
      title: '客户详情'
    }
  },
  {
    path: '/serviceContracts',
    component: ServiceContracts,
    meta: {
      title: '服务合同'
    }
  },
  {
    path: '/corporations',
    component: Corporations,
    meta: {
      title: '业务主体'
    }
  },
  {
    path: '/corporations/new',
    component: CorporationsNew,
    meta: {
      title: '新建业务主体'
    }
  },
  {
    path: '/corporations/:id/edit',
    component: CorporationsNew,
    meta: {
      title: '编辑业务主体'
    }
  },
  {
    path: '/serviceContracts/new',
    component: ServiceContractsNew,
    meta: {
      title: '添加合同'
    }
  },
  {
    path: '/serviceContracts/edit/:id',
    component: ServiceContractsEdit,
    meta: {
      title: '编辑合同'
    }
  },
  {
    path: '/serviceContracts/:id',
    component: ServiceContractDetail,
    meta: {
      title: '合同详情'
    }
  },
  {
    path: '/supplierUsers',
    component: SupplierUsers,
    meta: {
      title: '用户列表'
    }
  },
  {
    path: '/supplierUsers/new',
    component: SupplierUsersNew,
    meta: {
      title: '新建用户'
    }
  },
  {
    path: '/supplierUsers/:id/edit',
    component: SupplierUsersNew,
    meta: {
      title: '编辑用户'
    }
  },
  {
    path: '/todo',
    component: Todo,
    meta: {
      title: '开发中'
    }
  },
  {
    path: '/supplierSettings',
    component: SupplierSettings,
    meta: {
      title: '平台信息设置'
    }
  },
  {
    path: '/contractTemplates',
    component: ContractTemplates,
    meta: {
      title: '合同模板管理'
    }
  },
  {
    path: '/contractTemplates/new/step1',
    component: ContractTemplatesNewStep1,
    meta: {
      title: '新建合同模板'
    }
  },
  {
    path: '/contractTemplates/:id/edit/step1',
    component: ContractTemplatesNewStep1,
    meta: {
      title: '编辑合同模板'
    }
  }
]

export default routes
