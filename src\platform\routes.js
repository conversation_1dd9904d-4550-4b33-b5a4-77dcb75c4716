import Index from 'kit/pages/operateLabor/platform/index.vue'
import Login from 'kit/pages/operateLabor/platform/login.vue'
import LoginWithCaptcha from 'kit/pages/operateLabor/platform/loginWithCaptcha.vue'
import Register from 'kit/pages/operateLabor/platform/register.vue'
import FindPassword from 'kit/pages/operateLabor/platform/findPassword.vue'
import Roles from 'kit/pages/operateLabor/platform/roles.vue'
import RolesNew from 'kit/pages/operateLabor/platform/rolesNew.vue'
import SupplierCustomers from 'kit/pages/operateLabor/platform/supplierCustomers.vue'
import SupplierCustomersNew from 'kit/pages/operateLabor/platform/supplierCustomersNew.vue'
import ServiceContracts from 'kit/pages/operateLabor/platform/serviceContracts.vue'
import Corporations from 'kit/pages/operateLabor/platform/corporations.vue'
import CorporationsNew from 'kit/pages/operateLabor/platform/corporationsNew.vue'

const routes = [
  {
    path: '/',
    component: Index,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/login',
    component: Login,
    meta: {
      title: '登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/loginWithCaptcha',
    component: LoginWithCaptcha,
    meta: {
      title: '验证码登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/register',
    component: Register,
    meta: {
      title: '注册',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/findPassword',
    component: FindPassword,
    meta: {
      title: '找回密码',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/roles',
    component: Roles,
    meta: {
      title: '角色管理'
    }
  },
  {
    path: '/roles/new',
    component: RolesNew,
    meta: {
      title: '新建角色'
    }
  },
  {
    path: '/roles/:id/edit',
    component: RolesNew,
    meta: {
      title: '编辑角色'
    }
  },
  {
    path: '/supplierCustomers',
    component: SupplierCustomers,
    meta: {
      title: '客户管理'
    }
  },
  {
    path: '/supplierCustomers',
    component: SupplierCustomers,
    meta: {
      title: '客户管理'
    }
  },
  {
    path: '/supplierCustomers/new',
    component: SupplierCustomersNew,
    meta: {
      title: '新增客户'
    }
  },
  {
    path: '/supplierCustomers/:id/edit',
    component: SupplierCustomersNew,
    meta: {
      title: '编辑客户'
    }
  },
  {
    path: '/serviceContracts',
    component: ServiceContracts,
    meta: {
      title: '服务合同'
    }
  },
  {
    path: '/corporations',
    component: Corporations,
    meta: {
      title: '业务主体管理'
    }
  },
  {
    path: '/corporations/new',
    component: CorporationsNew,
    meta: {
      title: '新建业务主体'
    }
  },
  {
    path: '/corporations/:id/edit',
    component: CorporationsNew,
    meta: {
      title: '编辑业务主体'
    }
  }
]

export default routes
