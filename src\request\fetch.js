import axios from 'axios';
import { Message, MessageBox } from 'element-ui';
import store from '../store';
import router from '../router';
import { getToken, isError } from '@olading/olading-business-ui';
import autoUpdate from '../util/autoUpdate';
// import {Message} from 'element-ui'

// export const baseUrl = window.env.apiPath;
export const baseUrl =
  process.env.NODE_ENV == 'development' ? '' : window.env.apiPath;

export const baseUrls = window.env.apiPath

const defaultHeader = {
  Accept: 'application/json',
  'Content-Type': 'application/json',
};
const instance = axios.create({
  timeout: 1000*60*3,
  headers: defaultHeader,
  withCredentials: true,
  baseURL: baseUrl,
});
//请求拦截
instance.interceptors.request.use(
  function (config) {
    //每次发送请求之前检测vuex存有token,那么都要放在请求头发送给服务器
    // if (store.state.token) {
    //   config.headers.Authorization = store.state.token;
    // }

    //修改于2022年6月22日，原因为集成导航组件
    let token = getToken();
    if (token) {
      config.headers.Authorization = token;
    }
    autoUpdate(MessageBox.confirm);

    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);
//响应拦截
instance.interceptors.response.use(
  function (config) {
    return config;
  },
  function (error) {
    if (error && error.response) {
      // Message.error('系统出现意外错误，请联系管理员')
    }
    return Promise.reject(error);
  }
);

export function fetch(options, isShowErrorMessage = true) {
  return new Promise((resolve, reject) => {
    instance(options)
      .then((response) => {
        let data = response.data;
        if (data.success) {
          resolve(data);
        } else {
          if (isError(data.errorCode)) {
            resolve(data);
            return false;
          }
          if (data.message) {
            Message.error(data.message);
          }
          resolve(data);
        }

        // if (data.code && data.code != '0000' && isShowErrorMessage) {
        //   Message.error(data.message);
        // } else if (
        //   data.errorCode &&
        //   data.errorCode != '0' &&
        //   isShowErrorMessage
        // ) {
        //   Message.error(data.message);
        // }
        // if (data.errorCode == '2') {
        //   window.location.href = window.env.environmentConfig.sso + '/login';
        // }
        //   let originHref = "";
        //   switch (window.__CURRENT_ENV__) {
        //     case "prod":
        //       originHref='https://www.olading.com/main.html#/';
        //       break;
        //     case "stage":
        //       originHref='https://stage.olading.com/main.html#/';
        //       break;
        //     case "local":
        //       originHref='http://localhost:8081/main.html#/';
        //       break;
        //   }
        //   //未认证跳回登陆
        //   if(data.code === "802"){
        //     window.open(originHref+'login', "_self")
        //   }
        // }
      })
      .catch((error) => {
        // console.log('请求异常信息：' + error)
        reject(error);
      });
  });
}

export function fetch2(options, isShowErrorMessage = true) {
  return new Promise((resolve, reject) => {
    instance(options)
      .then((response) => {
        let data = response.data;
        if (data.success) {
          resolve([null, data]);
        } else {
          resolve([data]);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
}

//导出excel
export function fetchFile(options) {
  return new Promise((resolve, reject) => {
    options.responseType = 'blob';
    instance(options)
      .then((response) => {
        resolve(response);
        let data = response.data;
        let url = window.URL.createObjectURL(data);
        let a = document.createElement('a');
        a.href = url;
        a.download = decodeURI(
          response['headers']['content-disposition'].split(';')[1].split('=')[1]
        );
        a.click();
      })
      .catch((error) => {
        reject(error);
      });
  });
}
