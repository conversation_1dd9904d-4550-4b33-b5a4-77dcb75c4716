// import Vue from 'vue';
import Router from 'vue-router';
import { routerConfig } from './routerConfig';
//redirect
import { routerRedirect } from '@/assets/js/utils/routerRedirect';
//router
import loginRouter from 'modules/login/router';
import selectServer from 'modules/selectServer/router';
import homePage from 'modules/homePage/router';
import salaryCal from 'modules/salaryCal/router';
import tax from 'modules/tax/router';
import taxPaid from 'modules/taxPaid/router';
import initialize from 'modules/initialize/router';
import accountPage from 'modules/account/router';
import payManage from 'modules/payManage/router';
import withdrawal from 'modules/withdrawal/router';
import resultPage from 'modules/result/router';
import socialFund from 'modules/socialFund/router';
import paymaster from 'modules/paymaster/router';
import authorizeZx from 'modules/authorizeZx/router';
import paySalary from 'modules/paySalary/router';
import adjustSalary from 'modules/adjustSalary/router';
import attendance from 'modules/attendance/router';
import powerManage from 'modules/powerManage/router';
import staffManage from 'modules/staffManage/router';
import contractManage from 'modules/contractManage/router';
import openAuth from 'modules/openAuth/router';
import salaryRule from 'modules/salaryRule/router';
import customExport from 'modules/customExport/router';
import errorRouter from 'modules/error/router';
import intelligentAgent from 'modules/intelligentAgent/router';
import signing from '@/signing/router';
import employeeCare from 'modules/employeeCare/router';

import performance from '../performance/router'; //绩效管理路由
import payroll from 'modules/payroll/router'; //电子工资条路由
import intelligentAgentCGB from 'modules/intelligentAgentCGB/router'; //电子工资条路由

import reportForms from 'modules/reportForms/router'; //综合报表
import attendanceMachine from 'modules/attendanceMachine/router'; //考勤机
import xibei from 'modules/xibei/router'; //西贝

// Vue.use(Router);

let router = new Router({
  // base:'/pSalary',
  mode: 'history',
  saveScrollPosition: true,
  scrollBehavior: () => ({
    y: 0,
  }),
  base: window.env.staticPath,
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    ...routerRedirect,
    ...loginRouter,
    ...selectServer,
    ...homePage,
    ...salaryCal,
    ...tax,
    ...taxPaid,
    ...accountPage,
    ...payManage,
    ...withdrawal,
    ...resultPage,
    ...socialFund,
    ...paymaster,
    ...initialize,
    ...authorizeZx,
    ...paySalary,
    ...adjustSalary,
    ...attendance,
    ...powerManage,
    ...staffManage,
    ...contractManage,
    ...openAuth,
    ...salaryRule,
    ...customExport,
    ...errorRouter,
    ...intelligentAgent,
    ...signing,
    ...performance,
    ...employeeCare,
    ...payroll,
    ...reportForms,
    ...intelligentAgentCGB,
    ...attendanceMachine,
    ...xibei,
  ],
});

// routerConfig(router);
export default router;
