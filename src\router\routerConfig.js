/* eslint-disable*/
import store from '../store'
import { mainMenu_base } from "@/assets/js/utils/mainMenu";
import { Loading } from 'element-ui';
let loadingInstance;

// 获取是否是管理员
const handleIsMananger = () => {
  return new Promise((resolve, reject) => {
    store.dispatch("actionIsMananger").then((res) => {
      resolve(res)
    })
  })
}
// 获取是否是免费版
const getProductEdition = () => {
  return new Promise((resolve, reject) => {
    store.dispatch("actionGetIsPaid").then((res) => {
      resolve(res)
    })
  })
}
const fun1 = async () => {
  let productEdition = {
    salary: false,
    contract: false
  }
  //判断是否是付费版
  await getProductEdition().then(res => {
    if (res.success) {
      productEdition = res.data
    }
  })
  // 从工作台产品地图跳转权限控制
  let mainMenu = [], list = [], isMananger = "";
  await handleIsMananger().then(async (data) => {
    isMananger = data.data.mananger
    //权限列表
    await store.dispatch("actionUserPrivilege").then((res) => {
      if (data.data.mananger) {
        mainMenu = mainMenu_base;
      } else {
        let arr = res.data.privilegeGroupTreeVO ? res.data.privilegeGroupTreeVO.children : null;
        arr &&
          arr.forEach((item) => {
            item.children &&
              item.children.forEach((it) => {
                mainMenu.push(it);
              });
          });
      }
    });
  })
  return { mainMenu, list, isMananger, productEdition }
}
export const routerConfig = router => {
  router.beforeEach(async (to, from, next) => {
    loadingInstance = Loading.service();
    // 登录状态验证 权限验证 数据/参数完整性验证 url拦截跳转逻辑 url链路验证 数据埋点
    //与router的meta交互操作当前页 改变title 隐藏导航 等动作
    const { isMananger, mainMenu, list, productEdition } = await fun1()
    if (to.path == '/home') {
      next();
    } else {
      const { businessCode } = to.meta;
      const { urlBusinessCode } = to.query;
      //员工和假勤不查看开通权限
      if (
        isMananger &&
        businessCode &&
        // !['hrEmployee', 'hrAttend'].includes(businessCode.split('.')[0])
        !['hrEmployee', 'hrAttend', 'kpi', 'pay'].includes(businessCode.split('.')[0])//临时不校验绩效权限
      ) {
        router.app.$options.store
          .dispatch('actionCheckSalaryBusiness', {
            code: businessCode.split('.')[0]
          })
          .then(res => {
            if (res.data.isOpen) {
              if (businessCode.indexOf("hrContract") === -1) {
                //薪酬免费版，不可使用个税申报和三方
                if (businessCode.indexOf("salary.report") !== -1 || businessCode.indexOf("salary.taxpay") !== -1) {
                  if (!productEdition.salary) {
                    next({
                      path: '/openCharge',
                      query: { bnsCode: businessCode.split('.')[0] }
                    });
                  } else {
                    next()
                  }
                } else {
                  next()
                }
              } else {
                //合同免费版，合同模块只能使用劳动合同记录
                if (businessCode === 'hrContract.conManage.laborContract') {
                  next()
                } else {
                  if (!productEdition.contract) {
                    next({
                      path: '/openCharge',
                      query: { bnsCode: businessCode.split('.')[0] }
                    });
                  } else {
                    next()
                  }
                }
              }

            } else {
              //合同、薪酬开通页面
              next({
                path: '/openAuth',
                query: { bnsCode: businessCode.split('.')[0] }
              });
            }
          });
      } else if (urlBusinessCode) {// 是否是功能地图跳转过来的
        //处理数据
        mainMenu.forEach(item => { if (item.children) list.push(...item.children); })
        // 筛选当前权限列表中包含传过来的urlBusinessCode的项
        let result = list.filter(j => { return j.businessCode == urlBusinessCode })
        //包含则继续,不包含则跳转错误页面
        // result.length > 0 ? next() : next({ path: '/error' })
        if (result.length > 0) {
          if (urlBusinessCode.indexOf("hrContract") === -1) {
            //薪酬免费版，不可使用个税申报和三方
            if (urlBusinessCode.indexOf("salary.report") !== -1 || urlBusinessCode.indexOf("salary.taxpay") !== -1) {
              if (!productEdition.salary) {
                next({
                  path: '/openCharge',
                  query: { bnsCode: urlBusinessCode.split('.')[0] }
                });
              } else {
                next()
              }
            } else {
              next()
            }
          } else {
            //合同免费版，合同模块只能使用劳动合同记录
            if (urlBusinessCode === 'hrContract.conManage.laborContract') {
              next()
            } else {
              if (!productEdition.contract) {
                next({
                  path: '/openCharge',
                  query: { bnsCode: urlBusinessCode.split('.')[0] }
                });
              } else {
                next()
              }
            }
          }
        } else {
          next({ path: '/error' })
        }
      } else {
        if (businessCode) {
          if (businessCode.indexOf("hrContract") === -1) {
            //薪酬免费版，不可使用个税申报和三方
            if (businessCode.indexOf("salary.report") !== -1 || businessCode.indexOf("salary.taxpay") !== -1) {
              if (!productEdition.salary) {
                next({
                  path: '/openCharge',
                  query: { bnsCode: businessCode.split('.')[0] }
                });
              } else {
                next()
              }
            } else {
              next()
            }
          } else {
            //合同免费版，合同模块只能使用劳动合同记录
            if (businessCode === 'hrContract.conManage.laborContract') {
              next()
            } else {
              if (!productEdition.contract) {
                next({
                  path: '/openCharge',
                  query: { bnsCode: businessCode.split('.')[0] }
                });
              } else {
                next()
              }
            }
          }
        } else {
          next()
        }
      }
    }
    setTimeout(() => {
      loadingInstance.close();
    }, 500)
  })
  router.afterEach((to, from) => {
    //卸载全局数据 卸载全局事件 spa页面切换操作（关闭loading 关闭dialog 滚动条置顶）数据埋点
  })
  router.onError((error) => {
    //当router尝试解析当前组件出错时捕获错误
    const pattern = /Loading chunk (\d)+ failed/g
    const isChunkLoadFailed = error.message.match(pattern)
    const targetPath = router.history.pending.fullPath
    if (isChunkLoadFailed) {
      router.replace(targetPath)
    }
  })
  return router
}
