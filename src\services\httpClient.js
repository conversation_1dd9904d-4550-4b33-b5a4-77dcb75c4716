const isFunction = obj => {
  return (
    Object.prototype.toString.call(obj) === '[object Function]' ||
    Object.prototype.toString.call(obj) === '[object AsyncFunction]'
  )
}
const isObject = obj => {
  return Object.prototype.toString.call(obj) === '[object Object]'
}
const timeoutAdapter = (resource, timeout, abortController) => {
  if (timeout <= 0) {
    throw new Error('timeout need > 0')
  }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      reject('请求超时')
    }, timeout)
  })
}

class HttpClient {
  //customFetch: 自定义fetch方法
  //主要用于单元测试，可以传入一个mock的fetch方法
  //通用不需要传入，即直接采用 const httpClient = new HttpClient()即可
  constructor(customFetch) {
    this.globalRequestInterceptors = []
    this.globalResponseInterceptors = []
    this.timeout = 60 * 1000
    this.maxTimeout = 60 * 1000 * 60

    this.fetch = customFetch || window.fetch
  }

  resetDefaultTimeout(timeout) {
    if (timeout <= 0) {
      return
    }

    this.timeout = timeout
  }

  resetMaxTimeout(timeout) {
    if (timeout <= 0) {
      return
    }

    this.maxTimeout = timeout
  }

  attachGlobalRequestInterceptor(interceptor) {
    if (!isFunction(interceptor)) {
      throw new Error('interceptor must be a function')
    }

    this.globalRequestInterceptors.push(interceptor)
  }

  attachGlobalResponseInterceptor(interceptor) {
    if (!isFunction(interceptor)) {
      throw new Error('interceptor must be a function')
    }

    this.globalResponseInterceptors.push(interceptor)
  }

  //有错误会直接返回，不再触发其他拦截器
  async request(resource, options) {
    if (!resource) {
      throw new Error('resource is required')
    }

    if (!options) {
      options = {}
    }
    if (!options.headers) {
      options.headers = {}
    }

    for (const interceptor of this.globalRequestInterceptors) {
      const [newErr, newResource, newOptions] = await interceptor(
        resource,
        options
      )
      if (newErr) {
        return [newErr, null]
      }

      if (newResource) {
        resource = newResource
      }

      if (newOptions && isObject(newOptions)) {
        options = newOptions
      }
    }

    //执行当前调用配置的拦截器
    if (isFunction(options.requestInterceptor)) {
      const [newErr, newResource, newOptions] =
        await options.requestInterceptor(resource, options)
      if (newErr) {
        return [newErr, null]
      }

      if (newResource) {
        resource = newResource
      }

      if (newOptions && isObject(newOptions)) {
        options = newOptions
      }
    }

    var timeout = options.timeout ? options.timeout : this.timeout
    if (timeout < 0) {
      timeout = this.timeout
    }
    if (timeout > this.maxTimeout) {
      timeout = this.maxTimeout
      console.warn(`timeout suggest <= ${this.maxTimeout}`)
    }

    //转换请求体
    if (
      options.body &&
      options.headers &&
      options.headers['Content-Type'] === 'application/json' &&
      isObject(options.body)
    ) {
      options.body = JSON.stringify(options.body)
    }
    if (options.method === 'GET') {
      delete options.body
    }

    const abortController = new AbortController()
    const abortSignal = abortController.signal
    //在当前fetch执行的请求上关联取消信号
    options.signal = abortSignal

    var err = null
    var result = null
    try {
      result = await Promise.race([
        timeoutAdapter(resource, timeout, abortController),
        fetch(resource, options)
      ])
    } catch (ex) {
      if (ex.toString().includes('请求超时')) {
        abortController.abort()
        err = { errorCode: 504, message: '请求超时' }
      } else if (!navigator.onLine) {
        err = { errorCode: 503, message: '请检查网络连接是否正常' }
      } else {
        err = { errorCode: 500, message: '系统错误' }
      }
      console.log('race error', ex)
    }
    if (err) {
      return [err, null]
    }

    if (result && result.status) {
      if (result.status == 406 || result.status == 403) {
        err = { errorCode: 406, message: '接口不在白名单' }
      }

      if (result.status == 400 || result.status === 500) {
        // const errJSON = await result.json()
        err = { errorCode: result.status, message: '系统错误' }
        // return [{ errorCode: errJSON.errorCode ? errJSON.errorCode : result.status, message: errJSON.message ? errJSON.message : "系统错误" }, null]
      }
    }
    if (err) {
      return [err, null]
    }

    //执行全局返回拦截器
    for (var interceptor of this.globalResponseInterceptors) {
      const [newErr, newResult] = await interceptor(resource, options, result)
      if (newErr) {
        return [newErr, null]
      }

      result = newResult
    }

    //执行调用方返回拦截器
    if (isFunction(options.responseInterceptor)) {
      const [newErr, newResult] = await options.responseInterceptor(
        resource,
        options,
        result
      )
      if (newErr) {
        return [newErr, null]
      }

      result = newResult
    }

    return [null, result]
  }
}

export default HttpClient
