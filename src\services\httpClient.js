const isFunction = obj => {
  return (
    Object.prototype.toString.call(obj) === '[object Function]' ||
    Object.prototype.toString.call(obj) === '[object AsyncFunction]'
  )
}
const isObject = obj => {
  return Object.prototype.toString.call(obj) === '[object Object]'
}
const timeoutAdapter = (resource, timeout, abortController) => {
  if (timeout <= 0) {
    throw new Error('timeout need > 0')
  }

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve([{ message: 'Timeout', errorCode: 504 }, null])

      abortController.abort()
    }, timeout)
  })
}

class HttpClient {
  //customFetch: 自定义fetch方法
  //主要用于单元测试，可以传入一个mock的fetch方法
  //通用不需要传入，即直接采用 const httpClient = new HttpClient()即可
  constructor(customFetch) {
    this.globalRequestInterceptors = []
    this.globalResponseInterceptors = []
    this.defaultTimeout = 60 * 1000 * 3
    this.maxTimeout = 60 * 1000 * 60

    this.fetch = customFetch || window.fetch
  }

  resetDefaultTimeout(timeout) {
    if (timeout <= 0) {
      return
    }

    this.defaultTimeout = timeout
  }

  resetMaxTimeout(timeout) {
    if (timeout <= 0) {
      return
    }

    this.maxTimeout = timeout
  }

  attachGlobalRequestInterceptor(interceptor) {
    if (!isFunction(interceptor)) {
      throw new Error('interceptor must be a function')
    }

    this.globalRequestInterceptors.push(interceptor)
  }

  attachGlobalResponseInterceptor(interceptor) {
    if (!isFunction(interceptor)) {
      throw new Error('interceptor must be a function')
    }

    this.globalResponseInterceptors.push(interceptor)
  }

  //有错误会直接返回，不再触发其他拦截器
  async request(resource, options) {
    if (!resource) {
      throw new Error('resource is required')
    }

    if (!options) {
      options = {}
    }
    if (!options.headers) {
      options.headers = {}
    }
    for (const interceptor of this.globalRequestInterceptors) {
      const [newErr, newResource, newOptions] = await interceptor(
        resource,
        options
      )
      if (newErr) {
        return [newErr, null]
      }

      if (newResource) {
        resource = newResource
      }

      if (newOptions && isObject(newOptions)) {
        options = newOptions
      }
    }

    //执行当前调用配置的拦截器
    if (isFunction(options.requestInterceptor)) {
      const [newErr, newResource, newOptions] =
        await options.requestInterceptor(resource, options)
      if (newErr) {
        return [newErr, null]
      }

      if (newResource) {
        resource = newResource
      }

      if (newOptions && isObject(newOptions)) {
        options = newOptions
      }
    }

    var timeout = options.timeout ? options.timeout : this.defaultTimeout
    if (timeout < 0) {
      timeout = this.defaultTimeout
    }
    if (timeout > this.maxTimeout) {
      timeout = this.maxTimeout
      console.warn(`timeout suggest <= ${this.maxTimeout}`)
    }
    //转换请求体
    if (
      options.body &&
      options.headers &&
      options.headers['Content-Type'] === 'application/json'
    ) {
      options.body = JSON.stringify(options.body)
    }

    const abortController = new AbortController()
    const abortSignal = abortController.signal
    //在当前fetch执行的请求上关联取消信号
    options.signal = abortSignal

    var [err, result] = await Promise.race([
      timeoutAdapter(resource, timeout, abortController),
      fetch(resource, options)
    ])
      .then(data => [null, data])
      .catch(err => {
        if (!navigator.onLine) {
          return [
            {
              errorCode: 503,
              message: '请检查网络连接是否正常'
            },
            null
          ]
        }
        return [
          {
            errorCode: 500,
            message: err?.message
          },
          null
        ]
      })

    if (err) {
      return [err, null]
    }

    if (result.status === 404) {
      return [{ errorCode: 404, message: 'Not Found' }, null]
    }

    //执行全局返回拦截器
    for (var interceptor of this.globalResponseInterceptors) {
      const [newErr, newResult] = await interceptor(resource, options, result)
      if (newErr) {
        return [newErr, null]
      }

      result = newResult
    }

    //执行调用方返回拦截器
    if (isFunction(options.responseInterceptor)) {
      const [newErr, newResult] = await options.responseInterceptor(
        resource,
        options,
        result
      )
      if (newErr) {
        return [newErr, null]
      }

      result = newResult
    }

    return [null, result]
  }
}

export default HttpClient
