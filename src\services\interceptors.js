import { getToken } from 'kit/helpers/token.js'
import { encryptData, fillHeaders } from '../helpers/encrypt/encryptOld'

const isFormData = obj => {
  return Object.prototype.toString.call(obj) === '[object FormData]'
}
const isArray = obj => {
  return Object.prototype.toString.call(obj) === '[object Array]'
}
const isObject = obj => {
  return Object.prototype.toString.call(obj) === '[object Object]'
}

export const tokenInterceptor = () => {
  return function (resource, options) {
    const token = getToken()

    const headers = {
      Authorization: `Bearer ${token}`
    }

    options.headers = { ...options.headers, ...headers }

    return [null, resource, options]
  }
}

export const gatewayInterceptor = gateway => {
  return function (resource, options) {
    resource = `${gateway}${resource}`
    return [null, resource, options]
  }
}

export const requestDefaultHeadersInterceptor = () => {
  return function (resource, options) {
    options.method = options.method || 'POST'
    options.headers['Content-Type'] =
      options.headers['Content-Type'] || 'application/json'

    return [null, resource, options]
  }
}

export const falaLoginRequestInterceptor = (resource, options) => {
  if (resource.includes('merchant/kxy/falaLogin')) {
    const gRandomKKKYYY = () => {
      const b = new Uint8Array(16)
      window.crypto.getRandomValues(b)

      const hex = Array.from(b)
        .map(c => c.toString(16).padStart(2, '0'))
        .join('')
      return hex.substr(0, 128)
    }
    const randomKkkyyy = gRandomKKKYYY()
    options.headers['Hr-Name'] = randomKkkyyy
    localStorage.setItem('Hr-Name', randomKkkyyy)
  }

  return [null, resource, options]
}

export const falaLoginResponseInterceptor = (resource, options, result) => {
  if (resource.includes('merchant/kxy/falaLogin')) {
    const Kkkyyy = result.headers.get('Hr-Id')
    const KkkyyyOpened = result.headers.get('Hr-Op')
    if (Kkkyyy) {
      localStorage.setItem('Hr-Id', Kkkyyy)
      localStorage.setItem('Hr-Ia', result.headers.get('Hr-Ia'))
      localStorage.setItem('Hr-Ie', result.headers.get('Hr-Ie'))
      localStorage.setItem('Hr-Op', KkkyyyOpened)
    } else {
      localStorage.removeItem('Hr-Id')
      localStorage.removeItem('Hr-Ia')
      localStorage.removeItem('Hr-Ie')
      localStorage.removeItem('Hr-Name')
      localStorage.removeItem('Hr-Op')
    }
  }

  return [null, result]
}

// var actions = {}
// var needDuplicateSubmissionAPIs = [
//   //模板更新
//   'template/update',
//   //模板保存
//   'template/makeSave',
//   //模板第二步提交
//   'template/makeSubmit',
//   //预览填写
//   'signing/saveDraft',
//   'signing/updateDraft',
//   'signing/saveDraftModifiableData',
//   'signing/submitDraft'
// ]

// setInterval(() => {
//   actions = {}
// }, 7000)

// export const avoidDuplicateSubmissionRequestInterceptor = (
//   resource,
//   options
// ) => {
//   //todo 改成实例形式
//   // var isNeed = false
//   // for (var c of needDuplicateSubmissionAPIs) {
//   //   if (resource.includes(c)) {
//   //     isNeed = true
//   //     break
//   //   }
//   // }
//   // if (!isNeed) {
//   //   return [null, resource, options]
//   // }
//   // if (actions[resource]) {
//   //   return [
//   //     {
//   //       resource: resource,
//   //       options: options,
//   //       message: 'duplicated request'
//   //     },
//   //     null,
//   //     null
//   //   ]
//   // }

//   // actions[resource] = true

//   return [null, resource, options]
// }

export const encryptInterceptor = (resource, options) => {
  // debugger
  if (
    resource.includes('/api/merchant/kxy/falaLogin') ||
    resource.includes('/api/merchant/tyy/login')
  ) {
    return [null, resource, options]
  }
  //重要! 不要删除这里，为了解决后端无法解析空{}情况
  fillHeaders({
    url: resource,
    headers: options.headers,
    method: options.method,
    params: options.method.toUpperCase === 'GET' ? options.body : {}
  })

  if (isFormData(options.body) || options.responseType === 'blob') {
    return [null, resource, options]
  }

  if (options.body) {
    if (isObject(options.body) || isArray(options.body)) {
      options.body = encryptData(options.body)
    }
  }

  return [null, resource, options]
}

////////////////////////Response Interceptors////////////////////////

export const jsonResponseInterceptor = () => {
  return async function (resource, options, result) {
    const contentType = result.headers.get('content-type')
    if (!result || !contentType.includes('application/json')) {
      return [null, result]
    }

    var err = null
    var r = null

    r = await result.json()
    //格式化errorCode
    if (r && typeof r.error !== 'undefined' && parseInt(r.status, 10) !== 0) {
      err = {
        ...r,
        errorCode: parseInt(r.status, 10),
        message: r.error
      }

      r = null
    }

    if (r && typeof r.ok !== 'undefined' && r.ok === false) {
      err = {
        ...r,
        errorCode: parseInt(r.errorCode, 10),
        message: r.message
      }

      r = null
    }
    if (r && r.success === false && parseInt(r.errorCode, 10)) {
      err = {
        ...r,
        errorCode: parseInt(r.errorCode, 10),
        message: r.message
      }

      r = null
    }

    return [err, r]
  }
}
