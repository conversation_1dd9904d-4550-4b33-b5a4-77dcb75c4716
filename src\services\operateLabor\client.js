class Client {
  constructor(httpClient) {
    if (!httpClient) {
      throw new Error('httpClient is required')
    }

    this.httpClient = httpClient
  }
  //角色列表
  async listRoles(options = {}) {
    const resource = `/api/supplier/listRole`
    return this.httpClient.request(resource, options)
  }
  // 图形验证码
  async createCaptcha(options = {}) {
    const resource = `/api/public/createCaptcha`
    return this.httpClient.request(resource, options)
  }
  // 登录
  async login(options = {}) {
    const resource = `/api/public/login`
    return this.httpClient.request(resource, options)
  }
  // 客户列表
  async supplierListCustomer(options = {}) {
    const resource = `/api/supplier/customer/listCustomer`
    return this.httpClient.request(resource, options)
  }

  // 新增客户
  async addCustomer(options = {}) {
    const resource = `/api/supplier/customer/addCustomer`
    return this.httpClient.request(resource, options)
  }

  // 查询客户详情
  async queryCustomer(options = {}) {
    const resource = `/api/supplier/customer/queryCustomer`
    return this.httpClient.request(resource, options)
  }

  // 更新客户信息
  async updateCustomer(options = {}) {
    const resource = `/api/supplier/customer/updateCustomer`
    return this.httpClient.request(resource, options)
  }

  // 菜单
  async supplierGetMenu(options = {}) {
    const resource = `/api/supplier/getMenu`
    return this.httpClient.request(resource, options)
  }
  // 权限树
  async supplierGetAuthorityTree(options = {}) {
    const resource = `/api/supplier/getAuthorityTree`
    return this.httpClient.request(resource, options)
  }

  // 创建权限
  async createRole(options = {}) {
    const resource = `/api/supplier/addRole`
    return this.httpClient.request(resource, options)
  }
  // 启用/禁用角色
  async disableRole(options = {}) {
    const resource = `/api/supplier/disableRole`
    return this.httpClient.request(resource, options)
  }
  // 删除角色
  async deleteRole(options = {}) {
    const resource = `/api/supplier/deleteRole`
    return this.httpClient.request(resource, options)
  }
  // 编辑角色
  async editRole(options = {}) {
    const resource = `/api/supplier/editRole`
    return this.httpClient.request(resource, options)
  }
  // 角色详情
  async roleDetail(options = {}) {
    const resource = `/api/supplier/roleDetail`
    return this.httpClient.request(resource, options)
  }
  // 获取角色成员
  async getRoleMembers(options = {}) {
    const resource = `/api/supplier/getRoleMembers`
    return this.httpClient.request(resource, options)
  }

  // 服务合同列表
  async supplierListContract(options = {}) {
    const resource = `/api/supplier/contract/listContract`
    return this.httpClient.request(resource, options)
  }

  // 上传文件
  async uploadFile(options = {}) {
    const resource = `/api/public/uploadFile`
    return this.httpClient.request(resource, {
      ...options,
      requestInterceptor(resource, options) {
        delete options.headers['Content-Type']
        return [null, resource, options]
      }
    })
  }

  // 业务主体列表
  async listCorporation(options = {}) {
    const resource = `/api/supplier/listCorporation`
    return this.httpClient.request(resource, options)
  }

  // 业务主体详情
  async corporationDetail(options = {}) {
    const resource = `/api/supplier/corporationDetail`
    return this.httpClient.request(resource, options)
  }
  //  业务主体配置详情
  async corporationConfigDetail(options = {}) {
    const resource = `/api/supplier/corporationConfigDetail`
    return this.httpClient.request(resource, options)
  }

  // 添加业务主体
  async addCorporation(options = {}) {
    const resource = `/api/supplier/addCorporation`
    return this.httpClient.request(resource, options)
  }

  // 修改业务主体
  async editCorporation(options = {}) {
    const resource = `/api/supplier/editCorporation`
    return this.httpClient.request(resource, options)
  }

  // 添加修改业务主体配置
  async editCorporationBusiness(options = {}) {
    const resource = `/api/supplier/editCorporationBusiness`
    return this.httpClient.request(resource, options)
  }
  // 描述文件
  async describeFile(options = {}) {
    const resource = `/api/public/describeFile`
    return this.httpClient.request(resource, options)
  }
  // 获取可用通道
  async supplierPayChannelList(options = {}) {
    const resource = `/api/supplier/supplierPayChannelList`
    return this.httpClient.request(resource, options)
  }
  // 配置通道参数
  async supplierCorporationConfigPayChannel(options = {}) {
    const resource = `/api/supplier/corporationConfigPayChannel`
    return this.httpClient.request(resource, options)
  }
}

export default Client
