import {
  requestDefaultHeadersInterceptor,
  jsonResponseInterceptor
} from '../interceptors'
import HttpClient from 'kit/services/httpClient'
import Client from './client'
import { getToken } from 'kit/helpers/token'

const httpClient = new HttpClient()

const tokenInterceptor = () => {
  return function (resource, options) {
    const token = getToken()
    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
      Supplier: '9',
      supplierDomain: 'www.baibai.com'
    }

    if (import.meta.env.NODE_ENV === 'test') {
      options.credentials = 'include'
    }

    options.headers = { ...headers, ...options.headers }
    return [null, resource, options]
  }
}

httpClient.attachGlobalRequestInterceptor(tokenInterceptor())
httpClient.attachGlobalRequestInterceptor(requestDefaultHeadersInterceptor())
httpClient.attachGlobalRequestInterceptor((resource, options) => {
  if (!window.env || !window.env.apiPath) {
    return [null, resource, options]
  }
  resource = `${window.env.apiPath}${resource}`
  return [null, resource, options]
})

httpClient.attachGlobalResponseInterceptor(jsonResponseInterceptor())

const client = new Client(httpClient)

const makeClient = () => client

export default makeClient
