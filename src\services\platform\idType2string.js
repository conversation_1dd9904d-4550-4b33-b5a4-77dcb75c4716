// PRC_ID	居民身份证	居民身份证	101
// CHINA_PASSPORT	中国护照	中国护照	112
// COMPATRIOTS_CARD	港澳居民来往内地通行证	港澳居民来往内地通行证（香港）	108
// 港澳居民来往内地通行证（澳门）	114
// MACAU_PRC_ID	港澳居民居住证	港澳居民居住证（香港）	120
// 港澳居民居住证（澳门）	121
// FORMOSA_CARD	台湾居民来往大陆通行证	台湾居民来往大陆通行证	109
// FORMOSA_PRC_ID	台湾居民居住证	台湾居民居住证	122
// FOREIGN_PASSPORT	外国护照	外国护照	103
// FOREIGN_PRC_ID	外国人永久居留身份证	外国人永久居留身份证	111
// FOREIGN_WORK_PERMIT_A	外国人工作许可证（A类）	人事、薪税、费控保留现有取值，无需对应行内改造
// FOREIGN_WORK_PERMIT_B	外国人工作许可证（B类）
// FOREIGN_WORK_PERMIT_C	外国人工作许可证（C类）
export const codeNameMap = [
  {
    code: "101",
    name: "居民身份证",
  },
  {
    code: "112",
    name: "中国护照",
  },
  {
    code: "108",
    name: "港澳居民来往内地通行证（香港）",
  },
  {
    code: "114",
    name: "港澳居民来往内地通行证（澳门）",
  },
  {
    code: "120",
    name: "港澳居民居住证（香港）",
  },
  {
    code: "121",
    name: "港澳居民居住证（澳门）",
  },
  {
    code: "109",
    name: "台湾居民来往大陆通行证",
  },
  {
    code: "122",
    name: "台湾居民居住证",
  },
  {
    code: "103",
    name: "外国护照",
  },
  {
    code: "111",
    name: "外国人永久居留身份证",
  },
  {
    code: "FOREIGN_WORK_PERMIT_A",
    name: "外国人工作许可证（A类）",
  },
  {
    code: "FOREIGN_WORK_PERMIT_B",
    name: "外国人工作许可证（B类）",
  },
  {
    code: "FOREIGN_WORK_PERMIT_C",
    name: "外国人工作许可证（C类）",
  },
  {
    code: "102",
    name: "居民户口簿",
  },
  {
    code: "105",
    name: "军人军官证",
  },
  {
    code: "106",
    name: "军人士兵证",
  },
  {
    code: "107",
    name: "武警军官证",
  },
  {
    code: "110",
    name: "临时居民身份证",
  },
  {
    code: "113",
    name: "武警士兵证",
  },
  {
    code: "115",
    name: "边民出入境通行证",
  },
  {
    code: "199",
    name: "其他个人证件",
  },
  {
    code: "125",
    name: "港澳居民来往内地通行证（非中国籍）",
  },
  {
    code: "116",
    name: "台湾居民旅行证",
  },
];

export const otherIdTypeMap = [
  {
    code: "101",
    name: "居民身份证",
  },
  {
    code: "112",
    name: "中国护照",
  },
  {
    code: "108",
    name: "港澳居民来往内地通行证（香港）",
  },
  {
    code: "114",
    name: "港澳居民来往内地通行证（澳门）",
  },
  {
    code: "120",
    name: "港澳居民居住证（香港）",
  },
  {
    code: "121",
    name: "港澳居民居住证（澳门）",
  },
  {
    code: "109",
    name: "台湾居民来往大陆通行证",
  },
  {
    code: "122",
    name: "台湾居民居住证",
  },
  {
    code: "103",
    name: "外国护照",
  },
  {
    code: "111",
    name: "外国人永久居留身份证",
  },
  {
    code: "FOREIGN_WORK_PERMIT_A",
    name: "外国人工作许可证（A类）",
  },
  {
    code: "FOREIGN_WORK_PERMIT_B",
    name: "外国人工作许可证（B类）",
  },
  {
    code: "FOREIGN_WORK_PERMIT_C",
    name: "外国人工作许可证（C类）",
  },
  {
    code: "199",
    name: "其他个人证件",
  },
];

export const newOldCodeMap = [
  {
    new: "101",
    old: "PRC_ID",
  },
  {
    new: "112",
    old: "CHINA_PASSPORT",
  },
  {
    new: "108",
    old: "COMPATRIOTS_CARD",
  },
  {
    new: "114",
    old: "COMPATRIOTS_CARD",
  },
  {
    new: "120",
    old: "MACAU_PRC_ID",
  },
  {
    new: "121",
    old: "MACAU_PRC_ID",
  },
  {
    new: "109",
    old: "FORMOSA_CARD",
  },
  {
    new: "122",
    old: "FORMOSA_PRC_ID",
  },
  {
    new: "103",
    old: "FOREIGN_PASSPORT",
  },
  {
    new: "111",
    old: "FOREIGN_PRC_ID",
  },
  {
    new: "FOREIGN_WORK_PERMIT_A",
    old: "FOREIGN_WORK_PERMIT_A",
  },
  {
    new: "FOREIGN_WORK_PERMIT_B",
    old: "FOREIGN_WORK_PERMIT_B",
  },
  {
    new: "FOREIGN_WORK_PERMIT_C",
    old: "FOREIGN_WORK_PERMIT_C",
  },
  {
    new: "199",
    old: "OTHER",
  },
];

export const idType2string = (code) => {
  if (isOldCode(code)) {
    code = oldCode2newCode(code);
  }

  const item = codeNameMap.find((c) => c.code === code);
  if (!item) {
    return "不支持的证件类型";
  }

  return item.name;
};
export const toCode = (name) => {
  const item = codeNameMap.find((c) => c.name === name);
  if (!item) {
    return "不支持的证件类型";
  }

  return item.code;
};
export const oldCode2newCode = (oldCode) => {
  if (!isOldCode(oldCode)) {
    return oldCode;
  }
  const item = newOldCodeMap.find((c) => c.old === oldCode);
  if (!item) {
    return "不支持的证件类型";
  }

  return item.new;
};
export const newCode2oldCode = (newCode) => {
  if (isOldCode(newCode)) {
    return newCode;
  }
  const item = newOldCodeMap.find((c) => c.new === newCode);
  if (!item) {
    return "不支持的证件类型";
  }

  return item.old;
};
export const isOldCode = (code) => {
  const item = newOldCodeMap.find((c) => c.old === code);
  if (!item) {
    return false;
  }

  return true;
};
export const isChineseIDType = (code) => {
  return idType2string(code) === "居民身份证";
};
export const validateIdNo = (code, value) => {
  const oldCode = newCode2oldCode(code);
  switch (oldCode) {
    case "PRC_ID": //居民身份证
      var regIdNo = new RegExp("(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)");
      if (!regIdNo.test(value)) {
        return "居民身份证号录入不正确";
      }
      break;
    case "CHINA_PASSPORT": //中国护照
      if (value.length != 9) {
        return "中国护照的证件号码必须9位，且只含数字和字母";
      }
      break;
    case "COMPATRIOTS_CARD": //港澳居民来往内地通行证
      if (!(value.length == 9 || value.length == 11)) {
        return "证件号码长度不对，且必须是数字和字母组合";
      }
      break;
    case "FORMOSA_CARD": //台湾居民来往大陆通行证
      if (value.length != 8) {
        return "台湾居民来往大陆通行证的证件号码必须8位数字";
      }
      break;
    case "MACAU_PRC_ID": //港澳居民居住证
      if (value.length != 18) {
        return "港澳居民居住证的证件号码必须18位";
      }
      break;
    case "FORMOSA_PRC_ID": //台湾居民居住证
      if (value.length != 18) {
        return "台湾居民居住证的证件号码必须18位";
      }
      break;
    case "FOREIGN_PRC_ID": //外国人永久居留身份证
      if (!(value.length === 15 || value.length === 18)) {
        return "外国人永久居留身份证的证件号码必须15或18位";
      }
      break;
  }

  return "";
};
