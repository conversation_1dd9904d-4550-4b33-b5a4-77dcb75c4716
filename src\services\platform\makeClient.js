import {
  tokenInterceptor,
  requestDefaultHeadersInterceptor,
  encryptInterceptor,
  jsonResponseInterceptor,
  falaLoginRequestInterceptor,
  falaLoginResponseInterceptor
} from '../interceptors'
import HttpClient from '../httpClient'
import Client from './client'

const httpClient = new HttpClient()

httpClient.attachGlobalRequestInterceptor(tokenInterceptor())
httpClient.attachGlobalRequestInterceptor(requestDefaultHeadersInterceptor())
httpClient.attachGlobalRequestInterceptor(falaLoginRequestInterceptor)
httpClient.attachGlobalRequestInterceptor(encryptInterceptor)
//自定义的默认中断器 用来兼容平台各种奇奇怪怪的历史api格式
httpClient.attachGlobalRequestInterceptor((resource, options) => {
  options.headers.Authorization = options.headers.Authorization.replace(
    'Bearer ',
    ''
  )


  var pGateway = window.env?.platform?.api
  if (!pGateway) {
    throw new Error('window.env?.platform?.api is required')
  }
  const tmp = pGateway.split('/api')
  if (tmp.length !== 2) {
    throw new Error(
      'window.env?.platform?.api format error, must be https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/merchant'
    )
  }

  // 生产环境域名需要改变
  if((resource.includes('/api/olading') || resource.includes('/api/hrsaas-emp')) && window.env.current === 'olading-production' ){
    resource = `${resource}`
  }else{
    resource = `${tmp[0]}${resource}`
  }

  return [null, resource, options]
})

httpClient.attachGlobalResponseInterceptor(falaLoginResponseInterceptor)
httpClient.attachGlobalResponseInterceptor(jsonResponseInterceptor())

const client = new Client(httpClient)

const makeClient = () => client

export default makeClient
