import fetchJsonp from 'fetch-jsonp'
class QQMap {
    constructor(key) {
      this.key = key
      this.host = 'https://apis.map.qq.com/ws'
    }
    async pointToAddress(point) {
      if (!point || !point.lat || !point.lng) {
        return ['请传入正确的经纬坐标', null]
      }
      const r = await this._call('/geocoder/v1', {
        location: `${point.lat},${point.lng}`,
        get_poi: 1
      })
      const json = await r.json()
      if (json.message !== 'Success') {
        return [json.message, null]
      }

      if (
        json.result.formatted_addresses &&
        json.result.formatted_addresses.standard_address
      ) {
        return [null, json.result.formatted_addresses.standard_address]
      }

      return [null, json.result.address]
    }
    async suggestions(address) {
      if (!address) {
        return ['', []]
      }

      const r = await this._call('/place/v1/suggestion', {
        keyword: address,
        page_size: 20
      })
      const json = await r.json()
      if (json.status !== 0) {
        return [json.message, null]
      }

      return [null, json.data]
    }
    _call(apiPath, params = {}) {
      const url = new URL(this.host + apiPath)
      url.searchParams.append('key', this.key)
      params.output = 'jsonp'
      for (var key in params) {
        url.searchParams.append(key, params[key])
      }

      return fetchJsonp(url.toString())
    }
    staticMap(center, width, height) {
      return (
        `${this.host}/staticmap/v2/?center=${center}` +
        `&zoom=17&size=${width}*${height}&maptype=roadmap` +
        `&markers=size:large|color:0xFFCCFF|${center}&key=${this.key}`
      )
    }
  }

  export const qqMap = new QQMap('TLEBZ-EZJWG-WX6QW-IOWRD-BT2EE-MSBFW')
