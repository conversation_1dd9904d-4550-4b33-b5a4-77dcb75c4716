class Client {
  constructor(httpClient) {
    if (!httpClient) {
      throw new Error('httpClient is required')
    }

    this.httpClient = httpClient
  }
  async getEmpListByUser(options = {}) {
    const resource = 'hrosaas-emp/empList/getEmpListByUser?v=local'
    return this.httpClient.request(resource, options)
  }

  async getMerchantUserPrivilege(options = {}) {
    const resource = `/merchant/platform/getMerchantUserPrivilege`
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformProfile(options = {}) {
    const resource = '/merchant/platform/profile'
    return this.httpClient.request(resource, options)
  }

  async getContracts(options = {}) {
    const resource = 'hrosaas-emp/contractRecord/selectEmpContracts?v=local'
    return this.httpClient.request(resource, options)
  }

  async addSellRecord(options = {}) {
    const resource = 'merchant/yrl/addSellRecord'
    return this.httpClient.request(resource, options)
  }

  async checkIdNo(options = {}) {
    const resource = 'hrosaas-emp/addEmp/checkIdNo?v=local'
    return this.httpClient.request(resource, options)
  }

  async entryQuery(options = {}) {
    const resource = 'hrosaas-emp/entry/v1/emp/query/entryQuery?v=local'
    return this.httpClient.request(resource, options)
  }

  async addEmpByBasicInfo(options = {}) {
    const resource = 'hrosaas-emp/addEmp/addEmpByBasicInfo?v=local'
    return this.httpClient.request(resource, options)
  }

  async yrlUploadFile(options = {}) {
    options.headers = options.headers || {}
    options.headers['Content-Type'] = 'multipart/form-data'
    const resource = 'merchant/yrl/uploadFile'
    return this.httpClient.request(resource, options)
  }

  async ocrIdentify(options = {}) {
    const resource = 'merchant/yrl/ocrIdentify'
    return this.httpClient.request(resource, options)
  }

  async getEnumByCodeList(options = {}) {
    const resource = 'hrosaas-emp/options/getEnumByCodeList'
    return this.httpClient.request(resource, options)
  }

  async getEnumByCodeList(options = {}) {
    const resource = 'hrosaas-emp/options/getEnumByCodeList'
    return this.httpClient.request(resource, options)
  }

  async getAttendGroupListOverNight(options = {}) {
    const resource = 'hro-attend/group/plus/getAttendGroupListOverNight'
    return this.httpClient.request(resource, options)
  }

  async getContractOptions(options = {}) {
    const resource = 'hro/customer/contract/options'
    return this.httpClient.request(resource, options)
  }
}

export default Client
