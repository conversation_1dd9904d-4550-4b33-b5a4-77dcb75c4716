<template>
  <div class="canvas-box">
    <div class="hand-writing">
      <canvas
        class="hand-writing__board"
        disable-scroll="true"
        @touchstart="uploadScaleStart"
        @touchmove="uploadScaleMove"
        @touchend="uploadScaleEnd"
        canvas-id="hand-writing__board"
      ></canvas>
    </div>
    <div class="autograph-footer">
      <div class="autograph-icon flex-box">
        <span class="iconfont icon-shanchu del" @click="handleRetDraw"></span>
        <span class="iconfont icon-SVG- save" @click="handleSave"></span>
      </div>
    </div>
  </div>
</template>

<script>
let handwriting
import Handwriting from "../../util/handwriting"
export default {
  data() {
    return {
      isDraw: false,
      current: 2,
      infoList: [],
      rubberValue: 150,
      middleValue: 90,
      lineColor: "#333",
      slideValue: 90,
      startX: "",
      deltaX: "",
      ruleForm: {
        imageId: "",
        defaultImage: false,
        signatureType: "HAND_WRITER"
      }
    }
  }
}
</script>

<style></style>
