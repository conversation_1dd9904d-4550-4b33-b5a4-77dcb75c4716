<template>
  <div class="seeProcess clearfix">
    <h2 class="title">签署进度</h2>
    <div class="audit" v-if="fileSeeFlow.length != 0">
      <div v-for="(item, index) in fileSeeFlow" :key="index" class="operat">
        <p class="operat-conent">
          <span>{{ item.stepName }}：企业签署方</span>
          <span class="status">
            <i class="el-icon-success success-icon" v-show="item.status === 'pass'"></i>
          </span>
        </p>
        <p class="operat-conent">北京懒猫联银</p>
        <p class="operat-conent">操作人：{{ item.signerName }}</p>
        <p class="operat-conent" v-if="item.handleTime">{{ item.handleTime | timeFormat }}</p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    fileSeeFlow: {
      type: Array,
      default: []
    }
  },
  created() {},
  methods: {}
}
</script>
<style lang="scss" scope>
// @import "../../assets/scss/mixins";
.seeProcess {
  .gray {
    color: #cccccc;
  }
  .audit {
    font-size: 14px;
    color: #666;
    h3 {
      font-size: 16px;
      color: #252525;
      font-weight: bold;
      line-height: 40px;
      .dot-con {
        display: block;
        width: 8px;
        float: left;
        color: blue;
        margin: 16px 15px 0 -6px;
        img {
          display: block;
          width: 100%;
        }
      }
    }
    span {
      margin-right: 5px;
    }
    .operat {
      border-bottom: 1px solid #ededed;
    }
  }
  .title {
    font-size: 16px;
    color: #252525;
    line-height: 40px;
    border-bottom: 1px solid #ededed;
  }
  .operat-conent {
    overflow: hidden;
    line-height: 30px;
    font-size: 12px;
    .status {
      display: block;
      float: right;
      overflow: hidden;
      font-size: 14px;
      .success-icon {
        color: green;
      }
      .error-icon {
        color: #dc4040;
      }
      .doing {
        color: #ff8a00;
      }
    }
  }
}
</style>
