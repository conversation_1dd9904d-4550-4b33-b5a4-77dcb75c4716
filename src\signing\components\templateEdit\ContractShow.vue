<template>
  <div class="contract-show" style="user-select: none">
    <!--pdf预览区-->

    <div class="contract-warp" ref="refContractWarp">
      <el-scrollbar style="height: 100%" class="no-xScroll">
        <div class="countPage-contain">
          <div class="countPage card-box fixed-top">
            <!--<div>{{controlListBack[3]}}</div>-->
            <div class="count"></div>
            <el-button
              size="small"
              type="text"
              @click="pageChange(0)"
              :disabled="currentPdfPage === 1"
            >
              上一页
              <i class="el-icon-arrow-up"></i>
            </el-button>
            <el-button
              size="small"
              type="text"
              @click="pageChange(1)"
              :disabled="currentPdfPage === pdfPictures.length"
            >
              下一页
              <i class="el-icon-arrow-down"></i>
            </el-button>
            <span style="margin: 0 30px"
              >{{ currentPdfPage }}/{{ pdfPictures.length }} 页</span
            >
            <span style="margin: 0 50px">
              跳转至
              <el-input
                v-model="tempCurrentPdfPage"
                class="page-jump"
                size="small"
              ></el-input
              >页
              <img
                src="../../assets/images/right.png"
                width="14px"
                @click="handleGotoPage(false)"
                class="right"
              />
            </span>
          </div>
        </div>
        <div
          class="pdf-warp"
          ref="pdfWrap"
          @mouseenter.prevent="handleDragContainerEnter"
          @mouseleave.prevent="handleDragContainerLeave"
        >
          <!--<div>{{controlList}}</div>-->
          <!--拖拽的控件预览-->
          <div
            class="div-drag-tempnode"
            v-if="newContactObj.dragTempnodeShow && mouseXy.x > 0"
            :style="dragTempnodeStyle"
          >
            <!--<div v-if="newContactObj.controlType==='FIELD_CONTROL'" style="width: 130px;height: 30px;">自定义控件{{controlListBack.filter(it=>it.type==='FIELD_CONTROL').length}}</div>-->
            <div
              v-if="newContactObj.controlType === 'FIELD_CONTROL'"
              style="width: 130px; height: 30px"
            >
              {{ newContactObj.currentSelectRole.name }}
            </div>
            <div
              v-if="newContactObj.controlType === 'CUSTOM_CONTROL'"
              style="width: 130px; height: 30px"
            >
              自定义内容
            </div>
            <div
              v-if="newContactObj.controlType === 'DATE_CONTROL'"
              style="width: 130px; height: 30px"
            >
              签署日期
            </div>
            <div
              v-if="newContactObj.controlType === 'SIGN_CONTROL'"
              style="width: 110px; height: 35px"
            >
              <img
                src="../../assets/images/qianming.png"
                alt
                style="width: 100%"
              />
            </div>
            <div
              v-if="newContactObj.controlType === 'SEAL_CONTROL'"
              style="width: 152px; height: 152px"
            >
              <img
                src="../../assets/images/gongzhang.png"
                alt
                style="width: 100%"
              />
            </div>
          </div>
          <!--遍历出控件列表-->
          <template v-if="isShowControl">
            <vue-draggable-resizable
              v-for="(item, index) in controlListBack"
              v-if="
                item.belong && item.belong === currentPdfPage && !item.isDelete
              "
              :key="index"
              :active.sync="item.isActive"
              :prevent-deactivation="true"
              :parent="true"
              :w="item.width"
              :min-width="50"
              :h="item.height"
              :min-height="20"
              :x="item.xaxis"
              :y="item.yaxis"
              :draggable="!isReadonly"
              :resizable="
                item.type === 'FIELD_CONTROL' || item.type === 'CUSTOM_CONTROL'
              "
              @resizing="handleResizing(arguments, index)"
              @dragging="handleDragging(arguments, index)"
              @activated="handleActivated(item)"
              :class="{
                'height-auto': isReadonly && item.type === 'SIGN_CONTROL',
                signRead:
                  item.type != 'SEAL_CONTROL' &&
                  item.type != 'SIGN_CONTROL' &&
                  item.type != 'DATE_CONTROL' &&
                  isOnlySign
                    ? signRead
                    : '',
              }"
              :style="{ zIndex: item.isActive ? 999 : '1' }"
              class-name-handle="my-handle-class"
            >
              <!-- ||item.type=='SIGN_CONTROL'||item.type=='FIELD_CONTROL' -->
              <!-- @deactivated="handleActivated({controlInfo:{name:'',type:''}})" -->
              <!-- <div class="control-info ellipsis" v-if="item.isActive && !isOnlySign">{{ getInfoContent(item) }}</div> -->
              <!-- <div
                class="control-info ellipsis"
                v-if="
                  (item.isActive && item.type == 'SEAL_CONTROL' && isOnlySign) ||
                    (item.isActive && item.type == 'SIGN_CONTROL' && isOnlySign)
                "
              >
                {{ getInfoContent(item) }}
              </div>-->
              <!--只读模式下不显示删除按钮-->
              <div
                class="btn-close"
                v-if="!isReadonly"
                @mousedown="$event.stopPropagation()"
                @click="handleDeleteControl(item, index)"
              >
                <i class="el-icon-circle-close"></i>
              </div>
              <div
                class="control-content relative-position"
                style="width: 100%; height: 100%"
              >
                <!--自定义控件控件 在canEdit为true时 可编辑 但 选择多个人&&有关联项时 不可编辑-->
                <span
                  v-if="item.type === 'FIELD_CONTROL'"
                  class="control-name"
                  :title="item.name"
                  :style="{ 'font-size': item.fontSize + 'px' }"
                >
                  <span v-if="isReadonly && canEdit">
                    <!-- <span v-if="item.userName.length >= 2 && item.referenceField !== ''">
                      {{
                        item.operate !== "SEAL"
                          ? referenceFieldData[item.referenceField]
                          : referenceFieldDataQiye[item.referenceField]
                      }}
                    </span>-->
                    <textarea
                      disabled
                      class="input-control"
                      :style="{
                        border:
                          validateRequired &&
                          (item.value || item.referenceField) === ''
                            ? '1px solid red'
                            : 'none',
                      }"
                      v-model="item.value"
                      :placeholder="item.name"
                      maxlength="100"
                    ></textarea>
                  </span>
                  <div v-if="!isReadonly" class="relation-container">
                    <div class="relative-child" v-if="item.isShow">
                      <div class="child">
                        <span>字符：{{ item.strNumber }}</span>
                        <span>行数：{{ item.rowNumber }}</span>
                      </div>
                      <div class="child child-other">
                        {{ getName(item.signType) }}
                      </div>
                    </div>
                    <div class="child-special">
                      {{ item.name }}
                    </div>
                  </div>
                </span>
                <div
                  v-if="item.type === 'CUSTOM_CONTROL'"
                  class="control-name"
                  :title="item.name"
                  :style="{ 'font-size': item.fontSize + 'px' }"
                >
                  <div class="relative-child" v-if="item.isShow">
                    <div class="child">
                      <span>字符：{{ item.strNumber }}</span>
                      <span>行数：{{ item.rowNumber }}</span>
                    </div>
                    <div class="child child-other">
                      {{ getName(item.signType) }}
                    </div>
                  </div>
                  <div class="child-special">
                    {{ item.name }}
                  </div>
                </div>
                <div
                  v-if="item.type === 'DATE_CONTROL'"
                  class="control-name"
                  :title="item.name"
                >
                  {{ item.name }}
                </div>

                <div v-if="item.type === 'SIGN_CONTROL'">
                  <img
                    v-if="signImage.sign"
                    :src="signImage.sign"
                    alt
                    style="width: 100%; display: block"
                  />
                  <img
                    v-else
                    src="../../assets/images/qianming.png"
                    alt
                    style="width: 100%"
                  />
                </div>
                <div v-if="item.type === 'SEAL_CONTROL'">
                  <img
                    v-if="signImage.seal"
                    :src="signImage.seal"
                    alt
                    style="width: 100%; display: block"
                  />
                  <img
                    v-else
                    src="../../assets/images/gongzhang.png"
                    alt
                    style="width: 100%"
                  />
                </div>
              </div>
              <!--<label>-->
              <!--<input type="text" style="width: 100%;height:100%;background-color: transparent;">-->
              <!--</label>-->
            </vue-draggable-resizable>
          </template>
          <div class="movie-info">
            <img
              ref="refPdfImg"
              :src="pdfPictures[currentPdfPage - 1]"
              @click="handlePdfImgClick"
              @load="setControlList(1)"
            />
          </div>
        </div>
      </el-scrollbar>
    </div>
    <!--控件属性操作区-->
    <div class="control-warp card-box" v-if="!isReadonly">
      <!--已经选控件列表-->
      <div class="selected" v-if="!currentActiveControl.type">
        <div>
          <img
            src="../../assets/images/noControlList.png"
            alt
            v-if="controlListBack.filter((it) => !it.isDelete).length == 0"
          />
        </div>
        <div v-show="controlListBack.filter((it) => !it.isDelete).length">
          <div class="title" style="text-align: left" v-if="isShowEdit">
            <span>已选控件：</span>
          </div>
          <div class="line" v-if="isShowEdit"></div>
          <div class="div-right_step step-position" v-if="isShowEdit">
            <el-collapse v-model="activeNames" @change="handleChange">
              <el-collapse-item
                v-for="(val, inX) in stepsList"
                :key="inX"
                :title="val.stepName"
                :name="inX"
              >
                <template slot="title">
                  <span v-if="val.operate === 'SIGN'" class="part-title">
                    <img src="../../../assets/images/person.png" />{{
                      val.stepName
                    }}
                  </span>
                  <span v-else class="part-title">
                    <img src="../../../assets/images/enterprise.png" />{{
                      val.stepName
                    }}
                  </span>
                </template>
                <div class="drag-fixed">
                  <div
                    class="drag-item"
                    v-for="(item, index) in getRightList(val)"
                    :key="index"
                  >
                    <div class="drag-item_icon sign-item">
                      <span
                        class="sign-item_name"
                        :title="item.name"
                        @mousedown="handleSelectControl(item)"
                        >{{ item.name }}</span
                      >
                      <img
                        :src="personImg"
                        @click="handleActivated(item, 'edit')"
                        alt=""
                      />
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
      <!--自定义控件设置-->
      <div
        class="diy-set"
        v-if="currentActiveControl.type"
        body-style="padding:0"
      >
        <div class="title" style="text-align: left">
          <span>编辑{{ currentActiveControl.name }}</span>
        </div>
        <div class="line"></div>
        <el-form
          :model="currentActiveControl"
          :rules="rules"
          label-position="top"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="签署方" prop="signType">
            <el-select
              v-model="currentActiveControl.signType"
              placeholder="请选择"
              @change="changeSign"
              :disabled="
                currentActiveControl.isDisabled ||
                templateSteps.length === 1 ||
                currentActiveControl.isSave
              "
            >
              <el-option
                v-for="item in templateSteps"
                :key="item.stepName"
                :label="item.stepName"
                :value="item.stepId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="文本内容" v-if="isShowContro">
            <p
              v-if="
                currentActiveControl.systemCheck && currentActiveControl.text
              "
            >
              <span>{{ currentActiveControl.text }}</span>
              <i
                class="el-icon-remove"
                @click="clearText(currentActiveControl)"
              ></i>
            </p>
            <el-input
              v-else
              :disabled="currentActiveControl.type === 'FIELD_CONTROL'"
              v-model="currentActiveControl.text"
              @change="changeClear"
              @focus="focusName"
              clearable
              placeholder="请输入文本"
              maxlength="800"
            ></el-input>
            <p
              class="text-choose"
              @click="chooseField"
              v-if="currentActiveControl.type === 'CUSTOM_CONTROL'"
            >
              从系统选择字段
            </p>
          </el-form-item>
          <el-form-item label="名称" prop="name" v-if="isShowContro">
            <el-input
              :disabled="currentActiveControl.type === 'FIELD_CONTROL'"
              v-model="currentActiveControl.name"
              @input="fontSizeChange(true)"
              @focus="focusName"
              placeholder="请输入名称"
              maxlength="20"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="字号"
            required
            prop="fontSize"
            v-if="isShowContro"
          >
            <el-select
              v-model="currentActiveControl.fontSize"
              placeholder="请选择"
              @change="fontSizeChange"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-show="!item.isShow"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="格式"
            prop="delivery"
            v-if="currentActiveControl.type === 'DATE_CONTROL'"
          >
            <el-select
              v-model="currentActiveControl.dateFormat"
              placeholder="请选择"
            >
              <el-option
                v-for="item in dateList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <p class="date-tips">显示日期仅作为示例，具体以签署当天时间为准</p>
          </el-form-item>
          <div class="line set-btn_line"></div>
          <el-form-item>
            <div class="set-btn">
              <el-button
                :type="
                  currentActiveControl.type === 'CUSTOM_CONTROL'
                    ? ''
                    : 'primary'
                "
                @click="submitForm('ruleForm')"
                >保存</el-button
              >
              <el-button
                type="primary"
                @click="submitField('ruleForm')"
                v-if="currentActiveControl.type === 'CUSTOM_CONTROL'"
                >保存为常用字段</el-button
              >
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div style="clear: both"></div>
    <el-dialog :visible.sync="isShowFieldDialog" v-if="isShowFieldDialog">
      <el-radio-group
        v-model="currentActiveControl.referenceField"
        class="radios"
      >
        <el-radio
          v-for="(value, key) in referenceFieldData"
          :key="key"
          :label="key"
          >{{ value }}</el-radio
        >
      </el-radio-group>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="
            currentActiveControl.referenceField = '';
            isShowFieldDialog = false;
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="isShowFieldDialog = false"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!--选择关联信息项-->
    <FiledDialog
      ref="filedList"
      @getTextValue="getTextValue"
      :chooseData="chooseData"
    ></FiledDialog>
  </div>
</template>
<script>
import VueDraggableResizable from "vue-draggable-resizable";
import "vue-draggable-resizable/dist/VueDraggableResizable.css";
import {
  referenceFieldData,
  referenceFieldDataQiye,
} from "../../util/constData";
import personImg from "@/signing/assets/images/edit.png";
import FiledDialog from "./fieldDialog.vue";
import {
  apiPostSaveField,
  apiGetContractTemplate,
} from "@/signing/modules/template/store/api";
import { mapState } from "vuex";
import { verifyPoneDay } from "../../../modules/attendance/util/validate";

export default {
  name: "contractShow",
  components: {
    VueDraggableResizable,
    FiledDialog,
  },
  props: {
    pdfPictures: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // controlList: {
    //   type: Array,
    //   default: () => {
    //     return []
    //   }
    // },
    isReadonly: {
      type: Boolean,
      default: false,
    },
    isOnlySign: {
      type: Boolean,
      default: false,
    },
    canEdit: {
      type: Boolean,
      default: false,
    },
    signImage: {
      type: Object,
      default: () => {
        return { sign: "", seal: "" };
      },
    },
    contactObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 模板详情
    templateSteps: {
      type: Array,
      default: () => {
        return [];
      },
    },
    dragPersonList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    dragCompanyList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 编辑回显数据
    stepsList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // currentSelectRole:Object
    // currentPdfPage:{type:Number,default:1}
  },
  data() {
    //自定义内容名称重复校验
    const customName = (r, v, c) => {
      if (!v) return c(new Error("请输入名称"));
      if (this.customNameRept())
        return c(new Error("控件名称重复，请重新输入"));
      c();
    };
    return {
      num: 1,
      beforeCheckFlag: false,
      width: 0,
      height: 0,
      disabledEvent: false,
      newContactObj: this.contactObj, // 左侧控件
      currContactObj: {
        // 右侧控件拖拽
        currentSelectRole: {},
        controlType: "",
        presetControlInfo: {},
        dragTempnodeShow: false,
      },
      personImg: personImg,
      rules: {
        name: [{ required: true, validator: customName, trigger: "blur" }],
        signType: [
          { required: true, message: "请选择签署方", trigger: "change" },
        ],
      },
      ruleForm: {},
      isRightCopy: false, // 是否右侧拖拽 - 复制
      referenceFieldData: referenceFieldData,
      referenceFieldDataQiye: referenceFieldDataQiye,
      currentPdfPage: 1,
      tempCurrentPdfPage: 1,
      currentActiveControl: {
        type: "",
      },
      mouseXy: {
        x: 0,
        y: 0,
      },
      isShowFieldDialog: false,
      isfixdTop: false,
      validateRequired: false,
      controlListBack: [],
      isShowControl: false,
      defaultValue: "",
      options: [
        {
          value: "10",
          label: "小四号",
          isShow: true,
        },
        {
          value: "11",
          label: "小四号",
          isShow: true,
        },
        {
          value: "12",
          label: "小四号",
        },
        {
          value: "13",
          label: "四号",
          isShow: true,
        },
        {
          value: "14",
          label: "四号",
        },
        {
          value: "15",
          label: "小三号",
        },
        {
          value: "16",
          label: "三号",
        },
        {
          value: "17",
          label: "小二号",
          isShow: true,
        },
        {
          value: "18",
          label: "小二号",
        },
        {
          value: "21",
          label: "二号",
          isShow: true,
        },
        {
          value: "22",
          label: "二号",
        },
        {
          value: "23",
          label: "小一号",
          isShow: true,
        },
        {
          value: "24",
          label: "小一号",
        },
        {
          value: "25",
          label: "一号",
          isShow: true,
        },
        {
          value: "26",
          label: "一号",
        },
      ],
      dateList: [
        {
          value: "yyyy-MM-dd",
          label: "2021-01-01",
        },
        {
          value: "yyyy/MM/dd",
          label: "2021/01/01",
        },
        {
          value: "yyyy年MM月dd日",
          label: "2021年1月1日",
        },
      ], // 日期列表
      commonControl: ["自定义内容", "签署日期", "企业签章", "个人签章"],
      signRead: "signRead",
      activeNames: [],
      momentTempFiled: {}, //暂存需保存的控件
      relationData: null, //系统关联字段
      operateType: "", //系统关联字段类型
      chooseData: "", // 系统选择字段回显
      currentFontSize: null, //当前保存字号
    };
  },
  watch: {
    pdfPictures() {
      this.currentPdfPage = 1;
    },
    // controlListBack: {
    //   handler(val) {
    //     this.$emit('sumitData', val)
    //   },
    //   deep: true
    // },
    stepsList(val) {
      if (val.length) {
        this.getContractTemplate(val);
      }
    },
  },
  created() {},
  computed: {
    ...mapState("contractManageStore", {
      tempId: "tempId",
    }),
    dragTempnodeStyle() {
      const t = this;
      return {
        left: t.mouseXy.x + "px",
        top: t.mouseXy.y + "px",
      };
    },
    // plusShow() {
    //   const t = this
    //   return t.currentActiveControl.value === "" && t.currentActiveControl.referenceField === ""
    // },
    // 是否显示编辑内容
    isShowContro() {
      return (
        this.currentActiveControl.type !== "DATE_CONTROL" &&
        this.currentActiveControl.type !== "SEAL_CONTROL" &&
        this.currentActiveControl.type !== "SIGN_CONTROL"
      );
    },
    // 是否显示右侧编辑区域
    isShowEdit() {
      return this.controlListBack.filter((it) => !it.isDelete).length != 0;
    },
    // 是否可编辑右侧签署方
    // isEditSign() {
    //   const selectRole = this.currentActiveControl
    //   return selectRole.type === 'SEAL_CONTROL' ||
    //     (selectRole.type !== 'SEAL_CONTROL' && !this.templateSteps.length) ||
    //     (selectRole.type === '"CUSTOM_CONTROL"' && selectRole.isSaveSuccess)
    // }
  },
  mounted() {
    // if (this.controlList.length > 0) {
    //   let tempList = [...this.controlList]
    //   tempList.forEach(it => {
    //     it["isActive"] = false
    //     it["isDelete"] = false
    //   })
    //   this.$set(this, "controlListBack", tempList)
    //   this.setControlList(1)
    // }
  },
  methods: {
    // 获取name
    getName(signType) {
      let targetName = this.templateSteps.filter((v) => v.stepId === signType);
      if (targetName.length) {
        return targetName[0].stepName;
      }
    },

    getRows(index, list) {
      let obj = list[index];
      list[index].rowNumber = Math.floor(obj.height / 28);
    },

    getStrNumber(index, list) {
      let obj = list[index];
      list[index].strNumber = Math.floor(obj.width / obj.fontSize);
    },

    // 获取pdf-warp类宽度和高度
    getParentLimit() {
      const dom = this.$refs["pdfWrap"];
      this.width = dom.offsetWidth;
      this.height = dom.offsetHeight;
    },

    handleChange() {},
    // 回显处理 - 签章、日期控件name
    updateSignName(list, x) {
      let result =
        list &&
        list.map((val) => {
          if (val.type === "SIGN_CONTROL") {
            val.name = this.isOneSign()
              ? "个人签章"
              : "个人签章" + this.getSignIndex(x.stepName);
          }
          if (val.type === "SEAL_CONTROL") {
            val.name = "企业签章";
          }
          if (val.type === "DATE_CONTROL" && val.stepName === "企业签署方") {
            val.name = "签署日期(企业)";
          } else if (
            val.type === "DATE_CONTROL" &&
            val.stepName !== "企业签署方"
          ) {
            val.name = this.isOneSign()
              ? "签署日期"
              : "签署日期" + this.getSignIndex(x.stepName);
          }
          return val;
        });
      return result;
    },
    // 查看模板数据
    getContractTemplate(val) {
      let list = val;
      let currentArr = [];
      for (let i = 0; i < val.length; i++) {
        this.activeNames.push(i);
      }
      list.length &&
        list.forEach((x) => {
          if (x.controls && x.controls.length) {
            let updateControl = this.updateSignName(x.controls, x);
            x.controls = updateControl.map((y) => {
              if (x.filedList && x.filedList.length) {
                let field = x.filedList.filter((z) => y.name === z.fieldName);
                if (!field.length) return y;
                y = {
                  ...y,
                  ...field[0],
                  text: y.value ? y.value : field[0].relationName,
                  belong: y.page,
                  signType: x.stepId,
                  stepId: x.stepId,
                  isSaveSuccess: true,
                  initialName: y.name,
                };
                y.xaxis = parseFloat(y.xaxis);
                y.yaxis = parseFloat(y.yaxis);
                y.width = parseFloat(y.width);
                y.height = parseFloat(y.height);
                y.fontSize = parseFloat(y.fontSize);
              }
              return y;
            });
            currentArr = currentArr.concat(x.controls);
          }
        });
      this.controlListBack = currentArr;
      // 防止接口返回数据时间过长
      if (this.height) {
        this.setControlList(0);
      }
      this.$emit("sumitData", this.controlListBack);
    },
    // 修改签署方切换name后缀
    changeSign(id) {
      if (this.currentActiveControl.type === "CUSTOM_CONTROL") {
        this.currentActiveControl.text = "";
      }
      let type = this.templateSteps.filter(
        (val) => val.stepId === this.currentActiveControl.signType
      );
      if (type.length) {
        this.operateType = type[0].operate;
      }
      let isVer = this.controlListBack.filter((val) => !val.isSaveSuccess);
      if (isVer.length) {
        return;
      }
      this.editcontrolName(id);
    },
    // 获取签署方下标
    getSignIndex(str) {
      return str.slice(str.length - 1);
    },
    // 编辑控件
    editcontrolName(id) {
      let t = this;
      let step = this.templateSteps.filter((val) => val.stepId === id);
      if (step.length) {
        if (t.currentActiveControl.type === "SIGN_CONTROL") {
          t.currentActiveControl.name = t.isOneSign()
            ? t.currentActiveControl.name
            : "个人签章" + this.getSignIndex(step[0].stepName);
        }
        if (
          t.currentActiveControl.type === "DATE_CONTROL" &&
          step[0].operate === "SEAL"
        ) {
          t.currentActiveControl.name = "签署日期(企业)";
        }
        if (
          t.currentActiveControl.type === "DATE_CONTROL" &&
          step[0].operate == "SIGN"
        ) {
          t.currentActiveControl.name = t.isOneSign()
            ? "签署日期"
            : "签署日期" + this.getSignIndex(step[0].stepName);
        }
        if (t.currentActiveControl.type === "FIELD_CONTROL") {
          t.currentActiveControl.name = t.isOneSign()
            ? t.currentActiveControl.name
            : t.currentActiveControl.initialName +
              this.getSignIndex(step[0].stepName);
        }
      }
      // let repeatName = this.controlListBack.filter(val => val.name === this.currentActiveControl.name && !val.isHide)
      // if (repeatName.length === 2) {
      //   this.$message.error(this.currentActiveControl.name + '已选，不用重复添加了哦')
      //   this.controlListBack.pop()
      // }
      if (this.currentActiveControl.isRight) {
        let repeat = this.controlListBack.filter((it) => {
          return (
            it.isSaveSuccess &&
            it.name === this.currentActiveControl.name &&
            it.signType === this.currentActiveControl.signType
          );
        });
        if (repeat.length) {
          this.$message.error(
            this.currentActiveControl.name + "已选，不用重复添加了哦"
          );
          this.controlListBack = this.controlListBack.filter((val) => {
            return this.currentActiveControl.uuid !== val.uuid;
          });
          this.currentActiveControl = { type: "" };
        }
      }

      t.currentActiveControl = {
        ...t.currentActiveControl,
      };
    },
    // 是否仅有一个个人签署方后缀
    isOneSign() {
      let length = this.templateSteps.length;
      let count = 0;
      if (length) {
        count = this.templateSteps.filter((val) => val.operate === "SIGN");
      }
      return count.length <= 1;
    },
    // 是否添加控件名称后缀
    isUpdateName(val) {
      if (val.signType) {
        return (
          val.type === "SEAL_CONTROL" ||
          val.type === "CUSTOM_CONTROL" ||
          this.isOneSign() ||
          this.isCompanySign()
        );
      } else {
        return true;
      }
    },
    // 获取步骤sortby
    getStepSortby() {
      let item = this.templateSteps.filter(
        (val) => val.stepId === this.currentActiveControl.signType
      );
      return item.length && this.getSignIndex(item[0].stepName);
    },
    verifyNameRepate(isEdit) {
      let isVer = this.controlListBack.filter((val) => {
        return !val.isSaveSuccess;
      });
      if (isVer.length) {
        let repeat = this.controlListBack.filter((it) => {
          return (
            it.isSaveSuccess &&
            it.name === this.currentActiveControl.name &&
            !this.currentActiveControl.isSaveSuccess &&
            it.signType === this.currentActiveControl.signType
          );
        });
        if (repeat.length) {
          this.$message.error(
            this.currentActiveControl.name + "已选，不用重复添加了哦"
          );
          this.controlListBack = this.controlListBack.filter((val) => {
            return (
              (!val.isSaveSuccess &&
                this.currentActiveControl.name !== val.name) ||
              val.isSaveSuccess
            );
          });
          this.currentActiveControl = { type: "" };
        }
      }
      if (!isEdit) {
        this.currentActiveControl = { type: "" };
      }
      this.controlListBack = this.controlListBack.map((val, i) => {
        val.isSaveSuccess = true;
        return val;
      });
      this.$emit("sumitData", this.setControlList(0));
      this.$emit("beforeCheck", false);
      this.beforeCheckFlag = false;
    },
    // 批量编辑右侧控件
    batchEdit() {
      this.controlListBack = this.controlListBack.map((val) => {
        if (val.uuid === this.currentActiveControl.uuid) {
          let updateAtt = {
            belong: val.belong,
            xaxis: val.xaxis,
            yaxis: val.yaxis,
            isHide: val.isHide,
          };
          let newObj = { ...this.currentActiveControl };
          val = { ...newObj, ...updateAtt };
        }
        return val;
      });
    },
    // 校验自定义内容名称是否重复
    customNameRept() {
      let isRept = this.controlListBack.filter((val) => {
        return (
          this.currentActiveControl.name === val.name &&
          !val.isRight &&
          this.currentActiveControl.type === "CUSTOM_CONTROL"
        );
      });
      if (isRept.length > 1) {
        return true;
      }
      return false;
    },
    getIsDisabled() {
      this.controlListBack.forEach((val) => {
        if (val.uuid === this.currentActiveControl.uuid) {
          val.isDisabled = true;
        }
      });
    },
    // 控件保存
    submitForm(form, isEdit = false) {
      this.$nextTick(() => {
        this.$refs[form].validate((valid) => {
          if (valid) {
            this.getIsDisabled();
            this.controlStatus();

            if (!this.currentActiveControl.isRight) {
              this.editcontrolName(this.currentActiveControl.signType);
            }

            this.currentFontSize = this.currentActiveControl.fontSize;
            this.batchEdit();
            this.verifyNameRepate(isEdit);
            this.operateType = "";
            this.$emit("beforeCheck", false);
            this.beforeCheckFlag = false;
            console.log("form", form);
          } else {
            this.$emit("beforeCheck", true);
            this.beforeCheckFlag = true;
          }
        });
      });
    },
    // 控件保存为常用字段
    submitField(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          let listFlag = this.dragCompanyList
            .map((v) => v.name.trim())
            .includes(this.currentActiveControl.name.trim());
          let listOtherFlag = this.dragPersonList
            .map((v) => v.name.trim())
            .includes(this.currentActiveControl.name.trim());
          let listCommonFlag = this.commonControl.indexOf(
            this.currentActiveControl.name.trim()
          );
          let signType = this.templateSteps.filter(
            (val) => val.stepId === this.currentActiveControl.signType
          );
          if (listOtherFlag || listFlag || listCommonFlag !== -1) {
            this.$message.error(this.currentActiveControl.name + "已存在");
            return;
          }
          let isVer = this.controlListBack.filter((val) => {
            return !val.isSaveSuccess;
          });
          if (isVer.length) {
            let repeat = this.controlListBack.filter((val) => {
              return (
                val.isSaveSuccess &&
                val.signType === this.currentActiveControl.signType &&
                val.name === this.currentActiveControl.name
              );
            });
            if (repeat.length) {
              this.$message.error(
                this.currentActiveControl.name + "已选，不用重复添加了哦"
              );
              return;
            }
          }
          let params = {
            id: "",
            name: this.currentActiveControl.name,
            relationCode: this.relationData ? this.relationData.fieldCode : "",
            relationGroup: this.relationData
              ? this.relationData.templateGroupCode
              : "",
            relationName: this.relationData ? this.relationData.fieldName : "",
            signatory: signType.length ? signType[0].operate : "",
            value: this.currentActiveControl.text,
          };
          apiPostSaveField(params).then((res) => {
            if (res.success) {
              this.currentActiveControl = { type: "" };
              this.$emit("updateData");
            }
          });
          this.$emit("beforeCheck", false);
          this.beforeCheckFlag = false;
        } else {
          this.$emit("beforeCheck", true);
          this.beforeCheckFlag = true;
        }
      });
    },
    // 是否是企业签署方
    isCompanySign() {
      let isSeal =
        this.templateSteps.length && this.templateSteps[0].operate === "SEAL";
      return isSeal;
    },
    // 从系统选择字段
    chooseField() {
      let type = this.templateSteps.filter(
        (val) => val.stepId === this.currentActiveControl.signType
      );
      let optype;
      if (type.length) {
        optype = type[0].operate;
      }
      if (this.operateType === "SEAL" || optype === "SEAL") {
        this.chooseData = this.currentActiveControl.text;
        this.$refs.filedList.openSealDialog();
      } else if (this.operateType === "SIGN" || optype === "SIGN") {
        this.chooseData = this.currentActiveControl.text;
        this.$refs.filedList.openDialog();
      }
    },
    // 清除文本内容
    clearText(item) {
      item.text = "";
      item.systemCheck = false;
      this.chooseData = "";
    },
    // 系统选择字段
    getTextValue(val) {
      if (val) {
        this.relationData = val;
        this.chooseData = val.fieldName;
        // this.currentActiveControl = {
        //   ...this.currentActiveControl,
        //   ...val,
        //   text: val.fieldName,
        //   relationCode: val ? val.fieldCode : '',
        //   relationGroup: val ? val.templateGroupCode : '',
        //   relationName: val ? val.fieldName : '',
        //   systemCheck: true,
        //   name: val.fieldName
        // }
        this.currentActiveControl.text = val.fieldName;
        this.currentActiveControl.relationName = val ? val.fieldName : "";
        this.currentActiveControl.relationGroup = val
          ? val.templateGroupCode
          : "";
        this.currentActiveControl.relationCode = val ? val.fieldCode : "";
        this.currentActiveControl.name = val.fieldName;
        this.currentActiveControl.systemCheck = true;
        for (let i = 0; i < this.controlListBack.length; i++) {
          let temp = this.controlListBack[i];
          if (temp.uuid === this.currentActiveControl.uuid) {
            temp.text = this.currentActiveControl.text;
            temp.systemCheck = true;
            temp.name = this.currentActiveControl.name;
          }
        }
      }
    },
    // 数组去重
    deWeightThree(arr) {
      let map = new Map();
      for (let item of arr) {
        if (!map.has(item.name)) {
          map.set(item.name, item);
        }
      }
      return [...map.values()];
    },
    // 获取右侧控件列表
    getRightList(type) {
      let arr = this.controlListBack.filter(
        (it) => !it.isDelete && !it.isHide && it.signType === type.stepId
      );
      arr = arr.map((val) => {
        val = { ...val };
        val.isRight = true;
        return val;
      });
      return this.deWeightThree(arr);
    },
    handleSelectControl(item) {
      item.flag = true;
      const t = this;
      let fieldInfo = null;
      switch (item.signType) {
        case "CUSTOM":
          t.currContactObj.controlType = "CUSTOM_CONTROL";
          break;
        case "SIGNDATE":
          t.currContactObj.controlType = "DATE_CONTROL";
          break;
        case "SIGNENTER":
          t.currContactObj.controlType = "SEAL_CONTROL";
          break;
        case "SIGNPER":
          t.currContactObj.controlType = "SIGN_CONTROL";
          break;
        default:
          t.currContactObj.controlType = "FIELD_CONTROL";
      }
      t.currContactObj.dragTempnodeShow = true;
      t.currContactObj.currentSelectRole = item;
      if (fieldInfo) {
        t.currContactObj.presetControlInfo = fieldInfo;
      } else {
        t.currContactObj.presetControlInfo = null;
      }
      t.newContactObj = t.currContactObj;
      this.$emit("updateContact", t.currContactObj);
    },
    // 去除拖拽状态 变为预览状态
    initStatus() {
      this.currContactObj.dragTempnodeShow = false;
      this.newContactObj = this.currContactObj;
      this.$emit("updateContact", this.currContactObj);
    },
    //pdf翻页
    pageChange(flag) {
      let t = this;
      switch (flag) {
        case 0: {
          t.currentPdfPage = t.currentPdfPage <= 1 ? 1 : --t.currentPdfPage;
          break;
        }
        case 1: {
          t.currentPdfPage =
            t.currentPdfPage >= t.pdfPictures.length
              ? t.pdfPictures.length
              : ++t.currentPdfPage;
          break;
        }
      }
      this.tempCurrentPdfPage = this.currentPdfPage;
    },
    handleGotoPage(page) {
      const t = this;
      // t.validateControlContent(true);
      page ? (t.tempCurrentPdfPage = page.toString()) : "";
      const tempNum = parseInt(t.tempCurrentPdfPage);
      if (
        !isNaN(tempNum) &&
        tempNum <= t.pdfPictures.length &&
        tempNum > 0 &&
        t.pdfPictures.length >= 1
      ) {
        t.currentPdfPage = parseInt(t.tempCurrentPdfPage);
      } else {
        this.$message({
          message: "请输入正确的页码",
          type: "warning",
        });
      }
    },
    focusName(event) {
      this.defaultValue = event.target.value;
    },
    changeClear(val) {
      this.chooseData = val;
      // this.currentActiveControl.name = val
    },
    //删除某个控件
    handleDeleteControl(item, cindex) {
      console.log("删除某个控件");
      console.log("item", item);
      /*if (item.type == "CUSTOM_CONTROL") {
        this.num = this.num - 1;
        console.log(this.num);
      }*/
      if (!this.controlListBack[cindex].isSaveSuccess) {
        this.$emit("beforeCheck", false);
        this.beforeCheckFlag = false;
      }
      const t = this;
      // const isCanDelete = t.controlListBack.filter(val => val.name === t.controlListBack[cindex].name)
      // t.controlListBack[cindex].isDelete = true
      let temp = t.controlListBack.splice(cindex, 1);
      for (let i = 0; i < t.controlListBack.length; i++) {
        if (t.controlListBack[i].uuid === temp[0].uuid) {
          t.controlListBack[i].isHide = false;
          break;
        }
      }
      // console.log(t.controlListBack)
      //清空所有控件激活状态
      this.controlStatus();
      t.currentActiveControl = { type: "" };
      this.$emit("sumitData", this.setControlList(0));
    },
    //调整控件宽高
    handleResizing(args, index) {
      const t = this;
      t.controlListBack[index].width = args[2];
      t.controlListBack[index].height = args[3];
      this.getRows(index, this.controlListBack);
      this.getStrNumber(index, this.controlListBack);
    },
    //调整控件xy坐标
    handleDragging(args, index) {
      const t = this;
      t.controlListBack[index].xaxis = args[0];
      t.controlListBack[index].yaxis = args[1];
    },
    //进入拖动区
    handleDragContainerEnter() {
      const t = this;
      // this.dragTempnodeShow = true;
      //绑定拖动&鼠标抬起事件
      window.addEventListener("mousemove", t.handleUpdateMouseXy);
      window.addEventListener("mouseup", t.handleMouseup);
    },
    handleUpdateMouseXy($event) {
      if (this.newContactObj.dragTempnodeShow) {
        this.mouseXy.x = $event.clientX;
        this.mouseXy.y = $event.clientY;
      }
    },
    // 鼠标抬起
    handleMouseup(e) {
      console.log("fontSize", this.currentActiveControl.fontSize);
      const t = this;
      console.info("up----", t.newContactObj);
      //当移动过
      if (t.newContactObj.dragTempnodeShow && t.mouseXy.x > 0) {
        // 设置不同控件的初始宽高
        const cType =
          (t.newContactObj.currentSelectRole &&
            t.newContactObj.currentSelectRole.type) ||
          t.newContactObj.controlType;
        let cw = 130;
        let ch = 30;
        switch (cType) {
          case "DATE_CONTROL": {
            cw = 130;
            ch = 30;
            break;
          }
          case "SIGN_CONTROL": {
            cw = 110;
            ch = 35;
            break;
          }
          case "SEAL_CONTROL": {
            cw = 152;
            ch = 152;
            break;
          }
        }
        //读取预设值
        let tempName = t.newContactObj.currentSelectRole.name;
        if(cType == "CUSTOM_CONTROL" && !t.newContactObj.currentSelectRole.isRight) {
            tempName = "自定义内容" + this.num;
            this.num++;
        }

        let presetInfo = t.newContactObj.presetControlInfo;
        /*if (presetInfo) {
          tempName = presetInfo.name;
        } else {
          if (cType === "FIELD_CONTROL") {
            tempName = t.newContactObj.currentSelectRole.name;
          }
        }*/
        // 获取当前合同图片的宽高 单位px
        const pdfWidth = t.$refs["refPdfImg"].offsetWidth;
        const pdfHeight = t.$refs["refPdfImg"].offsetHeight;
        // 创建控件初始状态
        const tempControl = {
          ...t.newContactObj.currentSelectRole,
          belong: t.currentPdfPage,
          type: cType,
          isPreset: !!presetInfo,
          ...{
            width: cw,
            height: ch,
            //防止超出右边缘和下边缘
            xaxis: e.offsetX + cw > pdfWidth ? pdfWidth - cw : e.offsetX,
            yaxis: e.offsetY + ch > pdfHeight ? pdfHeight - ch : e.offsetY,
             name: tempName,
            // value: "",
            fontFamily: "SIMSUN",
            fontSize: this.currentFontSize || "12", //默认小四号
            dateFormat:
              t.newContactObj.currentSelectRole.dateFormat || "yyyy-MM-dd", // 默认日期格式
          },
          rowNumber: Math.floor(ch / 28),
          strNumber: Math.floor(cw / 12),
          text:
            t.newContactObj.currentSelectRole.text ||
            t.newContactObj.currentSelectRole.relationName,
          signType: "", //默认签署方类型
          isActive: true,
          isDelete: false,
          isHide: false, // 是否隐藏右侧复制控件
          initialName:
            t.newContactObj.currentSelectRole.initialName ||
            t.newContactObj.currentSelectRole.name,
        };
        //清空所有控件激活状态
        this.controlStatus();
        let sign = t.templateSteps.filter(
          (val) => val.operate === "SIGN"
        ).length;
        let seal = t.templateSteps.filter(
          (val) => val.operate === "SEAL"
        ).length;
        if (sign === 0 || (sign === 1 && !seal)) {
          tempControl.signType = t.templateSteps[0].stepId;
        }
        tempControl.sortby = t.templateSteps[0].sortby;
        if (tempControl.isRight) {
          tempControl.signType = t.newContactObj.currentSelectRole.signType;
          tempControl.fontSize = t.newContactObj.currentSelectRole.fontSize;
          tempControl.rowNumber = t.newContactObj.currentSelectRole.rowNumber;
          tempControl.strNumber = t.newContactObj.currentSelectRole.strNumber;
          tempControl.width = t.newContactObj.currentSelectRole.width;
          tempControl.height = t.newContactObj.currentSelectRole.height;
          tempControl.isHide = true;
          t.controlListBack.push(tempControl);
        } else {
          t.controlListBack.push(tempControl);
        }
        t.momentTempFiled = tempControl;
        //设置为当前选中的控件
        t.currentActiveControl = tempControl;
        if (!tempControl.isRight) {
          t.verifyControls(tempControl);
        } else {
          t.verifyNameRepate();
        }
      }
      t.newContactObj.dragTempnodeShow = false;
    },
    // 校验控件拖拽是否重复
    verifyControls(item) {
      item.name = this.isUpdateName(item)
        ? item.name.trim()
        : item.name.trim() + this.getStepSortby();
      let repeat = this.controlListBack.filter((it) => {
        return (
          it.isSaveSuccess &&
          it.name === item.name &&
          it.signType === item.signType &&
          !it.isDelete
        );
      });
      if (repeat.length) {
        this.$message.error(item.name + "已添加到已选控件区，请从右侧拖拽选择");
        this.controlListBack.pop();
        return;
      }
      this.submitForm("ruleForm", true);
    },
    fontSizeChange(val) {
      console.log("val", val);
      if (val) {
        this.currentActiveControl.name = this.currentActiveControl.name.replace(
          /\s+/g,
          ""
        );
      }
      this.editContent();
    },
    editContent() {
      let list = this.controlListBack.filter(
        (v) => v.uuid === this.currentActiveControl.uuid
      );
      this.$nextTick(() => {
        list.forEach((v, index) => {
          console.log("v====>", v);
          v.name = this.currentActiveControl.name;
          v.fontSize = this.currentActiveControl.fontSize;
          this.getRows(index, list);
          this.getStrNumber(index, list);
        });
      });
    },
    //离开拖动区
    handleDragContainerLeave(e) {
      const t = this;
      //重置tempNode状态
      t.resetTempnode();
    },
    //移除鼠标事件 归零xy
    resetTempnode() {
      const t = this;
      // t.dragTempnodeShow = false;
      window.removeEventListener("mousemove", this.handleUpdateMouseXy);
      window.removeEventListener("mouseup", this.handleMouseup);
      t.mouseXy.x = t.mouseXy.y = 0;
    },
    //处理坐标及宽高的转换
    setControlList(flag) {
      this.getParentLimit();
      // this.initXY();
      // flag为1 系数 转为 像素
      const t = this;
      // 获取当前合同图片的宽高 单位px
      const pdfWidth = this.width;
      let pdfHeight = this.height; //t.$refs['refPdfImg'].offsetHeight;
      if (flag) {
        //兼容拖拽插件 created中操作数据转换会造成控件如果设置parent=true 则x和height为负数的问题
        // setTimeout(() => {
        let img = new Image();
        img.src = t.pdfPictures[0];
        img.onload = function () {
          // 根据宽高比计算,宽度为800时高度的值
          if (t.controlListBack.length > 0) {
            if (parseFloat(t.controlListBack[0].width) < 1) {
              let tempControlList = JSON.parse(
                JSON.stringify(t.controlListBack)
              );
              tempControlList.forEach((item) => {
                item.xaxis = item.xaxis * pdfWidth;
                item.yaxis = item.yaxis * pdfHeight;
                item.width = item.width * pdfWidth;
                item.height = item.height * pdfHeight;
                item.fontSize = parseInt(item.fontSize * pdfWidth);
                item.rowNumber = Math.floor(item.height / 28);
                item.strNumber = Math.floor(item.width / item.fontSize);
                t.options.forEach((it) => {
                  if (it.value == item.fontSize) {
                    item.fontSize = it.value;
                  }
                });
              });
              t.$set(t, "controlListBack", tempControlList);
            }
          }
        };
        t.isShowControl = true;
        // }, 100)
      } else {
        // flag为0 克隆 像素 转为 系数
        let tempControlList = JSON.parse(
          JSON.stringify(t.controlListBack.filter((it) => !it.isDelete))
        );
        tempControlList.forEach((item) => {
          item.xaxis = parseFloat((item.xaxis / pdfWidth).toFixed(6));
          item.yaxis = parseFloat((item.yaxis / pdfHeight).toFixed(6));
          item.width = parseFloat((item.width / pdfWidth).toFixed(6));
          item.height = parseFloat((item.height / pdfHeight).toFixed(6));
          t.options.forEach((it) => {
            if (it.label == item.fontSize) {
              item.fontSize = it.value;
            }
          });
          item.fontSize = parseFloat((item.fontSize / pdfWidth).toFixed(6));
        });
        return tempControlList;
      }
    },
    handlePdfImgClick() {
      this.controlStatus();
      this.handleDeActivated();
    },
    // 展示行数，字数以及签署方
    showDetail(item) {
      this.controlListBack.forEach((it) => (it.isShow = false));
      if (
        item &&
        (item.type === "FIELD_CONTROL" || item.type === "CUSTOM_CONTROL")
      ) {
        item.isShow = true;
      }
    },
    // 控制所有控件的激活状态
    controlStatus(item) {
      this.controlListBack.forEach((it) => (it.isActive = false));
      if (item) item.isActive = true;
    },
    handleActivated(item, mark) {
      console.log(item, "控件点击事件");
      if (this.beforeCheckFlag) {
        return;
      }
      this.initStatus();
      this.chooseData = item.text;
      // 清空所有组件的激活状态
      if (mark) {
        this.controlStatus();
      } else {
        this.controlStatus(item);
      }
      this.$emit("updateFilterStep", item);

      /*
      if (item.type == "CUSTOM_CONTROL") {
        item.name = "自定义内容" + this.num;
        this.num = this.num + 1;
      }*/

      const t = this;
      setTimeout(() => {
        this.showDetail(item);
        this.currentActiveControl = item;
        //自动翻页
        t.currentPdfPage = item.belong;
        if (item.flag) {
          this.currentActiveControl = { type: "" };
          item.flag = false;
        }
      }, 1);
    },
    // 空白区域点击
    handleDeActivated() {
      console.log("空白区域点击事件");
      this.showDetail();
      if (this.currentActiveControl.type) {
        this.submitForm("ruleForm");
      }
    },
    //开启或关闭控件文本框验证
    // validateControlContent(isRequired) {
    //   const t = this
    //   t.validateRequired = isRequired
    //   if (isRequired) {
    //     return t.controlListBack.filter(it => (it.value || it.referenceField) === "").length === 0
    //   }
    // },
    // 控件信息内容计算
    // getInfoContent(item) {
    //   let result = item.stepName || "控件"
    //   if (item.userName.length > 0) {
    //     if (item.userName.length > 2) {
    //       result = [item.userName[0], item.userName[1]].join(",") + "..."
    //     } else {
    //       result = item.userName.join(",")
    //     }
    //   }
    //   return result
    // }
  },
};
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/helpers.scss";

.contract-show {
  text-align: center;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  /*border:1px solid green;*/
  .relative-position {
    position: relative;
    .relation-container {
      width: 100%;
      height: 100%;
    }
    .child-special {
      height: auto;
      line-height: 28px;
    }
    .relative-child {
      min-width: 180px;
      width: 100%;
      padding: 0 8px;
      box-sizing: border-box;
      height: 80px;
      position: absolute;
      background: #ffffd0;
      top: -93px;
      z-index: 1000000;
      left: 0;
      .child {
        font-size: 16px;
        color: #070f29;
        height: 50%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        &.child-other {
          justify-content: center;
        }
        span {
          margin-right: 12px;
        }
      }
    }
  }
  .contract-warp {
    @include widthHeight(calc(75% - 24px), 93vh);
    float: left;
    margin: 0 12px 0 12px;
    overflow: auto;
    .right {
      cursor: pointer;
      display: inline-block;
      margin-left: 20px;
      float: none;
    }
    .countPage-contain {
      width: 100%;
      height: 60px;
      background: #fff;
    }
    .countPage {
      text-align: left;
      padding: 8px 0;
      margin: 0 12px;
      height: 60px;
      width: calc(100% - 24px);
      box-sizing: border-box;
      line-height: 40px;
      border: none;
      box-shadow: 0 5px 16px -14px rgba(52, 61, 160, 0.3);
    }
    .page-jump {
      width: 70px;
      margin: 0 10px 0 10px;
    }
    .pdf-warp {
      overflow: hidden;
      position: relative;

      .div-drag-tempnode {
        background-color: #ffe5e5;
        opacity: 0.75;
        z-index: 1;
        position: fixed;
        pointer-events: none;
        user-select: none;

        text-align: center;
        line-height: 30px;
      }

      .movie-info {
        img {
          @include widthHeight(100%, auto);
          display: block;
        }
      }

      .height-auto {
        height: auto !important;
      }

      .vdr {
        background-color: #ffe5e5;
        opacity: 0.7;
        pointer-events: auto;
        border: 1px dashed #999;

        .control-info {
          box-sizing: border-box;
          position: absolute;
          width: 100%;
          padding: 4px;
          text-align: center;
          margin-top: -31px;
          background-color: #fff7d7;
          overflow: hidden;
          white-space: nowrap;
        }

        .btn-close {
          position: absolute;
          cursor: pointer;
          pointer-events: auto;
          top: -16px;
          right: -10px;
          font-size: 20px;
          opacity: 0.7;
        }
        .control-content {
          width: 100%;
          height: 100%;
          line-height: 30px;
          .control-name {
            display: inline-block;
            width: 100%;
            height: 100%;
            //white-space: nowrap;
            //text-overflow: ellipsis;
            //overflow: hidden;
            //word-break: break-all;
          }
        }
        .input-control {
          /*border:1px solid red;*/
          border: none;
          @include widthHeight(100%, 100%);
          font-size: 14px;
          background-color: transparent;
        }
      }
      .signRead {
        background: #fff;
        border: none;
      }
    }
  }

  .control-warp {
    box-sizing: border-box;
    width: 25%;
    height: 95vh;
    float: left;
    background-color: #fff;
    overflow: auto;
    padding: 20px;

    .selected,
    .date-set,
    .diy-set {
      .title {
        font-size: 16px;
        margin: 5px 0;
      }
      .line {
        width: 100%;
      }
      .set-btn_line {
        margin: 20px 0;
        width: 100%;
      }
      .set-btn {
        display: flex;
        justify-content: center;
      }
      .el-select {
        width: 100%;
      }
      .text-choose {
        max-width: 100px;
        color: #4f71ff;
        line-height: normal;
        padding-top: 10px;
        cursor: pointer;
      }
    }
    .date-set {
      text-align: left;
    }

    .diy-set {
      text-align: left;
      .label {
        margin: 10px 0;
      }

      .icon-plus-remove {
        width: 34px;
        line-height: 34px;
        text-align: center;
        font-size: 20px;
        cursor: pointer;
      }
      .date-tips {
        color: #888888;
        line-height: normal;
        padding-top: 13px;
      }
    }
  }

  .fixed-top {
    position: absolute;
    width: 800px;
    top: 0px;
    z-index: 999;
    border: 1px solid #f3f3f3;
    background-color: #fff;
  }
  .radios {
    .el-radio {
      text-align: left;
      width: 136px;
      margin-bottom: 10px;
    }
    .el-radio:last-child {
      margin-right: 30px;
    }
  }

  .div-right_step {
    .step-title {
      box-sizing: border-box;
      height: 50px;
      line-height: 50px;
      .sp-step-num {
        color: #4687f5;
      }
    }
    .div-role-item {
      cursor: pointer;
      // height: 50px;
      line-height: 25px;
      padding: 10px 5px;
      border-bottom: 1px solid #f3f3f3;
      font-size: 14px;
      box-sizing: border-box;
      .pinfo {
        line-height: 24px;
        position: relative;
        font-size: 14px;
        .tipToB {
          font-size: 12px;
          background: #00bec0;
          color: #fff;
          display: inline-block;
          height: 25px;
          width: 25px;
          text-align: center;
          border-radius: 25px;
          margin-left: 10px;
        }
      }
      .pstep {
        font-size: 12px;
        color: #999;
        line-height: 26px;
        width: 80%;
      }
      &:hover {
        background-color: #f1f1f1;
      }
    }
    .drag-fixed {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      width: 100%;
      .drag-item {
        position: relative;
        display: flex;
        width: 48%;
        padding-bottom: 20px;
        flex-direction: column;
        text-align: center;
        cursor: pointer;
        .drag-item_icon {
          display: flex;
          height: 60px;
          align-items: center;
          justify-content: center;
          background: #ffffff;
          border: 1px solid #eaeaea;
          border-radius: 4px;
          margin-bottom: 10px;
          &.sign-item {
            height: 40px;
            line-height: 40px;
            margin: 0;
            translate: all 0.5s ease;
            .sign-item_name {
              display: inline-block;
              height: 100%;
              width: calc(100% - 40px);
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              word-break: break-all;
            }
            img {
              position: absolute;
              right: 5px;
              opacity: 0;
            }
            &:hover {
              img {
                opacity: 1;
              }
            }
          }
        }
      }
    }
    /deep/ .el-collapse {
      border: none;
      .el-collapse-item__wrap {
        border: none;
      }
      .el-collapse-item__header {
        font-size: 14px;
        color: #555555;
        border: none;
      }
      .el-collapse-item__content {
        padding-bottom: 0;
      }
    }
    .part-title {
      display: flex;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
  }
  .fill-field {
    text-align: left;
  }
}
</style>
