<template>
  <div class="fill-field">
    <!--选择关联信息项-->
    <el-dialog
      title="选择关联信息项"
      :visible.sync="isShowRelation"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleCancel"
      width="600px"
    >
      <div
        v-for="(item, index) in relationList2"
        :key="index"
        :style="{ marginTop: index != 0 ? '10px' : '' }"
      >
        <div class="group-title">
          <b v-if="item.models.length > 0">{{ item.groupName }}</b>
          <i
            v-if="item.models.length > 0"
            :class="item.isShowAll ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
            @click="handleChangeShow(item, index)"
          ></i>
        </div>

        <div
          style="margin:10px 0px; border-bottom:1px solid #E5E5E5"
          v-if="item.models.length > 0"
        ></div>
        <el-radio-group
          v-model="selectRelation"
          v-show="item.models.length > 0 && item.isShowAll"
        >
          <el-radio
            v-for="(it, id) in item.models"
            :key="id"
            :label="it"
            :value="it"
            >{{ it.fieldName }}</el-radio
          >
        </el-radio-group>
      </div>
      <div slot="footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSaveRelation">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";

export default {
  props: ['chooseData'],
  data() {
    return {
      loading: false,
      fieldName: "",
      isEditFlag: false,
      isAddFlag: false,
      isShowRelation: false,
      selectRelation: "",
      currentIndex: null,
      relationList2: [],
      operateType: '',
    };
  },
  computed: {
    ...mapState("contractManageStore", {
      relationList: "relationList"
    })
  },
  watch: {
    chooseData(val) {

    }
  },
  components: {},
  mounted() {},
  methods: {
    openDialog() {
        // this.selectRelation = this.chooseData
        console.log(this.chooseData)
        this.isShowRelation = true
        this.handleShowRelation('SIGN')
    },
    openSealDialog() {
        this.selectRelation = this.chooseData
        this.isShowRelation = true
        this.handleShowRelation('SEAL')
    },
    //展示关联项弹窗
    handleShowRelation(type) {
      if (type === "SEAL") {
        this.relationList2 = [
          {
            groupName: "公司信息",
            models: [
              {
                fieldCode: "taxSubName",
                fieldName: "法人实体名称",
                templateGroupCode: ""
              },
              {
                fieldCode: "taxPayerNo",
                fieldName: "纳税人识别号",
                templateGroupCode: ""
              },
              {
                fieldCode: "contactName",
                fieldName: "联系人姓名",
                templateGroupCode: ""
              },
              {
                fieldCode: "contactPhone",
                fieldName: "联系人手机号",
                templateGroupCode: ""
              },
              {
                fieldCode: "legalName",
                fieldName: "法人姓名",
                templateGroupCode: ""
              },
              {
                fieldCode: "idType",
                fieldName: "证件类型",
                templateGroupCode: ""
              },
              {
                fieldCode: "idNo",
                fieldName: "证件号码",
                templateGroupCode: ""
              },
              {
                fieldCode: "registeredAddress",
                fieldName: "公司注册地址",
                templateGroupCode: ""
              }
            ]
          }
        ];
      } else {
        this.relationList2 = this.relationList;
      }
      this.relationList2.map(item => {
        item.isShowAll = true;
      });
      this.selectRelation = "";
      this.isShowRelation = true;
      this.getChooseItem()
    },
    // 获取选中项
    getChooseItem() {
      let arr = []
      this.relationList2.forEach(val => {
          arr = arr.concat(val.models)
      })
      let targetName = arr.filter(it => it.fieldName === this.chooseData)
      if (targetName.length) {
          this.selectRelation = targetName[0]
      }
    },
    //保存关联项
    handleSaveRelation() {
      this.isShowRelation = false;
      this.$emit('getTextValue', this.selectRelation)
    },
    // 取消
    handleCancel() {
      this.isShowRelation = false;
      this.$emit('getTextValue')
    },
    handleChangeShow(item, index) {
      item.isShowAll = !item.isShowAll;
      this.$set(this.relationList2, index, item);
    }
  }
};
</script>
<style lang="scss" scoped>
.fill-field {
  .el-table {
    .item-name {
      display: inline-block;
      margin: 0 10px;
    }
    /deep/ .el-button--primary {
      font-size: 12px;
    }
  }
  .add-container {
    border: 1px dashed #4F71FF;
    color: #4F71FF;
    font-size: 16px;
    text-align: center;
    padding: 20px 0;
    margin-top: 20px;
    cursor: pointer;
  }
  .table-name {
    color: #4F71FF;
    cursor: pointer;
  }
  /deep/ .el-table th:first-child,
  .el-table td:first-child {
    padding-left: 20px;
    text-align: left !important;
  }
  /deep/ .el-table td:first-child {
    padding-left: 20px;
    text-align: left !important;
  }
  .el-input {
    width: 250px;
  }
  /deep/ .el-dialog__body {
    max-height: 463px;
    overflow-y: auto;
  }
}
.group-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  i {
    cursor: pointer;
  }
}
</style>
