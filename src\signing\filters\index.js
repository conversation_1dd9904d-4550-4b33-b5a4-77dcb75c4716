import Vue from "vue"
import filterSample from "./sample"
Vue.filter("filterSample", filterSample)

const fileStatus = val => {
  switch (
    val //签署类型
  ) {
    case "ACCEPT": {
      return "签署中"
    }
    case "IN_PROCESS": {
      return "签署完成"
    }
    case "SUCCESS": {
      return "签署完成"
    }
  }
}
const signStatus = val => {
  switch (
    val //签署类型
  ) {
    case "ACCEPT": {
      return "待签署"
    }
    case "IN_PROCESS": {
      return "待签署"
    }
    case "SUCCESS": {
      return "已签署"
    }
  }
}

const hideCellPhone = val => {
  //隐藏手机号
  let statusName = ""
  if (val) {
    statusName = val.substring(0, 3) + "****" + val.substring(val.length - 4)
  }
  return statusName
}

export default {
  signStatus,
  fileStatus,
  hideCellPhone
}
