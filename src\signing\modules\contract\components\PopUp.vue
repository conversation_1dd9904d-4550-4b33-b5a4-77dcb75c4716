<template>
  <div>
    <!-- 拒签弹窗 -->
    <el-dialog
      title="验证身份"
      :visible.sync="popVisible"
      @close="handleClose"
      :close-on-click-modal="false"
      :width="width"
      :show-close="false"
    >
      <el-form
        :model="telFrom"
        :rules="rules"
        ref="telFrom"
        class="demo-ruleForm"
      >
        <el-form-item>
          <i class="icon tel-icon">
            <img src="../../../assets/images/tel-icon.png" alt />
          </i>
          手机号:{{ cellPhone | hideCellPhone }}
          <!-- <el-input v-model="telFrom.cellPhone" placeholder="手机号"></el-input> -->
        </el-form-item>
        <el-form-item prop="phoneCode" class="getValidContainer">
          <el-input
            type="verifyEmailCode"
            v-model="telFrom.phoneCode"
            placeholder="请输入验证码"
            maxlength="6"
          ></el-input>
          <div>
            <el-button
              :type="type"
              :disabled="!show"
              @click="getVerifyCode"
              class="getValidCode"
            >
              <span v-show="show">获取验证码</span>
              <span v-show="!show" class="count">{{ count }}s后重新发送</span>
            </el-button>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="btn-content">
            <el-button @click="noreset('telFrom')">取 消</el-button>
            <el-button
              type="primary"
              :loading="loadBtn"
              @click="confirmSignTel('telFrom')"
              >确 定</el-button
            >
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
const TIME_COUNT = 60;
// import { sendVerifyCode, getPersonal } from "../../../request/api.js";
export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
    },
    passed: {
      type: String,
    },
    cellPhone: {
      type: String,
    },
    telVaildate: {
      type: Boolean,
    },
    activeName: {
      type: String,
      default: "first",
    },
    loadBtn: {
      type: Boolean,
    },
    audit: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: "30%",
    },
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        let reg = /^\d{6}$/;
        if (reg.test(value)) {
          callback();
        } else {
          callback(new Error("密码格式有误"));
        }
      }
    };
    var validateMessage = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入理由"));
      } else {
        callback();
      }
    };
    var validateTel = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入手机号"));
      } else {
        let reg = /^1\d{10}$/;
        if (reg.test(value)) {
          callback();
        } else {
          callback(new Error("手机号格式有误"));
        }
      }
    };
    var validatetelCode = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    return {
      ruleForm: {
        authType: "BY_PASSWORD",
        signPassword: "", //签署密码
        message: "",
        passed: "",
        signImg: "",
      },
      telFrom: {
        // authType: "BY_PHONE_CODE",
        phoneCode: "",
        smsToken: "",
        // passed: "",
        // message: "",
      },
      rules: {
        signPassword: [{ validator: validatePass, trigger: "blur" }], //签署密码校验
        message: [{ validator: validateMessage, trigger: "blur" }], //拒签理由校验
        phoneCode: [{ validator: validatetelCode, trigger: "blur" }],
      },
      remnant: 0, //输入数
      remnantTel: 0,
      clickModal: false,
      count: "",
      show: true,
      type: "primary",
      myactiveName: "",
      popVisible: false,
    };
  },
  mounted() {
    // getPersonal().then(res => {
    //   this.cellPhone = res.data.data.cellPhone
    // })
  },
  filters: {
    hideCellPhone: (val) => {
      //隐藏手机号
      let statusName = "";
      if (val) {
        statusName =
          val.substring(0, 3) + "****" + val.substring(val.length - 4);
      }
      return statusName;
    },
  },
  watch: {
    dialogFormVisible(value) {
      this.popVisible = value;
      if (!value) {
        this.$refs["telFrom"].resetFields();
      }
    },
    passed(val) {
      this.ruleForm.passed = val;
      this.telFrom.passed = val;
    },
    activeName(val) {
      this.myactiveName = val;
    },
  },
  methods: {
    handleClick() {},
    descInputs() {
      let txtVal = this.ruleForm.message.length;
      this.remnant = txtVal;
    },
    descInputsTel() {
      let telVal = this.telFrom.message.length;
      this.remnantTel = telVal;
    },
    //取消拒签
    noreset(formName) {
      this.count = "";
      this.remnantTel = 0;
      this.remnant = 0;
      this.$emit("noreset");
    },
    handleClose() {
      this.count = "";
      this.remnantTel = 0;
      this.remnant = 0;
      this.$emit("closeBtn");
    },
    //拒绝验证签署密码确认按钮
    confirmSign(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit("confirmSign", this.ruleForm);
        } else {
          console.log("error submit!!");
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
          return false;
        }
      });
    },
    //手机号拒签
    confirmSignTel(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit("confirmSignTel", this.telFrom);
        } else {
          console.log("error submit!!");
          this.$nextTick(() => {
            this.errorScroll(
              document.querySelectorAll("div.el-form-item__error")
            );
          });
          return false;
        }
      });
    },
    //验证码倒计时
    send() {
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.show = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.show = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
    },
    getVerifyCode() {
      //获取短信验证码
      if (this.cellPhone != "") {
        this.$store.dispatch("templateStore/acSend").then((res) => {
          this.telFrom.smsToken = res.data;
          this.send();
        });
      }
    },
  },
};
</script>
<style scope lang="scss">
.tel-icon {
  float: left;
  height: 23px;
  margin: 10px 10px 0 0;
  img {
    display: block;
    height: 100%;
  }
}
.mytextArea {
  .el-textarea__inner {
    height: 90px;
  }
}
//获取验证码
.getValidContainer {
  position: relative;

  .getValidCode {
    position: absolute;
    right: 20px;
    top: 0px;
    color: #ff8a00;
    cursor: pointer;
    border: none;
    padding: 0px;
    top: 14px;
    background: #fff;
  }

  .getValidCode:hover {
    color: #ff7200;
    background-color: #fff;
    border-color: #fff;
  }
}
</style>
