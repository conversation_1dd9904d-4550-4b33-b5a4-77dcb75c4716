import { fetch } from "@/request/fetch"

//合同管理-查询企业预设字段
export function apiGetTemplateFields(data) {
  return fetch({
    url: "/api/hrsaas-emp/contractTemplate/fields",
    method: "get", //请求方法
    data
  })
}

//获取模板
export function apiGetTemplateDetail(data) {
  return fetch({
    url: "/api/hrsaas-emp/contractTemplate/getTemplateDetail",
    method: "get", //请求方法
    params: data
  })
}

//保存编辑企业预设字段
export function apiPostSaveField(data) {
  return fetch({
    url: "/api/hrsaas-emp/contractTemplate/saveField",
    method: "post", //请求方法
    data
  })
}

//合同模板提交
export function apiPostTemplateCommit(data) {
  return fetch({
    url: "/api/hrsaas-emp/contractTemplate/commit",
    method: "post", //请求方法
    data
  })
}

//查看合同模板
export function apiGetContractTemplate(data) {
  return fetch({
    url: "/api/hrsaas-emp/contractTemplate/get",
    method: "get", //请求方法
    params: data
  })
}

//查询模板
export function apiGetTemplate(data) {
  return fetch({
    url: "/api/signing/template/query",
    method: "post", //请求方法
    data
  })
}

//编辑模板
export function apiSaveTemplate(data) {
  return fetch({
    url: "/api/signing/template/perfect",
    method: "post", //请求方法
    data
  })
}

//查看文件
export function apiGetSigner(data) {
  return fetch({
    url: "/api/signing/file/query",
    method: "post", //请求方法
    data
  })
}

//签署文件
export function apiSignFile(data) {
  return fetch({
    url: "/api/signing/file/sign",
    method: "post", //请求方法
    data
  })
}

//发送短信
export function apiSend(data) {
  return fetch({
    url: "/api/signing/sms/send",
    method: "post", //请求方法
    data
  })
}

// 获取模板
