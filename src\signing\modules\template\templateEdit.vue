<template>
  <div v-loading="loading" class="template-page full-screen">
    <div class="header contract-header">
      <div class="header-title">
        <span @click="confirmReturn">返回</span>
        <i class="title-line"></i>
        {{ titleName }}
      </div>
      <div>
        <el-button
          type="primary"
          :loading="nextLoading"
          class="sumitBtn"
          size="mini"
          @click="debounce('handleSave')"
          >提交</el-button
        >
        <el-button
          type="text"
          class="closeBtn"
          size="mini"
          @click="handleClosed"
          >关闭</el-button
        >
      </div>
    </div>
    <div class="template-edit">
      <div class="left-step-warp" style="user-select: none">
        <el-scrollbar style="height: 100%" class="no-xScroll">
          <div class="dragItemWrap">
            <div class="div-step step-roles">
              <div class="drag-fixed">
                <div
                  class="drag-item"
                  v-for="(item, index) in dragFixList"
                  :key="index"
                >
                  <span
                    class="drag-item_icon"
                    @mousedown="handleSelectControl(item)"
                  >
                    <img :src="item.icon" alt="" />
                  </span>
                  <span>{{ item.name }}</span>
                </div>
              </div>
            </div>
            <div class="div-step step-position">
              <el-collapse v-model="activeNames" @change="handleChange">
                <el-collapse-item title="企业签署方" name="1">
                  <div class="drag-fixed">
                    <div
                      class="drag-item"
                      v-for="(item, index) in dragCompanyList"
                      :key="index"
                    >
                      <span
                        class="drag-item_icon sign-item"
                        :title="item.name"
                        @mousedown="handleSelectControl(item)"
                      >
                        {{ item.name }}
                      </span>
                    </div>
                  </div>
                </el-collapse-item>
                <el-collapse-item title="个人签署方" name="2">
                  <div class="drag-fixed">
                    <div
                      class="drag-item"
                      v-for="(item, index) in dragPersonList"
                      :key="index"
                    >
                      <span
                        class="drag-item_icon sign-item"
                        :title="item.name"
                        @mousedown="handleSelectControl(item)"
                      >
                        {{ item.name }}
                      </span>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="right-content-warp">
        <div class="contract-warp">
          <contract-show
            ref="refContractShow"
            :contactObj="contactObj"
            :isReadonly="false"
            :pdfPictures="pdfPictures"
            :templateSteps="pointer"
            :dragCompanyList="dragCompanyList"
            :dragPersonList="dragPersonList"
            :stepsList="stepsList"
            @updateContact="updateContact"
            @updateData="getFieldList"
            @sumitData="sumitData"
            @updateFilterStep="updateFilterStep"
            @beforeCheck="beforeCheck"
          ></contract-show>
        </div>
      </div>
      <div style="clear: both"></div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { deepClone } from "@/utils/utils";
import ContractShow from "../../components/templateEdit/ContractShow";
import dateImg from "@/signing/assets/images/date.png";
import signImg from "@/signing/assets/images/sign.png";
import companyImg from "@/signing/assets/images/company.png";
import customImg from "@/signing/assets/images/custom.png";
import {
  apiGetTemplateFields,
  apiGetTemplateDetail,
  apiPostTemplateCommit,
  apiGetContractTemplate,
} from "./store/api";

export default {
  name: "templateEdit",
  components: {
    ContractShow,
  },
  props: {},
  computed: {
    ...mapState({
      token: "token",
      urlInfo: "urlInfo",
    }),
    ...mapState("contractManageStore", {
      tempId: "tempId",
      titleName: "titleName",
      oriTemplateStatus: "tempStatus", // 列表页看到的模板原始状态（从列表页带过来，因为打开编辑后，所有状态都变成了草稿）
    }),
  },
  data() {
    return {
      nextLoading: false,
      timer: null,
      contactObj: {
        currentSelectRole: {},
        controlType: "",
        presetControlInfo: {},
        dragTempnodeShow: false,
      },
      // controlListBack: [],
      fields: null,
      pdfPictures: [],
      loading: false,
      contractFormData: {
        controlList: [],
        flowStep: [],
      },
      flag: false,
      verifyFlag: false,
      tempControlList: [],
      dragFixList: [
        // 左侧固定拖拽控件
        { name: "自定义内容", icon: customImg, signType: "CUSTOM" },
        { name: "签署日期", icon: dateImg, signType: "SIGNDATE" },
        { name: "企业签章", icon: companyImg, signType: "SIGNENTER" },
        { name: "个人签章", icon: signImg, signType: "SIGNPER" },
      ],
      dragCompanyList: [], //企业签署
      dragPersonList: [], // 个人签署
      activeNames: ["1", "2"],
      templateSteps: [], // 签署方列表
      initSaveParams: null,// 初始提交的数据（关闭用）
      pointer: [], // 签署方列表复制
      sumitDataList: [], // 提交模板数据
      stepsList: [], // 编辑回显数据
      isNewAdd: true, // 新增
    };
  },
  created() {
    this.$store.commit("SHOWAPP", false);
    this.getFieldList();
    this.getContractTemplate();
  },
  mounted() {
    this.$store.commit("SHOWAPP", false);
  },
  methods: {
    confirmReturn() {
      this.$router.replace({
        path: "/contract-manage/set-template",
        query: {
          tempId: this.tempId,
        },
      });
    },

    // 生成uuid
    guid() {
      function S4() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
      }
      return (
        S4() +
        S4() +
        "-" +
        S4() +
        "-" +
        S4() +
        "-" +
        S4() +
        "-" +
        S4() +
        S4() +
        S4()
      );
    },

    beforeCheck(val) {
      this.verifyFlag = val;
    },

    // 获取预设字段列表
    getFieldList() {
      apiGetTemplateFields().then((res) => {
        if (res.success) {
          const data = res.data;
          this.dragCompanyList = data.companyFields;
          this.dragPersonList = data.personalFields;
        }
      });
    },
    // 回显数据添加uuid
    addUUID(list) {
      list.forEach((v) => {
        if (v.filedList && v.filedList.length) {
          v.filedList.forEach((x) => {
            x.uuid = this.guid();
            v.controls.forEach((y) => {
              y.isSave = true;
              if (y.name === x.fieldName) {
                y.uuid = x.uuid;
                y.isRight = true;
              }
            });
          });
        }
      });
    },
    // 查看模板数据
    getContractTemplate() {
      this.loading = true;
      this.isNewAdd = this.titleName === "新增合同模板";
      let str =
        this.isNewAdd
          ? apiGetTemplateDetail
          : apiGetContractTemplate;
      let paramId = this.isNewAdd ? "tempId" : "id";
      str({ [paramId]: this.tempId }).then((res) => {
        if (res.success) {
          this.addUUID(res.data.steps);
          this.templateSteps = res.data.steps;
          this.pointer = this.templateSteps;
          this.pdfPictures = res.data.archives;
          this.contractFormData.flowStep = res.data.steps;
          this.fields = res.data.fields;
          this.stepsList = res.data.steps;
          this.loading = false;
        }
      });
    },
    // 提交模板数据
    sumitData(val) {
      this.sumitDataList = val;

      // TODO: 初始化一次
      if (null === this.initSaveParams) {
        this.initSaveParams = deepClone(this.getSaveParams());
      }
    },
    updateContact(obj) {
      this.contactObj = obj;
    },
    handleClosed() {
      const h = this.$createElement;
      this.$msgbox({
        title: "提示",
        message: h("div", null, [
          h("div", { style: "display:flex;align-items:center;height:30px;" }, [
            h("i", {
              class: "el-icon-warning",
              style: "font-size:16px;color: #FF9500;margin-right: 5px",
            }),
            h(
              "span",
              { style: "padding-right:12px;font-size:14px;" },
              "确定退出编辑模板吗？"
            ),
          ]),
          h(
            "p",
            { style: "color: #999;padding-left:21px;" },
            "退出后已编辑的信息不保存,直接返回模板管理"
          ),
        ]),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        if (!this.isNewAdd && null !== this.initSaveParams && this.oriTemplateStatus == "ENABLED") {
          // TODO: 编辑是会将模板置为草稿状态，关闭后提交保存未变更的数据，服务端会将状态改回为"启用"（变通的方法。）
          this.save(true);
        } else {
          this.$router.replace("/contract-manage/contract-template");
        }


      });
      // this.$confirm(
      //   `确定退出编辑模板吗？<p style="color: #999">退出后已编辑的信息不保存,直接返回模板管理</p>`,
      //   "提示",
      //   {
      //     confirmButtonText: "确定",
      //     cancelButtonText: "取消",
      //     type: "warning",
      //     closeOnClickModal: false,
      //     closeOnPressEscape: false,
      //     dangerouslyUseHTMLString: true,
      //   }
      // ).then(() => {
      //   this.$router.push("/contract-manage/contract-template")
      // });
    },
    // 更新右侧签署方下拉step列表
    updateFilterStep(val) {
      this.filterSteps(val);
    },
    filterSteps(item) {
      if (
        item.signType === "SIGNPER" ||
        item.signatory === "SIGN" ||
        item.type === "SIGN_CONTROL"
      ) {
        this.pointer = this.templateSteps.filter((v) => v.operate === "SIGN");
      } else if (
        item.signType === "SIGNENTER" ||
        item.signatory === "SEAL" ||
        item.type === "SEAL_CONTROL"
      ) {
        this.pointer = this.templateSteps.filter((v) => v.operate === "SEAL");
      } else {
        this.pointer = this.templateSteps;
      }
    },
    handleSelectControl(item) {
      const t = this;
      // let fieldInfo = null
      const list = this.stepsList.map((v) => v.operate);
      if (this.verifyFlag) {
        this.$message.warning("请先保存当前控件信息");
        return;
      }
      if (
        (item.signatory === "SIGN" || item.signType === "SIGNPER") &&
        !list.includes("SIGN")
      ) {
        this.$message.warning("您未设置个人签署方，无法使用此控件哦");
        return;
      } else if (
        (item.signatory === "SEAL" || item.signType === "SIGNENTER") &&
        !list.includes("SEAL")
      ) {
        this.$message.warning("您未设置企业签署方，无法使用此控件哦");
        return;
      }
      this.filterSteps(item);
      switch (item.signType) {
        case "CUSTOM":
          t.contactObj.controlType = "CUSTOM_CONTROL";

          console.log("t.dragFixList[0].name", t.dragFixList[0].name);

          break;
        case "SIGNDATE":
          t.contactObj.controlType = "DATE_CONTROL";
          break;
        case "SIGNENTER":
          t.contactObj.controlType = "SEAL_CONTROL";
          break;
        case "SIGNPER":
          t.contactObj.controlType = "SIGN_CONTROL";
          break;
        default:
          t.contactObj.controlType = "FIELD_CONTROL";
      }
      item.uuid = this.guid();
      t.contactObj.dragTempnodeShow = true;
      t.contactObj.currentSelectRole = item;
      t.contactObj.presetControlInfo = null;
      // if (fieldInfo) {
      //   t.contactObj.presetControlInfo = fieldInfo
      // } else {
      //   t.contactObj.presetControlInfo = null
      // }
      // console.log(t.contactObj, '000')
    },
    handleNext() {
      window.location.href = `${window.location.host}/index.html#modules/sign/signfilemaster.html`;
    },
    // 验证哪些人没有拖拽控件
    // checkControl() {
    //   this.tempControlList = this.$refs["refContractShow"].setControlList(0)
    //   let newTempControlList = []
    //   this.tempControlList.forEach(item => {
    //     newTempControlList.push(item.name)
    //     item.signWay = 'POSITION'
    //     item.stepName = '手机号1'
    //     item.page = 0
    //   })
    //   let newControList = []
    //   if (this.fields) {
    //     newControList = [...this.contractFormData.flowStep, ...this.fields]
    //   } else {
    //     newControList = this.contractFormData.flowStep
    //   }
    //   this.flag = newControList.every(item => {
    //     return newTempControlList.includes(item.name)
    //   })
    // },
    // 获取签署方控件列表
    getControls(x) {
      let controlsList = this.sumitDataList.filter(
        (it) => it.signType === x.stepId && !it.isDelete
      );
      let temp = deepClone(controlsList);
      let res = [];
      if (temp.length) {
        res = temp.map((y) => {
          y = {
            dateFormat: y.dateFormat,
            fontFamily: y.fontFamily,
            fontSize: y.fontSize.toString(),
            height: y.height.toString(),
            name:
              y.type === "FIELD_CONTROL" || y.type === "CUSTOM_CONTROL"
                ? y.name
                : x.stepName,
            page: y.belong,
            stepName:
              y.type === "FIELD_CONTROL" || y.type === "CUSTOM_CONTROL"
                ? y.name
                : x.stepName,
            type: y.type === "CUSTOM_CONTROL" ? "FIELD_CONTROL" : y.type,
            value: y.text ? y.text : "",
            width: y.width.toString(),
            xaxis: y.xaxis.toString(),
            yaxis: y.yaxis.toString(),
          };
          return y;
        });
      }
      return res;
    },
    // 数组去重
    deWeightThree(arr) {
      let map = new Map();
      for (let item of arr) {
        if (!map.has(item.name)) {
          map.set(item.name, item);
        }
      }
      return [...map.values()];
    },
    // 获取签署方字段列表
    getFields(x) {
      const signTypeEl = this.templateSteps.filter(
        (val) => val.operate === "SEAL"
      );
      const signType = signTypeEl.length ? signTypeEl[0].stepId : "";
      let resField = [];
      let fieldList = this.sumitDataList
        .filter((it) => it.signType === x.stepId && !it.isDelete)
        .map((val) => {
          val = {
            id: val.id ? val.id : "",
            name: val.name ? val.name : "",
            relationCode:
              val.relationCode && val.relationName === val.text
                ? val.relationCode
                : "",
            relationGroup:
              val.relationGroup && val.relationName === val.text
                ? val.relationGroup
                : "",
            relationName:
              val.relationName && val.relationName === val.text
                ? val.relationName
                : "",
            signatory: val.signType === signType ? "SEAL" : "SIGN",
            value: val.text ? val.text : "",
            fieldType: this.getFieldType(val.type),
          };
          return val;
        });
      resField = this.deWeightThree(fieldList);
      return resField;
    },
    // 区分不同类型控件
    getFieldType(type) {
      switch (type) {
        case "SIGN_CONTROL":
          return "SIGN";
        case "SEAL_CONTROL":
          return "SEAL";
        case "DATE_CONTROL":
          return "DATE";
        default:
          return "FIELD";
      }
    },
    // 防止重复提交
    debounce(fn) {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(() => {
        this[fn]();
      }, 500);
    },
    // 模板数据提交
    handleSave() {
      this.save(false)
    },
    getSaveParams() {
      const params = [];
      this.templateSteps.forEach((x) => {
        let obj = {
          controls: this.getControls(x),
          fields: this.getFields(x),
          stepId: x.stepId,
          type: x.operate,
        };
        params.push(obj);
      });

      return params;
    },
    save(closeSave) {
      // const t = this
      // t.checkControl()
      this.nextLoading = !closeSave;//true;
      const params = closeSave ? this.initSaveParams : this.getSaveParams();
      apiPostTemplateCommit({
        signatories: params,
        templateId: this.tempId,
      }).then((res) => {
        if (res.success) {
          if (this.urlInfo.callback) {
            window.location.href = decodeURIComponent(this.urlInfo.callback);
          } else {
            if (!closeSave) {
              this.$message.success("设置成功");
            }
            setTimeout(() => {
              this.$router.push("/contract-manage/contract-template");
              this.nextLoading = false;
            }, 1000);
          }
        } else {
          this.nextLoading = false;
        }
      });
      // if (!this.flag) {
      //
      // } else {
      //   this.$message.error("请确保所有签署步骤及待填充字段都在模板中确定位置后，再发起提交")
      // }
    },
    handleChange(val) {},
  },
  beforeDestroy() {
    this.$store.commit("SHOWAPP", true);
    if (this.timer) clearTimeout(this.timer);
  },
};
</script>

<style scoped lang="scss">
@import "../../assets/scss/mixins.scss";
@import "../../../assets/scss/helpers.scss";
.template-page {
  .left-step-warp {
    background-color: #fff;
    height: 95vh;
    width: 20%;
    float: left;
    overflow-y: auto;
    padding: 20px 0 20px 20px;
    box-sizing: border-box;
    /deep/ {
      .el-scrollbar__wrap {
        padding-right: 20px;
      }
    }
    .div-step {
      .step-title {
        box-sizing: border-box;
        height: 50px;
        line-height: 50px;
        .sp-step-num {
          color: #4687f5;
        }
      }
      .div-role-item {
        cursor: pointer;
        // height: 50px;
        line-height: 25px;
        padding: 10px 5px;
        border-bottom: 1px solid #f3f3f3;
        font-size: 14px;
        box-sizing: border-box;

        .pinfo {
          line-height: 24px;
          position: relative;
          font-size: 14px;

          .tipToB {
            font-size: 12px;
            background: #00bec0;
            color: #fff;
            display: inline-block;
            height: 25px;
            width: 25px;
            text-align: center;
            border-radius: 25px;
            margin-left: 10px;
          }
        }

        .pstep {
          font-size: 12px;
          color: #999;
          line-height: 26px;
          width: 80%;
        }

        &:hover {
          background-color: #f1f1f1;
        }
      }
      .drag-fixed {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width: 100%;
        .drag-item {
          display: flex;
          width: 48%;
          padding-bottom: 20px;
          flex-direction: column;
          text-align: center;
          cursor: pointer;
          .drag-item_icon {
            display: flex;
            height: 60px;
            align-items: center;
            justify-content: center;
            background: #ffffff;
            border: 1px solid #eaeaea;
            border-radius: 4px;
            margin-bottom: 10px;
            &.sign-item {
              display: inline-block;
              height: 40px;
              line-height: 40px;
              margin: 0;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              word-break: break-all;
            }
            img {
              width: 30px;
              height: 30px;
            }
          }
        }
      }
    }
  }

  .right-content-warp {
    flex: 4;
    height: 95vh;
    float: left;
  }
  .header {
    background: $mainColor;
    height: 60px;
    line-height: 60px;
    .header-title {
      display: flex;
      cursor: pointer;
      align-items: center;
      color: #ffffff;
      font-size: 16px;
      .title-line {
        display: inline-block;
        width: 1px;
        height: 16px;
        margin: 0 15px;
        background-color: #ffffff;
      }
    }
    .sumitBtn {
      color: $mainColor;
      font-size: 14px;
      font-weight: normal;
      width: 70px;
      height: 32px;
      background: #ffffff;
      border-radius: 4px;
    }
    .closeBtn {
      color: #ffffff !important;
      font-size: 16px;
      font-weight: normal;
    }
  }
  .template-edit {
    display: flex;
    margin: 12px auto 0;
  }
  /deep/ .el-collapse {
    border: none;
    .el-collapse-item__wrap {
      border: none;
    }
    .el-collapse-item__header {
      font-size: 16px;
      color: #070f29;
      border: none;
    }
    .el-collapse-item__content {
      padding-bottom: 0;
    }
  }
  .contract-header {
    margin: 0 !important;
    padding: 0 20px !important;
  }
}
</style>
