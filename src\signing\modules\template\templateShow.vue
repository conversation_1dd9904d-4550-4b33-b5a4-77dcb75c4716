<template>
  <div v-loading="loading" class="template-page full-screen">
    <div class="header contract-header header-template">
      <div class="header-title" :title="result.name" v-if="result.name">
<!--        <i class="el-icon-arrow-left" @click="$router.go(-1)"></i>-->
<!--        <i class="title-line"></i>-->
        {{ result.name.length <= 10 ? result.name : result.name.slice(0, 10) + '...' }}
      </div>
      <div>
        <el-button
          type="primary"
          class="sumitBtn other"
          size="mini"
          v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.conTemplate.edit'
                )
              "
          @click="handleEdit">编辑</el-button>
        <el-button
          type="primary"
          class="sumitBtn"
          size="mini"
          v-show="templateStatus === 'ENABLED'"
          v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.conTemplate.disable'
                )
              "
          @click="handleStop">停用</el-button>
        <el-button
          type="primary"
          class="sumitBtn"
          size="mini"
          v-show="templateStatus === 'DISABLED'"
          v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.conTemplate.disable'
                )
              "
          @click="handleStart">启用</el-button>
        <el-button
          type="primary"
          class="sumitBtn"
          size="mini"
          v-if="
                privilegeVoList.includes(
                  'hrContract.conManage.conTemplate.delete'
                )
              "
          @click="handleDelete">删除</el-button>
        <el-button
          type="text"
          class="closeBtn"
          size="mini"
          @click="handleClosed">关闭</el-button>
      </div>
    </div>
    <div class="template-show">
      <div class="template-show-left section">
        <div class="div-right_step">
          <h4>已选控件</h4>
          <el-collapse v-model="activeNames" @change="handleChange">
            <el-collapse-item
              v-for="(v, i) in result.steps"
              :key="i"
              :name="i" >
              <template slot="title">
                <span v-if="v.operate === 'SIGN'" class="part-title">
                  <img src="../../../assets/images/enterprise.png" />{{ v.stepName }}
                </span>
                <span v-else class="part-title">
                  <img src="../../../assets/images/person.png" />{{ v.stepName }}
                </span>
              </template>
              <div class="drag-fixed">
                <div
                  class="drag-item"
                  v-for="(item, index) in getRightList(v)"
                  :key="index"
                >
                  <div class="drag-item_icon sign-item">
                      <span class="sign-item_name" :title="item.name">{{ item.name }}</span>
<!--                    <img src="../../assets/images/edit.png" alt="">-->
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
      </div>
      </div>
      <div class="template-show-middle section">
        <el-scrollbar style="height:100%" class="no-xScroll">
          <div class="countPage-contain">
            <div class="countPage card-box fixed-top">
              <div class="count"></div>
              <el-button size="small" type="text" @click="pageChange(0)" :disabled="currentPdfPage === 1">
                上一页
                <i class="el-icon-arrow-up"></i>
              </el-button>
              <el-button v-if="result.archives" size="small" type="text" @click="pageChange(1)" :disabled="currentPdfPage === result.archives.length">
                下一页
                <i class="el-icon-arrow-down"></i>
              </el-button>
              <span v-if="result.archives" style="margin: 0 30px;">{{ currentPdfPage }}/{{ result.archives.length }} 页</span>
              <span style="margin: 0 50px;">
                跳转至
                <el-input v-model="tempCurrentPdfPage" class="page-jump" size="small"></el-input>页
                <img src="../../assets/images/right.png" width="14" @click="handleGotoPage(false)" class="right" />
              </span>
            </div>
          </div>
          <div
            ref="pdfWrap"
            class="pdf-warp"
          >
            <!--遍历出控件列表-->
            <template>
              <vue-draggable-resizable
                v-for="(item, index) in controlListBack"
                v-if="item.belong && item.belong === currentPdfPage && !item.isDelete && height"
                :key="index"
                :active.sync="item.isActive"
                :prevent-deactivation="true"
                :parent="true"
                :w="item.width"
                :min-width="50"
                :h="item.height"
                :min-height="20"
                :x="item.xaxis"
                :y="item.yaxis"
                :draggable="false"
                :resizable="false"
                :class="{
                'height-auto': item.type === 'SIGN_CONTROL',
                signRead:
                  item.type != 'SEAL_CONTROL' &&
                  item.type != 'SIGN_CONTROL' &&
                  item.type != 'DATE_CONTROL'
              }"
                :style="{ zIndex: item.isActive ? 999 : '1' }"
                class-name-handle="my-handle-class"
              >
                <div class="control-content vdr" style="width: 100%;height: 100%;">
                  <div class="relative-child" v-if="item.type === 'FIELD_CONTROL'" :title="item.name" :style="{'font-size':item.fontSize + 'px'}">{{ item.name }}</div>
                  <div class="relative-child" v-if="item.type === 'CUSTOM_CONTROL'" :title="item.name" :style="{'font-size':item.fontSize + 'px'}">{{ item.name }}</div>
                  <div class="relative-child" v-if="item.type === 'DATE_CONTROL'" :title="item.name" :style="{'font-size':item.fontSize + 'px'}">{{ item.name }}</div>

                  <div v-if="item.type === 'SIGN_CONTROL'">
                    <img v-if="signImage.sign" :src="signImage.sign" alt style="width: 100%;display: block;" />
                    <img v-else src="../../assets/images/qianming.png" alt style="width: 100%;" />
                  </div>
                  <div v-if="item.type === 'SEAL_CONTROL'">
                    <img v-if="signImage.seal" :src="signImage.seal" alt style="width: 100%;display: block;" />
                    <img v-else src="../../assets/images/gongzhang.png" alt style="width: 100%;" />
                  </div>
                </div>
              </vue-draggable-resizable>
            </template>
            <div class="movie-info">
              <img
                v-if="result.archives"
                ref="refPdfImg"
                :src="result.archives[currentPdfPage - 1]"
                @load="setControlList"
              />
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="template-show-right section">
        <template v-show="result.name">
          <h4>模板名称</h4>
          <div>
            <el-tooltip placement="top-start">
               <p slot="content">{{ result.name }}</p>
               <span class="span-name">{{ result.name }}</span>
            </el-tooltip>
          </div>
        </template>
        <template v-show="result.type">
          <h4>模板类型</h4>
          <div>
            <el-tooltip placement="top-start">
              <p slot="content">{{ format(result.type) }}</p>
              <span class="span-name">{{ format(result.type) }}</span>
            </el-tooltip>
          </div>
        </template>
        <template v-show="result.subjects && result.subjects.length">
          <h4>模板适用范围</h4>
          <div v-for="v in result.subjects" :key="v.contractSubId">
            <el-tooltip placement="top-start">
              <p slot="content">{{ v.contractName }}</p>
              <span class="span-name">{{ v.contractName }}</span>
            </el-tooltip>
          </div>
        </template>
        <template v-show="result.steps && result.steps.length">
          <h4>流程</h4>
          <p style="font-weight: bold">签署顺序</p>
          <div v-for="v in result.steps" :key="v.stepId">
            <el-tooltip placement="top-start">
              <p slot="content">
                {{ v.stepName }}
                <span style="margin-left:20px">{{v.compEmpName}}</span>
              </p>
              <span class="span-name">
                {{ v.stepName }}
                <span style="margin-left:20px">{{v.compEmpName}}</span>
              </span>
            </el-tooltip>
          </div>
        </template>
        <template v-show="result.carbonCopyList && result.carbonCopyList.length">
          <h4>抄送人</h4>
          <div v-for="v in result.carbonCopyList" :key="v.stepId">
            <el-tooltip placement="top-start">
              <p slot="content">{{ v.compEmpName }}</p>
              <span class="span-name">{{ v.compEmpName }}</span>
            </el-tooltip>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import VueDraggableResizable from "vue-draggable-resizable"
import "vue-draggable-resizable/dist/VueDraggableResizable.css"
import { apiGetContractTemplate } from "./store/api"
import {mapState} from "vuex";
import {apiCanUpdate, apiUpdateEmpStatus} from "../../../modules/contractManage/store/api";
export default {
  name: "templateShow",
  components: {
    VueDraggableResizable
  },
  data() {
    return {
      width: 0,
      height: 0,
      result: {},
      tempId: this.$route.query.tempId,
      templateStatus: this.$route.query.templateStatus,
      loading: false,
      activeNames: ['1'],
      currentPdfPage: 1,
      tempCurrentPdfPage: 1,
      signImage: {
        sign: "",
        seal: ""
      },
      controlListBack: []
    }
  },
  created() {
    this.$store.commit('SHOWAPP', false);
    this.getDetailData();
  },
  computed: {
    ...mapState({
      privilegeVoList: (state) => state.privilegeVoList,
    }),
  },
  mounted() {
    this.$store.commit('SHOWAPP', false);
  },
  beforeDestroy() {
    this.$store.commit('SHOWAPP', true);
  },
  methods: {
    // 获取pdf-warp类宽度和高度
    getParentLimit() {
      const dom = this.$refs['pdfWrap'];
      this.width = dom.offsetWidth;
      this.height = dom.offsetHeight;
    },

    initXY() {
      if (this.controlListBack.length) {
        this.controlListBack.forEach(v => {
          // debugger
          if (parseFloat(v.width) < 1) {
            v.xaxis = this.width * v.xaxis;
            v.yaxis = this.height * v.yaxis;
            v.width = v.width * this.width
            v.height = v.height * this.height
            v.fontSize = parseInt(v.fontSize * this.width)
          }
        })
        this.$forceUpdate();
      }
    },

    // 格式化合同类型
    format(val) {
      switch (val) {
        case 'LABOUR_CONTRACT': return '劳动合同'; break;
        case 'PROVE': return '证明'; break;
        case 'RULES': return '规章制度'; break;
        case 'OTHERS': return '其他'; break;
      }
    },

    // 获取签署方下标
    getSignIndex(str) {
      return str.slice(str.length - 1)
    },

    // 回显处理 - 签章、日期控件name
    updateSignName(list, x, arr) {
      let result = list && list.map(val => {
        if (val.type === 'SIGN_CONTROL') {
          val.name = this.isOneSign(arr) ? '个人签章' : ('个人签章' + this.getSignIndex(x.stepName))
        }
        if (val.type === 'SEAL_CONTROL') {
          val.name = '企业签章'
        }
        if (val.type === 'DATE_CONTROL' && val.stepName === '企业签署方') {
          val.name = '签署日期(企业)'
        } else if (val.type === 'DATE_CONTROL' && val.stepName !== '企业签署方') {
          val.name = this.isOneSign(arr) ? '签署日期' : ('签署日期' + this.getSignIndex(x.stepName))
        }
        return val
      })
      return result
    },

    // 是否仅有一个个人签署方后缀
    isOneSign(arr) {
      let length = arr.length
      let count = 0
      if (length) {
        count = arr.filter(val => val.operate === 'SIGN')
      }
      return count.length <= 1
    },

    // 数组去重
    deWeightThree(arr) {
      let map = new Map();
      for (let item of arr) {
          if (!map.has(item.name)) {
            map.set(item.name, item);
          }
      }
      return [...map.values()];
    },

    // 获取右侧控件列表
    getRightList(type) {
      let arr = this.controlListBack.filter(it => !it.isDelete && !it.isHide && it.signType === type.stepId)
      arr = arr.map(val => {
        val = {...val}
        val.isRight = true
        return val
      })
      return this.deWeightThree(arr)
    },

    // 获取合同模板详情
    getDetailData() {
      apiGetContractTemplate({
        id: this.tempId
      }).then(res => {
        if (res.success) {
          this.result = res.data;
          let list = res.data.steps
          let currentArr = []
          list.length && list.forEach(x => {
            if (x.controls && x.controls.length) {
              let updateControl = this.updateSignName(x.controls, x, list)
              x.controls = updateControl.map(y => {
                if (x.filedList && x.filedList.length ) {
                  let field = x.filedList.filter(z => y.name === z.fieldName)
                  if (!field.length) return y
                  y = {
                    ...y,
                    ...field[0],
                    text: y.value ? y.value : field[0].relationName,
                    belong: y.page,
                    signType: x.stepId,
                    stepId: x.stepId,
                    isSaveSuccess: true,
                    initialName: y.name
                  }
                  y.xaxis = parseFloat(y.xaxis)
                  y.yaxis = parseFloat(y.yaxis)
                  y.width = parseFloat(y.width)
                  y.height = parseFloat(y.height)
                  y.fontSize = parseFloat(y.fontSize).toString()
                }
                return y
              })
            }
            currentArr = currentArr.concat(x.controls)
          })
          this.controlListBack = currentArr
        }
      })
    },

    setControlList() {
      this.getParentLimit();
      this.initXY();
    },

    // 跳转编辑
    async handleEdit() {
      const { data } = await apiCanUpdate({
        id: this.tempId
      });
      if (data.data) {
        this.$router.push({
          path: "/contract-manage/set-template",
          query: {
            tempId: this.tempId,
            tempStatus: this.templateStatus
          },
        });
      } else {
        this.$message.warning('使用此模板的合同还未签完，无法更改模板哦');
      }
    },

    //启用
    handleStart() {
      this.$confirm(
        `确定启用吗？<p style="color: #999">启用后该模板可用于发起签约</p>`,
        "提示",
        {
          customClass: 'special-confirm',
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
          closeOnPressEscape: false,
          dangerouslyUseHTMLString: true,
        }
      ).then(() => {
        apiUpdateEmpStatus({ status: "ENABLED", tempId: this.tempId }).then(
          (res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.templateStatus = 'ENABLED';
            }
          }
        );
      });
    },

    //停用
    handleStop() {
      this.$confirm(
        `确定停用吗？<p style="color: #999">停用后该模板不可用于发起签约</p>`,
        "提示",
        {
          customClass: 'special-confirm',
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
          closeOnPressEscape: false,
          dangerouslyUseHTMLString: true,
        }
      ).then(() => {
        apiUpdateEmpStatus({ status: "DISABLED", tempId: this.tempId}).then(
          (res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.templateStatus = 'DISABLED';
            }
          }
        );
      });
    },

    //删除
    handleDelete() {
      this.$confirm(
        `确定删除该模板？<p style="color: #999">该操作无法撤回，请您谨慎操作</p>`,
        "提示",
        {
          customClass: 'special-confirm',
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
          closeOnPressEscape: false,
          dangerouslyUseHTMLString: true,
        }
      ).then(() => {
        apiUpdateEmpStatus({ status: "DELETED", tempId: this.tempId }).then(
          (res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.$router.push("/contract-manage/contract-template")
            }
          }
        );
      });
    },

    // 折叠
    handleChange() {

    },

    // 关闭
    handleClosed() {
      this.$router.push("/contract-manage/contract-template")
    },

    // pdf翻页
    pageChange(flag) {
      switch (flag) {
        case 0: {
          this.currentPdfPage
            = this.currentPdfPage <= 1
            ? 1
            : --this.currentPdfPage
          break
        }
        case 1: {
          this.currentPdfPage
            = this.currentPdfPage >= this.result.archives.length
            ? this.result.archives.length
            : ++this.currentPdfPage
          break
        }
      }
      this.tempCurrentPdfPage = this.currentPdfPage;
    },

    // pdf跳转页面
    handleGotoPage(page) {
      page ? (t.tempCurrentPdfPage = page.toString()) : ""
      const tempNum = parseInt(this.tempCurrentPdfPage)
      if (
        !isNaN(tempNum) && tempNum <= this.result.archives.length
        && tempNum > 0
        && this.result.archives.length >= 1
      ) {
        this.currentPdfPage = parseInt(this.tempCurrentPdfPage)
      } else {
        this.$message({
          message: "请输入正确的页码",
          type: "warning"
        })
      }
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/scss/helpers.scss";
.template-page {
  .relative-child {
    line-height: 28px;
  }
  .template-show {
    display: flex;
    justify-content: space-between;
    margin: 12px auto 0;
    overflow: hidden;
    height: calc(100vh - 60px);
    .pdf-warp {
      overflow: hidden;
      position: relative;

      .div-drag-tempnode {
        background-color: #ffe5e5;
        opacity: 0.75;
        z-index: 1;
        position: fixed;
        pointer-events: none;
        user-select: none;

        text-align: center;
        line-height: 30px;
      }

      .movie-info {
        img {
          @include widthHeight(100%, auto);
          display: block;
        }
      }

      .vdr {
        background-color: #ffe5e5;
        opacity: 0.7;
        pointer-events: auto;
        border: 1px dashed #999;
        div {
          text-align: center;
        }

        .control-info {
          box-sizing: border-box;
          position: absolute;
          width: 100%;
          padding: 4px;
          text-align: center;
          margin-top: -31px;
          background-color: #fff7d7;
          overflow: hidden;
          white-space: nowrap;
        }

        .input-control {
          /*border:1px solid red;*/
          border: none;
          @include widthHeight(100%, 100%);
          font-size: 14px;
          background-color: transparent;
        }
      }
      .signRead {
        background: #fff;
        border: none;
      }
    }
    .btn-close {
      position: absolute;
      cursor: pointer;
      pointer-events: auto;
      top: -16px;
      right: -10px;
      font-size: 20px;
      opacity: 0.7;
    }
    .countPage-contain {
      width: 100%;
      height: 60px;
      background: #fff;
      .countPage {
        text-align: left;
        padding: 8px 0;
        margin: 0 12px;
        height: 60px;
        width: calc(100% - 24px);
        box-sizing: border-box;
        line-height: 40px;
        border: none;
        box-shadow: 0 5px 16px -14px rgba(52,61,160,0.3)
      }
    }
    .page-jump {
      width: 70px;
      margin: 0 10px 0 10px;
    }
    .right {
      cursor: pointer;
      display: inline-block;
      margin-left: 20px;
      float: none;
    }
    .fixed-top {
      position: absolute;
      top: 0;
      z-index: 999;
      border: 1px solid #f3f3f3;
      background-color: #fff;
    }
    .movie-info {
      img {
        @include widthHeight(100%, auto);
        display: block;
      }
    }
    .drag-fixed {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      width: 100%;
      .drag-item {
        position: relative;
        display: flex;
        width: 48%;
        padding-bottom: 20px;
        flex-direction: column;
        text-align: center;
        cursor: pointer;
        .drag-item_icon {
          display: flex;
          height: 60px;
          align-items: center;
          justify-content: center;
          background: #FFFFFF;
          border: 1px solid #EAEAEA;
          border-radius: 4px;
          margin-bottom: 10px;
          &.sign-item {
            height: 40px;
            line-height: 40px;
            margin: 0;
            .sign-item_name {
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              word-break: break-all;
            }
          }
        }
        img {
          position: absolute;
          right: 5px;
        }
      }
    }
    .part-title {
      display: flex;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
    .section {
      width: 20%;
      height: 100%;
      overflow: auto;
      background: #fff;
      padding: 20px;
      box-sizing: border-box;
      h4 {
        font-size: 16px;
        color: #070F29;
        padding-bottom: 8px;
        font-weight: bold;
        border-bottom: 1px solid #EAEAEA;
      }
      .span-name {
        display: inline-block;
        height: 40px;
        line-height: 40px;
      }
      p {
        height: 40px;
        line-height: 40px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      /deep/ .el-collapse {
        border: none;
        .el-collapse-item__wrap {
          border: none;
        }
        .el-collapse-item__header {
          font-size: 14px;
          color: #555555;
          border: none;
        }
        .el-collapse-item__content {
          padding-bottom: 0;
        }
      }
    }
    &-middle.section {
      width: calc(60% - 24px);
      padding: 0;
    }
  }
  .header-template {
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
  }
  .header {
    background: $mainColor;
    height: 60px;
    padding: 0 30px;
    line-height: 60px;
    .header-title {
      display: flex;
      align-items: center;
      color: #ffffff;
      font-size: 16px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      .title-line {
        display: inline-block;
        width: 1px;
        height: 20px;
        margin: 0 30px;
        background-color: #ffffff;
      }
    }

    .sumitBtn {
      color: #fff;
      font-size: 14px;
      font-weight: normal;
      width: 70px;
      height: 32px;
      background: $mainColor;
      border: 1px solid #fff;
      border-radius: 4px;
      &.other {
        color: $mainColor;
        background: #FFFFFF;
      }
      &:hover {
        color: $mainColor;
        background: #FFFFFF;
      }
    }
    .closeBtn {
      color: #ffffff!important;
      font-size: 16px;
      font-weight: normal;
    }
  }
}
</style>
