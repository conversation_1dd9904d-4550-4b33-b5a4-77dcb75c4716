import { apiUserPrivilege, apiIsMananger, apiCheckSalaryBusiness, apiGetIsPaid } from './api';

export default {
  //获取用户权限
  actionUserPrivilege({ commit }) {
    return apiUserPrivilege();
  },
  //获取用户权限
  actionIsMananger({ commit }) {
    return apiIsMananger();
  },
  //判断商户是否开通产品
  actionCheckSalaryBusiness({ commit }, data) {
    return apiCheckSalaryBusiness(data);
  },
  //判断商户开通的是免费版还是收费版
  actionGetIsPaid({ commit }, data) {
    return apiGetIsPaid(data);
  },
};
