import oldFetch from 'request/oldFetch'
import { fetch } from 'request/fetch';

//获取用户权限
export function apiUserPrivilege() {
  return fetch({
    url: '/api/merchant/personnelManagement/getUserPrivilege',
    method: 'post',
    data: {
      applicationCodes: [
        'SALARY_MANAGER',
        'CONTRACT_MANAGEMENT',
        'HR_EMPLOYEE',
        'HRATTEND',
        'KPI_MANAGEMENT',
        'AUTOAPPROVAL',
      ]
    }
  });
}

//是否是该企业管理员
export function apiIsMananger() {
  return fetch({
    url: '/api/merchant/merchantNet/isMananger',
    method: 'post'
  });
}

//判断商户是否开通产品
export function apiCheckSalaryBusiness(data) {
  return fetch({
    url: '/api/hrsaas-salary/taxSubject/checkSalaryBusiness',
    method: 'get',
    params: data
  });
}

//判断商户开通的是免费版还是收费版
export function apiGetIsPaid() {
  return fetch({
    url: '/api/hrsaas-salary/salary/free/isPaid',
    method: 'post'
  });
}

//模板下载
export function apiDownloadTemplate(data) {
  return oldFetch({
    url: 'hrsaas-salary/async/templateDownload',
    method: 'post',
    data,
    responseType: 'blob'
  });
}

//获取工资项
export function apiGetSalaryItem(id) {
  return oldFetch({
    url: 'hrsaas-salary/salary/item/salary-config/' + id,
    method: 'get'
  });
}