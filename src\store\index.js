import Vue from 'vue';
import Vuex from 'vuex';
import createPersistedState from 'vuex-persistedstate';

//各模块store
import { loginStore } from 'modules/login/store';
import { selectServerStore } from 'modules/selectServer/store';
import { homePageStore } from '../modules/homePage/store';
import { salaryCalStore } from '../modules/salaryCal/store';
import { taxPageStore } from '../modules/tax/store';
import { cumulativePageStore } from '../modules/initialize/store';
import { taxPaidStore } from '../modules/taxPaid/store';
import { payManageStore } from '@/modules/payManage/store';
import { accountPageStore } from '@/modules/account/store';
import { withdrawalPageStore } from '@/modules/withdrawal/store';
import { payMasterStore } from '@/modules/paymaster/store';
import { socialFundStore } from '@/modules/socialFund/store';
import { authorizeZxStore } from '@/modules/authorizeZx/store';
import { paySalaryStore } from '@/modules/paySalary/store';
import { attendancePageStore } from '@/modules/attendance/store';
import { adjustSalaryStore } from '@/modules/adjustSalary/store';
import { powerManageStore } from '@/modules/powerManage/store';
import { staffManageStore } from '@/modules/staffManage/store';
import { contractManageStore } from '@/modules/contractManage/store';
import { salaryRuleStore } from '../modules/salaryRule/store';
import { intelligentAgentStore } from '@/modules/intelligentAgent/store';
import { templateStore } from "@/signing/modules/template/store"
import { payrollStore } from "@/modules/payroll/store"
import { reportFormsStore } from '../modules/reportForms/store';

Vue.use(Vuex);

import mutations from './mutations';
import actions from './action';

export default new Vuex.Store({
  modules: {
    loginStore,
    selectServerStore,
    homePageStore,
    salaryCalStore,
    taxPageStore,
    cumulativePageStore,
    taxPaidStore,
    withdrawalPageStore,
    payManageStore,
    accountPageStore,
    payMasterStore,
    socialFundStore,
    authorizeZxStore,
    paySalaryStore,
    attendancePageStore,
    adjustSalaryStore,
    powerManageStore,
    staffManageStore,
    contractManageStore,
    salaryRuleStore,
    intelligentAgentStore,
    templateStore,
    payrollStore,
    reportFormsStore
  },
  state: {
    ...attendancePageStore.state,
    token: '',
    isShowApp: true,
    isAuthorize: false,
    privilegeVoList: [],
    taxSubjectInfoList: [],
    countryList: [],
    bankList: [],
    isBaseMenu: false,
    mainMenu: [],
    productEdition: {
      salary: false,
      contract: false,
    },
    isManager: false,
    urlInfo: {},
  },
  mutations,
  actions,
  plugins: [
    createPersistedState({
      storage: sessionStorageOther,
    }),
  ],
});
