import * as AT from './actionTypes';
import { attendancePageStore } from '@/modules/attendance/store';
import {saveToken} from "@olading/olading-business-ui";

export default {
  ...attendancePageStore.mutations,
  [AT.SHOWAPP](state, value) {
    state.isShowApp = value;
  },
  [AT.USERTYPE](state, value) {
    state.userType = value;
  },
  [AT.AUTHORIZE](state, value) {
    state.isAuthorize = value;
  },
  [AT.SET_TOKEN](state, value) {
    state.token = value;
    saveToken(value);
    sessionStorageOther.setItem('token', value);
  },
  [AT.SET_AUTHCODE](state, value) {
    state.authcode = value;
  },
  [AT.SET_PRIVILIGEVOLiST](state, value) {
    state.privilegeVoList = value;
  },
  [AT.SET_TAXSUBJECTINFOLIST](state, value) {
    state.taxSubjectInfoList = value;
  },
  [AT.SET_DEPARTMENTLIST](state, value) {
    state.departmentList = value;
  },
  [AT.SET_POSITIONLIST](state, value) {
    state.positionList = value;
  },
  [AT.SET_RANKLIST](state, value) {
    state.rankList = value;
  },
  [AT.SET_EDUCATIONLIST](state, value) {
    state.educationList = value;
  },
  [AT.SET_COUNTRYLIST](state, value) {
    state.countryList = value;
  },
  [AT.SET_BANKLIST](state, value) {
    state.bankList = value;
  },
  [AT.SET_CITYLIST](state, value) {
    state.cityList = value;
  },
  [AT.SET_AREALIST](state, value) {
    state.areaList = value;
  },
  //退出
  [AT.EDITQUIT](state) {
    state.loginStore.profileInfo = {};
    state.loginStore.merprofileInfo = {};
    state.token = '';
    state.loginStore.userType = '';
    sessionStorageOther.clear();
  },
  [AT.SETBINDINFO](state, obj) {
    state.relevantMerchantVos = obj;
  },
  [AT.SETHOMEINFO](state, obj) {
    state.homeInfo = obj;
  },
  [AT.SET_ISBASEMENU](state, value) {
    state.isBaseMenu = value;
  },
  [AT.MAIN_MENU](state, value) {
    state.mainMenu = value;
  },
  [AT.SET_PRODUCTEDITION](state, value) {
    state.productEdition = value;
  },
  [AT.SET_ISMANAGER](state, value) {
    state.isManager = value;
  },
};
