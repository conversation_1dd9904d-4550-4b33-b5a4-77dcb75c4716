import themeArray from './themeArr';

const staticPath = window.env && window.env.staticPath ? window.env.staticPath : ""; //静态资源路径
export default function changeTheme (themeValue) {
  // console.log('切换主题颜色值：',themeValue,that.staticPath,JSON.stringify(themeArray) );

  var itemPath = staticPath + '/static/theme/' + themeValue + '/' + 'index.css';
  // var itemPath = staticPath + 'index.css';
  loadCss(itemPath);

  function loadCss (path) {
    var head = document.getElementsByTagName('head')[0];
    var link = document.createElement('link');
    link.href = path;
    link.rel = 'stylesheet';
    link.type = 'text/css';
    head.appendChild(link);
  }
  function removeCss (href) {
    const links = document.getElementsByTagName('link');
    const head = document.getElementsByTagName('head')[0];
    let arr = [];
    if (links && links.length > 0) {
      for (let i = 0, len = links.length; i < len; i++) {
        if (links[i]) {
          arr.push(links[i]);
        }
      }
      for (let i = 0, len = arr.length; i < len; i++) {
        head.removeChild(arr[i]);
      }
    }
  }
}
