import { getHroContractListApi,getHroCustomerListApi } from "../modules/staffManage/api/regular"
import Vue from "vue"

// 定义服务客户常量ID
export const HRO_CUSTOMER_ID = "hroCustomerId"

// 定义服务合同常量Id 
export const HRO_CONTRACT_ID = "hroContractId"

// 定义服务合同列表出参
export const HRO_CONTRACT = "hroContract"

// 定义服务客户列表出参
export const HRO_CUSTOMER = "hroCustomer"

// 是否开启HRO业务
export let isOpenHROBusiness = false

const createTestOpenButton = ()=> {
  const btn = document.createElement("button");
  const isOpen = window.localStorage.isOpenHROBusiness
  btn.innerHTML = isOpen ? `关闭HRO业务` : `开启HRO业务`;
  btn.style.position = "fixed";
  btn.style.top = "20px";
  btn.style.left = "20px";
  btn.style.zIndex = "9999";
  btn.onclick = function() {
    window.localStorage.isOpenHROBusiness = isOpen ? "" : "1";
    window.location.reload()
  };
  document.body.appendChild(btn);
}

const getHroIsOpenApi = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      const isOpenHROBusiness = window.localStorage.isOpenHROBusiness
      // createTestOpenButton()
      resolve(!!isOpenHROBusiness)
    }, 1000)
  })
}

// 获取全部服务客户
export function getHroCustomerOptionsApi() {
  // return getHroCustomerListApi()
  return []
}

// 获取服务合同
export async function getHroContractOptionsApi(hroCustomerId) {
  const param = {
    hroCustomerId
  }
  if(!hroCustomerId) {

    console.error(`hroCustomerId 必须有参数`)
    return []
  }
  // const data = await getHroContractListApi(param)
  return []
}

export function parseHroFieldName(map){
  try {
    return map ? map.name : "-"
  }catch(err){
    return "-"
  }
}

export async function getIsOpenHROBusinessApi(data) {
  const isOpen = await getHroIsOpenApi()
  isOpenHROBusiness = isOpen 

  Vue.component('hroSearchFormItem', createHroSearchFormItemComponent())
  Vue.component('hroCustomerFormItem', createHroCustomerFormItemComponent())
  Vue.component('hroContractFormItem', createHroContractFormItemComponent())
  Vue.component('hroCustomerTableColumn', createHroCustomerTableColumn())
  Vue.component('hroContractTableColumn', createHroContractTableColumn())
}

// 获取 HRO o-top-select 筛选项
export const getBaseTopSelectFormJson = ()=>{
  if(!isOpenHROBusiness) return []
  return [
    {
      type:"select",
      item:
      {
        prop:HRO_CUSTOMER_ID,
        label:"所属客户",
        placeholder:"请选择所属客户",
        labelMaxLength:"20",
        options:getHroCustomerOptionsApi,
        async onInput(id,formVm){
          const options = await getHroContractOptionsApi(id)
          formVm.setOptions({
            [HRO_CONTRACT_ID]:id?options:[]
          }).setFormValue({
            [HRO_CONTRACT_ID]:""
          })
        }
      },
    },
    {
      type:"select",
      item:
      {
        prop:HRO_CONTRACT_ID,
        label:"服务合同",
        placeholder:"请选择服务合同",
        labelMaxLength:"20",
      },
    },
  ]
}

// 获取 HRO o-table组件 表格列
export const getBaseTableHeader = ()=>{
  if(!isOpenHROBusiness) return []
  return [
    {
      label:"所属客户",
      prop:HRO_CUSTOMER,
      width:140,
      formatter:row => row[HRO_CUSTOMER] ? row[HRO_CUSTOMER].name : '-'
    },
    {
      label:"服务合同",
      prop:HRO_CONTRACT,
      width:140,
      formatter:row => row[HRO_CONTRACT] ? row[HRO_CONTRACT].name : '-'
    },
  ]
}

// 创建表格列
export const createTableColumn = (h,data)=>{
  if(!data) return h("span","")
  const props = {
    prop: data.prop,
    label: data.label,
    minWidth: data.width,
    showOverflowTooltip:true,
    formatter:data.formatter
  }
  return h('el-table-column', {
    props
  })
}

// 创建HRO表格-服务客户列
export const createHroCustomerTableColumn = ()=>{
  const [ customerItem ] = getBaseTableHeader()
  return {
    render(h){
      return createTableColumn(h,customerItem)
    }
  }
}

// 创建HRO表格-服务合同列
export const createHroContractTableColumn = ()=>{
  const [ _,contractItem ] = getBaseTableHeader()
  return {
    render(h){
      return createTableColumn(h,contractItem)
    }
  }
}

// 创建 服务客户 el-form-item 
export const createHroCustomerFormItemComponent = ()=>{
  return {
    props:{
      value:{
        type:[String,Number],
        default:"",
      },
      options:{
        type:Array,
        default:()=>[],
      }
    },
    data(){
      return {
        customerList:[],
      }
    },
    methods:{
      async loadOptions(){
        if(this.options.length) {
          this.options.forEach(item=>{
            item.label = item.optionEnumName;
            item.value = item.optionEnumCode;
          })
          this.customerList = this.options
          return 
        }
        const customer = await getHroCustomerOptionsApi()
        this.customerList = customer
      }
    },
    created(){
      this.loadOptions()
    },
    render(h){
      if(!isOpenHROBusiness) return h("span","")
      const selectProps = this.$attrs.selectProps || {}
      return h('el-form-item', {
        props: {
          label: '所属客户',
          ...this.$attrs
        },
      },[
        h('el-select', {
          props: {
            value: this.value,
            placeholder: '请选择所属客户',
            filterable: true,
            clearable: true,
            ...selectProps
          },
          style:{
            width:selectProps.width || '350px',
          },
          on: {
            input: value => this.$emit("input",value)
          },
        }, this.customerList.map(item => {
          return h('el-option', {
            props: {
              label: item.label,
              value: item.value,
            },
          })
        }))
      ])
    }
  }
}

// 创建 服务合同 el-form-item
export const createHroContractFormItemComponent = ()=>{
  return {
    props:{
      value:{
        type:[String,Number],
        default:"",
      },
      customerId:{
        type:[String,Number],
        default:''
      },
      autoClick:{
        type:Boolean,
        default:false
      },
    },
    data(){
      return {
        contractList:[],
      }
    },
    watch:{
      async customerId(newValue){
        if(!newValue ) {
          this.$emit("input","")
          this.contractList = []
          return 
        }
        const contract = await this.loadOptions()
        // 如果下拉选项只有一个服务合同 默认选中即可 ， 否则设置为空 
        this.$emit("input",contract.length === 1 && this.autoClick ?contract[0].newValue:"")
      }
    },
    async created(){
      this.loadOptions()
    },
    methods:{
      async loadOptions(){
        this.contractList = []
        // 客户id为空，就不请求数据
        if(!this.customerId) return 
        const contract = await getHroContractOptionsApi(this.customerId)
        this.contractList = contract
        return contract
      }
    },
    render(h){
      if(!isOpenHROBusiness) return h("span","")
      const selectProps = this.$attrs.selectProps || {}
      return h('el-form-item', {
        props: {
          label: '服务合同',
          ...this.$attrs
        },
      },[
        h('el-select', {
          props: {
            value: this.value,
            placeholder: '请选择服务合同',
            filterable: true,
            clearable: true,
            ...selectProps
          },
          style:{
            width:selectProps.width || '350px',
          },
          on: {
            input: val => this.$emit("input",val)
          },
        }, this.contractList.map(item => {
          return h('el-option', {
            props: {
              label: item.label,
              value: item.value,
            },
          })
        }))
      ])
    }
  }
}

// 创建（组合服务客户和服务合同） HRO el-form-item 筛选项组件
export const createHroSearchFormItemComponent = ()=>{
  return {
    props:{
      customerId:{
        type:[String,Number],
        default:"",
      },
      contractId:{
        type:[String,Number],
        default:"",
      },
    },
    render(h){
     
      
      const contract = h('hroContractFormItem',{
        props:{
          value:this.contractId,
          customerId:this.customerId,
        },
        on:{
          input:(val)=> this.$emit("update:contractId",val)
        }
      })

      const customer = h('hroCustomerFormItem',{
        props:{
          value:this.customerId,
        },
        on:{
          input:(val)=> {
            this.$emit("update:customerId",val)
            this.$emit("update:contractId","")
          }
        }
      })

      return h("div",[customer,contract])
    }
  }
}