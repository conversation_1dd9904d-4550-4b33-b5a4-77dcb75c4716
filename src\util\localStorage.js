const publicPath = 'xst'
const localStorageOther = {
  setItem: function (key, value) {
    window.localStorage.setItem(publicPath + '/' + key, value)
  },
  getItem: function (key) {
    window.localStorage.getItem(publicPath + '/' + key)
  },
  removeItem: function (key) {
    window.localStorage.removeItem(publicPath + '/' + key);
  },
  clear: function () {
    for (var i = 0; i < window.localStorage.length; i++) {
      if (window.localStorage.key(i).substring(0, publicPath.length) == publicPath) {
        window.localStorage.removeItem(window.localStorage.key(i));
        i--;
      }
    }
  }
}

const sessionStorageOther = {
  setItem: function (key, value) {
    window.sessionStorage.setItem(publicPath + '/' + key, value)
  },
  getItem: function (key) {
    return window.sessionStorage.getItem(publicPath + '/' + key)
  },
  removeItem: function (key) {
    return window.sessionStorage.removeItem(publicPath + '/' + key);
  },
  clear: function () {
    for (var i = 0; i < window.sessionStorage.length; i++) {
      if (window.sessionStorage.key(i).substring(0, publicPath.length) == publicPath) {
        window.sessionStorage.removeItem(window.sessionStorage.key(i));
        i--;
      }
    }
  }
}

window.localStorageOther = localStorageOther;
window.sessionStorageOther = sessionStorageOther;
