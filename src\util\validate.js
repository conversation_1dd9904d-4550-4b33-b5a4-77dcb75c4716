export const validatePhone = (phone) => {
  const phoneReg = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;
  if (!phoneReg.test(phone)) {
    return false;
  }
  return true;
};

export const validateNumber = (rule, value, callback) => {
  let reg = /^(0|[1-9]\d*)(\s|$|\.\d{1,2}\b)/;
  if (reg.test(value)) {
    callback();
  } else {
    callback(new Error('请输入正确数值'));
  }
};

export const validateNumber1 = (rule, value, callback) => {
  let reg = /^(0|[1-9]\d*)(\s|$|\.\d{1,2}\b)/;
  if (reg.test(value)) {
    if (value >= 999999.99) {
      rule.message = '不能大于999999';
      callback(new Error('不能大于999999'));
    } else {
      callback();
    }
  } else {
    callback(new Error('请输入正确数值'));
  }
};

export const validateTell = (rule, value, callback) => {
  let reg = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;
  if (reg.test(value)) {
    callback();
  } else {
    callback(new Error('请输入正确手机号'));
  }
};

export const validateIden = (rule, value, callback) => {
  //身份证号
  if (value === '') {
    callback(new Error('请输入身份证号'));
  } else {
    let reg = new RegExp('^\\d{17}[0-9x]$', 'i');
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('身份证号格式有误'));
    }
  }
};

//银行卡
export const validateBankId = (rule, value, callback) => {
  let reg = /^\d{8,30}$/;
  if (value) {
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('银行卡号只能录入8-30位的数字'));
    }
  } else {
    callback();
  }
};
//授权码
export const validateAuthCode = (rule, value, callback) => {
  let reg = /^\d{6}$/;
  if (reg.test(value)) {
    callback();
  } else {
    callback(new Error('请输入正确授权码'));
  }
};

export const validateAmount = (rule, value, callback) => {
  if (value) {
    let reg = /^\d+(\.\d{1,2})?$/;
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('请输入正确数值'));
    }
  } else {
    callback();
  }
};

export const validateAmountJE = (rule, value, callback) => {
  if (value) {
    if (value && Number(value) <= 0) {
      callback(new Error('金额须大于0'));
    }
    let reg = /^\d+(\.\d{1,2})?$/;
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('请输入正确金额'));
    }
  } else {
    if (rule.required) {
      callback(new Error('请输入金额'));
    } else {
      callback();
    }
  }
};

//报税校验码-只能输入4为数字
export const validateAmount2 = (rule, value, callback) => {
  if (value) {
    let reg = /^\d{4}$/;
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('请输入正确格式'));
    }
  } else {
    callback();
  }
};

//匹配括号是否合法
export const validBrackets = (str) => {
  // 匹配括号
  // eslint-disable-next-line
  let brackets = str.match(/[\[\]\(\)\{\}\（\）\“\”\《\》\「\」]/g);
  let arr = [];
  let symbol;
  for (var i in brackets) {
    switch (brackets[i]) {
      case '(':
        arr.push('(');
        break;
      case '[':
        arr.push('[');
        break;
      case '{':
        arr.push('{');
        break;
      case '（':
        arr.push('（');
        break;
      case '“':
        arr.push('“');
        break;
      case '《':
        arr.push('《');
        break;
      case '「':
        arr.push('「');
        break;
      case ')':
        symbol = arr.pop();
        if (symbol !== '(') return false;
        break;
      case ']':
        symbol = arr.pop();
        if (symbol !== '[') return false;
        break;
      case '}':
        symbol = arr.pop();
        if (symbol !== '{') return false;
        break;
      case '）':
        symbol = arr.pop();
        if (symbol !== '（') return false;
        break;
      // eslint-disable-next-line
      case '”':
        symbol = arr.pop();
        if (symbol !== '“') return false;
        break;
      case '》':
        symbol = arr.pop();
        if (symbol !== '《') return false;
        break;
      case '」':
        symbol = arr.pop();
        if (symbol !== '「') return false;
        break;
    }
  }
  // 当存在 (() 这样的情况时, 上面的都能通过, 最后需要检测一下arr的长度
  return !(arr.length > 0);
};
