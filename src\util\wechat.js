import { apiUserNameSearch } from '../modules/staffManage/store/ecosystemAPI.js';

const ua = window.navigator.userAgent;
const regMobile =
  /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i;
const isMobile = regMobile.test(ua);
var suiteId = '';
var corpId = '';
var agentId = '';
export const setSuiteId = (id) => {
  suiteId = id;
  localStorage.setItem('__wechatWorkSuiteId', suiteId);
};
export const getSuiteId = () => {
  if (!suiteId) {
    suiteId = localStorage.getItem('__wechatWorkSuiteId');
  }

  return suiteId;
};
export const setCorpId = (id) => {
  corpId = id;
  localStorage.setItem('__wechatWorkCorpId', corpId);
};
export const getCorpId = () => {
  if (!corpId) {
    corpId = localStorage.getItem('__wechatWorkCorpId');
  }

  return corpId;
};
export const setAgentId = (id) => {
  agentId = id;
  localStorage.setItem('__wechatWorkAgentId', agentId);
};
export const getAgentId = () => {
  if (!agentId) {
    agentId = localStorage.getItem('__wechatWorkAgentId');
  }

  return agentId;
};

export const findUsers = async (query) => {
  try {
    const suiteId = getSuiteId(); //@todo 改换合适的获取方式，不能硬编码
    const corpid = getCorpId();
    const r = await apiUserNameSearch({
      suiteId,
      corpid,
      userName: query,
    });
    if (
      r &&
      r.data &&
      r.data.departmentResultList &&
      r.data.departmentResultList.length
    ) {
      return r.data.departmentResultList;
    }
  } catch (err) {
    console.log(err);
  }
  return [];
};

export const isWechatEnv = /micromessenger/i.test(ua);
export const isWechatWorkEnv = /wxwork/i.test(ua);

export const isWechatWorkMobileEnv = isWechatWorkEnv && isMobile;
export const isWechatMobileEnv = isWechatEnv && isMobile;
