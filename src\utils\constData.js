//账户审核状态
export const accountStatus = {
  SUBMITTED: '已提交',
  WAIT: '待审核',
  PASS: '审核通过',
  BACK: '审核回退',
  FAIL: '审核拒绝',
};
//客服电话
export const serviceTel = {
  boc: '010-********',
  cgb: '************',
  olading: '************',
};
//激活状态
export const activeStatus = {
  NOT_SUBMIT: '未报送',
  WAIT_ACTIVE: '待激活',
  ACTIVED: '已激活',
};
//提现订单状态
export const withdrawStatus = {
  WITHDRAW_INIT: '已受理',
  MERCHANT_REMIT_ING: '出款中',
  SUCCESS: '出款成功',
  BACK_SUCCESS: '出款失败',
  ROLL_BACK_SUCCESS: '已退汇',
};
//充值订单状态
export const rechargeStatus = {
  ACCEPT: '支付中',
  SUCCESS: '成功',
  FAIL: '失败',
};
//订单类型
export const orderType = {
  BHA_WAP: '存管网银充值',
  BHA_UNLINE: '存管线下调账充值',
  BHA_TRANSFER: '线下转账充值',
  BHA_OFFLINE_WEB: '打款划拨充值',
};
//订单状态枚举
export const orderStatus = {
  ACCEPT: '已受理',
  CHECK_SUCCESS: '校验通过',
  CHECK_FAIL: '校验失败',
  BACK_SUCCESS: '出款失败',
  ROLL_BACK_SUCCESS: '已退汇',
  SUCCESS: '出款成功',
  CLOSED: '已关单',
  MERCHANT_REMIT_ING: '出款中',
  ROLL_BACK_ING: '退汇中',
};
//批次状态枚举
export const batchStatus = {
  CHECK_ING: '鉴权校验中',
  CHECK_ALL_SUCCESS: '校验全部成功',
  CHECK_PART_SUCCESS: '校验部分成功',
  CHECK_ALL_FAIL: '校验全部失败',
  PAID: '已支付',
  CLOSED: '已关单',
};
//开户审核状态
export const checkStatus = {
  SUBMITTED: '已提交',
  WAIT: '待审核',
  FAIL: '审核失败',
  BACK: '审核回退',
  PASS: '审核通过',
};
//雇员类型
export const workerTypeOption = [
  {
    label: '雇员',
    value: 'EMPLOYEE',
  },
  {
    label: '保险营销员',
    value: 'INSURANCE_SELLER',
  },
  {
    label: '实习学生（全日制学历教育）',
    value: 'INTERNSHIP_STUDENT',
  },
  {
    label: '证券经纪人',
    value: 'STOCK_BROKER',
  },
  {
    label: '其他',
    value: 'OTHER',
  },
];
//入职年度就业情形
export const employSituationOption = [
  {
    label: '当年首次入职学生',
    value: 'FIRST_EMP_STUDENT',
  },
  {
    label: '当年首次入职其他人员',
    value: 'FIRST_EMP_OTHER',
  },
];
//是否扣除减除费用
export const deductionCostOption = [
  {
    label: '是',
    value: '1',
  },
  {
    label: '否',
    value: '0',
  },
];
//合同状态
export const contractStatusList = [
  { label: '未执行', value: 'NOT_EXECUTE' },
  { label: '执行中', value: 'EXECUTE' },
  { label: '已到期', value: 'EXPIRE' },
  { label: '已终止', value: 'END' },
  { label: '已解除', value: 'REMOVE' },
];
//签约状态
export const contractSignStatusList = [
  { label: '未签约', value: 'WATING_SIGN' },
  { label: '签约中', value: 'SIGNING' },
  { label: '已签约', value: 'FINISH' },
];
//签订类型
export const signTypeList = [
  { label: '新签', value: 'NEW_SIGN' },
  { label: '续签', value: 'CONTINUE_SIGN' },
  { label: '变更', value: 'UPDATE_SIGN' },
];
//合同期限单位
export const contractTermUnitList = [
  { label: '年', value: 'YEAR' },
  { label: '月', value: 'MONTH' },
  { label: '日', value: 'DAY' },
];
//合同模板类型与电子合同文件类型
export const contractTemplateTypeList = [
  { label: '劳动合同', value: 'LABOUR_CONTRACT' },
  { label: '证明', value: 'PROVE' },
  { label: '规章制度', value: 'RULES' },
  { label: '其他', value: 'OTHERS' },
];
//模板使用状态
export const contractTemplateStatusList = [
  { label: '启用', value: 'ENABLED' },
  { label: '停用', value: 'DISABLED' },
  { label: '草稿', value: 'DRAFT' },
];
//电子合同文件状态
export const contractFiletatusList = [
  // { label: '待签署', value: 'WAIT_SIGN' },
  { label: '签署中', value: 'SIGNING' },
  { label: '已完成', value: 'COMPLETE' },
  { label: '已撤回', value: 'WITHDRAWN' },
  { label: '已废弃', value: 'CANCEL' },
  { label: '已终止', value: 'EXPIRED' },
  { label: '其他', value: 'OTHER' },
];
export const privilegeVOList = [
  { groupId: 5, name: '查看', code: 'salary.homePage.query' },
  { groupId: 7, name: '列表查看', code: 'salary.compute.salaryCheck.list' },
  {
    groupId: 7,
    name: '新增工资表',
    code: 'salary.compute.salaryCheck.addSalaryRule',
  },
  {
    groupId: 7,
    name: '设置工资表',
    code: 'salary.compute.salaryCheck.updateSalaryRule',
  },
  {
    groupId: 7,
    name: '启动算薪/计算/查看薪资',
    code: 'salary.compute.salaryCheck.query',
  },
  {
    groupId: 7,
    name: '核对人员-列表查看',
    code: 'salary.compute.salaryCheck.empList',
  },
  {
    groupId: 7,
    name: '核对人员-增减员导入',
    code: 'salary.compute.salaryCheck.empAdd',
  },
  {
    groupId: 7,
    name: '核对人员-删除',
    code: 'salary.compute.salaryCheck.empDelete',
  },
  {
    groupId: 7,
    name: '核对人员-导出',
    code: 'salary.compute.salaryCheck.empExport',
  },
  {
    groupId: 7,
    name: '采集报送-列表查看',
    code: 'salary.compute.salaryCheck.empReportList',
  },
  {
    groupId: 7,
    name: '采集报送-编辑',
    code: 'salary.compute.salaryCheck.empReportEdit',
  },
  {
    groupId: 7,
    name: '采集报送-报送',
    code: 'salary.compute.salaryCheck.empReport',
  },
  {
    groupId: 7,
    name: '采集报送-导出',
    code: 'salary.compute.salaryCheck.empReportExport',
  },
  {
    groupId: 7,
    name: '专项附加扣除-列表查看',
    code: 'salary.compute.salaryCheck.additionList',
  },
  {
    groupId: 7,
    name: '专项附加扣除-全部下载',
    code: 'salary.compute.salaryCheck.additionDownload',
  },
  {
    groupId: 7,
    name: '社保公积金-列表查看',
    code: 'salary.compute.salaryCheck.socialList',
  },
  {
    groupId: 7,
    name: '社保公积金-复制上月数据',
    code: 'salary.compute.salaryCheck.socialCopy',
  },
  {
    groupId: 7,
    name: '社保公积金-导入',
    code: 'salary.compute.salaryCheck.socialImport',
  },
  {
    groupId: 7,
    name: '社保公积金-导出',
    code: 'salary.compute.salaryCheck.socialExport',
  },
  {
    groupId: 7,
    name: '核算薪资-列表查看',
    code: 'salary.compute.salaryCheck.salaryList',
  },
  {
    groupId: 7,
    name: '核算薪资-浮动项导入',
    code: 'salary.compute.salaryCheck.salaryImport',
  },
  {
    groupId: 7,
    name: '核算薪资-薪资计算',
    code: 'salary.compute.salaryCheck.salaryCompute',
  },
  {
    groupId: 7,
    name: '核算薪资-薪资审核',
    code: 'salary.compute.salaryCheck.salaryReview',
  },
  {
    groupId: 7,
    name: '核算薪资-导出工资明细',
    code: 'salary.compute.salaryCheck.salaryExport',
  },
  {
    groupId: 7,
    name: '核算薪资-导出部门汇总',
    code: 'salary.compute.salaryCheck.depExport',
  },
  {
    groupId: 7,
    name: '发放薪资-银行代发',
    code: 'salary.compute.salaryCheck.payroll',
  },
  {
    groupId: 7,
    name: '发放薪资-银行报盘',
    code: 'salary.compute.salaryCheck.bankOffer',
  },
  {
    groupId: 7,
    name: '发放薪资-发放工资条',
    code: 'salary.compute.salaryCheck.providStubs',
  },
  { groupId: 9, name: '列表查看', code: 'salary.report.personReport.list' },
  { groupId: 9, name: '编辑', code: 'salary.report.personReport.edit' },
  { groupId: 9, name: '报送', code: 'salary.report.personReport.sendReport' },
  { groupId: 9, name: '导出', code: 'salary.report.personReport.export' },
  { groupId: 10, name: '列表查看', code: 'salary.report.additionl.list' },
  { groupId: 10, name: '导出', code: 'salary.report.additionl.export' },
  { groupId: 11, name: '列表查看', code: 'salary.report.taxReport.list' },
  {
    groupId: 11,
    name: '生成申报数据',
    code: 'salary.report.taxReport.generateReport',
  },
  { groupId: 11, name: '发送申报', code: 'salary.report.taxReport.sendReport' },
  { groupId: 11, name: '获取反馈', code: 'salary.report.taxReport.reportBack' },
  {
    groupId: 11,
    name: '作废申报',
    code: 'salary.report.taxReport.cancelReport',
  },
  { groupId: 11, name: '导出申报表', code: 'salary.report.taxReport.export' },
  { groupId: 13, name: '列表查看', code: 'salary.taxpay.paytax.list' },
  { groupId: 13, name: '立即缴款', code: 'salary.taxpay.paytax.sendPay' },
  {
    groupId: 13,
    name: '下载三方协议',
    code: 'salary.taxpay.paytax.downloadProtocol',
  },
  { groupId: 15, name: '列表查看', code: 'salary.init.taxSubject.list' },
  { groupId: 15, name: '新增/编辑', code: 'salary.init.taxSubject.save' },
  { groupId: 15, name: '删除', code: 'salary.init.taxSubject.delete' },
  { groupId: 16, name: '列表查看', code: 'salary.init.taxTotalBase.list' },
  { groupId: 16, name: '删除', code: 'salary.init.taxTotalBase.delete' },
  { groupId: 16, name: '导入', code: 'salary.init.taxTotalBase.import' },
  { groupId: 18, name: '列表查看', code: 'salary.psalaryIssuing.batch.select' },
  {
    groupId: 18,
    name: '激活账户',
    code: 'salary.psalaryIssuing.batch.activation',
  },
  {
    groupId: 18,
    name: '生成代发数据',
    code: 'salary.psalaryIssuing.batch.create',
  },
  {
    groupId: 18,
    name: '导入代发数据',
    code: 'salary.psalaryIssuing.batch.import',
  },
  {
    groupId: 19,
    name: '详情-确认代发',
    code: 'salary.psalaryIssuing.batchRecord.pay',
  },
  {
    groupId: 19,
    name: '列表查看',
    code: 'salary.psalaryIssuing.batchRecord.select',
  },
  {
    groupId: 19,
    name: '批量代发详情',
    code: 'salary.psalaryIssuing.batchRecord.details',
  },
  {
    groupId: 19,
    name: '继续代发',
    code: 'salary.psalaryIssuing.batchRecord.continue',
  },
  {
    groupId: 19,
    name: '删除批量代发记录',
    code: 'salary.psalaryIssuing.batchRecord.delete',
  },
  {
    groupId: 19,
    name: '导出',
    code: 'salary.psalaryIssuing.batchRecord.export',
  },
  {
    groupId: 19,
    name: '详情-编辑',
    code: 'salary.psalaryIssuing.batchRecord.detailsEdit',
  },
  {
    groupId: 19,
    name: '详情-重发',
    code: 'salary.psalaryIssuing.batchRecord.detailsContinue',
  },
  {
    groupId: 19,
    name: '详情-关闭订单',
    code: 'salary.psalaryIssuing.batchRecord.close',
  },
  { groupId: 20, name: '列表查看', code: 'salary.psalaryIssuing.order.select' },
  {
    groupId: 22,
    name: '列表查看',
    code: 'salary.account.psalaryAccount.select',
  },
  {
    groupId: 22,
    name: '新增资金账户',
    code: 'salary.account.psalaryAccount.add',
  },
  { groupId: 22, name: '立即开户', code: 'salary.account.psalaryAccount.open' },
  {
    groupId: 22,
    name: '激活账户',
    code: 'salary.account.psalaryAccount.activation',
  },
  {
    groupId: 22,
    name: '账户详情',
    code: 'salary.account.psalaryAccount.details',
  },
  { groupId: 22, name: '开户信息', code: 'salary.account.psalaryAccount.info' },
  {
    groupId: 22,
    name: '开户信息-保存修改',
    code: 'salary.account.psalaryAccount.saveUpdate',
  },
  {
    groupId: 22,
    name: '账户信息-余额提现',
    code: 'salary.account.psalaryAccount.withdraw',
  },
  {
    groupId: 22,
    name: '账户信息-修改交易密码',
    code: 'salary.account.psalaryAccount.changePwd',
  },
  { groupId: 23, name: '列表查看', code: 'salary.account.recharge.select' },
  { groupId: 23, name: '导出', code: 'salary.account.recharge.export' },
  { groupId: 24, name: '列表查看', code: 'salary.account.withdraw.select' },
  { groupId: 24, name: '导出', code: 'salary.account.withdraw.export' },
];
export const privilegeGroupTreeVO = [
  {
    name: '合同管理',
    businessCode: 'contract.manage',
    parentId: 5,
    children: [
      {
        name: '劳动合同记录',
        businessCode: 'contract.manage.laodong',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '电子合同',
        businessCode: 'contract.manage.electronic',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '合同模板',
        businessCode: 'contract.manage.template',
        parentId: 6,
        children: null,
        privileges: null,
      },
    ],
  },
  {
    name: '员工管理',
    businessCode: 'salary.compute',
    parentId: 4,
    children: [
      {
        name: '花名册',
        businessCode: 'hrEmployee.employee.roster',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '入职管理',
        businessCode: 'hrEmployee.employee.entry',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '转正管理',
        businessCode: 'hrEmployee.employee.turnRegular',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '离职管理',
        businessCode: 'hrEmployee.employee.leave',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '常用报表',
        businessCode: 'hrEmployee.employee.report',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '员工信息模板设置',
        businessCode: 'hrEmployee.employee.template',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '选项字典',
        businessCode: 'hrEmployee.employee.option',
        parentId: 6,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '薪资核算',
    businessCode: 'salary.compute',
    parentId: 4,
    children: [
      {
        name: '薪资核算',
        businessCode: 'salary.compute.salaryCheck',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '薪资档案',
        businessCode: 'salary.compute.salaryArchive',
        parentId: 6,
        children: null,
        privileges: null,
      },
      {
        name: '计薪规则',
        businessCode: 'salary.compute.salaryRule',
        parentId: 6,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '个税申报',
    businessCode: 'salary.report',
    parentId: 4,
    children: [
      {
        name: '人员信息采集',
        businessCode: 'salary.report.personReport',
        parentId: 8,
        children: null,
        privileges: null,
      },
      {
        name: '专项附加扣除',
        businessCode: 'salary.report.additionl',
        parentId: 8,
        children: null,
        privileges: null,
      },
      {
        name: '综合所得申报',
        businessCode: 'salary.report.taxReport',
        parentId: 8,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '税款缴纳',
    businessCode: 'salary.taxpay',
    parentId: 4,
    children: [
      {
        name: '三方协议缴税',
        businessCode: 'salary.taxpay.paytax',
        parentId: 12,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },

  {
    name: '初始化设置',
    businessCode: 'salary.init',
    parentId: 4,
    children: [
      {
        name: '扣缴义务人管理',
        businessCode: 'salary.init.taxSubject',
        parentId: 14,
        children: null,
        privileges: null,
      },
      {
        name: '累计应税项初始化',
        businessCode: 'salary.init.taxTotalBase',
        parentId: 14,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '代发管理',
    businessCode: 'salary.psalaryIssuing',
    parentId: 4,
    children: [
      {
        name: '批量代发',
        businessCode: 'salary.psalaryIssuing.batch',
        parentId: 17,
        children: null,
        privileges: null,
      },
      {
        name: '批量代发记录',
        businessCode: 'salary.psalaryIssuing.batchRecord',
        parentId: 17,
        children: null,
        privileges: null,
      },
      {
        name: '代发订单',
        businessCode: 'salary.psalaryIssuing.order',
        parentId: 17,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
  {
    name: '账户管理',
    businessCode: 'salary.account',
    parentId: 4,
    children: [
      {
        name: '代发账户管理',
        businessCode: 'salary.account.psalaryAccount',
        parentId: 21,
        children: null,
        privileges: null,
      },
      {
        name: '充值记录',
        businessCode: 'salary.account.recharge',
        parentId: 21,
        children: null,
        privileges: null,
      },
      {
        name: '提现记录',
        businessCode: 'salary.account.withdraw',
        parentId: 21,
        children: null,
        privileges: null,
      },
    ],
  },
  {
    name: '权限管理',
    businessCode: 'salary.auth',
    parentId: 4,
    children: [
      {
        name: '数据权限管理',
        businessCode: 'salary.auth.dataAuth"',
        parentId: 188,
        children: null,
        privileges: null,
      },
    ],
  },
];
//考勤管理菜单（demo）
export const attendancelistDemo = [
  {
    name: '加班管理',
    businessCode: 'hrAttend.attendManage.workOvertime',
    parentId: 66,
    children: null,
    privileges: null,
  },
  {
    name: '加班明细',
    businessCode: 'hrAttend.attendManage.overtimeDetail',
    parentId: 66,
    children: null,
    privileges: null,
  },
  // {
  //   name: '假勤管理',
  //   businessCode: 'hrAttend.attendManage',
  //   parentId: 4,
  //   children: [
  //     {
  //       name: '考勤组管理',
  //       businessCode: 'hrAttend.attendManage.group',
  //       parentId: 66,
  //       children: null,
  //       privileges: null
  //     },
  //     {
  //       name: '班次管理',
  //       businessCode: 'hrAttend.attendManage.work',
  //       parentId: 272,
  //       children: null,
  //       privileges: null
  //     },
  //     {
  //       name: '补卡管理',
  //       businessCode: 'hrAttend.attendManage.supplement',
  //       parentId: 66,
  //       children: null,
  //       privileges: null
  //     },
  //     {
  //       name: '加班管理',
  //       businessCode: 'hrAttend.attendManage.workOvertime',
  //       parentId: 66,
  //       children: null,
  //       privileges: null
  //     },
  //     {
  //       name: '加班明细',
  //       businessCode: 'hrAttend.attendManage.overTimeDetail',
  //       parentId: 66,
  //       children: null,
  //       privileges: null
  //     },
  //     {
  //       name: '假期管理',
  //       businessCode: 'hrAttend.attendManage.leave',
  //       parentId: 272,
  //       children: null,
  //       privileges: null
  //     },
  //     {
  //       name: '每日统计',
  //       businessCode: 'hrAttend.attendManage.dailyCount',
  //       parentId: 66,
  //       children: null,
  //       privileges: null
  //     },
  //     {
  //       name: '月度统计',
  //       businessCode: 'hrAttend.attendManage.monthlyCount',
  //       parentId: 272,
  //       children: null,
  //       privileges: null
  //     },
  //     {
  //       name: '打卡时间',
  //       businessCode: 'hrAttend.attendManage.signTime',
  //       parentId: 272,
  //       children: null,
  //       privileges: null
  //     },
  //     {
  //       name: '原始记录',
  //       businessCode: 'hrAttend.attendManage.count',
  //       parentId: 66,
  //       children: null,
  //       privileges: null
  //     },
  //   ],
  //   privileges: null
  // }
];

//绩效管理菜单（临时）

export const performanceList = [
  {
    name: '绩效考核',
    businessCode: 'salary.performance',
    parentId: 4,
    children: [
      {
        name: '考核管理',
        businessCode: 'salary.performance.manage',
        parentId: 14,
        children: null,
        privileges: null,
      },
      {
        name: '考核指标库',
        businessCode: 'salary.performance.libraryList',
        parentId: 14,
        children: null,
        privileges: null,
      },
      {
        name: '考核设置',
        businessCode: 'salary.performance.inspectionSetup',
        parentId: 14,
        children: null,
        privileges: null,
      },
      {
        name: '个人绩效档案',
        businessCode: 'salary.performance.profileList',
        parentId: 14,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
];

//电子工资条菜单（临时）

export const electPayList = [
  {
    name: '电子工资条',
    businessCode: 'pay.init',
    parentId: 4,
    children: [
      {
        name: '工资条管理',
        businessCode: 'pay.init.manage',
        parentId: 14,
        children: null,
        privileges: null,
      },
      {
        name: '工资条设置',
        businessCode: 'pay.init.set',
        parentId: 14,
        children: null,
        privileges: null,
      },
    ],
    privileges: null,
  },
];
