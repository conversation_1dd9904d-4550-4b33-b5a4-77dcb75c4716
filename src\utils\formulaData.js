//公式设置项
export const formulaConfigItems = [
  {
    showType: 'select',
    label: '工资项',
    value: '',
    itemCategory: 'SALARY_ITEM',
    itemCode: '', //工资项特有参数
    itemType: null //工资项特有参数
  },
  {
    showType: 'text',
    label: '文本',
    value: '',
    itemCategory: 'TEXT'
  },
  {
    showType: 'number',
    label: '数值',
    value: '',
    itemCategory: 'NUMBER'
  },
  {
    showType: 'date',
    label: '日期',
    value: '',
    itemCategory: 'DATE'
  },
  {
    showType: 'symbol',
    label: '＋',
    value: '＋',
    itemCategory: 'ADD'
  },
  {
    showType: 'symbol',
    label: '－',
    value: '－',
    itemCategory: 'SUB'
  },
  {
    showType: 'symbol',
    label: '×',
    value: '×',
    itemCategory: 'MUL'
  },
  {
    showType: 'symbol',
    label: '/',
    value: '/',
    itemCategory: 'DIV'
  },
  {
    showType: 'symbol',
    label: ',',
    value: ',',
    itemCategory: 'COMMA'
  },
  {
    showType: 'symbol',
    label: '(',
    value: '(',
    itemCategory: 'LEFT_BRACKET'
  },
  {
    showType: 'symbol',
    label: ')',
    value: ')',
    itemCategory: 'RIGHT_BRACKET'
  },
  {
    showType: 'formula',
    label: 'YEARS',
    value: 'YEARS',
    itemCategory: 'YEARS',
    tip:
      'YEARS(日期 , 日期)，计算两个日期之间的年数，不足1年部分舍去如：YEARS( 2018-10-12, 2020-05-10）的计算结果为 1'
  },
  {
    showType: 'formula',
    label: 'MONTHS',
    value: 'MONTHS',
    itemCategory: 'MONTHS',
    tip:
      'MONTHS(日期1,日期2)，获取日期1与日期2之间的月份数，舍去不足1月部分。例如：YEARS(2018-11-10 , 2019-03-02)=3'
  },
  {
    showType: 'formula',
    label: 'DAYS',
    value: 'DAYS',
    itemCategory: 'DAYS',
    tip: 'DAYS(日期1,日期2)，获取日期1与日期2之间的天数'
  }
];

//条件设置项
export const conditionConfigItems = [
  {
    showType: 'symbol',
    label: '>',
    value: '>',
    itemCategory: 'GREATER_THAN'
  },
  {
    showType: 'symbol',
    label: '>=',
    value: '>=',
    itemCategory: 'GREATER_OR_EQUAL'
  },
  {
    showType: 'symbol',
    label: '<',
    value: '<',
    itemCategory: 'LESS_THAN'
  },
  {
    showType: 'symbol',
    label: '<=',
    value: '<=',
    itemCategory: 'LESS_OR_EQUAL'
  },
  {
    showType: 'symbol',
    label: '=',
    value: '=',
    itemCategory: 'EQUAL'
  },
  {
    showType: 'symbol',
    label: '≠',
    value: '≠',
    itemCategory: 'NOT_EQUAL'
  },
  {
    showType: 'symbol',
    label: '且',
    value: '且',
    itemCategory: 'AND'
  },
  {
    showType: 'symbol',
    label: '或',
    value: '或',
    itemCategory: 'OR'
  }
];

//性别
export const empSex = [
  {
    label: '男',
    value: 'MALE'
  },
  {
    label: '女',
    value: 'FEMALE'
  }
];

//用工性质
export const enumEmpTypeOption = [
  {
    label: '全职',
    value: 'FULL_TIME'
  },
  {
    label: '兼职',
    value: 'PART_TIME'
  },
  {
    label: '实习',
    value: 'PRACTICE'
  },
  {
    label: '劳务',
    value: 'LABOUR'
  },
  {
    label: '退休返聘',
    value: 'RE_EMPLOY'
  }
];

//员工状态
export const employStatusOption = [
  {
    label: '在职',
    value: 'ON_THE_JOB'
  },
  {
    label: '离职',
    value: 'DIMISSION'
  },
  {
    label: '退休',
    value: 'RETIRED'
  }
];

//是否转正
export const regularEmpYnOption = [
  {
    label: '是',
    value: '1'
  },
  {
    label: '否',
    value: '0'
  },
  {
    label: '无试用期',
    value: '2'
  }
];

//雇员状态
export const workerType = [
  {
    label: '雇员',
    value: 'EMPLOYEE'
  },
  {
    label: '保险营销员',
    value: 'INSURANCE_SELLER'
  },
  {
    label: '证券经纪人',
    value: 'STOCK_BROKER'
  },
  {
    label: '其他',
    value: 'OTHER'
  }
];

//是否本月入职
export const thisMonthEmpYn = [
  {
    label: '是',
    value: '是'
  },
  {
    label: '否',
    value: '否'
  }
];

//是否本月离职
export const thisMonthLeaveYn = [
  {
    label: '是',
    value: '是'
  },
  {
    label: '否',
    value: '否'
  }
];

//是否本月转正
export const thisMonthTurnYn = [
  {
    label: '是',
    value: '1'
  },
  {
    label: '否',
    value: '0'
  },
  {
    label: '无试用期',
    value: '2'
  }
];
