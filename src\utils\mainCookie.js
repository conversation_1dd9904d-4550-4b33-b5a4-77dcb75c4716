/*
 * @Date: 2023-04-24 10:28:30
 * @LastEditors: zhaoxm
 * @LastEditTime: 2023-04-24 10:49:30
 * @Description: 设置cookie到主域 让其他子域都能拿到cookie里的数据
 */

function getMainDomain() {
  var domain = location.hostname.split('.').reverse();
  var mainDomain = '';
  if(domain.length > 1) {
    mainDomain = domain[1] + '.' + domain[0];
    if(domain[0].length === 2 && domain.length > 2) {
      mainDomain = domain[2] + '.' + mainDomain;
    }
  }
  return mainDomain;
}

export function setCookie(key, value) {
  var domain =getMainDomain();
  document.cookie = key + '=' + encodeURIComponent(value) + '; domain=.' + domain + '; path=/';
}

export function getCookieValue(key) {
  var cookies = document.cookie.split(';');
  for(var i = 0; i < cookies.length; i++) {
    var cookie = cookies[i].trim();
    if(cookie.indexOf(key + '=') === 0) {
      return decodeURIComponent(cookie.substring(key.length + 1));
    }
  }
  return null;
}