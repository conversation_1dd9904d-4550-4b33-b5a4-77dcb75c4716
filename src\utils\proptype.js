/*
 * @Date: 2022-08-10 13:29:56
 * @LastEditors: zhaoxm
 * @LastEditTime: 2022-08-10 13:44:31
 * @Description:挂载在原型上的常用方法 ， 子组件可直接通过 this.$xxx 直接使用
 */

import { Message } from "element-ui";

export function $routerPush (path){
  this.$router.push(path)
}

export function $showMessage(message,type="success"){
  Message.closeAll()
  Message[type](message)
}

export const $closeMessage = ()=>{
  Message.closeAll()
}

// 把当前页面的方法注册到Vue实例上去
export const install = (methods) => {
  return function (Vue) {
    Object.keys(methods).forEach((methodsName) => {
      if (methodsName.startsWith("$")) {
        Vue.prototype[methodsName] = methods[methodsName]
      }
    })
  }
}
