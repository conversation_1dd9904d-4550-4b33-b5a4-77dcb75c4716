/* eslint-disable */

function isNotNumber(value) {
  let filterBlank = parseFloat(value).toString() === 'NaN'; //如果是null,空串,空格,isNaN是做为数字0进行处理,过滤;
  if (isNaN(value) || filterBlank) {
    return true;
  } else {
    return false;
  }
}

// 列表标准API参数 适配 老项目列表查询参数
export function listStandardApiParamsAdaptation (params){
  let result = {}
  result = { ...params.filters}
  result.currPage = params.start
  result.pageSize = params.limit
  result.departmentId = result.departmentId ? [result.departmentId] : [];
  return result
}

export const deepClone = (source) => {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone');
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
};
// 表单校验错误滚动到首个错误的元素 参数为所有错误元素的数组
export const errorScroll = (element) => {
  if (element && element.length) {
    let target = element[0].parentElement;
    target.scrollIntoView && target.scrollIntoView({ behavior: 'smooth' });
  }
};

import _ from 'lodash';
// 获取URL全部参数 ， 返回一个对象
export function getParameters() {
  // 解决乱码问题
  const url = decodeURI(window.location.href);
  const result = {};
  const url_data = _.split(url, '?').length > 1 ? _.split(url, '?')[1] : null;
  if (!url_data) return {};
  const parameters_array = _.split(url_data, '&');
  for (const item of parameters_array) {
    const key = _.split(item, '=')[0];
    const value = _.split(item, '=')[1];
    result[key] = value;
  }
  return result;
}

export default {
  //深拷贝
  deepCopy(obj) {
    return JSON.parse(JSON.stringify(obj));
  },
  //递归深拷贝
  // deepClone(source) {
  //   if (!source && typeof source !== 'object') {
  //     throw new Error('error arguments', 'deepClone');
  //   }
  //   const targetObj = source.constructor === Array ? [] : {};
  //   Object.keys(source).forEach(keys => {
  //     if (source[keys] && typeof source[keys] === 'object') {
  //       targetObj[keys] = deepClone(source[keys]);
  //     } else {
  //       targetObj[keys] = source[keys];
  //     }
  //   });
  //   return targetObj;
  // },
  //获取url参数
  getUrlKey: function (name) {
    var url = decodeURIComponent(document.location.toString());
    var arrObj = url.split('?');
    if (arrObj.length > 1) {
      var arrPara = arrObj[1].split('&');
      var arr;
      for (var i = 0; i < arrPara.length; i++) {
        arr = arrPara[i].split('=');
        if (arr != null && arr[0] == name) {
          return arr[1];
        }
      }
      return '';
    } else {
      return '';
    }
  },
  //下载
  exportStream(data, fileName) {
    //需要请求头设置 responseType:'blob'
    let blob = new Blob([data], { type: 'application/octet-stream' });
    let link = document.createElement('a');

    link.href = URL.createObjectURL(blob);
    link.style.display = 'none';
    link.download = fileName;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },
  //生成跳转存管的html
  createAutoCommitFormHtml: function (obj) {
    let arr = [];
    arr.push('<!DOCTYPE HTML>');
    arr.push('<html>');
    arr.push('</head>');
    arr.push(
      "<meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>"
    );
    arr.push('</head>');
    arr.push('<body>');
    arr.push(
      "<form action='" +
        obj['url'] +
        "' accept-charset='UTF-8' method='post' id='autoSubmitForm'>"
    );
    Object.keys(obj).forEach(function (key) {
      arr.push(
        "<input type='hidden' name='" + key + "' value='" + obj[key] + "'/>"
      );
    });
    arr.push(
      "<script type='text/javascript'>document.getElementById('autoSubmitForm').submit()</script>"
    );
    arr.push('</body>');
    arr.push('</html>');
    var html = arr.join('');
    console.log(html);
    return html;
  },
  ruleCheckNumber(rule, value, callback) {
    if (isNotNumber(value)) {
      return callback(new Error('请输入数字'));
    } else {
      callback();
    }
  },
  convert26(num) {
    if (num == 0) {
      return '';
    }
    var str = '';
    while (num > 0) {
      var m = num % 26;
      if (m == 0) {
        m = 26;
      }
      str = String.fromCharCode(m + 64) + str;
      num = (num - m) / 26;
    }
    return str;
  },
  //手机号脱敏
  sencePhone(val) {
    let returnVal = val;
    if (val) {
      returnVal = val.substr(0, 3) + '****' + val.substr(7);
    }
    return returnVal;
  },
};
