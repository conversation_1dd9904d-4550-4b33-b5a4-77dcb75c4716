/*
 * @Date: 2023-04-24 10:54:51
 * @LastEditors: zhaoxm
 * @LastEditTime: 2023-04-24 10:57:26
 * @Description: 兼容企业微信跳转到人事
 */

import { getCookieValue } from "./mainCookie"

const wechatConfigKeys = ["__wechatWorkSuiteId","__wechatWorkAgentId","__wechatWorkCorpId"]

export const wechatConfigInit = ()=>{
  console.log("wechatConfigInit")
  wechatConfigKeys.forEach(key=>{
    const value = getCookieValue(key)
    if(value){
      localStorage.setItem(key,value)
    }
  })
}