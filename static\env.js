const env = 46;

const config = {
  156: {
    requestUrl: 'https://156-dev.lanmaoly.com/gd/hrsaas/webapi',
    platformApi:
      'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/merchant/platform/',
  },
  114: {
    requestUrl: 'https://114-qa.lanmaoly.com/gd/hrsaas/webapi',
    platformApi:
      'https://114-qa.lanmaoly.com/gd/hrsaas/webapi/api/merchant/platform/',
  },
  38: {
    requestUrl: 'https://webapi-qa.lanmaoly.com',
    platformApi: 'https://webapi-qa.lanmaoly.com/api/merchant/platform/',
  },
  46: {
    requestUrl: 'https://46-qa.olading.com/gd/hrsaas/webapi',
    platformApi:
      'https://46-qa.olading.com/gd/hrsaas/webapi/api/merchant/platform/',
  },
};

if (globalThis.window) {
  window.env = {
    server_env: 'qa',
    theme: '', //c81930
    apiPath: config[env].requestUrl,
    staticPath: '',
    environmentConfig: {
      sso: 'http://************:18010',
      salary: 'http://************:18490',
      approve: 'http://************:10028',
      hrSaasH5: 'http://************:10026',
      approval: 'http://************:18026',
      approvalH5: 'http://************:10041',
    },
    mapUrl: {
      SALARY_MANAGER: 'http://************:18490/',
      workflow: 'http://************:18010/',
      lggl: 'https://cgb-lggl.olading.com/new/index.html#/task-manage/',
      //帮助中心
      help: 'http://************:18010/',
      work: 'http://************:18010/',
    },
    requestUrl: config[env].requestUrl,
    //新增于2022年6月14日，此配置为导航组件所需
    platform: {
      api: config[env].platformApi,
      static: 'https://156-dev.lanmaoly.com/gd/hrsaas/static',
      theme: 'default', //可选default、red
      devProxy: {
        mode: 'dev',
        entry: {
          // sso:"http://*************:7777",
          expense: 'http://114alad.com:3000',
          salary: window.location.origin,
        },
      },
    },
  };
} else {
  module.exports.apiProxyConfig = config[env];
}
