static / env.js
api: "https://114-qa.lanmaoly.com/api/merchant/platform/"

wepack/webpack.config.dev.js
'/api': {
        //202
        // target: 'https://webapi-qa2.lanmaoly.com',
        //114
        target: 'https://114-qa.lanmaoly.com/gd/hrsaas/webapi',
        //38
        // target: 'https://webapi-qa.lanmaoly.com',
        //202
        // target: 'http://172.19.60.202:18490',
        //188
        // target:"https://webapi-dev.lanmaoly.com",
        //灰度
        // target: 'https://stage-webapi.olading.com',
        //123
        // target: "http://172.19.60.123:8074",
        //thu
        // target: "http://172.19.64.94:8070",
        secure: false,
        changeOrigin: true,
        //  pathRewrite: {
        //   '^/api/hrsaas-salary/': '/api/', //需要rewrite的,
        //    '^/api/hrsaas-emp/': '/api/'
        //  }
      },