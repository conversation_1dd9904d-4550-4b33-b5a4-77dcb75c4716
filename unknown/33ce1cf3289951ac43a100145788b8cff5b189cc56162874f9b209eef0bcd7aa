// mockApi.js
import { employees } from './mockData.js'

export const fetchEmployees = (page, pageSize) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const start = (page - 1) * pageSize
      const end = page * pageSize
      const data = employees.slice(start, end)
      resolve({
        data: data,
        total: employees.length,
        page: page,
        pageSize: pageSize
      })
    }, 500) // 模拟网络延迟
  })
}