import { isAmountZero } from 'kit/helpers/isAmountZero'

const validateBaseAmount = (
  nextCallback,
  minMessage = '金额必须大于等于0.1',
  maxMessage = '金额最大为100'
) => {
  return function (_rules, value, callback) {
    if (Number(value) < 0.1) return callback(minMessage)
    if (parseFloat(value) > 100) return callback(maxMessage)
    if (nextCallback) return nextCallback(_rules, value, callback)
    callback()
  }
}

export function getFormRules(vm) {
  return {
    supplierId: [
      { required: true, message: "请输入资质地id", trigger: "blur" },
    ],
    supplierName: [
      { required: true, message: "请输入资质地名称", trigger: "blur" },
    ],
    contactPhone: [
      { required: true, message: "请输入负责人联系方式", trigger: "blur" },
    ],
    contactName: [
      { required: true, message: "请输入负责人联系人姓名", trigger: "blur" },
    ],
    account: [
      { required: true, message: "请输入资质地账户", trigger: "blur" },
    ],
    clientKey: [
      { required: true, message: "请输入资质地Key", trigger: "blur" },
    ],
    securityKey: [
      { required: true, message: "请输入资质地secret", trigger: "blur" },
    ],
    supplierType: [
      { required: true, message: "请选择资质地类型", trigger: "change" },
    ],
    bizType: [
      { required: true, message: "请选择支持业务类型", trigger: "change" },
    ],
    disabled: [
      { required: true, message: "请选择状态支持的业务类型", trigger: "change" },
    ],
  };
}
