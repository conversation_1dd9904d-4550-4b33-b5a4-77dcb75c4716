<template>
    <div class="contract-list">
        <van-list v-model="loading" :finished="finished" finished-text="">
            <ContractAgreement v-for="contract in contracts" :key="contract.id" :contract="contract"
                @click="handleCardClick" style="margin-bottom: 10px;" />
        </van-list>

        <div v-if="contracts.length === 0 && !loading" class="empty">
            <img src="../../assets/images/mph5/employee/list-empty.png" alt="" />
            <p>暂无数据</p>
        </div>
    </div>
</template>

<script>
import ContractAgreement from 'kit/components/mpH5/yrlContractAgreement/Card.vue'
import { List, Button } from 'vant'
import makeYrlClient from 'kit/services/yrl/makeClient.js'
import handleError from 'kit/helpers/handleErrorH5.js'
import { getEnumByCodeOptions } from 'kit/components/mpH5/employees/options.js'

const yrlClient = makeYrlClient()

export default {
    components: {
        ContractAgreement,
        [List.name]: List,
        [Button.name]: Button
    },
    data() {
        return {
            contracts: [],
            loading: false,
            finished: true,
            options: {
                unit: [],
                contractType: [],
                contractStatus: []
            }
        }
    },
    methods: {
        async loadOptions() {
            const [unit, contractType, contractStatus] = await getEnumByCodeOptions(
                ['unit', 'contractType', 'contractStatus']
            )
            this.options.contractStatus = contractStatus
            this.options.unit = unit
            this.options.contractType = contractType
        },
        getByKeyEnum(data, key) {
            const value = data[key]
            if (!value) return '-'
            const options = this.options[key]
            const option = options.find(item => item.optionEnumCode == value)
            return option ? option.optionEnumName : '-'
        },
        async loadList() {
            if (this.loading) return

            this.loading = true

            this.finished = false
            const [err, r] = await yrlClient.getContracts({
                body: {}
            })
            if (err) return handleError(err)

            this.contracts = r?.data || []
            this.contracts.forEach(item => {
                item.unitName = this.getByKeyEnum(item, 'unit')
                item.contractType = this.getByKeyEnum(item, 'contractType')
                item.contractStatus = this.getByKeyEnum(item, 'contractStatus')
                // MOCK
                // item.contractFileList = [
                //     'https://112-qa.olading.com/static/icon-library/png/icon_mobile_attendance.png',
                //     'https://112-qa.olading.com/static/h5/yrl-official/bg.png',
                //     'https://112-qa.olading.com/static/h5/yrl-official/phone.png',
                // ]
            })
            this.finished = true
            this.loading = false
        },
        handleCardClick(contract) {
            this.$router.push({
                path: '/contractAgreementDetail',
                query: contract
            })
        },
    },
    async mounted() {
        await this.loadOptions()
        this.loadList()
    }
}
</script>

<style scoped>
.contract-list {
    background: #f6f6f7;
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 100px;
}

.empty {
    width: 60vw;
    text-align: center;
    margin: 0 auto;
    padding-top: 100px;
    color: #999;
    font-size: 14px;
}

.empty p {
    margin-top: 10px;
}

.empty img {
    width: 100%;
    display: block;
}
</style>