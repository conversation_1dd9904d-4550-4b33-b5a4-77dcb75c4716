<template>
    <div class="employee-detail">
        <van-cell-group title="合同信息">
            <van-cell title="合同编号" :value="getByKeyText('contractNo')" />
            <van-cell title="合同主体" :value="getByKeyText('contractSubName')" />
            <van-cell title="合同类型" :value="getByKeyText('contractType')" />
            <van-cell title="计量单位" :value="getByKeyText('unitName')" />
            <van-cell title="计量单价" v-if="contractInfo.unitPrice" :value="getByKeyText('unitPrice') + '元'" />
            <van-cell title="计量单价" v-else :value="'-'" />
            <van-cell title="合同有效期" :value="contractInfo.contractStartDate + '-' + contractInfo.contractEndDate" />
            <van-cell title="签订日期" :value="getByKeyText('contractSignDate')" />
            <!-- 合同编号：202410210001
            合同主体：北京天通建筑有限公司
            合同类型：固定期限合同
            计量单位：米
            计量单价：5元
            合同有效期：2024/10/01-2024/12/
            签订日期：2024/10/01 -->
        </van-cell-group>

        <van-cell-group title="合同附件">
            <div class="images" v-if="(contractInfo.contractFileList || []).length">
                <van-image @click="handlePreview(index)" style="border:0.5px solid #eee;border-radius: 4px;"
                    width="2.2rem" height="2.2rem" fit="contain" :src="url"
                    v-for="(url, index) in contractInfo.contractFileList" :key="url" />
            </div>
        </van-cell-group>
    </div>
</template>

<script>
import { Cell, CellGroup, Tag, Image, ImagePreview } from 'vant'

export default {
    components: {
        [Cell.name]: Cell,
        [CellGroup.name]: CellGroup,
        [Image.name]: Image,
        [Tag.name]: Tag
    },
    data() {
        return {
            contractInfo: {
                ...this.$route.query
            },
        }
    },
    methods: {
        getByKeyText(key) {
            return this.contractInfo[key] || "-"
        },
        handlePreview(index) {
            ImagePreview({
                images: this.contractInfo.contractFileList,
                startPosition: index,
            });
        },
    },
    computed: {
    },
}
</script>

<style scoped>
.employee-detail {
    background-color: #f8f8f8;
}

.images {
    background: #fff;
    padding: .2rem;
    flex-wrap: wrap;
    display: flex;
    gap: .2rem;
}
</style>