class Client {
  constructor(httpClient) {
    if (!httpClient) {
      throw new Error('httpClient is required')
    }

    this.httpClient = httpClient
  }

  async taskWechat(options = {}) {
    const resource = '/task/wechat'
    return this.httpClient.request(resource, options)
  }

  async smsCreateImageCaptcha(options = {}) {
    const resource = '/public/createCaptcha'
    return this.httpClient.request(resource, options)
  }

  async publicSendOtp(options = {}) {
    const resource = '/public/sendOtp'
    return this.httpClient.request(resource, options)
  }

  async smsSendSmsCode(options = {}) {
    const resource = '/public/sendLoginSms'
    return this.httpClient.request(resource, options)
  }

  async supplierListWechatOrderDetail(options = {}) {
    const resource = '/supplier/listWechatOrderDetail'
    return this.httpClient.request(resource, options)
  }

  async listWechatRedPacketDetail(options = {}) {
    const resource = '/supplier/listWechatRedPacketDetail'
    return this.httpClient.request(resource, options)
  }

  async supplierListAlipayOrderDetail(options = {}) {
    const resource = '/supplier/listAlipayOrderDetail'
    return this.httpClient.request(resource, options)
  }

  async supplierTaskSaveOrderDetailTask(options = {}) {
    const resource = '/supplier/task/saveOrderDetailTask'
    return this.httpClient.request(resource, options)
  }

  async supplierTaskQueryOrderDetailTask(options = {}) {
    const resource = '/supplier/task/queryOrderDetailTask'
    return this.httpClient.request(resource, options)
  }

  async supplierTaskDeleteOrderDetailTask(options = {}) {
    const resource = '/supplier/task/deleteOrderDetailTask'
    return this.httpClient.request(resource, options)
  }

  async supplierExportWechatOrderDetail(options = {}) {
    const resource = '/supplier/exportWechatOrderDetail'
    return this.httpClient.request(resource, options)
  }

  async supplierExportAlipayOrderDetail(options = {}) {
    const resource = '/supplier/exportAlipayOrderDetail'
    return this.httpClient.request(resource, options)
  }

  async userSmsLogin(options = {}) {
    const resource = '/public/login'
    return this.httpClient.request(resource, options)
  }

  async supplierRenewToken(options = {}) {
    const resource = '/supplier/renewToken'
    return this.httpClient.request(resource, options)
  }

  async publicDescribeFile(options = {}) {
    const resource = '/public/describeFile'
    return this.httpClient.request(resource, options)
  }

  async userLogout(options = {}) {
    const resource = '/public/logout'
    // TODO
    return [
      ,
      {
        success: true,
        message: null,
        errorCode: '0',
        data: 'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MTczODMzOTAsInN1YiI6IjEifQ.bom2C8i0ooQVfwBt-Na3C55ExqlfWm4iKBjCNZmXlDk0c8Z-HP8rbkl10xPUO35lVDgEw_gv7gu81ww8tjcuKQ'
      }
    ]
    return this.httpClient.request(resource, options)
  }

  async userProfile(options = {}) {
    const resource = '/supplier/profile'
    return this.httpClient.request(resource, options)
  }

  async bossProfile(options = {}) {
    const resource = '/boss/profile'
    return this.httpClient.request(resource, options)
  }

  async taskNotification(options = {}) {
    const resource = '/task/notification'
    return this.httpClient.request(resource, options)
  }

  async taskBatch(options = {}) {
    const resource = '/task/batch'
    return this.httpClient.request(resource, options)
  }

  async supplierSetCustomerAccountWechat(options = {}) {
    const resource = '/supplier/setCustomerAccountWechat'
    return this.httpClient.request(resource, options)
  }

  async supplierSetCustomerAccountAlipay(options = {}) {
    const resource = '/supplier/setCustomerAccountAlipay'
    return this.httpClient.request(resource, options)
  }

  async supplierCustomerAccountDetail(options = {}) {
    const resource = '/supplier/customerAccountDetail'
    return this.httpClient.request(resource, options)
  }

  async supplierListAuditAccountLog(options = {}) {
    const resource = '/supplier/listAuditAccountLog'
    return this.httpClient.request(resource, options)
  }

  async supplierListAccountTransferRecord(options = {}) {
    const resource = '/supplier/listAccountTransferRecord'
    return this.httpClient.request(resource, options)
  }

  async supplierRecharge(options = {}) {
    const resource = '/supplier/recharge'
    return this.httpClient.request(resource, options)
  }

  async supplierWithdraw(options = {}) {
    const resource = '/supplier/withdraw'
    return this.httpClient.request(resource, options)
  }

  async supplierSetCustomerAccountQuota(options = {}) {
    const resource = '/supplier/setCustomerAccountQuota'
    return this.httpClient.request(resource, options)
  }

  async supplierListWechatStock(options = {}) {
    const resource = '/supplier/listWechatStock'
    return this.httpClient.request(resource, options)
  }

  async supplierReceiptListWechatDetailReceipt(options = {}) {
    const resource = '/supplier/receipt/listWechatDetailReceipt'
    return this.httpClient.request(resource, options)
  }
  
  async supplierReceiptListWechatBatchReceipt(options = {}) {
    const resource = '/supplier/receipt/listWechatBatchReceipt'
    return this.httpClient.request(resource, options)
  }

  async supplierAddAlipayActivity(options = {}) {
    const resource = '/supplier/addAlipayActivity'
    return this.httpClient.request(resource, options)
  }

  async supplierAddRole(options = {}) {
    const resource = '/supplier/addRole'
    return this.httpClient.request(resource, options)
  }

  async supplierListAlipayActivity(options = {}) {
    const resource = '/supplier/listAlipayActivity'
    return this.httpClient.request(resource, options)
  }

  async supplierListCustomer(options = {}) {
    const resource = '/supplier/listCustomer'
    return this.httpClient.request(resource, options)
  }

  async supplierListCustomerAccount(options = {}) {
    const resource = '/supplier/listCustomerAccount'
    return this.httpClient.request(resource, options)
  }

  async supplierRoleDetail(options = {}) {
    const resource = '/supplier/roleDetail'
    return this.httpClient.request(resource, options)
  }

  async supplierSetCustomerAccountDisabled(options = {}) {
    const resource = '/supplier/setCustomerAccountDisabled'
    return this.httpClient.request(resource, options)
  }

  async supplierEditCustomer(options = {}) {
    const resource = '/supplier/editCustomer'
    return this.httpClient.request(resource, options)
  }

  async supplierStatCustomerAccountWechat(options = {}) {
    const resource = '/supplier/statCustomerAccountWechat'
    return this.httpClient.request(resource, options)
  }

  async supplierStatCustomerAccountAlipay(options = {}) {
    const resource = '/supplier/statCustomerAccountAlipay'
    return this.httpClient.request(resource, options)
  }

  async supplierDeleteWechatStock(options = {}) {
    const resource = '/supplier/deleteWechatStock'
    return this.httpClient.request(resource, options)
  }

  async supplierDeleteAlipayStock(options = {}) {
    const resource = '/supplier/deleteAlipayActivity'
    return this.httpClient.request(resource, options)
  }

  async supplierAddWechatStock(options = {}) {
    const resource = '/supplier/addWechatStock'
    return this.httpClient.request(resource, options)
  }

  async supplierCustomerDetail(options = {}) {
    const resource = '/supplier/customerDetail'
    return this.httpClient.request(resource, options)
  }

  async supplierResetCustomerSecurity(options = {}) {
    const resource = '/supplier/resetCustomerSecurity'
    return this.httpClient.request(resource, options)
  }

  async supplierAddCustomer(options = {}) {
    const resource = '/supplier/addCustomer'
    return this.httpClient.request(resource, options)
  }

  async bossListSupplier(options = {}) {
    const resource = '/boss/listSupplier'
    return this.httpClient.request(resource, options)
  }

  async bossAddSupplier(options = {}) {
    const resource = '/boss/addSupplier'
    return this.httpClient.request(resource, options)
  }

  async bossEditSupplier(options = {}) {
    const resource = '/boss/editSupplier'
    return this.httpClient.request(resource, options)
  }

  async bossSupplierPayDetail(options = {}) {
    const resource = '/boss/supplierPayDetail'
    return this.httpClient.request(resource, options)
  }

  async bossSaveSupplierWechat(options = {}) {
    const resource = '/boss/saveSupplierWechat'
    return this.httpClient.request(resource, options)
  }

  async bossSaveSupplierAlipay(options = {}) {
    const resource = '/boss/saveSupplierAlipay'
    return this.httpClient.request(resource, options)
  }

  async bossSetSupplierDisabled(options = {}) {
    const resource = '/boss/setSupplierDisabled'
    return this.httpClient.request(resource, options)
  }

  async bossListUser(options = {}) {
    const resource = '/boss/listUser'
    return this.httpClient.request(resource, options)
  }

  async bossEditUser(options = {}) {
    const resource = '/boss/editUser'
    return this.httpClient.request(resource, options)
  }

  async bossDeleteUser(options = {}) {
    const resource = '/boss/deleteUser'
    return this.httpClient.request(resource, options)
  }

  async bossAddUser(options = {}) {
    const resource = '/boss/addUser'
    return this.httpClient.request(resource, options)
  }

  async supplierGetRoleMembers(options = {}) {
    const resource = '/supplier/getRoleMembers'
    return this.httpClient.request(resource, options)
  }

  async supplierGetDeptTree(options = {}) {
    const resource = '/supplier/getDeptTree'
    return this.httpClient.request(resource, options)
  }

  async supplierSetRoleMembers(options = {}) {
    const resource = '/supplier/setRoleMembers'
    return this.httpClient.request(resource, options)
  }

  async supplierChangeAdmin(options = {}) {
    const resource = '/supplier/changeAdmin'
    return this.httpClient.request(resource, options)
  }

  async supplierAddDept(options = {}) {
    const resource = '/supplier/addDept'
    return this.httpClient.request(resource, options)
  }

  async supplierEditDept(options = {}) {
    const resource = '/supplier/editDept'
    return this.httpClient.request(resource, options)
  }

  async supplierDeleteDeptMember(options = {}) {
    const resource = '/supplier/deleteDeptMember'
    return this.httpClient.request(resource, options)
  }

  async listDeptMember(options = {}) {
    const resource = '/supplier/listDeptMember'
    return this.httpClient.request(resource, options)
  }

  async addDeptMember(options = {}) {
    const resource = '/supplier/addDeptMember'
    return this.httpClient.request(resource, options)
  }
  

  async supplierAddDept(options = {}) {
    const resource = '/supplier/addDept'
    return this.httpClient.request(resource, options)
  }

  async supplierDeleteDept(options = {}) {
    const resource = '/supplier/deleteDept'
    return this.httpClient.request(resource, options)
  }

  async supplierListRole(options = {}) {
    const resource = '/supplier/listRole'
    return this.httpClient.request(resource, options)
  }

  async supplierGetAuthorityTree(options = {}) {
    const resource = '/supplier/getAuthorityTree'
    return this.httpClient.request(resource, options)
  }

  async supplierGetRoleMembers(options = {}) {
    const resource = '/supplier/getRoleMembers'
    return this.httpClient.request(resource, options)
  }

  async supplierEditRole(options = {}) {
    const resource = '/supplier/editRole'
    return this.httpClient.request(resource, options)
  }

  async supplierQueryDeptTree(options = {}) {
    const resource = '/supplier/queryDeptTree'
    return this.httpClient.request(resource, options)
  }

  async supplierEditDeptMember(options = {}) {
    const resource = '/supplier/editDeptMember'
    return this.httpClient.request(resource, options)
  }

  async supplierDeleteRole(options = {}) {
    const resource = '/supplier/deleteRole'
    return this.httpClient.request(resource, options)
  }

  async supplierSetRoleMembers(options = {}) {
    const resource = '/supplier/setRoleMembers'
    return this.httpClient.request(resource, options)
  }

  async supplierSupplierMemberDetail(options = {}) {
    const resource = '/supplier/supplierMemberDetail'
    return this.httpClient.request(resource, options)
  }

  async riskFindRiskEventCategory(options = {}) {
    const resource = '/supplier/risk/findRiskEventCategory'
    return this.httpClient.request(resource, options)
  }

  async riskFindRiskEventInfo(options = {}) {
    const resource = '/supplier/risk/findRiskEventInfo'
    return this.httpClient.request(resource, options)
  }

  async riskExportRiskEvent(options = {}) {
    const resource = '/supplier/risk/exportRiskEvent'
    return this.httpClient.request(resource, options)
  }

  async riskGetRiskEventFilterOptions(options = {}) {
    const resource = '/supplier/risk/getRiskEventFilterOptions'
    return this.httpClient.request(resource, options)
  }

  async supplierSaveCustomerAccountRequiredFields(options = {}) {
    const resource = '/supplier/saveCustomerAccountRequiredFields'
    return this.httpClient.request(resource, options)
  }

  async supplierGetRequiredFields(options = {}) {
    const resource = '/supplier/getRequiredFields'
    return this.httpClient.request(resource, options)
  }

  async supplierResetCustomerPassword(options = {}) {
    const resource = '/supplier/resetCustomerPassword'
    return this.httpClient.request(resource, options)
  }

  async customerProfile(options = {}) {
    const resource = '/customer/profile'
    return this.httpClient.request(resource, options)
  }
  
  async customerListOrder(options = {}) {
    const resource = '/customer/listOrder'
    return this.httpClient.request(resource, options)
  }
  
  async customerListOrderDetail(options = {}) {
    const resource = '/customer/listOrderDetail'
    return this.httpClient.request(resource, options)
  }
  
  async customerListAccount(options = {}) {
    const resource = '/customer/listAccount'
    return this.httpClient.request(resource, options)
  }
  
  async customerListAccountId(options = {}) {
    const resource = '/customer/listAccountId'
    return this.httpClient.request(resource, options)
  }
  
  async customerGetAccountDetail(options = {}) {
    const resource = '/customer/getAccountDetail'
    return this.httpClient.request(resource, options)
  }

  async customerSaveTask(options = {}) {
    const resource = '/customer/saveTask'
    return this.httpClient.request(resource, options)
  }
  
  async customerQueryTask(options = {}) {
    const resource = '/customer/queryTask'
    return this.httpClient.request(resource, options)
  }

  async customerDeleteTask(options = {}) {
    const resource = '/customer/deleteTask'
    return this.httpClient.request(resource, options)
  }
  
}

export default Client
