<template>
    <Container :back="true" @confirm="confirm" :hideFootButton="isCreateSuccess" confirmButtonText="提交"
        @cancel="cancel" :title="title">
        <div class="wrap" v-loading="isLoading">
            <div style="width: 560px; margin: 0 auto" v-if="!isCreateSuccess">
                <Form ref="form" :model="formData" :rules="formRules" label-position="top">
                    <el-form-item label="资质地id" prop="supplierId">
                        <Input v-model="formData.supplierId" :trim="true" 
                            placeholder="请输入资质地id" maxlength="50" />
                    </el-form-item>

                    <el-form-item label="资质地名称" prop="supplierName">
                        <Input v-model="formData.supplierName" :trim="true" 
                            placeholder="请输入资质地名称" maxlength="50"/>
                    </el-form-item>

                    <el-form-item label="资质地负责人姓名" prop="contactName">
                        <Input v-model="formData.contactName" :trim="true" 
                            placeholder="请输入资质地负责人姓名" maxlength="50" />
                    </el-form-item>

                    <el-form-item label="负责人联系方式" prop="contactPhone">
                        <Input v-model="formData.contactPhone" :trim="true" 
                            placeholder="请输入负责人联系方式" maxlength="50" />
                    </el-form-item>

                    <el-form-item label="资质地账户号" prop="account">
                        <Input v-model="formData.account" :trim="true" 
                            :disabled="isFormItemDisabled" 
                            placeholder="请输入资质地账户号" maxlength="50" />
                    </el-form-item>

                    <el-form-item label="资质地Key" prop="clientKey">
                        <Input v-model="formData.clientKey" :trim="true" :disabled="isFormItemDisabled"
                            placeholder="请输入资质地Key" maxlength="50" />
                    </el-form-item>

                    <el-form-item label="资质地secret" prop="securityKey">
                        <Input v-model="formData.securityKey" :trim="true" :disabled="isFormItemDisabled"
                            placeholder="请输入资质地secret" maxlength="50" />
                    </el-form-item>

                    <el-form-item prop="supplierType" label="资质地类型" v-if="isEdit" class="supplierType">
                        <Select style="width: 100%" :options="supplierTypeOptions" :clearable="false"
                            v-model="formData.supplierType" />
                    </el-form-item>

                    <el-form-item prop="bizType" label="支持业务类型(多选)">
                        <Select class="bizType" style="width: 100%" placeholder="请选择支持的业务类型" multiple :options="bizTypeOptions" v-model="formData.bizType" />
                    </el-form-item>

                    <el-form-item prop="createTime" label="创建时间" v-if="isEdit">
                        <Input v-model="formData.createTime" :trim="true" :disabled="true" placeholder=""
                            maxlength="50" />
                    </el-form-item>

                    <!-- <el-form-item prop="disabled" label="状态" v-if="isEdit">
                        <Radio v-model="formData.disabled" :options="[
                            {
                                label: '启用',
                                value: true
                            },
                            {
                                label: '禁用',
                                value: false
                            },
                        ]" />
                    </el-form-item> -->

                </Form>
            </div>
            <ActivityCreatedCompleted v-show="isCreateSuccess" />
        </div>
    </Container>
</template>

<script>
import ActivityCreatedCompleted from 'kit/components/marketing/admin/activityCreatedCompleted.vue'
import Container from 'kit/components/marketing/admin/container.vue'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import Select from 'kit/components/marketing/admin/select.vue'
import Input from 'kit/components/marketing/admin/input.vue'
import { getFormRules } from './rules'
import Form from 'kit/components/marketing/admin/form.vue'
import formatAmount from 'kit/formatters/formatAmount'
import handleError from 'kit/helpers/handleError'
import deepClone from 'kit/helpers/deepClone'
import { delay } from 'kit/helpers/delay'
import Radio from 'kit/components/marketing/admin/radio.vue'
import { authorizationToken } from 'kit/helpers/marketingBossToken'

import { bizTypeOptions,supplierTypeOptions } from './options'

import { showMessage } from 'kit/helpers/showMessage'
import { oConfirm } from 'kit/components/marketing/admin/messageBox'

const marketingClient = makeMarketingClient()

const formatRequestParams = params => {
    const cloneParams = deepClone(params)
    return cloneParams
}

const formatResponseParams = params => {
    const newParams = deepClone(params)
    newParams.sendType = 1
    return newParams
}

export default {
    components: {
        ActivityCreatedCompleted,
        Radio,
        Container,
        Select,
        Form,
        Input,
    },
    data() {
        return {
            isLoading: false,
            formData: {
                "supplierId": "",
                "supplierName": "",
                "contactPhone": "",
                "contactName": "",
                "account": "",
                "securityKey": "",
                "clientKey": "",
                "bizType": "",
                "supplierType": "",
                "createTime": null
            },
            isCreateSuccess: false,
            formRules: getFormRules(this),
            bizTypeOptions,
            supplierTypeOptions,
            startPickerOptions: {
                disabledDate(time) {
                    const today = new Date()
                    today.setHours(0, 0, 0, 0)
                    return time.getTime() < today.getTime()
                }
            },
            endPickerOptions: {
                disabledDate: time => {
                    const today = new Date()
                    today.setHours(0, 0, 0, 0)
                    return time.getTime() < today.getTime()
                }
            }
        }
    },
    computed: {
        title(){
            if(this.isEdit) {
                return "查看资质地"
            }
            return "新建资质地"
        },
        isEdit() {
            return this.sId
        },
        sId() {
            return this.$route.query.id
        },
        isFormItemDisabled() {
            return Boolean(this.isEdit)
        },
        isFixedAmount() {
            return this.formData.amountFixed
        }
    },
    mounted() {
        if (this.sId) this.loadDetail()
    },
    methods: {
        formatAmount,
        async loadDetail() {
            this.isLoading = true
            const [err, { data }] = await marketingClient.adminTransferSupplierDetail({
                body: {
                    id: this.sId
                },
                ...authorizationToken()
            })
            await delay(200)
            this.isLoading = false
            if (err) return handleError(err)
            const result = formatResponseParams(data)
            this.formData = deepClone(result)

            const deepCloneBizTypeOptions =  deepClone(bizTypeOptions)
            this.bizTypeOptions = deepCloneBizTypeOptions.map(item=>{
                if (result.bizType.includes(item.value)) {
                    item.disabled = true
                }
                return item
            })
        },
        onChangeBeginTime() {
            this.$refs.form.validateField('availableBeginTime')
            this.$refs.form.validateField('availableEndTime')
        },
        validate() {
            return this.$refs.form.validate()
        },
        cancel() {
            this.$router.back()
        },
        showAmountErrorDialog(err) {
            if (!err?.message.includes('元')) return false
            oConfirm(err.message, `提示`, {
                type: 'warning',
                showCancelButton: false
            })
            return true
        },
        async confirm() {
            const error = await this.validate()
            if (error) return

            const params = formatRequestParams(this.formData)

            if (this.isEdit) {
                params.id = this.sId
                const [err] = await marketingClient.adminTransferSupplierUpdate({
                    body: params,
                    ...authorizationToken()
                })
                if (err) {
                    return handleError(err)
                }

                // const [statusError] = await marketingClient.adminTransferSupplierUpdateStatus({
                //     body: {
                //         id: this.sId,
                //         disabled: this.formData.disabled
                //     },
                //     ...authorizationToken()
                // })

                // if (statusError) {
                //     return handleError(err)
                // }

                this.cancel()
                showMessage('修改成功')
                return
            }

            const [err] = await marketingClient.adminTransferSupplierAdd({
                body: params,
                ...authorizationToken()
            })

            if (err) {
                if (this.showAmountErrorDialog(err)) return
                return handleError(err)
            }

            this.cancel()
            showMessage('创建成功')
            // this.isCreateSuccess = true
        }
    },
    created(){
        setTimeout(()=>{
            this.$refs.form.clearValidate()
        })
    }
}
</script>

<style scoped>
.wrap {
    padding: 24px 0;
}

.bizType ::v-deep.el-icon-close{
    display: none;
}

.oi-wenhao {
    position: absolute;
    top: -35px;
    left: 118px;
    cursor: pointer;
    color: #666;
}

.no-require-symbol ::v-deep .el-form-item__label::before {
    content: none !important;
}


</style>