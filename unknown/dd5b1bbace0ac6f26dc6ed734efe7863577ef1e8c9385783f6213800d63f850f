<template>
  <div>
    <ErrorAlert ref="errorAlert" />

    <div class="form-page" ref="box" v-if="info.collectUserItem.length">
      <div style="padding: 0 30px">
        <h1>领奖信息登记</h1>
        <div class="logo">
          <VantImage width="56" :src="info.merchantLogo" />
        </div>
        <Form ref="form" @submit="onSubmit" :model="form">
          <Field
            v-model="form.name"
            left-icon="icon iconfont icon-base-user-icon"
            v-if="info.collectUserItem.includes('NAME')"
            label=""
            clearable
            placeholder="请输入姓名"
            maxlength="10"
            :rules="rules.name"
          ></Field>
          <Field
            v-model="form.idCard"
            left-icon="icon iconfont icon-base-user-personal-information"
            label=""
            type="tel"
            inputmode="verbatim"
            :rules="rules.idCard"
            @blur="onIdCard"
            v-if="info.collectUserItem.includes('ID_NO')"
            maxlength="18"
            clearable
            placeholder="请输入身份证号"
          ></Field>
          <Field
            v-model="form.bankCardNo"
            left-icon="icon iconfont icon-base-safe"
            label=""
            type="tel"
            inputmode="verbatim"
            :rules="rules.bankCardNo"
            v-if="info.collectUserItem.includes('BANK_CARD_NO')"
            maxlength="32"
            clearable
            placeholder="请输入银行卡号"
          ></Field>
          <Field
            v-model="form.phone"
            left-icon="icon iconfont icon-application-phone"
            type="tel"
            clearable
            label=""
            v-if="info.collectUserItem.includes('MOBILE')"
            :rules="rules.phone"
            placeholder="请输入手机号"
            maxlength="11"
          ></Field>
          <Field
            v-model="form.captcha"
            left-icon="icon iconfont icon-base-img"
            label=""
            style="padding-right: 0"
            :rules="rules.captcha"
            clearable
            type="tel"
            placeholder="请输入图形验证码"
            v-if="info.collectUserItem.includes('SMS_CAPTCHA')"
            maxlength="4"
          >
            <template #button>
              <ImageCaptcha class="imageCaptcha" v-model="form.captchaToken" />
            </template>
          </Field>
          <Field
            v-model="form.code"
            left-icon="icon iconfont icon-base-privacy"
            label=""
            :rules="rules.code"
            clearable
            type="tel"
            placeholder="请输入短信验证码"
            v-if="info.collectUserItem.includes('SMS_CAPTCHA')"
            maxlength="6"
          >
            <template #button>
              <Button
                :color="codeButtonDisabled ? '#a6aebd' : '#f77234ff'"
                plain
                class="sms-button"
                size="small"
                type="text"
                :disabled="codeButtonDisabled"
                @click.prevent="handleGetCode"
              >
                {{ codeButtonText }}
              </Button>
            </template>
          </Field>
          <Field
            v-model="form.entName"
            left-icon="icon iconfont icon-base-data-data-visualization"
            v-if="info.collectUserItem.includes('ENT_NAME')"
            label=""
            clearable
            placeholder="请输入公司名称"
            maxlength="50"
            :rules="rules.entName"
          ></Field>
          <Button
            block
            class="submit"
            :loading="isLoading"
            :disabled="isDisabled"
            type="info"
            native-type="submit"
            >领取奖品</Button
          >
          <div class="des">获取的个人信息将仅用于发放方内部备案</div>
        </Form>
      </div>
    </div>
  </div>
</template>
<script>
import ImageCaptcha from 'kit/components/marketing/admin/imageCaptcha.vue'
import { getWechatOpenId, setWechatOpenId } from './utils/wechatOpenid'
import makeMarketingClient from 'kit/services/marketing/makeClient'
import ErrorAlert from './informationRegistration/scanErrorAlert.vue'
import { Image, Field, Form, Button, Toast, Dialog } from 'vant'
import rules from './informationRegistrationFormRules'
import handleErrorH5 from 'kit/helpers/handleErrorH5'
import deepClone from 'kit/helpers/deepClone'
import * as reg from 'kit/helpers/regexp'
import store from 'kit/helpers/store'
import {
  ACTIVITY_ENDED_CODE,
  ACTIVITY_FULL_CODE,
  ACTIVITY_NOT_STARTED_CODE,
  INVALID_LINK_CODE
} from '../admin/constants'

const marketingClient = makeMarketingClient()

const formatResponse = (data = {}) => {
  const cloneData = deepClone(data)

  cloneData.collectUserItem = cloneData.collectUserItem || []
  cloneData.couponsList = cloneData.couponsList || []

  if (cloneData.collectUserItem.includes('SMS_CAPTCHA')) {
    cloneData.collectUserItem.push('MOBILE')
  }
  return cloneData
}

export default {
  async beforeRouteEnter(to, from, next) {
    const params = new URLSearchParams(location.search)
    const code = params.get('code')
    const state = params.get('state')
    const openid = params.get('test_openid')

    const isProduction = import.meta.env.MODE === 'production'

    if (!isProduction) {
      setWechatOpenId('o5qyP6YOlyPG8la-ztiam0sLWnvs')
      return next()
    }

    if (openid) {
      setWechatOpenId(openid)
      return next()
    }

    if (!code) {
      Dialog.alert({
        title: '提示',
        showConfirmButton: false,
        message: '请勿转发或复制链接打开，将会导致您领取失败'
      })
      return
    }

    const [error, result] = await marketingClient.wechatGetOpenId({
      body: {
        code: code,
        state: state
      }
    })

    if (error) {
      handleErrorH5(error)
      return
    }

    setWechatOpenId(result.data.openId)

    next()
  },
  components: {
    ErrorAlert,
    ImageCaptcha,
    Button,
    VantImage: Image,
    Field,
    Form
  },
  computed: {
    imgSrc() {
      const baseUrl = window.env?.platform?.api
      if (!baseUrl) {
        handleError('无法获取平台的API地址')
        return
      }

      return `${baseUrl}captcha?token=${encodeURIComponent(this.captcha.token)}`
    }
  },
  data() {
    return {
      smsToken: '',
      isLoading: false,
      captcha: {
        token: ''
      },
      info: {
        id: 0,
        name: '',
        availableBeginTime: '2023-06-20T03:52:02.131Z',
        availableEndTime: '2023-06-20T03:52:02.131Z',
        getWay: '1',
        getLimit: 0,
        bannerImageUrl: '',
        remark: '',
        status: '1',
        qualification: '',
        rewardInfo: '',
        amount: 0,
        useCount: 0,
        merchantLogo: '',
        noStock: true,
        couponsList: [],
        collectUserItem: []
      },
      form: {
        name: '',
        idCard: '',
        phone: '',
        code: '',
        bankCardNo: '',
        captchaToken: '',
        captcha: '',
        entName:''
      },
      rules,
      codeButtonDisabled: false,
      codeButtonText: '获取验证码',
      codeCountDown: 60,
      codeTimer: null
    }
  },
  computed: {
    isDisabled() {
      const { name, idCard, phone, code, bankCardNo,entName } = this.form
      const map = {
        MOBILE: phone,
        NAME: name,
        SMS_CAPTCHA: code,
        ID_NO: idCard,
        BANK_CARD_NO: bankCardNo,
        ENT_NAME:entName
      }

      const collectUserItem = this.info.collectUserItem
      for (let key of collectUserItem) {
        if (!map[key]) return true
      }

      return false
    }
  },
  async created() {
    this.loadInfo()
  },
  methods: {
    onIdCard() {
      this.form.idCard = this.form.idCard.replace(/x/g, 'X')
    },
    showErrorAlert(errorCode) {
      this.$refs.errorAlert.open(errorCode)
    },
    async loadInfo() {
      const params = new URLSearchParams(location.search)
      const sn = params.get('sn')
      const channel = params.get('channel')

      const [error, result] = await marketingClient.mobileActivityInfo({
        body: {
          sn,
          channel,
          openid: getWechatOpenId()
        }
      })

      const errorCodeDialog = [
        ACTIVITY_NOT_STARTED_CODE,
        ACTIVITY_ENDED_CODE,
        ACTIVITY_FULL_CODE,
        INVALID_LINK_CODE
      ]

      if (error && errorCodeDialog.includes(error.errorCode)) {
        this.showErrorAlert(error.errorCode)
        return
      }

      if (error) {
        handleErrorH5(error)
        return
      }

      if (result.data.noStock) {
        this.showErrorAlert(503)
        return
      }

      if (Number(result.data.status) === 1) {
        this.showErrorAlert(501)
        return
      }

      if (Number(result.data.status) === 3) {
        this.showErrorAlert(ACTIVITY_ENDED_CODE)
        return
      }

      store.set('__activity_info_result__', JSON.stringify(result))

      this.info = formatResponse(result.data)

      if (this.info.collectUserItem.length === 0) this.toCouponPage(this.info)

      await this.$nextTick()
      if (this.$refs.box) {
        this.$refs.box.style.height = `${window.innerHeight}px`
      }
    },
    toCouponPage() {
      const params = new URLSearchParams(location.search)
      const sn = params.get('sn')
      const channel = params.get('channel')

      this.$router.replace({
        path: '/awardCollection',
        query: {
          sn,
          channel
        }
      })
    },
    async handleGetCode() {
      if (this.codeButtonDisabled) {
        return
      }
      if (!this.form.phone) {
        return Toast.fail('请输入手机号')
      }
      if (!reg.PHONE_NUMBER_REGEX.test(this.form.phone)) {
        return Toast.fail('请输入正确的手机号')
      }
      if (!this.form.captcha) {
        return Toast.fail('请输入图形验证码')
      }
      if (!this.form.captchaToken) {
        return Toast.fail('请重置图形验证码')
      }

      this.codeButtonDisabled = true

      const [err, r] = await marketingClient.smsSendSmsCode({
        body: {
          mobile: this.form.phone,
          captchaToken: this.form.captchaToken,
          captcha: this.form.captcha
        }
      })

      this.form.captchaToken = ''

      if (err) {
        handleErrorH5(err)
        this.codeButtonDisabled = false
        return
      }

      this.smsToken = r.data.smsToken

      this.startCodeCountDown()
    },
    startCodeCountDown() {
      this.codeButtonText = `${this.codeCountDown} 秒后重试`

      this.codeTimer = setInterval(() => {
        this.codeCountDown--
        if (this.codeCountDown === 0) {
          this.resetCodeCountDown()
        } else {
          this.codeButtonText = `${this.codeCountDown} 秒后重试`
        }
      }, 1000)
    },
    resetCodeCountDown() {
      clearInterval(this.codeTimer)
      this.codeButtonDisabled = false
      this.codeButtonText = '获取验证码'
      this.codeCountDown = 60
    },
    async onSubmit() {
      if (!this.smsToken && this.info.collectUserItem.includes('SMS_CAPTCHA')) {
        return Toast.fail('请获取短信验证码')
      }

      const params = new URLSearchParams(location.search)
      const sn = params.get('sn')
      const channel = params.get('channel')

      const body = {
        sn,
        openid: getWechatOpenId(),
        name: this.form.name,
        mobile: this.form.phone,
        smsCode: this.form.code,
        smsToken: this.smsToken,
        idNo: this.form.idCard,
        bankCardNo: this.form.bankCardNo,
        channel: channel,
        entName: this.form.entName
      }

      this.isLoading = true

      const [err, result] = await marketingClient.mobileActivityRegister({
        body
      })

      setTimeout(() => {
        this.isLoading = false
      }, 300)

      if (err) {
        handleErrorH5(err)
        return
      }

      this.toCouponPage(result.data || {})
    }
  }
}
</script>

<style scoped>
.form-page {
  background: url('../../../assets/images/log-on-bg.png') no-repeat left bottom;
  background-size: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}
.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  overflow: hidden;
  width: 56px;
  margin: 0 auto;
  margin-bottom: 40px;
}
h1 {
  padding: 40px 0 36px;
  margin: 0;
  text-align: center;
  color: #000000ff;
  font-size: 20px;
  font-weight: 500;
  font-family: 'PingFang SC';
  line-height: 28px;
}
.des {
  color: #828b9bff;
  font-size: 12px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 20px;
}
::v-deep .van-cell {
  padding: 0.32rem 0.16rem;
  align-items: center;
}
::v-deep .van-field__left-icon {
  margin-right: 16px;
  color: #86919e;
}

::v-deep .van-field__left-icon .van-icon-icon {
  font-size: 24px;
}

::v-deep .sms-button {
  font-size: 14px;
  font-weight: 400;
  left: 8px;
  position: relative;
}

::v-deep .submit {
  border-radius: 6px;
  margin: 32px auto 16px;
}

.imageCaptcha {
  position: absolute;
  right: 8px;
  top: -0.1rem;
}
</style>
