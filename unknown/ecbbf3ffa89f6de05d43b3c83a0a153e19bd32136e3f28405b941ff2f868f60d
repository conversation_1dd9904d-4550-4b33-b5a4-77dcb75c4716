<template>
    <o-container ref="container" :title="$route.meta.title">
        <CreateModal ref="createModal" @success="reload"></CreateModal>

        <DefaultRouterSetting :options="options" :defaultRouter="defaultRouter" @save-success="reload" />

        <h2 style="margin-top:10px; font-size: 14px">设置单独路由</h2>

        <div style="display: flex; justify-content: flex-end;padding-bottom: 10px">
            <el-button type="primary" @click="$refs.createModal.open('新建单独路由')">新建</el-button>
        </div>

        <o-table ref="o-table" :sticky="true" :pagination="{ fixed: true }" :showPagination="true"
            :deleteNullApiParams="true" :tableHeader="tableHeader" :actionButtons="actionButtons"
            :requestFn="getListApi" emptyHeight="calc(100vh - 450px)" />
    </o-container>
</template>
<script>
import { authorizationToken } from 'kit/helpers/marketingBossToken'
import { handleError } from 'kit/helpers/marketingBossToken'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()
import CreateModal from "./createModal.vue"
import Select from 'kit/components/marketing/admin/select.vue';
import DefaultRouterSetting from './DefaultRouterSetting.vue'
import { bizTypeOptions } from '../transferSupplier/options.js'

const loadList = async params => {
    const [err, result] = await marketingClient.adminTransferSupplierFindRouteList({
        body: params,
        ...authorizationToken()
    })
    if (err) return handleError(err)
    return result.data
}

export default {
    components: {
        CreateModal,
        Select,
        DefaultRouterSetting,
    },
    data() {
        return {
            getListApi: loadList,
            isFirstLoad: true,
            tableHeader: [
                {
                    prop: 'merchantName',
                    label: '商户名称',
                    width: 200,
                },
                {
                    prop: 'merchantId',
                    label: '商户id',
                    minWidth: 100,
                },

                {
                    prop: 'supplierName',
                    label: '资质地名称',
                    minWidth: 150
                },
                {
                    prop: 'supplierId',
                    label: '资质地id',
                    minWidth: 150
                },
                {
                    prop: 'bizType',
                    label: '业务类型',
                    minWidth: 100,
                    formatter: row => {
                        return row.bizType.map(item => {
                            const { label } = bizTypeOptions.find(option => option.value === item) || {}
                            return label
                        }).join('、')
                    }
                },
            ],
            actionButtons: [
                // {
                //     label: '修改',
                //     click: row => {
                //         this.$refs.createModal.open('编辑单独路由', row)
                //     }
                // }
            ],
            options: {
                wechatFund: [],
                wechatTransfer: [],
            },
            defaultRouter: {
                wechatFund: {
                    supplierId: "",
                    supplierName: ""
                },
                wechatTransfer: {
                    supplierId: "",
                    supplierName: ""
                }
            },
        }
    },
    computed: {
        oTable() {
            return this.$refs['o-table']
        }
    },
    methods: {
        reload() {
            this.oTable.reload()
            this.loadDefaultRouter()
        },
        onSearch() {
            this.oTable.appendRequestParams({})
        },
        async loadDefaultRouter() {
            const [err, result] = await marketingClient.adminTransferSupplierQueryDefaultRoute({
                body: {},
                ...authorizationToken()
            })
            if (err) return handleError(err)
            this.defaultRouter = result.data
        },
        async loadOptions() {
            const [[_err, wechatTransfer], [_err1, wechatFund]] = await Promise.all([
                marketingClient.adminTransferSupplierList({
                    body: {
                        bizType: "wechatTransfer",
                    },
                    ...authorizationToken()
                }),
                marketingClient.adminTransferSupplierList({
                    body: {
                        bizType: "wechatFund",
                    },
                    ...authorizationToken()
                })
            ])


            this.options.wechatTransfer = wechatTransfer.map(item => {
                return {
                    bizType: item.bizType,
                    label: item.supplierName,
                    value: item.supplierId
                }
            })

            this.options.wechatFund = wechatFund.map(item => {
                return {
                    bizType: item.bizType,
                    label: item.supplierName,
                    value: item.supplierId
                }
            })
        },
    },
    activated() {
        if (!this.isFirstLoad) this.reload()
    },
    mounted() {
        this.onSearch()
        this.loadDefaultRouter()
        this.loadOptions()
    },
}
</script>