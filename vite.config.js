import { basename, extname, resolve } from 'path'
import { defineConfig } from 'vite'
import { createVuePlugin } from 'vite-plugin-vue2'
import fs from 'fs'

const projects = ['platform', 'merchant', 'regulation', 'task', 'personal']
const mpaRewritePlugin = {
  name: 'mpa-rewrite',
  configureServer(server) {
    server.middlewares.use((req, res, next) => {
      const ext = extname(req.url)
      if (ext || req.url.includes('vite/') || req.url.includes('/api')) {
        return next()
      }

      var matchedProject = ''
      for (var c of projects) {
        if (req.url.includes(c)) {
          matchedProject = c
          break
        }
      }
      if (!matchedProject) {
        console.log('url not match', req.url)
        return next()
      }
      console.log('matchedProject', matchedProject)
      if (fs.existsSync(resolve(__dirname, `${matchedProject}.html`))) {
        req.url = `/operate-labor/${matchedProject}.html`
      }

      next()
    })
  }
}

export default defineConfig({
  plugins: [createVuePlugin(), mpaRewritePlugin],
  appType: 'mpa', // 关键设置：多页应用
  base: '/operate-labor',
  define: {
    __APP_VERSION__: Math.random()
  },
  build: {
    target: 'esnext',
    rollupOptions: {
      input: {
        platform: resolve(__dirname, 'platform.html'),
        merchant: resolve(__dirname, 'merchant.html'),
        regulation: resolve(__dirname, 'regulation.html'),
        task: resolve(__dirname, 'task.html'),
        personal: resolve(__dirname, 'personal.html')
      }
    }
  },
  server: {
    host: '0.0.0.0',
    fs: {
      allow: ['..']
    },
    proxy: {
      '/operate-labor/api': {
        target: 'https://156-dev.lanmaoly.com',
        // target: 'http://127.0.0.1:8080',
        changeOrigin: true
        // rewrite: path => path.replace('/operate-labor/api/', '/api')
      }
    }
  },
  resolve: {
    alias: {
      kit: resolve(__dirname, '../kit/src'),
      vue: resolve(__dirname, 'node_modules/vue'),
      'vue-router': resolve(__dirname, 'node_modules/vue-router'),
      'element-ui': resolve(__dirname, 'node_modules/element-ui')
    }
  }
})
