class WebpackCleanupPlugin {
  // 构造函数
  constructor(options) {
    console.log('WebpackCleanupPlugin', options);
  }
  // 应用函数
  apply(compiler) {
    const outputPath = compiler.options.output.path;
    compiler.plugin('compilation', function (compilation, options) {
      compilation.plugin(
        'html-webpack-plugin-before-html-processing',
        function (htmlPluginData, callback) {
          let resultHTML = htmlPluginData.html.replace(
            '/__PUBLIC_PATH_PLACEHOLDER__',
            '/'
          );
          // 返回修改后的结果
          htmlPluginData.html = resultHTML;
        }
      );
    });
  }
}

module.exports = WebpackCleanupPlugin;
