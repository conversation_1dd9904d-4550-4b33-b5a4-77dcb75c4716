#! /bin/bash

NAME=$1

FILE_PATH=$(cd "$(dirname "${BASH_SOURCE[0]}")/../src/modules" && pwd)

re="[[:space:]]+"

if [ "$#" -ne 1 ] || [[ $NAME =~ $re ]] || [ "$NAME" == "" ]; then
  echo "Usage: yarn gc \${name} with no space"
  exit 1
fi

DIRNAME="$FILE_PATH/$NAME"
INPUT_NAME=$NAME

if [ -d "$DIRNAME" ]; then
  echo "$NAME component already exists, please change it"
  exit 1
fi
NORMALIZED_NAME=""
for i in $(echo $NAME | sed 's/[_|-]\([a-z]\)/\ \1/;s/^\([a-z]\)/\ \1/'); do
  C=$(echo "${i:0:1}" | tr "[:lower:]" "[:upper:]")
  NORMALIZED_NAME="$NORMALIZED_NAME${C}${i:1}"
done
NAME=$NORMALIZED_NAME

mkdir -p "$DIRNAME"
mkdir -p "$DIRNAME/router"
mkdir -p "$DIRNAME/store"
mkdir -p "$DIRNAME/components"

cat > $DIRNAME/index.vue <<EOF
<template>
<div>
</div>
</template>

<script>
export default {
    name: '${NAME}',
    data() {
        return { 

        }
    },
    components: { },
    methods: { }
}
</script>

<style lang="scss" scoped>

</style>
EOF

cat > $DIRNAME/router/index.js <<EOF
import index from "../index"
export default [
  {
    path: "/",
    name: "",
    component: ""
  }
]
EOF

cat > $DIRNAME/store/api.js <<EOF
import fetch from "request/fetch"

export function api(data) {
  return fetch({
    url: "",
    method: "post",
    data
  })
}
EOF

cat > $DIRNAME/store/action.js <<EOF
import { Name } from "./api"

export const actionName = ({ commit }, data) => {
  return Name(data).then(res => {
    return res
  })
}
EOF

cat > $DIRNAME/store/actionTypes.js <<EOF
export const NAME = "LIJINGLUN"
EOF

cat > $DIRNAME/store/mutations.js <<EOF
import * as AT from "./actionTypes"
export default {
  [AT.NAME](state, value) {
    state = value
  }
}
EOF

cat > $DIRNAME/store/index.js <<EOF
import * as actions from "./actions"
import mutations from "./mutations"

export const $INPUT_NAME = {
  namespaced: true,
  state: {},
  actions,
  mutations
}
EOF

cat > $DIRNAME/components/name.vue <<EOF
<template>
<div>
    <slot></solt>
</div>
</template>
<script>
export default {
    props: { },
    data() {
        return { 

        }
    },
    methods: { }
}
</script>
<style lang="scss" scoped>
</style>
EOF