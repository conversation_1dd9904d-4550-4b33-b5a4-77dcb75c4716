const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const FriendlyErrorsPlugin = require('friendly-errors-webpack-plugin');
const webpackConfigBase = require('./webpack.config.base.js');
const proxyConfig = require('./proxy.config.js');
const os = require('os');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const path = require('path');
const Plugins2 = require('./Plugins2');
const { apiProxyConfig } = require('../static/env.js');

const config = Object.assign(webpackConfigBase.config, {
  mode: 'development',
  devtool: 'eval-source-map',
  // 入口
  entry: {
    app: ['babel-polyfill', webpackConfigBase.resolve('src/index.js')],
    nashuizhutirenzheng: [
      webpackConfigBase.resolve('src/nashuizhutirenzheng/index.js'),
    ],
    kaoqinji: [webpackConfigBase.resolve('src/kaoqinji/index.js')],
  },
  // 输出
  output: {
    path: webpackConfigBase.resolve('dev'),
    filename: '[name].js',
    publicPath: '/',
  },
  plugins: [
    // html 模板插件
    new HtmlWebpackPlugin({
      filename: 'index.html',
      chunksSortMode: 'none',
      template: webpackConfigBase.resolve('src/index.ejs'),
      excludeChunks: [
        'nashuizhutirenzhengVendor',
        'nashuizhutirenzheng',
        'kaoqinji',
      ],
    }),
    new HtmlWebpackPlugin({
      filename: 'nashuizhutirenzheng.html',
      template: webpackConfigBase.resolve('src/nashuizhutirenzheng.html'),
      chunks: ['vue', 'nashuizhutirenzhengVendor', 'nashuizhutirenzheng'],
    }),
    new HtmlWebpackPlugin({
      filename: 'kaoqinji.html',
      template: webpackConfigBase.resolve('src/kaoqinji.html'),
      chunks: ['vue', 'kaoqinjiVendor', 'kaoqinji'],
    }),
    new Plugins2(),
    new CopyWebpackPlugin([
      // 复制favicon到dist
      {
        from: webpackConfigBase.static,
        to: webpackConfigBase.resolve('dev/static'),
      },
      {
        from: path.resolve(__dirname, '../config'),
        to: 'config',
        ignore: ['.*'],
      },
    ]),
    // 热替换插件
    new webpack.HotModuleReplacementPlugin(),
    // 更友好地输出错误信息
    new FriendlyErrorsPlugin(),
    // make sure to include the plugin for the magic
    webpackConfigBase.VueLoaderPluginInstance,
  ],
  devServer: {
    proxy: {
      // '/api/attend':{
      //   target: 'http://192.168.18.69:8075',
      //   secure: false,
      //   changeOrigin: true,
      //   pathRewrite: {
      //     '/api/attend': '/api/',
      //   },
      // },
      '/api/': {
        target: 'https://46-qa.olading.com',
        secure: false,
        changeOrigin: true,
        pathRewrite: {
          '/api/': '/gd/hrsaas/webapi/api/',
        },
      },
      '/gd/hrsaas/webapi/api': {
        target: 'https://46-qa.olading.com',
        secure: false,
        changeOrigin: true,
      },
    },

    disableHostCheck: true, // 为了手机可以访问
    publicPath: '/',
    historyApiFallback: true,
    quiet: true, //安静模式 省略多余console
    inline: true, //实时刷新
    hot: true, // 使用热加载插件 HotModuleReplacementPlugin
    clientLogLevel: 'none', // HRM WDS 在浏览器控制台的输出
  },
});

/**
 * 获取本机ip
 */
function getIP() {
  const interfaces = os.networkInterfaces();
  let addresses = [];
  for (let k in interfaces) {
    for (let k2 in interfaces[k]) {
      let address = interfaces[k][k2];
      if (address.family === 'IPv4' && !address.internal) {
        addresses.push(address.address);
      }
    }
  }
  return addresses[0];
}

module.exports = config;
